var prod = process.env.NODE_ENV == "production";
var dev = process.env.NODE_ENV == "dev";
var local = process.env.NODE_ENV == "local";
var prodBasePath = "/home/<USER>/builds/neo";
var devBasePath = "/home/<USER>/builds/neo";
var localTemplate = "./index.local.hbs";
var devTemplate = "./index.dev.hbs";
var prodTemplate = "./index.prod.hbs";
var localBasePath = "C:/Users/<USER>/Documents/GitHub/chaayos/neo-framework/neo-service/WebContent/webapp/";
var webpack = require("webpack");
var path = require("path");
var HtmlWebpackPlugin = require("html-webpack-plugin");
var CleanWebpackPlugin = require("clean-webpack-plugin");
var WebpackMd5Hash = require("webpack-md5-hash");
var polyfill = require("babel-polyfill");
const PreloadWebpackPlugin = require("preload-webpack-plugin");
const ResourceHintWebpackPlugin = require("resource-hints-webpack-plugin");
var AssetsPlugin = require("assets-webpack-plugin");
var CopyWebpackPlugin = require("copy-webpack-plugin");

var commonPlugins = [
    new WebpackMd5Hash(),
    new CleanWebpackPlugin(["dist"], {
        root: prod ? prodBasePath : dev ? devBasePath : localBasePath,
        verbose: true,
        dry: false,
        exclude: [],
    }),
    new webpack.optimize.CommonsChunkPlugin({
        name: ["vendor"],
        minChunks: "2",
    }),
    new ResourceHintWebpackPlugin(),
    new AssetsPlugin({
        filename: "assets.json",
        path: path.join(__dirname, "dist"),
        entryPoints: true,
    }),
    new HtmlWebpackPlugin({
        data: require((prod
            ? prodBasePath
            : dev
            ? devBasePath
            : localBasePath) + "/dist/assets.json"),
        template: prod ? prodTemplate : dev ? devTemplate : localTemplate,
        //templateParameters:require('./dist/assets.json'),
        filename: "index.html",
        excludeChunks: ["vendors"],
        inject: false,
    }),
    //new PreloadWebpackPlugin(),
    /*new webpack.ProvidePlugin({
        'Promise': 'es6-promise', // Thanks Aaron (https://gist.github.com/Couto/b29676dd1ab8714a818f#gistcomment-1584602)
        'fetch': 'imports?this=>global!exports?global.fetch!whatwg-fetch'
    })*/
];

var prodExplicitPlugins = [
    new webpack.DefinePlugin({
        "process.env": {
            NODE_ENV: JSON.stringify("production"),
        },
    }),
    new webpack.optimize.UglifyJsPlugin({
        compress: true,
        warning: false,
        sourcemap: false,
    }),
    new webpack.optimize.DedupePlugin(),
    new webpack.optimize.OccurenceOrderPlugin(),
    new webpack.optimize.AggressiveMergingPlugin(),
];

var prodVendors = [
    "babel-polyfill",
    "jquery",
    "react",
    "react-router",
    "react-redux",
    "react-slick",
    "react-select",
    "redux",
    "lodash",
    "redux-thunk",
    "redux-promise-middleware",
];
var devVendors = [
    "babel-polyfill",
    "jquery",
    "react",
    "react-router",
    "react-redux",
    "react-slick",
    "react-select",
    "redux",
    "lodash",
    "redux-logger",
    "redux-thunk",
    "redux-promise-middleware",
];
var localVendors = [
    "babel-polyfill",
    "jquery",
    "react",
    "react-router",
    "react-redux",
    "react-slick",
    "react-select",
    "redux",
    "lodash",
    "redux-logger",
    "redux-thunk",
    "redux-promise-middleware",
];

var prodPlugins = commonPlugins.concat(prodExplicitPlugins);
var devPlugins = commonPlugins.concat(prodExplicitPlugins);

var copyPlugins = [
    new CopyWebpackPlugin([
        { from: prod ? "src/prod" : "src/dev", to: "dist" },
        { from: "src/commons", to: "dist" },
        { from: "src/chaayos", to: "dist/chaayos" },
        { from: "src/css", to: "dist/css" },
        { from: "src/fonts", to: "dist/fonts" },
        { from: "src/img", to: "dist/img" },
        {from: "src/js", to: "dist/js"}
    ]),
];

module.exports = [
    {
        context: path.join(__dirname, "src"),
        devtool: !prod ? "inline-sourcemap" : null,
        entry: {
            vendor: prod ? prodVendors : dev ? devVendors : localVendors,
            home: ["./js/home.js"],
        },
        module: {
            rules: [
                {
                    test: /\.js$/,
                    enforce: "pre",
                    loader: "eslint-loader",
                    options: {
                        emitWarning: true,
                    },
                },
                { test: /\.hbs/, loader: "handlebars-loader" },
            ],
            noParse: ["jquery"].map(function(name) {
                return path.join(__dirname, "node_modules", name);
            }),
            loaders: [
                {
                    test: /\.js?$/,
                    exclude: /(node_modules|bower_components)/,
                    loader: "babel-loader",
                    query: {
                        presets: ["react", "es2015", "stage-0"],
                        plugins: [
                            "react-html-attrs",
                            "transform-class-properties",
                            "transform-decorators-legacy",
                        ],
                    },
                },
                { test: /\.hbs/, loader: "handlebars-loader" },
            ],
        },
        output: {
            path: path.join(__dirname, "dist"),
            filename: prod
                ? "[name].[chunkhash].js"
                : dev
                ? "[name].[chunkhash].js"
                : "[name].min.js",
            crossOriginLoading: "anonymous",
        },
        plugins: prod ? prodPlugins : dev ? devPlugins : commonPlugins,
    },
    {
        plugins: copyPlugins,
        entry: {},
        output: {
            filename: "bundle",
        },
    },
    {
        context: path.join(__dirname, "src"),
        devtool: !prod ? "inline-sourcemap" : null,
        entry: {
            sw: ["./js/sw.js"],
        },
        module: {
            rules: [
                {
                    test: /\.js$/,
                    enforce: "pre",
                    loader: "eslint-loader",
                    options: {
                        emitWarning: true,
                    },
                },
            ],
            noParse: ["jquery"].map(function(name) {
                return path.join(__dirname, "node_modules", name);
            }),
            loaders: [
                {
                    test: /\.js?$/,
                    exclude: /(node_modules|bower_components)/,
                    loader: "babel-loader",
                    query: {
                        presets: ["react", "es2015", "stage-0"],
                        plugins: [
                            "react-html-attrs",
                            "transform-class-properties",
                            "transform-decorators-legacy",
                        ],
                    },
                },
            ],
        },
        output: {
            path: path.join(__dirname, "dist"),
            filename: "[name].js",
            crossOriginLoading: "anonymous",
        },
        plugins: prodExplicitPlugins,
    },
];
