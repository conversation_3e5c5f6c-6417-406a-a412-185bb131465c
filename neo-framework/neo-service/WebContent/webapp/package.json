{"name": "web-ordering", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "webpack-dev-server --content-base dist --inline --hot --progress --colors --port 8585 --history-api-fallback", "test": "echo \"Error: no test specified\" && exit 1", "prod": "node server.js production"}, "author": "rahul", "license": "ISC", "dependencies": {"axios": "^0.15.2", "babel-core": "^6.23.1", "babel-loader": "^6.3.2", "babel-plugin-add-module-exports": "*", "babel-plugin-react-html-attrs": "*", "babel-plugin-transform-class-properties": "^6.23.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-polyfill": "^6.23.0", "babel-preset-es2015": "^6.22.0", "babel-preset-react": "^6.23.0", "babel-preset-stage-0": "^6.22.0", "basic-react-timepicker": "^1.0.0", "clean-webpack-plugin": "^0.1.15", "compression": "^1.6.2", "copy-webpack-plugin": "^4.0.1", "ejs-loader": "^0.3.0", "express": "^4.14.1", "firebase": "^3.7.1", "flux": "^2.1.1", "helmet": "^3.12.1", "history": "^4.2.0", "html-webpack-plugin": "^2.28.0", "html2canvas": "^1.4.1", "if-env": "^1.0.0", "inline-manifest-webpack-plugin": "^3.0.1", "jquery": "^3.1.1", "lodash": "^4.17.4", "nuka-carousel": "^2.0.4", "promise-polyfill": "^6.0.2", "react": "^15.4.2", "react-datepicker": "0.40.0", "react-dom": "^15.4.2", "react-dropdown": "^1.9.2", "react-otp-input": "^2.3.0", "react-redux": "^4.4.5", "react-router": "^2.8.1", "react-select": "^1.0.0-rc.3", "react-share": "^3.0.0", "react-slick": "^0.14.6", "redux": "^3.6.0", "redux-logger": "^2.8.1", "redux-promise-middleware": "^4.1.0", "redux-thunk": "^2.1.0", "request": "^2.87.0", "sw-toolbox": "^3.6.0", "webpack": "^1.13.3", "webpack-cli": "^2.1.3", "webpack-dev-server": "^1.16.3", "webpack-md5-hash": "0.0.5"}, "devDependencies": {"assets-webpack-plugin": "^3.9.10", "handlebars": "^4.1.2", "handlebars-loader": "^1.7.1", "preload-webpack-plugin": "^2.3.0", "resource-hints-webpack-plugin": "0.0.2", "webpack-manifest-plugin": "^2.0.4"}}