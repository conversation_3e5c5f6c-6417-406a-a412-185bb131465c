

*{
  font-family: 'Nunito';
}





/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------ChaayosDestopLayout.js---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

.maincontainer {
  position: relative;
  width: 100%;
  margin: 0vw 0vw -0.1945vw 0vw;
  }
  
.mainChaayosSelectLogo {
  position: absolute;
  height: 13vh;
  width: 20vw;
  left: 75vw;
  }
  
.mainfirstimg  {

    width: 81vw;
    height: 105vh;
    margin-left: 19vw;
  
  
  }
  .mainchaayosSelectText1 {
  position: absolute;
  top: 23vh;
  font-size: 8vh;
  color: #0E8B45;
  font-weight: bolder;
  left: 5vw;
  }
  
  .mainchaayosSelectText2 {
  position: absolute;
  top: 45vh;
  font-size: 3vw;
  color: #0E8B45;
  left: 5vw;
  font-weight: bold;
  }
  
  .mainchaayosSelectText3 {
  position: absolute;
  top: 53vh;
  font-size: 3vmin;
  color: black;
  font-weight: bolder;
  left: 5vw;
  }
  .mainchaayosSelectText4{
    position: absolute;
    top: 57vh;
    font-size: 2vmin;
    left: 5vw;
  }

  .mainbenefits {

    overflow: hidden;
    position: absolute;
    top: 35vh;
    left: 5vw;
    line-height: 12vh;
    height: 4.2vw;
    font-size: 4vw;
    font-weight: bold;
  
  }
  
  .mainbenefits ul {
  
    animation: scrollUp 6s linear infinite ;
   
    
  }
  .mainbenefits ul li {
    opacity: 1;
  
    list-style: none;
  }
  
  
  
  @keyframes scrollUp {
    from {
     
        transform: translateY(0);
    }
    to {
        transform: translateY(-80%);
    }
  }
  

/*----------------------------------------*/

.mainsecondContainer{
  position: relative;
    height: 200vh;
    width: 100vw;
}

.mainellipse{
  
  position: absolute;
  top: -36vh;
  height: 208vh;
  width: 90vw;   
}

.maincircles {
position: relative;
display: flex;
flex-wrap: wrap;
grid-template-columns: auto auto auto;
top: 28vh;
justify-content: space-between;
margin: 0vh 10vw;
}

.mainCircleWithText{
  text-align: center;
  width: 30vmin;
}
.mainCircle1 {
/* filter: url(drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.15))); */
height: 30vmin;
width: 30vmin;
border-radius: 50%;
background-image: url(./../../../img/ChaayosSelectMembership_images/gradient_image.png);
display: flex;
border: 1.5vmin solid #FFFFFF;
box-shadow: 0px 2px 4px rgb(0 0 0 / 15%);
background-repeat: no-repeat;
background-position: center;
justify-content: center;
}
  
.mainWCCS{
  color: #0E8B45;
  font-size: 8vh;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
  margin: 0vh 0vw 10vh;

}

.maingifs {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}
.maingifWithText{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 16vw;
  justify-content: center;
  align-items: center;
  text-align:center;
   
}
.maingif {
  box-shadow: 0px 2px 4px rgb(0 0 0 / 15%);
  border-radius: 15px;
  padding: 1vw;
  height: 35vh;
  width: 16vw;
  background: white;
  }


  
.maingifheadings {
  font-weight: bold;
  font-size: 4vmin;
  margin: 5vh 0vw 3vh;
  }


.mainSubscribebtn {
  position: absolute;

  width: 27%;
  height:9vh;

  top: 65%;
  left: 5%;
  z-index:3;

  text-overflow: clip;
  background-color: #0E8B45;
  color: white;
  font-size: 2vw;

  border: none;
  cursor: pointer;
  border-radius: 108px;

  
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow:10px 14px 30px rgba(0, 0, 0, 0.2)

    
}

.mainSubscribebtn:hover,.mainsavingCalcSubscribeBtn:hover{
  background-color: #C6E3BA !important;
}
.mainSubscribebtn:active,.mainsavingCalcSubscribeBtn:active{
  background-color: #383838 !important;
}




.mainSecondarySubscribeBtn{

  width: 18vw;
  top: 123%;
  left: 50%;
  font-size:1.5vw;
  left: 50%;
  transform: translate(-50%, -50%)

}

.mainSecondarySecondSubscribeBtn{
  position: unset;
  margin: 11vh 50vw;
  transform: translate(-50%,0%);
}

.mainAboutUsImg{
  

  width: 30vw;


}

.mainAboutUsHeading,.mainfourthimgText{
  color: #0E8B45;
  font-size: 8vh;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
  margin: 10vh 0vw;
}

.mainfourthimgText{
  color: #0E8B45;
  font-size: 8vh;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
  margin: 20vh 0vw -1vh;
}


.mainAboutUsLeaves{
  position: absolute;
    height: 117vh;
    width: 56vw;
    border-radius: 100%;
  
}
.mainAboutUsDiscription{
  display: flex;
  flex-direction: row;
  gap: 30vw;
  height: 82vh;
  align-items: center;
}
.mainAboutUsText{
  height: 70vh;
  width: 35vw;
  border: 2px dashed #577C3A;
  box-shadow: inset 0px 41px 16px rgb(0 0 0 / 1%), inset 0px 23px 14px rgb(0 0 0 / 5%), inset 0px 10px 10px rgb(0 0 0 / 9%), inset 0px 3px 6px rgb(0 0 0 / 10%);
  border-radius: 20px;
  font-size: 2.6vmin;
  padding: 4vmin;
  overflow-y: auto;
  align-self: self-start;
}



.mainfourthimg{ 
  width:100%;
  margin:10vh 0vw 0vh;
  padding: 0vh 2vw 0vh 5vw;
} 

.mainfourthimg.mobileView{
  display: none;
}


.mainOpenModal{
  position: fixed;
  top: 90vh;
  height: 8vh;
  border-radius: 8px;
  left: 90vw;
  z-index: 5;

}

.maintooltip{
  background-color:white;
  border-radius:8px;
  z-index:10;
  height: 7vh;
  display: flex;
  color: black;
  align-items: center;
  justify-content: center;
  width: 18vw;
  position:fixed;
  top:85vh;
  left:71vw;
  filter: drop-shadow(-4px -4px 8px rgba(0, 0, 0, 0.26));
  

}

 .maintooltip::after{

  content:  "";
  position: absolute;
  top: 51%;
  transform: skew(-62deg,47deg);
  left: 71%;
  border-width: 3.3vmin;
  border-style: solid;
  border-color: white transparent transparent transparent;
  z-index: -1;
   
    
 }
.maintooltip>p{
  overflow: hidden; /* Ensures the content is not revealed until the animation */
  border-right: .15em solid transparent; /* The typwriter cursor */
  white-space: nowrap; /* Keeps the content on a single line */
  margin: 0 1vw; /* Gives that scrolling effect as the typing happens */
  
  /* animation: typing 3s steps(40, end), blink-caret .75s step-end backwards; */
   
  animation: typing 5s steps(60), blink-caret 5s;
  
  }
@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}
@keyframes blink-caret {
  from, to { border-color: transparent }
  80% { border-color: black; }
 

}





/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Faq Styles---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/


.mainfaqContainer{
    position:relative;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

.mainfaqHeading{

  border-bottom: #777;
  padding: 20px 60px;
  font-size: 8vh;
  color: #0E8B45;
  font-weight: bolder;
  text-align:center;
  margin-bottom: 3vh;
}



.mainfaqUpArrow{
  
  margin: 2.5vh 1.5vw;

  height: 1.5vh;
}
.mainfaqDownArrow{
  margin: 2.5vh 1.5vw;
  height: 1.5vh;
}





.mainfaqQues
{
    background-color: #F3F3F3;
    margin: 0vh 8vw 7vh;
    box-shadow: -20px -20px 50px #ffffff, 20px 20px 50px #d2d2d2;
    border-radius: 14px;
   
    
}

.mainfaqOne{
  position: relative;
  display: flex;
  flex-direction: row
}

.mainfaqPage {
line-height: 3vh;
color: #444;
cursor: pointer;
padding: 2vh 1.5vw 2vh;
width: 100%;
border: none;
outline: none;
transition: 0.4s;
font-size:3vh; 
}

.mainfaqLeaves{
  position:absolute;
  height:58vmin;
  top: 52vh
}



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Review.js---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

.mainreviewtestimonialWrapper::-webkit-scrollbar{
  display: none;
}

.mainreviewtestimonialWrapper{
  display:flex;
  margin:5vmin;
  flex-direction:row;
  overflow:auto;
  scrollbar-width: none;
  height:60vh;
  padding: 6vh;
  margin-left:-2vmin;
  position:relative



}

.mainreviewTestimonials{  

position: relative;
display: flex;
flex-direction: column;
background: #F3F3F3;
box-shadow: -20px -20px 50px #FFFFFF, 20px 20px 50px #D2D2D2;
z-index:1;
padding: 2vh 3vw;
border-radius: 43px;
margin:3vh 5vw;
}

.mainreviewImg{
  position: absolute;
  height: 32%;
  width: 25%;
  border: 1px;
  border-radius: 50%;
  top: -15%;
  left: 10%;
}

.mainreviewRating{

  /* position: absolute;
  left: 60%;
  top: 6%; */
  text-align: end;
  font-size: 2.5vw; 

}

.mainreviewRemainingPart{
  margin-top:3vh
  }  

.mainreviewName{
  font-weight:bolder;
  font-size: 2vw;
  text-align: center;
  padding: 0vh 0vh 1vh 0vh

}

.mainreviewDiscription {
  font-size: 1.7vmin;
  text-align:center;
  width:15vw;


}
.mainreviewRating img{
  height: 20px;
}

.mainreviewLeaves{
position: absolute;
height: 61vmin;
transform: rotate(-92deg);
left: 53vw;
top: -17vh

}








/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Footer styles---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/





.mainfooterContainer{
  position:relative;
  background-color:#F7F7F8;
  height:46vh;
  z-index:1


}

.mainfooterRow{
  display:flex;
  flex-direction:row
}

.mainfooterCol{
  display:flex;
  flex-direction:column
}

.mainfooterHeading{
  font-weight:Bolder;
  font-size: 7vh;
  margin-top: 5vh;
  color:#0E8B45
}

.mainfooterImg{
  /* // position:absolute; */
  height: 100%;
  /* width: 30%; */
  border: 1px;
  border-radius: 50%;
  margin:0 0.5vw
}
.mainfooterAppStores{
  
  height: 6.5vh;
  width: 24vw;
  justify-content: center;
  
}
.mainfooterFirst{
  align-items:center;
  font-size:1vw;
  width:40%

}
.mainfooterSecond{
  /* position:absolute; */
 
 width:20%
}


.mainfooterThird{
  /* position:absolute; */

 width:20%
}

.mainfooterFourth{
  /* position:absolute; */

 width:20%
}
.mainfooterSubHeading{
  font-weight:Bolder;
  font-size: 4vh;
  margin-top: 5vh;
  margin-bottom: 2vh;
  color:#0E8B45
}
.mainfooterHeading img{

  width:16vw;

}
.mainfooterSubSubHeading{
  font-size:3vh;
  margin:1vh 1vh 1vh 0;

}

.mainfooterCopyright{
  background-color: #F7F7F8;
  text-align: center;
  padding: 2vh;
  font-size:2vh
}

.mainfooterLeaves{
  position: absolute;
  height: 60vmin;
  top: -37vh;
  left: 87vw;
  transform: rotate(205deg)
}




/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Saving Calculator Styles---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/


.mainSavingCalcBtn{
  opacity: 0;
 
}
.scrollToTopSavingCalcBtn,.DesktopViewSavingCalcBtn{
  opacity: 1;
 
}



.mainsavingCalcOverlay{
  position:fixed;
  height:100%;
  width:100%;
  background-color:rgba(255,255,255,0.9);
    /* background-color:rgba(5, 255, 255, 1.9); */
  z-index:10;
  
  top:0%
  
}
.mainsavingCalcModalContainer{
  width:fit-content;
  height:fit-content;
  top:10%;
  position:relative;
  left:25%;
  right:25%;
  display:grid;
  background-color:white;
  grid-template-columns: auto auto;
  padding:6vh 2vw;
  border: 26px solid #D9D9D9;
  border-radius: 30px;
}
.mainsavingCalcfirstbox{
    margin:0vh 0vw 4vh
}
.mainsavingCalcheading{
  font-size: 3.5vmin;
  font-weight: bolder;
  margin-bottom: 0.5vh;
  width:18vw;
}
.mainsavingCalcheadingDiscription{
  font-size: 1.7vmin;

}

.mainsavingCalcRightCol{
  display: flex;
  justify-content: flex-end
}

.mainsavingCalcSelectOptionPlans{
  height: 4vmin;
  margin:0vh 0vw 0vh 1vw;
  display: flex;
  align-items: end;
}

.mainsavingCalcSelectOptionPlans select{
 border: 0.5px solid #0E8B45;
 border-radius: 4px;
 font-size:2.5vmin;
 margin:0vh 1.5vh 0vw 1vw;
 height:4vh;
}

.mainsavingCalcSecondbox{
  font-size: 2.5vmin;
  font-weight:bold
  
}

.mainsavingCalcSlider{
  width: 100%;
  padding:2vh 0vw 1vh;
}
.mainSavingCalcInput{
  border-radius: 8px;
  text-align: center;
  border: 1px solid;
}
 
.mainsavingCalcRightSavingBox{
  padding:3vh 2vw;
  font-size: 2vmin;
  background: #F9FAFE;
  box-shadow:18px 39px 17px rgba(0, 0, 0, 0.01), 10px 22px 14px rgba(0, 0, 0, 0.05), 5px 10px 11px rgba(0, 0, 0, 0.09), 1px 2px 6px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  width:60%; 
  display: flex;
  flex-direction:column
}
.mainsavingCalcSubscribeBtn{
  display: flex;
  justify-content: center;
  padding:8px 16px;
  background:#0E8B45;
  border-radius: 50px;
  width: 12vw;
  color: white;
  border:none;
  align-items: center;
  font-size:2.5vmin;
  cursor: pointer;
}
.mainsavingCalcCrossBtn{
  position: absolute;
  right: 0%;
}
  

/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Underline effect---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

.hover-underline-animation {
  display: inline-block;
  position: relative;
  width: fit-content;
  
}

.hover-underline-animation::after {
  content: '';
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 2px;
  bottom: 0;
  left: 0;
  background-color:black;
  transform-origin: bottom right;
  transition: transform 0.25s ease-out;
}

.hover-underline-animation:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}


/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Range slider---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/


input[type=range] {

  margin: 18px 0;
  width: 100%;
  -webkit-appearance: none;
}
input[type=range]:focus {
  outline: none;
}
input[type=range]::-webkit-slider-runnable-track {
  width: 100%;
  height: 8.4px;
  cursor: pointer;
  
  
  background: #CECECE;;
  border-radius: 10px;
  border: 1px solid #B9B9B9;
}
input[type=range]::-webkit-slider-thumb {
 
  border: 1px solid #0E8B45;
  height: 25px;
  width: 25px;
  border-radius: 50px;
  background: #0E8B45;
  cursor: pointer;
  -webkit-appearance: none;
  margin-top: -10px;
}
input[type=range]:focus::-webkit-slider-runnable-track {
  background: #CECECE;;
}




/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/





@media only screen and (max-width: 803px) {

.mainChaayosSelectLogo {
  position: absolute;
  height: 7vh;
  width: 50vw;
  left: 48vw;
}

.mainfirstimg {
  width: 155vw;
  height: 53vh;
  margin-left: -55.5vw;
}

.mainchaayosSelectText1{
  position: absolute;
  top: 105%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3vh;
  color: rgb(14, 139, 69);
  font-weight: bolder;
 
}

.mainbenefits ul{
  text-align: center;
}
.mainchaayosSelectText2{
  position: absolute;
  top: 123%;
  font-size: 2.4vh;
  transform: translate(-50%, -50%);;
  left: 50%;
  font-weight: bold;
}

.mainchaayosSelectText3{
  position: absolute;
  top: 129%;
  font-size: 1.6vh;
  color: black;
  font-weight: bolder;
  left: 45%;
  transform: translate(-50%, -50%);
}


.mainchaayosSelectText4{
  position: absolute;
  top: 129%;
  font-size: 1.6vh;
  left: 67%;

  transform: translate(-50%, -50%);
}
.mainchaayosSelectText4::before{
  content: '(';
}
.mainchaayosSelectText4::after{
  content: ')';
}
.mainsecondContainer{
  height: 142vh;
}
.mainbenefits{
  overflow: hidden;
  position: absolute;
  top: 114%;
  left: 50%;
  transform: translate(-50%, -50%);
  line-height: inherit;
  height: 4vh;
  width: 100vw;
  font-size: 3vh;
  font-weight: bold;
}

.mainSubscribebtn{
  position: absolute;
  width: 85vw;
  height: 7vh;
  text-align: center;
  font-size: 2.4vh;
  top: 77vh;
  left: 50%;
  transform: translate(-50%,-50%);
  z-index: 3;
  text-overflow: clip;
  background-color: #0E8B45;
  color: white;

  border: none;
  cursor: pointer;
  border-radius: 108px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: rgb(0 0 0 / 20%) 10px 14px 30px;
}
.mainSubscribebtn:hover{
  background-color: #0E8B45 !important;
}
.mainSubscribebtn:active{
  background-color: #383838 !important;
}

.mainSecondarySubscribeBtn{
  left: 43% !important;
  transform: translate(-50%,-50%);
  width: 85vw;
  height: 7vh;
  text-align: center;
  font-size: 2.4vh;
}

.mainellipse {
  position: absolute;
  top: 12vh;
  height: 69vh;
  width: 91vw;
}

.mainCircle1 {
  height: 25vmin;
  width: 25vmin;
}
.maincircles {
  position: relative;
  display: flex;
  grid-template-columns: auto auto auto;
  top: 38vh;
  margin: 0vh 5vw;
}
.mainCircleWithText{
  width: 25vmin;
}
.mainCircle1Text{
  margin: 2vh 1vw !important;
 font-size: 1.6vh !important;
}

.mainWCCS{
  font-size: 3vh;
  margin: 2vh 0vw;
}
.mainwhyChaayos{
  top: 72vh !important
}

.mainAboutUsHeading{
  margin-bottom: 5vh;
  font-size: 3vh;
}
.mainAboutUsDiscription{
  height: auto;
  gap: 0vw;
}
.mainAboutUsText{
  height: auto;
  width: 56vw;
  font-size: 2.8vmin;
  background-color: white;
  position: relative;
  z-index: 1;

}
.mainAboutUsImg{
  width: 40vw;
  position: inherit;
  z-index: 1;
}
.mainAboutUsLeaves{
  position: absolute;
  height: 66vh;
  width: 106vw;
  left: -21vh;
}
.mainfourthimg{ 
  
  display: none;
} 
.mainfourthimg.mobileView{
  display: block;
  width: 90vw;
  margin: 0vh 3vw 8vh;
  height: 70vh;
}
.mainfourthimgText.mobileView{
  display: block;
  font-size: 3vh;
  margin: 3vh 0vw;
  color: #0E8B45;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
}
.mainreviewLeaves {

  height: 44vmin;
 
  left: 65vw;
  top: -5vh;
}
.maingifs{
  flex-direction: column;
  margin: 0vh 10vw;
 
}
.maingifWithText{
  display: flex;
  width: 100%;
  flex-direction: row;
  margin: 2vh 0vw 2vh -2.5vw;
}
.maingif {
  height: 25vmin;
  width: 25vmin;
}
.maingifText{
  margin: 0vh 4vw;
  text-align: left;
}
.maingifheadings{
  font-weight: 700 !important;
 
  margin: 3vh 0vw 0.8vh;
  font-size: 2.4vh;
  width: 65vw;
}
.maingifSubheadings{
  font-size: 1.6vh !important;
  width: 55vw;
}
.mainSecondaryFirstSubscribeBtn{
  top: 66vh;

}
.mainSecondarySecondSubscribeBtn{
  position: unset;
}

.mainfaqQues{
  
  padding: 0vh 2vw;
}
.mainfaqPage{
  font-size: 2.4vh;
  padding: 2vh 1.5vw 2vh;
}
.mainfaqans
{
  font-size: 1.6vh !important;
  
}
.mainOpenModal{
  top: 83vh;
  left:80%
}

.mainreviewtestimonialWrapper{
  /* height: 52vh; */
}
.mainreviewTestimonials {
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: 70vmin;
  border-radius: 25px;
}
.mainreviewName{
  font-size: 5vw;
  padding: 0vh 0vh 1vh 0vh;
}

.mainreviewImg {

  height: 15vmin;
  width: 15vmin;
  top: -10%;
  left: 8%;
}
.mainreviewRating {

  text-align: end;
  /* left: 48%;
  top: 2%; */
}

.mainreviewDiscription{
  width: inherit;
  line-height: 2.4vh;
  font-size: 1.6vh;
}
.mainfaqHeading {

  font-size: 3vh;
  margin-bottom: 3vh;

}
.mainfaqUpArrow,.mainfaqDownArrow{
  
    height: 1.3vh;

}
.mainfooterContainer{
  height: 30vh;
  
  padding: 0vh 2vw;
}
.mainfooterHeading{
  font-size: 3vh;
  margin: 4.5vh 0vw 1vh;
}
.mainfooterHeading img{

    width: 25vw;

}
.mainfooterHeadingText{
    width: 90% !important;
    font-size: 1.5vh;
    display: none;
}
.mainfooterSocialMediaIcons{
  height: 5vh !important;
  margin-top: 0vh !important;
}

.mainfooterImg {
  height: 100% !important;
  /* width: 60% !important; */

}
.mainfooterSubHeading {
  font-size: 2vh;
}
.mainfooterSubSubHeading {
  font-size: 1.5vh;

}
.mainfooterSecond{
  /* position:absolute; */

 width:25%
}
.mainfooterThird{
  /* position:absolute; */

 width:15%
}

.mainfooterLeaves{
 
    top: -20vh;
    left: 70vw;

}
.mainfooterAppStores{
  
  height: 11vh;
  display: flex;
  flex-direction: column;
  width: 55vw;
  transform: translateX(21%);
  
}
.mainfooterCopyright {
  font-size: 1.5vh;
}


.mainfooterAppstoreIcons{
  width: 60% !important;
}
.mainSavingCalcLeftSecondRow
{
  padding-top: 4vh;
}
.mainSavingCalcLeftFourthRow{
  align-self: end;
}
.mainsavingCalcOverlay{
  display: flex;
  justify-content: center;
  align-items: center;
}
.mainsavingCalcModalContainer{
  padding: 2vh 2vw;
  border: 10px solid #D9D9D9;
  position: static;
  margin: 0vh 3vw;
}
.mainsavingCalcheading{
  width: 38vw
}
.mainsavingCalcheadingDiscription{
  font-size: 2.4vmin;
}
.mainsavingCalcSelectOptionPlans{
  display: flex;
  justify-content: center;
  height: 7vmin;
  margin: 0vh 0vw 0vh 1vw;
  align-items: flex-end !important;
}
.mainsavingCalcSelectOptionPlans p{
  font-size: 3.8vmin !important;
  height: 7vmin;
  display: flex;
  align-items: center;
}
.mainsavingCalcSelectOptionPlans select{
  font-size: 2.5vmin;
  margin: 0vh 2vw 0vw 0vw !important;
  height: 2.5vh !important;
 

}
.mainsavingCalcCrossBtn{
  right: 6%;
  /* top: 27%; */
}

input[type=range]{
  margin: 0;
  width: 73%;
}
.mainsavingCalcRightCol,.mainsavingCalcSubscribeBtn {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
  margin: 0;
}
.mainsavingCalcRightSavingBox {
  padding: 2vh 2vw 0vh;
  width: 100%;
  margin-right: 3vw;
}
.mainsavingCalcfirstbox{
  margin:0vh 0vw 1vh
}
.mainsavingCalcRightSavingBox p:first-child{
  text-align: center;
  font-size: 2.9vmin;
}


.mainsavingCalcRightSavingBox>div p:first-child{

  font-size: 2.8vmin !important;
  width: 16vw;
  text-align: center;
  width: 100%;
  padding: 1vh 0vw;

}

.mainsavingCalcSubscribeBtn{
  width: 30vw;
  font-size: 3vmin;
  transform: translateY(-50%);
}
.mainsavingCalcSubscribeBtnDiv{
  margin: 0% 0% 0% 107% !important;
}
.maintooltip {
  height: 7vh;
  width: 70vw;
  top: 79vh;

    left: 4vw;
}
.mainsavingCalcRightColThirdRow{
  width: 30vw !important;
}

.maintooltip::after {
  border-width: 5vmin;
  top: 47%;
  transform: skew(-39deg,21deg);
  left: 89%;
}

}