/************************************************************************/
/*                              common styles                           */
/************************************************************************/

/**************** generic styles *****************/
* {
    cursor: pointer;
    -webkit-text-size-adjust: none;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent
}

:not(input):not(textarea):not(button) {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, canvas, caption, center, cite, code, dd, del,
details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header,
html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section,
small, span, strike, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, tt, u, ul, var, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    box-sizing: border-box;
}

a {
    text-decoration: none;
    color: #333
}

a:focus {
    outline: 0;
}

a:active {
    background: transparent
}

html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    -webkit-transition: .2s linear;
    transition: .2s linear;
    font-family: 'Varela Round', sans-serif;
    background: url("../img/bgDoodle.png") #fbf8eb top left repeat;
    /*background: #fbf8eb;*/
    font-size: 12px;
    height: 100%;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    font-smoothing: antialiased;
}

input {
    outline: none;
}

.align-right {
    text-align: right;
}

.right {
    float: right;
}

.left {
    float: left;
}

.text-center {
    text-align: center;
}

.ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.clear {
    clear: both;
}

.rel {
    position: relative;
}

.text-right {
    text-align: right;
}

.btn {
    text-align: center;
    box-shadow: 0 1px 2px 0 #c8c8c3;
}

.btn-default {
    border: #5e7e47 1px solid;
    background: #fff;
    color: #5e7e47;
}

.btn-primary {
    background: #5e7e47;
    color: #fff;
    border: #5e7e47 1px solid;
    margin: 10px 20px;
    min-width: 90px;
}

.alert.error {
    background: rgba(218, 105, 92, 0.79);
    border: rgba(218, 65, 50, 0.79) 1px solid;
}

.alert.info {
    background: rgba(113, 215, 218, 0.79);
    border: rgba(77, 214, 218, 0.79) 1px solid;
}

.ptop50 {
    padding-top: 50px;
}

.pairBtnWrapper {
    text-align: center;
    position: relative;
}

.pairBtnWrapper .leftBtn {
    position: absolute;
}

/************** radio checkbox styling *****************/
input[type=checkbox]:not(old),
input[type=radio   ]:not(old) {
    width: 15px;
    margin: 0;
    padding: 0;
    font-size: 12px;
    opacity: 0;
}

input[type=checkbox]:not(old) + label,
input[type=radio   ]:not(old) + label {
    display: inline-block;
    margin-left: -25px;
    line-height: 27px;
    vertical-align: top;
}

input[type=checkbox]:not(old) + label > span,
input[type=radio   ]:not(old) + label > span {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin: 4px 10px;
    border: 2px solid rgb(192, 192, 192);
    vertical-align: bottom;
    -webkit-transition: all 0.25s linear;
}

input[type=radio   ]:not(old) + label > span {
    border-radius: 50%;
}

input[type=checkbox]:not(old):checked + label > span,
input[type=radio   ]:not(old):checked + label > span {
    border: #5e7e47 2px solid;
    -webkit-transition: all 0.25s linear;
}

input[type=checkbox]:not(old):checked + label > span:before {
    display: block;
    width: 1em;
    color: #5e7e47;
    font-size: 21px;
    line-height: 10px;
    text-shadow: 0 0 0.0714em #5e7e47;
    font-weight: bold;
    -webkit-transition: all 0.25s linear;
}

input[type=radio]:not(old):checked + label > span > span {
    display: block;
    width: 10px;
    height: 10px;
    margin: 3px;
    border: 1px solid rgb(115, 153, 77);
    border-radius: 50%;
    background: #5e7e47;
}

.noscroll {
    overflow: hidden !important;
}

#appRoot > div > div {
    box-shadow: none !important;
}

/************************ css loader ********************************/
.load8.loader, .load8.loader:after {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    text-align: center;
}

.load8.loader {
    margin: 35px auto;
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 2px solid rgba(189, 189, 189, 0.2);
    border-right: 2px solid rgba(189, 189, 189, 0.2);
    border-bottom: 2px solid rgba(189, 189, 189, 0.2);
    border-left: 2px solid #065904;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 .6s infinite linear;
    animation: load8 .6s infinite linear;
}

@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/************** main routing layout styles ******************/
.internetErrorContainer {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center;
    background: #fff;
    vertical-align: middle;
    -webkit-transition: all 0.25s linear;
    transition: all 0.25s linear;
}

.internetErrorContainer > img {
    height: 200px;
    margin: 50px 0 20px 0;
}

.internetErrorContainer .msg {
    font-size: 24px;
    padding: 0 0 20px 0;
}

/******************************sidebar styles****************************/
.sidebarWrapper {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 9999;
    pointer-events: auto;
}

.sidebarShadow {
    position: absolute;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.6);
    opacity: 0;
    will-change: opacity;
}

.sidebarContentWrapper {
    background: #fff;
    height: 100%;
    will-change: transform;
    box-shadow: 3px 0 8px 1px rgba(0, 0, 0, .4);
    -webkit-transform: translate3d(-102%, 0, 0);
    transform: translate3d(-102%, 0, 0);
    transition: .2s linear;
    -webkit-transition: .2s linear;
}

.sidebarContentWrapper .sideBarMenu.head {
    padding: 17px 10px;
    text-align: center;
}

.sidebarContentWrapper .sideBarMenu.head .sidebarLogo {
    width: 150px;
}

.sidebarContentWrapper .sideBarMenu.head .listIcon {
    margin: 0 13px 0 20px;
    height: 35px;
    display: inline-block;
    width: 35px;
    text-align: center;
    float: left;
    padding: 6px 0 0 0;
}

.sidebarContentWrapper .sideBarMenu .listIcon {
    margin: 0 13px -6px 0;
    height: 35px;
    display: inline-block;
    width: 35px;
    text-align: center;
}

.sidebarContentWrapper .sideBarMenu .listIcon img {
    margin: 1px 0 -6px 0;
    height: 27px;
}

.sidebarContentWrapper .sideBarMenu {
    display: block;
    color: #62635e;
    background: #fff;
}

.sidebarContentWrapper .static .sideBarMenu {
    color: #a6a6a0;
}

.sidebarContentWrapper .sideBarMenu a {
    text-decoration: none;
    display: block;
    color: #b3b3b3;
}

.sidebarContentWrapper .sidebarSeparator {
    height: 1px;
    background: #e4e4df;
    margin: 20px 0 20px 20px;
}

/**************** utility layout styles ***************/
.popupWrapper {
    position: fixed;
    background: #404040;
    color: #fff;
    text-align: center;
    z-index: 99999;
    will-change: transform;
}

.popupWrapper.error {
    background: #b55a5a;
}

.popupWrapper.info {
    background: rgba(244, 179, 64, 1);
}

.fullPageLoader {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-align: center;
    z-index: 99999;
}

.fullPageLoader .loaderWrapper {
    background: #fff;
    color: #000;
}

.lsMsgWrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #5e7e47;
    display: none;
    color: #fff;
    text-align: center;
    z-index: 9999999;
}

.lsMsgWrapper.active {
    display: flex;
    pointer-events: auto;
}

.lsMsgContainer {
    width: 80%;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    margin: auto;
}

.lsImgContainer {
    background-image: url(../img/rotate.png);
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    width: 30%;
    padding-bottom: 25.5%;
}

/******************************header styles*****************************/
.headerWrapper {
    background: #5e7e47;
    color: #fff;
    position: relative;
}

.headerBtn {
    color: #fff;
    cursor: pointer;
    display: inline-block;
    text-align: center;
}

/*.headerBtn .ion-navicon {
    color: #fff;
}*/

.headerLogo {
}

.headerWrapper .localityWrapper, .headerWrapper .outletWrapper {
    position: absolute;
    text-align: center;
}

.headerWrapper .localityWrapper .tagLine, .headerWrapper .outletWrapper .tagLine {
    text-transform: uppercase;
    text-align: center;
}

.headerWrapper .localityWrapper .downIcon, .headerWrapper .outletWrapper .downIcon {
    width: 17px;
    margin: 0 0 -2px 4px;
}

.headerWrapper .cartSizeLabel {
    position: absolute;
    background: #f0af3b;
    color: #5e7e47;
    line-height: normal;
    border: solid 1px #f9d99e;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
}

/******************* banner layout styles ***************************/
.imgSlide {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    color: #fff;
    cursor: pointer;
}

.siteTitle {
    text-align: center;
}

.slick-slider {
    overflow: hidden;
}

/*******************  home layout styles *************************/
.actionAreaContainer {
    margin-top: 10px;
}

.actionAreaHeader {
    text-align: center;
    text-transform: uppercase;
}

.actionAreaContainer .actionInputWrapper {
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
}

.actionAreaContainer .actionInputWrapper .radioLabels {
    /*width: 50%;*/
    width: 33.3%;
    display: inline-block;
    vertical-align: top;
    /*background: #fff;*/
    text-align: center;
    color: #62635e;
}

.actionAreaContainer .citySelector {
    text-align: center;
}

.actionAreaContainer .cityBtn {
    color: #aaa;
    display: inline-block;
}

.actionAreaContainer .cityBtn.active {
    border: #5e7e47 1px solid;
    color: #5e7e47;
    background: #fff;
}

.actionAreaContainer .localitySelector, .actionAreaContainer .outletSelector {
    padding-left: 10px;
}

.localitySelector, .outletSelector {
    background: #fff;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
}

.locationWrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #fff;
    opacity: 0;
    will-change: opacity;
    transition: .2s linear;
    -webkit-transition: .2s linear;
}

.locationWrapper.active {
    opacity: 1;
}

.locationWrapper .expandHead {
    display: block;
    height: 50px;
    line-height: 50px;
    border-bottom: #ddd 1px solid;
}

.locationWrapper .backArrow {
    position: absolute;
    width: 50px;
    text-align: center;
    font-size: 16px;
}

.locationWrapper .headLine {
    text-align: center;
    font-size: 16px;
}

.locationWrapper .Select-control {
    border: none;
}

.locationWrapper .Select-input > input {
    vertical-align: top;
}

.locationWrapper .Select-menu-outer {
    border: none;
}

.locationWrapper .Select-menu {
    overflow-y: auto;
}

.locationWrapper .outletListItem {
    border-bottom: #ddd 1px solid;
    background: #fff;
    color: #000 !important;
}

.actionAreaContainer .errorMessage {
    border: red 1px solid;
    display: none;
}

/********************** outlet menu layout ***********************/
.navTabContainer {
    opacity: 0;
    position: relative;
    white-space: nowrap;
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
    box-shadow: 0 2px 3px -1px #bcbcb8;
    -webkit-user-select: none;
    -webkit-overflow-scrolling: touch;
    transition: .5s linear;
    -webkit-transition: .5s linear;
}

.navTabContainer.active {
    opacity: 1;
}

.navTabContainer .slider {
    position: absolute;
    bottom: 0;
    left: 0;
    background: #f0af3b;
    border-radius: 2px;
    -webkit-transition: .6s;
    transition: .6s;
    -webkit-transform: translateX(4px);
    -ms-transform: translateX(4px);
    transform: translateX(4px);
    will-change: transform;
}

.navTabContainer::-webkit-scrollbar {
    display: none;
}

.navTabContainer .navTab {
    margin: 0;
    color: #fff;
    text-transform: uppercase;
    text-align: center;
    -webkit-transition: .2s linear;
    transition: .2s linear;
}

.navTabContainer .navTab:first-child {
    margin-left: 0;
}

.menuWrapper {
    opacity: 0;
    -webkit-transition: .2s linear;
    transition: .2s linear;
}

.menuWrapper.active {
    opacity: 1;
}

.unitLoadingError {
    margin-top: 150px;
    text-align: center;
    font-size: 18px;
}

.unitLoadingError img {
    height: 100px;
    margin-bottom: 20px;
}

.fetchingData {
    position: absolute;
    width: 100%;
    top: 150px;
    text-align: center;
    opacity: 0;
    margin-top: 150px;
}

.fetchingData.active {
    opacity: 1;
    height: auto;
    margin-top: 150px;
    z-index: 111;
}

.fetchingData .loadingMessage {
    padding: 30px 20px 0 20px;
    font-size: 18px;
}

.menuContainer {
    position: absolute;
    width: 100%;
    z-index: 99;
    opacity: 0;
    transition: .5s linear;
    -webkit-transition: .2s linear;
}

.menuContainer.active {
    opacity: 1;
}

.menuContainer .menu .categoryHeader {
    color: #292826;
    text-transform: capitalize;
}

.staticHead {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 999;
    background: #5e7e47;
    will-change: transform;
    transition: .5s linear;
    -webkit-transition: .2s linear;
}

.staticHead.small {
    transform: translateY(-52px);
}

.productsWrapper {
}

.productsWrapper .productContainer {
    box-shadow: 0 1px 2px 0 #bcbcb8;
    margin-bottom: 10px;
    vertical-align: top;
    position: relative;
}

.productsWrapper .productContainer .productImage {
    background-size: cover;
    background-position: center;
    position: relative;
}

.productsWrapper .productContainer .productImage .stockOutWrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
}

.productsWrapper .productContainer .productImage .stockOutWrapper .stockOut {
    display: inline-block;
    background: #b55a5a;
    color: #fff;
    text-transform: uppercase;
    line-height: normal;
    position: absolute;
}

.productsWrapper .productContainer .productImage .tagWrapper {
    position: absolute;
    color: #fff;
    text-transform: uppercase;
}

.productsWrapper .productContainer .productImage .tagWrapper .tagName {
    background: #5e7e47;
}

.productsWrapper .productContainer .productTitleWrapper, .productsWrapper .productContainer .productDescriptionWrapper {
    position: relative;
}

.productsWrapper .productContainer .productTitle {
    text-transform: capitalize;
    color: #292825;
}

.productsWrapper .productContainer .productPrice {
    position: absolute;
    text-transform: capitalize;
    text-align: center;
    color: #292825;
}

.productsWrapper .productContainer .productDetail {
    background: #fff;
    clear: both;
    padding: 7px 0 0;
}

.productsWrapper .productContainer .productDetail .productDescription {
    color: #62635e;
}

.productsWrapper .productContainer .productDetail .recipeLoader {
    position: absolute;
    top: 5px;
    right: 10px;
    width: 56px;
    height: 32px;
    line-height: 32px;
}

.productsWrapper .productContainer .productDetail .addProductBtn {
    position: absolute;
    background: #fff;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    border: solid 2px #5e7e47;
    color: #5e7e47;
    text-align: center;
    text-transform: uppercase;
}

.productsWrapper .vegNonVegIcon {
    height: 15px;
    margin: 0 5px -2px 0px;
}

.productsWrapper .productContainer .rupeeIcon {
    height: 12px;
    margin: 0 5px -1px 0;
}

.productsWrapper .productContainer .customizationWrapper {
    position: relative;
}

.productsWrapper .productContainer .customizationWrapper .sizeCustomizationWrapper {
    border-top: #ddd 1px solid;
    /*text-transform: uppercase;*/
    position: relative;
}

/************** modal layout *********************/
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
}

.modal .modalBody {
    background: #fff;
    position: absolute;
    overflow: auto;
}

.modal .modalBody .modalCloseBtn {
    position: absolute;
    right: 0;
    top: 0;
    color: #5e7e47;
    text-align: center;
}

.modalTitle {
    text-align: center;
    color: #292826;
    margin: 0;
}

/************** customization modal layout *********************/
.customizationSection {
    padding: 10px;
    border-bottom: #ddd 1px solid;
}

.customizationSection.productHead {
    background: #f5f6f0;
    border-bottom: none;
}

.customizationSection .customizationHead {
    color: #62635e;
    text-transform: uppercase;
}

.customizationSection .constituent .productName {
    color: #292826;
}

.customizationSection .constituent .productDesc {
    color: #62635e;
}

.customizationSection .constituent .qtyWrapper {
    position: absolute;
    top: 0px;
    right: 0px;
    text-align: center;
    text-transform: uppercase;
}

.customizationSection .productName {
    color: #292826;
}

.customizationSection .productPrice {
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
}

.customizationSection .rupeeIcon {
    height: 12px;
    margin: 0 3px -1px 0;
}

.customizationSection .productDesc {
    color: #62635e;
}

.qtyWrapper {
    position: absolute;
    border-radius: 100px;
    background: #5e7e47;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    border: solid 2px #5e7e47;
    line-height: normal;
}

.customizationSection .constituent .qtyWrapper {
    background: transparent;
    box-shadow: none;
    border: none;
}

.qtyWrapper .incr, .qtyWrapper .dcr {
    text-align: center;
    display: inline-block;
    color: #5e7e47;
    background-color: #ffffff;
}

.qtyWrapper .qty {
    display: inline-block;
    text-align: center;
    color: #fff;
    background: #5e7e47;
}

.qtyWrapper .incr {
    float: right;
}

.qtyWrapper .dcr {
    float: left;
}

.customizationSection .dimensionBtn {
    display: inline-block;
}

.customizationSection .dimensionBtn > div {
    background: #f5f6f0;
    border: solid 1px #dedede;
    color: #000;
    text-align: center;
}

.customizationSection .dimensionBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px -2px 0;
}

.customizationSection .dimensionBtn > div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .sizeDescription {
    display: block;
    text-align: center;
    padding: 0 3px;
}

.customizationSection .menuProductBtn {
    display: inline-block;
}

.customizationSection .menuProductBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
}

.customizationSection .menuProductBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .addonBtn {
    display: inline-block;
    float: left;
}

.customizationSection .addonBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
}

.customizationSection .addonBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px -2px 0;
}

.customizationSection .addonBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .ingredientProductBtn {
    display: inline-block;
    float: left;
}

.customizationSection .ingredientProductBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
}

.customizationSection .ingredientProductBtn div img {
    vertical-align: text-bottom;
    margin: 0 5px 0 0;
}

.customizationSection .ingredientProductBtn div.active {
    background: #5e7e47;
    color: #fff;
}

.customizationSection .ingredientVariantBtn {
    display: inline-block;
    float: left;
}

.customizationSection .ingredientVariantBtn div {
    background: #ededed;
    color: #000;
    text-align: center;
}

.customizationSection .ingredientVariantBtn div svg {
    vertical-align: text-bottom;
    margin: 0 5px -3px 0;
}

.customizationSection .ingredientVariantBtn div.active {
    background: #5e7e47;
    color: #fff;
}

/**************** cart layout styles ***********************/
.cartHead {
    color: #292826;
    text-transform: uppercase;
}

.itemCountHead {
    color: #62635e;
    text-transform: uppercase;
}

.cartItemContainer {
    background: #fff;
    box-shadow: 0 1px 2px 0 #e4e4df;
}

.cartItemContainer .stockTag {
    display: inline-block;
    text-transform: uppercase;
}

.cartItemContainer .pic {
    display: inline-block;
    background-size: cover;
    background-position: center;
    float: left;
}

.cartItemContainer .itemTitle {
    color: #292826;
}

.cartItemContainer .itemPrice {
    position: absolute;
    top: 8px;
    right: 5px;
    text-align: right;
}

.cartItemContainer .itemDetail {
    color: #62635e;
}

.cartItemContainer .qtyWrapper {
    position: absolute;
    top: 0px;
    right: 0px;
    border-radius: 100px;
    background: #5e7e47;
    box-shadow: 0 1px 2px 0 #c8c8c3;
    border: solid 2px #5e7e47;
}

.cartItemContainer .qtyWrapper .incr, .cartItemContainer .qtyWrapper .dcr {
    text-align: center;
    display: inline-block;
    color: #5e7e47;
    background-color: #ffffff;
}

.cartItemContainer .qtyWrapper .incr {
    border-top-right-radius: 25px;
    border-bottom-right-radius: 25px;
    float: right;
}

.cartItemContainer .qtyWrapper .dcr {
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
    float: left;
}

.cartItemContainer .qtyWrapper .qty {
    display: inline-block;
    text-align: center;
    color: #fff;
    background: #5e7e47;
}

.cartItemContainer .actionContainer {

}

.cartItemContainer .editItem {
    color: #368101;
    text-decoration: underline;
    display: inline-block;
}

.cartItemContainer .removeItem {
    color: #368101;
    text-decoration: underline;
    display: inline-block;
    float: right;
}

.cartContainer .emptyCartHead {
    color: #292826;
}

.cartContainer .emptyCartTag {
    color: #62635e;
}

.cartItemContainer .orderRemark {
    border: none;
    color: #62635e;
}

.cartItemContainer .orderCoupon {
    border: none;
    text-transform: uppercase;
    color: #62635e;
}

.cartItemContainer .couponLink {
    position: absolute;
    top: 0;
    right: 0;
    color: #5e7e47;
    text-align: center;
}

.offerError {
    color: #b55a5a;
    padding: 0 10px;
}

.couponLogin {
    color: #5e7e47;
    padding: 0 5px;
    cursor: pointer;
    text-decoration: underline;
}

.campaignCoupon {
    margin: 5px;
    padding: 10px;
    background: aquamarine;
    border-radius: 3px;
    cursor: pointer;
}

.cartItemContainer .transactionDetails {
    color: #62635e;
}

.cartItemContainer .totalAmount {
    color: #292826;
}

.cartItemContainer .taxDetailWrapper {
    -webkit-transition: .2s linear;
    transition: .2s linear;
    height: 0px;
    display: none;
    background: #f0f0f0;
}

.cartItemContainer .taxDetailWrapper.open {
    height: auto;
    display: block;
    margin-bottom: 6px;
}

/************** mobile login layout styles ************/
.loginSectionTagline {
    text-align: center;
    color: #62635e;
}

.contactContainer {
    box-shadow: 0 1px 2px 0 #bcbcb8;
    background: #fff;
}

.contactContainer input {
    border: none;
    width: 100%;
}

.resendText {
    color: #62635e;
    text-align: center;
}

.resendLink {
    color: #5e7e47;
    margin-left: 5px;
}

/************* addresses style **************/
.addressesSubHead {
    text-transform: uppercase;
}

.addressContainer {
    background: #fff;
    position: relative;
    border-bottom: #ddd 1px solid;
}

.addressContainer:last-child {
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
}

.addressContainer.disabled .addressBtn, .addressContainer.disabled .addressDetail {
    opacity: .5;
}

.addressContainer.disabled {
    box-shadow: none;
}

.addressContainer .addressTypeWrapper {
    position: absolute;
    right: 0;
    top: 0;
}

.addressContainer .addressBtn {
    display: inline-block;
    vertical-align: top;
    position: absolute;
}

.addressContainer .radioWrapper {
    vertical-align: top;
    position: absolute;
    top: 11px;
}

.addressContainer .addressDetail .addressName {
    color: #292826;
}

.addressContainer .addressDetail .address {
    color: #62635e;
}

.addNewAddressBtn {
    text-align: center;
    color: #5e7e47;
    text-decoration: underline;
}

/**************** new address style ****************/
.newAddressSubHead {
    text-align: center;
    text-transform: uppercase;
    color: #62635e;
}

.newAddressInputContainer {
    box-shadow: 0 1px 2px 0 #bcbcb8;
    background: #fff;
    position: relative;
}

.newAddressInputContainer input[type=text] {
    border: none;
    width: 100%;
    color: #bcbcb8;
}

.newAddressInputContainer .selectedLocWrapper {
    color: #292826;
    text-decoration: underline;
}

.newAddressInputContainer .changeLocBtn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #5e7e47;
    text-align: center;
}

.newAddressInputContainer .radioLabels {
    display: inline-block;
}
/**********membership page styles *******/

.membershipMobile{
    z-index:0 ;
    cursor: pointer;
}

.membershipButtonMobile{
    position: absolute;
    text-align: center;
    background-color: #b05826;
    border: 0;
    font-size: 25px;
    color: white;
    width: 100%;
    z-index: 1;
}

/********** payment modes styles ********/
.payModeTypeContainer {
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12);
    background: #fff;
    position: relative;
}

.payModeTypeContainer .payableAmount {
    color: #292826;
}

.payModeTypeContainer .mode {
    color: #292826;
}

.payModeTypeContainer .mode span {
    color: #5e7e47;
}

/************** order list page ************/
.orderListSubHead {
    text-transform: uppercase;
    color: #62635e;
}

.orderContainer {
    border-bottom: #ddd 1px solid;
    background: #fff;
    position: relative;
}

.orderContainer:last-child {
    box-shadow: 0 1px 2px 0 #bcbcb8;
}

.orderContainer .orderStatus {
    position: absolute;
    right: 10px;
    top: 10px;
}

.orderContainer .reOrder {
    position: absolute;
    right: 10px;
    bottom: 10px;
}

.orderContainer .orderStatus {
    color: #f0af3b;
}

.orderContainer .reOrder {
    color: #5e7e47;
}

.orderContainer .orderStatus, .orderContainer .reOrder {
    font-size: 14px;
    line-height: 18px;
}

.orderContainer .orderPics {
    float: left;
}

.orderContainer .orderPics .pic {
    background-position: center;
    background-size: cover;
}

.orderContainer .orderContent .orderId {
    color: #62635e;
    text-transform: uppercase;
}

.orderContainer .orderContent .productName {
    color: #292826;
}

.orderContainer .orderContent .address {
    color: #bcbcb8;
    text-transform: uppercase;
}

.orderContainer .orderContent .address b {
    color: #000;
}

/************** order detail page ************/
.orderDetailSubHead {
    text-transform: uppercase;
    color: #62635e;
}

.orderStatusContainer {
    box-shadow: 0 1px 2px 0 #bcbcb8;
    background: #fff;
}

.orderStatusContainer .orderStatus {
    display: inline-block;
    text-align: center;
    color: #a6a6a0;
}

.orderStatusContainer .orderStatus .active {
    color: #5e7e47;
}

.orderStatusContainer .orderStatus img {
    display: inline-block;
}

.orderStatusContainer .separator {
    display: inline-block;
    vertical-align: top;
    text-align: center;
}

.orderItemContainer {
    background: #fff;
    position: relative;
    border-bottom: #ccc 1px solid;
}

.orderItemContainer:last-child {
    box-shadow: 0 1px 2px 0 #bcbcb8;
}

.orderItemContainer .pic {
    display: inline-block;
    background-size: cover;
    background-position: center;
    float: left;
}

.orderItemContainer .productTitle {
    color: #292826;
}

.orderItemContainer .productPrice {
    color: #292826;
    position: absolute;
    text-align: right;
}

.orderItemContainer .customizationDetail {
    color: #62635e;
    display: inline-block;
}

.orderItemContainer .productQty {
    color: #292826;
    text-align: right;
    float: right;
    margin-right: 5px;
}

.orderItemContainer .remark {
    background-color: #fbf8eb;
    border-left: solid 1px #f0af3b;
    color: #826b5c;
    word-break: break-all;
}

.orderItemContainer .right .qty {
    color: #a6a6a0;
    text-align: right;
}

.orderItemContainer .right .price {
    color: #292826;
}

.cityIcon {
    height: 95px;
    width: 25%;
    display: inline-block;
    padding: 9px 17px 30px 13px;
    cursor: pointer;
    border: #ccc 1px solid;
    vertical-align: top;
    border-left: 0;
    text-align: center;
}

.cityIcon:hover, .cityIcon.active {
    background: #EEE;
}

.cityIcon .cityTag {
    text-align: center;
}

.cityOutletListContainer {

}

.cityOutletList {
    vertical-align: top;
}

.cityOutletItem {
    border: #5e7e47 1px solid;
    margin: 10px;
    vertical-align: top;
    border-radius: 5px;
}

.cityOutletItem .head {
    padding: 15px;
    font-size: 18px;
    border-bottom: #5e7e47 1px solid;
    color: #fff;
    background: #5e7e47;
    cursor: pointer;
}

.cityOutletItem .body {
    font-size: 18px;
    cursor: pointer;
}

.cityOutletItem .address {
    padding: 20px;
    border-bottom: #5e7e47 1px solid;
}

.cityOutletItem .address img {
    width: 25px;
    margin-bottom: -9px;
}

.cityOutletItem .address span {
    margin-left: 36px;
    display: inline-block;
    margin-top: -25px;
}

.cityOutletItem .call {
    padding: 15px;
    display: inline-block;
    text-align: center;
    font-size: 16px;
    width: 32%;
    border-right: #5e7e47 1px solid;
}

.cityOutletItem .call img {
    width: 18px;
    margin-bottom: -4px;
}

.cityOutletItem .order {
    padding: 15px;
    display: inline-block;
    font-size: 18px;
    width: 33%;
    text-align: center;
    cursor: pointer;
}

.cityOutletMap {
    border: #CCC 1px solid;
    margin: 0 0 0 10px;
    display: inline-block;
    vertical-align: top;
}

.cityOutletItem .locate {
    width: 34%;
    display: inline-block;
    padding: 15px;
    border-right: #5e7e47 1px solid;
    text-align: center;
    vertical-align: top;
    font-size: 16px;
}

.cityOutletItem .locate img {
    width: 25px;
    margin-bottom: -6px;
    margin-right: 10px;
}

.cityOutletItem .order {
    padding: 15px;
    display: inline-block;
    font-size: 16px;
    width: 33%;
    text-align: center;
    cursor: pointer;
}

.cityOutletItem .order img {
    width: 24px;
    margin-bottom: -6px;
    margin-right: 10px;
}

.citySelector {
    margin: 20px 0 50px 0;
}

/***************************************************************************/
/*                     media for small handheld devices                          */
/***************************************************************************/
@media only screen and (max-width: 340px) {

    /******************* generic styles *****************/
    .btn {
        padding: 0 10px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
    }

    .alert {
        margin: 10px;
        padding: 10px;
        background: #ddd;
        border: #ccc 1px solid;
        border-radius: 3px;
    }

    .backArrow {
        top: 0;
        left: 0;
        width: 50px;
        height: 50px;
        text-align: center;
        font-size: 24px;
        line-height: 50px;
        position: absolute;
    }

    .pairBtnWrapper {
        padding: 0;
        margin: 40px 0px 10px 0px;
    }

    .pairBtnWrapper .mainBtn {
        margin-left: 140px;
        margin-right: 10px;
    }

    .pairBtnWrapper .leftBtn {
        top: 0;
        left: 10px;
        width: 120px;
    }

    /**************** utility layout styles ***************/
    .popupWrapper {
        bottom: -2px;
        left: 0;
        right: 0;
        height: 0;
        -webkit-transition: .2s linear;
        transition: .2s linear;
        font-size: 16px;
    }

    .popupWrapper.active {
        bottom: 0;
        padding: 15px 10px;
        height: auto;
    }

    .fullPageLoader .loaderWrapper {
        margin: 50px 30px 0 30px;
        border-radius: 3px;
        padding: 1px;
    }

    .fullPageLoader .loaderWrapper .loaderMessage {
        margin: 0 30px 10px 30px;
    }

    .fullPageLoader .loaderWrapper .promptMessage {
        margin: 20px 30px 20px 30px;
    }

    .lsMsg {
        font-size: 20px;
        margin-bottom: 10px;
        margin-top: 30px;
    }

    .lsDesc {
        font-size: 14px;
    }

    /******************************sidebar styles****************************/
    .sidebarContentWrapper {
        width: 75%;
    }

    .sidebarContentWrapper .sideBarMenu {
        padding: 10px 30px;
        font-size: 18px;
    }

    /******************* header styles ****************/
    .headerWrapper {
        height: 52px;
    }

    .headerBtn {
        font-size: 21px;
        height: 52px;
        line-height: 52px;
        width: 52px;
    }

    .menuIcon {
        width: 19px;
    }

    .headerLogo {
        width: calc(100% - 104px);
        text-align: center;
        height: 52px;
        line-height: 52px;
        display: inline;
        position: absolute;
    }

    .headerLogo img {
        height: 27px;
        margin-top: 12px;
    }

    .headerWrapper .localityWrapper, .headerWrapper .outletWrapper {
        top: 0;
        left: 52px;
        right: 52px;
        height: 52px;
        font-size: 14px;
        line-height: 18px;
    }

    .headerWrapper .localityWrapper .tagLine, .headerWrapper .outletWrapper .tagLine {
        font-size: 10px;
        margin: 8px 0 1px 0;
    }

    .headerWrapper .cartSizeLabel {
        top: 5px;
        right: 5px;
        font-size: 12px;
        line-height: 18px;
        border-radius: 20px;
        min-width: 18px;
        height: 18px;
    }

    .mobilePageContainer {
        /*margin-top: 52px;*/
        position: relative;
    }

    .mobilePageContainer .mobilePageHead {
        font-size: 18px;
        text-align: center;
        color: #292826;
        padding: 15px 0;
    }

    /******************* banner layout styles ***************************/
    .imgSlide {
        height: 160px !important;
    }

    .siteTitle {
        font-size: 24px;
        padding-top: 30px;
    }

    /*******************  home layout styles *************************/
    .actionAreaHeader {
        color: #62635e;
        font-size: 12px;
        line-height: 18px;
        margin: 15px 0 8px 0;
    }

    .actionAreaContainer .actionInputWrapper .radioLabels {
        padding: 13px 0;
        font-size: 14px;
    }

    .actionAreaContainer .citySelector {
        margin: 15px 0;
    }

    /* .actionAreaContainer .cityBtn {
        padding: 8px 10px;
        border-radius: 20px;
        margin: 0 6px 8px 0;
        font-size: 16px;
    } */
    .localitySelector, .outletSelector {
        height: 60px;
        line-height: 60px;
        font-size: 14px;
    }

    /*.actionAreaContainer .localitySelector *, .actionAreaContainer .outletSelector * {
        border: none !important;
    }*/
    .actionAreaContainer .outletHeader {
        padding: 10px;
    }

    .actionAreaContainer .outletListItem {
        height: 30px;
        line-height: 30px;
    }

    .locationWrapper .Select-menu {
        min-height: 400px;
    }

    .locationWrapper .Select-option {
        padding: 0 10px;
        height: 45px !important;
        line-height: 45px !important;
        border-bottom: #f1f1f1 1px solid;
    }

    .locationWrapper .Select-placeholder {
        height: 60px;
        line-height: 60px;
        font-size: 16px;
    }

    .locationWrapper .Select-value-label {
        line-height: 60px;
        height: 60px;
    }

    .locationWrapper .errorMessage {
        padding: 10px 15px;
        border-radius: 5px;
        margin: 10px;
    }

    /********************** outlet menu layout ***********************/
    .navTabContainer {
        padding: 14px 0;
    }

    .navTabContainer .slider {
        height: 4px;
    }

    .navTabContainer .navTab {
        padding: 9px 15px;
        font-size: 12px;
        line-height: 18px;
    }

    .menuContainer {
        margin-top: 100px;
    }

    .menuContainer .menu {
        margin-top: -100px;
        padding-top: 100px;
        padding-bottom: 10px;
    }

    .menuContainer .menu .categoryHeader {
        font-size: 16px;
        padding: 17px 10px 12px 10px;
    }

    .productsWrapper .productContainer .productImage {
        height: 200px;
    }

    .productsWrapper .productContainer .productImage .stockOutWrapper {
        line-height: 200px;
        font-size: 24px;
    }

    .productsWrapper .productContainer .productImage .stockOutWrapper .stockOut {
        padding: 2px 10px;
        border-radius: 10px;
        font-size: 12px;
        bottom: 10px;
        left: 10px;
    }

    .productsWrapper .productContainer .productImage .tagWrapper {
        top: 12px;
        left: 10px;
        right: 10px;
        font-size: 10px;
    }

    .productsWrapper .productContainer .productImage .tagWrapper .tagName {
        border: solid 1px rgba(255, 255, 255, 1);
        padding: 3px 6px 1px 6px;
        border-radius: 20px;
        margin: 0 5px 5px 0;
    }

    .productsWrapper .productContainer .productTitle {
        margin-right: 76px;
        padding: 5px 10px;
        font-size: 14px;
        line-height: 18px;
    }

    .productsWrapper .productContainer .productPrice {
        top: 0;
        right: 10px;
        width: 56px;
        padding: 5px 0px;
        font-size: 14px;
        line-height: 18px;
    }

    .productsWrapper .sizeDescription {
        padding: 0 3px;
    }

    .productsWrapper .productContainer .productDetail .productDescription {
        margin-right: 122px;
        padding: 5px 10px;
        min-height: 53px;
        font-size: 12px;
        line-height: 16px;
    }

    .productsWrapper .productContainer .productDetail .addProductBtn {
        top: 5px;
        right: 10px;
        width: 56px;
        height: 32px;
        line-height: 32px;
        border-radius: 100px;
        font-size: 27px;
    }

    .productsWrapper .productContainer .productDetail .sizeCustomizationWrapper .addProductBtn {
        right: 0;
        top: 8px;
    }

    .productsWrapper .productContainer .productDetail .addProductBtn div {
        font-size: 14px;
    }

    .productsWrapper .productContainer .customizationWrapper {
        padding: 0 10px 10px;
    }

    .productsWrapper .productContainer .customizationWrapper .sizeCustomizationWrapper {
        padding: 0 5px;
        font-size: 14px;
        height: 55px;
        line-height: 55px;
    }

    .qtyWrapper {
        top: 8px;
        right: 0;
        width: 112px;
    }

    .qtyWrapper .incr, .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 27px;
    }

    .qtyWrapper .qty {
        width: 38px;
        line-height: 35px;
    }

    .qtyWrapper .incr {
        border-top-right-radius: 25px;
        border-bottom-right-radius: 25px;
    }

    .qtyWrapper .dcr {
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
    }

    /************** modal layout *********************/
    .modal .modalBody {
        top: 15px;
        right: 15px;
        bottom: 15px;
        left: 15px;
        border-radius: 5px;
    }

    .modal .modalBody .modalCloseBtn {
        width: 50px;
        height: 44px;
        line-height: 44px;
        font-size: 30px;
    }

    .modalTitle {
        font-size: 18px;
        height: 45px;
        line-height: 45px;
    }

    /************** customization modal layout *********************/
    .customizationSection {
        padding: 10px;
    }

    .customizationSection .customizationHead {
        margin: 5px;
        font-size: 16px;
    }

    .customizationSection .constituent .productName {
        margin-right: 100px;
        font-size: 18px;
    }

    .customizationSection .constituent .productDesc {
        font-size: 12px;
    }

    .customizationSection .constituent .qtyWrapper {
        width: 90px;
    }

    .customizationSection .productName {
        margin: 0 140px 10px 0;
        font-size: 14px;
        line-height: 17px;
    }

    .customizationSection .productPrice {
        width: 112px;
        font-size: 14px;
    }

    .customizationSection .customizationSection .rupeeIcon {
        height: 12px;
        margin: 0 3px -1px 0;
    }

    .customizationSection .productDesc {
        margin-right: 135px;
        line-height: 16px;
        min-height: 45px;
        font-size: 12px;
    }

    .customizationSection .qtyWrapper {
        top: 1px;
        right: 5px;
        width: 112px;
    }

    .customizationSection .qtyWrapper .incr, .customizationSection .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 27px;
    }

    .customizationSection .qtyWrapper .qty {
        width: 38px;
        line-height: 35px;
    }

    .customizationSection .qtyWrapper .incr {
        border-top-right-radius: 25px;
        border-bottom-right-radius: 25px;
    }

    .customizationSection .qtyWrapper .dcr {
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
    }

    .customizationSection .dimensionBtn {
        width: 50%;
    }

    .customizationSection .dimensionBtn div {
        margin: 5px;
        height: 45px;
        line-height: 45px;
        border-radius: 5px;
    }

    .customizationSection .menuProductBtn {
        max-width: 100%;
    }

    .customizationSection .menuProductBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
        padding: 0 10px;
    }

    .customizationSection .addonBtn {
        width: 33%;
    }

    .customizationSection .addonBtn.subKuch {
        width: 100%;
        margin-top: 10px;
    }

    .customizationSection .addonBtn.subKuch img {
        display: none;
    }

    .customizationSection .addonBtn div {
        margin: 3px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
    }

    .customizationSection .ingredientProductBtn {
        width: 50%;
    }

    .customizationSection .ingredientProductBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
    }

    .customizationSection .ingredientVariantBtn {
        width: 50%;
    }

    .customizationSection .ingredientVariantBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
    }

    /**************** cart layout styles ***********************/
    .cartHead {
        padding: 17px 0 13px 10px;
        font-size: 12px;
        line-height: 18px;
    }

    .itemCountHead {
        font-size: 12px;
        padding: 0px 0 6px 13px;
        line-height: 18px;
    }

    .cartItemContainer {
        margin-bottom: 10px;
        padding: 10px;
    }

    .cartItemContainer .stockTag {
        font-size: 12px;
        color: rgb(240, 175, 59);
        border: rgb(240, 175, 59) 1px solid;
        padding: 1px 10px;
        border-radius: 10px;
        margin-bottom: 5px;
    }

    .cartItemContainer .stockTag.red {
        color: #b55a5a;
        border: #b55a5a 1px solid;
    }

    .cartItemContainer .pic {
        width: 32px;
        height: 32px;
        margin: 0 8px 8px 0;
    }

    .cartItemContainer .itemTitle {
        margin: 0px 80px 0px 0px;
        font-size: 14px;
        line-height: 18px;
        padding: 9px 0px;
    }

    .cartItemContainer .itemPrice {
        width: 70px;
        font-size: 14px;
        line-height: 18px;
    }

    .cartItemContainer .rupeeIcon {
        height: 12px;
        margin: 0 3px -1px 0;
    }

    .cartItemContainer .itemDetail {
        margin: 5px 115px 5px 0px;
        font-size: 12px;
        min-height: 40px;
        line-height: 16px;
    }

    .cartItemContainer .qtyWrapper {
        width: 110px;
    }

    .cartItemContainer .qtyWrapper .incr, .cartItemContainer .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 37px;
        font-size: 27px;
    }

    .cartItemContainer .qtyWrapper .qty {
        width: 36px;
        line-height: 35px;
    }

    .cartItemContainer .actionContainer {
        height: 33px;
    }

    .cartItemContainer .editItem {
        font-size: 14px;
        line-height: 18px;
        padding: 10px 0 5px 0;
    }

    .cartItemContainer .removeItem {
        font-size: 14px;
        line-height: 18px;
        padding: 10px 0 5px 0;
    }

    .emptyCartIcon {
        width: 50%;
        margin-top: 30px;
    }

    .emptyCartHead {
        font-size: 21px;
        margin: 10px 0;
    }

    .emptyCartTag {
        font-size: 16px;
        width: 80%;
        margin: 0 auto;
    }

    .cartItemContainer .orderRemark {
        width: 100%;
        margin: 0;
        font-size: 14px;
        line-height: 18px;
        padding: 6px 0;
    }

    .cartItemContainer .orderCoupon {
        width: calc(100% - 100px);
        height: 37px;
        font-size: 14px;
        line-height: 18px;
    }

    .cartItemContainer .couponLink {
        width: 90px;
        line-height: 18px;
        font-size: 14px;
        padding: 9px 0;
    }

    .cartItemContainer .transactionDetails {
        font-size: 12px;
        line-height: 18px;
    }

    .cartItemContainer .totalAmount {
        font-size: 14px;
        line-height: 18px;
    }

    .cartItemContainer .rupeeIcon {
        margin-right: 5px;
    }

    .cartItemContainer .transactionDetails .txDetailItem {
        margin-bottom: 6px;
    }

    .cartItemContainer .taxDetailWrapper {
        padding: 10px;
    }

    .cartItemContainer .taxInfo {
        width: 17px;
        margin: 0 0 -3px 5px;
    }

    /************** mobile login layout styles ************/
    .loginSectionTagline {
        font-size: 14px;
        margin: 10px;
        line-height: 18px;
    }

    .contactContainer {
        padding: 10px;
    }

    .contactContainer input {
        font-size: 14px;
        line-height: 18px;
        padding: 5px 0px;
    }

    .resendText {
        font-size: 14px;
        line-height: 18px;
    }

    /************* addresses style **************/
    .addressesSubHead {
        padding: 15px 10px 10px 10px;
    }

    .addressContainer {
        padding: 16px;
    }

    .addressContainer .addressTypeWrapper {
        padding: 10px;
        font-size: 14px;
        color: #f0af3b;
    }

    .addressContainer .addressBtn {
        width: 20px;
        height: 20px;
        border-radius: 15px;
        border: #62635e 2px solid;
    }

    .addressContainer .addressDetail {
        margin: 0 0 0 30px;
    }

    .addressContainer .addressDetail .addressName {
        font-size: 14px;
        line-height: 18px;
        padding: 0 0 8px 0;
    }

    .addressContainer .addressDetail .address {
        font-size: 14px;
        line-height: 18px;
    }

    .addNewAddressBtn {
        padding: 20px 0 10px 0;
        font-size: 14px;
        line-height: 18px;
    }

    /**************** new address style ****************/
    .newAddressSubHead {
        font-size: 12px;
        line-height: 18px;
        padding: 15px 0;
    }

    .newAddressInputContainer {
        padding: 10px;
    }

    .newAddressInputContainer input[type=text] {
        font-size: 14px;
        line-height: 18px;
        padding: 10px 0px;
    }

    .newAddressInputContainer .selectedLocWrapper {
        margin-right: 110px;
        padding: 10px 0;
        font-size: 14px;
        line-height: 18px;
    }

    .newAddressInputContainer .changeLocBtn {
        font-size: 14px;
        width: 80px;
        line-height: 18px;
        padding: 10px 0;
    }

    .newAddressInputContainer .radioLabels {
        padding: 10px;
    }

    /********** payment modes styles ********/
    .payModeTypeContainer {
        padding: 10px;
        font-size: 18px;
    }

    .payModeTypeContainer .payableAmount {
        padding: 15px;
        font-size: 14px;
        line-height: 18px;
    }

    .payModeTypeContainer .payableAmount .rupeeIcon {
        height: 14px;
        margin: 0 5px -2px 0;
    }

    .payModeTypeContainer .mode {
        padding: 15px;
        font-size: 14px;
        line-height: 18px;
    }

    .payModeTypeContainer .mode span {
        font-size: 24px;
        line-height: 15px;
    }

    /************** order list page ************/
    .orderListSubHead {
        font-size: 12px;
        padding: 5px 10px;
    }

    .orderContainer {
        padding: 10px;
    }

    .orderContainer:last-child {
        margin-bottom: 20px;
    }

    .orderContainer .orderPics {
        width: 50px;
        margin-right: 8px;
    }

    .orderContainer .orderPics .pic {
        width: 50px;
        height: 50px;
        margin: 1px 1px 0 0;
    }

    .orderContainer .orderContent {
        margin-left: 55px;
    }

    .orderContainer .orderContent .orderId {
        font-size: 10px;
        line-height: 18px;
    }

    .orderContainer .orderContent .productName {
        font-size: 12px;
        margin: 5px 0;
        line-height: 18px;
    }

    .orderContainer .orderContent .address {
        margin-top: 2px;
        font-size: 12px;
        line-height: 18px;
    }

    /************** order detail page ************/
    .orderDetailSubHead {
        font-size: 12px;
        line-height: 18px;
        padding: 10px;
    }

    .orderStatusContainer {
        padding: 9px 0;
    }

    .orderStatusContainer .orderStatus {
        width: 20%;
        font-size: 12px;
        line-height: 18px;
    }

    .orderStatusContainer .orderStatus img {
        height: 27px;
        margin: 5px 0;
    }

    .orderStatusContainer .separator {
        width: 6%;
        margin-top: 16px;
    }

    .orderStatusContainer .separator img {
        width: 50%;
    }

    .orderItemContainer {
        padding: 10px 12px 10px 11px;
    }

    .orderItemContainer .pic {
        width: 32px;
        height: 32px;
        margin: 0 8px 8px 0;
    }

    .orderItemContainer .productTitle {
        margin: 10px 90px 0 0;
        font-size: 14px;
        line-height: 18px;
    }

    .orderItemContainer .productPrice {
        font-size: 14px;
        top: 20px;
        line-height: 18px;
        right: 15px;
        width: 80px;
    }

    .orderItemContainer .productPrice .rupeeIcon {
        height: 14px;
        margin: 0 3px -1px 0;
    }

    .orderItemContainer .customizationDetail {
        font-size: 12px;
        line-height: 16px;
        width: calc(100% - 90px);
    }

    .orderItemContainer .productQty {
        font-size: 14px;
        line-height: 18px;
        width: 80px;
    }

    .orderItemContainer .remark {
        padding: 10px;
        font-size: 12px;
        line-height: 16px;
    }

    .orderItemContainer .total {
        font-size: 14px;
        line-height: 18px;
        padding: 6px;
        display: inline-block;
    }

    .orderItemContainer .right .qty {
        font-size: 12px;
        line-height: 18px;
        margin: -4px 0 0 0;
    }

    .orderItemContainer .right .totalPrice {
        font-size: 14px;
        line-height: 18px;
    }

    /************** profile page *************************/
    .mobileProfileHead {
        background: #5e7e47;
        border-top: #759261 1px solid;
        color: #fff;
        text-align: center;
        padding: 20px 15px;
    }

    .mobileProfileHead .profilePic {
        border: #fff 3px solid;
        border-radius: 50%;
        background: #e3e0e0;
    }

    .mobileProfileHead .userName {
        font-size: 21px;
        padding: 9pt 10px;
    }

    .mobileProfileHead .userContact {
        font-size: 12px;
        margin: 3px 0;
    }

    .mobileProfileHead .userEmail {
        font-size: 12px;
        margin: 3px 0;
    }

    .profileCard {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 1px 0 rgba(0, 0, 0, .1);
        padding: 15px;
        margin: 10px;
    }

    .profileCard .cardTitle {
        font-size: 18px;
        margin-bottom: 10px;
        word-break: break-all;
    }

    .profileCard .loyalteaScore {
        text-align: center;
        font-size: 52px;
        color: #5e7e47;
    }

    .profileCard .cardDesc {

    }

    .profileCard .cardFooter {
        border-top: 1px solid #ebebeb;
        padding: 5px 0 0;
        margin-top: 10px;
        color: #5e7e47;
        line-height: 30px;
    }

    .profileCard .cardFooter img {
        width: 20px;
        margin: 5px 10px 0 0;
        float: left;
    }

    /********************contact page ******************/
    .addressBlock {
        width: calc(100% - 30px);
        line-height: normal;
        font-size: 14px;
        float: left;
    }

}

/***************************************************************************/
/*                     media for handheld devices                          */
/***************************************************************************/
@media only screen and (min-width: 341px) and (max-width: 767px) {

    /******************* generic styles *****************/
    .btn {
        padding: 0 10px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
    }

    .alert {
        margin: 10px;
        padding: 10px;
        background: #ddd;
        border: #ccc 1px solid;
        border-radius: 3px;
    }

    .backArrow {
        top: 0;
        left: 0;
        width: 50px;
        height: 50px;
        text-align: center;
        font-size: 24px;
        line-height: 50px;
        position: absolute;
    }

    .pairBtnWrapper {
        padding: 0;
        margin: 40px 0px 10px 0px;
    }

    .pairBtnWrapper .mainBtn {
        margin-left: 140px;
        margin-right: 10px;
    }

    .pairBtnWrapper .leftBtn {
        top: 0;
        left: 10px;
        width: 120px;
    }

    /**************** utility layout styles ***************/
    .popupWrapper {
        bottom: -2px;
        left: 0;
        right: 0;
        height: 0;
        -webkit-transition: .2s linear;
        transition: .2s linear;
        font-size: 16px;
    }

    .popupWrapper.active {
        bottom: 0;
        padding: 15px 10px;
        height: auto;
    }

    .fullPageLoader .loaderWrapper {
        margin: 50px 30px 0 30px;
        border-radius: 3px;
        padding: 1px;
    }

    .fullPageLoader .loaderWrapper .loaderMessage {
        margin: 0 30px 10px 30px;
    }

    .fullPageLoader .loaderWrapper .promptMessage {
        margin: 20px 30px 20px 30px;
    }

    .lsMsg {
        font-size: 20px;
        margin-bottom: 10px;
        margin-top: 30px;
    }

    .lsDesc {
        font-size: 14px;
    }

    /******************************sidebar styles****************************/
    .sidebarContentWrapper {
        width: 75%;
    }

    .sidebarContentWrapper .sideBarMenu {
        padding: 10px 30px;
        font-size: 18px;
    }

    /******************* header styles ****************/
    .headerWrapper {
        height: 52px;
    }

    .headerBtn {
        font-size: 21px;
        height: 52px;
        line-height: 52px;
        width: 52px;
    }

    .menuIcon {
        width: 19px;
    }

    .headerLogo {
        width: calc(100% - 104px);
        text-align: center;
        height: 52px;
        line-height: 52px;
        display: inline;
        position: absolute;
    }

    .headerLogo img {
        height: 27px;
        margin-top: 12px;
    }

    .headerWrapper .localityWrapper, .headerWrapper .outletWrapper {
        top: 0;
        left: 52px;
        right: 52px;
        height: 52px;
        font-size: 14px;
        line-height: 18px;
    }

    .headerWrapper .localityWrapper .tagLine, .headerWrapper .outletWrapper .tagLine {
        font-size: 10px;
        margin: 8px 0 1px 0;
    }

    .headerWrapper .cartSizeLabel {
        top: 5px;
        right: 5px;
        font-size: 12px;
        line-height: 18px;
        border-radius: 20px;
        min-width: 18px;
        height: 18px;
    }

    .mobilePageContainer {
        /*margin-top: 52px;*/
        position: relative;
    }

    .mobilePageContainer .mobilePageHead {
        font-size: 18px;
        text-align: center;
        color: #292826;
        padding: 15px 0;
    }

    /******************* banner layout styles ***************************/
    .imgSlide {
        height: 205px !important;
    }

    .siteTitle {
        font-size: 24px;
        padding-top: 30px;
    }

    /*******************  home layout styles *************************/
    .actionAreaHeader {
        color: #62635e;
        font-size: 12px;
        line-height: 18px;
        margin: 15px 0 8px 0;
    }

    .actionAreaContainer .actionInputWrapper .radioLabels {
        padding: 13px 0;
        font-size: 14px;
    }

    .actionAreaContainer .citySelector {
        margin: 15px 0;
    }

    /* .actionAreaContainer .cityBtn {
        padding: 8px 10px;
        border-radius: 20px;
        margin: 0 6px 8px 0;
        font-size: 16px;
    } */
    .localitySelector, .outletSelector {
        height: 60px;
        line-height: 60px;
        font-size: 14px;
    }

    /*.actionAreaContainer .localitySelector *, .actionAreaContainer .outletSelector * {
        border: none !important;
    }*/
    .actionAreaContainer .outletHeader {
        padding: 10px;
    }

    .actionAreaContainer .outletListItem {
        height: 30px;
        line-height: 30px;
    }

    .locationWrapper .Select-menu {
        min-height: 400px;
    }

    .locationWrapper .Select-option {
        padding: 0 10px;
        height: 45px !important;
        line-height: 45px !important;
        border-bottom: #f1f1f1 1px solid;
    }

    .locationWrapper .Select-placeholder {
        height: 60px;
        line-height: 60px;
        font-size: 16px;
    }

    .locationWrapper .Select-value-label {
        line-height: 60px;
        height: 60px;
    }

    .locationWrapper .errorMessage {
        padding: 10px 15px;
        border-radius: 5px;
        margin: 10px;
    }

    /********************** outlet menu layout ***********************/
    .navTabContainer {
        padding: 14px 0;
    }

    .navTabContainer .slider {
        height: 4px;
    }

    .navTabContainer .navTab {
        padding: 9px 15px;
        font-size: 12px;
        line-height: 18px;
    }

    .menuContainer {
        margin-top: 100px;
    }

    .menuContainer .menu {
        margin-top: -100px;
        padding-top: 100px;
        padding-bottom: 10px;
    }

    .menuContainer .menu .categoryHeader {
        font-size: 16px;
        padding: 17px 10px 12px 10px;
    }

    .productsWrapper .productContainer .productImage {
        height: 200px;
    }

    .productsWrapper .productContainer .productImage .stockOutWrapper {
        line-height: 200px;
        font-size: 24px;
    }

    .productsWrapper .productContainer .productImage .stockOutWrapper .stockOut {
        padding: 2px 10px;
        border-radius: 10px;
        font-size: 12px;
        bottom: 10px;
        left: 10px;
    }

    .productsWrapper .productContainer .productImage .tagWrapper {
        top: 12px;
        left: 10px;
        right: 10px;
        font-size: 10px;
    }

    .productsWrapper .productContainer .productImage .tagWrapper .tagName {
        border: solid 1px rgba(255, 255, 255, 1);
        padding: 3px 6px 1px 6px;
        border-radius: 20px;
        margin: 0 5px 5px 0;
    }

    .productsWrapper .productContainer .productTitle {
        margin-right: 76px;
        padding: 5px 10px;
        font-size: 14px;
        line-height: 18px;
    }

    .productsWrapper .productContainer .productPrice {
        top: 0;
        right: 10px;
        width: 56px;
        padding: 5px 0px;
        font-size: 14px;
        line-height: 18px;
    }

    .productsWrapper .sizeDescription {
        padding: 0 3px;
    }

    .productsWrapper .productContainer .productDetail .productDescription {
        margin-right: 122px;
        padding: 5px 10px;
        min-height: 53px;
        font-size: 12px;
        line-height: 16px;
    }

    .productsWrapper .productContainer .productDetail .addProductBtn {
        top: 5px;
        right: 10px;
        width: 56px;
        height: 32px;
        line-height: 32px;
        border-radius: 100px;
        font-size: 27px;
    }

    .productsWrapper .productContainer .productDetail .sizeCustomizationWrapper .addProductBtn {
        right: 0;
        top: 8px;
    }

    .productsWrapper .productContainer .productDetail .addProductBtn div {
        font-size: 14px;
    }

    .productsWrapper .productContainer .customizationWrapper {
        padding: 0 10px 10px;
    }

    .productsWrapper .productContainer .customizationWrapper .sizeCustomizationWrapper {
        padding: 0 5px;
        font-size: 14px;
        height: 55px;
        line-height: 55px;
    }

    .qtyWrapper {
        top: 8px;
        right: 0;
        width: 112px;
    }

    .qtyWrapper .incr, .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 27px;
    }

    .qtyWrapper .qty {
        width: 38px;
        line-height: 35px;
    }

    .qtyWrapper .incr {
        border-top-right-radius: 25px;
        border-bottom-right-radius: 25px;
    }

    .qtyWrapper .dcr {
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
    }

    /************** modal layout *********************/
    .modal .modalBody {
        top: 15px;
        right: 15px;
        bottom: 15px;
        left: 15px;
        border-radius: 5px;
    }

    .modal .modalBody .modalCloseBtn {
        width: 50px;
        height: 44px;
        line-height: 44px;
        font-size: 30px;
    }

    .modalTitle {
        font-size: 18px;
        height: 45px;
        line-height: 45px;
    }

    /************** customization modal layout *********************/
    .customizationSection {
        padding: 10px;
    }

    .customizationSection .customizationHead {
        margin: 5px;
        font-size: 16px;
    }

    .customizationSection .constituent .productName {
        margin-right: 100px;
        font-size: 18px;
    }

    .customizationSection .constituent .productDesc {
        font-size: 12px;
    }

    .customizationSection .constituent .qtyWrapper {
        width: 90px;
    }

    .customizationSection .productName {
        margin: 0 140px 10px 0;
        font-size: 14px;
        line-height: 17px;
    }

    .customizationSection .productPrice {
        width: 112px;
        font-size: 14px;
    }

    .customizationSection .customizationSection .rupeeIcon {
        height: 12px;
        margin: 0 3px -1px 0;
        margin-bottom: -1px;
    }

    .customizationSection .productDesc {
        margin-right: 135px;
        line-height: 16px;
        min-height: 45px;
        font-size: 12px;
    }

    .customizationSection .qtyWrapper {
        top: 1px;
        right: 5px;
        width: 112px;
    }

    .customizationSection .qtyWrapper .incr, .customizationSection .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 27px;
    }

    .customizationSection .qtyWrapper .qty {
        width: 38px;
        line-height: 35px;
    }

    .customizationSection .qtyWrapper .incr {
        border-top-right-radius: 25px;
        border-bottom-right-radius: 25px;
    }

    .customizationSection .qtyWrapper .dcr {
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
    }

    .customizationSection .dimensionBtn {
        width: 50%;
    }

    .customizationSection .dimensionBtn div {
        margin: 5px;
        height: 45px;
        line-height: 45px;
        border-radius: 5px;
    }

    .customizationSection .menuProductBtn {
        max-width: 100%;
    }

    .customizationSection .menuProductBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
        padding: 0 10px;
    }

    .customizationSection .addonBtn {
        width: 33%;
    }

    .customizationSection .addonBtn.subKuch {
        width: 100%;
        margin-top: 10px;
    }

    .customizationSection .addonBtn.subKuch img {
        display: none;
    }

    .customizationSection .addonBtn div {
        margin: 3px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
    }

    .customizationSection .ingredientProductBtn {
        width: 50%;
    }

    .customizationSection .ingredientProductBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
    }

    .customizationSection .ingredientVariantBtn {
        width: 50%;
    }

    .customizationSection .ingredientVariantBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 5px;
    }

    /**************** cart layout styles ***********************/
    .cartHead {
        padding: 17px 0 13px 10px;
        font-size: 12px;
        line-height: 18px;
    }

    .itemCountHead {
        font-size: 12px;
        padding: 0px 0 6px 13px;
        line-height: 18px;
    }

    .cartItemContainer {
        margin-bottom: 10px;
        padding: 10px;
    }

    .cartItemContainer .stockTag {
        font-size: 12px;
        color: rgb(240, 175, 59);
        border: rgb(240, 175, 59) 1px solid;
        padding: 1px 10px;
        border-radius: 10px;
        margin-bottom: 5px;
    }

    .cartItemContainer .stockTag.red {
        color: #b55a5a;
        border: #b55a5a 1px solid;
    }

    .cartItemContainer .pic {
        width: 32px;
        height: 32px;
        margin: 0 8px 8px 0;
    }

    .cartItemContainer .itemTitle {
        margin: 0px 80px 0px 0px;
        font-size: 14px;
        line-height: 18px;
        padding: 9px 0px;
    }

    .cartItemContainer .itemPrice {
        width: 70px;
        font-size: 14px;
        line-height: 18px;
    }

    .cartItemContainer .rupeeIcon {
        height: 12px;
        margin: 0 3px -1px 0;
    }

    .cartItemContainer .itemDetail {
        margin: 5px 115px 5px 0px;
        font-size: 12px;
        min-height: 40px;
        line-height: 16px;
    }

    .cartItemContainer .qtyWrapper {
        width: 110px;
    }

    .cartItemContainer .qtyWrapper .incr, .cartItemContainer .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 37px;
        font-size: 27px;
    }

    .cartItemContainer .qtyWrapper .qty {
        width: 36px;
        line-height: 35px;
    }

    .cartItemContainer .actionContainer {
        height: 33px;
    }

    .cartItemContainer .editItem {
        font-size: 14px;
        line-height: 18px;
        padding: 10px 0 5px 0;
    }

    .cartItemContainer .removeItem {
        font-size: 14px;
        line-height: 18px;
        padding: 10px 0 5px 0;
    }

    .emptyCartIcon {
        width: 50%;
        margin-top: 30px;
    }

    .emptyCartHead {
        font-size: 21px;
        margin: 10px 0;
    }

    .emptyCartTag {
        font-size: 16px;
        width: 80%;
        margin: 0 auto;
    }

    .cartItemContainer .orderRemark {
        width: 100%;
        margin: 0;
        font-size: 14px;
        line-height: 18px;
        padding: 6px 0;
    }

    .cartItemContainer .orderCoupon {
        width: calc(100% - 100px);
        height: 37px;
        font-size: 14px;
        line-height: 18px;
    }

    .cartItemContainer .couponLink {
        width: 90px;
        line-height: 18px;
        font-size: 14px;
        padding: 9px 0;
    }

    .cartItemContainer .transactionDetails {
        font-size: 12px;
        line-height: 18px;
    }

    .cartItemContainer .totalAmount {
        font-size: 14px;
        line-height: 18px;
    }

    .cartItemContainer .rupeeIcon {
        margin-right: 5px;
    }

    .cartItemContainer .transactionDetails .txDetailItem {
        margin-bottom: 6px;
    }

    .cartItemContainer .taxDetailWrapper {
        padding: 10px;
    }

    .cartItemContainer .taxInfo {
        width: 17px;
        margin: 0 0 -3px 5px;
    }

    /************** mobile login layout styles ************/
    .loginSectionTagline {
        font-size: 14px;
        margin: 10px;
        line-height: 18px;
    }

    .contactContainer {
        padding: 10px;
    }

    .contactContainer input {
        font-size: 14px;
        line-height: 18px;
        padding: 5px 0px;
    }

    .resendText {
        font-size: 14px;
        line-height: 18px;
    }

    /************* addresses style **************/
    .addressesSubHead {
        padding: 15px 10px 10px 10px;
    }

    .addressContainer {
        padding: 16px;
    }

    .addressContainer .addressTypeWrapper {
        padding: 10px;
        font-size: 14px;
        color: #f0af3b;
    }

    .addressContainer .addressBtn {
        width: 20px;
        height: 20px;
        border-radius: 15px;
        border: #62635e 2px solid;
    }

    .addressContainer .addressDetail {
        margin: 0 0 0 30px;
    }

    .addressContainer .addressDetail .addressName {
        font-size: 14px;
        line-height: 18px;
        padding: 0 0 8px 0;
    }

    .addressContainer .addressDetail .address {
        font-size: 14px;
        line-height: 18px;
    }

    .addNewAddressBtn {
        padding: 20px 0 10px 0;
        font-size: 14px;
        line-height: 18px;
    }

    /**************** new address style ****************/
    .newAddressSubHead {
        font-size: 12px;
        line-height: 18px;
        padding: 15px 0;
    }

    .newAddressInputContainer {
        padding: 10px;
    }

    .newAddressInputContainer input[type=text] {
        font-size: 14px;
        line-height: 18px;
        padding: 10px 0px;
    }

    .newAddressInputContainer .selectedLocWrapper {
        margin-right: 110px;
        padding: 10px 0;
        font-size: 14px;
        line-height: 18px;
    }

    .newAddressInputContainer .changeLocBtn {
        font-size: 14px;
        width: 80px;
        line-height: 18px;
        padding: 10px 0;
    }

    .newAddressInputContainer .radioLabels {
        padding: 10px;
    }

    /********** payment modes styles ********/
    .payModeTypeContainer {
        padding: 10px;
        font-size: 18px;
    }

    .payModeTypeContainer .payableAmount {
        padding: 15px;
        font-size: 14px;
        line-height: 18px;
    }

    .payModeTypeContainer .payableAmount .rupeeIcon {
        height: 14px;
        margin: 0 5px -2px 0;
    }

    .payModeTypeContainer .mode {
        padding: 15px;
        font-size: 14px;
        line-height: 18px;
    }

    .payModeTypeContainer .mode span {
        font-size: 24px;
        line-height: 15px;
    }

    /************** order list page ************/
    .orderListSubHead {
        font-size: 12px;
        padding: 5px 10px;
    }

    .orderContainer {
        padding: 10px;
    }

    .orderContainer:last-child {
        margin-bottom: 20px;
    }

    .orderContainer .orderPics {
        width: 50px;
        margin-right: 8px;
    }

    .orderContainer .orderPics .pic {
        width: 50px;
        height: 50px;
        margin: 1px 1px 0 0;
    }

    .orderContainer .orderContent {
        margin-left: 55px;
    }

    .orderContainer .orderContent .orderId {
        font-size: 10px;
        line-height: 18px;
    }

    .orderContainer .orderContent .productName {
        font-size: 12px;
        margin: 5px 0;
        line-height: 18px;
    }

    .orderContainer .orderContent .address {
        margin-top: 2px;
        font-size: 12px;
        line-height: 18px;
    }

    /************** order detail page ************/
    .orderDetailSubHead {
        font-size: 12px;
        line-height: 18px;
        padding: 10px;
    }

    .orderStatusContainer {
        padding: 9px 0;
    }

    .orderStatusContainer .orderStatus {
        width: 20%;
        font-size: 12px;
        line-height: 18px;
    }

    .orderStatusContainer .orderStatus img {
        height: 27px;
        margin: 5px 0;
    }

    .orderStatusContainer .separator {
        width: 6%;
        margin-top: 16px;
    }

    .orderStatusContainer .separator img {
        width: 50%;
    }

    .orderItemContainer {
        padding: 10px 12px 10px 11px;
    }

    .orderItemContainer .pic {
        width: 32px;
        height: 32px;
        margin: 0 8px 8px 0;
    }

    .orderItemContainer .productTitle {
        margin: 10px 90px 0 0;
        font-size: 14px;
        line-height: 18px;
    }

    .orderItemContainer .productPrice {
        font-size: 14px;
        top: 20px;
        line-height: 18px;
        right: 15px;
        width: 80px;
    }

    .orderItemContainer .productPrice .rupeeIcon {
        height: 14px;
        margin: 0 3px -1px 0;
    }

    .orderItemContainer .customizationDetail {
        font-size: 12px;
        line-height: 16px;
        width: calc(100% - 90px);
    }

    .orderItemContainer .productQty {
        font-size: 14px;
        line-height: 18px;
        width: 80px;
    }

    .orderItemContainer .remark {
        padding: 10px;
        font-size: 12px;
        line-height: 16px;
    }

    .orderItemContainer .total {
        font-size: 14px;
        line-height: 18px;
        padding: 6px;
        display: inline-block;
    }

    .orderItemContainer .right .qty {
        font-size: 12px;
        line-height: 18px;
        margin: -4px 0 0 0;
    }

    .orderItemContainer .right .totalPrice {
        font-size: 14px;
        line-height: 18px;
    }

    /************** profile page *************************/
    .mobileProfileHead {
        background: #5e7e47;
        border-top: #759261 1px solid;
        color: #fff;
        text-align: center;
        padding: 20px 15px;
    }

    .mobileProfileHead .profilePic {
        border: #fff 3px solid;
        border-radius: 50%;
        background: #e3e0e0;
    }

    .mobileProfileHead .userName {
        font-size: 21px;
        padding: 9pt 10px;
    }

    .mobileProfileHead .userContact {
        font-size: 12px;
        margin: 3px 0;
    }

    .mobileProfileHead .userEmail {
        font-size: 12px;
        margin: 3px 0;
    }

    .profileCard {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 1px 0 rgba(0, 0, 0, .1);
        padding: 15px;
        margin: 10px;
    }

    .profileCard .cardTitle {
        font-size: 18px;
        margin-bottom: 10px;
        word-break: break-all;
    }

    .profileCard .loyalteaScore {
        text-align: center;
        font-size: 52px;
        color: #5e7e47;
    }

    .profileCard .giftCardAmount {
        text-align: center;
        font-size: 52px;
        color: #5e7e47;
    }

    .profileCard .cardDesc {

    }

    .profileCard .cardFooter {
        border-top: 1px solid #ebebeb;
        padding: 5px 0 0;
        margin-top: 10px;
        color: #5e7e47;
        line-height: 30px;
    }

    .profileCard .cardFooter img {
        width: 20px;
        margin: 5px 10px 0 0;
        float: left;
    }

    /********************contact page ******************/
    .addressBlock {
        width: calc(100% - 30px);
        line-height: normal;
        font-size: 14px;
        float: left;
    }

    /************** promo modal styles ***********/
    .promoWrapper {
        position: absolute;
        top: 75px;
        right: 20px;
        left: 20px;
        background: #fff;
        border: #ddd 1px solid;
        text-align: left;
        z-index: 99999;
        border-radius: 3px;
    }

    .promoWrapper .promoClose {
        position: absolute;
        top: -15px;
        right: -15px;
        width: 30px;
        height: 30px;
        line-height: 28px;
        color: #000;
        text-align: center;
        font-size: 21px;
        background: #fff;
        border-radius: 50%;
    }

    .promoWrapper .promoImage {
        float: left;
        width: 135px;
        height: 135px;
        margin-right: 5px;
        border-radius: 50%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        margin-top: -10px;
        margin-left: -10px;
        border: #fff 4px solid;
    }

    .promoWrapper .promoBody {
        margin-left: 130px;
    }

    .promoWrapper .promoHeadline {
        font-weight: normal;
        font-size: 18px;
        margin-bottom: 10px;
        margin-right: 18px;
        color: #000;
        margin-top: 10px;
    }

    .promoWrapper .promoText {
        margin-bottom: 10px;
        color: #000;
        margin-right: 5px;
    }

    .promoWrapper .bottomNote {
        margin: 10px 0 10px 0;
        color: #868686;
        font-size: 12px;
    }

    .promoWrapper .applyBtn {
        background: #5e7e47;
        display: inline-block;
        padding: 8px 25px;
        color: #fff;
        border-radius: 3px;
    }
}

/***************************************************************************/
/*                     media for handheld devices - Iphone5                */
/***************************************************************************/
@media only screen and (min-width: 320px) and (max-width: 641px) {

    .actionAreaContainer .cityBtn {
        padding: 8px 10px;
        border-radius: 20px;
        margin: 0 6px 8px 0;
        font-size: 13px;
    }
}

/***************************************************************************/
/*                     media for handheld devices - Iphone5                */
/***************************************************************************/
@media only screen and (min-width: 641px) and (max-width: 767px) {

    .actionAreaContainer .cityBtn {
        padding: 8px 10px;
        border-radius: 20px;
        margin: 0 6px 8px 0;
        font-size: 16px;
    }
}

/***************************************************************************/
/*                     media for tablet devices                          */
/***************************************************************************/
@media only screen and (min-width: 768px) {

    /******************* generic styles *****************/
    .btn {
        padding: 0 10px;
        height: 60px;
        line-height: 60px;
        border-radius: 30px;
        cursor: pointer;
        font-size: 24px;
    }

    .alert {
        margin: 10px;
        padding: 10px;
        background: #ddd;
        border: #ccc 1px solid;
        border-radius: 5px;
        font-size: 24px;
    }

    .pairBtnWrapper {
        padding: 0;
        margin: 50px 0px 10px 0px;
    }

    .pairBtnWrapper .mainBtn {
        margin-left: 240px;
        margin-right: 10px;
    }

    .pairBtnWrapper .leftBtn {
        top: 0;
        left: 10px;
        width: 200px;
    }

    /**************** utility layout styles ***************/
    .popupWrapper {
        bottom: -2px;
        left: 0;
        right: 0;
        height: 0;
        -webkit-transition: .2s linear;
        transition: .2s linear;
        font-size: 16px;
    }

    .popupWrapper.active {
        bottom: 0;
        padding: 15px 10px;
        height: auto;
    }

    .fullPageLoader .loaderWrapper {
        margin: 50px 30px 0 30px;
        border-radius: 3px;
        padding: 1px;
    }

    .fullPageLoader .loaderWrapper .loaderMessage {
        margin: 0 30px 10px 30px;
    }

    .lsMsg {
        font-size: 20px;
        margin-bottom: 10px;
        margin-top: 30px;
    }

    .lsDesc {
        font-size: 14px;
    }

    /******************************sidebar styles****************************/
    .sidebarContentWrapper {
        width: 350px;
    }

    .sidebarContentWrapper .sideBarMenu {
        padding: 20px;
        font-size: 24px;
    }

    /******************* header styles ****************/
    .headerWrapper {
        height: 70px;
    }

    .headerBtn {
        font-size: 27px;
        height: 70px;
        line-height: 70px;
        width: 70px;
    }

    .headerLogo {
        height: 70px;
        line-height: 70px;
        font-size: 24px;

    }

    .imageLogo {
    margin-left :20px;
    }

    .headerWrapper .localityWrapper, .headerWrapper .outletWrapper {
        top: 0;
        left: 80px;
        right: 140px;
        height: 70px;
        font-size: 21px;
    }

    .headerWrapper .localityWrapper .tagLine, .headerWrapper .outletWrapper .tagLine {
        font-size: 16px;
        margin: 10px 0 3px 0;
    }

    .headerWrapper .cartSizeLabel {
        top: 8px;
        left: 8px;
        font-size: 16px;
        padding: 5px;
        border-radius: 20px;
        min-width: 20px;
    }

    .mobilePageContainer {
        margin-top: 70px;
        position: relative;
    }

    .mobilePageContainer .mobilePageHead {
        font-size: 24px;
        text-align: center;
        color: #292826;
        padding: 20px 0;
    }

    /******************* banner layout styles ***************************/
    .imgSlide {
        height: 370px !important;
    }

    .siteTitle {
        font-size: 28px;
        margin-top: 100px;
    }

    /*******************  home layout styles *************************/
    .actionAreaHeader {
        color: #62635e;
        font-size: 16px;
        line-height: 18px;
        margin: 15px 0 8px 0;
    }

    .actionBtn {
        margin: 5px;
        border-radius: 5px;
        padding: 15px;
    }

    .actionAreaContainer .actionInputWrapper .radioLabels {
        padding: 13px 0;
        font-size: 14px;
    }

    /******************* location layout styles ***************************/
    .goBackLink {
        left: 0;
        top: 0;
        width: 70px;
        height: 70px;
        font-size: 34px;
        line-height: 70px;
    }

    .selectCityLabel {
        height: 70px;
        line-height: 70px;
        font-size: 24px;
    }

    .citySelector {
        margin: 20px 20px 50px 20px;
    }

    .cityBtn {
        padding: 15px 20px;
        border-radius: 40px;
        margin: 0 15px 15px 0;
        font-size: 24px;
    }

    .localitySelector, .outletSelector {
        padding: 20px;
    }

    .localitySelector *, .outletSelector * {
        border: none !important;
        font-size: 21px;
    }

    .outletHeader {
        padding: 20px;
        font-size: 18px;
    }

    .outletListItem {
        height: 50px;
        line-height: 50px;
    }

    .Select-menu {
        min-height: 700px;
        margin: -20px;
        margin-top: 20px;
    }

    .Select-option {
        height: 70px !important;
        line-height: 60px !important;
    }

    .errorMessage {
        padding: 15px 20px;
        border-radius: 5px;
        margin: 10px;
        font-size: 21px;
    }

    /********************** outlet menu layout ***********************/
    .navTabContainer {
        padding: 20px 0;
    }

    .navTabContainer .slider {
        height: 6px;
    }

    .navTabContainer .navTab {
        padding: 9px 30px;
        font-size: 20px;
    }

    .menuContainer {
        margin-top: 130px;
    }

    .menuContainer .menu {
        padding-bottom: 20px;
    }

    .menuContainer .menu .categoryHeader {
        font-size: 21px;
        padding: 25px 20px 20px 20px;
    }

    .productsWrapper {
        margin: 0 10px;
    }

    .productsWrapper .productContainer {
        box-shadow: 0 1px 2px 0 #bcbcb8;
        margin: 1%;
        width: 48%;
        display: inline-block;
    }

    .productsWrapper .productContainer .productImage {
        height: 180px;
    }

    .productsWrapper .productContainer .productImage .stockOutWrapper {
        line-height: 200px;
        font-size: 24px;
    }

    .productsWrapper .productContainer .productImage .stockOutWrapper .stockOut {
        padding: 2px 10px;
        border-radius: 10px;
        font-size: 12px;
        bottom: 10px;
        left: 10px;
    }

    .productsWrapper .productContainer .productImage .tagWrapper {
        top: 12px;
        left: 10px;
        right: 10px;
        font-size: 10px;
    }

    .productsWrapper .productContainer .productImage .tagWrapper .tagName {
        border: solid 1px rgba(255, 255, 255, 1);
        padding: 3px 6px 1px 6px;
        border-radius: 20px;
        margin: 0 5px 5px 0;
    }

    .productsWrapper .productContainer .productTitle {
        margin-right: 110px;
        padding: 5px 10px;
        font-size: 18px;
    }

    .productsWrapper .productContainer .productPrice {
        top: 0;
        right: 10px;
        width: 90px;
        padding: 5px 0px;
        font-size: 16px;
    }

    .productsWrapper .productContainer .productDetail .productDescription {
        margin-right: 110px;
        padding: 5px 10px;
        min-height: 53px;
        font-size: 16px;
    }

    .productsWrapper .productContainer .productDetail .addProductBtn {
        top: 5px;
        right: 10px;
        width: 56px;
        height: 35px;
        line-height: 35px;
        border-radius: 25px;
        font-size: 27px;
    }

    .productsWrapper .productContainer .productDetail .addProductBtn div {
        font-size: 14px;
    }

    .productsWrapper .productContainer .customizationWrapper {
        padding: 0 10px 10px;
    }

    .productsWrapper .productContainer .customizationWrapper .sizeCustomizationWrapper {
        padding: 0 5px;
        font-size: 14px;
        height: 55px;
        line-height: 55px;
    }

    .qtyWrapper {
        top: 8px;
        right: 0;
        width: 112px;
    }

    .qtyWrapper .incr, .qtyWrapper .dcr {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 27px;
    }

    .qtyWrapper .qty {
        width: 38px;
        line-height: 35px;
    }

    .qtyWrapper .incr {
        border-top-right-radius: 25px;
        border-bottom-right-radius: 25px;
    }

    .qtyWrapper .dcr {
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
    }

    /************** modal layout *********************/
    .modal .modalBody {
        top: 15px;
        right: 15px;
        bottom: 15px;
        left: 15px;
        border-radius: 5px;
    }

    .modal .modalBody .modalCloseBtn {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 46px;
    }

    .modalTitle {
        font-size: 21px;
        height: 48px;
        line-height: 48px;
    }

    /************** customization modal layout *********************/
    .customizationSection {
        padding: 10px;
    }

    .customizationSection.productHead {
        padding: 15px 10px;
    }

    .customizationSection .customizationHead {
        margin: 5px;
        font-size: 16px;
    }

    .customizationSection .constituent .productName {
        margin-right: 100px;
        font-size: 18px;
    }

    .customizationSection .constituent .productDesc {
        font-size: 12px;
    }

    .customizationSection .constituent .qtyWrapper {
        width: 90px;
    }

    .customizationSection .productName {
        margin-right: 140px;
        font-size: 18px;
    }

    .customizationSection .productPrice {
        width: 130px;
        font-size: 14px;
    }

    .customizationSection .customizationSection .rupeeIcon {
        height: 12px;
        margin: 0 3px -1px 0;
        margin-bottom: -1px;
    }

    .customizationSection .productDesc {
        margin-right: 135px;
        min-height: 45px;
        font-size: 12px;
    }

    .customizationSection .qtyWrapper {
        top: 10px;
        right: 5px;
        width: 118px;
    }

    .customizationSection .qtyWrapper .incr, .customizationSection .qtyWrapper .dcr {
        width: 37px;
        height: 35px;
        line-height: 37px;
        font-size: 27px;
    }

    .customizationSection .qtyWrapper .qty {
        width: 40px;
        line-height: 35px;
    }

    .customizationSection .qtyWrapper .incr {
        border-top-right-radius: 25px;
        border-bottom-right-radius: 25px;
    }

    .customizationSection .qtyWrapper .dcr {
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
    }

    .customizationSection .dimensionBtn {
        width: 50%;
    }

    .customizationSection .dimensionBtn div {
        margin: 5px;
        height: 45px;
        line-height: 45px;
        border-radius: 23px;
    }

    .customizationSection .menuProductBtn {
        max-width: 100%;
    }

    .customizationSection .menuProductBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 23px;
        padding: 0 10px;
    }

    .customizationSection .addonBtn {
        width: 33%;
    }

    .customizationSection .addonBtn div {
        margin: 3px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
    }

    .customizationSection .ingredientProductBtn {
        width: 50%;
    }

    .customizationSection .ingredientProductBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
    }

    .customizationSection .ingredientVariantBtn {
        width: 50%;
    }

    .customizationSection .ingredientVariantBtn div {
        margin: 5px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
    }

    /**************** cart layout styles ***********************/
    .cartHead {
        padding: 20px 0 15px 0;
        font-size: 24px;
    }

    .itemCountHead {
        font-size: 18px;
        padding: 0px 0 15px 15px;
    }

    .cartItemContainer {
        margin-bottom: 15px;
        padding: 15px;
    }

    .cartItemContainer .stockTag {
        font-size: 12px;
        color: rgb(240, 175, 59);
        border: rgb(240, 175, 59) 1px solid;
        padding: 1px 10px;
        border-radius: 10px;
        margin-bottom: 5px;
    }

    .cartItemContainer .stockTag.red {
        color: #b55a5a;
        border: #b55a5a 1px solid;
    }

    .cartItemContainer .itemTitle {
        margin: 0px 125px 0px 0px;
        font-size: 21px;
    }

    .cartItemContainer .itemPrice {
        width: 170px;
        font-size: 18px;
    }

    .cartItemContainer .rupeeIcon {
        height: 14px;
        margin: 0 3px -1px 0;
    }

    .cartItemContainer .itemDetail {
        margin: 5px 125px 5px 0px;
        font-size: 16px;
        min-height: 60px;
    }

    .cartItemContainer .qtyWrapper {
        width: 170px;
    }

    .cartItemContainer .qtyWrapper .incr, .cartItemContainer .qtyWrapper .dcr {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 24px;
    }

    .cartItemContainer .qtyWrapper .qty {
        width: 66px;
        line-height: 50px;
        font-size: 18px;
    }

    .cartItemContainer .actionContainer {

    }

    .cartItemContainer .editItem {
        font-size: 21px;
        padding: 10px 0 5px 0;
    }

    .cartItemContainer .removeItem {
        font-size: 21px;
        padding: 10px 10px 5px 0;
    }

    .emptyCartIcon {
        width: 50%;
        margin-top: 30px;
    }

    .emptyCartHead {
        font-size: 21px;
        margin: 10px 0;
    }

    .emptyCartTag {
        font-size: 16px;
        width: 80%;
        margin: 0 auto;
    }

    .cartItemContainer .orderRemark {
        width: 100%;
        margin: 0;
        font-size: 21px;
        padding: 8px 0;
    }

    .cartItemContainer .orderCoupon {
        margin-right: 100px;
        height: 50px;
        font-size: 28px;
        line-height: 50px;
    }

    .cartItemContainer .couponLink {
        width: 90px;
        height: 50px;
        line-height: 50px;
        font-size: 24px;
    }

    .cartItemContainer .transactionDetails {
        font-size: 18px;
    }

    .cartItemContainer .totalAmount {
        font-size: 21px;
    }

    .cartItemContainer .rupeeIcon {
        margin-right: 5px;
    }

    .cartItemContainer .transactionDetails .txDetailItem {
        line-height: 35px;
        height: 35px;
    }

    .cartItemContainer .taxDetailWrapper {
        padding: 0 10px;
    }

    .cartItemContainer .taxInfo {
        width: 17px;
        margin: 0 0 -3px 5px;
    }

    /************** mobile login layout styles ************/
    .loginSectionTagline {
        font-size: 16px;
        margin: 10px;
    }

    .contactContainer {
        margin-bottom: 10px;
        padding: 10px;
    }

    .contactContainer input {
        font-size: 18px;
        padding: 10px 0px;
    }

    .resendText {
        font-size: 14px;
        line-height: 18px;
    }

    /************* addresses style **************/
    .addressesSubHead {
        padding: 15px 10px 10px 10px;
    }

    .addressContainer {
        padding: 10px;
    }

    .addressContainer .addressBtn {
        width: 13px;
        height: 13px;
        border-radius: 15px;
        border: #62635e 2px solid;
    }

    .addressContainer .addressDetail {
        margin: 0 0 0 30px;
    }

    .addressContainer .addressDetail .addressName {
        font-size: 18px;
        padding: 0 0 10px 0;
    }

    .addNewAddressBtn {
        padding: 10px;
        font-size: 18px;
    }

    /**************** new address style ****************/
    .newAddressSubHead {
        font-size: 16px;
        padding: 15px 0;
    }

    .newAddressInputContainer {
        padding: 10px;
    }

    .newAddressInputContainer input[type=text] {
        font-size: 18px;
        padding: 10px 0px;
    }

    .newAddressInputContainer .selectedLocWrapper {
        margin-right: 110px;
        padding: 10px 0;
    }

    .newAddressInputContainer .changeLocBtn {
        font-size: 18px;
        width: 80px;
        padding: 10px 0;
    }

    .newAddressInputContainer .radioLabels {
        padding: 10px;
    }

    /********** payment modes styles ********/
    .payModeTypeContainer {
        padding: 10px;
        font-size: 18px;
    }

    .payModeTypeContainer .payableAmount {
        padding: 10px 0;
    }

    .payModeTypeContainer .payableAmount .rupeeIcon {
        height: 18px;
        margin: 0 5px -2px 0;
    }

    .payModeTypeContainer .mode {
        padding: 10px 0;
    }

    .payModeTypeContainer .mode span {
        font-size: 24px;
        line-height: 20px;
    }

    /************** order list page ************/
    .orderListSubHead {
        font-size: 16px;
        padding: 15px;
    }

    .orderContainer {
        padding: 10px;
    }

    .orderContainer .orderPics {
        width: 50px;
        margin-right: 8px;
    }

    .orderContainer .orderPics .pic {
        width: 50px;
        height: 50px;
        margin: 1px 1px 0 0;
    }

    .orderContainer .orderContent {
        margin-left: 55px;
    }

    .orderContainer .orderContent .orderId {
        font-size: 14px;
    }

    .orderContainer .orderContent .productName {
        margin-top: 7px;
        font-size: 18px;
        margin-right: 0;
    }

    .orderContainer .orderContent .address {
        margin-top: 10px;
        font-size: 14px;
    }

    /************** order detail page ************/
    .orderDetailSubHead {
        font-size: 16px;
        padding: 15px;
        margin-top: 10px;
    }

    .orderStatusContainer {
        padding: 10px 0;
    }

    .orderStatusContainer .orderStatus {
        width: 23%;
    }

    .orderStatusContainer .orderStatus img {
        height: 50px;
    }

    .orderStatusContainer .separator {
        height: 50px;
        margin-top: 30px;
    }

    .orderStatusContainer .separator img {
        width: 50%;
    }

    .orderItemContainer {
        padding: 10px;
    }

    .orderItemContainer .pic {
        width: 50px;
        height: 50px;
        margin: 0 8px 10px 0;
    }

    .orderItemContainer .productTitle {
        margin: 10px 90px 0 0;
        font-size: 18px;
    }

    .orderItemContainer .productPrice {
        font-size: 16px;
        top: 21px;
        right: 10px;
        width: 80px;
    }

    .orderItemContainer .productPrice .rupeeIcon {
        height: 14px;
        margin: 0 3px -1px 0;
    }

    .orderItemContainer .customizationDetail {
        font-size: 16px;
        width: calc(100% - 90px);
    }

    .orderItemContainer .productQty {
        font-size: 18px;
        width: 80px;
    }

    .orderItemContainer .remark {
        padding: 10px;
    }

    .orderItemContainer .total {
        font-size: 21px;
        line-height: 44px;
        padding: 0 10px;
    }

    .orderItemContainer .right .qty {
        font-size: 14px;
    }

    .orderItemContainer .right .totalPrice {
        font-size: 16px;
    }

}


