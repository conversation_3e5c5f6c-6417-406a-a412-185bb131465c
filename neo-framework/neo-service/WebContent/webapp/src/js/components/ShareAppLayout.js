/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import DesktopShareAppLayout from "./desktop/DesktopShareAppLayout";
import MobileShareAppLayout from "./mobile/MobileShareAppLayout";

export default class ShareAppLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileShareAppLayout props={this.props} />
            )
        } else {
            return (
                <DesktopShareAppLayout props={this.props} />
            )
        }
    }
}