/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileTakeawayRoutingLayout from "./mobile/MobileTakeawayRoutingLayout";
import DesktopTakeawayRoutingLayout from "./desktop/DesktopTakeawayRoutingLayout";

export default class TakeawayRoutingLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileTakeawayRoutingLayout props={this.props} />
            )
        } else {
            return (
                <DesktopTakeawayRoutingLayout props={this.props} />
            )
        }
    }
}