import React from "react";
import {connect} from "react-redux";
import NinjaGameScreen from "./NinjaGameScreen";
import * as SlotMachineAction from "../../actions/SlotMachineActions";
import {gamifiedOfferStage, slotMachineJourneyState, slotMachineStage} from "../../actions/SlotMachineActions";
import {styles} from "./GamifiedOfferStyles";
import appUtil from "../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import {lookupCustomer} from "../../actions/CustomerActions";
import * as UtilityActions from "../../../../src/js/actions/UtilityActions";
import OtpInput from "react-otp-input";

import {
    EmailIcon,
    EmailShareButton,
    FacebookIcon,
    FacebookShareButton,
    TelegramShareButton, TwitterIcon,
    TwitterShareButton, WhatsappIcon,
    WhatsappShareButton
} from "react-share";
import AppUtil from "../../AppUtil";
import * as MyOfferAction from "../../actions/MyOfferActions";
import {value} from "lodash/seq";
import html2canvas from "html2canvas";
import * as CustomerAction from "../../../../src/js/actions/CustomerActions";
import {myOfferStage} from "../../actions/MyOfferActions";


@connect((store) => {
    return {
        currentScreen: store.gamifiedOfferReducer.currentScreen,
        gameScore: store.gamifiedOfferReducer.gameScore,
        gamifiedOffer: store.gamifiedOfferReducer.gamifiedOffer,
        showContactNumberField:store.gamifiedOfferReducer.showContactNumberField,
        showNameField:store.gamifiedOfferReducer.showNameField,
        showOTPField:store.gamifiedOfferReducer.showOTPField,
        contactNumber: store.gamifiedOfferReducer.contactNumber,
        campaignDetail:store.slotMachineReducer.campaignDetail,
        customerName: store.gamifiedOfferReducer.customerName,
        email: store.gamifiedOfferReducer.email,
        utmData: store.slotMachineReducer.utmData,
        termAndCondition: store.slotMachineReducer.termAndCondition,
        otp:store.gamifiedOfferReducer.otp,
        sessionKey:store.gamifiedOfferReducer.sessionKey,
        leaderboardData:store.gamifiedOfferReducer.leaderboardData,
        authToken:store.gamifiedOfferReducer.authToken,
        refLink:store.gamifiedOfferReducer.refLink,
        futureScreen:store.gamifiedOfferReducer.futureScreen,
        isShowGame:store.gamifiedOfferReducer.isShowGame,
        isStartGame:store.gamifiedOfferReducer.isStartGame,
        screenshotString:store.gamifiedOfferReducer.screenshotString,
    };
})

export default class MobileGamifiedOfferLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            isContactFromUrl:false,
            authToken:null,
            otp:0,
            otpResendCounter: 15,
        };
        this.onGameEnd = this.onGameEnd.bind(this);
    }

    componentWillMount() {
        window.scrollTo(0, 0);
    }

    componentDidUpdate(prevProps, prevState) {
        if (this.props.showOTPField && this.state.otpResendCounter === 15) {
            this.startOTPResendWaitCounter();
        }
    }
    offerData = {
        "offerType": "DNBO",
        "offerCode": "CLMAUG238",
        "validityFrom": "2023-03-18",
        "validityTill": "2023-04-16",
        "text": "A Free Chai",
        "chaayosCash": null,
        "offerTnCString": null,
        "maxUsage": 1,
        "existingOffer": null
    }

    componentDidMount() {
        this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.gameHomeScreen))
        const queryParams = new URLSearchParams(window.location.search);
        if(appUtil.checkEmpty(queryParams.get("token"))){
            this.props.dispatch(SlotMachineAction.setCampaignDetail({"campaignToken":"3416A75F4CEA9109507CACD8E2F2AEFC","campaignId":41}));
        }else{
            var campaignId = !appUtil.checkEmpty(this.getCookie("campaignId")) ? this.getCookie("campaignId") : 41;
            this.props.dispatch(SlotMachineAction.setCampaignDetail({"campaignToken":queryParams.get("token"),"campaignId": campaignId}))
        }
        var utmData={
            "utmSource":!appUtil.checkEmpty(queryParams.get("utm_source")) ? queryParams.get("utm_source") : "DEFAULT",
            "utmMedium":!appUtil.checkEmpty(queryParams.get("utm_medium")) ? queryParams.get("utm_medium") : "DEFAULT"
        }
        this.props.dispatch(SlotMachineAction.setUtmData(utmData));
        var token = this.getCookie("token");
        this.props.dispatch(SlotMachineAction.setAuthToken(token));
        var urlContact = queryParams.get("contact");
        if(urlContact != null && urlContact.length >= 10){
            urlContact=urlContact.substring(urlContact.length-10);
        }
        if(!appUtil.checkEmpty(urlContact) && appUtil.validContact(urlContact)){
            this.props.dispatch(SlotMachineAction.setContactNumber(urlContact))
        }
        this.props.dispatch(MyOfferAction.recordJourney(gamifiedOfferStage.gameHomeScreen,"mobile",null))
        fbq('trackCustom', 'NinjaGamePageView');
        trackUtils.trackNinjaGamePageView();
    }

    onGameEnd(data){
        console.log("Game END");
        console.log(data);
        this.props.dispatch(SlotMachineAction.setShowGame(false))
        document.getElementById('ninjaPlayGround').remove()
        this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.yourScore))
        this.props.dispatch(SlotMachineAction.setGameScore(data.score))
    }

    resendVerification = () => {
        this.startOTPResendWaitCounter();
        this.props.dispatch(CustomerAction.resendVerification(this.props.contactNumber));
    };

    startOTPResendWaitCounter() {
        let sec = 15;
        let interval = setInterval(() => {
            this.setState({otpResendCounter: --sec});
            //console.log("seconds:: " + sec);
            if (sec === 0) {
                clearInterval(interval);
            }
        }, 1000);
    }


    handleSave = () => {
        html2canvas(document.querySelector("#offerCanvas")).then(function (canvas){
            const downloadLink = document.createElement('a');
            downloadLink.href = canvas.toDataURL("..assets/image/jpeg").replace("image/jpeg", "image/octet-stream");
            downloadLink.download = "offer.jpg";
            downloadLink.click();
        })
    };

    gotoSignInScreen(){
        console.log("*************")
        this.props.dispatch(SlotMachineAction.setGamifiedFutureScreen(gamifiedOfferStage.offerScreen))
        if(AppUtil.checkEmpty(this.props.sessionKey)){
            this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.signIn))
        }else{
            this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getOTP))
            this.props.dispatch(SlotMachineAction.getGamifiedOffer(null, this.props.contactNumber, this.props.campaignDetail.campaignToken, this.props.customerName,
                this.props.email, null, this.props.utmData.utmSource, this.props.utmData.utmMedium, this.props.authToken, "1", this.showConfetti, true, 'mobile'));
        }
    }

    handleOTPChange(otp){
        this.props.dispatch(SlotMachineAction.setCustomerOtp(otp))
    }

    onPhoneInputChange(){
        var value =  document.getElementById("phoneNumber").value;
        this.props.dispatch(SlotMachineAction.setContactNumber(value));
        if (appUtil.validContact(this.props.contactNumber)){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            // this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,value,this.props.campaignDetail.campaignToken,name,this.state.email,
            //     null,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.props.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"desktop"))
        }else if(!appUtil.validContact(this.props.contactNumber.length)){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false))
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
        }else{
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false))
        }
    }

    gotoGameRule(){
        this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.gameRule))
    }
    goBackToHomeScreen(){
        this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.gameHomeScreen))
    }

    gotoLeaderboard(){
        this.props.dispatch(SlotMachineAction.setGamifiedFutureScreen(gamifiedOfferStage.leaderboardScreen))
        if(!AppUtil.checkEmpty(this.props.sessionKey) || !AppUtil.checkEmpty(this.props.contactNumber)){
            this.props.dispatch(SlotMachineAction.getLeaderBoardData(this.props.contactNumber, this.props.campaignDetail.campaignToken,"mobile"))
        }else{
            this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.signIn))
        }
    }

    getBasicInfo() {
        this.props.dispatch(UtilityActions.showFullPageLoader("Verifying Otp"))
        console.log("On getBasicInfo");
        if(this.props.showOTPField){
            var otp = this.props.otp;
            this.verifySignUpOfferCustomer(otp)
            return;
        }
        var value =  document.getElementById("contact").value;
        if (appUtil.checkEmpty(value) || !appUtil.validContact(value)) {
            this.props.dispatch(SlotMachineAction.setContactNumber(null));
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
        }
        else {
            this.props.dispatch(SlotMachineAction.setContactNumber(value));
            this.props.dispatch(lookupCustomer(value,"desktop"));
        }
    }

    redirectToChaayos(){
        window.location.replace("https://cafes.chaayos.com");
    }

    getNBOGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center',justifyContent:'space-between'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'30px', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px'}}>Congratulation</p>
                <p style={{fontSize:'20px', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'14vw', textAlign:'center', marginTop:'20px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>{offerData.text}</p>
            <div style={{...styles.couponCodeContainer, width:'80%'}}>
                <div style={styles.couponCodeBox}>
                    <p style={styles.couponCodeText}>{offerData.offerCode}</p>
                </div>
                <div style={{width:'100%'}} onClick={()=>{this.props.dispatch(SlotMachineAction.copyCodeToClipboard(offerData.offerCode, "Coupon code Copied"))}}>
                    <p style={styles.useCodeText}>Copy Code</p>
                </div>
            </div>
        </div>)
    }
    getNoOfferPage(offerData){
        return (<div style={{width:'60%', display:'flex', flexDirection:'column', alignItems:'center'}}>
            <p style={{fontSize:'30px', fontWeight:'bold', color:'white'}}>Sorry!</p>
            <p style={{fontSize:'8vw', textAlign:'center', lineHeight:'40px',
                fontFamily:"'Gotham Black', sans-serif", width:'100%', fontWeight:'900'}}>Better Luck Next Time</p>
            <div style={styles.couponCodeContainer}>
                <div style={{width:'100%'}}>
                    <p style={styles.useCodeText}>{'uh oh!'}</p>
                    <p style={styles.useCodeText}>{'Looks Like it is taking longer!'}</p>
                    <p style={styles.useCodeText}>{'to brew your offer'}</p>
                </div>
            </div>
        </div>)
    }

    getDNBOGameOfferScreenView(offerData){
        return(<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center',justifyContent:'space-between'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'30px', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px'}}>Congratulation</p>
                <p style={{fontSize:'20px', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'14vw', textAlign:'center', marginTop:'20px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>{offerData.text}</p>
            <div style={{...styles.couponCodeContainer, width:'80%'}}>
                <div style={styles.couponCodeBox}>
                    <p style={styles.couponCodeText}>{offerData.offerCode}</p>
                </div>
                <div style={{width:'100%'}} onClick={()=>{this.props.dispatch(SlotMachineAction.copyCodeToClipboard(offerData.offerCode, "Coupon code Copied"))}}>
                    <p style={styles.useCodeText}>Copy Code</p>
                </div>
            </div>
        </div>)
    }
    getDays(startDate, endDate){
        return (new Date(endDate) - new Date(startDate))/(1000*60*60*24);
    }

    getDateString(date){
        return new Date(date).toDateString().substring(8,10)+" "+
            new Date(date).toDateString().substring(4,7)+" "+
            new Date(date).toDateString().substring(11)
    }
    showConfetti(){
        var times =0;
        const canvas = document.getElementById('your_custom_canvas_id')
        const jsConfetti = new JSConfetti({ canvas })
        document.querySelector("#crowdCheer").play();
        jsConfetti.addConfetti({
            confettiColors: [
                '#ffcd2b','#070105','rgba(255,233,0,0.83)','rgba(189,87,0,0.83)',
                'rgba(238,255,0,0.83)','#ffaa63'
            ],
            confettiNumber: 300,
            confettiRadius: 5,
        })
        var timeInterval = setInterval(()=>{
            jsConfetti.addConfetti({
                confettiColors: [
                    '#ffcd2b','#070105','rgba(255,233,0,0.83)','rgba(189,87,0,0.83)',
                    'rgba(238,255,0,0.83)','#ffaa63'
                ],
                confettiNumber: 300,
                confettiRadius: 5,
            })
            if(times === 1){
                clearInterval(timeInterval);
            }
            times+=1;
        },2000);
        var stopCheerSound = setTimeout(()=>{
            document.querySelector("#crowdCheer").pause();
            document.querySelector("#crowdCheer").currentTime =0;
            clearInterval(stopCheerSound);
        },6000);
    }

    getMembershipGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'30px', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px'}}>Congratulation</p>
                <p style={{fontSize:'20px', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'12vw', textAlign:'center', marginTop:'75px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>Chaayos Select Membership</p>

            <div style={styles.couponCodeContainer}>
                <div style={{width:'100%'}}>
                    <p style={{...styles.useCodeText, fontSize:'30px'}}>Worth 199&#x20b9;</p>
                </div>
                <div style={{width:'100%'}}>
                    <p style={{...styles.useCodeText, textDecoration:'none', fontSize:'20px'}}>for next {this.getDays(offerData.validityFrom,offerData.validityTill)} days</p>
                </div>
            </div>
        </div>)
    }

    getCashGameOfferScreenView(offerData){
        return (<div style={{width:'100%', display:'flex', flexDirection:'column', alignItems:'center',justifyContent:'space-between'}}>
            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                <p style={{fontSize:'30px', fontWeight:'900', color:'#000000', fontFamily:"'Inter',sans-serif", textShadow:'#0000003b -2px 2px 3px'}}>Congratulation</p>
                <p style={{fontSize:'20px', fontWeight:'500', color:'#000000', textShadow:'#0000003b -2px 2px 3px',textAlign:'center'}}>You have {offerData.isExistingOffer ? "already" : ""} won</p>
            </div>
            <p style={{fontSize:'20vw', textAlign:'center', marginTop:'75px', lineHeight:'55px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'900', color:'#fff', textShadow:'10px 4px 14px #0000008c'}}>{offerData.chaayosCash}</p>
            <p style={{fontSize:'9vw', textAlign:'center', textShadow:'10px 4px 14px #0000008c', color:'#fff', marginTop:'20px',
                fontFamily:"'Inter',sans-serif", width:'100%', fontWeight:'bolder'}}>Chaayos Cash</p>

            <div style={styles.couponCodeContainer}>
                <div style={{...styles.flexCenterColumn,width:'100%'}}>
                    <p style={{...styles.useCodeText, fontSize:'5vw'}}>use before {this.getDateString(offerData.validityTill)}</p>
                </div>
            </div>
        </div>)
    }

    verifySignUpOfferCustomer(otp) {
        console.log("On verifyOTP this.state.otp ::: this.state.otp.length " + otp.length);
        if(!AppUtil.checkEmpty(this.props.sessionKey)){
            this.props.dispatch(SlotMachineAction.getGamifiedOffer(null, this.props.contactNumber, this.props.campaignDetail.campaignToken, this.props.customerName,
                this.props.email, otp, this.props.utmData.utmSource, this.props.utmData.utmMedium, this.props.authToken, "1", this.showConfetti, true, 'mobile'));
        }
        if (!appUtil.validOtp(otp)) {
            this.props.dispatch(UtilityActions.showPopup("Please enter valid OTP!", "error", 3000));
            return;
        }
        var name = document.querySelector("#name");
        this.props.dispatch(SlotMachineAction.setCustomerName(name))
        if(this.props.showNameField && !appUtil.validName(name)){
            UtilityActions.showPopup("Invalid Name Entered","error", 2000);
            return;
        }
        if (!appUtil.checkEmpty(this.props.contactNumber)) {
            this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getOTP))
            this.props.dispatch(SlotMachineAction.getGamifiedOffer(null, this.props.contactNumber, this.props.campaignDetail.campaignToken, this.props.customerName,
                 this.props.email, otp, this.props.utmData.utmSource, this.props.utmData.utmMedium, this.props.authToken, "1", this.showConfetti, true, 'mobile'));
        }
    }

    getCookie(cname) {
        let name = cname + "=";
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for(let i = 0; i <ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }
    getTNC(){
        var tncHtml =``;
        if(!AppUtil.checkEmpty(this.props.termAndCondition)){
            this.props.termAndCondition.map((value => {
                if(value !== ""){
                    tncHtml = tncHtml + `${value}` + `\n`
                }
            }))
        }
        return tncHtml;
    }

    render() {
        var h = window.innerHeight;
        var w = window.innerWidth;
        return (
            <div className="row "  style={{width:w+"px", height: h+"px", margin:'0px', padding:'0px'}}>
                <audio id="crowdCheer" src="./../../../img/SlotGame/crowdCheer.wav"/>
                <div  className="col-sm-4" style={{backgroundColor: "red",  height:'0px'}}/>
                <div  className="col-sm-4" style={styles.middleChildContainer}>
                    {/*TODO Game Home Screen*/}
                    {this.props.currentScreen === gamifiedOfferStage.gameHomeScreen ?
                        <div style={styles.gameHomeScreenContainer}>
                            <div style={{...styles.flexCenterColumn,height:'95%', width:'90%',backgroundColor:'#ffffffcc',borderRadius:'15px',paddingTop:'20px'}}>
                                <img style={{width:'65%'}} src="./../../../img/ninjaGameImg/images/frappeGameHeadText.webp" />
                                <img style={{width:'90%', marginTop:'20px'}} src="./../../../img/ninjaGameImg/images/sipSliceText.png" />
                                <div style={{...styles.flexCenterColumn,backgroundImage:`url("../../../img/ninjaGameImg/images/homeFrappeGroup.png")`,backgroundRepeat:'no-repeat',backgroundSize:'contain',backgroundPosition:'center',width:'90%',marginTop:'30px'}}>
                                    <img style={{width:'80%'}} src="../../../img/ninjaGameImg/images/ninjaCharacter.gif"/>
                                </div>
                                <img style={{width:'90%', marginTop:'50px'}} src="./../../../img/ninjaGameImg/images/playSliceShare.png" />
                                <div style={{ height:'30%', width:'100%', textAlign:'-webkit-center'}} >
                                    <div style={{...styles.flexCenterColumn ,width:'60%',  height:'50px', padding:'5px', border:'2px solid #bf6430', borderRadius:'100px', marginTop:'40px'}}
                                         onClick={()=>{
                                             this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.gameScreen))
                                             this.props.dispatch(SlotMachineAction.setShowGame(true))
                                             document.getElementById('ninjaPlayGround').contentWindow.start();
                                             this.props.dispatch(MyOfferAction.recordJourney(gamifiedOfferStage.gameScreen,"mobile",null))
                                             fbq('trackCustom', 'NinjaGamePlayed');
                                             trackUtils.trackSlotMachineGamePlayed({});
                                         }} >
                                        <div style={{...styles.flexCenterColumn, borderRadius:'100px', backgroundColor:'#bf6430', height:'100%', width:'100%'}} >
                                            <p style={{fontSize:'4vw', fontWeight:'500', color:'white'}}>Play Now</p>
                                        </div>
                                    </div>
                                </div>
                                <div style={{width:'100%'}} onClick={()=>{
                                    this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.gameRule))
                                }}>
                                    <p style={{...styles.useCodeText, fontSize:'20px',marginBottom:'20px'}}>Read Game Rules</p>
                                </div>
                            </div>
                        </div> : <div/>}
                    {(true || this.props.currentScreen === gamifiedOfferStage.gameScreen)?<NinjaGameScreen showGame={this.props.isShowGame} startGame={this.props.isStartGame} onGameEnd={(data)=>{this.onGameEnd(data)}}/> : <div/>}
                    {(this.props.currentScreen === gamifiedOfferStage.yourScore) ?
                        <div style={{...styles.gameScoreScreenContainer, ...styles.flexCenterColumn}}>
                            <div style={{ display:'flex', flexDirection:'column', justifyContent:'space-evenly', alignItems:'center', height:'90%', width:'90%',backgroundColor:'#ffffffad',borderRadius:'15px'}}>
                                <div style={{display:'flex', flexDirection:'row', justifyContent:'center',alignItems:'center', flex:2}}>
                                    <p style={{fontSize:'7vw', fontWeight:'900'}}>Your score is</p>
                                    <p style={{fontSize:'14vw', fontWeight:'900', color:'#387549', textShadow:'#cfe3ae -5px 0px, #ffffff -7px 0px', marginLeft:'20px'}}>{this.props.gameScore}</p>
                                </div>
                                <div style={{...styles.myScoreContainer, ...styles.flexCenterColumn}}>
                                    <img height={230} width={230} src="./../../../img/ninjaGameImg/images/giftBox.gif"/>
                                </div>
                                <div style={{flex:4, marginTop:'25px',width:'100%', ...styles.flexCenterColumn}}>
                                    { this.props.showContactNumberField ? <div style={{...styles.flexCenterColumn,width:'100%'}}>
                                        <p style={{fontSize:'4.5vw', marginBottom : '5%', fontWeight:'900'}}>Enter your phone number to reveal gift</p>
                                        <input style={styles.signInInputField} type='number' value={this.props.contactNumber} placeholder={'Contact'} id="contact"/> </div>:<div/>}
                                    { this.props.showNameField ?<div style={{...styles.flexCenterColumn,width:'100%', height:'15%'}}>
                                        <p style={{fontSize:'6vw', marginBottom : '10%', fontWeight:'900'}}>Enter your Name</p>
                                        <input style={{...styles.signInInputField, marginBottom:'0px'}}type='text' placeholder={'Name'} id="name"/></div> : <div/>}
                                    {this.props.showOTPField ? <div style={{...styles.flexCenterColumn,width:'100%', height:'20%'}}>
                                        <p style={{fontSize:'3.5vw', textAlign:'center',width:'80%', fontWeight:'bold'}}>We just send a 4 digit OTP to XXXXXX{this.props.contactNumber.substring(6,10)}</p>
                                        <p style={{fontSize:'3vw', textAlign:'center',width:'80%', fontWeight:'bold', textDecoration:'underline'}}
                                           onClick={()=>{
                                               this.props.dispatch(SlotMachineAction.setShowContactField(true));
                                               this.props.dispatch(SlotMachineAction.setCustomerName(false));
                                               this.props.dispatch(SlotMachineAction.setShowOtpField(false));
                                           }}
                                        >Change Number</p>
                                    </div>:<div/>}
                                    { this.props.showOTPField ?<div style={{...styles.flexCenterColumn,width:'100%', marginTop:'15px'}}>
                                        <OtpInput
                                            containerStyle={styles.oTPInputContainer}
                                            inputStyle={styles.oTPInputBox}
                                            numInputs={4}
                                            errorStyle={styles.oTPErrorStyle}
                                            onChange={(otp)=>{this.handleOTPChange(otp)}}
                                            separator={<span> </span>}
                                            isInputNum={true}
                                            shouldAutoFocus
                                            value={this.props.otp}
                                            placeholder={'    '}
                                        />
                                        <div style={styles.resendOTPSection}>
                                            <span>Didn't receive OTP?</span>
                                            {this.state.otpResendCounter === 0 ? (
                                                <span className={'resendLink'} style={{fontWeight: 'bold'}}
                                                      onClick={this.resendVerification}> RESEND OTP</span>
                                            ) : (
                                                <span
                                                    style={{fontWeight: 'bold'}}> Try again in {this.state.otpResendCounter}</span>
                                            )}
                                        </div>
                                    </div> : <div/>}
                                </div>
                                <div style={{flex:2,width:'80%'}}>
                                    <div style={{...styles.flexCenterColumn ,width:'100%', height:'50%', padding:'5px', border:'2px solid #bf6430', borderRadius:'100px'}}
                                         onClick={()=>{this.getBasicInfo()}} >
                                        <div style={{...styles.flexCenterColumn, borderRadius:'100px', backgroundColor:'#bf6430', height:'100%', width:'100%'}} >
                                            <p style={{fontSize:'4vw', fontWeight:'500', color:'white'}}>Unwrap Gift</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> : <div/>}
                    {(this.props.currentScreen === gamifiedOfferStage.signIn) ?
                        <div style={styles.signInContainerStyle}>
                            { this.props.showContactNumberField ? <div style={{...styles.flexCenterColumn,width:'100%', height:'20%'}}>
                                <p style={{fontSize:'6vw', marginBottom : '10%', fontWeight:'900'}}>Log in to reveal gifts</p>
                                <input style={styles.signInInputField} type='number' value={this.props.contactNumber} placeholder={'Contact'} id="contact"/> </div>:<div/>}
                            {this.props.showOTPField ? <div style={{...styles.flexCenterColumn,width:'100%', height:'20%'}}>
                                <p style={{fontSize:'7vw', marginBottom:'4vw'}}>Enter 4 digit OTP</p>
                                <p style={{fontSize:'4vw', marginBottom:'10px', textAlign:'center',width:'80%'}}>We just send an OTP to your mobile number</p>
                            </div>:<div/>}
                            { this.props.showNameField ?<div style={{...styles.flexCenterColumn,width:'100%', height:'15%'}}>
                                <input style={{...styles.signInInputField, marginBottom:'0px'}}type='text' placeholder={'Name'} id="name"/></div> : <div/>}
                            { this.props.showOTPField ?<div style={{...styles.flexCenterColumn,width:'100%', marginTop:'15px'}}>
                                <OtpInput
                                    containerStyle={styles.oTPInputContainer}
                                    inputStyle={styles.oTPInputBox}
                                    numInputs={4}
                                    errorStyle={styles.oTPErrorStyle}
                                    onChange={(otp)=>{this.handleOTPChange(otp)}}
                                    separator={<span> </span>}
                                    isInputNum={true}
                                    shouldAutoFocus
                                    value={this.props.otp}
                                    placeholder={'    '}
                                />
                                <div style={styles.resendOTPSection}>
                                    <span>Didn't receive OTP?</span>
                                    {this.state.otpResendCounter === 0 ? (
                                        <span className={'resendLink'} style={{fontWeight: 'bold'}}
                                              onClick={this.resendVerification}> RESEND OTP</span>
                                    ) : (
                                        <span
                                            style={{fontWeight: 'bold'}}> Try again in {this.state.otpResendCounter}</span>
                                    )}
                                </div>
                            </div> : <div/>}
                            <div style={{...styles.flexCenterColumn ,width:'75%', height:'45px', padding:'5px', border:'2px solid #bf6430', borderRadius:'100px', marginTop:'40px'}}
                                 onClick={()=>{this.getBasicInfo()}} >
                                <div style={{...styles.flexCenterColumn, borderRadius:'100px', backgroundColor:'#bf6430', height:'100%', width:'100%'}} >
                                    {!this.props.showOTPField ?<p style={{fontSize:'4vw', fontWeight:'500', color:'white'}}>Submit</p> :
                                    <p style={{fontSize:'4vw', fontWeight:'500', color:'white'}}>Verify OTP</p>}
                                </div>
                            </div>
                        </div>:<div/>}
                    {this.props.currentScreen === gamifiedOfferStage.offerScreen ?
                        <div style={styles.gameOfferScreenContainer} id="offerCanvas">
                            {this.props.gamifiedOffer[0].offerType === "NBO"?this.getNBOGameOfferScreenView(this.props.gamifiedOffer[0]) :
                                this.props.gamifiedOffer[0].offerType === "DNBO"?this.getDNBOGameOfferScreenView(this.props.gamifiedOffer[0]):
                                    this.props.gamifiedOffer[0].offerType === "CHAAYOS_CASH"?this.getCashGameOfferScreenView(this.props.gamifiedOffer[0]):
                                        this.props.gamifiedOffer[0].offerType === "MEMBERSHIP" ? this.getMembershipGameOfferScreenView(this.props.gamifiedOffer[0]):
                                            this.getNoOfferPage(this.props.gamifiedOffer[0])}
                            <div style={{...styles.flexCenterColumn, width:'100%'}}>
                                <div style={{width:'100%'}} onClick={()=>{this.handleSave()}}>
                                    <p style={{...styles.useCodeText, fontSize:'20px'}}>Take a screenshot</p>
                                </div>
                                <div style={{display:'flex', justifyContent:'space-evenly',width:'100%'}}>
                                    <div style={{...styles.flexCenterColumn ,width:'40%', height:'40px', padding:'5px', border:'2px solid #bf6430', borderRadius:'100px', marginTop:'40px'}}
                                         onClick={()=>{document.querySelector("#shareModal").style.display="flex"}} >
                                        <div style={{...styles.flexCenterColumn, borderRadius:'100px', backgroundColor:'#bf6430', height:'100%', width:'100%'}} >
                                            <p style={{fontSize:'3.5vw', fontWeight:'500', color:'#fff'}}>Share</p>
                                        </div>
                                    </div>
                                    <div style={{...styles.flexCenterColumn ,width:'40%', height:'40px', padding:'5px', border:'2px solid #bf6430', borderRadius:'100px', marginTop:'40px'}}
                                         onClick={()=>{this.gotoLeaderboard()}} >
                                        <div style={{...styles.flexCenterColumn, borderRadius:'100px', backgroundColor:'#bf6430', height:'100%', width:'100%'}} >
                                            <p style={{fontSize:'3.5vw', fontWeight:'500', color:'#fff'}}>Leaderboard</p>
                                        </div>
                                    </div>
                                </div>
                                <div style={{...styles.tncContainer,marginTop:'30px'}}>
                                    <p style={styles.tncHeading}>Terms and Conditions</p>
                                    <p id="tnc" style={{position:'absolute',top:-100000}}/>
                                    {!AppUtil.checkEmpty(this.props.termAndCondition) ?
                                        this.props.termAndCondition.map(function (value){
                                            return (
                                                <p style={styles.tncText}>{value}</p>
                                            )
                                        }):<div/>}
                                </div>
                            </div>
                        </div> : <div/>
                    }
                    {this.props.currentScreen === gamifiedOfferStage.gameRule ?
                        <div style={styles.gameRuleScreenContainer} id="offerCanvas">
                            <div style={{width:'100%'}}>
                                <div style={{...styles.flexCenterColumn,backgroundColor:'#ffffffcc', padding:'5px', borderRadius:'10px', width:'50px', position:'fixed' }}>
                                    <img height={30} width={30} src="../../../img/back.svg" onClick={()=>{this.goBackToHomeScreen()}}/>
                                </div>
                            </div>
                            <div style={{...styles.flexCenterColumn,backgroundColor:'#ffffffcc', padding:'10px',borderRadius:'15px', marginTop:'50px'}}>
                                <p style={{fontSize:'17px', fontWeight:'900',marginBottom:'10px', textAlign:'center'}}>How you play Frappe ninja and rack up points as a master ninja</p>
                                <p style={{fontSize:'17px', fontWeight:'900',marginBottom:'10px', textAlign:'center'}}>-: Game Rule :-</p>
                                <ul style={styles.gameRuleHeadStyle}>
                                    <li style={styles.gameRuleTextStyle}>Slash all Chai Frappe and collect points in 30 seconds.</li>
                                    <li style={styles.gameRuleTextStyle}>Your game ends once you hit a bomb</li>
                                    <li style={styles.gameRuleTextStyle}>You get 30 seconds to play game and collect points</li>
                                    <li style={styles.gameRuleTextStyle}>Total Chai Frappes collected count for leaderboard rank.</li>+
                                </ul>
                                <p style={{fontSize:'17px', fontWeight:'900', marginTop:'30px',marginBottom:'10px', textAlign:'center'}}>-: Collect Additional points :-</p>
                                <ul style={styles.gameRuleHeadStyle}>
                                    <li style={styles.gameRuleTextStyle}>Share game with friends on social media</li>
                                    <li style={styles.gameRuleTextStyle}>The more you share the game's link to your friends and family on social media, the higher your leaderboard rank will be.</li>
                                    <li style={styles.gameRuleTextStyle}>2 point will be added to your collection for each new player who joins the game via your referral link.</li>
                                    <li style={styles.gameRuleTextStyle}>Your ranking on the leader board will improve with every extra point that you collect.</li>
                                </ul>
                                <p style={{fontSize:'17px', fontWeight:'900', marginTop:'30px',marginBottom:'10px', textAlign:'center'}}>-: Game Objective :-</p>
                                <ul style={styles.gameRuleHeadStyle}>
                                    <li style={styles.gameRuleTextStyle}>Slash more frappe = huge score = leaderboard rank</li>
                                </ul>
                                <p style={{fontSize:'17px', fontWeight:'900', marginTop:'30px',marginBottom:'10px', textAlign:'center'}}>-: Winners :-</p>
                                <ul style={styles.gameRuleHeadStyle}>
                                    <li style={styles.gameRuleTextStyle}>Top 10 weekly winners will get Chai Frappe coupons.</li>
                                    <li style={styles.gameRuleTextStyle}>Top 25 monthly winners will receive Chai Frappe coupons.</li>
                                    <li style={styles.gameRuleTextStyle}>Each game player will get a special surprise gift from Chaayos.</li>
                                </ul>
                            </div>
                        </div> : <div/>
                    }
                    {this.props.currentScreen === gamifiedOfferStage.leaderboardScreen ?
                        <div style={styles.gameRuleScreenContainer} id="offerCanvas">
                            <div style={{width:'100%'}}>
                                <div style={{...styles.flexCenterColumn,backgroundColor:'#ffffffcc', padding:'5px', borderRadius:'10px', width:'50px', position:'fixed' }}>
                                    <img height={30} width={30} src="../../../img/back.svg" onClick={()=>{
                                        this.props.dispatch(SlotMachineAction.setGamifiedCurrentScreen(gamifiedOfferStage.offerScreen));
                                    }}/>
                                </div>
                            </div>
                            <div style={{...styles.flexCenterColumn,backgroundColor:'#ffffffcc', padding:'10px',borderRadius:'15px', marginTop:'50px',width:'100%'}}>
                            <div style={{ padding:'10px',borderRadius:'15px',width:'100%' ,marginTop:'20px',
                                display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center'}}>
                                {!AppUtil.checkEmpty(this.props.leaderboardData) ?<div style={{width:'80%', display:'flex',flexDirection:'row',justifyContent:'center',alignItems:'center'}}>
                                    <div style={styles.leaderBoardHeading}>Rank</div>
                                    <div style={styles.leaderBoardHeading}>User</div>
                                    <div style={styles.leaderBoardHeading}>Score</div>
                                </div>:<div/>}
                                {!AppUtil.checkEmpty(this.props.leaderboardData) ?
                                    this.props.leaderboardData.top10Score.map((item, index)=>{
                                        if(!AppUtil.checkEmpty(this.props.leaderboardData) && !AppUtil.checkEmpty(this.props.leaderboardData.yourScore) &&
                                            this.props.leaderboardData.yourScore.refCode === item.refCode){
                                            return (
                                                <div style={{width:'90%', display:'flex',flexDirection:'row',justifyContent:'center',alignItems:'center'}}>
                                                    <div style={{...styles.leaderBoardSubRow,height:'40px',fontSize:'17px',borderRadius:'100px 0px 0px 100px',backgroundImage:'linear-gradient(0deg, #ff8600,#ffe826, #ff8600)'}}>{index+1}</div>
                                                    <div style={{...styles.leaderBoardSubRow,height:'40px',fontSize:'17px',backgroundImage:'linear-gradient(0deg, #ff8600,#ffe826, #ff8600)'}}>{item.name}</div>
                                                    <div style={{...styles.leaderBoardSubRow,height:'40px',fontSize:'17px',borderRadius:'0px 100px 100px 0px',backgroundImage:'linear-gradient(0deg, #ff8600,#ffe826, #ff8600)'}}>{item.totalScore}</div>
                                                </div>
                                            )
                                        }else{
                                            return (
                                                <div style={{width:'80%', display:'flex',flexDirection:'row',justifyContent:'center',alignItems:'center'}}>
                                                    <div style={styles.leaderBoardSubRow}>{index+1}</div>
                                                    <div style={styles.leaderBoardSubRow}>{item.name}</div>
                                                    <div style={styles.leaderBoardSubRow}>{item.totalScore}</div>
                                                </div>
                                            )
                                        }
                                    }):<div/>}
                                {!AppUtil.checkEmpty(this.props.leaderboardData) && !this.props.leaderboardData.inTop10 ?
                                    <div style={{width:'90%', display:'flex',flexDirection:'row',justifyContent:'center',alignItems:'center'}}>
                                        <div style={{...styles.leaderBoardSubRow,height:'40px',fontSize:'17px',borderRadius:'100px 0px 0px 100px',backgroundImage:'linear-gradient(0deg, #ff8600,#ffe826, #ff8600)'}}>{this.props.leaderboardData.rank}</div>
                                        <div style={{...styles.leaderBoardSubRow,height:'40px',fontSize:'17px',backgroundImage:'linear-gradient(0deg, #ff8600,#ffe826, #ff8600)'}}>{this.props.leaderboardData.yourScore.name}</div>
                                        <div style={{...styles.leaderBoardSubRow,height:'40px',fontSize:'17px',borderRadius:'0px 100px 100px 0px',backgroundImage:'linear-gradient(0deg, #ff8600,#ffe826, #ff8600)'}}>{this.props.leaderboardData.yourScore.totalScore}</div>
                                    </div>:<div/>}
                            </div>
                            <p style={{width:'100%',textAlign:'center',marginTop:'30px',fontSize:'18px'}}>Share with your friends and get additional points</p>
                            { !appUtil.checkEmpty(this.props.gamifiedOffer) && !appUtil.checkEmpty(this.props.gamifiedOffer[0])
                            && !appUtil.checkEmpty(this.props.gamifiedOffer[0].refCode) && !appUtil.checkEmpty(this.props.refLink) ?
                                <div style={{display:'flex',justifyContent:'center',alignItems:'center',width:'100%',marginTop:'30px'}}>
                                    <div style = {{margin : '5px'}}>
                                        <FacebookShareButton title={"Chai Frappe Ninja"} quote={this.props.refLink.message} url={this.props.refLink.message+this.props.refLink.url}>
                                            <FacebookIcon size={35} logoFillColor="white" round={true} />
                                        </FacebookShareButton>
                                    </div>

                                    <div style = {{margin : '5px'}}>
                                        <WhatsappShareButton title={''} seperator={this.props.refLink.message} url={this.props.refLink.message+this.props.refLink.url}>
                                            <WhatsappIcon size={35} logoFillColor="white" round={true} />
                                        </WhatsappShareButton>
                                    </div>

                                    <div style = {{margin : '5px'}}>
                                        <TwitterShareButton title={this.props.refLink.message} url={this.props.refLink.message+this.props.refLink.url}>
                                            <TwitterIcon size={35} logoFillColor="white" round={true} />
                                        </TwitterShareButton>
                                    </div>

                                    <div style = {{margin : '5px'}}>
                                        <EmailShareButton subject={"Inviting You to Chaayos #ChaiFrappeNinja Game"} body={this.props.refLink.message+this.props.refLink.url}>
                                            <EmailIcon size={35} logoFillColor="white" round={true} />
                                        </EmailShareButton>
                                    </div>
                                    <div style={{...styles.flexCenterColumn,height:'40px',width:'40px'}}>
                                        <img height={40} width={40} src="../../../img/link.png" onClick={()=>{this.props.dispatch(SlotMachineAction.copyCodeToClipboard(this.props.refLink.message+this.props.refLink.url), "Message Copied")}}/>
                                    </div>
                                </div>:<div/>
                            }
                            </div>
                        </div> : <div/>
                    }
                    <div id="shareModal" style={{...styles.flexCenterColumn,display:'none',position:'absolute',height:window.innerHeight, width: window.innerWidth, backgroundColor:'#000000a3',top:'0px'}}
                         onClick={()=>{document.querySelector("#shareModal").style.display="none"}}>
                        <div  style={{...styles.flexCenterColumn,height:'40%', width:'90%', backgroundColor:'#fff', borderRadius:'15px'}}>
                            <p style={{width:'100%',textAlign:'center',marginTop:'30px',fontSize:'18px'}}>Share with your friends and get additional points</p>
                            { !appUtil.checkEmpty(this.props.gamifiedOffer) && !appUtil.checkEmpty(this.props.gamifiedOffer[0])
                            && !appUtil.checkEmpty(this.props.gamifiedOffer[0].refCode) && !appUtil.checkEmpty(this.props.refLink) ?
                                <div style={{display:'flex',justifyContent:'center',alignItems:'center',width:'100%',marginTop:'30px'}}>
                                    <div style = {{margin : '5px'}}>
                                        <FacebookShareButton title={"Chai Frappe Ninja"} quote={this.props.refLink.message} url={this.props.refLink.url}>
                                            <FacebookIcon size={35} logoFillColor="white" round={true} />
                                        </FacebookShareButton>
                                    </div>

                                    <div style = {{margin : '5px'}}>
                                        <WhatsappShareButton title={''} seperator={this.props.refLink.message} url={this.props.refLink.url}>
                                            <WhatsappIcon size={35} logoFillColor="white" round={true} />
                                        </WhatsappShareButton>
                                    </div>

                                    <div style = {{margin : '5px'}}>
                                        <TwitterShareButton title={"Chai Frappe Ninja"} via={this.props.refLink.message} url={this.props.refLink.message+this.props.refLink.url}>
                                            <TwitterIcon size={35} logoFillColor="white" round={true} />
                                        </TwitterShareButton>
                                    </div>

                                    <div style = {{margin : '5px'}}>
                                        <EmailShareButton subject={"Inviting You to Chaayos #ChaiFrappeNinja Game"} body={this.props.refLink.message+this.props.refLink.url}>
                                            <EmailIcon size={35} logoFillColor="white" round={true} />
                                        </EmailShareButton>
                                    </div>
                                    <div style={{...styles.flexCenterColumn,height:'40px',width:'40px'}}>
                                        <img height={40} width={40} src="../../../img/link.png" onClick={()=>{this.props.dispatch(SlotMachineAction.copyCodeToClipboard(this.props.refLink.message+this.props.refLink.url), "Message Copied")}}/>
                                    </div>
                                </div>:<div/>
                            }
                        </div>
                    </div>
                </div>
                <div  className="col-sm-4" style={{backgroundColor: "yellow",  height:'0px'}}/>
            </div>
        );
    }
}

