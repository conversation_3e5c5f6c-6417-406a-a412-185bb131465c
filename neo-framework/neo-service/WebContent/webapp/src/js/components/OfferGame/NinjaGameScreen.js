import React from "react";
import {connect} from "react-redux";
import AppUtil from "../../AppUtil";
@connect((store) => {
    return {

    };
})

export default class NinjaGameScreen extends React.Component {
    gameUrl="";
    constructor() {
        super();
        this.state = {

        };
        this.onMessageReceived = this.onMessageReceived.bind(this);
    }

    componentWillMount() {
        console.log(__dirname)
        window.scrollTo(0, 0);
    }

    componentDidMount() {
        window.addEventListener("message",this.onMessageReceived, false);
        if(window.location.origin.indexOf("cafes")>-1){
            this.gameUrl = "https://cafes.chaayos.com/gameScreen"
        }else if(window.location.origin.indexOf("stage")>-1){
            this.gameUrl = "http://stage.kettle.chaayos.com:8989/gameScreen"
        }else if(window.location.origin.indexOf("dev")>-1){
            this.gameUrl = "http://dev.kettle.chaayos.com:8989/gameScreen"
        }else{
            this.gameUrl = "http://localhost:8989/gameScreen"
        }
    }

    onMessageReceived(event) {
       console.log("Game end in ninja screen")
        this.props.onGameEnd(JSON.parse(event.data))
    }

    showScore(val){
        console.log("score is ",val);
    }


    render() {
        var width=!AppUtil.checkEmpty(this.props.maxWidth) ? this.props.maxWidth : window.innerWidth
        return (
            <iframe id="ninjaPlayGround"  style={{position:'absolute', top:this.props.showGame ? '0px':'-100000px'}} src="/js/components/OfferGame/GameScreen/gameIndex.html" width={width} height={window.innerHeight} scrolling="not"></iframe>
        )
    }
}

