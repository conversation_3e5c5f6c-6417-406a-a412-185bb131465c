<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/2.6.2/phaser.min.js"></script>
    <audio id="chopSound" src="../../../../img/ninjaGameImg/images/chopSound.wav"></audio>
    <audio id="bombSound" src="../../../../img/ninjaGameImg/images/bombSound.wav"></audio>
    <title>Fruit Ninja</title>

    <style>
        html,
        body {
            padding: 0;
            margin: 0;
        }
        .textBorder{
            text-shadow: 2px 0 #fff, -2px 0 #fff, 0 2px #fff, 0 -2px #fff,
            1px 1px #fff, -1px -1px #fff, 1px -1px #fff, -1px 1px #fff;
        }
    </style>
</head>

<body>
<div id="gameContainer" style="position: absolute; height: 100%; width: 100%; flex: 1;display: flex; justify-content: center; align-items: center; background-image: url('../../../../img/ninjaGameImg/images/gameBackground2.png'); background-size: cover; background-repeat: no-repeat">
    <div id="game" style="height: 95%; width: 90%; border-radius: 15px; background-color: #ffffffcc">

    </div>
</div>
<div style="position: absolute;  width: 84%; padding-left: 8%; padding-right: 8%; ">
    <div style="display: flex; flex-direction: row; justify-content: space-between;">
        <p id="score" style="font-size: 25px; color: #ad3f26"></p>
        <p id="timmer" style="font-size: 25px; color: #ad3f26"></p>
    </div>
    <div id="readyTimmerContainer" style="width: 100%; height: 100%; display: flex; justify-content: center;align-items: center">
        <p class="textBorder" id="readyTimmer" style="font-size: 60px; font-weight: bolder; font-family: Nunito,serif; color: #0E8B45; text-align: center; margin-top: 70px"></p>
    </div>
</div>



<script>
    var w = window.innerWidth*.90,
        h = window.innerHeight*.95;
    var maxGameTime = 30;
    var deviceOS = getDeviceOS();
    var maxSpeed =0;
    var goodObjectThrowFreq=1;
    const readySteadySliceAnimation = [
        { transform: "scale(0)" },
        { transform: "scale(1)" },
    ];

    const readySteadySliceAnimationTimming = {
        duration: 700,
        iterations: 1,
    };


    var game = null;

    var fruits = ['frappe_1','frappe_2','frappe_3','frappe_1','frappe_2','frappe_3'];
    var emitImage1=['frappe_1_left','frappe_2_left','frappe_3_left','frappe_1_left','frappe_2_left','frappe_3_left'];
    var emitImage2=['frappe_1_right','frappe_2_right','frappe_3_right','frappe_1_right','frappe_2_right','frappe_3_right'];

    function getReady(){
        document.querySelector("body").style.overscrollBehavior='contain'
        var i = 2;
        document.querySelector('#readyTimmer').innerHTML="Ready"
        document.querySelector('#readyTimmer').animate(readySteadySliceAnimation, readySteadySliceAnimationTimming)
        const interval = setInterval(function () {
            if(i === 2){
                document.querySelector('#readyTimmer').innerHTML="Steady"
                document.querySelector('#readyTimmer').animate(readySteadySliceAnimation, readySteadySliceAnimationTimming)
            }else if(i===1){
                document.querySelector('#readyTimmer').innerHTML="Slice"
                document.querySelector('#readyTimmer').animate(readySteadySliceAnimation, readySteadySliceAnimationTimming)
            }else{
                document.querySelector('#readyTimmer').innerHTML=""
                document.querySelector("#readyTimmerContainer").style.display="none"
                isGameRunning=true;
                dropTime()
                updateShouldThrowBadObject()
                create()
                clearInterval(interval)
            }
            i=i-1;
        },1000)
    }

    function stop(){
        isGameRunning=false;
        document.querySelector("#readyTimmerContainer").style.display="block"
        document.querySelector("#readyTimmer").style.color="#cd1111"
        document.querySelector('#readyTimmer').innerHTML="Game Over"
        document.querySelector('#readyTimmer').animate(readySteadySliceAnimation, readySteadySliceAnimationTimming)
        game.destroy()
        const timeOut = setTimeout(function (){
            document.querySelector('#readyTimmer').innerHTML=""
            document.querySelector("#readyTimmerContainer").style.display="none"
            window.top.postMessage(
                JSON.stringify({
                    error: false,
                    message: 'Game End',
                    score:score
                }),
                '*'
            );
            resetScore();
        },2000)
    }
    function start() {
        console.log("%%%%%%%%");
        game =new  Phaser.Game(w, h, Phaser.AUTO, 'game', {
            preload: preload,
            update: update,
            create:create,
            render: render
        }, transparent=true);
        getReady();
    }
    function getDeviceOS(){
        var name = "Unknown OS";
        if (navigator.userAgent.indexOf("Win") != -1)
            name = "Windows OS";
        if (navigator.userAgent.indexOf("Mac") != -1)
            name = "Macintosh";
        if (navigator.userAgent.indexOf("Linux") != -1)
            name = "Linux OS";
        if (navigator.userAgent.indexOf("Android") != -1)
            name = "Android OS";
        if (navigator.userAgent.indexOf("like Mac") != -1)
            name = "iOS";
        return name
    }
    function dropTime(){
        document.querySelector("#timmer").innerHTML = `00:${maxGameTime}`;
        var interval = setInterval(function (){
            if(!isGameRunning){
                clearInterval(interval);
            }
            maxGameTime-=1
            if(maxGameTime<10){
                document.querySelector("#timmer").innerHTML = `00:0${maxGameTime}`;
            }else{
                document.querySelector("#timmer").innerHTML = `00:${maxGameTime}`;
            }
            if(maxGameTime === 0){
                clearInterval(interval);
                stop();
            }
        },1000)
    }
    function preload() {
        for(var i in fruits){
            game.load.image(fruits[i],`../../../../img/ninjaGameImg/images/fruits/${fruits[i]}.svg`);
            game.load.image(emitImage1[i],`../../../../img/ninjaGameImg/images/fruits/${emitImage1[i]}.svg`);
            game.load.image(emitImage2[i],`../../../../img/ninjaGameImg/images/fruits/${emitImage2[i]}.svg`);
        }
        game.load.image('bomb', '../../../../img/ninjaGameImg/images/tnt.png');
        game.load.image('explosion', '../../../../img/ninjaGameImg/images/explosion.png');
    }

    var good_objects,
        bad_objects,
        slashes,
        line,
        tipLabel,
        scoreLabel,
        chromeLabel,
        fontSize,
        fruitSize,
        score = 0,
        points = [];

    var fireRate = 1500;
    var nextFire = 0;
    var isGameRunning = false;
    var shouldThrowBadObject=false;

    function updateShouldThrowBadObject(){
        var timeOut = setTimeout(function (){
            shouldThrowBadObject=true;
            clearTimeout(timeOut)
        },6000)
    }

    function create() {
        if(!isGameRunning){
            return;
        }
        game.physics.startSystem(Phaser.Physics.ARCADE);
        game.physics.arcade.gravity.y = 300;

        good_objects = createGroup(fruits);
        bad_objects = createGroupMultiple(6, 'bomb');

        slashes = game.add.graphics(0, 0);
        slashes.clear();

        fontSize = game.world.width < 500 ? 24 : 40;
        fruitSize = Math.round(Math.min(game.world.width, game.world.height) * 0.25);
        // dropTime()
        createText();
        // updateShouldThrowBadObject();
        // throwObject();
    }

    function getEmitter(key){
        var emitterList = []
        emitter1 = game.add.emitter(0, 0, 300);
        emitter1.makeParticles(emitImage1[fruits.indexOf(key)]);
        emitter1.gravity = 300;
        if(deviceOS==='iOS'){
            emitter1.setScale(0.25, 0.4, 0.25, 0.4);
        }else{
            emitter1.setScale(1.3, 1.3, 1.3, 1.3);
        }
        emitter1.setYSpeed(-400, 400);

        emitter2 = game.add.emitter(0, 0, 300);
        emitter2.makeParticles(emitImage2[fruits.indexOf(key)]);
        emitter2.gravity = 300;
        if(deviceOS==='iOS'){
            emitter2.setScale(0.25, 0.4, 0.25, 0.4);
        }else{
            emitter2.setScale(1.3, 1.3, 1.3, 1.3);
        }
        emitter2.setYSpeed(-400, 400);
        emitterList.push(emitter1,emitter2)
        return emitterList
    }

    function createText() {

        scoreLabel = game.add.text(10, 70);
        scoreLabel.fontSize = fontSize;
        updateScore(0);
    }

    function createGroup(sprites) {
        var group = game.add.group();
        group.enableBody = true;
        group.physicsBodyType = Phaser.Physics.ARCADE;
        sprites.forEach(sprite => {
            group.add(game.make.sprite(30000, 30000, sprite));
        })
        group.setAll('checkWorldBounds', true);
        group.setAll('outOfBoundsKill', true);
        return group;
    }

    function createGroupMultiple(numItems, sprite) {
        var group = game.add.group();
        group.enableBody = true;
        group.physicsBodyType = Phaser.Physics.ARCADE;
        group.createMultiple(numItems, sprite);
        group.setAll('checkWorldBounds', true);
        group.setAll('outOfBoundsKill', true);
        return group;
    }

    function throwObject() {
        if (game.time.now > nextFire && good_objects.countDead() > 0 && bad_objects.countDead() > 0) {
            if(!isGameRunning){
                return
            }
            nextFire = game.time.now + fireRate;
            if (Math.random() > .7) {
                if(shouldThrowBadObject){
                    throwBadObject();
                    shouldThrowBadObject=false;
                    return;
                }
            }
            if(getMaxFrappeThrowCount() === 1){
                throwGoodObject(Math.random()*10,Math.random()*10,0.7 * game.world.height);
            }else if(getMaxFrappeThrowCount() === 2){
                shouldThrowBadObject=true
                throwGoodObject(80,80,0.7 * game.world.height);
                throwGoodObject(-40,-100,0.8 * game.world.height);
            }else if(getMaxFrappeThrowCount() === 3){
                shouldThrowBadObject=true
                throwGoodObject(80,80,0.7 * game.world.height);
                throwGoodObject(-40,-100,0.8 * game.world.height);
                throwGoodObject(-100,-20,0.9 * game.world.height);
            }else{
                shouldThrowBadObject=true
                throwGoodObject(80,80,0.7 * game.world.height);
                throwGoodObject(-40,-100,0.75 * game.world.height);
                throwGoodObject(-100,-20,0.8 * game.world.height);
                throwGoodObject(-40,-40,0.85 * game.world.height);
            }
        }
    }

    function throwGoodObject(x,y,speed) {
        var obj = getRandomDead(good_objects);
        obj.reset(getRandomX(), game.world.height);
        obj.anchor.setTo(Math.random(), Math.random());
        obj.angle = getRandomStartingAngle();
        obj.height = fruitSize +25;
        obj.width = fruitSize-20;
        obj.body.angularAcceleration = getRandomAngularAcceleration();
        game.physics.arcade.moveToXY(obj, game.world.centerX+x, game.world.centerY+y, speed);
    }

    function throwBadObject() {
        var obj = bad_objects.getFirstDead();
        obj.reset(getRandomX(), game.world.height);
        obj.anchor.setTo(Math.random(), Math.random());
        obj.angle = getRandomStartingAngle();
        obj.height = fruitSize;
        obj.width = fruitSize;
        obj.body.angularAcceleration = getRandomAngularAcceleration();
        game.physics.arcade.moveToXY(obj, game.world.centerX, game.world.centerY, getRandomSpeed(true));
    }

    function getRandomDead(group) {
        let deadChildren = group.children.filter(function (e) {
            return !e.alive;
        });
        deadChildren = group.children;
        let randIndex = Math.floor((Math.random() * deadChildren.length));
        randIndex = Math.min(randIndex, deadChildren.length - 1);
        return deadChildren[randIndex];
    }

    // get random value between -50 and 50
    function getRandomAngularAcceleration() {
        return ((Math.random() * 2) - 1) * 50;
    }

    // get random angle between -10 and 10
    function getRandomStartingAngle() {
        return ((Math.random() * 2) - 1) * 10;
    }

    // get random x position from the central 60% of the screen
    function getRandomX() {
        return (((Math.random() * game.world.width) - game.world.centerX) * 0.6) + game.world.centerX;
    }

    // get random value from 0 and screen's height, up to max 500
    function getRandomSpeed(isBomb) {
        if(isBomb){
            return Math.max(Math.random() * game.world.height, 500);
        }
        if (score >= 0 && score <=2) {
            return Math.max(0.8 * game.world.height, maxSpeed);
        }
        if (score <= 6 && score > 2) {
            goodObjectThrowFreq=2;
            return Math.max(0.85 * game.world.height, maxSpeed);
        } else if (score <= 15 && score > 6) {
            goodObjectThrowFreq=3;
            return Math.max(0.9 * game.world.height, maxSpeed);
        } else if (score > 15 && score <= 22) {
            goodObjectThrowFreq=4;
            return  Math.max(0.95 * game.world.height, maxSpeed);
        } else {
            goodObjectThrowFreq=4;
            return  Math.max(1  * game.world.height, maxSpeed);
        }
    }

    function getMaxFrappeThrowCount(){
        if(score < 3 && score >=0){
            fireRate=1300
            return 1
        } else if(score < 8 && score >=3){
            fireRate=1200
            return 2
        } else if(score < 13 && score >=8){
            fireRate=1100
            return 3
        } else if(score < 18 && score >=13){
            fireRate=1000
            return 4
        }else{
            fireRate=1000
            return 5
        }
    }



    function getInput() {
        return game.input;
    }

    function update() {
        if(!isGameRunning){
            return;
        }
        throwObject()
        let input = getInput();
        points.push({
            x: input.x,
            y: input.y
        });
        points = points.splice(points.length - 10, points.length);

        if (points.length < 1 || points[0].x == 0) {
            return;
        }

        slashes.clear();
        slashes.beginFill(0xFFFFFF);
        slashes.alpha = .5;
        slashes.moveTo(points[0].x, points[0].y);
        for (var i = 1; i < points.length; i++) {
            slashes.lineTo(points[i].x, points[i].y);
        }
        slashes.endFill();

        for (var i = 1; i < points.length; i++) {
            line = new Phaser.Line(points[i].x, points[i].y, points[i - 1].x, points[i - 1].y);
            game.debug.geom(line);

            good_objects.forEachExists(checkIntersects);
            bad_objects.forEachExists(checkIntersects);
        }
    }

    var contactPoint = new Phaser.Point(0, 0);

    function checkIntersects(fruit, callback) {
        var l1 = new Phaser.Line(fruit.body.right - fruit.width, fruit.body.bottom - fruit.height, fruit.body.right,
            fruit.body.bottom);
        var l2 = new Phaser.Line(fruit.body.right - fruit.width, fruit.body.bottom, fruit.body.right, fruit.body
            .bottom - fruit.height);
        l2.angle = 90;

        if (Phaser.Line.intersects(line, l1, true) ||
            Phaser.Line.intersects(line, l2, true)) {

            let input = getInput();
            contactPoint.x = input.x;
            contactPoint.y = input.y;
            var distance = Phaser.Point.distance(contactPoint, new Phaser.Point(fruit.x, fruit.y));
            if (Phaser.Point.distance(contactPoint, new Phaser.Point(fruit.x, fruit.y)) > 110) {
                return;
            }

            if (fruit.parent == good_objects) {
                killFruit(fruit);
            } else {

                stop();
                document.querySelector("#chopSound").pause()
                document.querySelector("#chopSound").currentTime =.3
                document.querySelector("#chopSound").currentTime =.3
                document.querySelector("#bombSound").play()
                var soundStopTimer = setTimeout(()=>{
                    document.querySelector("#bombSound").pause();
                    document.querySelector("#bombSound").currentTime =.3;
                },700)
            }
        }
    }

    function resetScore() {
        var highscore = Math.max(score, localStorage.getItem("highscore"));
        localStorage.setItem("highscore", highscore);

        good_objects.forEachExists(killFruit);
        bad_objects.forEachExists(killFruit);

        score = 0;
        updateScoreLabel(`Game Over!\nHigh Score: ${highscore}`, 'tomato');
    }

    function killFruit(fruit) {
        document.querySelector("#chopSound").currentTime =.3
        var sound =document.querySelector("#chopSound").play()
        var soundStopTimer = setTimeout(()=>{
            document.querySelector("#chopSound").pause();
            document.querySelector("#chopSound").currentTime =.3;
        },400)
        var emitter = getEmitter(fruit.key)
        emitter[0].x = fruit.x;
        emitter[0].y = fruit.y;
        emitter[1].x = fruit.x;
        emitter[1].y = fruit.y;
        emitter[0].start(true, 2000, null, 1);
        emitter[1].start(true, 2000, null, 1);
        fruit.kill();
        points = [];
        updateScore(score + 1);
    }

    function updateScore(_score) {
        score = _score;
        updateScoreLabel(``, '#9adcfa');
        document.querySelector("#score").innerHTML=`Score : ${score}`;
    }

    function updateScoreLabel(text, color) {
        scoreLabel.text = text;
        scoreLabel.fill = color;
    }

    function render() {}
</script>

</body>

</html>
