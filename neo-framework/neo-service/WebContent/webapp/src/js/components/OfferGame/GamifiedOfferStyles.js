export const styles = {
    gameHomeScreenContainer: {
        height: '100%',
        width: '100%',
        backgroundImage: `url("../../../img/ninjaGameImg/images/gameBackground2.png")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        display:'flex',
        flexDirection:'column',
        justifyContent:'center',
        alignItems:'center',
        minHeight:'740px',
        overflowY:'scroll',
        scrollbarWidth:'none'
    },
    gameOfferScreenContainer: {
        height: '100%',
        width: '100%',
        backgroundImage: `url("../../../img/ninjaGameImg/images/game_offer_screen.jpg")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        display:'flex',
        flexDirection:'column',
        justifyContent:'space-between',
        alignItems:'flex-end',
        padding:'5%',
        minHeight:'740px',
        overflowY:'scroll'
    },
    gameScoreScreenContainer: {
        height: '100%',
        width: '100%',
        backgroundImage: `url("../../../img/ninjaGameImg/images/gameBackground2.png")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        minHeight:'740px',
        overflowY:'scroll'
    },
    myScoreContainer: {
        width: '76%',
        borderRadius: '10px',
        padding: '15px',
        flex:3
    },
    myScoreContainerInner: {
        height: '100%',
        width: '100%',
        backgroundColor: '#118d15',
        borderRadius: '10px',
        boxShadow: '3px 3px 8px 0px #8d8585',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
    },
    resendOTPSection: {flex: 1, height: '50%', textAlign: 'center', fontSize: 15, color: '#70777D'},
    flexCenterColumn: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
    },
    signInInputField: {
        height: '6vh',
        width: '75%',
        paddingLeft: '10px',
        borderRadius: '100px',
        backgroundColor: 'rgba(255,255,255,0.63)',
        marginBottom: '5%',
        border: '#808080a1 solid 2px'
    },
    signInContainerStyle: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        backgroundImage: `url("../../../img/ninjaGameImg/images/gameBackground2.png")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat'
    },
    oTPInputContainer: {alignItem: 'center', justifyContent: 'center'},
    oTPInputBox: {
        width: '5rem',
        height: '50px',
        margin: '0.5rem',
        color: '#5e7e47',
        fontSize: 25,
        borderRadius: 6,
        border: '2px solid #808080a1',
        backgroundColor: '#ffffffa1'
    },
    oTPErrorStyle: {borderWidth: 2, borderColor: 'red'},
    middleChildContainer: {
        height: '100%',
        padding: '0px',
    },
    tncContainer: {
        display:'flex',
        justifyContent:'flex-start',
        alignItems:'center',
        borderRadius:'10px',
        flexDirection:'column',
        padding:'5px',
        minWidth:'20%',
        width:'100%',
        marginBottom: '30px',
        backgroundColor:'#ffffff70'
    },
    tncText:{
        fontSize:'10px',
        color:'black',
        wordSpacing:'1px',
        textAlign:'center'
    },
    tncHeading: {
        fontSize:'14px',
        color:'black',
        wordSpacing:'2px',
        marginBottom:'10px'
    },
    couponCodeBox: {
        width:'100%',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        padding:'5px 25px 5px 25px',
        height:'70px',
        backgroundImage:`url(./../../../img/ninjaGameImg/images/couponCodeBg.png)`,
        backgroundSize:'contain',
        backgroundRepeat:'no-repeat'
    },
    couponCodeContainer: {
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        flexDirection:'column',
        marginTop:'20px',
    },
    couponCodeText: {
        fontSize:'25px',
        color:'black',
        fontWeight:'bold',
        letterSpacing: '1.5px'
    },
    useCodeText:{
        fontSize:'18px',
        color:'black',
        fontWeight:'900',
        textAlign: 'center',
        marginTop: '15px',
        textDecoration:'underline'
    }, gameRuleScreenContainer: {
        height: '100%',
        width: '100%',
        backgroundImage: `url("../../../img/ninjaGameImg/images/gameBackground2.png")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        display:'flex',
        flexDirection:'column',
        justifyContent:'flex-start',
        alignItems:'flex-end',
        padding:'5%',
        minHeight:'740px',
        overflowY:'scroll'
    }, gameRuleTextStyle:{
        fontSize:'13px',
    },
    gameRuleHeadStyle: {listStyleType:'disc', listStylePosition:'inside'},
    leaderBoardHeading: {
        fontWeight:'900',
        textAlign:'center',
        backgroundColor:'black',
        color:'white',
        flex:1,
        height:'30px',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        fontSize:'15px',
        backgroundImage:'linear-gradient(0deg, #00000085, #ffffff7d,#00000085)'
    },
    leaderBoardSubRow: {
        textAlign:'center',
        backgroundColor:'#cfcfcf87',
        flex:1,
        height:'30px',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        fontSize:'15px',
        backgroundImage:'linear-gradient(360deg, #005fff52, #afe2ff,#005fff52)'
    }


}
