import React from "react";
import appUtil from "../AppUtil";
import MobileCartLayout from "./mobile/MobileCartLayout";
import DesktopCartLayout from "./desktop/DesktopCartLayout";

export default class CartLayout extends React.Component {
	render (){
		if(appUtil.isMobile()){
			return(
				<MobileCartLayout props={this.props} />
			)
		}else{
			return(
				<DesktopCartLayout props={this.props} />
			)
		}
	}
}