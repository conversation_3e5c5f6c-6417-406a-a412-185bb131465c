import React from "react";
import appUtil from "../AppUtil";
import MobileSignUpDeliveryOfferLayout from "./mobile/MobileSignUpDeliveryOffer/MobileSignUpDeliveryOfferLayout";
import MobileMyOfferLayout from './mobile/MobileMyOfferLayout'
import DesktopMyOfferLayout from "./desktop/DesktopMyOfferLayout";

export default class MyOfferLayout extends React.Component {
    render (){
        console.log("in signup")
        if(appUtil.isMobile()){
            return(
                <MobileMyOfferLayout props={this.props} />
            )
        }else{
            return(
                <DesktopMyOfferLayout props={this.props} />
            )
        }
    }
}
