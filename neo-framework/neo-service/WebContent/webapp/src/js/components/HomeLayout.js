import React from "react";
import appUtil from "../AppUtil";
import MobileHomeLayout from "./mobile/MobileHomeLayout";
import DesktopHomeLayout from "./desktop/DesktopHomeLayout";

export default class HomeLayout extends React.Component {
	render (){
		if(appUtil.isMobile()){
			return(
				<MobileHomeLayout props={this.props} />
			)
		}else{
			return(
				<DesktopHomeLayout props={this.props} />
			)
		}
	}
}