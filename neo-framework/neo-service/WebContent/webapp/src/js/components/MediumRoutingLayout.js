import React from "react";
import {connect} from "react-redux";
import MobileHeader from "./mobile/MobileHeader";

@connect((store) => {
    return {
    };
})
export default class MediumRoutingLayout extends React.Component {

    constructor() {
        super();
        this.state = {};
    }

    componentWillMount() {
        window.location.href = "https://medium.com/giveback-chaayos";
    }

    render() {
        return (
            <div>
                <div className="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false}
                                  props={this.props.props}/>
                </div>
                <div className="fullPageLoader">
                    <div className="loaderWrapper">
                        <div className="load8 loader"></div>
                        <p className="loaderMessage">Loading giveback blog!</p>
                    </div>
                </div>
            </div>
        )
    }
}