import React from "react";
import appUtil from "../AppUtil";
import MobileSignUpDeliveryOfferLayout from "./mobile/MobileSignUpDeliveryOffer/MobileSignUpDeliveryOfferLayout";
import DesktopSignUpDeliveryOfferLayout from "./desktop/DesktopSignUpDeliveryOffer/DesktopSignUpDeliveryOfferLayout";
import MobileCustomerOfferLayout from "./mobile/MobileCustomerOfferLayout";
import DesktopCustomerOfferLayout from "./desktop/DesktopCustomerOfferLayout";

export default class CustomerOfferLayout extends React.Component {
    render (){
        console.log("in signup")
        if(appUtil.isMobile()){
            return(
                <MobileCustomerOfferLayout props={this.props} />
            )
        }else{
            return(
                <DesktopCustomerOfferLayout props={this.props} />
            )
        }
    }
}
