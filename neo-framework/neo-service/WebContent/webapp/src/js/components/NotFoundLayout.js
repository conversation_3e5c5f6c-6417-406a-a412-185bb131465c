/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileNotFoundLayout from "./mobile/MobileNotFoundLayout";
import DesktopNotFoundLayout from "./desktop/DesktopNotFoundLayout";

export default class NotFoundLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileNotFoundLayout props={this.props} />
            )
        } else {
            return (
                <DesktopNotFoundLayout props={this.props} />
            )
        }
    }
}