
import React from "react";
import appUtil from "../AppUtil";
import DesktopSlotMachineLayout from "./desktop/DesktopSlotMachineLayout";
import MobileSlotMachineLayout from "./mobile/MobileSlotMachineLayout";

export default class SlotMachine extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileSlotMachineLayout props={this.props} />
            )
        } else {
            return (
                <DesktopSlotMachineLayout props={this.props} />
            )
        }
    }
}
