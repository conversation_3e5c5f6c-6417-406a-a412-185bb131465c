/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import {connect} from "react-redux";
import {browserHistory} from "react-router";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils"
import * as PaymentActions from "../actions/PaymentActions";
import * as OrderManagementActions from "../actions/OrderManagementActions";
import * as UtilityActions from "../actions/UtilityActions";
import trackUtils from "../utils/TrackUtils";

@connect((store) => {
    return {
        paymentInitiated: store.paymentReducer.paymentInitiated,
        paymentMessage: store.paymentReducer.paymentMessage,
        paymentStatus: store.paymentReducer.paymentStatus,
        showLoader: store.paymentReducer.showLoader,
        failureMessage: store.paymentReducer.failureMessage,
        criteria:store.localityReducer.criteria,
        orderStatusTimeouts: store.orderManagementReducer.orderStatusTimeouts,
        cart: store.cartManagementReducer.cart,
    };
})
export default class PaymentProcessLayout extends React.Component {

    constructor(){
        super();
        this.state = {
            msg:"Initiating payment request..."
        };
        this.checkoutByCash = this.checkoutByCash.bind(this);
        this.alternatePayment = this.alternatePayment.bind(this);
    }

    checkoutByCash(){
        if(StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount>1500){
            this.props.dispatch(UtilityActions.showPopup("Orders with value greater than 1500 are not allowed on cash payment. Please pay online.","error"));
        }else{
            this.props.dispatch(PaymentActions.payByCash(this.props.cart, this.props.orderStatusTimeouts));
        }
    }

    alternatePayment(){
        browserHistory.push("/paymentModes");
    }

    componentWillMount(){
        if(!appUtil.checkEmpty(this.props.location.query) && this.props.location.query.orderId!=null){
            this.props.dispatch(OrderManagementActions.orderCheckoutByWOId(this.props.location.query.orderId, this.props.orderStatusTimeouts));
            browserHistory.push("/payProcess");
            try{trackUtils.trackPaymentSuccess({paymentPartner:"",mode:"",orderMode:this.props.criteria,
                amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:this.props.location.query.orderId});}catch(e){}
        }else if(!appUtil.checkEmpty(this.props.location.query) && this.props.location.query.error!=null){
            this.props.dispatch(PaymentActions.cancelPaymentBackend(this.props.location.query.error, this.props.location.query.reason));
            browserHistory.push("/payProcess");
            try{trackUtils.trackPaymentFailed({paymentPartner:"",mode:"",orderMode:this.props.criteria,
                amount:StorageUtils.getCartDetail().orderDetail.transactionDetail.paidAmount,externalOrderId:this.props.location.query.error,reason:this.props.location.query.reason});}catch(e){}
        }else if(!this.props.paymentInitiated){
            browserHistory.push("/menu");
        }
    }

    render (){
        return (
            <div class="text-center" style={{background:"#fff",position:"fixed",width:"100%",height:"100%"}}>
                <div style={{maxWidth:"500px", margin:"auto", marginTop:"100px"}}>
                    {this.props.showLoader?(
                        <div class="loader load8"></div>
                    ):null}
                    {this.props.paymentStatus=="FAILED"?(
                        <div>
                            {appUtil.isMobile() ? (
                                <img src="../../img/sadPhone.png"/>
                            ) : (
                                <img src="../../img/sadLaptop.png"/>
                            )}
                            <p style={{marginBottom:"30px",marginTop:"10px",color:"red",fontSize:"18px"}}>{this.props.failureMessage}</p>
                            {this.props.criteria=="DELIVERY"?(
                                <div class="btn btn-primary small" style={{display:"inline-block", margin:"10px"}} onClick={this.checkoutByCash.bind(this)}>Pay by Cash on Delivery</div>
                            ):null}
                            <div class="btn btn-primary small" style={{display:"inline-block", margin:"10px"}} onClick={this.alternatePayment.bind(this)}>Change Payment Mode</div>
                        </div>
                    ):(
                        <p style={{padding:"20px"}}>{this.props.paymentMessage}</p>
                    )}
                </div>
            </div>
        );
    }
}