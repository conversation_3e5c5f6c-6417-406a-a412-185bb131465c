import React from "react";
import appUtil from "../AppUtil";
import MobileDeliveryRoutingLayout from "./mobile/MobileDeliveryRoutingLayout";
import DesktopDeliveryRoutingLayout from "./desktop/DesktopDeliveryRoutingLayout";

export default class DeliveryRoutingLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileDeliveryRoutingLayout props={this.props} />
            )
        } else {
            return (
                <DesktopDeliveryRoutingLayout props={this.props} />
            )
        }
    }
}