import React from "react";
import appUtil from "../AppUtil";
import MobileOutletDeliveryRedirectLayout from "./mobile/MobileOutletDeliveryRedirectLayout";
import DesktopOutletDeliveryRedirectLayout from "./desktop/DesktopOutletDeliveryRedirectLayout";

export default class OutletDeliveryRedirectLayout extends React.Component {
	render (){
		if(appUtil.isMobile()){
			return(
				<MobileOutletDeliveryRedirectLayout props={this.props} />
			)
		}else{
			return(
				<DesktopOutletDeliveryRedirectLayout props={this.props} />
			)
		}
	}
}