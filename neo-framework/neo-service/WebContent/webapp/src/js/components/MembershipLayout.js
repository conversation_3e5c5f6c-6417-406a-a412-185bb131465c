
import React from "react";
import appUtil from "../AppUtil";
import MobileMembershipLayout from "./mobile/MobileMembershipLayout";
import DesktopMembershipLayout from "./desktop/DesktopMembershipLayout";
import ChaayosSelectLayout from "./desktop/ChaayosSelectMembership/ChaayosSelectLayout";


export default class MembershipLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                // <MobileMembershipLayout props={this.props} />
                <ChaayosSelectLayout props={this.props} />
            )
        } else {
            return (
              //  <DesktopMembershipLayout props={this.props} />
                  <ChaayosSelectLayout props={this.props} /> 
                
            )
        }
    }
}
