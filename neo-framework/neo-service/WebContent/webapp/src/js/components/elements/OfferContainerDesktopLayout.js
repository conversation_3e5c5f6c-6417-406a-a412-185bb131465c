import React from "react";
import {connect} from "react-redux";
import {styles} from './elementStyles/OfferContainerDesktopStyle'

export default class OfferContainerDesktopLayout extends React.Component{


    render() {
        let data = this.props.data;
        let color = '#11a01d';
        let partnerImage="../../../img/logo.svg";
        let couponBackground="../../../img/chaayos_coupon_card.webp";
        let nextVisitText="on your next visit at";
        if(data.channelPartnerId===null || data.channelPartnerId === 1){
            color = '#11a01d';
            partnerImage="../../../img/chaayos_offer_card.webp"
            couponBackground="../../../img/chaayos_coupon_card.webp"
            nextVisitText="on your next visit at";
        }else if(data.channelPartnerId === 3){
            color = '#e22a4a';
            partnerImage="../../../img/zomato_offer_card.webp"
            couponBackground="../../../img/zomato_coupon_card.webp"
            nextVisitText="on your next order on";
        }
        return <div style={{margin:'0px 20px'}}>
            <div style={{margin:'0px 10px',position:'relative'}}>
                <div style={{...styles.offerCard}}>
                    {/*<p style={styles.youWonText}>You have a surprise!</p>*/}
                    <p style={styles.offerText}>{data.offerDesc} *</p>
                    {/*<p style={styles.nextVisitText}>{nextVisitText}</p>*/}
                    {/*<img src={partnerImage} width={100} style={{alignSelf: 'center', marginTop:10}}/>*/}
                </div>
                <img src={partnerImage} style={{width:'250px'}} draggable={false}/>
            </div>
            <div style={{margin:'0px 10px'}}>
                {data.offerCouponCode === "LOYAL_TEA" ?
                    <p style={{...styles.useCodeText, color:color}}> No coupon needed</p> :
                    <div>
                        <p style={{...styles.useCodeText, color:color}}>USE CODE</p>
                        <div style={{position:'relative'}}>
                            <div style={styles.couponBox}>
                                <p style={styles.coponCode}>{data.offerCouponCode}</p>
                                <img src="../../../img/copy_code.svg" height={20} draggable={false} onClick={()=> this.props.onCodeCopy(data.offerCouponCode)}/>
                            </div>
                            <img src={couponBackground} style={{width:'250px',height:'35px'}} draggable={false}/>
                        </div>
                    </div> }
            </div>
            <p style={{...styles.validityText, color : color}}>valid from {data.validityFrom} till {data.validityTill}</p>
        </div>
    }
}
