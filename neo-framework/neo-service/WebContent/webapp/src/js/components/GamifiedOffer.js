import React from "react";
import appUtil from "../AppUtil";
import DesktopGamifiedOfferLayout from "./OfferGame/DesktopGamifiedOfferLayout";
import MobileGamifiedOfferLayout from "./OfferGame/MobileGamifiedOfferLayout";


export default class GamifiedOffer extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileGamifiedOfferLayout props={this.props} />
            )
        } else {
            return (
                <DesktopGamifiedOfferLayout props={this.props} />
            )
        }
    }
}
