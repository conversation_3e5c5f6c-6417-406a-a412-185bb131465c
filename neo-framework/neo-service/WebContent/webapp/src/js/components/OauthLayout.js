/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import * as UtilityActions from "./../actions/UtilityActions";
import * as CampaignManagementActions from "./../actions/CampaignManagementActions";
import StorageUtils from "./../utils/StorageUtils";
import MobileHeader from "./mobile/MobileHeader";
import {connect} from "react-redux";

@connect((store) => {
    return {};
})
export default class OauthLayout extends React.Component {

    componentWillMount() {
        console.log(this.props.location.query);
        const {accessKey, token, coupon} = {...this.props.location.query};
        if(coupon != null && typeof coupon == "string") {
            StorageUtils.setCampaignDetails({cp:coupon}, 1);
            this.props.dispatch(CampaignManagementActions.setCampaignDetail({cp:coupon}));
        }
        if (accessKey != null && token != null) {
            this.props.dispatch(UtilityActions.authenticateExternalPartner(accessKey, token));
        } else {
            this.props.dispatch(UtilityActions.showFullPageLoader("Required parameters not passed."));
        }
    }

    render() {
        return (
            <div>
                <div className="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false}
                                  props={this.props.props}/>
                </div>
                <div className="fullPageLoader">
                    <div className="loaderWrapper">
                        <div className="load8 loader"></div>
                        <p className="loaderMessage">Loading menu please wait!</p>
                    </div>
                </div>
            </div>
        )
    }
}