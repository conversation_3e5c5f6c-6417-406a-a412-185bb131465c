import React from "react";
import appUtil from "../AppUtil";
import MobileSignUpDeliveryOfferLayout from "./mobile/MobileSignUpDeliveryOffer/MobileSignUpDeliveryOfferLayout";
import DesktopSignUpDeliveryOfferLayout from "./desktop/DesktopSignUpDeliveryOffer/DesktopSignUpDeliveryOfferLayout";

export default class SignUpDeliveryOfferLayout extends React.Component {
    render (){
        console.log("in signup")
        if(appUtil.isMobile()){
            return(
                <MobileSignUpDeliveryOfferLayout props={this.props} />
            )
        }else{
            return(
                <DesktopSignUpDeliveryOfferLayout props={this.props} />
            )
        }
    }
}
