/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import MobileDineinRoutingLayout from "./mobile/MobileDineinRoutingLayout";
import DesktopDineinRoutingLayout from "./desktop/DesktopDineinRoutingLayout";

export default class DineinRoutingLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileDineinRoutingLayout props={this.props} />
            )
        } else {
            return (
                <DesktopDineinRoutingLayout props={this.props} />
            )
        }
    }
}