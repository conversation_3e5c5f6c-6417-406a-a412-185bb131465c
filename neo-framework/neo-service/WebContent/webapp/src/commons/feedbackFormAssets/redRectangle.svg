<svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1539_2193)">
<rect x="5" y="2" width="22" height="21" rx="3" fill="#E0221E"/>
</g>
<defs>
<filter id="filter0_d_1539_2193" x="0" y="0" width="30" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1539_2193"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1539_2193" result="shape"/>
</filter>
</defs>
</svg>
