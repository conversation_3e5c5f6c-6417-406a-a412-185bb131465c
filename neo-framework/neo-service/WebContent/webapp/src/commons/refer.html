<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON></title>
    <meta property="description" content="<PERSON><PERSON><PERSON>fer and <PERSON><PERSON><PERSON>"/>
    <meta property="og:url" content="https://cafes.chaayos.com/refer"/>
    <meta property="og:type" content="website"/>
    <meta property="og:title" content="<PERSON><PERSON><PERSON>fer and <PERSON>arn"/>
    <meta property="og:description" content="<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>"/>
    <meta property="og:image" content="https://cafes.chaayos.com/img/referral/referShare1.png"/>
    <meta property="og:image:secure_url" content="https://cafes.chaayos.com/img/referral/referShare1.png"/>
    <meta property="og:image:width" content="1200"/>
    <meta property="og:image:type" content="image/png"/>
    <meta property="og:image:height" content="630"/>
    <meta property="og:image:alt" content="Chaayos Refer and Earn Chaayos Cash"/>
    <meta property="fb:app_id" content="1300275053451615"/>
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:site" content="@chaayos"/>
    <meta name="twitter:title" content="Chaayos Refer and Earn Chaayos Cash"/>
    <meta name="twitter:description" content="Chaayos Refer and Earn Chaayos Cash"/>
    <meta property="twitter:image" content="https://cafes.chaayos.com/img/referral/referShare.png"/>
    <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">
    <style data-cfasync="false" type="text/css">
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-family: 'Montserrat', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .page#desktop .container {
            margin: 0 100px;
        }

        .page#mobile .container {
            margin: 0 20px;
        }

        .page#desktop .banner {
            /*background: #571abc;
            background-image: linear-gradient(171deg, #571abc 29%, #8144e5);*/
            background: #056c06;
            background-image: linear-gradient(171deg, #306f31 41%, #48a249);
            padding: 20px;
            height: 380px;
            position: relative;
        }

        .page#mobile .banner {
            /*background: #571abc;
            background-image: linear-gradient(171deg, #571abc 29%, #8144e5);*/
            background: #056c06;
            background-image: linear-gradient(171deg, #306f31 41%, #48a249);
            padding: 20px;
            height: 380px;
            position: relative;
        }

        .page#desktop .banner .logo {
            height: 45px;
        }

        .page#mobile .banner .logo {
            width: 155px;
        }

        .page#desktop .banner .refer {
            max-height: 350px;
            float: right;
            position: absolute;
            right: 0;
            bottom: 20px;
            max-width: 65%;
        }

        .page#mobile .banner .refer {
            width: 100%;
        }

        .page#desktop .banner .referText {
            color: #FFF;
            font-size: 35px;
            padding: 0;
            margin: 100px 0 0 0;
            max-width: 50%;
        }

        .page#mobile .banner .referText {
            color: #FFF;
            font-size: 25px;
            margin: 0;
            padding: 0;
        }

        .page#desktop .banner .referText span {
            font-size: 21px;
        }

        .page#mobile .banner .referText span {
            font-size: 18px;
        }

        .page#desktop .contactForm {
            margin-top: 20px;
            max-width: 500px;
        }

        .page#mobile .contactForm {
            margin-top: 20px;
            max-width: 500px;
        }

        .page#desktop .copyLinkBtn{
            display: inline-block;
            padding: 15px 20px;
            color: #FFF;
            cursor: pointer;
            background: #14a386;
            font-size: 21px;
            margin-left: -5px;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
        }

        .page#mobile .copyLinkBtn{
            display: inline-block;
            padding: 10px 15px;
            color: #fff;
            cursor: pointer;
            background: #14a386;
            font-size: 18px;
            margin-left: -6px;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
        }

        .page#desktop .inputContainer {
            margin-bottom: 10px;
            background: #fff;
            padding: 15px 75px;
            display: inline-block;
            border-radius: 2px;
            font-size: 21px;
            font-weight: bold;
            color: #0d620d;
        }

        .page#mobile .inputContainer {
            margin-bottom: 10px;
            background: #fff;
            padding: 10px 45px;
            display: inline-block;
            border-radius: 3px;
            font-size: 18px;
            font-weight: bold;
        }

        .page#desktop .socialContainer {
            text-align: center;
        }

        .page#mobile .socialContainer {
            text-align: center;
        }

        .page#desktop .social {
            display: inline-block;
            width: 114px;
            padding: 20px 0;
            background: #fafafa;
            margin: 0 8%;
            cursor: pointer;
        }

        .page#mobile .social {
            display: inline-block;
            padding: 10px;
            background: #fafafa;
            margin: 0 5%;
        }

        .page#mobile .social img{
            width: 50px;
        }

        #refCode {
            text-transform: uppercase;
        }
    </style>
    <script data-cfasync="false">
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-MLDTX5R');
    </script>
    <script data-cfasync="false" type="application/javascript">
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            window.isMobile = true;
        } else {
            window.isMobile = false;
        }
    </script>
    <script data-cfasync="false">
        (function (w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-MLDTX5R');
    </script>
</head>

<body>
<div class="page">
    <div class="banner" id="mobile">
        <div class="container">
            <img src="/chaayos/logo/chaayosLogo.png" class="logo"/>
            <div>
                <img src="../img/referral/referPeople.png" class="refer"/>
            </div>
            <p class="referText" id="referrer"></p>
            <div class="contactForm">
                <div class="inputContainer" id="refCode"></div>
                <div class="copyLinkBtn" id="copyLinkBtn" onclick="copyLink('refCode')">Copy Link</div>
            </div>
        </div>
    </div>
    <div class="container">
        <div style="padding: 30px 0; font-size: 21px; font-weight: bold; text-align: center;">Share with friends</div>
        <div class="socialContainer">
            <div class="social" onclick="sharefb()"><img src="../img/referral/fb.png" /></div>
            <a id="twitterLink" class="social" target="_blank" onclick="trackTweet()" href="https://twitter.com/intent/tweet?text=Refer%20your%20friends%20%26%20get%20Chaayos%20Cash%20&url=https%3A%2F%2Fwww.chaayos.com%2Fsignup%3Fr%3Drahu138&via=Chaayos">
                <img src="../img/referral/tw.png" />
            </a>
            <a id="waLink" onclick="trackWhatsapp()" class="social" href="whatsapp://send?text=http://cafes.chaayos.com/refer">
                <img src="../img/referral/wa.png" />
            </a>
        </div>
    </div>
    <div class="container" style="text-align: left; margin: 20px auto; max-width: 600px; border: #127b2e 1px solid; padding: 20px; border-radius: 10px;">
        <div style="padding: 30px 0; font-size: 21px; font-weight: bold; text-align: center;">How it works</div>
        <p>Step 1: Share coupon codes with your friends and family.</p>
        <p>Step 2: Your referred friends visit Chaayos.</p>
        <p>Step 3: On their first successful transaction you will be credited 300 Chaayos Cash in your account.</p>
        <p>Step 4: You can now visit Chaayos and use upto a maximum of 100 Chaayos Cash in any Dine-in order with a minimum purchase of Rs 350</p>
        <p>Step 5: Keep referring and keep earning more.</p>
    </div>
    <div style="text-align: center;">
        <a href="https://cafes.chaayos.com/terms">Terms & conditions</a>
    </div>
</div>

<script data-cfasync="false" type="application/javascript">
    window.dataLayer = window.dataLayer || [];
    var refCode = getUrlParam("r");
    var referrer = getUrlParam("n");
    var campaign = getUrlParam("c");
    document.getElementById("twitterLink").href = "https://twitter.com/intent/tweet?text=Click%20and%20earn%20300%20Chaayos%20Cash%20instantly.%20&url=https%3A%2F%2Fwww.chaayos.com%2Fsignup%3Fr%3D"+refCode+"%26c%3D"+campaign+"%26s%3DTWITTER&via=Chaayos";
    if (!window.isMobile) {
        document.getElementsByClassName("page")[0].setAttribute("id", "desktop");
        document.getElementById("waLink").remove();
    } else {
        document.getElementsByClassName("page")[0].setAttribute("id", "mobile");
        document.getElementById("waLink").href = "whatsapp://send?text=Click%20and%20earn%20300%20Chaayos%20Cash%20instantly%20https%3A%2F%2Fwww.chaayos.com%2Fsignup%3Fr%3D"+refCode+"%26n%3D%26c%3D"+campaign+"%26s%3DWHATSAPP";
    }
    if (refCode != null) {
        document.getElementById("refCode").innerText = refCode.toUpperCase();
    }
    document.getElementById("referrer").innerHTML = "Hi " + referrer + "<br /><span>Refer your friends & earn <strong style=\"color: #ff9400;font-size: 32px;\">300</strong> Chaayos Cash. Share the code below.<span/>";

    function getUrlVars() {
        var vars = {};
        var parts = window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
            vars[key] = value;
        });
        return vars;
    }

    function getUrlParam(parameter, defaultvalue) {
        var urlparameter = defaultvalue;
        if (Object.keys(getUrlVars()).length > 0 && getUrlVars()[parameter] != null && getUrlVars()[parameter].length > 0) {
            urlparameter = getUrlVars()[parameter];
        }
        return decodeURIComponent(urlparameter);
    }

    function copyLink(element) {
        var $temp = document.createElement("input");
        document.getElementsByTagName("BODY")[0].appendChild($temp);
        $temp.value = "https://cafes.chaayos.com/signup?r="+refCode.toUpperCase()+"&n="+referrer+"&c="+campaign+"&s=COPY_LINK";
        $temp.select();
        document.execCommand("copy");
        $temp.remove();
    }

    window.fbAsyncInit = function () {
        FB.init({
            appId: '1300275053451615',
            autoLogAppEvents: true,
            xfbml: true,
            version: 'v2.12'
        });
    };

    (function (d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
            return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));

    function trackShareFbSuccess(){
        dataLayer.push({
            refCode: refCode,
            campaign:campaign,
            event:"referFacebookShare"
        });
    }

    function sharefb() {
        FB.ui({
                method: 'share',
                href: "https://cafes.chaayos.com/signup?r="+refCode+"&n="+referrer+"&c="+campaign+"&s=FACEBOOK"
            },
            // callback
            function (response) {
                if (response && !response.error_message) {
                    trackShareFbSuccess();
                }
            }
        );
    }

    window.twttr = (function (d, s, id) {
        var t, js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;
        js.src = "//platform.twitter.com/widgets.js";
        fjs.parentNode.insertBefore(js, fjs);
        return window.twttr || (t = {
            _e: [], ready: function (f) {
                t._e.push(f)
            }
        });
    }(document, "script", "twitter-wjs"));

    function trackTweet(){
        dataLayer.push({
            refCode: refCode,
            campaign:campaign,
            event:"referTwitterShare"
        });
    }

    function trackWhatsapp() {
        dataLayer.push({
            refCode: refCode,
            campaign:campaign,
            event:"referWhatsappShare"
        });
    }

</script>
</body>
</html>