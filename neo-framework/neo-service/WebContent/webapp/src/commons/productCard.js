window.productCard=(productDetail,index,basePath)=>{
    console.log(productDetail);
    return `
    <div class="mainMargin mt-4">
                    <div class="row">
                        <div style="flex:1; justify-content: flex-end; max-width: 35px; min-width: 35px" class="flex-all-center pe-0">
                            <img class="productPlaceholder" id="product_icon_${index}" src=${basePath+productDetail.pi} alt="" 
                            style="height: 35px; width: 35px; border-radius: 900px;box-shadow: 0px 1px 2px 1px #cccccc;"/>
                        </div>
                        <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                            <p class="rateYourExText mb-0">${productDetail.in} x${productDetail.qt}</p>
                        </div>
                    </div>
                </div>
                <div class="mainMargin" >
                        <div class="row">
                            <div style="flex:1;max-width: 35px;"></div>
                            <div style="flex:17; justify-content: flex-start; " class="flex-all-center">
                                <p class="productDimentionText mb-0"> ${productDetail.d !== "None" ? productDetail.d : ""}</p>
                            </div>
                        </div>
                    </div>
                    <div class="mainMargin">
                        <div class="row">
                            <div style="flex:1;max-width: 35px; "></div>
                            <div style="flex:17; justify-content: flex-start; " class="flex-all-center">
                                <p class="productDimentionText mb-0"> ${productDetail.c}</p>
                            </div>
                        </div>
                    </div>
                <div class="productExpand">
                    <div class="mainMargin mt-3">
                        <div class="row">
                            <div style="flex:1;max-width: 35px;"></div>
                            <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                                <p class="didYouLikeText mb-0">${productDetail.q}</p>
                            </div>
                        </div>
                    </div>
                    <div class="mainMargin mt-2">
                        <div class="row">
                            <div style="flex:1; justify-content: flex-end; max-width: 35px; min-height: 50px" class="flex-all-center pe-0"></div>
                            <div class="row" style="flex: 17; max-width: 220px;"> 
                                <div class="px-0 flex-all-center" id="product_star_${index}_1" style="flex: 1; flex-direction: column;" onclick="changeProductRating(1,${index});"></div>
                                <div class="px-0 flex-all-center" id="product_star_${index}_2" style="flex: 1; flex-direction: column;" onclick="changeProductRating(2,${index});"></div>
                                <div class="px-0 flex-all-center" id="product_star_${index}_3" style="flex: 1; flex-direction: column;" onclick="changeProductRating(3,${index});"></div>
                                <div class="px-0 flex-all-center" id="product_star_${index}_4" style="flex: 1; flex-direction: column;" onclick="changeProductRating(4,${index});"></div>
                                <div class="px-0 flex-all-center" id="product_star_${index}_5" style="flex: 1; flex-direction: column;" onclick="changeProductRating(5,${index});"></div>
                            </div>
                        </div>
                    </div>
                    <div class="mainMargin mt-3" id="issueTagContainer_${index}"></div>
                    <div class="mainMargin mt-3">
                        <div class="row">
                            <div style="flex:1;max-width: 35px;"></div>
                            <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                                <p class="didYouLikeText mb-0"> Write a review for ${productDetail.in}</p>
                            </div>
                        </div>
                    </div>
                    <div class="mainMargin mt-3">
                        <div class="row">
                            <div style="flex:1;max-width: 35px;"></div>
                            <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                                <textarea class="productTextArea" id="product_textArea_${index}" rows="3" maxlength="200"></textarea>
                            </div>
                        </div>
                    </div>
<!--                    <div class="mainMargin mt-3">-->
<!--                        <div class="row">-->
<!--                            <div style="flex:1;max-width: 35px;"></div>-->
<!--                            <div style="flex:17; justify-content: flex-start;" class="flex-all-center" onclick="hello();">-->
<!--                                <img src="./feedbackFormAssets/uploadPhoto.svg" style="height: 30px; width: 120px;"/>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <div class="thinDivider mt-3"></div>
    `;
}

window.getIssueTagButtons=(index,tags)=>{
    return `
    <div class="row">
    <div style="flex:1;max-width: 30px;"></div>
    <div style="flex:17; justify-content: space-between; max-width: 450px;" class="flex-all-center" >
        <div style="flex: 1; " id="issueTag_${index}_1" onclick="addIssueTag(${index},1);">
            <img src="feedbackFormAssets/${tags[0]}TastelessButton.svg" style=" width: 90%" />
        </div>
        <div style="flex: 1; " id="issueTag_${index}_2" onclick="addIssueTag(${index},2);">
            <img src="feedbackFormAssets/${tags[1]}LessKadakButton.svg" style=" width: 90%"/>
        </div>
        <div style="flex: 1; " id="issueTag_${index}_3" onclick="addIssueTag(${index}, 3);">
            <img src="feedbackFormAssets/${tags[2]}WateryButton.svg" style=" width: 90%"/>
        </div>
        <div style="flex: 1; " id="issueTag_${index}_4" onclick="addIssueTag(${index}, 4);">
            <img src="feedbackFormAssets/${tags[3]}NoAdarakButton.svg" style=" width: 90%"/>
        </div>
    </div>
</div>
    `;
}



window.getURLParameter=(paramKey)=> {
    var pageURL = window.location.search.substring(1);
    var sURLVariables = pageURL.split('&');
    for (var i = 0; i < sURLVariables.length; i++) {
        var sParameterName = sURLVariables[i].split('=');
        if (sParameterName[0] == paramKey) {
            return sParameterName[1];
        }
    }
}

window.setItemComment=()=>{
    for(var i=1; i<=orderDetail.fiol.length; i++){
        orderDetail.fiol[i-1].ic = document.getElementById("product_textArea_"+i).value;
    }
    console.log(orderDetail);
}
