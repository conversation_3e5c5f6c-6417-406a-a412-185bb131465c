<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Chaayos - Chaayos Now Open at Churchgate</title>
        <meta property="description" content="Chaayos Now Open at Churchgate"/>
        <meta property="og:url" content="https://cafes.chaayos.com/churchgate" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Chaayos Now Open at Churchgate" />
        <meta property="og:description" content="Visit Chaayos Churchgate & get free chai on these days" />
        <meta property="og:image" content="https://cafes.chaayos.com/churchgateMobile.jpg" />
        <meta property="og:image:secure_url" content="https://cafes.chaayos.com/churchgateMobile.jpg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:type" content="image/jpeg" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content="Chaayos Now Open at Churchgate" />
        <meta property="fb:app_id" content="715946468505409" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@chaayos" />
        <meta name="twitter:title" content="Chaayos Now Open at Churchgate" />
        <meta name="twitter:description" content="Visit Chaayos Churchgate & get free chai on these days" />
        <meta property="twitter:image" content="https://cafes.chaayos.com/churchgateMobile.jpg" />
        <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">
        <script data-cfasync="false" type="application/javascript" src="/jquery-3.2.1.min.js"></script>
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '3084496791828091');
            fbq('track', 'PageView');
        </script>
        <style data-cfasync="false" type="text/css">
            * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                font-family: 'Montserrat', sans-serif;
            }
            body {
                margin: 0;
                padding: 0;
                text-align: center;
                background-color: #1a1434;
                /*background: #5e7e47;*/
                /*background-image: linear-gradient(141deg, #6c3b91 0%, #4c2c67 51%, #150b26 75%);*/
                color: white;
            }
            .headerWrapper {
                color: #fff;
                height: 60px;
                text-align: center;
            }
            .headerWrapper img {
                max-height: 40px;
                margin: 10px 10px;
            }
            .banner#mobile{
                height: 220px;
                background-size: cover;
                background-image: url("./churchgateMobile.jpg");
                background-repeat: no-repeat;
                background-position: center;
            }
            .banner#desktop{
                height: 325px;
                background-size: cover;
                background-image: url("./churchgate.jpg");
                background-repeat: no-repeat;
                background-position: center;
            }
            .banner img{
                width: 100%;
            }
            .loaderBox {
                display: none;
                margin: 10px;
            }
            .loader {
                height: 200px;
                border-radius: 10px;
            }
            .loaderText {
                color: #fff;
                /*position: absolute;*/
                left: 0;
                right: 0;
                padding: 10px;
            }
            .contactForm{
                margin: auto;
                margin-top: 20px;
                max-width: 500px;
                width: 80%;
            }
            .inputContainer{
                margin: auto;
                padding: 2px;
                margin-bottom: 10px;
            }
            .inputContainer label{
                display: block;
                text-align: left;
                padding-bottom: 3px;
            }
            .inputContainer input{
                display: block;
                width: 100%;
                border:none;
                background: #fff;
                border-radius: 4px;
                color: #000;
                font-size: 21px;
                padding: 15px 0;
                text-align: center;
            }
            .inputContainer input[type="button"]{
                display: block;
                width: 100%;
                border:none;
                background: green;
                border-radius: 4px;
                color:#FFF;
                font-size: 21px;
                padding: 15px 0;
            }
            .errorText{
                color: red;
            }
            .couponBox{
                background: #fff;
                width: 300px;
                height: 200px;
                margin: auto;
                text-align: center;
                border-radius: 8px;
                max-width: 80%;
                position: relative;
                background-image: url("./celebration.gif");
                background-size: contain;
                display: none;
                margin-top: 20px;
            }
            .couponBox#desktop{
                background: #fff;
                width: 500px;
                height: 300px;
                margin: auto;
                text-align: center;
                border-radius: 8px;
                position: relative;
                background-image: url("./celebration.gif");
                background-size: contain;
                display: none;
                margin-top: 20px;
            }
            .couponErrorBox{
                background: #fff;
                width: 300px;
                height: 200px;
                margin: auto;
                text-align: center;
                border-radius: 8px;
                max-width: 80%;
                position: relative;
                display: none;
                margin-top: 20px;
            }
            .couponErrorBox#desktop{
                background: #fff;
                width: 500px;
                height: 300px;
                margin: auto;
                text-align: center;
                border-radius: 8px;
                position: relative;
                display: none;
                margin-top: 20px;
            }
            .offerText{
                color: #616161;
                position: absolute;
                left: 20px;
                right:20px;
                bottom: 20px;
                background: #fff;
            }
            .offerError{
                color: #ce0000;
                position: absolute;
                left: 20px;
                right:20px;
                bottom: 20px;
                background: #fff;
            }
            .shareButton{
                border: none;
                background: transparent;
                max-width: 288px;
                width: 80%;
                margin: 15px 0;
                display: inline-block;
            }
            .shareButton img{width: 100%}
            .twitter{
                background: #1a8bf0;
                color: #fff;
                border-radius: 5px;
                padding: 15px 0;
                max-width: 288px;
                width: 80%;
                display: inline-block;
                font-size: 21px;
                text-decoration: none;
                font-family: sans-serif;
            }
        </style>
        <script data-cfasync="false" type="application/javascript">
            if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
                window.isMobile = true;
            }else{
                window.isMobile = false;
            }
        </script>
        <script data-cfasync="false">
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-MLDTX5R');
        </script>
    </head>

    <body>
        <div class="headerWrapper">
            <img src="/chaayos/logo/chaayosLogo.png"/>
        </div>
        <div class="banner" id="mobile">
        </div>
        <!--<a href="whatsapp://send?text=http://cafes.chaayos.com/chaayos/logo/chaayosLogo.png" data-action="share/whatsapp/share">Share
            via Whatsapp</a>-->
        <div class="contactForm">
            <div class="inputContainer">
                <!--<label>Contact</label>-->
                <input type="tel" maxlength="10" id="contact" placeholder="10 digit contact" />
            </div>
            <div class="inputContainer">
                <input type="button" value="Get Coupon" id="submit" onclick="getCoupon()" />
            </div>
            <div class="errorText"></div>
        </div>
        <div class="loaderBox">
            <img src="./loader.gif" class="loader"/>
            <div class="loaderText"></div>
        </div>
        <div class="couponBox">
            <div class="offerText"></div>
        </div>
        <div class="couponErrorBox">
            <div class="offerError"></div>
        </div>
        <div style="display: inline-block; vertical-align: top;">
            <button onclick="sharefb(true)" class="shareButton" style="font-size: 21px;text-transform: capitalize;"><img src="/img/fb-share.png" /></button>
            <a target="_blank" href="https://twitter.com/intent/tweet?text=Visit%20Chaayos%20Churchgate%20%26%20get%20free%20chai%20on%20these%20days%20https%3A%2F%2Fcafes.chaayos.com%2Fchurchgate" class="twitter"
               >Tweet</a>
        </div>
        <script data-cfasync="false" type="application/javascript">
            window.dataLayer = window.dataLayer || [];
            if(!window.isMobile){
                document.getElementsByClassName("banner")[0].setAttribute("id", "desktop");
                document.getElementsByClassName("couponBox")[0].setAttribute("id", "desktop");
                document.getElementsByClassName("couponErrorBox")[0].setAttribute("id", "desktop");
            }

            function formatDate(date, format) {
                let time = new Date(date);
                let yyyy = time.getFullYear();
                let M = time.getMonth() + 1;
                let d = time.getDate();
                let MM = M;
                const monthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                let dd = d;
                let hh = time.getHours();
                let mm = time.getMinutes();
                let ss = time.getSeconds();
                const A = hh < 12 ? "AM" : "PM";
                if (format.indexOf("A") > -1) {
                    hh = (hh > 12) ? hh - 12 : hh;
                }
                MM = (M < 10) ? "0" + M : M;
                dd = (d < 10) ? "0" + d : d;
                hh = (hh < 10) ? "0" + hh : hh;
                mm = (mm < 10) ? "0" + mm : mm;
                format = format.replace("yyyy", yyyy);
                format = format.replace("MMM", monthList[M - 1]);
                format = format.replace("MM", MM);
                format = format.replace("dd", dd);
                format = format.replace("hh", hh);
                format = format.replace("mm", mm);
                format = format.replace("ss", ss);
                //format = format.replace("A", A);
                return format;
            }
            function getUrlVars() {
                var vars = {};
                var parts = window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
                    vars[key] = value;
                });
                return vars;
            }
            function getUrlParam(parameter, defaultvalue){
                var urlparameter = defaultvalue;
                if(Object.keys(getUrlVars()).length > 0 && getUrlVars()[parameter] != null && getUrlVars()[parameter].length>0) {
                    urlparameter = getUrlVars()[parameter];
                }
                return urlparameter;
            }
            function getCookie(cname) {
                var name = cname + "=";
                var decodedCookie = decodeURIComponent(document.cookie);
                var ca = decodedCookie.split(';');
                for(var i = 0; i <ca.length; i++) {
                    var c = ca[i];
                    while (c.charAt(0) == ' ') {
                        c = c.substring(1);
                    }
                    if (c.indexOf(name) == 0) {
                        return c.substring(name.length, c.length);
                    }
                }
                return "";
            }
            function getCoupon() {
                console.log("clicked::::::::::");
                var contact = parseInt(document.getElementById("contact").value);
                var contactRegex = /^([6-9]\d{9})$/;
                if(!contactRegex.test(contact)) {
                    document.getElementsByClassName("errorText")[0].innerText = "Please add valid contact!";
                } else {
                    document.getElementsByClassName("contactForm")[0].style.display = "none";
                    document.getElementsByClassName("loaderText")[0].innerText = "Preparing brand new coupon for you.";
                    document.getElementsByClassName("loaderBox")[0].style.display = "block";
                    var source = getUrlParam("s", "OPEN_LINK");
                    var token  = getCookie("token");
                    $.ajax({
                        method:"POST",
                        url: "https://internal.chaayos.com/neo-service/rest/v1/cn/sclo",
                        data:JSON.stringify({token:token, contactNumber:contact, offerDetailId:3174, unitId:26183, acquisitionSource:source}),
                        contentType: "application/json",
                        headers:{},
                        success: function(result){
                            document.getElementsByClassName("loaderBox")[0].style.display = "none";
                            if(result.errorMessage != null) {
                                document.getElementsByClassName("offerError")[0].innerText = result.errorMessage;
                                document.getElementsByClassName("offerText")[0].style.display = "none";
                                document.getElementsByClassName("offerError")[0].style.display = "block";
                                document.getElementsByClassName("offerText")[0].innerText = "";
                                document.getElementsByClassName("couponErrorBox")[0].style.display = "block";
                                document.getElementsByClassName("couponBox")[0].style.display = "none";
                            } else {
                                var offerData = "Congratulations! You have won FREE Chai for " + result.offerDayCount +
                                    (result.offerDayCount > 1 ? " Days":" Day") + ". Please use coupon code - " + result.couponCode +
                                    " at Chaayos, " + result.unitLocation + ". This coupon is valid from "+ formatDate(result.startDate, "dd MMM yyyy")
                                    +" till "+formatDate(result.endDate, "dd MMM yyyy");
                                document.getElementsByClassName("offerText")[0].innerText = offerData;
                                document.getElementsByClassName("offerText")[0].style.display = "block";
                                document.getElementsByClassName("offerError")[0].style.display = "none";
                                document.getElementsByClassName("offerError")[0].innerText = "";
                                document.getElementsByClassName("couponBox")[0].style.display = "block";
                                document.getElementsByClassName("couponErrorBox")[0].style.display = "none";
                            }
                        },
                        error: function (error) {
                            console.log(result);
                            document.getElementsByClassName("offerError")[0].innerText = "Error loading coupon!";
                            document.getElementsByClassName("offerText")[0].style.display = "none";
                            document.getElementsByClassName("offerError")[0].style.display = "block";
                            document.getElementsByClassName("offerText")[0].innerText = "";
                        }
                    });
                    /*window.setTimeout(function () {
                        document.getElementsByClassName("loaderBox")[0].style.display = "none";
                        document.getElementsByClassName("couponBox")[0].style.display = "block";
                    }, 1000)*/
                    /*document.getElementsByClassName("loaderText")[0].innerText = "Attaching offer to coupon.";
                    document.getElementsByClassName("loaderText")[0].innerText = "Offer is ready!";*/
                }
            }

            window.fbAsyncInit = function () {
                FB.init({
                    appId: '715946468505409',
                    autoLogAppEvents: true,
                    xfbml: true,
                    version: 'v2.12'
                });
            };

            (function (d, s, id) {
                var js, fjs = d.getElementsByTagName(s)[0];
                if (d.getElementById(id)) {
                    return;
                }
                js = d.createElement(s);
                js.id = id;
                js.src = "https://connect.facebook.net/en_US/sdk.js";
                fjs.parentNode.insertBefore(js, fjs);
            }(document, 'script', 'facebook-jssdk'));

            function sharefb() {
                FB.ui({
                        method: 'share',
                        href: 'https://cafes.chaayos.com/churchgate'
                    },
                    // callback
                    function (response) {
                    }
                );
            }

            window.twttr = (function (d,s,id) {
                var t, js, fjs = d.getElementsByTagName(s)[0];
                if (d.getElementById(id)) return; js=d.createElement(s); js.id=id;
                js.src="//platform.twitter.com/widgets.js"; fjs.parentNode.insertBefore(js, fjs);
                return window.twttr || (t = { _e: [], ready: function(f){ t._e.push(f) } });
            }(document, "script", "twitter-wjs"));

        </script>
    </body>
</html>