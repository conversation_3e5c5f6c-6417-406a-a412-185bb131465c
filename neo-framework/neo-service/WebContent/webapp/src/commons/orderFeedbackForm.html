<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chaayos Feedback</title>
<!--    <link rel="stylesheet" href="bootstrap-5.0.2/dist/css/bootstrap.min.css"/>-->
<!--    <link href="../../../dist/css/bootstrap.min.css" rel="stylesheet">-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" />
    <link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'>
    <script src="./largeStar.js" type="application/javascript"></script>
    <script src="./productCard.js" type="application/javascript"></script>
    <script src="./orderFeedbackFormCtrl.js" type="application/javascript"></script>
    <script data-cfasync="false" type="application/javascript" src="/jquery-3.2.1.min.js"></script>
    <script data-cfasync="false" type="application/javascript">
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            window.isMobile = true;
        } else {
            window.isMobile = false;
        }
    </script>
</head>
<style>
    .mainContainer{
        background-color: white;
        padding-right: 0px;
        padding-left: 0px;
        display: none;
    }
    .flex-all-center{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .parent{
        background-color: #F2F2F2;
    }
    .chaayosLogo{
        margin-top: 10px;
        margin-bottom: 15px;
    }
    .smallText{
        font-size: 12px;
        font-weight: 400;
        font-family: 'Nunito';
    }
    .mainMargin{
        margin-left: 8%;
        margin-right: 8%;
    }
    .thinDivider{
        height: 3px;
        background-color: #F2F2F2;
        margin-top: 5px;
    }
    .thickDivider{
        height: 6px;
        background-color: #F2F2F2;
        margin-top: 25px;
        width: 100%;
    }
    .rateYourExText{
        font-size: 16px;
        font-weight: 700;
        font-family: 'Nunito';
    }
    .rateYourNpsExText{
        font-size: 14px;
        font-weight: 700;
        font-family: 'Nunito';
    }
    .productDimentionText{
        font-size: 10px;
        font-weight: 500;
        font-family: 'Nunito';
        color: #70777D;
    }
    .didYouLikeText{
        font-size: 13px;
        font-weight: 500;
        font-family: 'Nunito';
        color: #8C8B8B;
    }
    .productTextArea{
        height: 100%;
        width: 100%;
        background-color: #F2F2F2;
        border: none;
        overflow: auto;
        outline: none;
        border-width: 0;
        border-radius: 5px;
        resize: none;
        font-size: 11px;
        font-family: 'Nunito';
        padding: 5px 10px;
    }
    .submitButton{
        background-color: #108A45;
        color: white;
        text-align: center;
        align-items: center;
        width: 65%;
        height: 40px;
        flex-direction: row;
        justify-content: center;
        padding-left: 40px;
        padding-right: 40px;
        padding-top: 10px;
        padding-bottom: 10px;
        border-radius: 700px;
    }

</style>

<body>
<div class="parent">
    <div class=" row me-0">
        <div class="col-md-3" style="background-color: aqua; height: 0px;"></div>

        <!-- MAIN UI START HERE -->
        <div class="col-md-6 mainContainer" id="mainContainer1" >
            <div class="mainMargin">
                <div class="flex-all-center chaayosLogo">
                    <img src="./feedbackFormAssets/chaayoslogo.svg" style="height: 40px; width:100%;"/>
                </div>
                <div class="row">
                    <div style="flex:1; justify-content: flex-end; max-width: 30px;" class="flex-all-center pe-0">
                        <img src="./feedbackFormAssets/location.svg" style="height: 15px; width: 15px;"/>
                    </div>
                    <div style="flex:8; justify-content: flex-start;" class="flex-all-center">
                        <p class="smallText mb-0" id="cafeNameAndCity">SDA Market, New Delhi</p>
                    </div>
                    <div style="flex:9; justify-content: flex-end;" class="flex-all-center mb-0">
                        <p class="smallText mb-0" id="orderDate">April 13, 11:33am</p>
                    </div>
                </div>

                <div class="row">
                    <div style="flex:1; max-width: 30px" class="pe-0"></div>
                    <div style="flex:17; justify-content: flex-start; " class="flex-all-center">
                        <p class="smallText mb-0" id="orderId">Order Id: 53537382937465672</p>
                    </div>
                </div>
            </div>

            <div class="thinDivider"></div>

            <div class="mainMargin mt-4" id="npsRatingHeading">
                <div class="row">
                    <div style="flex:1; justify-content: flex-end; max-width: 30px;" class="flex-all-center pe-0">
                        <img src="./feedbackFormAssets/ratingIcon.svg" style="height: 20px; width: 20px;"/>
                    </div>
                    <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                        <p class="rateYourNpsExText mb-0" id="npsQuestion"></p>
                    </div>
                </div>
            </div>
            <div class="mainMargin mt-2" id="npsRatingBlock">
                <div class="row" style="min-height: 80px;">
                    <div style="flex:1; justify-content: flex-end; max-width: 30px;" class="flex-all-center pe-0"></div>
                    <div class="row" style="flex: 17; max-width: 400px;">
                        <div class="px-0 flex-all-center" id="main_nps_star_1" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(1);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_2" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(2);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_3" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(3);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_4" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(4);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_5" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(5);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_6" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(6);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_7" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(7);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_8" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(8);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_9" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(9);"></div>
                        <div class="px-0 flex-all-center" id="main_nps_star_10" style="flex: 1; flex-direction: column;" onclick="changeNpsRating(10);"></div>
                    </div>
                </div>
            </div>
            <div class="thickDivider" id="npsRatingDivider"></div>
            <div class="mainMargin mt-2" id="orderRatingHeading">
                <div class="row">
                    <div style="flex:1; justify-content: flex-end; max-width: 30px;" class="flex-all-center pe-0">
                        <img src="./feedbackFormAssets/ratingIcon.svg" style="height: 20px; width: 20px;"/>
                    </div>
                    <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                        <p class="rateYourExText mb-0" id="orderFeedBackQuestion"></p>
                    </div>
                </div>
            </div>
            <div class="mainMargin mt-4" id="orderRatingBlock">
                <div class="row" style="min-height: 80px">
                    <div style="flex:1; justify-content: flex-end; max-width: 30px;" class="flex-all-center pe-0"></div>
                    <div class="row" style="flex: 17; max-width: 280px;">
                        <div class="px-0 flex-all-center" id="main_star_1" style="flex: 1; flex-direction: column;" onclick="changeMainRating(1);"></div>
                        <div class="px-0 flex-all-center" id="main_star_2" style="flex: 1; flex-direction: column;" onclick="changeMainRating(2);"></div>
                        <div class="px-0 flex-all-center" id="main_star_3" style="flex: 1; flex-direction: column;" onclick="changeMainRating(3);"></div>
                        <div class="px-0 flex-all-center" id="main_star_4" style="flex: 1; flex-direction: column;" onclick="changeMainRating(4);"></div>
                        <div class="px-0 flex-all-center" id="main_star_5" style="flex: 1; flex-direction: column;" onclick="changeMainRating(5);"></div>
                    </div>
                </div>
            </div>
            <div class="thickDivider" id="orderRatingDivider"></div>
            <div id="productCards"></div>
            <div class="mainMargin mt-4" id="orderCommentText">
                <div class="row">
                    <div style="flex:1; justify-content: flex-end; max-width: 30px;" class="flex-all-center pe-0">

                    </div>
                    <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                        <p class="rateYourExText mb-0">Anything else you would like to add?</p>
                    </div>
                </div>
            </div>
            <div class="mainMargin mt-3" id="orderCommentArea">
                <div class="row">
                    <div style="flex:1;max-width: 30px;"></div>
                    <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                        <textarea class="productTextArea" id="order_textarea" rows="4" maxlength="200"></textarea>
                    </div>
                </div>
            </div>
            <div class="mainMargin mt-3" id="callbackCheckboxContainer">
                <div class="row">
                    <div style="flex:1;max-width: 30px;">
                        <input class="form-check-input" type="checkbox" value="" id="callbackCheckbox" onclick="checkboxClick();">
                    </div>
                    <div style="flex:17; justify-content: flex-start;" class="flex-all-center">
                        <p>Would you like to receive a call from our customer service.</p>
                    </div>
                </div>
            </div>
            <div class="mainMargin mt-3 mb-3" id="submitFeedbackButton">
                <div class="row">
                    <div style="flex:1;max-width: 30px;"></div>
                    <div style="flex:17; justify-content: center;" class="flex-all-center">
                        <div class="submitButton" id="submitButton" onclick="submitFeedback();">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="mainContainer2" style="padding: 0px; text-align: -webkit-center; display: none">
            <div class="col-md-6 mainContainer d-flex align-items-center"  id="mainSubContainer2" style=" max-width: 450px;">
                <div style="text-align: center; width: 100%">
                    <div style="font-size: 90px; color:#5e7e47; font-family: 'Nunito';" >OOPS!</div>
                    <p style="text-transform:uppercase; margin-bottom:0px; font-size:23px; font-family: Nunito;">Something Went Wrong</p>
                    <p style="font-size:16px; font-family: Nunito;">Unable to fetch your order detail.</p>
                </div>
            </div>
        </div>
        <!-- Already rated view        -->
        <div id="mainContainer3" style="padding: 0px; text-align: -webkit-center; display: none">
            <div class="col-md-6 mainContainer d-flex align-items-center" id="mainSubContainer3" style=" max-width: 450px; padding-left:10px; padding-right: 10px ">
                <div style="text-align: center; width: 100%;">
                    <div style="font-size: 25px; color:#5e7e47; font-family: Nunito; " >Feedback already submitted.</div>
                    <div id="alreadySubmitNpsBlock" style="margin-top: 40px">
                        <p style=" margin-bottom:0px; font-size:19px; font-family: Nunito;" >This was your rating.</p>
                        <div class="row" style="flex: 17; margin-left: 3%; margin-right: 3%; margin-top: 3%" >
                            <div class="px-0 flex-all-center" id="already_star_nps_1" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_2" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_3" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_4" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_5" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_6" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_7" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_8" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_9" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_nps_10" style="flex: 1; flex-direction: column;" ></div>
                        </div>
                    </div >
                    <div id="alreadySubmitBlock" style="margin-top: 40px">
                        <p style=" margin-bottom:0px; font-size:19px; font-family: Nunito;" >This was your rating.</p>
                        <div class="row" style="flex: 17; margin-left: 10%; margin-right: 10%; margin-top: 3%" >
                            <div class="px-0 flex-all-center" id="already_star_1" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_2" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_3" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_4" style="flex: 1; flex-direction: column;" ></div>
                            <div class="px-0 flex-all-center" id="already_star_5" style="flex: 1; flex-direction: column;" ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- MAIN UI ENDS HERE -->

        <div class="col-md-3" style="background-color: black; height: 0px;"></div>
    </div>
</div>
<script>
    var selectedMainRating=0;
    var selectedNpsRating=0;
    var productSelectedRating=[];
    var isSubmitting = false;
     window.initialViewPort = window.innerHeight;
    var issueTagApplicableProducts=[10,11,12,13,14,15,30,50,80,1376,1033,1282,1292,1293,1294,1324];
    var tagsDesc=["Tasteless", "Less Kadak", "Watery", "No Adrak"];
    document.getElementById("mainContainer1").style.minHeight = window.innerHeight.toString()+"px";
    document.getElementById("mainContainer2").style.minHeight = window.innerHeight.toString()+"px";
    document.getElementById("mainSubContainer2").style.minHeight = window.innerHeight.toString()+"px";
    document.getElementById("mainContainer3").style.minHeight = window.innerHeight.toString()+"px";
    document.getElementById("mainSubContainer3").style.minHeight = window.innerHeight.toString()+"px";
    document.getElementById("submitButton").innerHTML=getSubmitButtonChild(false);
    document.getElementById("orderRatingDivider").style.display="none";
    document.getElementById("orderRatingBlock").style.display="none";
    document.getElementById("npsRatingDivider").style.display="none";
    document.getElementById("npsRatingBlock").style.display="none";
    document.getElementById("orderRatingHeading").style.display="none";
    document.getElementById("npsRatingHeading").style.display="none";
    document.getElementById("alreadySubmitBlock").style.display="none";
    document.getElementById("alreadySubmitNpsBlock").style.display="none";
    document.getElementById("mainContainer1").style.display="none";
    document.getElementById("mainContainer2").style.display="none";
    document.getElementById("mainContainer3").style.display="none";
    function getCookie(cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }
    var token= getCookie("token");
    var orderDetail;
    try{
        orderDetail = JSON.parse(getCookie("orderDetail"));
        if(orderDetail.sor){
            document.getElementById("orderFeedBackQuestion").innerText=orderDetail.ofq;
            document.getElementById("orderRatingDivider").style.display="block";
            document.getElementById("orderRatingBlock").style.display="block";
            document.getElementById("orderRatingHeading").style.display="block";
        }
        if(orderDetail.sonr){
            document.getElementById("npsQuestion").innerText=orderDetail.nq;
            document.getElementById("npsRatingDivider").style.display="block";
            document.getElementById("npsRatingBlock").style.display="block";
            document.getElementById("npsRatingHeading").style.display="block";
        }
        if(orderDetail.or > 0 || orderDetail.onr > 0){
            if(orderDetail.or > 0){
                document.getElementById("alreadySubmitBlock").style.display="block";
                changeAlreadyRating(orderDetail.or);
            }
            if(orderDetail.onr > 0){
                document.getElementById("alreadySubmitNpsBlock").style.display="block";
                changeAlreadyNpsRating(orderDetail.onr);
            }
            document.getElementById("mainContainer1").style.display="none";
            document.getElementById("mainContainer2").style.display="none";
            document.getElementById("mainContainer3").style.display="block";
        }else{
            document.getElementById("mainContainer1").style.display="block";
            document.getElementById("mainContainer2").style.display="none";
            document.getElementById("mainContainer3").style.display="none";
        }

        document.getElementById("cafeNameAndCity").innerText = orderDetail["un"]+", "+orderDetail["uc"];
        document.getElementById("orderDate").innerText = orderDetail["od"];
        document.getElementById("orderId").innerText = "Order Id: "+orderDetail["goid"];
        document.getElementById("submitButton").style.backgroundColor='#B5D7C3';


        for(var i=1;i<=orderDetail.fiol.length;i++){
            productSelectedRating.push(0);
            orderDetail.fiol[i-1].tags=[false,false,false,false];
            var d=document.createElement("div");
            d.id="productCard_"+i;
            document.getElementById("productCards").appendChild(d);
        }


        for(var i=1;i<=orderDetail.fiol.length;i++){
            document.getElementById("productCard_"+i).innerHTML=productCard(orderDetail.fiol[i-1],i, orderDetail.bpiu);
            if(issueTagApplicableProducts.includes(orderDetail.fiol[i-1].pid)){
                document.getElementById("issueTagContainer_"+i).innerHTML = getIssueTagButtons(i,orderDetail.fiol[i-1].tags);
            }
            document.getElementsByClassName("productExpand")[i-1].style.display='none';
            changeProductRating(0,i);
        }
        document.getElementById("submitFeedbackButton").style.display='none';
        document.getElementById("orderCommentArea").style.display='none';
        document.getElementById("orderCommentText").style.display='none';
        document.getElementById("callbackCheckboxContainer").style.display='none';

        changeMainRating(0);
        changeNpsRating(0);
    }catch (e){
        console.log(e);
        document.getElementById("mainContainer1").style.display="none";
        document.getElementById("mainContainer2").style.display="block";
        document.getElementById("mainContainer3").style.display="none";
    }


</script>
</body>
</html>
