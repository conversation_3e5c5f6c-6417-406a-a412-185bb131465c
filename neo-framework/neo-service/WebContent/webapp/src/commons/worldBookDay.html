<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chaayos - World Book Day Contest</title>
    <meta property="description" content="World Book Day contest by Chaayos in association with HarperCollins India. Books and Chai, what else can one ask for!" />
    <meta property="og:url" content="https://cafes.chaayos.com/worldBookDay" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Books and Chai, what else can one ask for!" />
    <meta property="og:description" content="Won the World Book Day contest by Chaayos in association with HarperCollins India!!" />
    <meta property="og:image" content="https://cafes.chaayos.com/img/worldBookDayChaayos.jpg" />
    <meta property="og:image:secure_url" content="https://cafes.chaayos.com/img/worldBookDayChaayos.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:type" content="image/jpeg" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Chaayos worldBookDay" />
    <meta property="fb:app_id" content="1300275053451615" />
    <style type="text/css">
        body{
            margin: 0;
            padding: 0;
            text-align: center;
        }
        .headerWrapper {
            background: #5e7e47;
            color: #fff;
            height: 50px;
            text-align: center;
        }
        .headerWrapper img{
            max-height: 40px;
            margin: 5px 10px;
        }
        .shareButton{
            border: none;
            background: transparent;
            max-width: 288px;
            width: 80%;
            margin: 15px 0;
            display: inline-block;
        }
        .shareButton img{width: 100%}
        #couponCode{
            border:#ccc 3px dashed;
            background: lightyellow;
            padding: 5px 15px;
            font-size: 18px;
            max-width: 400px;
            display: inline-block;
            text-transform: uppercase;
            margin: 10px 0;
        }
        #general, #couponWrapper{
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top:50px;
        }
        #general img.banner, #couponWrapper img.banner{
            width: 100%;
            max-width: 1000px;
        }
        #couponError{
            color: red;
        }
        .twitter{
            background: #1a8bf0;
            color: #fff;
            border-radius: 5px;
            padding: 15px 0;
            max-width: 288px;
            width: 80%;
            display: inline-block;
            font-size: 21px;
            text-decoration: none;
            font-family: sans-serif;
        }
    </style>
</head>

<body>
<div class="headerWrapper">
    <img src="/chaayos/logo/chaayosLogo.png"/>
</div>
<div id="general">
    <img class="banner" src="/img/worldBookDayChaayos.jpg" /><br />
    <button onclick="sharefb(false)" class="shareButton"><img src="img/fb-share.png" /> </button>
</div>
<div id="couponWrapper">
    <img class="banner" src="/img/worldBookDayChaayos.jpg" /><br />
    <div id="couponCode"></div><br />
    <div id="couponError" style="display: none;">Oops try again.</div><br />
    <button onclick="sharefb(true)" class="shareButton" style="font-size: 21px;text-transform: capitalize;"><img src="/img/fb-share.png" /></button>
    <br /> <p style="text-transform: capitalize; font-size: 18px;font-family: sans-serif">to see your coupon</p>
    <p id="saveCouponHelper">Save this coupon code for further usage.</p>
</div>
<!--<script type="text/javascript" async src="https://platform.twitter.com/widgets.js"></script>-->
<script data-cfasync="false" type="application/javascript">
    window.dataLayer = window.dataLayer || [];
    var url_string = window.location.href;
    function getParameterByName(name, url) {
        if (!url) url = window.location.href;
        name = name.replace(/[\[\]]/g, "\\$&");
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    }
    var c = getParameterByName("cc");
    if(c != null){
        document.getElementById("couponError").style.display = "none";
        document.getElementById("couponWrapper").style.display = "block";
        document.getElementById("general").style.display = "none";
        document.getElementById("couponCode").innerText = "*******";
        document.getElementById("saveCouponHelper").style.display = "none";
    }else{
        document.getElementById("couponWrapper").style.display = "none";
        document.getElementById("general").style.display = "block";
    }
    window.fbAsyncInit = function () {
        FB.init({
            appId: '1300275053451615',
            autoLogAppEvents: true,
            xfbml: true,
            version: 'v2.12'
        });
    };

    (function (d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
            return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));

    function sharefb(isCoupon) {
        FB.ui({
                method: 'share',
                href: 'https://cafes.chaayos.com/worldBookDay'
            },
            // callback
            function (response) {
                if (response && !response.error_message) {
                    document.getElementById("couponCode").innerText = c;
                } else {
                    document.getElementById("couponError").style.display = "block";
                }
            }
        );
    }

</script>
</body>
</html>