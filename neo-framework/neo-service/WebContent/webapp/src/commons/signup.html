<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON></title>
    <meta property="description" content="<PERSON><PERSON><PERSON>fer and <PERSON><PERSON><PERSON>"/>
    <meta property="og:url" content="https://cafes.chaayos.com/signup"/>
    <meta property="og:type" content="website"/>
    <meta property="og:title" content="Chaayos Referral Program"/>
    <meta property="og:description" content="Click and earn 300 Chaayos Cash instantly."/>
    <meta property="og:image" content="https://cafes.chaayos.com/img/referral/referShare1.png"/>
    <meta property="og:image:secure_url" content="https://cafes.chaayos.com/img/referral/referShare1.png"/>
    <meta property="og:image:width" content="1200"/>
    <meta property="og:image:type" content="image/png"/>
    <meta property="og:image:height" content="630"/>
    <meta property="og:image:alt" content="Click and earn 300 Chaayos Cash instantly."/>
    <meta property="fb:app_id" content="1300275053451615"/>
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:site" content="@chaayos"/>
    <meta name="twitter:title" content="Chaayos Referral Program"/>
    <meta name="twitter:description" content="Click and earn 300 Chaayos Cash instantly."/>
    <meta property="twitter:image" content="https://cafes.chaayos.com/img/referral/referShare.png"/>
    <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">
    <script data-cfasync="false" type="application/javascript" src="/jquery-3.2.1.min.js"></script>
    <style data-cfasync="false" type="text/css">
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-family: 'Montserrat', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .page#desktop .container {
            margin: 0 100px;
        }

        .page#mobile .container {
            margin: 0 20px;
        }

        .page#desktop .banner {
            /*background: #571abc;
            background-image: linear-gradient(171deg, #571abc 29%, #8144e5);*/
            background: #056c06;
            background-image: linear-gradient(171deg, #306f31 41%, #48a249);
            padding: 20px;
            height: 380px;
            position: relative;
        }

        .page#mobile .banner {
            /*background: #571abc;
            background-image: linear-gradient(171deg, #571abc 29%, #8144e5);*/
            background: #056c06;
            background-image: linear-gradient(171deg, #306f31 41%, #48a249);
            padding: 20px;
            position: relative;
        }

        .page#desktop .banner .logo {
            height: 45px;
        }

        .page#mobile .banner .logo {
            width: 155px;
        }

        .page#desktop .banner .refer {
            max-height: 350px;
            float: right;
            position: absolute;
            right: 0;
            bottom: 20px;
            max-width: 65%;
        }

        .page#mobile .banner .refer {
            width: 100%;
            margin: 10px 0;
        }

        .page#desktop .banner .referText {
            color: #FFF;
            font-size: 35px;
            padding: 0;
            margin: 100px 0 0 0;
            max-width: 50%;
        }

        .page#mobile .banner .referText {
            color: #FFF;
            font-size: 25px;
            margin: 0;
            padding: 0;
        }

        .page#desktop .banner .referText span {
            font-size: 21px;
        }

        .page#mobile .banner .referText span {
            font-size: 18px;
        }

        .page#desktop .contactForm {
            margin-top: 20px;
            max-width: 500px;
        }

        .page#mobile .contactForm {
            margin-top: 20px;
            max-width: 500px;
        }

        .page#desktop .copyLinkBtn{
            display: inline-block;
            padding: 15px 20px;
            color: #FFF;
            cursor: pointer;
            background: #14a386;
            font-size: 21px;
            margin-left: -5px;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
        }

        .page#mobile .copyLinkBtn{
            display: inline-block;
            padding: 10px 15px;
            color: #fff;
            cursor: pointer;
            background: #14a386;
            font-size: 18px;
            margin-left: -6px;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
        }

        .page#desktop .inputContainer {
            margin-bottom: 10px;
            background: #fff;
            padding: 15px 75px;
            display: inline-block;
            border-radius: 2px;
            font-size: 21px;
            font-weight: bold;
            color: #0d620d;
        }

        .page#mobile .inputContainer {
            margin-bottom: 10px;
            background: #fff;
            padding: 10px 45px;
            display: inline-block;
            border-radius: 3px;
            font-size: 18px;
            font-weight: bold;
        }

        .page#desktop .socialContainer {
            text-align: center;
        }

        .page#mobile .socialContainer {
            text-align: center;
        }

        .page#desktop .social {
            display: inline-block;
            width: 114px;
            padding: 20px 0;
            background: #fafafa;
            margin: 0 8%;
            cursor: pointer;
        }

        .page#mobile .social {
            display: inline-block;
            padding: 10px;
            background: #fafafa;
            margin: 0 5%;
        }

        .page#mobile .social img{
            width: 50px;
        }

        #refCode {
            text-transform: uppercase;
        }




        .loaderBox {
            display: none;
            margin: 10px;
            text-align: center;
        }

        .loader {
            height: 200px;
            border-radius: 10px;
        }

        .loaderText {
            color: #000;
            left: 0;
            right: 0;
            padding: 20px;
            font-size: 21px;
        }

        .contactForm {
            margin: auto;
            margin-top: 20px;
            max-width: 500px;
            width: 80%;
        }

        .inputContainer {
            margin: auto;
            padding: 2px;
            margin-bottom: 10px;
        }

        .inputContainer label {
            display: block;
            text-align: left;
            padding-bottom: 3px;
        }

        .inputContainer input {
            display: block;
            width: 100%;
            background: #fff;
            border-radius: 4px;
            color: #000;
            font-size: 21px;
            padding: 15px 0;
            text-align: center;
            border: #ccc 1px solid;
        }

        .inputContainer input[type="button"] {
            display: block;
            width: 100%;
            border: none;
            background: green;
            border-radius: 4px;
            color: #FFF;
            font-size: 21px;
            padding: 15px 0;
        }

        .errorText {
            color: red;
        }

        .otpForm {
            margin: auto;
            margin-top: 20px;
            max-width: 500px;
            width: 80%;
            display: none;
        }

        .couponBox {
            background: #fff;
            width: 300px;
            height: 200px;
            margin: auto;
            text-align: center;
            border-radius: 8px;
            max-width: 80%;
            position: relative;
            background-image: url("./celebration.gif");
            background-size: contain;
            display: none;
            margin-top: 20px;
        }

        .couponBox#desktop {
            background: #fff;
            width: 500px;
            height: 300px;
            margin: auto;
            text-align: center;
            border-radius: 8px;
            position: relative;
            background-image: url("./celebration.gif");
            background-size: contain;
            display: none;
            margin-top: 20px;
        }

        .couponErrorBox {
            background: #fff;
            width: 300px;
            height: 200px;
            margin: auto;
            text-align: center;
            border-radius: 8px;
            max-width: 80%;
            position: relative;
            display: none;
            margin-top: 20px;
        }

        .couponErrorBox#desktop {
            background: #fff;
            width: 500px;
            height: 300px;
            margin: auto;
            text-align: center;
            border-radius: 8px;
            position: relative;
            display: none;
            margin-top: 20px;
        }

        .offerText {
            color: #616161;
            position: absolute;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background: #fff;
        }

        .offerError {
            color: #ce0000;
            position: absolute;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background: #fff;
        }

        .shareButton {
            border: none;
            background: transparent;
            max-width: 288px;
            width: 80%;
            margin: 15px 0;
            display: inline-block;
        }

        .shareButton img {
            width: 100%
        }

        .twitter {
            background: #1a8bf0;
            color: #fff;
            border-radius: 5px;
            padding: 15px 0;
            max-width: 288px;
            width: 80%;
            display: inline-block;
            font-size: 21px;
            text-decoration: none;
            font-family: sans-serif;
        }

        #refCode {
            text-transform: uppercase;
        }
    </style>
    <script data-cfasync="false" type="application/javascript">
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            window.isMobile = true;
        } else {
            window.isMobile = false;
        }
    </script>
    <script data-cfasync="false">
        (function (w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-MLDTX5R');
    </script>
</head>

<body>
<div class="page">
    <div class="banner" id="mobile">
        <div class="container">
            <img src="/chaayos/logo/chaayosLogo.png" class="logo"/>
            <div>
                <img src="../img/referral/referPeople.png" class="refer"/>
            </div>
            <p class="referText">Signup with referral code and earn <strong style="color: #ff9400;font-size: 43px;">300</strong> Chaayos Cash</p>
        </div>
    </div>
</div>

<div class="contactForm">
    <div class="inputContainer">
        <input type="text" maxlength="10" id="refCode" placeholder="Referral Code (Ex. CHAI123)"/>
    </div>
    <div class="inputContainer">
        <input type="tel" maxlength="10" id="contact" placeholder="10 digit contact"/>
    </div>
    <div class="inputContainer">
        <input type="button" value="Submit" id="submit" onclick="validate()"/>
    </div>
    <div class="errorText"></div>
</div>
<div class="loaderBox">
    <img src="./loader.gif" class="loader"/>
    <div class="loaderText"></div>
</div>
<div class="otpForm">
    <div class="inputContainer">
        <input type="number" maxlength="4" id="otp" placeholder="OTP"/>
    </div>
    <div class="inputContainer">
        <input type="button" value="Verify" id="verifyOtp" onclick="submit()"/>
    </div>
    <div class="otpErrorText"></div>
</div>
<div class="couponBox">
    <div class="offerText"></div>
</div>
<div class="couponErrorBox">
    <div class="offerError"></div>
</div>
<div class="container" style="text-align: left; margin: auto; margin-top: 20px; max-width: 600px; border: #127b2e 1px solid; padding: 20px; border-radius: 10px;">
    <div style="padding: 30px 0; font-size: 21px; font-weight: bold; text-align: left;">How it works</div>
    <p>Step 1: Received a Chaayos referral link from someone you know.</p>
    <p>Step 2: Click on the shared link and sign-up for Chaayos Referral Program.</p>
    <p>Step 3: You will be credited 300 Chaayos Cash in your account.</p>
    <p>Step 4: You can now visit Chaayos and use upto a maximum of 100 Chaayos Cash in any Dine-in order with a minimum purchase of Rs 350</p>
    <p>Step 5: You will also receive your own referral code post your first transaction, which you can use for referring ahead.</p>
</div>
<div style="text-align: center;">
    <a href="https://cafes.chaayos.com/terms">Terms & conditions</a>
</div>
<script data-cfasync="false" type="application/javascript">
    window.dataLayer = window.dataLayer || [];
    if (!window.isMobile) {
        document.getElementsByClassName("banner")[0].setAttribute("id", "desktop");
        document.getElementsByClassName("couponBox")[0].setAttribute("id", "desktop");
        document.getElementsByClassName("couponErrorBox")[0].setAttribute("id", "desktop");
    }
    if (!window.isMobile) {
        document.getElementsByClassName("page")[0].setAttribute("id", "desktop");
    } else {
        document.getElementsByClassName("page")[0].setAttribute("id", "mobile");
    }

    function getUrlVars() {
        var vars = {};
        var parts = window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
            vars[key] = value;
        });
        return vars;
    }

    function getUrlParam(parameter, defaultvalue) {
        var urlparameter = defaultvalue;
        if (Object.keys(getUrlVars()).length > 0 && getUrlVars()[parameter] != null && getUrlVars()[parameter].length > 0) {
            urlparameter = getUrlVars()[parameter];
        }
        return urlparameter;
    }

    var refCode = getUrlParam("r");
    var source = getUrlParam("s", "OPEN_LINK");
    var referrer = getUrlParam("n");
    var campaign = getUrlParam("c");
    if (refCode != null) {
        document.getElementById("refCode").value = refCode;
        document.getElementById("refCode").disabled = true;
        document.getElementById("refCode").style.background = "#CCC";
    }

    function getCookie(cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }

    function validate() {
        console.log("clicked::::::::::");
        var contact = parseInt(document.getElementById("contact").value);
        var refCode = document.getElementById("refCode").value;
        var contactRegex = /^([6-9]\d{9})$/;
        if (!contactRegex.test(contact)) {
            document.getElementsByClassName("errorText")[0].innerText = "Please add valid contact!";
        } else if (refCode == null || refCode.trim().length == 0) {
            document.getElementsByClassName("errorText")[0].innerText = "Please provide valid referral code!";
        } else {
            document.getElementsByClassName("contactForm")[0].style.display = "none";
            document.getElementsByClassName("loaderText")[0].innerText = "Please wait while we add your details.";
            document.getElementsByClassName("loaderBox")[0].style.display = "block";
            var token = getCookie("token");
            $.ajax({
                method: "POST",
                url: "https://internal.chaayos.com/neo-service/rest/v1/ref/v",
                data: JSON.stringify({
                    token: token,
                    contact: contact,
                    signUpRefCode: refCode.toUpperCase(),
                    source: source,
                    campaign: campaign
                }),
                contentType: "application/json",
                headers: {},
                success: function (result) {
                    document.getElementsByClassName("loaderBox")[0].style.display = "none";
                    if (result.otpSent != false) {
                        document.getElementsByClassName("otpForm")[0].style.display = "block";
                        document.getElementsByClassName("errorText")[0].style.display = "none";
                        document.getElementsByClassName("otpErrorText")[0].style.display = "none";
                    } else {
                        alert(result.msg);
                        window.location.reload();
                    }
                },
                error: function (error) {
                    console.log(error);
                    alert("Error validating details. Please try again.");
                    window.location.reload();
                }
            });
        }
    }

    function submit() {
        console.log("clicked::::::::::");
        var contact = parseInt(document.getElementById("contact").value);
        var refCode = document.getElementById("refCode").value;
        var otp = document.getElementById("otp").value;
        console.log(contact, refCode, otp);
        var contactRegex = /^([6-9]\d{9})$/;
        if (!contactRegex.test(contact)) {
            document.getElementsByClassName("errorText")[0].innerText = "Please add valid contact!";
        } else if (refCode == null || refCode.trim().length == 0) {
            document.getElementsByClassName("errorText")[0].innerText = "Please provide valid referral code!";
        } else {
            document.getElementsByClassName("contactForm")[0].style.display = "none";
            document.getElementsByClassName("otpForm")[0].style.display = "none";
            document.getElementsByClassName("loaderText")[0].innerText = "Please wait while we verify OTP.";
            document.getElementsByClassName("loaderBox")[0].style.display = "block";
            //var source = getUrlParam("s", "OPEN_LINK");
            var token = getCookie("token");
            $.ajax({
                method: "POST",
                url: "https://internal.chaayos.com/neo-service/rest/v1/ref/s",
                data: JSON.stringify({
                    token: token,
                    contact: contact,
                    signUpRefCode: refCode,
                    source: source,
                    otp: otp,
                    campaign: campaign
                }),
                contentType: "application/json",
                headers: {},
                success: function (result) {
                    document.getElementsByClassName("loaderBox")[0].style.display = "none";
                    if (result.status == true) {
                        var offerData = "Congratulations! We have credited your account with 300 Chaayos Cash. Visit your nearest Chaayos & choose from 80,000 types of Chai " +
                            "and our amazing range of food items.";
                        document.getElementsByClassName("couponBox")[0].style.display = "block";
                        document.getElementsByClassName("offerText")[0].innerHTML = offerData;
                        document.getElementsByClassName("errorText")[0].style.display = "none";
                        document.getElementsByClassName("otpErrorText")[0].style.display = "none";
                    } else {
                        alert("This customer already exists. Please refer someone else.");
                        window.location.reload();
                    }
                },
                error: function (error) {
                    console.log(error);
                    alert("Error validating details. Please try again.");
                    window.location.reload();
                }
            });
        }
    }

    window.fbAsyncInit = function () {
        FB.init({
            appId: '1300275053451615',
            autoLogAppEvents: true,
            xfbml: true,
            version: 'v2.12'
        });
    };

    (function (d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
            return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));

    function sharefb() {
        FB.ui({
                method: 'share',
                href: 'https://cafes.chaayos.com/signup?r=' + refCode + "&s=" + source
            },
            // callback
            function (response) {
            }
        );
    }

    window.twttr = (function (d, s, id) {
        var t, js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;
        js.src = "//platform.twitter.com/widgets.js";
        fjs.parentNode.insertBefore(js, fjs);
        return window.twttr || (t = {
            _e: [], ready: function (f) {
                t._e.push(f)
            }
        });
    }(document, "script", "twitter-wjs"));

</script>
</body>
</html>