/**
 * Created by Chaayos on 26-04-2017.
 */
var prod = process.env.NODE_ENV == "production";
var dev = process.env.NODE_ENV == "dev";
var local = process.env.NODE_ENV == "local";
var webpack = require('webpack');
var path = require('path');
var polyfill = require("babel-polyfill");

var plugins = [
    new webpack.DefinePlugin({
        'process.env': {
            NODE_ENV: JSON.stringify('production')
        }
    }),
    new webpack.optimize.UglifyJsPlugin({compress:true,warning:false, sourcemap:false}),
    new webpack.optimize.DedupePlugin(),
    new webpack.optimize.OccurenceOrderPlugin(),
    new webpack.optimize.AggressiveMergingPlugin(),
];

module.exports = {
    context: path.join(__dirname, "src"),
    devtool: !prod ? "inline-sourcemap" : null,
    entry: {
        sw: ["./js/sw.js"],
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                enforce: 'pre',
                loader: 'eslint-loader',
                options: {
                    emitWarning: true
                },
            },
        ],
        noParse: ["jquery"].map(function(name) {
            return path.join(__dirname, "node_modules", name);
        }),
        loaders: [
            {
                test: /\.js?$/,
                exclude: /(node_modules|bower_components)/,
                loader: 'babel-loader',
                query: {
                    presets: ['react', 'es2015', 'stage-0'],
                    plugins: ['react-html-attrs', 'transform-class-properties', 'transform-decorators-legacy']
                }
            },
            { test: /\.hbs$/, loader: 'handlebars-loader' }
        ]
    },
    output: {
        path: path.join(__dirname, "dist"),
        filename: "[name].js",
        crossOriginLoading: "anonymous"
    },
    plugins: plugins,
};