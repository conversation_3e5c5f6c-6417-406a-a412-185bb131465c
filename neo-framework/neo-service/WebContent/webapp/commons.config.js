var prod = process.env.NODE_ENV == "production";
var dev = process.env.NODE_ENV == "dev";
var local = process.env.NODE_ENV == "local";
var prodBasePath = "/home/<USER>/builds/neo";
var devBasePath = "/home/<USER>/builds/neo";
var localBasePath = "C:/Users/<USER>/Documents/GitHub/chaayos/neo-framework/neo-service/WebContent/webapp";
var localTemplate = "ejs!src/index.local.ejs";
var devTemplate = "ejs!src/index.dev.ejs";
var prodTemplate = "ejs!src/index.prod.ejs";
var webpack = require("webpack");
var path = require("path");
var WebpackMd5Hash = require("webpack-md5-hash");
var polyfill = require("babel-polyfill");
var CopyWebpackPlugin = require("copy-webpack-plugin");

var commonPlugins = [
    new WebpackMd5Hash(),
    new CopyWebpackPlugin([
        { from: prod ? "src/prod" : "src/dev", to: "dist" },
        { from: "src/commons", to: "dist" },
        { from: "src/chaayos", to: "dist/chaayos" },
        { from: "src/css", to: "dist/css" },
        { from: "src/fonts", to: "dist/fonts" },
        { from: "src/img", to: "dist/img" },
    ]),
];

module.exports = {
    plugins: commonPlugins,
    entry: {},
    output: {
        filename: "bundle",
    },
};
