# must be unique in a given SonarQube instance
sonar.projectKey=chaayos

# --- optional properties ---

# defaults to project key
sonar.projectName=chaayos
# defaults to 'not provided'
sonar.projectVersion=3.1.0-SNAPSHOT
 
# Path is relative to the sonar-project.properties file. Defaults to .
sonar.sources=.
 
# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8
sonar.java.source=1.8
sonar.java.binaries=**/target/classes
sonar.exclusions=**/*.xsd,**/*.js,**/*.png,**/*.jpeg,**/*.html,**/*.xml,**/*.json,**/*.jpg,**/*.svg,**/*.css,**/*.php,**/*.py,**/*.xlsx,**/*.pdf


