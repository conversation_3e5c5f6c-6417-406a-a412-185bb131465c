// Example controller method showing how to extract auth header and pass to service

@RestController
@RequestMapping("/payment")
public class PaymentController {

    @Autowired
    private PayTMNewPaymentService paymentServiceNew;

    @RequestMapping(method = RequestMethod.POST, value = "order/refund", 
                   consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderPayment refundOrder(@RequestParam(required = false) String partnerTransactionId,
                                   HttpServletRequest request) throws AuthenticationFailureException,
                                   DataUpdationException, DataNotFoundException, TemplateRenderingException, 
                                   CardValidationException {
        LOG.info("Refund order for Partner Transaction ID = " + partnerTransactionId);
        
        // Extract auth header from incoming request
        String authToken = request.getHeader("auth");
        if (authToken == null || authToken.isEmpty()) {
            authToken = request.getHeader("Authorization");
        }
        
        try {
            // Pass the auth token to the service method
            return paymentServiceNew.refundRequestForKettle(
                paymentServiceNew.getOrderPaymentByPartnerTransactionId(partnerTransactionId), 
                authToken
            );
        } catch (IOException | PaymentFailureException e) {
            throw new RuntimeException(e);
        }
    }

    // Alternative approach using @RequestHeader annotation
    @RequestMapping(method = RequestMethod.POST, value = "order/refund/v2", 
                   consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderPayment refundOrderV2(@RequestParam(required = false) String partnerTransactionId,
                                     @RequestHeader(value = "auth", required = false) String authToken) 
                                     throws AuthenticationFailureException, DataUpdationException, 
                                     DataNotFoundException, TemplateRenderingException, CardValidationException {
        LOG.info("Refund order for Partner Transaction ID = " + partnerTransactionId);
        
        try {
            // Pass the auth token directly to the service method
            return paymentServiceNew.refundRequestForKettle(
                paymentServiceNew.getOrderPaymentByPartnerTransactionId(partnerTransactionId), 
                authToken
            );
        } catch (IOException | PaymentFailureException e) {
            throw new RuntimeException(e);
        }
    }
}
