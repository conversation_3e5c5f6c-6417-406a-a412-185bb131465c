<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.0 r1840935">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Test Plan" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">true</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Create Order with COD" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">startnextloop</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">10</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">100</stringProp>
        <stringProp name="ThreadGroup.ramp_time">0</stringProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <ConfigTestElement guiclass="HttpDefaultsGui" testclass="ConfigTestElement" testname="HTTP Request Defaults" enabled="true">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value"></stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">dev.kettle.chaayos.com</stringProp>
          <stringProp name="HTTPSampler.port">9595</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path"></stringProp>
          <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </ConfigTestElement>
        <hashTree/>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Create Order COD" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
  &quot;generateOrderId&quot;: ${generateOrderId},&#xd;
  &quot;employeeId&quot;: 120057,&#xd;
  &quot;hasParcel&quot;: false,&#xd;
  &quot;status&quot;: &quot;CREATED&quot;,&#xd;
  &quot;cancelReason&quot;: null,&#xd;
  &quot;orders&quot;: [&#xd;
    {&#xd;
      &quot;itemId&quot;: 1,&#xd;
      &quot;productId&quot;: 50,&#xd;
      &quot;productName&quot;: &quot;Desi Paani Kum&quot;,&#xd;
      &quot;productType&quot;: 5,&#xd;
      &quot;billType&quot;: &quot;NET_PRICE&quot;,&#xd;
      &quot;addons&quot;: [&#xd;
        &#xd;
      ],&#xd;
      &quot;complimentaryDetail&quot;: {&#xd;
        &quot;reasonCode&quot;: null,&#xd;
        &quot;isComplimentary&quot;: null,&#xd;
        &quot;reason&quot;: null&#xd;
      },&#xd;
      &quot;quantity&quot;: 1,&#xd;
      &quot;price&quot;: 144.76,&#xd;
      &quot;amount&quot;: 144.76,&#xd;
      &quot;discountDetail&quot;: {&#xd;
        &quot;discountCode&quot;: null,&#xd;
        &quot;discountReason&quot;: null,&#xd;
        &quot;promotionalOffer&quot;: 0,&#xd;
        &quot;discount&quot;: {&#xd;
          &quot;percentage&quot;: 0,&#xd;
          &quot;value&quot;: 0&#xd;
        },&#xd;
        &quot;totalDiscount&quot;: 0&#xd;
      },&#xd;
      &quot;dimension&quot;: &quot;ChotiKetli&quot;,&#xd;
      &quot;hasBeenRedeemed&quot;: false,&#xd;
      &quot;isCombo&quot;: false,&#xd;
      &quot;composition&quot;: {&#xd;
        &quot;variants&quot;: [&#xd;
          {&#xd;
            &quot;productId&quot;: 100330,&#xd;
            &quot;alias&quot;: &quot;Regular Sugar&quot;,&#xd;
            &quot;uom&quot;: &quot;KG&quot;,&#xd;
            &quot;quantity&quot;: 0.021,&#xd;
            &quot;captured&quot;: true,&#xd;
            &quot;defaultSetting&quot;: true,&#xd;
            &quot;selected&quot;: true&#xd;
          },&#xd;
          {&#xd;
            &quot;productId&quot;: 100123,&#xd;
            &quot;alias&quot;: &quot;Regular Patti&quot;,&#xd;
            &quot;uom&quot;: &quot;KG&quot;,&#xd;
            &quot;quantity&quot;: 0.00638,&#xd;
            &quot;captured&quot;: true,&#xd;
            &quot;defaultSetting&quot;: true,&#xd;
            &quot;selected&quot;: true&#xd;
          }&#xd;
        ],&#xd;
        &quot;products&quot;: [&#xd;
          &#xd;
        ],&#xd;
        &quot;menuProducts&quot;: [&#xd;
          &#xd;
        ],&#xd;
        &quot;addons&quot;: [&#xd;
          {&#xd;
            &quot;product&quot;: {&#xd;
              &quot;productId&quot;: 986,&#xd;
              &quot;name&quot;: &quot;Tulsi&quot;,&#xd;
              &quot;displayName&quot;: null,&#xd;
              &quot;shortCode&quot;: &quot;T&quot;,&#xd;
              &quot;type&quot;: 12,&#xd;
              &quot;subType&quot;: 1202,&#xd;
              &quot;variantLevelOrdering&quot;: false,&#xd;
              &quot;classification&quot;: &quot;FREE_ADDON&quot;,&#xd;
              &quot;isInventoryTracked&quot;: false&#xd;
            },&#xd;
            &quot;dimension&quot;: {&#xd;
              &quot;infoId&quot;: 23,&#xd;
              &quot;name&quot;: &quot;Choti Ketli&quot;,&#xd;
              &quot;code&quot;: &quot;ChotiKetli&quot;,&#xd;
              &quot;shortCode&quot;: &quot;CK&quot;,&#xd;
              &quot;type&quot;: null&#xd;
            },&#xd;
            &quot;uom&quot;: null,&#xd;
            &quot;quantity&quot;: 1,&#xd;
            &quot;defaultSetting&quot;: false,&#xd;
            &quot;critical&quot;: false,&#xd;
            &quot;selected&quot;: true&#xd;
          },&#xd;
          {&#xd;
            &quot;product&quot;: {&#xd;
              &quot;productId&quot;: 969,&#xd;
              &quot;name&quot;: &quot;Adrak&quot;,&#xd;
              &quot;displayName&quot;: null,&#xd;
              &quot;shortCode&quot;: &quot;A&quot;,&#xd;
              &quot;type&quot;: 12,&#xd;
              &quot;subType&quot;: 1202,&#xd;
              &quot;variantLevelOrdering&quot;: false,&#xd;
              &quot;classification&quot;: &quot;FREE_ADDON&quot;,&#xd;
              &quot;isInventoryTracked&quot;: false&#xd;
            },&#xd;
            &quot;dimension&quot;: {&#xd;
              &quot;infoId&quot;: 23,&#xd;
              &quot;name&quot;: &quot;Choti Ketli&quot;,&#xd;
              &quot;code&quot;: &quot;ChotiKetli&quot;,&#xd;
              &quot;shortCode&quot;: &quot;CK&quot;,&#xd;
              &quot;type&quot;: null&#xd;
            },&#xd;
            &quot;uom&quot;: null,&#xd;
            &quot;quantity&quot;: 1,&#xd;
            &quot;defaultSetting&quot;: false,&#xd;
            &quot;critical&quot;: false,&#xd;
            &quot;selected&quot;: true&#xd;
          }&#xd;
        ],&#xd;
        &quot;options&quot;: [&#xd;
          &#xd;
        ]&#xd;
      },&#xd;
      &quot;recipeId&quot;: 114,&#xd;
      &quot;totalAmount&quot;: 144.76,&#xd;
      &quot;code&quot;: &quot;00009963&quot;,&#xd;
      &quot;taxes&quot;: [&#xd;
        {&#xd;
          &quot;type&quot;: &quot;GST&quot;,&#xd;
          &quot;code&quot;: &quot;CGST&quot;,&#xd;
          &quot;percentage&quot;: 2.5,&#xd;
          &quot;value&quot;: 3.6189999999999998,&#xd;
          &quot;total&quot;: 144.76,&#xd;
          &quot;taxable&quot;: 144.76&#xd;
        },&#xd;
        {&#xd;
          &quot;type&quot;: &quot;GST&quot;,&#xd;
          &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
          &quot;percentage&quot;: 2.5,&#xd;
          &quot;value&quot;: 3.6189999999999998,&#xd;
          &quot;total&quot;: 144.76,&#xd;
          &quot;taxable&quot;: 144.76&#xd;
        }&#xd;
      ],&#xd;
      &quot;tax&quot;: 7.2379999999999995,&#xd;
      &quot;originalTax&quot;: 7.2379999999999995&#xd;
    },&#xd;
    {&#xd;
      &quot;itemId&quot;: 2,&#xd;
      &quot;productId&quot;: 1201,&#xd;
      &quot;productName&quot;: &quot;Samosa Matar Chaat&quot;,&#xd;
      &quot;productType&quot;: 7,&#xd;
      &quot;billType&quot;: &quot;NET_PRICE&quot;,&#xd;
      &quot;addons&quot;: [&#xd;
        &#xd;
      ],&#xd;
      &quot;complimentaryDetail&quot;: {&#xd;
        &quot;isComplimentary&quot;: false,&#xd;
        &quot;reasonCode&quot;: null,&#xd;
        &quot;reason&quot;: null&#xd;
      },&#xd;
      &quot;quantity&quot;: 1,&#xd;
      &quot;price&quot;: 142.86,&#xd;
      &quot;amount&quot;: 142.86,&#xd;
      &quot;discountDetail&quot;: {&#xd;
        &quot;discountCode&quot;: null,&#xd;
        &quot;discountReason&quot;: null,&#xd;
        &quot;promotionalOffer&quot;: 0,&#xd;
        &quot;discount&quot;: {&#xd;
          &quot;percentage&quot;: 0,&#xd;
          &quot;value&quot;: 0&#xd;
        },&#xd;
        &quot;totalDiscount&quot;: 0&#xd;
      },&#xd;
      &quot;dimension&quot;: &quot;None&quot;,&#xd;
      &quot;hasBeenRedeemed&quot;: false,&#xd;
      &quot;isCombo&quot;: false,&#xd;
      &quot;recipeId&quot;: 664,&#xd;
      &quot;totalAmount&quot;: 142.86,&#xd;
      &quot;code&quot;: &quot;00009963&quot;,&#xd;
      &quot;taxes&quot;: [&#xd;
        {&#xd;
          &quot;type&quot;: &quot;GST&quot;,&#xd;
          &quot;code&quot;: &quot;CGST&quot;,&#xd;
          &quot;percentage&quot;: 2.5,&#xd;
          &quot;value&quot;: 3.5715000000000003,&#xd;
          &quot;total&quot;: 142.86,&#xd;
          &quot;taxable&quot;: 142.86&#xd;
        },&#xd;
        {&#xd;
          &quot;type&quot;: &quot;GST&quot;,&#xd;
          &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
          &quot;percentage&quot;: 2.5,&#xd;
          &quot;value&quot;: 3.5715000000000003,&#xd;
          &quot;total&quot;: 142.86,&#xd;
          &quot;taxable&quot;: 142.86&#xd;
        }&#xd;
      ],&#xd;
      &quot;tax&quot;: 7.143000000000001,&#xd;
      &quot;originalTax&quot;: 7.143000000000001&#xd;
    }&#xd;
  ],&#xd;
  &quot;transactionDetail&quot;: {&#xd;
    &quot;totalAmount&quot;: 287.62,&#xd;
    &quot;taxableAmount&quot;: 287.62,&#xd;
    &quot;savings&quot;: 0,&#xd;
    &quot;discountDetail&quot;: {&#xd;
      &quot;discount&quot;: {&#xd;
        &quot;percentage&quot;: 0,&#xd;
        &quot;value&quot;: 0,&#xd;
        &quot;wasValueSet&quot;: false&#xd;
      },&#xd;
      &quot;discountReason&quot;: null,&#xd;
      &quot;discountCode&quot;: null,&#xd;
      &quot;totalDiscount&quot;: 0,&#xd;
      &quot;promotionalOffer&quot;: 0&#xd;
    },&#xd;
    &quot;paidAmount&quot;: 302,&#xd;
    &quot;roundOffValue&quot;: -0.0009999999999763531,&#xd;
    &quot;tax&quot;: 14.381,&#xd;
    &quot;taxes&quot;: [&#xd;
      {&#xd;
        &quot;type&quot;: &quot;GST&quot;,&#xd;
        &quot;code&quot;: &quot;CGST&quot;,&#xd;
        &quot;percentage&quot;: 2.5,&#xd;
        &quot;value&quot;: 7.1905,&#xd;
        &quot;total&quot;: 287.62,&#xd;
        &quot;taxable&quot;: 287.62&#xd;
      },&#xd;
      {&#xd;
        &quot;type&quot;: &quot;GST&quot;,&#xd;
        &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
        &quot;percentage&quot;: 2.5,&#xd;
        &quot;value&quot;: 7.1905,&#xd;
        &quot;total&quot;: 287.62,&#xd;
        &quot;taxable&quot;: 287.62&#xd;
      }&#xd;
    ]&#xd;
  },&#xd;
  &quot;settlementType&quot;: &quot;DEBIT&quot;,&#xd;
  &quot;source&quot;: &quot;COD&quot;,&#xd;
  &quot;settlements&quot;: [&#xd;
    {&#xd;
      &quot;mode&quot;: 1,&#xd;
      &quot;amount&quot;: 302,&#xd;
      &quot;externalSettlements&quot;: null&#xd;
    }&#xd;
  ],&#xd;
  &quot;unitId&quot;: &quot;10002&quot;,&#xd;
  &quot;billStartTime&quot;: &quot;2019-01-04 13:00:43&quot;,&#xd;
  &quot;billCreationSeconds&quot;: 96,&#xd;
  &quot;billCreationTime&quot;: &quot;2019-01-04 13:02:19&quot;,&#xd;
  &quot;billingServerTime&quot;: &quot;2019-01-04 13:02:19&quot;,&#xd;
  &quot;channelPartner&quot;: 2,&#xd;
  &quot;deliveryPartner&quot;: 8,&#xd;
  &quot;pointsRedeemed&quot;: 0,&#xd;
  &quot;terminalId&quot;: 1,&#xd;
  &quot;tableNumber&quot;: null,&#xd;
  &quot;awardLoyalty&quot;: true,&#xd;
  &quot;offerCode&quot;: null,&#xd;
  &quot;subscriptionDetail&quot;: null,&#xd;
  &quot;customerId&quot;: 74,&#xd;
  &quot;customerName&quot;: null,&#xd;
  &quot;enquiryItems&quot;: [&#xd;
    {&#xd;
      &quot;id&quot;: 660,&#xd;
      &quot;name&quot;: &quot;Mom&apos;s Poha&quot;,&#xd;
      &quot;unitName&quot;: &quot;Galaxy  Park&quot;,&#xd;
      &quot;dimension&quot;: &quot;None&quot;,&#xd;
      &quot;orderedQuantity&quot;: 1,&#xd;
      &quot;availableQuantity&quot;: 0,&#xd;
      &quot;replacementServed&quot;: &quot;false&quot;,&#xd;
      &quot;linkedOrderId&quot;: null,&#xd;
      &quot;linkedCustomerId&quot;: 74,&#xd;
      &quot;linkedUnitId&quot;: &quot;10002&quot;&#xd;
    },&#xd;
    {&#xd;
      &quot;id&quot;: 1113,&#xd;
      &quot;name&quot;: &quot;Bun Samosa&quot;,&#xd;
      &quot;unitName&quot;: &quot;Galaxy It Park&quot;,&#xd;
      &quot;dimension&quot;: &quot;None&quot;,&#xd;
      &quot;orderedQuantity&quot;: 1,&#xd;
      &quot;availableQuantity&quot;: -3,&#xd;
      &quot;replacementServed&quot;: &quot;false&quot;,&#xd;
      &quot;linkedOrderId&quot;: null,&#xd;
      &quot;linkedCustomerId&quot;: 74,&#xd;
      &quot;linkedUnitId&quot;: &quot;10002&quot;&#xd;
    }&#xd;
  ],&#xd;
  &quot;newCustomer&quot;: true,&#xd;
  &quot;optionResultEventId&quot;: null,&#xd;
  &quot;orderType&quot;: &quot;order&quot;,&#xd;
  &quot;sourceId&quot;: 11001,&#xd;
  &quot;deliveryAddress&quot;: 3,&#xd;
  &quot;isGiftOrder&quot;: false,&#xd;
  &quot;metadataList&quot;: [&#xd;
    {&#xd;
      &quot;attributeName&quot;: &quot;SETTLEMENT_AMOUNT&quot;,&#xd;
      &quot;attributeValue&quot;: &quot;amount:302,bill:302,change:0&quot;&#xd;
    }&#xd;
  ]&#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain"></stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/kettle-service/rest/v1/order-management/order/create </stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <RandomVariableConfig guiclass="TestBeanGUI" testclass="RandomVariableConfig" testname="OrderID_Random_Generator" enabled="true">
            <stringProp name="TestPlan.comments">Variable for order ID</stringProp>
            <stringProp name="maximumValue">99999999</stringProp>
            <stringProp name="minimumValue">1</stringProp>
            <stringProp name="outputFormat">00000000</stringProp>
            <boolProp name="perThread">true</boolProp>
            <stringProp name="randomSeed">14000000</stringProp>
            <stringProp name="variableName">generateOrderId</stringProp>
          </RandomVariableConfig>
          <hashTree/>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HeaderManager_Login" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">auth-internal</stringProp>
                <stringProp name="Header.value">eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
        </hashTree>
        <ResultCollector guiclass="GraphVisualizer" testclass="ResultCollector" testname="Graph Results" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
          <boolProp name="ResultCollector.success_only_logging">true</boolProp>
        </ResultCollector>
        <hashTree/>
        <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="Response Assertion" enabled="true">
          <collectionProp name="Asserion.test_strings">
            <stringProp name="49586">200</stringProp>
            <stringProp name="0"></stringProp>
          </collectionProp>
          <stringProp name="Assertion.custom_message"></stringProp>
          <stringProp name="Assertion.test_field">Assertion.response_code</stringProp>
          <boolProp name="Assertion.assume_success">false</boolProp>
          <intProp name="Assertion.test_type">2</intProp>
        </ResponseAssertion>
        <hashTree/>
        <ResultCollector guiclass="AssertionVisualizer" testclass="ResultCollector" testname="Assertion Results" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="TableVisualizer" testclass="ResultCollector" testname="View Results in Table" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Create Order with NEO" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <intProp name="LoopController.loops">-1</intProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">1</stringProp>
        <stringProp name="ThreadGroup.ramp_time">0</stringProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration">1</stringProp>
        <stringProp name="ThreadGroup.delay">0</stringProp>
      </ThreadGroup>
      <hashTree>
        <ConfigTestElement guiclass="HttpDefaultsGui" testclass="ConfigTestElement" testname="NEO Defaults" enabled="true">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
            <collectionProp name="Arguments.arguments"/>
          </elementProp>
          <stringProp name="HTTPSampler.domain">dev-staging.chaayos.com</stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol">https</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path"></stringProp>
          <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </ConfigTestElement>
        <hashTree/>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get Device ID" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
  &quot;userAgent&quot;: &quot;Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36&quot;,&#xd;
  &quot;platform&quot;: &quot;Win32&quot;&#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain"></stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/neo-service/rest/v1/st/rd</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HeaderGetDeviceId" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="origin" elementType="Header">
                <stringProp name="Header.name">origin</stringProp>
                <stringProp name="Header.value">https://dev-staging.chaayos.com</stringProp>
              </elementProp>
              <elementProp name="accept-language" elementType="Header">
                <stringProp name="Header.name">accept-language</stringProp>
                <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
              </elementProp>
              <elementProp name="user-agent" elementType="Header">
                <stringProp name="Header.name">user-agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="content-type" elementType="Header">
                <stringProp name="Header.name">content-type</stringProp>
                <stringProp name="Header.value">application/json</stringProp>
              </elementProp>
              <elementProp name="accept" elementType="Header">
                <stringProp name="Header.name">accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="authority" elementType="Header">
                <stringProp name="Header.name">authority</stringProp>
                <stringProp name="Header.value">dev-staging.chaayos.com</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="deviceKeyExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames">deviceKey</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.deviceKey</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
        </hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Login_WebCustId" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
    &quot;otp&quot;: &quot;1234&quot;,&#xd;
    &quot;contact&quot;: &quot;${contact}&quot;,&#xd;
    &quot;deviceKey&quot;: &quot;${deviceKey}&quot;,&#xd;
    &quot;update&quot;: false&#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain"></stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/neo-service/rest/v1/c/lgn</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="Login RQ Headers" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="origin" elementType="Header">
                <stringProp name="Header.name">origin</stringProp>
                <stringProp name="Header.value">https://dev-staging.chaayos.com</stringProp>
              </elementProp>
              <elementProp name="accept-language" elementType="Header">
                <stringProp name="Header.name">accept-language</stringProp>
                <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
              </elementProp>
              <elementProp name="user-agent" elementType="Header">
                <stringProp name="Header.name">user-agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="content-type" elementType="Header">
                <stringProp name="Header.name">content-type</stringProp>
                <stringProp name="Header.value">application/json</stringProp>
              </elementProp>
              <elementProp name="accept" elementType="Header">
                <stringProp name="Header.name">accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="referer" elementType="Header">
                <stringProp name="Header.name">referer</stringProp>
                <stringProp name="Header.value">https://dev-staging.chaayos.com/login</stringProp>
              </elementProp>
              <elementProp name="authority" elementType="Header">
                <stringProp name="Header.name">authority</stringProp>
                <stringProp name="Header.value">dev-staging.chaayos.com</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <UserParameters guiclass="UserParametersGui" testclass="UserParameters" testname="User Parameters" enabled="true">
            <collectionProp name="UserParameters.names">
              <stringProp name="951526432">contact</stringProp>
              <stringProp name="0"></stringProp>
            </collectionProp>
            <collectionProp name="UserParameters.thread_values">
              <collectionProp name="-369385962">
                <stringProp name="1454283020">9557790639</stringProp>
                <stringProp name="0"></stringProp>
              </collectionProp>
            </collectionProp>
            <boolProp name="UserParameters.per_iteration">false</boolProp>
          </UserParameters>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="cartIdExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames">cartId</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.device.cartDetail.cartId</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="deviceIdExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames">deviceId</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.device.cartDetail.deviceId</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="CustomerIdExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames">customerId</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.device.cartDetail.customerId</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="sessionIdExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames">sessionId</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.device.cartDetail.sessionId</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="WebCustomerIdExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames">webCustomerId</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.device.cartDetail.orderDetail.webCustomerId</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
        </hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Create Order" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
    &quot;cartId&quot;: &quot;${cartId}&quot;,&#xd;
    &quot;deviceId&quot;: &quot;${deviceId}&quot;,&#xd;
    &quot;customerId&quot;: &quot;${customerId}&quot;,&#xd;
    &quot;sessionId&quot;: &quot;${sessionId}&quot;,&#xd;
    &quot;orderDetail&quot;: {&#xd;
        &quot;orderId&quot;: null,&#xd;
        &quot;generateOrderId&quot;: null,&#xd;
        &quot;externalOrderId&quot;: null,&#xd;
        &quot;unitOrderId&quot;: null,&#xd;
        &quot;campaignId&quot;: null,&#xd;
        &quot;customerId&quot;: null,&#xd;
        &quot;webCustomerId&quot;: &quot;${webCustomerId}&quot;,&#xd;
        &quot;employeeId&quot;: 0,&#xd;
        &quot;pointsRedeemed&quot;: 0,&#xd;
        &quot;source&quot;: &quot;COD&quot;,&#xd;
        &quot;sourceId&quot;: null,&#xd;
        &quot;hasParcel&quot;: false,&#xd;
        &quot;status&quot;: null,&#xd;
        &quot;orders&quot;: [&#xd;
            {&#xd;
                &quot;itemId&quot;: 1,&#xd;
                &quot;productId&quot;: 1114,&#xd;
                &quot;itemName&quot;: null,&#xd;
                &quot;customizationStrategy&quot;: 5,&#xd;
                &quot;productName&quot;: &quot;Shahi Chai&quot;,&#xd;
                &quot;productCategory&quot;: {&#xd;
                    &quot;id&quot;: 3623,&#xd;
                    &quot;name&quot;: null,&#xd;
                    &quot;code&quot;: null,&#xd;
                    &quot;shortCode&quot;: null,&#xd;
                    &quot;type&quot;: null,&#xd;
                    &quot;status&quot;: null&#xd;
                },&#xd;
                &quot;quantity&quot;: 1,&#xd;
                &quot;price&quot;: 247.62,&#xd;
                &quot;totalAmount&quot;: 247.62,&#xd;
                &quot;amount&quot;: 247.62,&#xd;
                &quot;discountDetail&quot;: {&#xd;
                    &quot;discountCode&quot;: EMP3500,&#xd;
                    &quot;discountReason&quot;: null,&#xd;
                    &quot;discount&quot;: {&#xd;
                        &quot;percentage&quot;: 100,&#xd;
                        &quot;value&quot;: 900&#xd;
                    },&#xd;
                    &quot;promotionalOffer&quot;: 0,&#xd;
                    &quot;totalDiscount&quot;: 0&#xd;
                },&#xd;
                &quot;complimentaryDetail&quot;: null,&#xd;
                &quot;addons&quot;: [],&#xd;
                &quot;dimension&quot;: &quot;ChotiKetli&quot;,&#xd;
                &quot;billType&quot;: &quot;NET_PRICE&quot;,&#xd;
                &quot;composition&quot;: {&#xd;
                    &quot;variants&quot;: [],&#xd;
                    &quot;products&quot;: [],&#xd;
                    &quot;addons&quot;: [],&#xd;
                    &quot;menuProducts&quot;: []&#xd;
                },&#xd;
                &quot;recipeId&quot;: 494,&#xd;
                &quot;itemCode&quot;: null,&#xd;
                &quot;tax&quot;: &quot;12.3810&quot;,&#xd;
                &quot;code&quot;: &quot;00009963&quot;,&#xd;
                &quot;taxes&quot;: [&#xd;
                    {&#xd;
                        &quot;type&quot;: &quot;GST&quot;,&#xd;
                        &quot;code&quot;: &quot;CGST&quot;,&#xd;
                        &quot;percentage&quot;: 2.5,&#xd;
                        &quot;value&quot;: &quot;6.1905&quot;,&#xd;
                        &quot;total&quot;: 247.62,&#xd;
                        &quot;taxable&quot;: 247.62&#xd;
                    },&#xd;
                    {&#xd;
                        &quot;type&quot;: &quot;GST&quot;,&#xd;
                        &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
                        &quot;percentage&quot;: 2.5,&#xd;
                        &quot;value&quot;: &quot;6.1905&quot;,&#xd;
                        &quot;total&quot;: 247.62,&#xd;
                        &quot;taxable&quot;: 247.62&#xd;
                    }&#xd;
                ],&#xd;
                &quot;originalTax&quot;: &quot;12.3810&quot;&#xd;
            },&#xd;
            {&#xd;
                &quot;itemId&quot;: 2,&#xd;
                &quot;productId&quot;: 1109,&#xd;
                &quot;itemName&quot;: null,&#xd;
                &quot;customizationStrategy&quot;: 5,&#xd;
                &quot;productName&quot;: &quot;Baarish Wale Pakore&quot;,&#xd;
                &quot;productCategory&quot;: {&#xd;
                    &quot;id&quot;: 3628,&#xd;
                    &quot;name&quot;: null,&#xd;
                    &quot;code&quot;: null,&#xd;
                    &quot;shortCode&quot;: null,&#xd;
                    &quot;type&quot;: null,&#xd;
                    &quot;status&quot;: null&#xd;
                },&#xd;
                &quot;quantity&quot;: 1,&#xd;
                &quot;price&quot;: 190.48,&#xd;
                &quot;totalAmount&quot;: 190.48,&#xd;
                &quot;amount&quot;: 190.48,&#xd;
                &quot;discountDetail&quot;: {&#xd;
                    &quot;discountCode&quot;: null,&#xd;
                    &quot;discountReason&quot;: null,&#xd;
                    &quot;discount&quot;: {&#xd;
                        &quot;percentage&quot;: 0,&#xd;
                        &quot;value&quot;: 0&#xd;
                    },&#xd;
                    &quot;promotionalOffer&quot;: 0,&#xd;
                    &quot;totalDiscount&quot;: 0&#xd;
                },&#xd;
                &quot;complimentaryDetail&quot;: null,&#xd;
                &quot;addons&quot;: [],&#xd;
                &quot;dimension&quot;: &quot;None&quot;,&#xd;
                &quot;billType&quot;: &quot;NET_PRICE&quot;,&#xd;
                &quot;composition&quot;: {&#xd;
                    &quot;variants&quot;: [],&#xd;
                    &quot;products&quot;: [],&#xd;
                    &quot;addons&quot;: [],&#xd;
                    &quot;menuProducts&quot;: []&#xd;
                },&#xd;
                &quot;recipeId&quot;: 430,&#xd;
                &quot;itemCode&quot;: null,&#xd;
                &quot;tax&quot;: &quot;9.5240&quot;,&#xd;
                &quot;code&quot;: &quot;00009963&quot;,&#xd;
                &quot;taxes&quot;: [&#xd;
                    {&#xd;
                        &quot;type&quot;: &quot;GST&quot;,&#xd;
                        &quot;code&quot;: &quot;CGST&quot;,&#xd;
                        &quot;percentage&quot;: 2.5,&#xd;
                        &quot;value&quot;: &quot;4.7620&quot;,&#xd;
                        &quot;total&quot;: 190.48,&#xd;
                        &quot;taxable&quot;: 190.48&#xd;
                    },&#xd;
                    {&#xd;
                        &quot;type&quot;: &quot;GST&quot;,&#xd;
                        &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
                        &quot;percentage&quot;: 2.5,&#xd;
                        &quot;value&quot;: &quot;4.7620&quot;,&#xd;
                        &quot;total&quot;: 190.48,&#xd;
                        &quot;taxable&quot;: 190.48&#xd;
                    }&#xd;
                ],&#xd;
                &quot;originalTax&quot;: &quot;9.5240&quot;&#xd;
            },&#xd;
            {&#xd;
                &quot;itemId&quot;: 3,&#xd;
                &quot;productId&quot;: 1043,&#xd;
                &quot;itemName&quot;: null,&#xd;
                &quot;customizationStrategy&quot;: 0,&#xd;
                &quot;productName&quot;: &quot;Packaging Charges&quot;,&#xd;
                &quot;productCategory&quot;: null,&#xd;
                &quot;quantity&quot;: 1,&#xd;
                &quot;price&quot;: 30,&#xd;
                &quot;totalAmount&quot;: 30,&#xd;
                &quot;amount&quot;: 30,&#xd;
                &quot;discountDetail&quot;: {&#xd;
                    &quot;discountCode&quot;: null,&#xd;
                    &quot;discountReason&quot;: null,&#xd;
                    &quot;discount&quot;: {&#xd;
                        &quot;percentage&quot;: 0,&#xd;
                        &quot;value&quot;: 0&#xd;
                    },&#xd;
                    &quot;promotionalOffer&quot;: 0,&#xd;
                    &quot;totalDiscount&quot;: 0&#xd;
                },&#xd;
                &quot;complimentaryDetail&quot;: null,&#xd;
                &quot;addons&quot;: [],&#xd;
                &quot;dimension&quot;: &quot;None&quot;,&#xd;
                &quot;billType&quot;: &quot;NET_PRICE&quot;,&#xd;
                &quot;composition&quot;: {&#xd;
                    &quot;variants&quot;: [],&#xd;
                    &quot;products&quot;: [],&#xd;
                    &quot;addons&quot;: [],&#xd;
                    &quot;menuProducts&quot;: []&#xd;
                },&#xd;
                &quot;recipeId&quot;: 0,&#xd;
                &quot;itemCode&quot;: null,&#xd;
                &quot;tax&quot;: &quot;1.5000&quot;,&#xd;
                &quot;code&quot;: &quot;00009963&quot;,&#xd;
                &quot;taxes&quot;: [&#xd;
                    {&#xd;
                        &quot;type&quot;: &quot;GST&quot;,&#xd;
                        &quot;code&quot;: &quot;CGST&quot;,&#xd;
                        &quot;percentage&quot;: 2.5,&#xd;
                        &quot;value&quot;: &quot;0.7500&quot;,&#xd;
                        &quot;total&quot;: 30,&#xd;
                        &quot;taxable&quot;: 30&#xd;
                    },&#xd;
                    {&#xd;
                        &quot;type&quot;: &quot;GST&quot;,&#xd;
                        &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
                        &quot;percentage&quot;: 2.5,&#xd;
                        &quot;value&quot;: &quot;0.7500&quot;,&#xd;
                        &quot;total&quot;: 30,&#xd;
                        &quot;taxable&quot;: 30&#xd;
                    }&#xd;
                ],&#xd;
                &quot;originalTax&quot;: &quot;1.5000&quot;&#xd;
            }&#xd;
        ],&#xd;
        &quot;enquiryItems&quot;: [],&#xd;
        &quot;transactionDetail&quot;: {&#xd;
            &quot;totalAmount&quot;: &quot;468.1000&quot;,&#xd;
            &quot;taxableAmount&quot;: &quot;468.1000&quot;,&#xd;
            &quot;savings&quot;: 0,&#xd;
            &quot;discountDetail&quot;: {&#xd;
                &quot;discount&quot;: {&#xd;
                    &quot;percentage&quot;: 0,&#xd;
                    &quot;value&quot;: 0,&#xd;
                    &quot;wasValueSet&quot;: false&#xd;
                },&#xd;
                &quot;discountReason&quot;: null,&#xd;
                &quot;discountCode&quot;: null,&#xd;
                &quot;totalDiscount&quot;: &quot;0.0000&quot;,&#xd;
                &quot;promotionalOffer&quot;: 0&#xd;
            },&#xd;
            &quot;tax&quot;: &quot;23.4050&quot;,&#xd;
            &quot;taxes&quot;: [&#xd;
                {&#xd;
                    &quot;type&quot;: &quot;GST&quot;,&#xd;
                    &quot;code&quot;: &quot;CGST&quot;,&#xd;
                    &quot;percentage&quot;: 2.5,&#xd;
                    &quot;value&quot;: &quot;11.7025&quot;,&#xd;
                    &quot;total&quot;: 468.1,&#xd;
                    &quot;taxable&quot;: 468.1&#xd;
                },&#xd;
                {&#xd;
                    &quot;type&quot;: &quot;GST&quot;,&#xd;
                    &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
                    &quot;percentage&quot;: 2.5,&#xd;
                    &quot;value&quot;: &quot;11.7025&quot;,&#xd;
                    &quot;total&quot;: 468.1,&#xd;
                    &quot;taxable&quot;: 468.1&#xd;
                }&#xd;
            ],&#xd;
            &quot;paidAmount&quot;: 492,&#xd;
            &quot;roundOffValue&quot;: &quot;0.4950&quot;&#xd;
        },&#xd;
        &quot;settlementType&quot;: &quot;DEBIT&quot;,&#xd;
        &quot;settlements&quot;: [&#xd;
            {&#xd;
                &quot;mode&quot;: 1,&#xd;
                &quot;amount&quot;: 492&#xd;
            }&#xd;
        ],&#xd;
        &quot;unitId&quot;: 10002,&#xd;
        &quot;unitName&quot;: &quot;Galaxy It Park&quot;,&#xd;
        &quot;terminalId&quot;: 786,&#xd;
        &quot;billStartTime&quot;: null,&#xd;
        &quot;billCreationTime&quot;: null,&#xd;
        &quot;billCreationSeconds&quot;: 0,&#xd;
        &quot;billingServerTime&quot;: null,&#xd;
        &quot;channelPartner&quot;: 0,&#xd;
        &quot;deliveryPartner&quot;: 0,&#xd;
        &quot;offerCode&quot;: null,&#xd;
        &quot;cancellationDetails&quot;: null,&#xd;
        &quot;orderRemark&quot;: null,&#xd;
        &quot;deliveryAddress&quot;: 3,&#xd;
        &quot;address&quot;: null,&#xd;
        &quot;customerName&quot;: &quot;abhinav&quot;,&#xd;
        &quot;containsSignupOffer&quot;: null,&#xd;
        &quot;tempCode&quot;: null,&#xd;
        &quot;metadataList&quot;: [],&#xd;
        &quot;tokenNumber&quot;: null,&#xd;
        &quot;stateId&quot;: 28&#xd;
    },&#xd;
    &quot;creationTime&quot;: 1546599627172,&#xd;
    &quot;checkoutTime&quot;: null,&#xd;
    &quot;cartStatus&quot;: &quot;CREATED&quot;,&#xd;
    &quot;interState&quot;: false,&#xd;
    &quot;currentStateId&quot;: 28&#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain"></stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/neo-service/rest/v1/wcrt/ckt</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="origin" elementType="Header">
                <stringProp name="Header.name">origin</stringProp>
                <stringProp name="Header.value">https://dev-staging.chaayos.com</stringProp>
              </elementProp>
              <elementProp name="accept-encoding" elementType="Header">
                <stringProp name="Header.name">accept-encoding</stringProp>
                <stringProp name="Header.value">gzip, deflate, br</stringProp>
              </elementProp>
              <elementProp name="accept-language" elementType="Header">
                <stringProp name="Header.name">accept-language</stringProp>
                <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
              </elementProp>
              <elementProp name="user-agent" elementType="Header">
                <stringProp name="Header.name">user-agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="content-type" elementType="Header">
                <stringProp name="Header.name">content-type</stringProp>
                <stringProp name="Header.value">application/json</stringProp>
              </elementProp>
              <elementProp name="accept" elementType="Header">
                <stringProp name="Header.name">accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="//referer" elementType="Header">
                <stringProp name="Header.name">//referer</stringProp>
                <stringProp name="Header.value">https://dev-staging.chaayos.com/payProcess</stringProp>
              </elementProp>
              <elementProp name="authority" elementType="Header">
                <stringProp name="Header.name">authority</stringProp>
                <stringProp name="Header.value">dev-staging.chaayos.com</stringProp>
              </elementProp>
              <elementProp name="//cookie" elementType="Header">
                <stringProp name="Header.name">//cookie</stringProp>
                <stringProp name="Header.value">__cfduid=dfa789694f8b2780100021ec3c135a6601546599623; _ga=GA1.3.447515785.1546599628; _gid=GA1.3.20298254.1546599628; clmd={&quot;criteria&quot;:&quot;DELIVERY&quot;,&quot;city&quot;:&quot;Noida&quot;,&quot;state&quot;:28,&quot;locality&quot;:{&quot;value&quot;:10,&quot;label&quot;:&quot;Sector 62&quot;},&quot;outlet&quot;:null}; cud={&quot;id&quot;:10002,&quot;name&quot;:&quot;Galaxy It Park&quot;}; dpci={&quot;delivery&quot;:{&quot;id&quot;:1044,&quot;name&quot;:&quot;Delivery Charges&quot;,&quot;price&quot;:40,&quot;dimension&quot;:&quot;None&quot;,&quot;billType&quot;:&quot;MRP&quot;,&quot;taxCode&quot;:&quot;00009963&quot;},&quot;packaging&quot;:{&quot;id&quot;:1043,&quot;name&quot;:&quot;Packaging Charges&quot;,&quot;price&quot;:30,&quot;dimension&quot;:&quot;None&quot;,&quot;billType&quot;:&quot;NET_PRICE&quot;,&quot;taxCode&quot;:&quot;00009963&quot;}}; cad={&quot;deviceKey&quot;:&quot;5c2f3ccbe4b046fa4c7068a0:1546599679872&quot;,&quot;sessionKey&quot;:&quot;5c2f3dc6e4b046fa4c7068a3#5c2f3ccbe4b046fa4c7068a0#1546599878047#5c2f3dc2e4b046fa4c7068a2#9879879878&quot;,&quot;cartDetail&quot;:null}; ccsd={&quot;contact&quot;:&quot;9879879878&quot;,&quot;name&quot;:&quot;abhinav&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;loyalty&quot;:20}; _gat_UA-96852049-1=1</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="generateOrderIdExtractor" enabled="true">
            <stringProp name="JSONPostProcessor.referenceNames"></stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
          </JSONPostProcessor>
          <hashTree/>
        </hashTree>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="TableVisualizer" testclass="ResultCollector" testname="View Results in Table" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatGraphVisualizer" testclass="ResultCollector" testname="Aggregate Graph" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Create Order POS" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">100</stringProp>
        <stringProp name="ThreadGroup.ramp_time">1</stringProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <ConfigTestElement guiclass="HttpDefaultsGui" testclass="ConfigTestElement" testname="HTTP Request Defaults" enabled="true">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value"></stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">dev.kettle.chaayos.com</stringProp>
          <stringProp name="HTTPSampler.port">9595</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path"></stringProp>
          <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </ConfigTestElement>
        <hashTree/>
        <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HeaderManager_Login" enabled="true">
          <collectionProp name="HeaderManager.headers">
            <elementProp name="" elementType="Header">
              <stringProp name="Header.name">Content-Type</stringProp>
              <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
            </elementProp>
            <elementProp name="" elementType="Header">
              <stringProp name="Header.name">auth-internal</stringProp>
              <stringProp name="Header.value">eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw</stringProp>
            </elementProp>
          </collectionProp>
        </HeaderManager>
        <hashTree/>
        <CSVDataSet guiclass="TestBeanGUI" testclass="CSVDataSet" testname="CSV Data Set Config" enabled="true">
          <stringProp name="filename">D:/Load Test/orderID.csv</stringProp>
          <stringProp name="fileEncoding">UTF-8</stringProp>
          <stringProp name="variableNames">generateOrderId</stringProp>
          <boolProp name="ignoreFirstLine">false</boolProp>
          <stringProp name="delimiter">,</stringProp>
          <boolProp name="quotedData">false</boolProp>
          <boolProp name="recycle">true</boolProp>
          <boolProp name="stopThread">false</boolProp>
          <stringProp name="shareMode">Create Order from cafe counter</stringProp>
        </CSVDataSet>
        <hashTree/>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Create Order from cafe counter" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
  &quot;generateOrderId&quot;: ${generateOrderId},&#xd;
  &quot;employeeId&quot;: 120057,&#xd;
  &quot;hasParcel&quot;: false,&#xd;
  &quot;status&quot;: &quot;CREATED&quot;,&#xd;
  &quot;cancelReason&quot;: null,&#xd;
  &quot;orders&quot;: [&#xd;
    {&#xd;
      &quot;itemId&quot;: 1,&#xd;
      &quot;productId&quot;: 1114,&#xd;
      &quot;productName&quot;: &quot;Shahi Chai&quot;,&#xd;
      &quot;productType&quot;: 5,&#xd;
      &quot;billType&quot;: &quot;NET_PRICE&quot;,&#xd;
      &quot;addons&quot;: [&#xd;
        &#xd;
      ],&#xd;
      &quot;complimentaryDetail&quot;: {&#xd;
        &quot;isComplimentary&quot;: false,&#xd;
        &quot;reasonCode&quot;: null,&#xd;
        &quot;reason&quot;: null&#xd;
      },&#xd;
      &quot;quantity&quot;: 1,&#xd;
      &quot;price&quot;: 123.81,&#xd;
      &quot;amount&quot;: 123.81,&#xd;
      &quot;discountDetail&quot;: {&#xd;
        &quot;discountCode&quot;: null,&#xd;
        &quot;discountReason&quot;: null,&#xd;
        &quot;promotionalOffer&quot;: 0,&#xd;
        &quot;discount&quot;: {&#xd;
          &quot;percentage&quot;: 0,&#xd;
          &quot;value&quot;: 0&#xd;
        },&#xd;
        &quot;totalDiscount&quot;: 0&#xd;
      },&#xd;
      &quot;dimension&quot;: &quot;None&quot;,&#xd;
      &quot;hasBeenRedeemed&quot;: false,&#xd;
      &quot;isCombo&quot;: false,&#xd;
      &quot;composition&quot;: {&#xd;
        &quot;variants&quot;: [&#xd;
          &#xd;
        ],&#xd;
        &quot;products&quot;: [&#xd;
          &#xd;
        ],&#xd;
        &quot;menuProducts&quot;: [&#xd;
          &#xd;
        ],&#xd;
        &quot;addons&quot;: [&#xd;
          &#xd;
        ],&#xd;
        &quot;options&quot;: [&#xd;
          &#xd;
        ]&#xd;
      },&#xd;
      &quot;recipeId&quot;: 493,&#xd;
      &quot;totalAmount&quot;: 123.81,&#xd;
      &quot;code&quot;: &quot;00009963&quot;,&#xd;
      &quot;taxes&quot;: [&#xd;
        {&#xd;
          &quot;type&quot;: &quot;GST&quot;,&#xd;
          &quot;code&quot;: &quot;CGST&quot;,&#xd;
          &quot;percentage&quot;: 2.5,&#xd;
          &quot;value&quot;: 3.0952499999999996,&#xd;
          &quot;total&quot;: 123.81,&#xd;
          &quot;taxable&quot;: 123.81&#xd;
        },&#xd;
        {&#xd;
          &quot;type&quot;: &quot;GST&quot;,&#xd;
          &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
          &quot;percentage&quot;: 2.5,&#xd;
          &quot;value&quot;: 3.0952499999999996,&#xd;
          &quot;total&quot;: 123.81,&#xd;
          &quot;taxable&quot;: 123.81&#xd;
        }&#xd;
      ],&#xd;
      &quot;tax&quot;: 6.190499999999999,&#xd;
      &quot;originalTax&quot;: 6.190499999999999&#xd;
    }&#xd;
  ],&#xd;
  &quot;transactionDetail&quot;: {&#xd;
    &quot;totalAmount&quot;: 123.81,&#xd;
    &quot;taxableAmount&quot;: 123.81,&#xd;
    &quot;savings&quot;: 0,&#xd;
    &quot;discountDetail&quot;: {&#xd;
      &quot;discount&quot;: {&#xd;
        &quot;percentage&quot;: 0,&#xd;
        &quot;value&quot;: 0,&#xd;
        &quot;wasValueSet&quot;: false&#xd;
      },&#xd;
      &quot;discountReason&quot;: null,&#xd;
      &quot;discountCode&quot;: null,&#xd;
      &quot;totalDiscount&quot;: 0,&#xd;
      &quot;promotionalOffer&quot;: 0&#xd;
    },&#xd;
    &quot;paidAmount&quot;: 130,&#xd;
    &quot;roundOffValue&quot;: -0.0004999999999881766,&#xd;
    &quot;tax&quot;: 6.190499999999999,&#xd;
    &quot;taxes&quot;: [&#xd;
      {&#xd;
        &quot;type&quot;: &quot;GST&quot;,&#xd;
        &quot;code&quot;: &quot;CGST&quot;,&#xd;
        &quot;percentage&quot;: 2.5,&#xd;
        &quot;value&quot;: 3.0952499999999996,&#xd;
        &quot;total&quot;: 123.81,&#xd;
        &quot;taxable&quot;: 123.81&#xd;
      },&#xd;
      {&#xd;
        &quot;type&quot;: &quot;GST&quot;,&#xd;
        &quot;code&quot;: &quot;SGST/UTGST&quot;,&#xd;
        &quot;percentage&quot;: 2.5,&#xd;
        &quot;value&quot;: 3.0952499999999996,&#xd;
        &quot;total&quot;: 123.81,&#xd;
        &quot;taxable&quot;: 123.81&#xd;
      }&#xd;
    ]&#xd;
  },&#xd;
  &quot;settlementType&quot;: &quot;DEBIT&quot;,&#xd;
  &quot;source&quot;: &quot;CAFE&quot;,&#xd;
  &quot;settlements&quot;: [&#xd;
    {&#xd;
      &quot;mode&quot;: 2,&#xd;
      &quot;amount&quot;: 130,&#xd;
      &quot;externalSettlements&quot;: null&#xd;
    }&#xd;
  ],&#xd;
  &quot;unitId&quot;: 12014,&#xd;
  &quot;billStartTime&quot;: &quot;2018-12-27 15:24:25&quot;,&#xd;
  &quot;billCreationSeconds&quot;: 263,&#xd;
  &quot;billCreationTime&quot;: &quot;2018-12-27 15:28:48&quot;,&#xd;
  &quot;billingServerTime&quot;: &quot;2018-12-27 15:28:48&quot;,&#xd;
  &quot;channelPartner&quot;: 1,&#xd;
  &quot;deliveryPartner&quot;: null,&#xd;
  &quot;pointsRedeemed&quot;: 0,&#xd;
  &quot;terminalId&quot;: 1,&#xd;
  &quot;tableNumber&quot;: null,&#xd;
  &quot;awardLoyalty&quot;: true,&#xd;
  &quot;offerCode&quot;: null,&#xd;
  &quot;subscriptionDetail&quot;: null,&#xd;
  &quot;customerId&quot;: null,&#xd;
  &quot;customerName&quot;: &quot;abhinav&quot;,&#xd;
  &quot;enquiryItems&quot;: [&#xd;
    &#xd;
  ],&#xd;
  &quot;metadataList&quot;: [&#xd;
    {&#xd;
      &quot;attributeName&quot;: &quot;CUSTOMER_SCREEN_DOWN&quot;,&#xd;
      &quot;attributeValue&quot;: &quot;2018-12-27T15:28:22+05:30&quot;&#xd;
    },&#xd;
    {&#xd;
      &quot;attributeName&quot;: &quot;SETTLEMENT_MODEL_OPEN&quot;,&#xd;
      &quot;attributeValue&quot;: &quot;2018-12-27T15:28:22+05:30&quot;&#xd;
    },&#xd;
    {&#xd;
      &quot;attributeName&quot;: &quot;CUSTOMER_NAME_MANUALLY&quot;,&#xd;
      &quot;attributeValue&quot;: &quot;abhinav&quot;&#xd;
    },&#xd;
    {&#xd;
      &quot;attributeName&quot;: &quot;SETTLEMENT_AMOUNT&quot;,&#xd;
      &quot;attributeValue&quot;: &quot;amount:130,bill:130,change:0&quot;&#xd;
    }&#xd;
  ],&#xd;
  &quot;newCustomer&quot;: false,&#xd;
  &quot;optionResultEventId&quot;: null,&#xd;
  &quot;orderType&quot;: &quot;order&quot;,&#xd;
  &quot;isGiftOrder&quot;: false&#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain"></stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/kettle-service/rest/v1/order-management/order/create</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">D:\Load Test\ViewResults_CO_POS</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="Aggregate Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">D:\Load Test\Summar_CO_POS</stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="GraphVisualizer" testclass="ResultCollector" testname="Graph Results" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
