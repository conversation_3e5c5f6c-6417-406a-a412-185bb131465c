{"info": {"_postman_id": "16710d13-303b-434e-a9e7-8f5e04f99a6f", "name": "Major Use Cases", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "COD_Create_Order", "item": [{"name": "COD_createOrder", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A11001%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%223Nx3WsUVeHQOTjOpsU%2BiXgxR332d11LQTuxut5yEpKHDR75fuh6MxXU%2FphoiyofKNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22COD%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiM054M1dzVVZlSFFPVGpPcHNVK2lYZ3hSMzMyZDExTFFUdXh1dDV5RXBLSERSNzVmdWg2TXhYVS9waG9peW9mS05MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjExMDAxLCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ2NDk1MzAxLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.wTWKMXm6U2-3kus67RUMj2n630S3e-y2ODoQeVUyMGI%22%7D%7D; __cfduid=d0ca04862265cece4ad4b38e6a8298b1b1546437680"}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth-internal", "value": "eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"13913191\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n    {\r\n      \"itemId\": 1,\r\n      \"productId\": 11,\r\n      \"productName\": \"Full Doodh\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"reasonCode\": null,\r\n        \"isComplimentary\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 2,\r\n      \"price\": 144.76,\r\n      \"amount\": 289.52,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"ChotiKetli\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          {\r\n            \"productId\": 100330,\r\n            \"alias\": \"Regular Sugar\",\r\n            \"uom\": \"KG\",\r\n            \"quantity\": 0.01051,\r\n            \"captured\": true,\r\n            \"defaultSetting\": true,\r\n            \"selected\": true\r\n          },\r\n          {\r\n            \"productId\": 100123,\r\n            \"alias\": \"Regular Patti\",\r\n            \"uom\": \"KG\",\r\n            \"quantity\": 0.00638,\r\n            \"captured\": true,\r\n            \"defaultSetting\": true,\r\n            \"selected\": true\r\n          }\r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 802,\r\n      \"totalAmount\": 289.52,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 7.2379999999999995,\r\n          \"total\": 289.52,\r\n          \"taxable\": 289.52\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 7.2379999999999995,\r\n          \"total\": 289.52,\r\n          \"taxable\": 289.52\r\n        }\r\n      ],\r\n      \"tax\": 14.475999999999999,\r\n      \"originalTax\": 14.475999999999999\r\n    },\r\n    {\r\n      \"itemId\": 2,\r\n      \"productId\": 271,\r\n      \"productName\": \"Desi Filter Coffee\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 228.57,\r\n      \"amount\": 228.57,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"ChotiKetli\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 130,\r\n      \"totalAmount\": 228.57,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 5.71425,\r\n          \"total\": 228.57,\r\n          \"taxable\": 228.57\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 5.71425,\r\n          \"total\": 228.57,\r\n          \"taxable\": 228.57\r\n        }\r\n      ],\r\n      \"tax\": 11.4285,\r\n      \"originalTax\": 11.4285\r\n    },\r\n    {\r\n      \"itemId\": 3,\r\n      \"productId\": 170,\r\n      \"productName\": \"Green Tea\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 163.81,\r\n      \"amount\": 163.81,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"ChotiKetli\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 813,\r\n      \"totalAmount\": 163.81,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.09525,\r\n          \"total\": 163.81,\r\n          \"taxable\": 163.81\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.09525,\r\n          \"total\": 163.81,\r\n          \"taxable\": 163.81\r\n        }\r\n      ],\r\n      \"tax\": 8.1905,\r\n      \"originalTax\": 8.1905\r\n    },\r\n    {\r\n      \"itemId\": 4,\r\n      \"productId\": 1114,\r\n      \"productName\": \"Shahi Chai\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 247.62,\r\n      \"amount\": 247.62,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"ChotiKetli\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 494,\r\n      \"totalAmount\": 247.62,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 6.190499999999999,\r\n          \"total\": 247.62,\r\n          \"taxable\": 247.62\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 6.190499999999999,\r\n          \"total\": 247.62,\r\n          \"taxable\": 247.62\r\n        }\r\n      ],\r\n      \"tax\": 12.380999999999998,\r\n      \"originalTax\": 12.380999999999998\r\n    }\r\n  ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 929.5199999999999,\r\n    \"taxableAmount\": 929.5199999999999,\r\n    \"savings\": 0,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 0,\r\n        \"value\": 0,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": null,\r\n      \"discountCode\": null,\r\n      \"totalDiscount\": 0,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 976,\r\n    \"roundOffValue\": 0.004000000000019099,\r\n    \"tax\": 46.476,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 23.238,\r\n        \"total\": 929.5199999999999,\r\n        \"taxable\": 929.5199999999999\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 23.238,\r\n        \"total\": 929.5199999999999,\r\n        \"taxable\": 929.5199999999999\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"COD\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 1,\r\n      \"amount\": 976,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": \"26055\",\r\n  \"billStartTime\": \"2019-01-03 11:32:51\",\r\n  \"billCreationSeconds\": 49,\r\n  \"billCreationTime\": \"2019-01-03 11:33:40\",\r\n  \"billingServerTime\": \"2019-01-03 11:33:40\",\r\n  \"channelPartner\": 2,\r\n  \"deliveryPartner\": 8,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": null,\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": 71,\r\n  \"customerName\": null,\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"newCustomer\": true,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"sourceId\": 11001,\r\n  \"deliveryAddress\": 29,\r\n  \"isGiftOrder\": false,\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:976,bill:976,change:0\"\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}}, "response": []}], "description": "API to create order from COD i.e. call center"}, {"name": "NEO", "item": [{"name": "1. Check order with coupon code", "item": [{"name": "Device StampRD Copy", "event": [{"listen": "test", "script": {"id": "197de2ea-d732-4177-a054-1a52785eada8", "exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"deviceKey\", jsonData.deviceKey);", "console.log(pm.environment.get(\"deviceKey\"));", "", "//verifying the device has been stamped or not", "", "pm.test(\"Device Key Generated\",function()", "{", "    pm.expect(pm.response.text()).to.include(jsonData.deviceKey);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "authority", "value": "dev-staging.chaayos.com", "disabled": true}], "body": {"mode": "raw", "raw": "{\"userAgent\":\"Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36\",\"platform\":\"Win32\"}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/st/rd", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "st", "rd"]}, "description": "https://dev-staging.chaayos.com/neo-service/rest/v1/st/rd"}, "response": []}, {"name": "Get WEB Cus ID Login Copy", "event": [{"listen": "test", "script": {"id": "463a4b8a-eeaf-4bbd-b6dd-5a21a2b5f652", "exec": ["//verifying status code", "", "pm.test(\"Status code is 200\", function()", "{", "    pm.response.to.have.status(200);", "});", "//setting sessionKey", "var jsonData = pm.response.json();", "pm.environment.set(\"sessionKey\", jsonData.device.sessionKey);", "pm.environment.set(\"cartId\", jsonData.device.cartDetail.cartId);", "pm.environment.set(\"deviceId\", jsonData.device.cartDetail.deviceId);", "pm.environment.set(\"customerId\", jsonData.device.cartDetail.customerId);", "pm.environment.set(\"webCustomerId\", jsonData.device.cartDetail.orderDetail.webCustomerId);", "console.log(pm.environment.get(\"deviceId\", \"cartId\", \"webCustomerId\", \"sessionKey\"));", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-encoding", "value": "gzip, deflate, br", "disabled": true}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "referer", "value": "https://dev-staging.chaayos.com/login"}, {"key": "authority", "value": "dev-staging.chaayos.com"}, {"key": "cookie", "value": "__cfduid=d36efc9c66c1ee526a5545f44ec28b8281546842225; cad={\"deviceKey\":\"5c32f072e4b08839f95cde53:1546842226457\",\"sessionKey\":null,\"cartDetail\":null}; clmd={\"criteria\":\"DELIVERY\",\"city\":\"Noida\",\"state\":28,\"locality\":{\"value\":10,\"label\":\"Sector 62\"},\"outlet\":null}; _ga=GA1.3.46698243.1546842261; _gid=GA1.3.1514214065.1546842261; cud={\"id\":10002,\"name\":\"Galaxy It Park\"}; dpci={\"delivery\":{\"id\":1044,\"name\":\"Delivery Charges\",\"price\":40,\"dimension\":\"None\",\"billType\":\"MRP\",\"taxCode\":\"00009963\"},\"packaging\":{\"id\":1043,\"name\":\"Packaging Charges\",\"price\":30,\"dimension\":\"None\",\"billType\":\"NET_PRICE\",\"taxCode\":\"00009963\"}}; ccsd={\"contact\":\"9557790639\"}"}], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"1234\",\n    \"contact\": \"9557790639\",\n    \"deviceKey\": \"{{deviceKey}}\",\n    \"update\": false\n}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/c/lgn", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "c", "lgn"]}}, "response": []}, {"name": "Apply Coupon", "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-encoding", "value": "gzip, deflate, br", "disabled": true}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "referer", "value": "https://dev-staging.chaayos.com/cart"}, {"key": "authority", "value": "dev-staging.chaayos.com"}, {"key": "cookie", "value": "__cfduid=d3ea0c6391e990b8862ba3dd14642fafd1546923778; _ga=GA1.3.10536586.1546923782; _gid=GA1.3.1506850970.1546923782; clmd={\"criteria\":\"DELIVERY\",\"city\":\"Noida\",\"state\":28,\"locality\":{\"value\":10,\"label\":\"Sector 62\"},\"outlet\":null}; cud={\"id\":10002,\"name\":\"Galaxy It Park\"}; dpci={\"delivery\":{\"id\":1044,\"name\":\"Delivery Charges\",\"price\":40,\"dimension\":\"None\",\"billType\":\"MRP\",\"taxCode\":\"00009963\"},\"packaging\":{\"id\":1043,\"name\":\"Packaging Charges\",\"price\":30,\"dimension\":\"None\",\"billType\":\"NET_PRICE\",\"taxCode\":\"00009963\"}}; cco={\"id\":\"2734284249431614\",\"type\":\"GEN\"}; _fbp=fb.1.1546929190757.1252101462; _ga=GA1.2.1922616713.1546929198; _gid=GA1.2.*********.1546929198; cad={\"deviceKey\":\"5c342f06e4b03e6882ae2a12:1546931061524\",\"sessionKey\":\"5c344b19e4b03e6882ae2a53#5c342f06e4b03e6882ae2a12#1546930969734#5c32ef83e4b08839f95cde4d#9557790639\",\"cartDetail\":null}; ccsd={\"contact\":\"9557790639\",\"name\":\"Abhinav\",\"email\":\"<EMAIL>\",\"loyalty\":10}; _gat_UA-96852049-1=1"}], "body": {"mode": "raw", "raw": "{\n    \"couponCode\": \"EMP35\",\n    \"order\": {\n        \"orderId\": null,\n        \"generateOrderId\": null,\n        \"externalOrderId\": null,\n        \"unitOrderId\": null,\n        \"campaignId\": null,\n        \"customerId\": null,\n        \"webCustomerId\": \"{{webCustomerId}}\",\n        \"employeeId\": 0,\n        \"pointsRedeemed\": 0,\n        \"source\": \"COD\",\n        \"sourceId\": null,\n        \"hasParcel\": false,\n        \"status\": null,\n        \"orders\": [\n            {\n                \"itemId\": 1,\n                \"productId\": 1033,\n                \"itemName\": null,\n                \"customizationStrategy\": 5,\n                \"productName\": \"Gur Wali Chai\",\n                \"productCategory\": {\n                    \"id\": 3623,\n                    \"name\": null,\n                    \"code\": null,\n                    \"shortCode\": null,\n                    \"type\": null,\n                    \"status\": null\n                },\n                \"quantity\": 3,\n                \"price\": 209.52,\n                \"totalAmount\": 628.5600000000001,\n                \"amount\": 628.5600000000001,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"promotionalOffer\": 0,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"totalDiscount\": \"0.0000\",\n                    \"wasValueSet\": false\n                },\n                \"complimentaryDetail\": null,\n                \"addons\": [],\n                \"dimension\": \"ChotiKetli\",\n                \"billType\": \"NET_PRICE\",\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"addons\": [],\n                    \"menuProducts\": []\n                },\n                \"recipeId\": 337,\n                \"itemCode\": null,\n                \"tax\": \"31.4280\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"15.7140\",\n                        \"total\": 628.5600000000001,\n                        \"taxable\": 628.5600000000001\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"15.7140\",\n                        \"total\": 628.5600000000001,\n                        \"taxable\": 628.5600000000001\n                    }\n                ],\n                \"originalTax\": \"31.4280\"\n            },\n            {\n                \"itemId\": 2,\n                \"productId\": 1110,\n                \"itemName\": null,\n                \"customizationStrategy\": 5,\n                \"productName\": \"Kulhad Wali Jalebi\",\n                \"productCategory\": {\n                    \"id\": 3629,\n                    \"name\": null,\n                    \"code\": null,\n                    \"shortCode\": null,\n                    \"type\": null,\n                    \"status\": null\n                },\n                \"quantity\": 1,\n                \"price\": 190.48,\n                \"totalAmount\": 190.48,\n                \"amount\": 190.48,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"promotionalOffer\": 0,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"totalDiscount\": \"0.0000\",\n                    \"wasValueSet\": false\n                },\n                \"complimentaryDetail\": null,\n                \"addons\": [],\n                \"dimension\": \"None\",\n                \"billType\": \"NET_PRICE\",\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"addons\": [],\n                    \"menuProducts\": []\n                },\n                \"recipeId\": 431,\n                \"itemCode\": null,\n                \"tax\": \"9.5240\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"4.7620\",\n                        \"total\": 190.48,\n                        \"taxable\": 190.48\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"4.7620\",\n                        \"total\": 190.48,\n                        \"taxable\": 190.48\n                    }\n                ],\n                \"originalTax\": \"9.5240\"\n            },\n            {\n                \"itemId\": 3,\n                \"productId\": 80,\n                \"customizationStrategy\": 2,\n                \"productName\": \"Kulhad Chai\",\n                \"productCategory\": {\n                    \"id\": 3623\n                },\n                \"quantity\": 2,\n                \"price\": 188.57,\n                \"totalAmount\": 377.14,\n                \"amount\": 377.14,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"promotionalOffer\": 0,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"totalDiscount\": \"0.0000\",\n                    \"wasValueSet\": false\n                },\n                \"addons\": [],\n                \"dimension\": \"ChotiKetli\",\n                \"billType\": \"NET_PRICE\",\n                \"isCombo\": false,\n                \"composition\": {\n                    \"variants\": [\n                        {\n                            \"productId\": 100330,\n                            \"alias\": \"Regular Sugar\",\n                            \"uom\": \"KG\",\n                            \"quantity\": 0,\n                            \"captured\": true,\n                            \"defaultSetting\": true,\n                            \"active\": true\n                        }\n                    ],\n                    \"products\": [],\n                    \"menuProducts\": [],\n                    \"addons\": []\n                },\n                \"recipeId\": \"217\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"9.4285\",\n                        \"total\": 377.14,\n                        \"taxable\": 377.14\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"9.4285\",\n                        \"total\": 377.14,\n                        \"taxable\": 377.14\n                    }\n                ],\n                \"tax\": \"18.8570\",\n                \"originalTax\": \"18.8570\"\n            },\n            {\n                \"itemId\": 4,\n                \"productId\": 1043,\n                \"customizationStrategy\": 5,\n                \"productName\": \"Packaging Charges\",\n                \"quantity\": 1,\n                \"price\": 30,\n                \"totalAmount\": 30,\n                \"amount\": 30,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"promotionalOffer\": 0,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"totalDiscount\": \"0.0000\",\n                    \"wasValueSet\": false\n                },\n                \"addons\": [],\n                \"dimension\": \"None\",\n                \"billType\": \"NET_PRICE\",\n                \"isCombo\": false,\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"menuProducts\": [],\n                    \"addons\": []\n                },\n                \"recipeId\": null,\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"0.7500\",\n                        \"total\": 30,\n                        \"taxable\": 30\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"0.7500\",\n                        \"total\": 30,\n                        \"taxable\": 30\n                    }\n                ],\n                \"tax\": \"1.5000\",\n                \"originalTax\": \"1.5000\"\n            }\n        ],\n        \"enquiryItems\": [],\n        \"transactionDetail\": {\n            \"totalAmount\": \"1226.1800\",\n            \"taxableAmount\": \"1226.1800\",\n            \"savings\": 0,\n            \"discountDetail\": {\n                \"discountCode\": null,\n                \"discountReason\": null,\n                \"promotionalOffer\": 0,\n                \"discount\": {\n                    \"percentage\": 0,\n                    \"value\": 0\n                },\n                \"totalDiscount\": \"0.0000\",\n                \"wasValueSet\": false\n            },\n            \"tax\": \"61.3090\",\n            \"taxes\": [\n                {\n                    \"type\": \"GST\",\n                    \"code\": \"CGST\",\n                    \"percentage\": 2.5,\n                    \"value\": \"30.6545\",\n                    \"total\": 1226.18,\n                    \"taxable\": 1226.18\n                },\n                {\n                    \"type\": \"GST\",\n                    \"code\": \"SGST/UTGST\",\n                    \"percentage\": 2.5,\n                    \"value\": \"30.6545\",\n                    \"total\": 1226.18,\n                    \"taxable\": 1226.18\n                }\n            ],\n            \"paidAmount\": 1287,\n            \"roundOffValue\": \"-0.4890\"\n        },\n        \"settlementType\": \"DEBIT\",\n        \"settlements\": [],\n        \"unitId\": 10002,\n        \"unitName\": \"Galaxy It Park\",\n        \"terminalId\": 786,\n        \"billStartTime\": null,\n        \"billCreationTime\": null,\n        \"billCreationSeconds\": 0,\n        \"billingServerTime\": null,\n        \"channelPartner\": 0,\n        \"deliveryPartner\": 0,\n        \"offerCode\": null,\n        \"cancellationDetails\": null,\n        \"orderRemark\": null,\n        \"deliveryAddress\": null,\n        \"address\": null,\n        \"customerName\": null,\n        \"containsSignupOffer\": null,\n        \"tempCode\": null,\n        \"metadataList\": [],\n        \"tokenNumber\": null,\n        \"stateId\": 28\n    },\n    \"contact\": \"9557790639\"\n}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/woff/a", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "woff", "a"]}, "description": "https://dev-staging.chaayos.com/neo-service/rest/v1/woff/a"}, "response": []}, {"name": "Order with coupon code", "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-encoding", "value": "gzip, deflate, br", "disabled": true}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "referer", "value": "https://dev-staging.chaayos.com/payProcess"}, {"key": "authority", "value": "dev-staging.chaayos.com"}, {"key": "cookie", "value": "__cfduid=d3ea0c6391e990b8862ba3dd14642fafd1546923778; _ga=GA1.3.10536586.1546923782; _gid=GA1.3.1506850970.1546923782; clmd={\"criteria\":\"DELIVERY\",\"city\":\"Noida\",\"state\":28,\"locality\":{\"value\":10,\"label\":\"Sector 62\"},\"outlet\":null}; cud={\"id\":10002,\"name\":\"Galaxy It Park\"}; dpci={\"delivery\":{\"id\":1044,\"name\":\"Delivery Charges\",\"price\":40,\"dimension\":\"None\",\"billType\":\"MRP\",\"taxCode\":\"00009963\"},\"packaging\":{\"id\":1043,\"name\":\"Packaging Charges\",\"price\":30,\"dimension\":\"None\",\"billType\":\"NET_PRICE\",\"taxCode\":\"00009963\"}}; cco={\"id\":\"2734284249431614\",\"type\":\"GEN\"}; _fbp=fb.1.1546929190757.1252101462; _ga=GA1.2.1922616713.1546929198; _gid=GA1.2.*********.1546929198; cad={\"deviceKey\":\"5c342f06e4b03e6882ae2a12:1546931061524\",\"sessionKey\":\"5c344b19e4b03e6882ae2a53#5c342f06e4b03e6882ae2a12#1546930969734#5c32ef83e4b08839f95cde4d#9557790639\",\"cartDetail\":null}; ccsd={\"contact\":\"9557790639\",\"name\":\"Abhinav\",\"email\":\"<EMAIL>\",\"loyalty\":10}; _gat_UA-96852049-1=1"}], "body": {"mode": "raw", "raw": "{\n    \"cartId\": \"{{cartId}}\",\n    \"deviceId\": \"{{deviceId}}\",\n    \"customerId\": \"{{customerId}}\",\n    \"sessionId\": \"{{sessionId}}\",\n    \"orderDetail\": {\n        \"orderId\": null,\n        \"generateOrderId\": null,\n        \"externalOrderId\": null,\n        \"unitOrderId\": null,\n        \"campaignId\": null,\n        \"customerId\": null,\n        \"webCustomerId\": \"{{webCustomerId}}\",\n        \"employeeId\": 0,\n        \"pointsRedeemed\": 0,\n        \"source\": \"COD\",\n        \"sourceId\": null,\n        \"hasParcel\": false,\n        \"status\": null,\n        \"orders\": [\n            {\n                \"itemId\": 1,\n                \"productId\": 1033,\n                \"itemName\": null,\n                \"customizationStrategy\": 5,\n                \"productName\": \"Gur Wali Chai\",\n                \"productCategory\": {\n                    \"id\": 3623,\n                    \"name\": null,\n                    \"code\": null,\n                    \"shortCode\": null,\n                    \"type\": null,\n                    \"status\": null\n                },\n                \"quantity\": 3,\n                \"price\": 209.52,\n                \"totalAmount\": 628.5600000000001,\n                \"amount\": 408.56000000000006,\n                \"discountDetail\": {\n                    \"discountCode\": 2004,\n                    \"discountReason\": \"EMP3567\",\n                    \"discount\": {\n                        \"percentage\": 99,\n                        \"value\": 399\n                    },\n                    \"promotionalOffer\": 0,\n                    \"totalDiscount\": 399\n                },\n                \"complimentaryDetail\": null,\n                \"addons\": [],\n                \"dimension\": \"ChotiKetli\",\n                \"billType\": \"NET_PRICE\",\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"addons\": [],\n                    \"menuProducts\": []\n                },\n                \"recipeId\": 337,\n                \"itemCode\": null,\n                \"tax\": \"20.4280\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"10.2140\",\n                        \"total\": 628.5600000000001,\n                        \"taxable\": 408.56000000000006\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"10.2140\",\n                        \"total\": 628.5600000000001,\n                        \"taxable\": 408.56000000000006\n                    }\n                ],\n                \"originalTax\": \"31.4280\"\n            },\n            \n            \n            {}\n        ],\n        \"enquiryItems\": [],\n        \"transactionDetail\": {\n            \"totalAmount\": \"1226.1800\",\n            \"taxableAmount\": \"797.0100\",\n            \"savings\": 450,\n            \"discountDetail\": {\n                \"discountCode\": 2004,\n                \"discountReason\": \"EMP35\",\n                \"discount\": {\n                    \"percentage\": 35,\n                    \"value\": 429.16\n                },\n                \"promotionalOffer\": 0,\n                \"totalDiscount\": \"429.1700\"\n            },\n            \"tax\": \"39.8506\",\n            \"taxes\": [\n                {\n                    \"type\": \"GST\",\n                    \"code\": \"CGST\",\n                    \"percentage\": 2.5,\n                    \"value\": \"19.9253\",\n                    \"total\": 1226.18,\n                    \"taxable\": 797.01\n                },\n                {\n                    \"type\": \"GST\",\n                    \"code\": \"SGST/UTGST\",\n                    \"percentage\": 2.5,\n                    \"value\": \"19.9253\",\n                    \"total\": 1226.18,\n                    \"taxable\": 797.01\n                }\n            ],\n            \"paidAmount\": 837,\n            \"roundOffValue\": \"0.1394\"\n        },\n        \"settlementType\": \"DEBIT\",\n        \"settlements\": [\n            {\n                \"mode\": 1,\n                \"amount\": 837\n            }\n        ],\n        \"unitId\": 10002,\n        \"unitName\": \"Galaxy It Park\",\n        \"terminalId\": 786,\n        \"billStartTime\": null,\n        \"billCreationTime\": null,\n        \"billCreationSeconds\": 0,\n        \"billingServerTime\": null,\n        \"channelPartner\": 0,\n        \"deliveryPartner\": 0,\n        \"offerCode\": \"EMP3567\",\n        \"cancellationDetails\": null,\n        \"orderRemark\": null,\n        \"deliveryAddress\": 7,\n        \"address\": null,\n        \"customerName\": null,\n        \"containsSignupOffer\": null,\n        \"tempCode\": null,\n        \"metadataList\": [],\n        \"tokenNumber\": null,\n        \"stateId\": 28\n    },\n    \"creationTime\": 1546930969737,\n    \"checkoutTime\": null,\n    \"cartStatus\": \"CREATED\",\n    \"interState\": false,\n    \"currentStateId\": 28\n}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/wcrt/ckt", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "wcrt", "ckt"]}, "description": "https://dev-staging.chaayos.com/neo-service/rest/v1/wcrt/ckt"}, "response": []}], "_postman_isSubFolder": true}, {"name": "<PERSON>ce StampRD", "event": [{"listen": "test", "script": {"id": "197de2ea-d732-4177-a054-1a52785eada8", "exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"deviceKey\", jsonData.deviceKey);", "console.log(pm.environment.get(\"deviceKey\"));", "", "//verifying the device has been stamped or not", "", "pm.test(\"Device Key Generated\",function()", "{", "    pm.expect(pm.response.text()).to.include(jsonData.deviceKey);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "authority", "value": "dev-staging.chaayos.com", "disabled": true}], "body": {"mode": "raw", "raw": "{\"userAgent\":\"Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36\",\"platform\":\"Win32\"}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/st/rd", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "st", "rd"]}, "description": "https://dev-staging.chaayos.com/neo-service/rest/v1/st/rd"}, "response": []}, {"name": "Get WEB Cus ID Login", "event": [{"listen": "test", "script": {"id": "463a4b8a-eeaf-4bbd-b6dd-5a21a2b5f652", "exec": ["//verifying status code", "", "pm.test(\"Status code is 200\", function()", "{", "    pm.response.to.have.status(200);", "});", "//setting sessionKey", "var jsonData = pm.response.json();", "pm.environment.set(\"sessionKey\", jsonData.device.sessionKey);", "pm.environment.set(\"cartId\", jsonData.device.cartDetail.cartId);", "pm.environment.set(\"deviceId\", jsonData.device.cartDetail.deviceId);", "pm.environment.set(\"customerId\", jsonData.device.cartDetail.customerId);", "pm.environment.set(\"webCustomerId\", jsonData.device.cartDetail.orderDetail.webCustomerId);", "console.log(pm.environment.get(\"deviceId\", \"cartId\", \"webCustomerId\", \"sessionKey\"));", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-encoding", "value": "gzip, deflate, br", "disabled": true}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "referer", "value": "https://dev-staging.chaayos.com/login"}, {"key": "authority", "value": "dev-staging.chaayos.com"}, {"key": "cookie", "value": "__cfduid=d36efc9c66c1ee526a5545f44ec28b8281546842225; cad={\"deviceKey\":\"5c32f072e4b08839f95cde53:1546842226457\",\"sessionKey\":null,\"cartDetail\":null}; clmd={\"criteria\":\"DELIVERY\",\"city\":\"Noida\",\"state\":28,\"locality\":{\"value\":10,\"label\":\"Sector 62\"},\"outlet\":null}; _ga=GA1.3.46698243.1546842261; _gid=GA1.3.1514214065.1546842261; cud={\"id\":10002,\"name\":\"Galaxy It Park\"}; dpci={\"delivery\":{\"id\":1044,\"name\":\"Delivery Charges\",\"price\":40,\"dimension\":\"None\",\"billType\":\"MRP\",\"taxCode\":\"00009963\"},\"packaging\":{\"id\":1043,\"name\":\"Packaging Charges\",\"price\":30,\"dimension\":\"None\",\"billType\":\"NET_PRICE\",\"taxCode\":\"00009963\"}}; ccsd={\"contact\":\"9557790639\"}"}], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"1234\",\n    \"contact\": \"9557790639\",\n    \"deviceKey\": \"{{deviceKey}}\",\n    \"update\": false\n}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/c/lgn", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "c", "lgn"]}}, "response": []}, {"name": "Get Generated ID ckt", "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-encoding", "value": "gzip, deflate, br", "disabled": true}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "referer", "value": "https://dev-staging.chaayos.com/payProcess", "disabled": true}, {"key": "authority", "value": "dev-staging.chaayos.com"}, {"key": "cookie", "value": "__cfduid=dfa789694f8b2780100021ec3c135a6601546599623; _ga=GA1.3.*********.1546599628; _gid=GA1.3.20298254.1546599628; clmd={\"criteria\":\"DELIVERY\",\"city\":\"Noida\",\"state\":28,\"locality\":{\"value\":10,\"label\":\"Sector 62\"},\"outlet\":null}; cud={\"id\":10002,\"name\":\"Galaxy It Park\"}; dpci={\"delivery\":{\"id\":1044,\"name\":\"Delivery Charges\",\"price\":40,\"dimension\":\"None\",\"billType\":\"MRP\",\"taxCode\":\"00009963\"},\"packaging\":{\"id\":1043,\"name\":\"Packaging Charges\",\"price\":30,\"dimension\":\"None\",\"billType\":\"NET_PRICE\",\"taxCode\":\"00009963\"}}; cad={\"deviceKey\":\"5c2f3ccbe4b046fa4c7068a0:1546599679872\",\"sessionKey\":\"5c2f3dc6e4b046fa4c7068a3#5c2f3ccbe4b046fa4c7068a0#1546599878047#5c2f3dc2e4b046fa4c7068a2#9879879878\",\"cartDetail\":null}; ccsd={\"contact\":\"9879879878\",\"name\":\"abhinav\",\"email\":\"<EMAIL>\",\"loyalty\":20}; _gat_UA-96852049-1=1", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"cartId\": \"{{cartId}}\",\n    \"deviceId\": \"{{deviceId}}\",\n    \"customerId\": \"{{customerId}}\",\n    \"sessionId\": \"{{sessionId}}\",\n    \"orderDetail\": {\n        \"orderId\": null,\n        \"generateOrderId\": null,\n        \"externalOrderId\": null,\n        \"unitOrderId\": null,\n        \"campaignId\": null,\n        \"customerId\": null,\n        \"webCustomerId\": \"{{webCustomerId}}\",\n        \"employeeId\": 0,\n        \"pointsRedeemed\": 0,\n        \"source\": \"COD\",\n        \"sourceId\": null,\n        \"hasParcel\": false,\n        \"status\": null,\n        \"orders\": [\n            {\n                \"itemId\": 1,\n                \"productId\": 1114,\n                \"itemName\": null,\n                \"customizationStrategy\": 5,\n                \"productName\": \"Shahi Chai\",\n                \"productCategory\": {\n                    \"id\": 3623,\n                    \"name\": null,\n                    \"code\": null,\n                    \"shortCode\": null,\n                    \"type\": null,\n                    \"status\": null\n                },\n                \"quantity\": 1,\n                \"price\": 247.62,\n                \"totalAmount\": 247.62,\n                \"amount\": 247.62,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"promotionalOffer\": 0,\n                    \"totalDiscount\": 0\n                },\n                \"complimentaryDetail\": null,\n                \"addons\": [],\n                \"dimension\": \"ChotiKetli\",\n                \"billType\": \"NET_PRICE\",\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"addons\": [],\n                    \"menuProducts\": []\n                },\n                \"recipeId\": 494,\n                \"itemCode\": null,\n                \"tax\": \"12.3810\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"6.1905\",\n                        \"total\": 247.62,\n                        \"taxable\": 247.62\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"6.1905\",\n                        \"total\": 247.62,\n                        \"taxable\": 247.62\n                    }\n                ],\n                \"originalTax\": \"12.3810\"\n            },\n            {\n                \"itemId\": 2,\n                \"productId\": 1109,\n                \"itemName\": null,\n                \"customizationStrategy\": 5,\n                \"productName\": \"Baarish Wale Pakore\",\n                \"productCategory\": {\n                    \"id\": 3628,\n                    \"name\": null,\n                    \"code\": null,\n                    \"shortCode\": null,\n                    \"type\": null,\n                    \"status\": null\n                },\n                \"quantity\": 1,\n                \"price\": 190.48,\n                \"totalAmount\": 190.48,\n                \"amount\": 190.48,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"promotionalOffer\": 0,\n                    \"totalDiscount\": 0\n                },\n                \"complimentaryDetail\": null,\n                \"addons\": [],\n                \"dimension\": \"None\",\n                \"billType\": \"NET_PRICE\",\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"addons\": [],\n                    \"menuProducts\": []\n                },\n                \"recipeId\": 430,\n                \"itemCode\": null,\n                \"tax\": \"9.5240\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"4.7620\",\n                        \"total\": 190.48,\n                        \"taxable\": 190.48\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"4.7620\",\n                        \"total\": 190.48,\n                        \"taxable\": 190.48\n                    }\n                ],\n                \"originalTax\": \"9.5240\"\n            },\n            {\n                \"itemId\": 3,\n                \"productId\": 1043,\n                \"itemName\": null,\n                \"customizationStrategy\": 0,\n                \"productName\": \"Packaging Charges\",\n                \"productCategory\": null,\n                \"quantity\": 1,\n                \"price\": 30,\n                \"totalAmount\": 30,\n                \"amount\": 30,\n                \"discountDetail\": {\n                    \"discountCode\": null,\n                    \"discountReason\": null,\n                    \"discount\": {\n                        \"percentage\": 0,\n                        \"value\": 0\n                    },\n                    \"promotionalOffer\": 0,\n                    \"totalDiscount\": 0\n                },\n                \"complimentaryDetail\": null,\n                \"addons\": [],\n                \"dimension\": \"None\",\n                \"billType\": \"NET_PRICE\",\n                \"composition\": {\n                    \"variants\": [],\n                    \"products\": [],\n                    \"addons\": [],\n                    \"menuProducts\": []\n                },\n                \"recipeId\": 0,\n                \"itemCode\": null,\n                \"tax\": \"1.5000\",\n                \"code\": \"00009963\",\n                \"taxes\": [\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"CGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"0.7500\",\n                        \"total\": 30,\n                        \"taxable\": 30\n                    },\n                    {\n                        \"type\": \"GST\",\n                        \"code\": \"SGST/UTGST\",\n                        \"percentage\": 2.5,\n                        \"value\": \"0.7500\",\n                        \"total\": 30,\n                        \"taxable\": 30\n                    }\n                ],\n                \"originalTax\": \"1.5000\"\n            }\n        ],\n        \"enquiryItems\": [],\n        \"transactionDetail\": {\n            \"totalAmount\": \"468.1000\",\n            \"taxableAmount\": \"468.1000\",\n            \"savings\": 0,\n            \"discountDetail\": {\n                \"discount\": {\n                    \"percentage\": 0,\n                    \"value\": 0,\n                    \"wasValueSet\": false\n                },\n                \"discountReason\": null,\n                \"discountCode\": null,\n                \"totalDiscount\": \"0.0000\",\n                \"promotionalOffer\": 0\n            },\n            \"tax\": \"23.4050\",\n            \"taxes\": [\n                {\n                    \"type\": \"GST\",\n                    \"code\": \"CGST\",\n                    \"percentage\": 2.5,\n                    \"value\": \"11.7025\",\n                    \"total\": 468.1,\n                    \"taxable\": 468.1\n                },\n                {\n                    \"type\": \"GST\",\n                    \"code\": \"SGST/UTGST\",\n                    \"percentage\": 2.5,\n                    \"value\": \"11.7025\",\n                    \"total\": 468.1,\n                    \"taxable\": 468.1\n                }\n            ],\n            \"paidAmount\": 492,\n            \"roundOffValue\": \"0.4950\"\n        },\n        \"settlementType\": \"DEBIT\",\n        \"settlements\": [\n            {\n                \"mode\": 1,\n                \"amount\": 492\n            }\n        ],\n        \"unitId\": 10002,\n        \"unitName\": \"Galaxy It Park\",\n        \"terminalId\": 786,\n        \"billStartTime\": null,\n        \"billCreationTime\": null,\n        \"billCreationSeconds\": 0,\n        \"billingServerTime\": null,\n        \"channelPartner\": 0,\n        \"deliveryPartner\": 0,\n        \"offerCode\": null,\n        \"cancellationDetails\": null,\n        \"orderRemark\": null,\n        \"deliveryAddress\": 3,\n        \"address\": null,\n        \"customerName\": \"abhinav\",\n        \"containsSignupOffer\": null,\n        \"tempCode\": null,\n        \"metadataList\": [],\n        \"tokenNumber\": null,\n        \"stateId\": 28\n    },\n    \"creationTime\": 1546599627172,\n    \"checkoutTime\": null,\n    \"cartStatus\": \"CREATED\",\n    \"interState\": false,\n    \"currentStateId\": 28\n}"}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/wcrt/ckt", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "wcrt", "ckt"]}, "description": "https://dev-staging.chaayos.com/neo-service/rest/v1/wcrt/ckt"}, "response": []}, {"name": "CreateOrder", "request": {"method": "POST", "header": [{"key": "origin", "value": "https://dev-staging.chaayos.com"}, {"key": "accept-encoding", "value": "gzip, deflate, br", "disabled": true}, {"key": "accept-language", "value": "en-US,en;q=0.9"}, {"key": "user-agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "content-type", "value": "application/json"}, {"key": "accept", "value": "application/json, text/plain, */*"}, {"key": "referer", "value": "https://dev-staging.chaayos.com/payProcess"}, {"key": "authority", "value": "dev-staging.chaayos.com"}, {"key": "cookie", "value": "__cfduid=d36efc9c66c1ee526a5545f44ec28b8281546842225; clmd={\"criteria\":\"DELIVERY\",\"city\":\"Noida\",\"state\":28,\"locality\":{\"value\":10,\"label\":\"Sector 62\"},\"outlet\":null}; _ga=GA1.3.46698243.1546842261; _gid=GA1.3.1514214065.1546842261; cud={\"id\":10002,\"name\":\"Galaxy It Park\"}; dpci={\"delivery\":{\"id\":1044,\"name\":\"Delivery Charges\",\"price\":40,\"dimension\":\"None\",\"billType\":\"MRP\",\"taxCode\":\"00009963\"},\"packaging\":{\"id\":1043,\"name\":\"Packaging Charges\",\"price\":30,\"dimension\":\"None\",\"billType\":\"NET_PRICE\",\"taxCode\":\"00009963\"}}; cad={\"deviceKey\":\"5c32f072e4b08839f95cde53:1546842226457\",\"sessionKey\":\"5c32f0d6e4b08839f95cde55#5c32f072e4b08839f95cde53#1546842326202#5c32ef83e4b08839f95cde4d#9557790639\",\"cartDetail\":null}; ccsd={\"contact\":\"9557790639\",\"name\":\"Abhinav\",\"email\":\"<EMAIL>\",\"loyalty\":10}; _gat_UA-96852049-1=1; cco={\"id\":\"5407468602901408\",\"type\":\"GEN\"}"}], "body": {"mode": "raw", "raw": "\"5407468602901408\""}, "url": {"raw": "https://dev-staging.chaayos.com/neo-service/rest/v1/word/s/id", "protocol": "https", "host": ["dev-staging", "chaayos", "com"], "path": ["neo-service", "rest", "v1", "word", "s", "id"]}, "description": "https://dev-staging.chaayos.com/neo-service/rest/v1/word/s/id"}, "response": []}]}, {"name": "POS Test Cases", "item": [{"name": "1_Check for single item order.", "item": [{"name": "Login_Unit", "event": [{"listen": "test", "script": {"id": "b3a7776d-1018-4268-aed6-4e2d7bfd5972", "exec": ["var jsonData = pm.response.json();    //parsing json response body", "pm.environment.set(\"jwtToken\", jsonData.jwtToken);  // setting jwtToken for further requests", "console.log(pm.environment.get(\"jwtToken\"));  //verifying if it is set or not", "", "/*verifying phone number of logged in user*/", "pm.test(\"is primar contact = 8055852367\", function () {", "    pm.expect(pm.response.text()).to.include(jsonData.user.primaryContact);", "});", "// pm.test(\"Your test name\", function () {", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.user.primaryContact).to.eql(100);", "// });"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiZmM4c3lnSGcxb0VIeThaTWhjeTF4bU9yeEJUZmhwMjd2T2F4NXIvdFhWSjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk3NDAzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.9BGwvnMjWmezmExCezEYq2yBOJ0AMiUocjFHiRb6fh0", "disabled": true}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"unitId\": 10000,\r\n  \"userId\": 120458 , \r\n  \"password\": \"321321\",\r\n  \"terminalId\": 1,\r\n  \"screenType\": \"POS\",\r\n  \"application\": \"KETTLE_SERVICE\"\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "users", "login"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login"}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"id": "05345d59-22aa-4dd9-b567-d5c87fe07b52", "exec": ["// assertion 1 - checking if API has generated the response or not", "var jsonData = pm.response.json();", "pm.test(\"Is response generated\", function()", "{", "    pm.response.to.have.status(200);", "    console.log(\"inside respone generated\");", "});", "", "//assertion 2 - checking for the orderNumber generated or not to verify order creation", "", "pm.test(\"Is Order Number Generated?\", function () {", "    pm.expect(pm.response.text()).to.include(\"generatedOrderId\");", "    console.log(\"inside Order Number Generated\");", "   console.log(pm.response.text());", "});", "", "//assertion 3 - checking for receipt if generated", "pm.test(\"Is receipt generated?\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.billIncludedInReceipts).to.eql(true);", "    console.log(\"inside receipt generated\");", "   console.log(jsonData);", "});", "", "pm.test(\"counting\", function()", "{", "    //pm.expect(jsonData.receipts.length)===4;", "    pm.expect(Object.keys(jsonData.receipts).length).to.eql(6);", "    ", "})"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22FDO1WAsHmWGunHd52q5XmoBAOph0vJQ3obW7qp1D7PF8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiRkRPMVdBc0htV0d1bkhkNTJxNVhtb0JBT3BoMHZKUTNvYlc3cXAxRDdQRjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk5OTk0LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.gY02v4R6aj0VeGKbYXagnZYtPoBqUFfbqNfHvBoFgjQ%22%7D%7D; isQZLoaded=true", "disabled": true}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth-internal", "value": "eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"13913102\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n    {\r\n      \"itemId\": 1,\r\n      \"productId\": 1114,\r\n      \"productName\": \"<PERSON><PERSON>\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 123.81,\r\n      \"amount\": 123.81,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"None\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 493,\r\n      \"totalAmount\": 123.81,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        }\r\n      ],\r\n      \"tax\": 6.190499999999999,\r\n      \"originalTax\": 6.190499999999999\r\n    }\r\n  ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 123.81,\r\n    \"taxableAmount\": 123.81,\r\n    \"savings\": 0,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 0,\r\n        \"value\": 0,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": null,\r\n      \"discountCode\": null,\r\n      \"totalDiscount\": 0,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 130,\r\n    \"roundOffValue\": -0.0004999999999881766,\r\n    \"tax\": 6.190499999999999,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 3.0952499999999996,\r\n        \"total\": 123.81,\r\n        \"taxable\": 123.81\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 3.0952499999999996,\r\n        \"total\": 123.81,\r\n        \"taxable\": 123.81\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"CAFE\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 2,\r\n      \"amount\": 130,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": 12014,\r\n  \"billStartTime\": \"2018-12-27 15:24:25\",\r\n  \"billCreationSeconds\": 263,\r\n  \"billCreationTime\": \"2018-12-27 15:28:48\",\r\n  \"billingServerTime\": \"2018-12-27 15:28:48\",\r\n  \"channelPartner\": 1,\r\n  \"deliveryPartner\": null,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": null,\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": null,\r\n  \"customerName\": \"abhinav\",\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"CUSTOMER_SCREEN_DOWN\",\r\n      \"attributeValue\": \"2018-12-27T15:28:22+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_MODEL_OPEN\",\r\n      \"attributeValue\": \"2018-12-27T15:28:22+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"CUSTOMER_NAME_MANUALLY\",\r\n      \"attributeValue\": \"abhinav\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:130,bill:130,change:0\"\r\n    }\r\n  ],\r\n  \"newCustomer\": false,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"isGiftOrder\": false\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create"}, "response": []}], "_postman_isSubFolder": true}, {"name": "2_Check for first order of the day", "item": [{"name": "Login_Unit", "event": [{"listen": "test", "script": {"id": "b3a7776d-1018-4268-aed6-4e2d7bfd5972", "exec": ["var jsonData = pm.response.json();    //parsing json response body", "pm.environment.set(\"jwtToken\", jsonData.jwtToken);  // setting jwtToken for further requests", "console.log(pm.environment.get(\"jwtToken\"));  //verifying if it is set or not", "", "/*verifying phone number of logged in user*/", "pm.test(\"is primar contact = 8055852367\", function () {", "    pm.expect(pm.response.text()).to.include(jsonData.user.primaryContact);", "});", "// pm.test(\"Your test name\", function () {", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.user.primaryContact).to.eql(100);", "// });"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiZmM4c3lnSGcxb0VIeThaTWhjeTF4bU9yeEJUZmhwMjd2T2F4NXIvdFhWSjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk3NDAzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.9BGwvnMjWmezmExCezEYq2yBOJ0AMiUocjFHiRb6fh0"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"unitId\": 10000,\r\n  \"userId\": 120458 , \r\n  \"password\": \"321321\",\r\n  \"terminalId\": 1,\r\n  \"screenType\": \"POS\",\r\n  \"application\": \"KETTLE_SERVICE\"\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "users", "login"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login"}, "response": []}, {"name": "Load Products", "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "12014"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/unit-metadata/unit-products", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "unit-metadata", "unit-products"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/unit-metadata/unit-products\t"}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"id": "791fc145-387b-4b6c-ab9a-7ec10837345a", "exec": ["// assertion 1 - checking if API has generated the response or not", "var jsonData = pm.response.json();", "pm.test(\"Is response generated\", function()", "{", "    pm.response.to.have.status(200);", "});", "", "//assertion 2 - checking for the orderNumber generated or not to verify order creation", "", "pm.test(\"Is Order Number Generated?\", function () {", "    pm.expect(pm.response.text()).to.include(\"generatedOrderId\");", "});", "", "//assertion 3 - checking for receipt if generated", "pm.test(\"Is receipt generated?\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.billIncludedInReceipts).to.eql(true);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "bd466700-b2eb-41cc-94bb-bc70c10a35f8", "exec": ["", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22FDO1WAsHmWGunHd52q5XmoBAOph0vJQ3obW7qp1D7PF8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiRkRPMVdBc0htV0d1bkhkNTJxNVhtb0JBT3BoMHZKUTNvYlc3cXAxRDdQRjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk5OTk0LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.gY02v4R6aj0VeGKbYXagnZYtPoBqUFfbqNfHvBoFgjQ%22%7D%7D; isQZLoaded=true", "disabled": true}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"13785772\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n    {\r\n      \"itemId\": 1,\r\n      \"productId\": 1114,\r\n      \"productName\": \"<PERSON><PERSON>\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 123.81,\r\n      \"amount\": 123.81,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"None\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 493,\r\n      \"totalAmount\": 123.81,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        }\r\n      ],\r\n      \"tax\": 6.190499999999999,\r\n      \"originalTax\": 6.190499999999999\r\n    }\r\n  ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 123.81,\r\n    \"taxableAmount\": 123.81,\r\n    \"savings\": 0,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 0,\r\n        \"value\": 0,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": null,\r\n      \"discountCode\": null,\r\n      \"totalDiscount\": 0,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 130,\r\n    \"roundOffValue\": -0.0004999999999881766,\r\n    \"tax\": 6.190499999999999,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 3.0952499999999996,\r\n        \"total\": 123.81,\r\n        \"taxable\": 123.81\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 3.0952499999999996,\r\n        \"total\": 123.81,\r\n        \"taxable\": 123.81\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"CAFE\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 2,\r\n      \"amount\": 130,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": 12014,\r\n  \"billStartTime\": \"2018-12-27 15:24:25\",\r\n  \"billCreationSeconds\": 263,\r\n  \"billCreationTime\": \"2018-12-27 15:28:48\",\r\n  \"billingServerTime\": \"2018-12-27 15:28:48\",\r\n  \"channelPartner\": 1,\r\n  \"deliveryPartner\": null,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": null,\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": null,\r\n  \"customerName\": \"abhinav\",\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"CUSTOMER_SCREEN_DOWN\",\r\n      \"attributeValue\": \"2018-12-27T15:28:22+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_MODEL_OPEN\",\r\n      \"attributeValue\": \"2018-12-27T15:28:22+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"CUSTOMER_NAME_MANUALLY\",\r\n      \"attributeValue\": \"abhinav\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:130,bill:130,change:0\"\r\n    }\r\n  ],\r\n  \"newCustomer\": false,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"isGiftOrder\": false\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create"}, "response": []}], "_postman_isSubFolder": true}, {"name": "3_Check for more than 1 item from same cateogory.", "item": [{"name": "Login_Unit", "event": [{"listen": "test", "script": {"id": "b3a7776d-1018-4268-aed6-4e2d7bfd5972", "exec": ["var jsonData = pm.response.json();    //parsing json response body", "pm.environment.set(\"jwtToken\", jsonData.jwtToken);  // setting jwtToken for further requests", "console.log(pm.environment.get(\"jwtToken\"));  //verifying if it is set or not", "", "/*verifying phone number of logged in user*/", "pm.test(\"is primar contact = 8055852367\", function () {", "    pm.expect(pm.response.text()).to.include(jsonData.user.primaryContact);", "});", "// pm.test(\"Your test name\", function () {", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.user.primaryContact).to.eql(100);", "// });"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiZmM4c3lnSGcxb0VIeThaTWhjeTF4bU9yeEJUZmhwMjd2T2F4NXIvdFhWSjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk3NDAzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.9BGwvnMjWmezmExCezEYq2yBOJ0AMiUocjFHiRb6fh0", "disabled": true}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"unitId\": 10000,\r\n  \"userId\": 120458 , \r\n  \"password\": \"321321\",\r\n  \"terminalId\": 1,\r\n  \"screenType\": \"POS\",\r\n  \"application\": \"KETTLE_SERVICE\"\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "users", "login"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login"}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"id": "05345d59-22aa-4dd9-b567-d5c87fe07b52", "exec": ["// assertion 1 - checking if API has generated the response or not", "var jsonData = pm.response.json();", "pm.test(\"Is response generated\", function()", "{", "    pm.response.to.have.status(200);", "    console.log(\"inside respone generated\");", "});", "", "//assertion 2 - checking for the orderNumber generated or not to verify order creation", "", "pm.test(\"Is Order Number Generated?\", function () {", "    pm.expect(pm.response.text()).to.include(\"generatedOrderId\");", "    console.log(\"inside Order Number Generated\");", "   console.log(pm.response.text());", "});", "", "//assertion 3 - checking for receipt if generated", "pm.test(\"Is receipt generated?\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.billIncludedInReceipts).to.eql(true);", "    console.log(\"inside receipt generated\");", "   console.log(jsonData);", "});", "", "pm.test(\"counting\", function()", "{", "    //pm.expect(jsonData.receipts.length)===4;", "    pm.expect(Object.keys(jsonData.receipts).length).to.eql(3);", "    ", "});", "", "pm.environment.set(\"generatedOrderId\", jsonData.generatedOrderId);  ", "console.log(pm.environment.get(\"generatedOrderId\")); "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22FDO1WAsHmWGunHd52q5XmoBAOph0vJQ3obW7qp1D7PF8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiRkRPMVdBc0htV0d1bkhkNTJxNVhtb0JBT3BoMHZKUTNvYlc3cXAxRDdQRjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk5OTk0LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.gY02v4R6aj0VeGKbYXagnZYtPoBqUFfbqNfHvBoFgjQ%22%7D%7D; isQZLoaded=true", "disabled": true}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"19627158\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n    {\r\n      \"itemId\": 1,\r\n      \"productId\": 1033,\r\n      \"productName\": \"<PERSON><PERSON>\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 104.76,\r\n      \"amount\": 104.76,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"Regular\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"recipeId\": 314,\r\n      \"totalAmount\": 104.76,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 2.619,\r\n          \"total\": 104.76,\r\n          \"taxable\": 104.76\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 2.619,\r\n          \"total\": 104.76,\r\n          \"taxable\": 104.76\r\n        }\r\n      ],\r\n      \"tax\": 5.238,\r\n      \"originalTax\": 5.238\r\n    },\r\n    {\r\n      \"itemId\": 2,\r\n      \"productId\": 130,\r\n      \"productName\": \"Honey Ginger Lemon\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 123.81,\r\n      \"amount\": 123.81,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"Regular\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 76,\r\n      \"totalAmount\": 123.81,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        }\r\n      ],\r\n      \"tax\": 6.190499999999999,\r\n      \"originalTax\": 6.190499999999999\r\n    },\r\n    {\r\n      \"itemId\": 3,\r\n      \"productId\": 150,\r\n      \"productName\": \"Pahadi Chai\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 161.9,\r\n      \"amount\": 161.9,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"Regular\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          {\r\n            \"productId\": 100330,\r\n            \"alias\": \"Regular Sugar\",\r\n            \"uom\": \"KG\",\r\n            \"quantity\": 0.00503,\r\n            \"captured\": true,\r\n            \"defaultSetting\": true,\r\n            \"selected\": true\r\n          }\r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 193,\r\n      \"totalAmount\": 161.9,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.0475,\r\n          \"total\": 161.9,\r\n          \"taxable\": 161.9\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.0475,\r\n          \"total\": 161.9,\r\n          \"taxable\": 161.9\r\n        }\r\n      ],\r\n      \"tax\": 8.095,\r\n      \"originalTax\": 8.095\r\n    }\r\n  ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 390.47,\r\n    \"taxableAmount\": 390.47,\r\n    \"savings\": 0,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 0,\r\n        \"value\": 0,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": null,\r\n      \"discountCode\": null,\r\n      \"totalDiscount\": 0,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 410,\r\n    \"roundOffValue\": 0.006499999999959982,\r\n    \"tax\": 19.5235,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 9.76175,\r\n        \"total\": 390.47,\r\n        \"taxable\": 390.47\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 9.76175,\r\n        \"total\": 390.47,\r\n        \"taxable\": 390.47\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"CAFE\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 1,\r\n      \"amount\": 410,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": 12014,\r\n  \"billStartTime\": \"2018-12-28 13:07:47\",\r\n  \"billCreationSeconds\": 77,\r\n  \"billCreationTime\": \"2018-12-28 13:09:04\",\r\n  \"billingServerTime\": \"2018-12-28 13:09:04\",\r\n  \"channelPartner\": 1,\r\n  \"deliveryPartner\": null,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": null,\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": null,\r\n  \"customerName\": \"dfsfsdf\",\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"CUSTOMER_SCREEN_DOWN\",\r\n      \"attributeValue\": \"2018-12-28T13:08:55+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_MODEL_OPEN\",\r\n      \"attributeValue\": \"2018-12-28T13:08:17+05:30::2018-12-28T13:08:55+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:410,bill:410,change:0::amount:410,bill:410,change:0::amount:410,bill:410,change:0\"\r\n    },\r\n    {\r\n      \"attributeName\": \"CUSTOMER_NAME_MANUALLY\",\r\n      \"attributeValue\": \"dfsfsdf\"\r\n    }\r\n  ],\r\n  \"newCustomer\": false,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"isGiftOrder\": false\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create"}, "response": []}, {"name": "View Generated Order Summary", "event": [{"listen": "test", "script": {"id": "65e30d3b-5216-419b-a9ce-3bd22ba12082", "exec": ["", "pm.test(\"Response generated?\", function()", "{", "    pm.response.to.have.status(200);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Item Ordered is 3\", function()", "{", "    pm.expect(Object.keys(jsonData.orders).length).to.eql(3);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22716GrRCL7br9wJd%2BhNjDuj1%2FUEWA5OQpXqaqvkw1xmV8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiNzE2R3JSQ0w3YnI5d0pkK2hOakR1ajEvVUVXQTVPUXBYcWFxdmt3MXhtVjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1OTc3NjEzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.bRVSyPW-a-lcb2wy9K1Rv4Q_1N2Q4Qp4GRBqgeGlV4Y%22%7D%7D; isQZLoaded=true; lastThreeOrders=%7B%22unitId%22%3A12014%2C%22orders%22%3A%5B%226273909680348135%22%2C%226901812525766626%22%5D%7D"}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiNzE2R3JSQ0w3YnI5d0pkK2hOakR1ajEvVUVXQTVPUXBYcWFxdmt3MXhtVjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1OTc3NjEzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.bRVSyPW-a-lcb2wy9K1Rv4Q_1N2Q4Qp4GRBqgeGlV4Y"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{{generatedOrderId}}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/generated-order", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "generated-order"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/generated-order"}, "response": []}], "_postman_isSubFolder": true}, {"name": "4_Check for items with different categories.", "item": [{"name": "Login_Unit", "event": [{"listen": "test", "script": {"id": "b3a7776d-1018-4268-aed6-4e2d7bfd5972", "exec": ["var jsonData = pm.response.json();    //parsing json response body", "pm.environment.set(\"jwtToken\", jsonData.jwtToken);  // setting jwtToken for further requests", "console.log(pm.environment.get(\"jwtToken\"));  //verifying if it is set or not", "", "/*verifying phone number of logged in user*/", "pm.test(\"is primar contact = 8055852367\", function () {", "    pm.expect(pm.response.text()).to.include(jsonData.user.primaryContact);", "});", "// pm.test(\"Your test name\", function () {", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.user.primaryContact).to.eql(100);", "// });"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiZmM4c3lnSGcxb0VIeThaTWhjeTF4bU9yeEJUZmhwMjd2T2F4NXIvdFhWSjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk3NDAzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.9BGwvnMjWmezmExCezEYq2yBOJ0AMiUocjFHiRb6fh0", "disabled": true}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"unitId\": 10000,\r\n  \"userId\": 120458 , \r\n  \"password\": \"321321\",\r\n  \"terminalId\": 1,\r\n  \"screenType\": \"POS\",\r\n  \"application\": \"KETTLE_SERVICE\"\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "users", "login"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login"}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"id": "8c63bfab-72b0-42ed-8a05-ee5509d69f69", "exec": ["// assertion 1 - checking if API has generated the response or not", "var jsonData = pm.response.json();", "pm.test(\"Is response generated\", function()", "{", "    pm.response.to.have.status(200);", "    console.log(\"inside respone generated\");", "});", "", "//assertion 2 - checking for the orderNumber generated or not to verify order creation", "", "pm.test(\"Is Order Number Generated?\", function () {", "    pm.expect(pm.response.text()).to.include(\"generatedOrderId\");", "    console.log(\"inside Order Number Generated\");", "   console.log(pm.response.text());", "});", "", "//assertion 3 - checking for receipt if generated", "pm.test(\"Is receipt generated?\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.billIncludedInReceipts).to.eql(true);", "    console.log(\"inside receipt generated\");", "   console.log(jsonData);", "});", "", "pm.test(\"counting\", function()", "{", "    //pm.expect(jsonData.receipts.length)===4;", "    pm.expect(Object.keys(jsonData.receipts).length).to.eql(3);", "    ", "});", "", "pm.environment.set(\"generatedOrderId\", jsonData.generatedOrderId);  ", "console.log(pm.environment.get(\"generatedOrderId\")); "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22716GrRCL7br9wJd%2BhNjDuj1%2FUEWA5OQpXqaqvkw1xmV8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiNzE2R3JSQ0w3YnI5d0pkK2hOakR1ajEvVUVXQTVPUXBYcWFxdmt3MXhtVjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1OTc3NjEzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.bRVSyPW-a-lcb2wy9K1Rv4Q_1N2Q4Qp4GRBqgeGlV4Y%22%7D%7D; isQZLoaded=true; lastThreeOrders=%7B%22unitId%22%3A12014%2C%22orders%22%3A%5B%226273909680348135%22%2C%226901812525766626%22%5D%7D"}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"60966313\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n    {\r\n      \"itemId\": 1,\r\n      \"productId\": 10,\r\n      \"productName\": \"<PERSON>i Chai\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 66.67,\r\n      \"amount\": 66.67,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"Regular\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          {\r\n            \"productId\": 100330,\r\n            \"alias\": \"Regular Sugar\",\r\n            \"uom\": \"KG\",\r\n            \"quantity\": 0,\r\n            \"captured\": true,\r\n            \"defaultSetting\": true,\r\n            \"selected\": true\r\n          },\r\n          {\r\n            \"productId\": 100298,\r\n            \"alias\": \"Regular Patti\",\r\n            \"uom\": \"SACHET\",\r\n            \"quantity\": 1,\r\n            \"captured\": true,\r\n            \"defaultSetting\": true,\r\n            \"selected\": true\r\n          }\r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 104,\r\n      \"totalAmount\": 66.67,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 1.6667500000000002,\r\n          \"total\": 66.67,\r\n          \"taxable\": 66.67\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 1.6667500000000002,\r\n          \"total\": 66.67,\r\n          \"taxable\": 66.67\r\n        }\r\n      ],\r\n      \"tax\": 3.3335000000000004,\r\n      \"originalTax\": 3.3335000000000004\r\n    },\r\n    {\r\n      \"itemId\": 2,\r\n      \"productId\": 1033,\r\n      \"productName\": \"Gur Wali Chai\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 104.76,\r\n      \"amount\": 104.76,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"Regular\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"recipeId\": 314,\r\n      \"totalAmount\": 104.76,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 2.619,\r\n          \"total\": 104.76,\r\n          \"taxable\": 104.76\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 2.619,\r\n          \"total\": 104.76,\r\n          \"taxable\": 104.76\r\n        }\r\n      ],\r\n      \"tax\": 5.238,\r\n      \"originalTax\": 5.238\r\n    },\r\n    {\r\n      \"itemId\": 3,\r\n      \"productId\": 420,\r\n      \"productName\": \"Chocolate Shake\",\r\n      \"productType\": 6,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 161.9,\r\n      \"amount\": 161.9,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"Regular\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 64,\r\n      \"totalAmount\": 161.9,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.0475,\r\n          \"total\": 161.9,\r\n          \"taxable\": 161.9\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.0475,\r\n          \"total\": 161.9,\r\n          \"taxable\": 161.9\r\n        }\r\n      ],\r\n      \"tax\": 8.095,\r\n      \"originalTax\": 8.095\r\n    },\r\n    {\r\n      \"itemId\": 4,\r\n      \"productId\": 1037,\r\n      \"productName\": \"Date & Fig Cake\",\r\n      \"productType\": 10,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 133.33,\r\n      \"amount\": 133.33,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"None\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"recipeId\": 319,\r\n      \"totalAmount\": 133.33,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.3332500000000005,\r\n          \"total\": 133.33,\r\n          \"taxable\": 133.33\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.3332500000000005,\r\n          \"total\": 133.33,\r\n          \"taxable\": 133.33\r\n        }\r\n      ],\r\n      \"tax\": 6.666500000000001,\r\n      \"originalTax\": 6.666500000000001\r\n    },\r\n    {\r\n      \"itemId\": 5,\r\n      \"productId\": 1109,\r\n      \"productName\": \"Baarish Wale Pakore\",\r\n      \"productType\": 7,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 190.48,\r\n      \"amount\": 190.48,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"None\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"recipeId\": 430,\r\n      \"totalAmount\": 190.48,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.762,\r\n          \"total\": 190.48,\r\n          \"taxable\": 190.48\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 4.762,\r\n          \"total\": 190.48,\r\n          \"taxable\": 190.48\r\n        }\r\n      ],\r\n      \"tax\": 9.524,\r\n      \"originalTax\": 9.524\r\n    }\r\n  ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 657.1400000000001,\r\n    \"taxableAmount\": 657.1400000000001,\r\n    \"savings\": 0,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 0,\r\n        \"value\": 0,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": null,\r\n      \"discountCode\": null,\r\n      \"totalDiscount\": 0,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 690,\r\n    \"roundOffValue\": 0.0029999999999290594,\r\n    \"tax\": 32.857,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 16.4285,\r\n        \"total\": 657.1400000000001,\r\n        \"taxable\": 657.1400000000001\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 16.4285,\r\n        \"total\": 657.1400000000001,\r\n        \"taxable\": 657.1400000000001\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"CAFE\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 1,\r\n      \"amount\": 690,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": 12014,\r\n  \"billStartTime\": \"2018-12-28 16:13:59\",\r\n  \"billCreationSeconds\": 92,\r\n  \"billCreationTime\": \"2018-12-28 16:15:31\",\r\n  \"billingServerTime\": \"2018-12-28 16:15:31\",\r\n  \"channelPartner\": 1,\r\n  \"deliveryPartner\": null,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": null,\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": null,\r\n  \"customerName\": \"abhinav\",\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"CUSTOMER_SCREEN_DOWN\",\r\n      \"attributeValue\": \"2018-12-28T16:14:41+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_MODEL_OPEN\",\r\n      \"attributeValue\": \"2018-12-28T16:14:41+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:690,bill:690,change:0\"\r\n    },\r\n    {\r\n      \"attributeName\": \"CUSTOMER_NAME_MANUALLY\",\r\n      \"attributeValue\": \"abhinav\"\r\n    }\r\n  ],\r\n  \"newCustomer\": false,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"isGiftOrder\": false\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create"}, "response": []}, {"name": "View Generated Order Summary", "event": [{"listen": "test", "script": {"id": "65e30d3b-5216-419b-a9ce-3bd22ba12082", "exec": ["", "pm.test(\"Response generated?\", function()", "{", "    pm.response.to.have.status(200);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Item Ordered is 3\", function()", "{", "    pm.expect(Object.keys(jsonData.orders).length).to.eql(3);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22716GrRCL7br9wJd%2BhNjDuj1%2FUEWA5OQpXqaqvkw1xmV8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiNzE2R3JSQ0w3YnI5d0pkK2hOakR1ajEvVUVXQTVPUXBYcWFxdmt3MXhtVjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1OTc3NjEzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.bRVSyPW-a-lcb2wy9K1Rv4Q_1N2Q4Qp4GRBqgeGlV4Y%22%7D%7D; isQZLoaded=true; lastThreeOrders=%7B%22unitId%22%3A12014%2C%22orders%22%3A%5B%226273909680348135%22%2C%226901812525766626%22%5D%7D"}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{{generatedOrderId}}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/generated-order", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "generated-order"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/generated-order"}, "response": []}], "_postman_isSubFolder": true}, {"name": "5_Check for item out of stock", "item": [{"name": "Login_Unit", "event": [{"listen": "test", "script": {"id": "b3a7776d-1018-4268-aed6-4e2d7bfd5972", "exec": ["var jsonData = pm.response.json();    //parsing json response body", "pm.environment.set(\"jwtToken\", jsonData.jwtToken);  // setting jwtToken for further requests", "console.log(pm.environment.get(\"jwtToken\"));  //verifying if it is set or not", "", "/*verifying phone number of logged in user*/", "pm.test(\"is primar contact = 8055852367\", function () {", "    pm.expect(pm.response.text()).to.include(jsonData.user.primaryContact);", "});", "// pm.test(\"Your test name\", function () {", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.user.primaryContact).to.eql(100);", "// });"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiZmM4c3lnSGcxb0VIeThaTWhjeTF4bU9yeEJUZmhwMjd2T2F4NXIvdFhWSjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk3NDAzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.9BGwvnMjWmezmExCezEYq2yBOJ0AMiUocjFHiRb6fh0", "disabled": true}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"unitId\": 10000,\r\n  \"userId\": 120458 , \r\n  \"password\": \"321321\",\r\n  \"terminalId\": 1,\r\n  \"screenType\": \"POS\",\r\n  \"application\": \"KETTLE_SERVICE\"\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "users", "login"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login"}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"id": "05345d59-22aa-4dd9-b567-d5c87fe07b52", "exec": ["// assertion 1 - checking if API has generated the response or not", "var jsonData = pm.response.json();", "pm.test(\"Is response generated\", function()", "{", "    pm.response.to.have.status(200);", "    console.log(\"inside respone generated\");", "});", "", "//assertion 2 - checking for the orderNumber generated or not to verify order creation", "", "pm.test(\"testName\", function()", "{", "pm.expect(jsonData.status!==\"CREATED\");    ", "});", "", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22FDO1WAsHmWGunHd52q5XmoBAOph0vJQ3obW7qp1D7PF8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiRkRPMVdBc0htV0d1bkhkNTJxNVhtb0JBT3BoMHZKUTNvYlc3cXAxRDdQRjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk5OTk0LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.gY02v4R6aj0VeGKbYXagnZYtPoBqUFfbqNfHvBoFgjQ%22%7D%7D; isQZLoaded=true", "disabled": true}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"13785774\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n    {\r\n      \"itemId\": 1,\r\n      \"productId\": 1114,\r\n      \"productName\": \"<PERSON><PERSON>\",\r\n      \"productType\": 5,\r\n      \"billType\": \"NET_PRICE\",\r\n      \"addons\": [\r\n        \r\n      ],\r\n      \"complimentaryDetail\": {\r\n        \"isComplimentary\": false,\r\n        \"reasonCode\": null,\r\n        \"reason\": null\r\n      },\r\n      \"quantity\": 1,\r\n      \"price\": 123.81,\r\n      \"amount\": 123.81,\r\n      \"discountDetail\": {\r\n        \"discountCode\": null,\r\n        \"discountReason\": null,\r\n        \"promotionalOffer\": 0,\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0\r\n        },\r\n        \"totalDiscount\": 0\r\n      },\r\n      \"dimension\": \"None\",\r\n      \"hasBeenRedeemed\": false,\r\n      \"isCombo\": false,\r\n      \"composition\": {\r\n        \"variants\": [\r\n          \r\n        ],\r\n        \"products\": [\r\n          \r\n        ],\r\n        \"menuProducts\": [\r\n          \r\n        ],\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"options\": [\r\n          \r\n        ]\r\n      },\r\n      \"recipeId\": 493,\r\n      \"totalAmount\": 123.81,\r\n      \"code\": \"00009963\",\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 3.0952499999999996,\r\n          \"total\": 123.81,\r\n          \"taxable\": 123.81\r\n        }\r\n      ],\r\n      \"tax\": 6.190499999999999,\r\n      \"originalTax\": 6.190499999999999\r\n    }\r\n  ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 123.81,\r\n    \"taxableAmount\": 123.81,\r\n    \"savings\": 0,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 0,\r\n        \"value\": 0,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": null,\r\n      \"discountCode\": null,\r\n      \"totalDiscount\": 0,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 130,\r\n    \"roundOffValue\": -0.0004999999999881766,\r\n    \"tax\": 6.190499999999999,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 3.0952499999999996,\r\n        \"total\": 123.81,\r\n        \"taxable\": 123.81\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 3.0952499999999996,\r\n        \"total\": 123.81,\r\n        \"taxable\": 123.81\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"CAFE\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 2,\r\n      \"amount\": 130,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": 12014,\r\n  \"billStartTime\": \"2018-12-27 15:24:25\",\r\n  \"billCreationSeconds\": 263,\r\n  \"billCreationTime\": \"2018-12-27 15:28:48\",\r\n  \"billingServerTime\": \"2018-12-27 15:28:48\",\r\n  \"channelPartner\": 1,\r\n  \"deliveryPartner\": null,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": null,\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": null,\r\n  \"customerName\": \"abhinav\",\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"CUSTOMER_SCREEN_DOWN\",\r\n      \"attributeValue\": \"2018-12-27T15:28:22+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_MODEL_OPEN\",\r\n      \"attributeValue\": \"2018-12-27T15:28:22+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"CUSTOMER_NAME_MANUALLY\",\r\n      \"attributeValue\": \"abhinav\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:130,bill:130,change:0\"\r\n    }\r\n  ],\r\n  \"newCustomer\": false,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"isGiftOrder\": false\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create"}, "response": []}], "_postman_isSubFolder": true}, {"name": "6_Check create order with coupon code", "item": [{"name": "Login_Unit", "event": [{"listen": "test", "script": {"id": "b3a7776d-1018-4268-aed6-4e2d7bfd5972", "exec": ["var jsonData = pm.response.json();    //parsing json response body", "pm.environment.set(\"jwtToken\", jsonData.jwtToken);  // setting jwtToken for further requests", "console.log(pm.environment.get(\"jwtToken\"));  //verifying if it is set or not", "", "/*verifying phone number of logged in user*/", "pm.test(\"is primar contact = 8055852367\", function () {", "    pm.expect(pm.response.text()).to.include(jsonData.user.primaryContact);", "});", "// pm.test(\"Your test name\", function () {", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.user.primaryContact).to.eql(100);", "// });"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiZmM4c3lnSGcxb0VIeThaTWhjeTF4bU9yeEJUZmhwMjd2T2F4NXIvdFhWSjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk3NDAzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.9BGwvnMjWmezmExCezEYq2yBOJ0AMiUocjFHiRb6fh0", "disabled": true}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"unitId\": 10000,\r\n  \"userId\": 120458 , \r\n  \"password\": \"321321\",\r\n  \"terminalId\": 1,\r\n  \"screenType\": \"POS\",\r\n  \"application\": \"KETTLE_SERVICE\"\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["master-service", "rest", "v1", "users", "login"]}, "description": "http://dev.kettle.chaayos.com:9595/master-service/rest/v1/users/login"}, "response": []}, {"name": "ApplyCoupon", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22716GrRCL7br9wJd%2BhNjDuj1%2FUEWA5OQpXqaqvkw1xmV8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiNzE2R3JSQ0w3YnI5d0pkK2hOakR1ajEvVUVXQTVPUXBYcWFxdmt3MXhtVjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1OTc3NjEzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.bRVSyPW-a-lcb2wy9K1Rv4Q_1N2Q4Qp4GRBqgeGlV4Y%22%7D%7D; isQZLoaded=true; lastThreeOrders=%7B%22unitId%22%3A12014%2C%22orders%22%3A%5B%226273909680348135%22%2C%226901812525766626%22%2C%229711598419231948%22%5D%7D"}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiNzE2R3JSQ0w3YnI5d0pkK2hOakR1ajEvVUVXQTVPUXBYcWFxdmt3MXhtVjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1OTc3NjEzLCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.bRVSyPW-a-lcb2wy9K1Rv4Q_1N2Q4Qp4GRBqgeGlV4Y"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"order\": {\r\n    \"employeeId\": 120057,\r\n    \"hasParcel\": false,\r\n    \"status\": null,\r\n    \"cancelReason\": null,\r\n    \"orders\": [\r\n      {\r\n        \"itemId\": 6,\r\n        \"productId\": 1169,\r\n        \"productName\": \"Homemade Snacks - Gur Para\",\r\n        \"productType\": 9,\r\n        \"billType\": \"NET_PRICE\",\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"complimentaryDetail\": {\r\n          \"isComplimentary\": false,\r\n          \"reasonCode\": null,\r\n          \"reason\": null\r\n        },\r\n        \"quantity\": 7,\r\n        \"price\": 75.24,\r\n        \"amount\": 526.68,\r\n        \"discountDetail\": {\r\n          \"discountCode\": null,\r\n          \"discountReason\": null,\r\n          \"promotionalOffer\": 0,\r\n          \"discount\": {\r\n            \"percentage\": 0,\r\n            \"value\": 0\r\n          },\r\n          \"totalDiscount\": 0\r\n        },\r\n        \"dimension\": \"None\",\r\n        \"hasBeenRedeemed\": false,\r\n        \"isCombo\": false,\r\n        \"recipeId\": 586,\r\n        \"totalAmount\": 526.68,\r\n        \"code\": \"21069040\",\r\n        \"taxes\": [\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"CGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 13.166999999999998,\r\n            \"total\": 526.68,\r\n            \"taxable\": 526.68\r\n          },\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"SGST/UTGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 13.166999999999998,\r\n            \"total\": 526.68,\r\n            \"taxable\": 526.68\r\n          }\r\n        ],\r\n        \"tax\": 26.333999999999996,\r\n        \"originalTax\": 26.333999999999996\r\n      },\r\n      {\r\n        \"itemId\": 8,\r\n        \"productId\": 1165,\r\n        \"productName\": \"Spiced Chai Patti - Adrak Elaichi Chai\",\r\n        \"productType\": 9,\r\n        \"billType\": \"MRP\",\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"complimentaryDetail\": {\r\n          \"isComplimentary\": false,\r\n          \"reasonCode\": null,\r\n          \"reason\": null\r\n        },\r\n        \"quantity\": 4,\r\n        \"price\": 122.86,\r\n        \"amount\": 491.44,\r\n        \"discountDetail\": {\r\n          \"discountCode\": null,\r\n          \"discountReason\": null,\r\n          \"promotionalOffer\": 0,\r\n          \"discount\": {\r\n            \"percentage\": 0,\r\n            \"value\": 0\r\n          },\r\n          \"totalDiscount\": 0\r\n        },\r\n        \"dimension\": \"None\",\r\n        \"hasBeenRedeemed\": false,\r\n        \"isCombo\": false,\r\n        \"recipeId\": 581,\r\n        \"totalAmount\": 491.44,\r\n        \"code\": \"09021090\",\r\n        \"taxes\": [\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"CGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 12.286,\r\n            \"total\": 491.44,\r\n            \"taxable\": 491.44\r\n          },\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"SGST/UTGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 12.286,\r\n            \"total\": 491.44,\r\n            \"taxable\": 491.44\r\n          }\r\n        ],\r\n        \"tax\": 24.572,\r\n        \"originalTax\": 24.572\r\n      },\r\n      {\r\n        \"itemId\": 9,\r\n        \"productId\": 1144,\r\n        \"productName\": \"Premium Diwali Gift Box\",\r\n        \"productType\": 9,\r\n        \"billType\": \"NET_PRICE\",\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"complimentaryDetail\": {\r\n          \"isComplimentary\": false,\r\n          \"reasonCode\": null,\r\n          \"reason\": null\r\n        },\r\n        \"quantity\": 2,\r\n        \"price\": 760.95,\r\n        \"amount\": 1521.9,\r\n        \"discountDetail\": {\r\n          \"discountCode\": null,\r\n          \"discountReason\": null,\r\n          \"promotionalOffer\": 0,\r\n          \"discount\": {\r\n            \"percentage\": 0,\r\n            \"value\": 0\r\n          },\r\n          \"totalDiscount\": 0\r\n        },\r\n        \"dimension\": \"None\",\r\n        \"hasBeenRedeemed\": false,\r\n        \"isCombo\": false,\r\n        \"recipeId\": 537,\r\n        \"totalAmount\": 1521.9,\r\n        \"code\": \"09109100\",\r\n        \"taxes\": [\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"CGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 38.0475,\r\n            \"total\": 1521.9,\r\n            \"taxable\": 1521.9\r\n          },\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"SGST/UTGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 38.0475,\r\n            \"total\": 1521.9,\r\n            \"taxable\": 1521.9\r\n          }\r\n        ],\r\n        \"tax\": 76.095,\r\n        \"originalTax\": 76.095\r\n      }\r\n    ],\r\n    \"transactionDetail\": {\r\n      \"totalAmount\": 2540.02,\r\n      \"taxableAmount\": 2540.02,\r\n      \"savings\": 0,\r\n      \"discountDetail\": {\r\n        \"discount\": {\r\n          \"percentage\": 0,\r\n          \"value\": 0,\r\n          \"wasValueSet\": false\r\n        },\r\n        \"discountReason\": null,\r\n        \"discountCode\": null,\r\n        \"totalDiscount\": 0,\r\n        \"promotionalOffer\": 0\r\n      },\r\n      \"paidAmount\": 2667,\r\n      \"roundOffValue\": -0.02099999999973079,\r\n      \"tax\": 127.00099999999999,\r\n      \"taxes\": [\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"CGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 63.500499999999995,\r\n          \"total\": 2540.02,\r\n          \"taxable\": 2540.02\r\n        },\r\n        {\r\n          \"type\": \"GST\",\r\n          \"code\": \"SGST/UTGST\",\r\n          \"percentage\": 2.5,\r\n          \"value\": 63.500499999999995,\r\n          \"total\": 2540.02,\r\n          \"taxable\": 2540.02\r\n        }\r\n      ]\r\n    },\r\n    \"settlementType\": \"DEBIT\",\r\n    \"source\": \"CAFE\",\r\n    \"settlements\": null,\r\n    \"unitId\": 12014,\r\n    \"billStartTime\": null,\r\n    \"billCreationSeconds\": null,\r\n    \"billCreationTime\": null,\r\n    \"billingServerTime\": null,\r\n    \"channelPartner\": 1,\r\n    \"customerId\": 65,\r\n    \"deliveryPartner\": null,\r\n    \"pointsRedeemed\": null,\r\n    \"terminalId\": 1\r\n  },\r\n  \"couponCode\": \"ZZZZZ\",\r\n  \"newCustomer\": true\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/customer-offer-management/coupon/apply", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "customer-offer-management", "coupon", "apply"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/customer-offer-management/coupon/apply"}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"id": "05345d59-22aa-4dd9-b567-d5c87fe07b52", "exec": ["// assertion 1 - checking if API has generated the response or not", "var jsonData = pm.response.json();", "pm.test(\"Is response generated\", function()", "{", "    pm.response.to.have.status(200);", "    console.log(\"inside respone generated\");", "});", "", "//assertion 2 - checking for the orderNumber generated or not to verify order creation", "", "pm.test(\"testName\", function()", "{", "pm.expect(jsonData.status!==\"CREATED\");    ", "});", "", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "pointsRedeemed=%7B%22pointsRedeemed%22%3A0%2C%22pointsRedeemedSuccessfully%22%3Afalse%7D; globals=%7B%22currentUser%22%3A%7B%22userId%22%3A120057%2C%22unitId%22%3A12014%2C%22terminalId%22%3A1%2C%22sessionKeyId%22%3A%22FDO1WAsHmWGunHd52q5XmoBAOph0vJQ3obW7qp1D7PF8LhEjvJZaMRKS%2Bt%2FzUv7nNLhx3sgy1LEZ%5CnRHTav7FG1UmWdJf%2BJ%2BhORFrzQ6LjCtQ%3D%22%2C%22userName%22%3A%22Mayank%20Malik%22%2C%22designation%22%3A%7B%22id%22%3A1009%2C%22name%22%3A%22Admin%22%2C%22description%22%3A%22General%20Kettle%20Admin%22%2C%22transactionSystemAccess%22%3Atrue%2C%22scmSystemAccess%22%3Atrue%2C%22adminSystemAccess%22%3Atrue%2C%22clmSystemAccess%22%3Atrue%2C%22analyticsSystemAccess%22%3Atrue%2C%22crmSystemAccess%22%3Atrue%2C%22formsSystemAccess%22%3Atrue%2C%22channelPartnerSystemAccess%22%3Atrue%2C%22appInstallerAccess%22%3Atrue%7D%2C%22screenType%22%3A%22POS%22%2C%22unitFamily%22%3A%22CAFE%22%2C%22issuer%22%3A%22KETTLE_SERVICE%22%2C%22jwtToken%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiRkRPMVdBc0htV0d1bkhkNTJxNVhtb0JBT3BoMHZKUTNvYlc3cXAxRDdQRjhMaEVqdkpaYU1SS1MrdC96VXY3bk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEyMDE0LCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNTQ1ODk5OTk0LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.gY02v4R6aj0VeGKbYXagnZYtPoBqUFfbqNfHvBoFgjQ%22%7D%7D; isQZLoaded=true", "disabled": true}, {"key": "Origin", "value": "http://dev.kettle.chaayos.com:9595"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Basic"}, {"key": "auth", "value": "{{jwtToken}}"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "<PERSON><PERSON><PERSON>", "value": "http://dev.kettle.chaayos.com:9595/kettle-service/"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"}, {"key": "Connection", "value": "keep-alive"}], "body": {"mode": "raw", "raw": "{\r\n  \"generateOrderId\": \"39476174\",\r\n  \"employeeId\": 120057,\r\n  \"hasParcel\": false,\r\n  \"status\": \"CREATED\",\r\n  \"cancelReason\": null,\r\n  \"orders\": [\r\n      {\r\n        \"itemId\": 6,\r\n        \"productId\": 1169,\r\n        \"productName\": \"Homemade Snacks - Gur Para\",\r\n        \"productType\": 9,\r\n        \"billType\": \"NET_PRICE\",\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"complimentaryDetail\": {\r\n          \"isComplimentary\": false,\r\n          \"reasonCode\": null,\r\n          \"reason\": null\r\n        },\r\n        \"quantity\": 7,\r\n        \"price\": 75.24,\r\n        \"amount\": 99999.68,\r\n        \"discountDetail\": {\r\n          \"discountCode\": null,\r\n          \"discountReason\": null,\r\n          \"promotionalOffer\": 0,\r\n          \"discount\": {\r\n            \"percentage\": 0,\r\n            \"value\": 0\r\n          },\r\n          \"totalDiscount\": 0\r\n        },\r\n        \"dimension\": \"None\",\r\n        \"hasBeenRedeemed\": false,\r\n        \"isCombo\": false,\r\n        \"recipeId\": 586,\r\n        \"totalAmount\": 5999926.68,\r\n        \"code\": \"21069040\",\r\n        \"taxes\": [\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"CGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 13.166999999999998,\r\n            \"total\": 526.68,\r\n            \"taxable\": 526.68\r\n          },\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"SGST/UTGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 13.166999999999998,\r\n            \"total\": 526.68,\r\n            \"taxable\": 526.68\r\n          }\r\n        ],\r\n        \"tax\": 26.333999999999996,\r\n        \"originalTax\": 26.333999999999996\r\n      },\r\n      {\r\n        \"itemId\": 8,\r\n        \"productId\": 1165,\r\n        \"productName\": \"Spiced Chai Patti - Adrak Elaichi Chai\",\r\n        \"productType\": 9,\r\n        \"billType\": \"MRP\",\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"complimentaryDetail\": {\r\n          \"isComplimentary\": false,\r\n          \"reasonCode\": null,\r\n          \"reason\": null\r\n        },\r\n        \"quantity\": 4,\r\n        \"price\": 122.86,\r\n        \"amount\": 49991.44,\r\n        \"discountDetail\": {\r\n          \"discountCode\": null,\r\n          \"discountReason\": null,\r\n          \"promotionalOffer\": 0,\r\n          \"discount\": {\r\n            \"percentage\": 0,\r\n            \"value\": 0\r\n          },\r\n          \"totalDiscount\": 0\r\n        },\r\n        \"dimension\": \"None\",\r\n        \"hasBeenRedeemed\": false,\r\n        \"isCombo\": false,\r\n        \"recipeId\": 581,\r\n        \"totalAmount\": 491.44,\r\n        \"code\": \"09021090\",\r\n        \"taxes\": [\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"CGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 12.286,\r\n            \"total\": 491.44,\r\n            \"taxable\": 491.44\r\n          },\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"SGST/UTGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 12.286,\r\n            \"total\": 4999991.44,\r\n            \"taxable\": 4991.44\r\n          }\r\n        ],\r\n        \"tax\": 24.572,\r\n        \"originalTax\": 24.572\r\n      },\r\n      {\r\n        \"itemId\": 9,\r\n        \"productId\": 1144,\r\n        \"productName\": \"Premium Diwali Gift Box\",\r\n        \"productType\": 9,\r\n        \"billType\": \"NET_PRICE\",\r\n        \"addons\": [\r\n          \r\n        ],\r\n        \"complimentaryDetail\": {\r\n          \"isComplimentary\": false,\r\n          \"reasonCode\": null,\r\n          \"reason\": null\r\n        },\r\n        \"quantity\": 2,\r\n        \"price\": 79999960.95,\r\n        \"amount\": 15999921.9,\r\n        \"discountDetail\": {\r\n          \"discountCode\": null,\r\n          \"discountReason\": null,\r\n          \"promotionalOffer\": 0,\r\n          \"discount\": {\r\n            \"percentage\": 0,\r\n            \"value\": 0\r\n          },\r\n          \"totalDiscount\": 0\r\n        },\r\n        \"dimension\": \"None\",\r\n        \"hasBeenRedeemed\": false,\r\n        \"isCombo\": false,\r\n        \"recipeId\": 537,\r\n        \"totalAmount\": 19.9,\r\n        \"code\": \"09109100\",\r\n        \"taxes\": [\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"CGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 38.0475,\r\n            \"total\": 1521.9,\r\n            \"taxable\": 1521.9\r\n          },\r\n          {\r\n            \"type\": \"GST\",\r\n            \"code\": \"SGST/UTGST\",\r\n            \"percentage\": 2.5,\r\n            \"value\": 38.0475,\r\n            \"total\": 1521.9,\r\n            \"taxable\": 1521.9\r\n          }\r\n        ],\r\n        \"tax\": 76.095,\r\n        \"originalTax\": 76.095\r\n      }\r\n    ],\r\n  \"transactionDetail\": {\r\n    \"totalAmount\": 2540.02,\r\n    \"taxableAmount\": 2286.02,\r\n    \"savings\": 267,\r\n    \"discountDetail\": {\r\n      \"discount\": {\r\n        \"percentage\": 10,\r\n        \"value\": 254,\r\n        \"wasValueSet\": false\r\n      },\r\n      \"discountReason\": \"ZZZZZ\",\r\n      \"discountCode\": 2004,\r\n      \"totalDiscount\": 254,\r\n      \"promotionalOffer\": 0\r\n    },\r\n    \"paidAmount\": 0,\r\n    \"roundOffValue\": -0.3209999999999127,\r\n    \"tax\": 114.30099999999999,\r\n    \"taxes\": [\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"CGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 57.1505,\r\n        \"total\": 2540.02,\r\n        \"taxable\": 2286.02\r\n      },\r\n      {\r\n        \"type\": \"GST\",\r\n        \"code\": \"SGST/UTGST\",\r\n        \"percentage\": 2.5,\r\n        \"value\": 57.1505,\r\n        \"total\": 10.02,\r\n        \"taxable\": 2286.02\r\n      }\r\n    ]\r\n  },\r\n  \"settlementType\": \"DEBIT\",\r\n  \"source\": \"CAFE\",\r\n  \"settlements\": [\r\n    {\r\n      \"mode\": 1,\r\n      \"amount\": 24,\r\n      \"externalSettlements\": null\r\n    }\r\n  ],\r\n  \"unitId\": 12014,\r\n  \"billStartTime\": \"2018-12-28 17:31:53\",\r\n  \"billCreationSeconds\": 180,\r\n  \"billCreationTime\": \"2018-12-28 17:34:53\",\r\n  \"billingServerTime\": \"2018-12-28 17:34:53\",\r\n  \"channelPartner\": 1,\r\n  \"deliveryPartner\": null,\r\n  \"pointsRedeemed\": 0,\r\n  \"terminalId\": 1,\r\n  \"tableNumber\": null,\r\n  \"awardLoyalty\": true,\r\n  \"offerCode\": \"ZZZZZ\",\r\n  \"subscriptionDetail\": null,\r\n  \"customerId\": 65,\r\n  \"customerName\": \"Abhinav\",\r\n  \"enquiryItems\": [\r\n    \r\n  ],\r\n  \"metadataList\": [\r\n    {\r\n      \"attributeName\": \"EMAIL_REQUESTED\",\r\n      \"attributeValue\": \"Y\"\r\n    },\r\n    {\r\n      \"attributeName\": \"NEW_CUSTOMER\",\r\n      \"attributeValue\": \"Y\"\r\n    },\r\n    {\r\n      \"attributeName\": \"REDEMPTION_AVAILABLE\",\r\n      \"attributeValue\": 0\r\n    },\r\n    {\r\n      \"attributeName\": \"COUPON_CODE_APPLIED\",\r\n      \"attributeValue\": \"EMP35::BO10OFF::BO10OFF::BO10OFF\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_MODEL_OPEN\",\r\n      \"attributeValue\": \"2018-12-28T17:34:48+05:30\"\r\n    },\r\n    {\r\n      \"attributeName\": \"SETTLEMENT_AMOUNT\",\r\n      \"attributeValue\": \"amount:0000000,bill:2400,change:0\"\r\n    }\r\n  ],\r\n  \"containsSignupOffer\": false,\r\n  \"newCustomer\": true,\r\n  \"optionResultEventId\": null,\r\n  \"orderType\": \"order\",\r\n  \"isGiftOrder\": false\r\n}"}, "url": {"raw": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create", "protocol": "http", "host": ["dev", "kettle", "chaayos", "com"], "port": "9595", "path": ["kettle-service", "rest", "v1", "order-management", "order", "create"]}, "description": "http://dev.kettle.chaayos.com:9595/kettle-service/rest/v1/order-management/order/create"}, "response": []}], "_postman_isSubFolder": true}]}], "event": [{"listen": "prerequest", "script": {"id": "02d024d5-e82a-442a-bfa6-303cd73b0db6", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "cef4e096-60fb-4421-8c55-558442b3c8ad", "type": "text/javascript", "exec": [""]}}], "variable": [{"id": "7741735f-873c-4be2-8c44-9df7a08974f2", "key": "generatedOrderId", "value": "6901812525766626", "type": "string"}]}