/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.notification;

import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import com.stpl.tech.kettle.core.TestUtil;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EmailGenerationException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { TransactionConfig.class }, loader = AnnotationConfigContextLoader.class)
public class ReceiptNotificationTest {

	@Autowired
	private OrderManagementService metadataDao;
	@Autowired
	private OrderSearchService searchDao;
	@Autowired
	private CustomerService customerDao;
	@Autowired
	private EnvironmentProperties props;
	@Autowired
	MasterDataCache masterCache;
	@Autowired
	MetadataCache cache;

	@Autowired
	CustomerOfferManagementService customerOfferManagementService;

	@Test
	public void testReceiptEmail() throws DataNotFoundException, DataUpdationException, EmailGenerationException {
		Customer cust = TestUtil.getCustomer("9599597741", "<EMAIL>", "Batman");
		Customer custId = customerDao.addCustomer(cust);
		int orderId = metadataDao.createOrder(TestUtil.getOrder(custId.getId(), 1)).getOrderId();
		Order order = searchDao.getOrderDetail(orderId);
		Customer customer = customerDao.getCustomer(custId.getId());
		OrderInfo receipt = new OrderInfo(props.getEnvironmentType(), order, customer, getUnit(),
				cache.getDeliveryPartner(order.getDeliveryPartner()),
				masterCache.getChannelPartner(order.getChannelPartner()));
		CustomerEmailData customerEmailData = customerOfferManagementService.getCustomerEmailData(customer.getId(), order.getBrandId());
		ReceiptNotification c = new ReceiptNotification(
				new OrderEmailReceipt("https://cafes.chaayos.com/", masterCache.getUnit(receipt.getOrder().getUnitId()), receipt,
						props.getRecieptEmail(), props.getToEmail(), props.getBasePath(), true, null, null, null,false, customerEmailData),
				true);
		c.body();
	}

	private Unit getUnit() {
		Unit unit = new Unit();
		unit.setAddress(getAddress());
		unit.setName("Good Earth City Centre");
		unit.setTin("06851834097");
		return unit;
	}

	private Address getAddress() {
		Address add = new Address();
		add.setLine1("No-71, Ground Floor,Good Earth City Centre");
		add.setContact1("+91-9717779761");
		add.setCity("Gurgaon");
		return add;
	}
}
