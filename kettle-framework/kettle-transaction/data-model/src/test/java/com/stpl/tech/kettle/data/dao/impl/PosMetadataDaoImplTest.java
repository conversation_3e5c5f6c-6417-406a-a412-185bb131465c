/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.core.TestUtil;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppUtils;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { TransactionConfig.class }, loader = AnnotationConfigContextLoader.class)
public class PosMetadataDaoImplTest {

	@Autowired
	private PosMetadataService posDao;
	@Autowired
	private OrderManagementService orderdao;

	@Test
	public void testCreateMultipleOrders() throws DataUpdationException, DataNotFoundException {
		int orderId = 0;
		for (int i = 1; i <= 2; i++) {
			orderId = orderdao.createOrder(TestUtil.getOrder(1, i)).getOrderId();
			Assert.assertEquals(true, orderId >= 1);
		}
		posDao.closeDay(10000, 100002, "test", 1, orderId, AppUtils.getCurrentDate());
	}
}
