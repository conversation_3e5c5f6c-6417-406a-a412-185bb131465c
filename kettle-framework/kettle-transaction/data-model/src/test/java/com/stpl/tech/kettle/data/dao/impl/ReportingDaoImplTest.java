/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import com.stpl.tech.kettle.core.ReportDefinitionEnum;
import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.data.model.ReportExecutionDetail;
import com.stpl.tech.master.core.exception.DataUpdationException;

import junit.framework.Assert;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { TransactionConfig.class }, loader = AnnotationConfigContextLoader.class)
public class ReportingDaoImplTest {
	@Autowired
	private ReportingService metadataDao;
	private static final Logger LOG = LoggerFactory.getLogger(ReportingDaoImplTest.class);

	@Test
	public void testCreateReportExecution() {
		ReportExecutionDetail id = new ReportExecutionDetail();
		try {
			id = metadataDao.createReportExecutionDetail(10000,
					ReportDefinitionEnum.UNIT_ADHOC_REPORT.getReportDefId());
		} catch (DataUpdationException e) {
			LOG.error("Error in creating report execution detail", e);
		}
		ReportExecutionDetail detail = metadataDao.getReportExecutionDetail(id.getExecutionDetailId());
		Assert.assertEquals(ReportStatus.CREATED.name(), detail.getCurrentStatus());
		try {
			metadataDao.updateStatus(id.getExecutionDetailId(), ReportStatus.RUNNING);
		} catch (DataUpdationException e) {
			LOG.error("Error in updating report execution detail", e);
		}
		detail = metadataDao.getReportExecutionDetail(id.getExecutionDetailId());
		Assert.assertEquals(ReportStatus.RUNNING.name(), detail.getCurrentStatus());
	}
}
