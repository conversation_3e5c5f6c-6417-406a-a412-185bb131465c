/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import com.stpl.tech.kettle.core.TestUtil;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { TransactionConfig.class }, loader = AnnotationConfigContextLoader.class)
public class OrderManagementDaoImplTest {

	@Autowired
	private OrderManagementService metadataDao;
	@Autowired
	private OrderSearchService searchDao;

	@Autowired
	private CustomerService customerDao;

	@Test
	public void testCreateOrder() throws DataUpdationException, DataNotFoundException {
		Customer cust = createCustomer();
		int orderId = metadataDao.createOrder(TestUtil.getOrder(cust.getId(), 1)).getOrderId();
		Assert.assertEquals(true, orderId >= 1);
	}

	private Customer createCustomer() throws DataUpdationException {
		Customer cust = TestUtil.getCustomer("9599597740", "<EMAIL>", "Batman");
		return customerDao.addCustomer(cust);
	}

	@Test
	public void testCreateMultipleOrders() throws DataUpdationException, DataNotFoundException {
		Customer cust = createCustomer();
		for (int i = 1; i <= 100; i++) {
			int orderId = metadataDao.createOrder(TestUtil.getOrder(cust.getId(), i)).getOrderId();
			Assert.assertEquals(true, orderId >= 1);
		}
	}

	@Test
	public void testGetMultipleOrders() throws DataUpdationException, DataNotFoundException, InterruptedException {
		Customer cust = createCustomer();
		Thread.sleep(6000);
		int orderId = metadataDao.createOrder(TestUtil.getOrder(cust.getId(), 1)).getOrderId();
		Assert.assertEquals(true, orderId >= 1);
		List<Order> orders = searchDao.getOrderDetails(10000, searchDao.getLastDayCloseOrderId(10000),
				Integer.MAX_VALUE, null);
		Assert.assertEquals(1, orders.size());
		Thread.sleep(6000);
		orderId = metadataDao.createOrder(TestUtil.getOrder(cust.getId(), 1)).getOrderId();
		Assert.assertEquals(true, orderId >= 1);
		orders = searchDao.getOrderDetails(10000, searchDao.getLastDayCloseOrderId(10000), Integer.MAX_VALUE, null);
		Assert.assertEquals(1, orders.size());

		List<OrderEmailNotification> emails = searchDao.getEmailEvents();
		System.out.println(emails.size());
	}

	@Test
	public void testGetMultipleSettlements() throws DataUpdationException, DataNotFoundException {
		Customer cust = createCustomer();
		Date startTime = new Date();
		for (int i = 1; i <= 100; i++) {
			int orderId = metadataDao.createOrder(TestUtil.getOrder(cust.getId(), i)).getOrderId();
			Assert.assertEquals(true, orderId >= 1);
		}
		Date endTime = new Date();
		List<Settlement> orders = searchDao.getSettlementDetails(10000, startTime, endTime);
		Assert.assertEquals(100, orders.size());
	}

	@Ignore
	@Test(expected = DataUpdationException.class)
	public void testCreateOrderWithOrderIdSet() throws DataUpdationException, DataNotFoundException {
		Customer cust = createCustomer();
		Order order = TestUtil.getOrder(cust.getId(), 1);
		int orderId = metadataDao.createOrder(order).getOrderId();
		Assert.assertEquals(true, orderId >= 1);
		order.setOrderId(orderId);
		orderId = metadataDao.createOrder(order).getOrderId();
	}

}
