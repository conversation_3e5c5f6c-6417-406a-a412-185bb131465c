/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.MasterObjectFactory;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { TransactionConfig.class }, loader = AnnotationConfigContextLoader.class)
public class CustomerDaoImplTest {

	@Autowired
	private CustomerService metadataDao;

	private static final ObjectFactory objectFactory = new ObjectFactory();
	private static final MasterObjectFactory masterObjectFactory = new MasterObjectFactory();

	@Test
	public void testCreateCustomer() throws DataUpdationException, DataNotFoundException {
		Customer customer = metadataDao.addCustomer(create("8888888888", "Test", "<EMAIL>"));

		Assert.assertEquals("8888888888", customer.getContactNumber());
		Assert.assertEquals("Test", customer.getFirstName());
		Assert.assertEquals("<EMAIL>", customer.getEmailId());

	}

	@Test
	public void testCreateCustomerThatAlreadyExist() throws DataUpdationException, DataNotFoundException {
		Customer customer = metadataDao.addCustomer(create("8888888881", "Test", "<EMAIL>"));

		Assert.assertEquals("8888888881", customer.getContactNumber());
		Assert.assertEquals("Test", customer.getFirstName());
		Assert.assertEquals("<EMAIL>", customer.getEmailId());
		Customer customer1 = metadataDao.addCustomer(create("8888888881", "Test", "<EMAIL>"));
		Assert.assertEquals(customer.getId(), customer1.getId());

	}

	@Test
	public void testCreateCustomerWithAddress() throws DataUpdationException, DataNotFoundException {
		Customer customer = metadataDao.addCustomer(create("8888888882", "Test", "<EMAIL>", "Udyog vihar",
				"Gurgaon", "Haryana", "India", "122001"));
		Assert.assertEquals("8888888882", customer.getContactNumber());
		Assert.assertEquals("Test", customer.getFirstName());
		Assert.assertEquals("<EMAIL>", customer.getEmailId());
		Assert.assertEquals(1, customer.getAddresses().size());
	}

	@Test
	public void testAddAddress() throws DataUpdationException, DataNotFoundException {
		Customer customer = metadataDao.addCustomer(create("8888888883", "Test", "<EMAIL>", "Udyog vihar",
				"Gurgaon", "Haryana", "India", "122001"));
		Assert.assertEquals("8888888883", customer.getContactNumber());
		Assert.assertEquals("Test", customer.getFirstName());
		Assert.assertEquals("<EMAIL>", customer.getEmailId());
		Assert.assertEquals(1, customer.getAddresses().size());
		Assert.assertEquals("Udyog vihar", customer.getAddresses().get(0).getLine1());
		customer = metadataDao.getCustomer("8888888883");
		Address add1 = createAddress("Sector 49", "Gurgaon", "Haryana", "India", "122001");
		metadataDao.addAddress(customer.getId(), add1);
		customer = metadataDao.getCustomer("8888888883");
		Assert.assertEquals(2, customer.getAddresses().size());
		Assert.assertEquals("Sector 49", customer.getAddresses().get(1).getLine1());
		Address add2 = createAddress("Sector 51", "Gurgaon", "Haryana", "India", "122001");
		metadataDao.addAddress(customer.getId(), add2);
		customer = metadataDao.getCustomer("8888888883");
		Assert.assertEquals(3, customer.getAddresses().size());
		Assert.assertEquals("Sector 51", customer.getAddresses().get(2).getLine1());

	}

	private Customer create(String number, String fName, String email, String line1, String city, String state,
			String country, String zip) {
		Customer customer = create(number, fName, email);
		customer.getAddresses().add(createAddress(line1, city, state, country, zip));
		return customer;

	}

	private Address createAddress(String line1, String city, String state, String country, String zip) {
		Address address = masterObjectFactory.createAddress();
		address.setAddressType(TransactionConstants.DEFAULT_ADDRESS_TYPE);
		address.setLine1(line1);
		address.setCity(city);
		address.setState(state);
		address.setCountry(country);
		address.setZipCode(zip);
		return address;
	}

	private Customer create(String number, String fName, String email) {
		Customer customer = objectFactory.createCustomer();
		customer.setContactNumber(number);
		customer.setFirstName(fName);
		customer.setEmailId(email);
		customer.setCountryCode(TransactionConstants.DEFAULT_COUNTRY_CODE);
		return customer;
	}
}
