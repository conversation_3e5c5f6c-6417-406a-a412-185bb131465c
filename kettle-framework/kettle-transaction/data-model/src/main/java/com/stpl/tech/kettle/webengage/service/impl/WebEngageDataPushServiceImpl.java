package com.stpl.tech.kettle.webengage.service.impl;

import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderMetadataDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.webengage.data.dao.WebEngageDataPushDao;
import com.stpl.tech.kettle.webengage.data.model.CustomerDataPushTrack;
import com.stpl.tech.kettle.webengage.data.model.OrderDataPushTrack;
import com.stpl.tech.kettle.webengage.domain.model.WebengageEvent;
import com.stpl.tech.kettle.webengage.service.WebEngageDataPushService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.stpl.tech.kettle.webengage.converter.WebEngageConverter.convert;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Service
public class WebEngageDataPushServiceImpl implements WebEngageDataPushService {

    @Autowired
    private WebEngageDataPushDao webEngageDataPushDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public CustomerInfo getCustomerInfo(Integer id) {
        return webEngageDataPushDao.find(CustomerInfo.class, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public CustomerAddressInfo getCustomerAddressInfo(Integer id) {
        return webEngageDataPushDao.find(CustomerAddressInfo.class, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<CustomerInfo> getCustomerBatch(Integer startUserId, Integer batchSize) {
        return webEngageDataPushDao.getCustomerBatch(startUserId, batchSize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<LoyaltyScore> getCustomerLoyaltyScores(Collection<Integer> customerIds) {
        return webEngageDataPushDao.getCustomerLoyaltyScores(customerIds.stream().collect(Collectors.toSet()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<Integer> getSettledKettleOrderBatch(Integer startOrderId, Integer batchSize) {
        return webEngageDataPushDao.getSettledKettleOrderBatch(startOrderId, batchSize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<OrderMetadataDetail> getOrderMetadataDetail(Integer orderId) {
        return webEngageDataPushDao.getOrderMetadataDetail(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public WebengageEvent getOrderBatch(Integer orderId) {
        //List<OrderDetail> orderDetails = webEngageDataPushDao.getSettledKettleOrderBatch(startOrderId, batchSize);
        //for (OrderDetail orderDetail:orderDetails) {
        OrderDetail orderDetail = webEngageDataPushDao.find(OrderDetail.class, orderId);
            CustomerInfo customerInfo = webEngageDataPushDao.find(CustomerInfo.class, orderDetail.getCustomerId());
            CustomerAddressInfo addressInfo = null;
            if(orderDetail.getDeliveryAddress()!=null){
                addressInfo = webEngageDataPushDao.find(CustomerAddressInfo.class, orderDetail.getDeliveryAddress());
            }
            List<String> modes = orderDetail.getOrderSettlements().stream().map(orderSettlement -> masterDataCache.getPaymentMode(orderSettlement.getPaymentModeId()).getName()).collect(Collectors.toList());
            //List<OrderMetadataDetail> orderMetadataDetails = webEngageDataPushDao.getOrderMetadataDetail(orderDetail.getOrderId());
            List<String> settlements = new ArrayList<>();
            for(OrderSettlement orderSettlement:orderDetail.getOrderSettlements()){
                StringBuilder item = new StringBuilder();
                item.append(masterDataCache.getPaymentMode(orderSettlement.getPaymentModeId()).getName()).append("x").append(orderSettlement.getAmountPaid().floatValue());
                settlements.add(item.toString());
            }
            /*List<WebengageOrderSettlement> webengageOrderSettlements = new ArrayList<>();
            for (OrderSettlement orderSettlement:orderDetail.getOrderSettlements()) {
                WebengageOrderSettlement webengageOrderSettlement = new WebengageOrderSettlement();
                webengageOrderSettlement.setAmount(orderSettlement.getAmountPaid().floatValue());
                List<OrderPaymentDenomination> orderPaymentDenominations = new ArrayList<>();
                for (OrderPaymentDenominationDetail orderPaymentDenominationDetail:orderSettlement.getDenominations()) {
                    orderPaymentDenominations.add(convert(orderPaymentDenominationDetail));
                }
                webengageOrderSettlement.setDenominations(orderPaymentDenominations);
                webengageOrderSettlement.setExtraVouchers(orderSettlement.getExtraVouchers()!=null?orderSettlement.getExtraVouchers().floatValue():null);
                webengageOrderSettlement.setMode(masterDataCache.getPaymentMode(orderSettlement.getPaymentModeId()).getName());
                //webengageOrderSettlement.setModeDetail(masterDataCache.getPaymentMode(orderSettlement.getPaymentModeId()));
                webengageOrderSettlements.add(webengageOrderSettlement);
            }*/
            WebengageEvent webengageEvent = convert(orderDetail, customerInfo,
                masterDataCache.getUnit(orderDetail.getUnitId()).getName(), addressInfo, modes, String.join(",",settlements));
            //webengageEvents.put(orderDetail.getOrderId(), webengageEvent);
        //}
        return webengageEvent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void persistTrackOrder(OrderDataPushTrack entity) {
        webEngageDataPushDao.add(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void persistTrackCustomer(CustomerDataPushTrack entity) {
        webEngageDataPushDao.add(entity);
    }

    @Override
    public OrderDataPushTrack getStartingBizDateAndOrder() {
        List<OrderDataPushTrack> orderDataPushTracks = webEngageDataPushDao.findLastOrderTrack();
        if (orderDataPushTracks != null && orderDataPushTracks.size() > 0) {
            return orderDataPushTracks.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OrderDetail> getOrdersByBizDate(Date bizDate) {
        return webEngageDataPushDao.findOrdersFromBizDate(bizDate);
    }
}
