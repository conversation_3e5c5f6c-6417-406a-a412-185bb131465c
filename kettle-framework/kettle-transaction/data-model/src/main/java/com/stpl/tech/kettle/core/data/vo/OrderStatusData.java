/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.data.vo;

import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.UnitCategory;

public class OrderStatusData {

	private int orderId;
	private UnitCategory category;
	private int unitId;
	private OrderStatus status;

	public OrderStatusData(int orderId, int unitId, OrderStatus status, UnitCategory category) {
		super();
		this.orderId = orderId;
		this.category = category;
		this.unitId = unitId;
		this.status = status;
	}

	public int getOrderId() {
		return orderId;
	}

	public OrderStatus getStatus() {
		return status;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Override
	public String toString() {
		return "OrderStatusData{" + "orderId=" + orderId + ", unitId=" + unitId + ", status=" + status + '}';
	}

	public UnitCategory getCategory() {
		return category;
	}

	public void setCategory(UnitCategory category) {
		this.category = category;
	}

}
