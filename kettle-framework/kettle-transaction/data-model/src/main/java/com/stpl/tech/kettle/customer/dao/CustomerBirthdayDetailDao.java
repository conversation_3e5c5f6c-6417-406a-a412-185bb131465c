package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.master.data.dao.AbstractMasterDao;
import com.stpl.tech.master.data.model.CustomerEventDetailData;
import com.stpl.tech.master.util.CustomerEventType;

import java.util.List;

public interface CustomerBirthdayDetailDao extends AbstractMasterDao {
    List<CustomerEventDetailData> findByCustomerId(Integer customerId);
    CustomerEventDetailData findByCustomerIdAndEventType(Integer customerId, CustomerEventType eventType);
    CustomerEventDetailData findByContactNumberAndEventType(String contactNumber, CustomerEventType eventType);
    List<CustomerEventDetailData> findByEventMonth(Integer eventMonth);
    List<CustomerEventDetailData> findByEventMonthAndEventType(Integer eventMonth,CustomerEventType eventType);
}
