package com.stpl.tech.kettle.partner.reporting.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface PartnerReportDao extends AbstractDao {

	public List<OrderDetail> getOrderDetailsOfUnit(int unitId, Date businessDate);

	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate);
	
}
