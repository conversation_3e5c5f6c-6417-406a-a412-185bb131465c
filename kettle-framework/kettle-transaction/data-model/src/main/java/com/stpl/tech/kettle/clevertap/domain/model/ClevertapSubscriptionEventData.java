package com.stpl.tech.kettle.clevertap.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;

import lombok.Getter;
import lombok.Setter;
import org.codehaus.jackson.annotate.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClevertapSubscriptionEventData extends ClevertapChargedEventData {

    @JsonProperty("SubscriptionPlanCode")
    @SerializedName("SubscriptionPlanCode")
    private String subscriptionPlanCode;
    @JsonProperty("PlanStartDate")
    @SerializedName("PlanStartDate")
    private String planStartDate;
    @JsonProperty("SubscriptionPlanEndDate")
    @SerializedName("SubscriptionPlanEndDate")
    private String subscriptionPlanEndDate;
    @JsonProperty("EventType")
    @SerializedName("EventType")
    private String eventType;

}
