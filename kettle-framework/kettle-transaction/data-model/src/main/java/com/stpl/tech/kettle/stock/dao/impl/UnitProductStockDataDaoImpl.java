package com.stpl.tech.kettle.stock.dao.impl;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventDataDetail;
import com.stpl.tech.kettle.stock.service.impl.AutomatedStockEventReportImpl;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.stock.dao.UnitProductStockDataDao;

@Repository
public class UnitProductStockDataDaoImpl extends AbstractDaoImpl implements UnitProductStockDataDao {

    private static final Logger LOG = LoggerFactory.getLogger(AutomatedStockEventReportImpl.class);
    @Autowired
    MasterDataCache masterDataCache;

    @Override
    public void deleteData(Date calculationDate, Integer unitId) {
        Query query = manager.createQuery("DELETE FROM UnitProductStockData a WHERE a.calculationDate = :calculationDate AND a.unitId = :unitId");
        query.setParameter("unitId",unitId);
        query.setParameter("calculationDate", calculationDate);
        query.executeUpdate();
//        manager.flush();
    }

    @Override
    public List<UnitProductStockEventDataDetail> findAllByUnitIdAndEventTimeStampOrderByProductIdAsc(Integer unitId, Date previousDate) {
        Query query = manager.createQuery("FROM UnitProductStockEventDataDetail U WHERE U.unitId = :unitId AND U.eventTimeStamp BETWEEN :startDate AND :endDate " +
                "ORDER BY U.productId ASC,U.dimension ASC");
        query.setParameter("unitId",unitId);
        query.setParameter("startDate", AppUtils.getStartOfBusinessDay(previousDate));
        query.setParameter("endDate",AppUtils.getStartOfBusinessDay(AppUtils.getDayBeforeOrAfterDay(previousDate,1)));
        List<UnitProductStockEventDataDetail> result = query.getResultList();
        return result;
    }

    @Override
    public Map<String, PartnerUnitProductStockData> getPreviousDayStockData(int unitId, Date businessDate) {
        Query query = manager.createQuery("FROM PartnerUnitProductStockData P where P.unitId =:unitId and P.businessDate = :businessDate order by P.key DESC");
        query.setParameter("unitId",unitId);
        query.setParameter("businessDate",businessDate);
        List<PartnerUnitProductStockData> partnerUnitProductStockDataList = query.getResultList();
        Map<String, PartnerUnitProductStockData> stockDataMap = new HashMap<>();
        for(PartnerUnitProductStockData pupsd : partnerUnitProductStockDataList){
            if(!stockDataMap.containsKey(pupsd.getProductId()+pupsd.getDimension())){
                stockDataMap.put(pupsd.getProductId()+pupsd.getDimension(), pupsd);
            }
        }
        return stockDataMap;
    }

    @Override
    public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate) {
        try{
            LOG.info("Getting Unit Timing");
            Query query = manager.createNativeQuery("SELECT OD.UNIT_ID,UCD.BUSINESS_DATE, OD.BILLING_SERVER_TIME " +
                    "FROM UNIT_CLOSURE_DETAILS UCD INNER JOIN ORDER_DETAIL OD ON OD.ORDER_ID = UCD.LAST_ORDER_ID " +
                    "WHERE UCD.BUSINESS_DATE = :startDate AND UCD.CURRENT_STATUS = :status");
            query.setParameter("startDate", AppUtils.getSQLFormattedDate(previousDate)).setParameter("status", AppConstants.INITIATED);
            Map<Integer, List<Date>> map = new HashMap<>();
            List<Object[]> result = query.getResultList();
            for (Object[] obj : result) {
                try {
                    UnitHours unitHourDetail = masterDataCache.getUnit((Integer) obj[0]).getOperationalHours()
                            .get(AppUtils.getWeekDayNumber((Date) obj[1])-1);
                    Date openingDate = leastOpeningTime(leastOpeningTime(unitHourDetail.getDineInOpeningTime(), unitHourDetail.getDeliveryOpeningTime()),
                            unitHourDetail.getTakeAwayOpeningTime());
                    if(openingDate==null){
                        continue;
                    }
                    List<Date> time = new ArrayList<>();
                    time.add(AppUtils.setTimeToDate(previousDate, openingDate));
                    time.add((Date) obj[2]);
                    map.put((Integer) obj[0], time);
                }catch (Exception e){
                    LOG.error("Exception Caught :::{}",(Integer) obj[0],e);
                }
            }
            return map;
        } catch (Exception e){
            LOG.error("Exception Caught!!:::",e);
            return null;
        }
    }

    public static Time leastOpeningTime(Time a, Time b) {
        if(a == null && b == null){
            return null;
        }
        else if(a == null){
            return b;
        }
        else if(b == null){
            return a;
        }
        else {
            return (a.before(b) ? a : b);
        }
    }
}