/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.customer.service.impl;

import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.LoyaltyService;
import com.stpl.tech.kettle.data.model.LoyaltyScore;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Service
public class LoyaltyServiceImpl implements LoyaltyService {

	@Autowired
	private LoyaltyDao dao;

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateScore(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId) {
		return dao.updateScore(customerId, type, points, orderId, hasSignupOffer, updateLastOrderId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean createEvent(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId) {
		return dao.createEventAndUpdate(customerId, type, points, orderId, hasSignupOffer, updateLastOrderId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public LoyaltyScore getScore(int customerId) {
		return dao.getScore(customerId);
	}

	@Override
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<LoyaltyEvents> getLoyaltyEvents(String id, String searchType,Integer limit){
		return dao.getLoyaltyEventsForCustomerId(id,limit);
	}

}
