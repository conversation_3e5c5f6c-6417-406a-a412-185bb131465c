/**
 *
 */
package com.stpl.tech.kettle.core.data.vo;

import com.stpl.tech.kettle.domain.model.OrderStatus;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
public class OrderFetchStrategy {

	private boolean fetchSettlement;
	private boolean fetchItems;
	private boolean fetchTaxes;
	private boolean fetchPrintDetails;
	private boolean onlyDelivery;
	private boolean donotFetchCustomerAddress;
	private List<OrderStatus> orderStatuses;
	private int terminalId;
	private boolean pageable;
	private Integer start;
	private Integer batchSize;

	public OrderFetchStrategy(boolean fetchSettlement, boolean fetchItems, boolean fetchTaxes,
			boolean fetchPrintDetails, boolean onlyDelivery, List<OrderStatus> orderStatuses, int terminalId, boolean donotFetchCustomerAddress) {
		super();
		this.fetchSettlement = fetchSettlement;
		this.fetchItems = fetchItems;
		this.fetchTaxes = fetchTaxes;
		this.fetchPrintDetails = fetchPrintDetails;
		this.onlyDelivery = onlyDelivery;
		this.orderStatuses = orderStatuses;
		this.terminalId = terminalId;
		this.donotFetchCustomerAddress = donotFetchCustomerAddress;
	}

	public OrderFetchStrategy(boolean fetchSettlement, boolean fetchItems, boolean fetchTaxes,
							  boolean fetchPrintDetails,boolean onlyDelivery, List<OrderStatus> orderStatuses,int terminalId,boolean donotFetchCustomerAddress,
							  boolean pageable, Integer start,Integer batchSize){
		super();
		this.fetchSettlement = fetchSettlement;
		this.fetchItems = fetchItems;
		this.fetchTaxes = fetchTaxes;
		this.fetchPrintDetails = fetchPrintDetails;
		this.onlyDelivery = onlyDelivery;
		this.orderStatuses = orderStatuses;
		this.terminalId = terminalId;
		this.donotFetchCustomerAddress = donotFetchCustomerAddress;
		this.pageable = pageable;
		this.start = Objects.isNull(start) ? 1 : start;
		this.batchSize = Objects.isNull(batchSize) ? 50 : batchSize;
	}

	public boolean isOnlyDelivery() {
		return onlyDelivery;
	}

	public void setOnlyDelivery(boolean onlyDelivery) {
		this.onlyDelivery = onlyDelivery;
	}

	public List<OrderStatus> getOrderStatuses() {
		return orderStatuses;
	}

	public void setOrderStatuses(List<OrderStatus> orderStatuses) {
		this.orderStatuses = orderStatuses;
	}

	public int getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(int terminalId) {
		this.terminalId = terminalId;
	}

	public boolean isFetchSettlement() {
		return fetchSettlement;
	}

	public void setFetchSettlement(boolean fetchSettlement) {
		this.fetchSettlement = fetchSettlement;
	}

	public boolean isFetchItems() {
		return fetchItems;
	}

	public void setFetchItems(boolean fetchItems) {
		this.fetchItems = fetchItems;
	}

	public boolean isFetchTaxes() {
		return fetchTaxes;
	}

	public void setFetchTaxes(boolean fetchTaxes) {
		this.fetchTaxes = fetchTaxes;
	}

	public boolean isFetchPrintDetails() {
		return fetchPrintDetails;
	}

	public void setFetchPrintDetails(boolean fetchPrintDetails) {
		this.fetchPrintDetails = fetchPrintDetails;
	}

	public boolean isDonotFetchCustomerAddress() {
		return donotFetchCustomerAddress;
	}

	public void setDonotFetchCustomerAddress(boolean donotFetchCustomerAddress) {
		this.donotFetchCustomerAddress = donotFetchCustomerAddress;
	}

	public boolean isPageable(){return pageable;}

	public void setPageable(boolean pageable){this.pageable = pageable;}

	public Integer getStart(){return start;}

	public void setStart(Integer start) {
		this.start = start;
	}

	public Integer getBatchSize(){return batchSize;}

	public void setBatchSize(Integer batchSize) {
		this.batchSize = batchSize;
	}
}
