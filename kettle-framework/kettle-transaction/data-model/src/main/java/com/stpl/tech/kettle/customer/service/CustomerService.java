/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


package com.stpl.tech.kettle.customer.service;

import com.stpl.tech.kettle.core.CampaignOfferDetail;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerTransactionData;
import com.stpl.tech.kettle.core.data.vo.CustomerVisitInfo;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CustomerAdditionalDetail;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerBasicInfo;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerPostRequest;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.master.OfferLastRedemptionView;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CustomerService {

    public Customer getCustomer(int customerId) throws DataNotFoundException;

    public Customer getCustomer(String contactNumber);

    public boolean isCustomerOld(String contactNumber, int beforeYear);

    public Customer getCustomer(String code, String contactNumber) throws DataNotFoundException;

    public Customer addCustomer(Customer customer) throws DataUpdationException;

    public boolean updateCustomer(Customer customer) throws DataUpdationException;

    public boolean updateCustomerAppAction(Customer customer) throws DataUpdationException;


    public boolean updateShopifyCustomer(Customer customer) throws DataUpdationException;

    boolean updateCustomerEmail(Customer customer,String flag) throws DataUpdationException;

    public boolean updateBasicCustomerInfo(Customer customer) throws DataUpdationException;


    public void syncCustomerProfileDineInApp(Customer customer);

    public int addAddress(int customerId, Address address) throws DataUpdationException;

    public List<CustomerOffer> getOfferDetail(int customerId, String offerCode);

    public void addOfferDetail(int customerId, String offerCode, int orderId);

    public void verifyContactNumber(String contactNumber);

    public boolean eligibleForSignupOffer(int customerId);

    public void verifyEmailAddress(String contactNumber);

    public CustomerTransactionData getCustomerTransactionInfo(String contactNumber) throws DataNotFoundException, CardValidationException;

    public Customer getCustomerByDeliveryAddress(int deliveryAddressId);

    public Customer addCustomerUnchecked(Customer customer) throws DataUpdationException;

    public void overrideContactVerificationStatus(String contactNumber, boolean contactVerified);

    public List<CustomerInfo> getCustomersWithPendingLoyalty(Date startDate, Date endDate);

    public List<CustomerInfo> getNewCustomers(String firstOrderSource, Date startDate, Date endDate);

    Customer viewCustomer(String contactNumber) throws DataNotFoundException;

    boolean blacklistCustomer(int customerId) throws DataNotFoundException;

    boolean whitelistCustomer(int customerId) throws DataNotFoundException;

    public Address addAddress(String contact, Address address) throws DataUpdationException;

    public List<Address> getNewAddress(int id, List<Integer> addressIds);

    public void removeInvalidEmails(List<String> invalidEmails) throws DataUpdationException;

    public boolean sendVerificationEmail(String email, String oldEmail, String contact, String customerName , Integer brandId);

    void sendOTPEmail(Customer customer, String token);
    void sendOTPEmailBrandWise(Customer customer, String token, Integer brandId);

    public TrueCallerVerifiedProfile getTrueCallerVerifiedProfile(String requestId);

    public String sendTrueCallerRequest(TrueCallerPostRequest request) throws IOException;

    public Customer updateTrueCallerInfo(TrueCallerVerifiedProfile trueCallerVerifiedProfile);

    public void markAsInternalCustomers(List<String> contactNumber);

    public boolean existsContactNumber(String number);

    public BigDecimal getAvailableCash(Integer customerId);

    public Customer getCustomerByRefCode(String refCode);

    public void gererateRefCodes();

    public CustomerInfo getCustomerInfoObject(String contact);

    public List<CustomerInfo> getCustomerWithReferralCode(List<String> contacts);

    public Customer getCustomerByFaceId(String faceId);

    public boolean mapCustomerByFaceId(Pair<String, String> data);

    public Customer createCustomerAndAwardLoyalty(String contactNumber, String name, Integer orderId,
                                                  String acquisitionSource, String acquisitionToken) throws DataUpdationException, DataNotFoundException;

    public String optOutOfFaceIt(String contactNumber, boolean flag);

    public String optOutOfFaceIt(int customerId);

    public OrderDetail getOrderDetail(int orderId);

    public List<Integer> getCustomerIds(int customerId, int batchSize);

    List<Integer> getCustomerIdsWithPendingSignupOffer(int noOfDays);

    void expireSignupOffer(List<Integer> customerIds);

    void expireSignupOffer(int noOfDays);

    boolean isBlackListed(String contactNumber);

    public BigDecimal getAvailableCashback(Integer customerId);

    List<LoyaltyScore> getCustomersLoyaltyScore(List<Integer> customerIds);

    CustomerAdditionalDetail saveCustomerAdditionalDetail(IdCodeName idCodeName);

    Long checkCustomerAdditionalDetail(Integer customerId, String type);

    boolean hasOrdersForOrderSource(String contactNumber, String source);

    boolean sendDownloadAppLinkToCustomer(String contactNumber, Customer customerInfo, String screenType);

    CustomerAdditionalDetail saveAppDownloadLinkData(int customerId, String campaignName, Boolean status);

    boolean updateCrmScreenUrl(CrmAppScreenDetail detail);

    boolean updateCrmScreenStatus(List<CrmAppScreenDetail> details);

	CustomerVisitInfo customerVisit(Integer customerId, Integer unitId);


    boolean updateCustomerAppId(String appId,int customerId);

    CustomerVisitInfo feedbackDetail(CustomerVisitInfo customerVisitInfo, Integer customerId);

    void  removeIncorrectEmailInfo(int customerId);

    public List<Integer> removeAllFaceIdsWithGivenFaceId(String faceId);

	CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

    CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

    CustomerEmailData getCustomerEmailData(int customerId, Integer brandId);

	Map<String, List<CustomerCampaignOfferDetail>> getNextBestOfferDetails(Integer brandId);

	List<CustomerCampaignOfferDetail> getAllNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate);

	List<CustomerCampaignOfferDetail> getUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate);

	List<CustomerCampaignOfferDetail> getNotUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate);

	public Map<String, Pair<Boolean, Boolean>> getNotificationFlags(List<String> customerContact);

    boolean updateWhatsappOptInOut(CustomerResponse customerResponse);

	public Integer checkCouponUsage(int customerId, String code);

	Map<String, List<CustomerCampaignOfferDetail>> getNotUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate);

	Map<String, List<CustomerCampaignOfferDetail>> getUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate);

    boolean getValidOfferFlag(OfferOrder offer, CouponDetail couponOffer);

    boolean doOfferValidation(OfferLastRedemptionView offerLastRedemptionView, CouponDetail couponDetail);

    CustomerContactInfoMapping getCustomerContactInfoMapping(Integer customerId);

    List<CampaignOfferDetail> getNotUsedNextOfferDetails(Integer brandId);

    List<CustomerCampaignOfferDetail> getPendingNextJourneyEligibleOffer(Integer brandId, Date nextOfferDate);

    com.stpl.tech.kettle.customer.CustomerCardInfo getCustomerCardInfo(Integer customerId);

    List<CustomerBasicInfo> getEmpDiscount();

    Boolean deactivateEmpDiscount(List<Integer> customerIds);

    void deactivateOldSubscriptionPlans();

    void deactivateOldSubscriptionPlanEvents();

    Map<String,List<String>> uploadCustomerSheet(MultipartFile file,String acquisitionSource) throws Exception;

    boolean getCustomerOrders(int customerId);

    Integer getCustomerId(String contactNumber);

	public CustomerInfo subscribeCustomer(int customerId, String channel) throws DataNotFoundException;

	public CustomerInfo unsubscribeCustomer(int customerId, String channel) throws DataNotFoundException;
    public Integer addNewCustomer(String contactNumber,String customerName,String acquisitionSource,String acquisitionToken) throws DataUpdationException;

    Pair<Integer, Integer> saveIncrementalCustomerSaveChaiData(Integer batchSize, Integer page);

    List<CustomerInfo> getCustomersWithDOBorAnniversary();

    public String getCustomerContactBrandWise(String contactNumber, Integer brandId);

    public String getBrandContactCode(Integer brandId);
}
