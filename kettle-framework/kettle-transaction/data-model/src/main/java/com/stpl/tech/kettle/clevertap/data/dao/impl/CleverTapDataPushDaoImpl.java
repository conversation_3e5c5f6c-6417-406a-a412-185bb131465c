package com.stpl.tech.kettle.clevertap.data.dao.impl;

import com.stpl.tech.kettle.clevertap.data.dao.CleverTapDataPushDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Arrays;
import java.util.List;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Repository
public class CleverTapDataPushDaoImpl extends AbstractDaoImpl implements CleverTapDataPushDao {

    @Override
    public List<Integer> getCustomerIdsBatch(int batchSize, Integer lastCustomerId, List<Integer> excludeCustomerIds) {
        Query query = manager.createQuery("SELECT o.customerId FROM CustomerInfo o WHERE o.customerId > :customerId and o.customerId NOT IN :excludeCustomerId and  o.customerAppId IS NULL  order by o.customerId");
        query.setParameter("customerId", lastCustomerId);
        query.setParameter("excludeCustomerId", excludeCustomerIds);
        query.setMaxResults(batchSize);
        return query.getResultList();
    }

    @Override
    public List<Integer> getOrderBatch(Integer startOrderId, Integer batchSize, List<Integer> excludingPartnerIds, List<Integer> excludeCustomerIds) {
        Query query = manager.createQuery("SELECT o.orderId FROM OrderDetail o WHERE " +
            "o.orderId > :startOrderId AND o.orderStatus NOT IN (:orderStatus) AND o.channelPartnerId NOT IN (:appPartnerId) and o.customerId NOT IN :excludeCustomerIds order by o.orderId");
        query.setParameter("startOrderId", startOrderId);
        query.setParameter("appPartnerId", excludingPartnerIds);
        query.setParameter("excludeCustomerIds", excludeCustomerIds);
        query.setParameter("orderStatus", Arrays.asList(OrderStatus.CANCELLED_REQUESTED.value(), OrderStatus.CANCELLED.value()));
        query.setMaxResults(batchSize);
        return query.getResultList();
    }

}
