/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.service;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import com.stpl.tech.master.util.MasterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import com.google.common.io.Files;
import com.stpl.tech.kettle.core.monk.simulation.model.RecipeKey;
import com.stpl.tech.kettle.core.monk.simulation.model.RecipeProductionData;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationInputData;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationOutputData;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationResultData;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationSummary;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationTimeData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.util.ServiceUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.ExecutionEnvironment;

/**
 * <AUTHOR>
 *
 */
public class MonkSimulator {

	private static final Logger LOG = LoggerFactory.getLogger(MonkSimulator.class);

	private static final Map<String, Integer> dimensionSplitter;
	private static final Map<Integer, String> unitIds;
	private final RecipeTimeLoader loader;

	static {
		dimensionSplitter = new HashMap<>();
		dimensionSplitter.put("None", 7);
		dimensionSplitter.put("Regular", 7);
		dimensionSplitter.put("Full", 5);
		dimensionSplitter.put("ChotiKetli", 3);
		dimensionSplitter.put("BadiKetli", 1);

		unitIds = new HashMap<>();
		//unitIds.put(10000, "Good Earth City Centre");
		//unitIds.put(10001, "DLF Cyber City Building 5");
		//unitIds.put(10002, "Galaxy It Park");
		//unitIds.put(10003, "Unitech Infospace");
		unitIds.put(10005, "Galleria Market");
		unitIds.put(10006, "SDA Market");
		unitIds.put(10009, "Chapel Road Bandra");
		unitIds.put(10010, "Juhu");
		unitIds.put(10012, "New Friends Colony");
		unitIds.put(10013, "Netaji Subhash Place");
		unitIds.put(12011, "Kirti Nagar");
		unitIds.put(12014, "Vatika Business Park");
		unitIds.put(12015, "Connaught Place- F Block");
		unitIds.put(12018, "DLF Mall Of India - Lower Ground Floor");
		unitIds.put(12019, "DLF Promenade Mall");
		unitIds.put(12020, "Garden Galleria Mall");
		unitIds.put(12028, "Nehru Place - Epicuria");
		unitIds.put(12030, "Cyber Green");
		unitIds.put(12031, "Powai Hiranandani");
		unitIds.put(26005, "Greater Kailash-2");
		unitIds.put(26012, "Pacific Mall");
		unitIds.put(26015, "Central Plaza Gurgaon");
		unitIds.put(26016, "Logix City Centre");
		unitIds.put(26017, "Dhaulakuan");
		unitIds.put(26018, "Dwarka");
		unitIds.put(26019, "Indirapuram");
		unitIds.put(26021, "Kurla Phoenix");
		unitIds.put(26022, "Versova");
		unitIds.put(26023, "Elante Mall Chandigarh");
		unitIds.put(26025, "Dlf Mall Of India - First Floor");
		unitIds.put(26026, "Karol Bagh");
		unitIds.put(26027, "Connaught Place 2 - M Block");
		unitIds.put(26028, "The Walk Thane");
		unitIds.put(26029, "Mulund Runwal Greens");
		unitIds.put(26034, "Oshiwara");
		unitIds.put(26036, "One Horizon Centre");
		unitIds.put(26037, "World Mark 1");
		unitIds.put(26038, "Savoy Greens");
		unitIds.put(26039, "Iit Delhi");
		unitIds.put(26042, "Red Mall Ghaziabad");
		unitIds.put(26044, "Seawoods Mumbai");
		//unitIds.put(26048, "Mumbai Airport");
		//unitIds.put(26050, "Growels Mall");

	}

	/**
	 * 
	 */
	public MonkSimulator() {
		loader = new RecipeTimeLoader("E:/RecipeTimes.xlsx");
		loader.loadData();

	}

	public static void main(String[] args) throws SQLException {
		MonkSimulator simulator = new MonkSimulator();
		for (int unitId : unitIds.keySet()) {
			LOG.info("Start of Process for " + unitIds.get(unitId));
			SimulationOutputData result = simulator
					.process(new SimulatorInputCriteria(unitId, unitIds.get(unitId)).getInput());
			simulator.writeToExcel(result);
			LOG.info("End of Process for " + unitIds.get(unitId));
		}
	}

	public SimulationOutputData process(SimulationInputData input) throws SQLException {
		SimulationOutputData output = new SimulationOutputData();
		LOG.info("Staring simulation for input :" + input);
		List<SimulationResultData> result = fetchData(input);
		LOG.info("Total Records fetched for simulation are : " + result.size());
		result = splitTasks(result);
		LOG.info("Total Records split for simulation are : " + result.size());
		settingDesiredTime(result, loader);
		LOG.info("Total Records split for simulation are : " + result.size());
		/*
		 * LOG.info("Running for  fluctuation"); for (int i = 1; i <=
		 * input.countOfMonks; i++) { LOG.info(
		 * "Running for  fluctuation with monk :" + i);
		 * process(input.transitionTime, true, input.strategy, i, result); }
		 */
		LOG.info("Running for WITHOUT fluctuation");
		for (int i = 1; i <= input.countOfMonks; i++) {
			LOG.info("Running for WITHOUT fluctuation with monk :" + i);
			process(input.transitionTime, false, input.strategy, i, result);
		}
		output.simulations = result;
		output.monkCount = input.countOfMonks;
		output.transitionTime = input.transitionTime;
		output.filePath = "E:/simulation-latest/" + input.unitName.replace(" ", "") + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
		LOG.info("Summarizing Results");
		output.summary = summarizeResults(input.countOfMonks, result);
		return output;
	}

	/**
	 * @param result
	 * @return
	 */
	private Map<Integer, SimulationSummary> summarizeResults(int countOfMonks, List<SimulationResultData> result) {
		Map<Integer, SimulationSummary> map = new HashMap<>();
		for (int i = 1; i <= countOfMonks; i++) {
			map.put(i, new SimulationSummary());
		}
		for (SimulationResultData r : result) {

		}
		return null;
	}

	/**
	 * @param result
	 * @param loader
	 * @return
	 */
	private void settingDesiredTime(List<SimulationResultData> result, RecipeTimeLoader loader) {
		result.forEach((item) -> {
			RecipeProductionData data = loader.get(item.productData);
			if (data == null) {
				item.toBeSkipped = true;
				LOG.error("Did Not Find any Recipe Time Entry For :" + item.productData);
			} else {
				item.recipeString = data.recipeString;
				item.actualTimeWithVoltageFluctuation = data.timeWithFluctuation;
				item.actualTimeWithoutVoltageFluctuation = data.timeWithoutFluctuation;
				item.steepingTime = data.steepingTime;
				item.hasTransition = data.hasTransition;
			}
		});

	}

	/**
	 * @param result
	 * @return
	 */
	private List<SimulationResultData> splitTasks(List<SimulationResultData> result) {
		List<SimulationResultData> list = new ArrayList<>();
		result.forEach((item) -> {
			int max = dimensionSplitter.get(item.productData.dimension);
			int current = item.productData.quantity;
			if (max < current) {
				int taskId = 1;
				while (current != 0) {
					try {
						SimulationResultData copy = (SimulationResultData) item.clone();
						copy.isSplit = true;
						if (max < current) {
							copy.productData.quantity = max;
							current = current - max;

						} else {
							copy.productData.quantity = current;
							current = 0;
						}
						copy.taskId = taskId;
						list.add(copy);
						taskId++;
						LOG.debug("Split : " + copy);
					} catch (Exception e) {
					}

				}
			} else {
				list.add(item);
			}
		});
		return list;
	}

	public void process(int transitionTime, boolean withFluctuation, String strategy, int monkCount,
			List<SimulationResultData> result) {
		if (strategy.equalsIgnoreCase("fifo")) {
			FifoMonkRouter router = new FifoMonkRouter(monkCount);
			for (SimulationResultData s : result) {
				if (!s.toBeSkipped) {
					router.route(s, withFluctuation, transitionTime);
					// LOG.info(s.toString(monkCount, withFluctuation));
				}
			}

		}
	}

	private List<SimulationResultData> fetchData(SimulationInputData input) throws SQLException {
		List<SimulationResultData> result = new ArrayList<>();
		PreparedStatement stmt = null;
		Connection con = null;
		String query = "SELECT " + "od.ORDER_ID orderId, " + "oi.ORDER_ITEM_ID orderItemId, "
				+ "od.ORDER_SOURCE orderSource, " + "od.TAXABLE_AMOUNT taxableAmount, "
				+ "od.BILLING_SERVER_TIME orderTime, " + "oi.PRODUCT_ID productId,  " + "pd.PRODUCT_NAME productName, "
				+ "oi.DIMENSION dimension, " + "oi.QUANTITY quantity" + " FROM " + "KETTLE_DUMP.ORDER_DETAIL od, "
				+ "KETTLE_DUMP.ORDER_ITEM oi, " + "KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd " + "WHERE "
				+ "od.ORDER_ID = oi.ORDER_ID " + "AND od.UNIT_ID = ? " + "AND od.BILLING_SERVER_TIME >= ? "
				+ "AND od.BILLING_SERVER_TIME < ? " + "AND oi.PRODUCT_ID = pd.PRODUCT_ID " + "AND pd.PRODUCT_TYPE = 5 "
				+ "AND od.ORDER_STATUS <> 'CANCELLED' " + "AND od.ORDER_TYPE = 'order' "
				+ "ORDER BY od.ORDER_ID, oi.ORDER_ITEM_ID";
		try {
			DataSource dataSource = ServiceUtil.getDataSourceBean(ExecutionEnvironment.UAT);
			con = dataSource.getConnection();
			stmt = con.prepareStatement(query);
			stmt.setInt(1, input.unitId);
			Date currentDate = AppUtils.getBusinessDate();
			Date monthStart = AppUtils.getStartOfMonth(currentDate.getYear(), currentDate.getMonth());
			Date oldDate = AppUtils.getDateBeforeOrAfter(monthStart, input.noOfMonths * -1);
			Timestamp oldTimeStamp = new Timestamp(oldDate.getTime());
			Timestamp monthStartTimestamp = new Timestamp(monthStart.getTime());
			stmt.setTimestamp(2, oldTimeStamp);
			stmt.setTimestamp(3, monthStartTimestamp);
			LOG.info(String.format("Fetching Orders from %s to %s", oldTimeStamp, monthStartTimestamp));
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) {
				SimulationResultData data = new SimulationResultData();
				data.orderId = rs.getInt("orderId");
				data.orderItemId = rs.getInt("orderItemId");
				data.orderSource = rs.getString("orderSource");
				data.taxableAmount = rs.getBigDecimal("taxableAmount");
				data.orderTime = rs.getTimestamp("orderTime");
				RecipeKey key = new RecipeKey();
				key.productId = rs.getInt("productId");
				key.dimension = rs.getString("dimension");
				key.quantity = rs.getInt("quantity");
				data.productData = key;
				data.productName = rs.getString("productName");
				result.add(data);
			}

		} catch (SQLException e) {
			LOG.error("Error in fetching data", e);
			if (con != null) {
				try {
					LOG.error("Transaction is being rolled back");
					con.rollback();
				} catch (SQLException excep) {
				}
			}
		} finally {
			if (stmt != null) {
				stmt.close();
			}
		}
		return result;
	}

	public void writeToExcel(SimulationOutputData output) {
		WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();
		WorkbookContext workbookCtx = ctxFactory.createWorkbook();
		writeSummary(workbookCtx, output);
		// writeResults(workbookCtx, output, true);
		writeResults(workbookCtx, output, false);
		try {
			Files.write(workbookCtx.toNativeBytes(), new File(output.filePath));
		} catch (IOException e) {
			LOG.error("Error in writing simulation report :" + output.filePath, e);
		}
	}

	/**
	 * @param workbookCtx
	 * @param output
	 */
	private void writeSummary(WorkbookContext workbookCtx, SimulationOutputData output) {

	}

	/**
	 * @param workbookCtx
	 * @param output
	 */
	private void writeResults(WorkbookContext workbookCtx, SimulationOutputData output, boolean withFluctuation) {

		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		SheetContext sheetCtx = workbookCtx
				.createSheet(withFluctuation ? "Data Dump With Fluctuation" : "Data Dump Without Fluctuation");
		writeDumpHeader(output.monkCount, sheetCtx, headerStyle);

		for (int i = 1; i <= output.monkCount; i++) {
			for (SimulationResultData r : output.simulations) {
				RowContext row = sheetCtx.nextRow();
				row.number(i).number(r.orderId).number(r.orderItemId).number(r.taskId)
						.text(AppConstants.getValue(r.hasTransition)).text(AppConstants.getValue(r.toBeSkipped))
						.text(r.orderSource).number(r.taxableAmount).number(r.productData.productId).text(r.productName)
						.text(r.productData.dimension).number(r.productData.quantity).number(r.orderTime.getHours())
						.text(AppUtils.dayPart(r.orderTime.getHours())).date(r.orderTime);
				writeTimeData(row, r, i, withFluctuation, output.transitionTime);
			}
		}

	}

	private void writeTimeData(RowContext row, SimulationResultData r, int monkCount, boolean withFluctuation,
			int transitionTime) {
		if (r.toBeSkipped) {
			return;
		}
		Pair<Integer, Boolean> key1 = new Pair<Integer, Boolean>(monkCount, withFluctuation);
		SimulationTimeData t = r.result.get(key1);
		row.date(t.startTime).date(t.endTime)
				.number(withFluctuation ? r.actualTimeWithVoltageFluctuation + transitionTime
						: r.actualTimeWithoutVoltageFluctuation + transitionTime)
				.number(t.timeTaken).number(t.waitTimeInSecs).number(t.idealMonks).number(t.monkNumber);

	}

	/**
	 * @param sheetCtx
	 */
	private void writeDumpHeader(int countOfMonks, SheetContext sheetCtx, Style headerStyle) {
		RowContext row = sheetCtx.nextRow();
		row = sheetCtx.nextRow();
		row.setTextStyle(headerStyle).text("Monk Count").text("Order Id").text("Order Item Id").text("Task Id").text("Has Transition")
				.text("Skipped").text("Source").text("Taxable Amount").text("Product Id").text("Product Name")
				.text("Dimension").text("Quantity").text("Hour of Day").text("Day Part").text("Order Time")
				.text("Start Time").text("End Time").text("Ideal Taken").text("Time Taken").text("Wait Time")
				.text("Ideal Monks").text("Monk Number");
	}
}
