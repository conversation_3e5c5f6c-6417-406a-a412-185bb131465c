package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.CustomerFavChaiManagementDao;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import com.stpl.tech.kettle.domain.model.CustomerFavChaiMappingVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class CustomerFavChaiManagementDaoImpl extends AbstractDaoImpl implements CustomerFavChaiManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerFavChaiManagementDaoImpl.class);

    @Override
    public List<CustomerFavChaiMapping> findCustomerFavChaiByConsumeTypeAndTagType(int customerId, String consumeType, String tagType) {
        List<CustomerFavChaiMapping> customerFavChaiMappings=null;
        try{
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId AND c.consumeType = :consumeType "
                    + "  AND c.tagType = :tagType");
            query.setParameter("customerId", customerId);
            query.setParameter("consumeType", consumeType);
            query.setParameter("tagType", tagType);
            customerFavChaiMappings = query.getResultList();
        }catch(NoResultException e){
            LOG.info("No fav chai mappings for customer :{} by consumeType :{} and tagType:{}",customerId,consumeType,tagType);
        }
        return customerFavChaiMappings;
    }

    @Override
    public List<CustomerFavChaiMapping> findAllActiveCustomerFavChai(int customerId, String status) {
        List<CustomerFavChaiMapping> customerFavChaiMappings=null;
        try {
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId AND c.status=:status order by c.creationTime desc");
            query.setParameter("customerId", customerId);
            query.setParameter("status", status);
            customerFavChaiMappings = query.getResultList();
        }catch(Exception e){
            LOG.error("No fav chai mappings for customer :{}",customerId);;
        }
        return customerFavChaiMappings;
    }

    @Override
    public CustomerFavChaiMapping findLastCustomerFavChaiMapping(int customerId, String tagType, String consumeType) {
        CustomerFavChaiMapping lastestCustomerFavChaiMapping = null;
        try{
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId AND c.consumeType = :consumeType "
                    + "  AND c.tagType = :tagType ORDER BY c.customizationId DESC").setMaxResults(1);
            query.setParameter("customerId", customerId);
            query.setParameter("consumeType", consumeType);
            query.setParameter("tagType", tagType);
            return (CustomerFavChaiMapping)query.getSingleResult();
        }catch(NoResultException e){
            LOG.error("No fav chai mappings for customer :{}",customerId);
            return null;
        }

    }

    @Override
    public void saveNewCustomerFavChaiMapping(CustomerFavChaiMapping newCustomerFavChaiMappping) {
        try{
            manager.persist(newCustomerFavChaiMappping);
        }catch(Exception e){
            LOG.error("Error while adding new customer fav chai mapping for customer :::{}",newCustomerFavChaiMappping.getCustomerId(),e);
        }
    }

    @Override
    public List<CustomerFavChaiMappingVO> findAllActiveCustomerFavChaiMappings(int customerId, String status) {
        List<CustomerFavChaiMapping> customerFavChaiMappings=null;
        List<CustomerFavChaiMappingVO> customerFavChaiMappingVOList = new ArrayList<>();
        try{
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId AND c.status=:status order by c.creationTime desc");
            query.setParameter("customerId", customerId);
            query.setParameter("status",status);
            customerFavChaiMappings = query.getResultList();
            if(Objects.nonNull(customerFavChaiMappings) && !customerFavChaiMappings.isEmpty()){
                for (CustomerFavChaiMapping customerFavChaiMapping : customerFavChaiMappings) {
                    try {
                        CustomerFavChaiMappingVO customerFavChaiMappingVO = DataConverter.convert(customerFavChaiMapping);
//                        List<FavChaiCustomizationDetail> favChaiCustomizationDetailList = getAllFavChaiCustomization(customerFavChaiMapping);
//                        if(Objects.nonNull(favChaiCustomizationDetailList) && !favChaiCustomizationDetailList.isEmpty()){
//                            for (FavChaiCustomizationDetail favChaiCustomizationDetail : favChaiCustomizationDetailList) {
//                                customerFavChaiMappingVO.getFavChaiCustomizationDetailList().add(DataConverter.convert(favChaiCustomizationDetail));
//                            }
//                        }
                        customerFavChaiMappingVOList.add(customerFavChaiMappingVO);
                    } catch (Exception e ) {
                        LOG.error("Exception while converting cutomer fav chai mapping of data model to domain model::::",e.getMessage());
                    }
                }
            }
        }catch(NoResultException e){
            LOG.error("No fav chai mappings for customer :{}",customerId);
        }
        return customerFavChaiMappingVOList;
    }

    private List<FavChaiCustomizationDetail> getAllFavChaiCustomization(CustomerFavChaiMapping customerFavChaiMapping) {
        try {
            Query query = manager.createQuery("from FavChaiCustomizationDetail where customerFavChaiMapping =:customerFavChaiMapping");
            query.setParameter("customerFavChaiMapping", customerFavChaiMapping);
            return query.getResultList();
        }catch(Exception e){
            LOG.error("No customization detail found ",e.getMessage());
            return null;
        }
    }

    @Override
    public CustomerFavChaiMapping findLastCustomerFavChaiMappingByTagType(int customerId, String tagType) {
        CustomerFavChaiMapping lastestCustomerFavChaiMapping = null;
        try{
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId "
                    + "  AND c.tagType = :tagType ORDER BY c.customizationId DESC").setMaxResults(1);
            query.setParameter("customerId", customerId);
            query.setParameter("tagType", tagType);
            return (CustomerFavChaiMapping)query.getSingleResult();
        }catch(NoResultException e){
            LOG.error("No fav chai mappings for customer :{}",customerId);
            return null;
        }
    }

    @Override
    public CustomerFavChaiMapping findCustomerFavChaiMappingByTagTypeAndConsumeTypeAndStatus(int customerId, String consumeType, String tagType, String status) {
        CustomerFavChaiMapping lastestCustomerFavChaiMapping = null;
        try{
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId "
                    + "  AND c.tagType = :tagType AND c.consumeType = :consumeType AND c.status =:status");
            query.setParameter("customerId", customerId);
            query.setParameter("tagType", tagType);
            query.setParameter("status",status);
            query.setParameter("consumeType", consumeType);
            return (CustomerFavChaiMapping)query.getSingleResult();
        }catch(NoResultException e){
            LOG.error("No fav chai mappings for customer :{}",customerId);
            return null;
        }
    }

    @Override
    public CustomerFavChaiMapping findCustomerFavChaiMappingByTagTypeAndStatus(int customerId, String tagType, String status) {
        CustomerFavChaiMapping lastestCustomerFavChaiMapping = null;
        try{
            Query query = manager.createQuery(" FROM CustomerFavChaiMapping c WHERE c.customerId = :customerId "
                    + "  AND c.tagType = :tagType AND c.status =:status");
            query.setParameter("customerId", customerId);
            query.setParameter("tagType", tagType);
            query.setParameter("status",status);
            return (CustomerFavChaiMapping)query.getSingleResult();
        }catch(NoResultException e){
            LOG.error("No fav chai mappings for customer :{}",customerId);
            return null;
        }
    }
}
