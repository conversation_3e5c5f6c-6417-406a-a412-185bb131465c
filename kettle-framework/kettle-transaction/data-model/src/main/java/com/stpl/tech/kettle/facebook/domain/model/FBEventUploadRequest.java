package com.stpl.tech.kettle.facebook.domain.model;

import java.math.BigDecimal;

public class FBEventUploadRequest {

    private FBMatchKeys match_keys;
    private String currency;
    private BigDecimal value;
    private String event_name;
    private long event_time;
    private String order_id;

    @Override
    public String toString() {
        return "FBEventUploadRequest{" +
                "match_keys=" + match_keys +
                ", currency='" + currency + '\'' +
                ", value=" + value +
                ", event_name='" + event_name + '\'' +
                ", event_time=" + event_time +
                ", order_id='" + order_id + '\'' +
                '}';
    }

    public FBMatchKeys getMatch_keys() {
        return match_keys;
    }

    public void setMatch_keys(FBMatchKeys match_keys) {
        this.match_keys = match_keys;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getEvent_name() {
        return event_name;
    }

    public void setEvent_name(String event_name) {
        this.event_name = event_name;
    }

    public long getEvent_time() {
        return event_time;
    }

    public void setEvent_time(long event_time) {
        this.event_time = event_time;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }
}
