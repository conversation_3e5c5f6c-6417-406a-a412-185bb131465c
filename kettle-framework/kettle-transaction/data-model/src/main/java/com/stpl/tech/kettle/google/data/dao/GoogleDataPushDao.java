package com.stpl.tech.kettle.google.data.dao;

import com.stpl.tech.kettle.google.domain.model.GoogleCustomerOrderData;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.Date;
import java.util.List;

public interface GoogleDataPushDao extends AbstractDao {

    List<GoogleCustomerOrderData> getGooglePushDataForLeads(Date startTime, Date endTime, Integer brandId);

    List<GoogleCustomerOrderData> getCustomerOrdersBulk(Date startTime, Date endTime, Integer brandId);

}
