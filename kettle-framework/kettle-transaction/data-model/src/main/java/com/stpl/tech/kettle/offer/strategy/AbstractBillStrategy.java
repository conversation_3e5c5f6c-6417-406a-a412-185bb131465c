/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.util.AppUtils;

public abstract class AbstractBillStrategy {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractBillStrategy.class);

	protected void updateOrderDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

		LOG.info("Updating order with coupon code:{}", code);
		DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
		BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
				offerOrder.getOrder().getTransactionDetail().getTotalAmount());
		discountDetail.setTotalDiscount(offerValue);
		if (offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null) {
			discountDetail.setTotalDiscount(discountDetail.getTotalDiscount()
					.add(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()));
		}
		discountDetail.getDiscount().setValue(offerValue);
		offerOrder.getOrder().getTransactionDetail().setDiscountDetail(discountDetail);
	}
	
	
	protected DiscountDetail getOrderDiscount(String code, BigDecimal discountPercentage, BigDecimal totalAmount) {
		LOG.info("Getting order with coupon code:{}", code);
		DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
		BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
				totalAmount);
		discountDetail.setTotalDiscount(offerValue);
		discountDetail.getDiscount().setValue(offerValue);
		return discountDetail;
	}

	protected void updateOrderItemDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

		LOG.info("Updating order item with coupon code:{}", code);
		for (OrderItem item : offerOrder.getOrder().getOrders()) {
			if(item.getAmount().compareTo(BigDecimal.ZERO) <= 0){
				continue;
			}
			DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
			BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
					item.getTotalAmount());
			discountDetail.setTotalDiscount(offerValue);
			if (item.getDiscountDetail().getPromotionalOffer() != null) {
				discountDetail.setTotalDiscount(
						discountDetail.getTotalDiscount().add(item.getDiscountDetail().getPromotionalOffer()));
			}
			discountDetail.getDiscount().setValue(offerValue);
			item.setDiscountDetail(discountDetail);
		}
	}

	private DiscountDetail getDiscountDetail(String code, BigDecimal discountPercentage) {
		PercentageDetail percentageDetail = new PercentageDetail();
		percentageDetail.setPercentage(discountPercentage);
		DiscountDetail discountDetail = new DiscountDetail();
		discountDetail.setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
		discountDetail.setDiscountReason(code);
		discountDetail.setDiscount(percentageDetail);
		discountDetail.setPromotionalOffer(BigDecimal.ZERO);
		return discountDetail;
	}

	public abstract String getOfferMessage();

}
