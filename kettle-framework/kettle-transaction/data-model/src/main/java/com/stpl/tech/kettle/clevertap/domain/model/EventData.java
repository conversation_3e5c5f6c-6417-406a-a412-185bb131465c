package com.stpl.tech.kettle.clevertap.domain.model;

import java.math.BigDecimal;
import java.util.List;

public class EventData {

    private Boolean transaction_orderFree;

    private String billingTime;

    private BigDecimal transaction_wbalIfSucc;

    private Integer cartId;

    private BigDecimal transaction_orderSummary_value;

    private BigDecimal transaction_walletSummary_value;

    private String source;

    private String recreated;

    private Integer companyId;

    private BigDecimal transaction_saving;

    private BigDecimal transaction_orderSummary_offer;

    private String availableWallet;

    private int customerId;

    private BigDecimal transaction_paid;

    private Integer unitId;

    private Boolean dontUseWallet;

    private String transaction_walletSummary_offer;

    private String status;

    private String updatedAt;

    private String transaction_afterWallet;

    private String transaction_walletSummary_additional;

    private String CTAppVersion;

    private String CTLatitude;

    private String CTLongitutde;

    private String CTSource;

    private String transaction_orderSummary_additional;

    private String recreationStatus;

    private Integer CTSessionId;

    private BigDecimal Amount;

    private String currency;

    private String shipCountry;

    private String shipRegion;

    private String shipCity;

    private String email;

    private String customerType;

    private Integer chargedId;

    private List<ClevertapOrderItem> Items;

    private Integer channelPartnerId;


    public List<ClevertapOrderItem> getOrderItem() {
        return Items;
    }

    public BigDecimal getTransaction_paid() {
        return transaction_paid;
    }

    public void setTransaction_paid(BigDecimal transaction_paid) {
        this.transaction_paid = transaction_paid;
    }

    public void setOrderItem(List<ClevertapOrderItem> orderItem) {
        this.Items = orderItem;
    }

    public BigDecimal getAmount() {
        return Amount;
    }

    public void setAmount(BigDecimal amount) {
        Amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getShipCountry() {
        return shipCountry;
    }

    public void setShipCountry(String shipCountry) {
        this.shipCountry = shipCountry;
    }

    public String getShipRegion() {
        return shipRegion;
    }

    public void setShipRegion(String shipRegion) {
        this.shipRegion = shipRegion;
    }

    public String getShipCity() {
        return shipCity;
    }

    public void setShipCity(String shipCity) {
        this.shipCity = shipCity;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getChargedId() {
        return chargedId;
    }

    public void setChargedId(Integer chargedId) {
        this.chargedId = chargedId;
    }


    public Boolean getTransaction_orderFree() {
        return transaction_orderFree;
    }

    public void setTransaction_orderFree(Boolean transaction_orderFree) {
        this.transaction_orderFree = transaction_orderFree;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }

    public BigDecimal getTransaction_orderSummary_value() {
        return transaction_orderSummary_value;
    }

    public void setTransaction_orderSummary_value(BigDecimal transaction_orderSummary_value) {
        this.transaction_orderSummary_value = transaction_orderSummary_value;
    }

    public BigDecimal getTransaction_wbalIfSucc() {
        return transaction_wbalIfSucc;
    }

    public void setTransaction_wbalIfSucc(BigDecimal transaction_wbalIfSucc) {
        this.transaction_wbalIfSucc = transaction_wbalIfSucc;
    }

    public Integer getCartId() {
        return cartId;
    }

    public void setCartId(Integer cartId) {
        this.cartId = cartId;
    }

    public BigDecimal getTransaction_walletSummary_value() {
        return transaction_walletSummary_value;
    }

    public void setTransaction_walletSummary_value(BigDecimal transaction_walletSummary_value) {
        this.transaction_walletSummary_value = transaction_walletSummary_value;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRecreated() {
        return recreated;
    }

    public void setRecreated(String recreated) {
        this.recreated = recreated;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public BigDecimal getTransaction_saving() {
        return transaction_saving;
    }

    public void setTransaction_saving(BigDecimal transaction_saving) {
        this.transaction_saving = transaction_saving;
    }

    public BigDecimal getTransaction_orderSummary_offer() {
        return transaction_orderSummary_offer;
    }

    public void setTransaction_orderSummary_offer(BigDecimal transaction_orderSummary_offer) {
        this.transaction_orderSummary_offer = transaction_orderSummary_offer;
    }

    public String getAvailableWallet() {
        return availableWallet;
    }

    public void setAvailableWallet(String availableWallet) {
        this.availableWallet = availableWallet;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Boolean getDontuseWallet() {
        return dontUseWallet;
    }

    public void setDontuseWallet(Boolean dontuseWallet) {
        this.dontUseWallet = dontuseWallet;
    }

    public String getTransaction_walletSummary_offer() {
        return transaction_walletSummary_offer;
    }

    public void setTransaction_walletSummary_offer(String transaction_walletSummary_offer) {
        this.transaction_walletSummary_offer = transaction_walletSummary_offer;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getTransaction_afterWallet() {
        return transaction_afterWallet;
    }

    public void setTransaction_afterWallet(String transaction_afterWallet) {
        this.transaction_afterWallet = transaction_afterWallet;
    }

    public String getTransaction_walletSummary_additional() {
        return transaction_walletSummary_additional;
    }

    public void setTransaction_walletSummary_additional(String transaction_walletSummary_additional) {
        this.transaction_walletSummary_additional = transaction_walletSummary_additional;
    }

    public String getCTAppVersion() {
        return CTAppVersion;
    }

    public void setCTAppVersion(String CTAppVersion) {
        this.CTAppVersion = CTAppVersion;
    }

    public String getCTLatitude() {
        return CTLatitude;
    }

    public void setCTLatitude(String CTLatitude) {
        this.CTLatitude = CTLatitude;
    }

    public String getCTLongitutde() {
        return CTLongitutde;
    }

    public void setCTLongitutde(String CTLongitutde) {
        this.CTLongitutde = CTLongitutde;
    }

    public String getCTSource() {
        return CTSource;
    }

    public void setCTSource(String CTSource) {
        this.CTSource = CTSource;
    }

    public String getTransaction_orderSummary_additional() {
        return transaction_orderSummary_additional;
    }

    public void setTransaction_orderSummary_additional(String transaction_orderSummary_additional) {
        this.transaction_orderSummary_additional = transaction_orderSummary_additional;
    }

    public String getRecreationStatus() {
        return recreationStatus;
    }

    public void setRecreationStatus(String recreationStatus) {
        this.recreationStatus = recreationStatus;
    }

    public Integer getCTSessionId() {
        return CTSessionId;
    }

    public void setCTSessionId(Integer CTSessionId) {
        this.CTSessionId = CTSessionId;
    }

    public Integer getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    @Override
    public String toString() {
        return "EventData{" +
            "transaction_orderFree=" + transaction_orderFree +
            ", creationTime='" + billingTime + '\'' +
            ", transaction_wbalIfSucc=" + transaction_wbalIfSucc +
            ", cartId=" + cartId +
            ", transaction_orderSummary_value=" + transaction_orderSummary_value +
            ", transaction_walletSummary_value=" + transaction_walletSummary_value +
            ", source='" + source + '\'' +
            ", recreated='" + recreated + '\'' +
            ", companyId=" + companyId +
            ", transaction_saving=" + transaction_saving +
            ", transaction_orderSummary_offer='" + transaction_orderSummary_offer + '\'' +
            ", availableWallet='" + availableWallet + '\'' +
            ", customerId=" + customerId +
            ", transaction_paid=" + transaction_paid +
            ", unitId=" + unitId +
            ", dontuseWallet=" + dontUseWallet +
            ", transaction_walletSummary_offer='" + transaction_walletSummary_offer + '\'' +
            ", status='" + status + '\'' +
            ", updatedAt='" + updatedAt + '\'' +
            ", transaction_afterWallet='" + transaction_afterWallet + '\'' +
            ", transaction_walletSummary_additional='" + transaction_walletSummary_additional + '\'' +
            ", CTAppVersion='" + CTAppVersion + '\'' +
            ", CTLatitude='" + CTLatitude + '\'' +
            ", CTLongitutde='" + CTLongitutde + '\'' +
            ", CTSource='" + CTSource + '\'' +
            ", transaction_orderSummary_additional='" + transaction_orderSummary_additional + '\'' +
            ", recreationStatus='" + recreationStatus + '\'' +
            ", CTSessionId=" + CTSessionId +
            ", Amount=" + Amount +
            ", currency='" + currency + '\'' +
            ", shipCountry='" + shipCountry + '\'' +
            ", shipRegion='" + shipRegion + '\'' +
            ", shipCity='" + shipCity + '\'' +
            ", email='" + email + '\'' +
            ", customerType='" + customerType + '\'' +
            ", chargedId=" + chargedId +
            ", Items=" + Items.toString() +
            '}';
    }


}
