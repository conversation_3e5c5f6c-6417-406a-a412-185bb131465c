package com.stpl.tech.kettle.clm.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.clm.dao.SpecialOfferDao;
import com.stpl.tech.kettle.clm.service.SpecialOfferService;

@Service
public class SpecialOfferServiceImpl implements SpecialOfferService {

	@Autowired
	private SpecialOfferDao specialOfferDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<String> getOfferString(Integer brandId, Integer customerId, String utmSource, String previousUtmSource,
			String utmMedium, String previousUtmMedium, boolean hasSubscription) {
		return specialOfferDao.getOfferString(brandId, customerId, utmSource, previousUtmSource, utmMedium,
				previousUtmMedium, hasSubscription);
	}
}
