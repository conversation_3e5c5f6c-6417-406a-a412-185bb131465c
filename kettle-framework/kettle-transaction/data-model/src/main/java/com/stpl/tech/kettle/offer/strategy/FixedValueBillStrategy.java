package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;

public class FixedValueBillStrategy extends FlatBillStrategy implements OfferActionStrategy {

	@Override
	public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache,
			Map<String, OrderItem> foundItems) throws OfferValidationException {

		BigDecimal orderValue = offerOrder.getOrder().getTransactionDetail().getPaidAmount();
		BigDecimal flatDiscount = BigDecimal.ZERO;
		BigDecimal offerValue = new BigDecimal(coupon.getOffer().getOfferValue());

		if (orderValue.compareTo(offerValue) >= 0) {
			flatDiscount = orderValue.subtract(offerValue);
		} else {
			flatDiscount = orderValue;
		}

		return applyStrategy(String.format(messageTemplate, flatDiscount.intValue()), flatDiscount, offerOrder, coupon,
				cache);

	}
}
