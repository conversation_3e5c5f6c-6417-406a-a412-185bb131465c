/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.mappers;

import java.math.BigDecimal;

import org.apache.poi.ss.usermodel.Cell;

import com.stpl.tech.kettle.core.file.load.AbstractRowMapper;
import com.stpl.tech.kettle.domain.model.UnitExpense;

public class ExpenseRowMapper extends AbstractRowMapper<UnitExpense, Cell> {

	@Override
	public void setData(UnitExpense expense, Cell nextCell) {
		try {
			int columnIndex = nextCell.getColumnIndex();
			switch (columnIndex) {
			// 0 Comment
			case 0:
				expense.setComments((String) getCellValue(nextCell));
				break;
			// 1 Unit Id
			case 1:
				expense.setUnitId(((Double) getCellValue(nextCell)).intValue());
				break;
			// 2 Unit Name - not required
			case 2:
				expense.setUnitName((String) getCellValue(nextCell));
				break;
			// 3 Manpower
			case 3:
				expense.setManpower(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 4 Consumables - Utilities
			case 4:
				expense.setConsumablesAndUtilities(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 5 Consumables - Stationary
			case 5:
				expense.setConsumablesStationary(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 6 Consumables - Uniform
			case 6:
				expense.setConsumablesUniform(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 7 Consumables - Equipment
			case 7:
				expense.setConsumablesEquipment(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 8 Consumables - Cutlery
			case 8:
				expense.setConsumablesCutlery(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 9 Wastage & Expired
			case 9:
				expense.setWastageAndExpired(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 10 Electricity
			case 10:
				expense.setElectricity(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 11 Water
			case 11:
				expense.setWater(new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 12 DG - Rent
			case 12:
				expense.setRentDG(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 13 DG - Charges
			case 13:
				expense.setChargesDG(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 14 SCM Rental
			case 14:
				expense.setScmRental(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 15 EDC Machine
			case 15:
				expense.setEdcMachine(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 16 Freight Outward
			case 16:
				expense.setFreightOutward(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 17 Conveyance
			case 17:
				expense.setConvenyance(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 18 Staff Welfare
			case 18:
				expense.setStaffWelfare(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 19 Repair & Maintenance (Minor)
			case 19:
				expense.setRepairAndMaintenanceMinor(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 20 Change Commission
			case 20:
				expense.setChangeCommission(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 21 Courier
			case 21:
				expense.setCourier(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 22 Printing & Stationary
			case 22:
				expense.setPrintingAndStationery(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 23 Misc Exp
			case 23:
				expense.setMiscExp(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 24 Parking charges
			case 24:
				expense.setParkingCharges(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 25 Cleaning Charges
			case 25:
				expense.setCleaningCharges(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 26 Newspaper
			case 26:
				expense.setNewspaper(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 27 Fixed Rent
			case 27:
				expense.setFixedRent(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 28 CAM
			case 28:
				expense.setCamCharges(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 29 Internet
			case 29:
				expense.setInternet(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 30 Telephone
			case 30:
				expense.setTelephone(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 31 Ops Cost
			case 31:
				expense.setOpsCostPercentage(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 32 Kitchen Cost
			case 32:
				expense.setKitchenCostPercentage(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 33 Repair & Maintenance
			case 33:
				expense.setRepairAndMaintenanceMajor(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 34 Rent Percentage
			case 34:
				expense.setRentPercentage(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 35 Customer Care Cost
			case 35:
				expense.setCustomerCareCost(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 36 Maintenance Team Cost
			case 36:
				expense.setMaintenanceTeamCost(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 37 Training Team Cost
			case 37:
				expense.setTrainingTeamCost(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 38 IT Team Cost
			case 38:
				expense.setItTeamCost(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 39 MSP
			case 39:
				expense.setMsp(new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 40 System Rent
			case 40:
				expense.setSystemRent(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 41 Insurance
			case 41:
				expense.setInsurance(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			default:
				break;
			}
		} catch (Exception e) {
			addError(nextCell, e.getMessage());
		}
	}

	@Override
	public UnitExpense createNewInstance() {
		return new UnitExpense();
	}


}
