package com.stpl.tech.kettle.datalake.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CUSTOMER_ONE_VIEW_FEEDBACK_STATS")
public class CustomerNPSFeedbackStats {

	private Date businessDate;
	private Integer customerId; // @Id

	private String isLatest;
	private String isLastFeedbackOfLastOrder;
	private String isLastCafeFeedbackOfLastOrder;
	private String isLastDeliveryFeedbackOfLastOrder;

	private Integer lastNpsSurveyId;
	private Integer secondLastNpsSurveyId;
	private Integer thirdLastNpsSurveyId;

	private Integer lastNpsScore;
	private Integer secondLastNpsScore;
	private Integer thirdLastNpsScore;

	private Date lastNpsScoreDate;
	private Date secondLastNpsScoreDate;
	private Date thirdLastNpsScoreDate;

	private Date lastNpsScoreOrderDate;
	private Date secondLastNpsScoreOrderDate;
	private Date thirdLastNpsScoreOrderDate;

	private Integer lastNpsScoreTimeGap;
	private Integer secondLastNpsScoreTimeGap;
	private Integer thirdLastNpsScoreTimeGap;

	private Integer lastNpsScoreOrderId;
	private Integer secondLastNpsScoreOrderId;
	private Integer thirdLastNpsScoreOrderId;

	private String lastNpsScoreOrderSource;
	private String secondLastNpsScoreOrderSource;
	private String ThirdLastNpsScoreOrderSource;

	private String lastNpsScoreCallBackRequested;
	private String secondLastNpsScoreCallBackRequested;
	private String thirdLastNpsScoreCallBackRequested;

	private Integer lastNpsScoreChannelPartner;
	private Integer secondLastNpsScoreChannelPartner;
	private Integer thirdLastNpsScoreChannelPartner;

	private Integer lastNpsScoreUnitId;
	private Integer secondLastNpsScoreUnitId;
	private Integer thirdLastNpsScoreUnitId;

	// cafe
	private Integer cafeLastNpsScore;
	private Integer cafeSecondLastNpsScore;
	private Integer cafeThirdLastNpsScore;

	private Date cafeLastNpsScoreDate;
	private Date cafeSecondLastNpsScoreDate;
	private Date cafeThirdLastNpsScoreDate;

	private Integer cafeLastNpsScoreOrderId;
	private Integer cafeSecondLastNpsScoreOrderId;
	private Integer cafeThirdLastNpsScoreOrderId;

	private String cafeLastNpsScoreOrderSource;
	private String cafeSecondLastNpsScoreOrderSource;
	private String cafeThirdLastNpsScoreOrderSource;

	private String cafeLastNpsScoreCallBackRequested;
	private String cafeSecondLastNpsScoreCallBackRequested;
	private String cafeThirdLastNpsScoreCallBackRequested;

	private Integer cafeLastNpsScoreChannelPartner;
	private Integer cafeSecondLastNpsScoreChannelPartner;
	private Integer cafeThirdLastNpsScoreChannelPartner;

	private Integer cafeLastNpsScoreUnitId;
	private Integer cafeSecondLastNpsScoreUnitId;
	private Integer cafeThirdLastNpsScoreUnitId;

	// delivery
	private Integer deliveryLastNpsScore;
	private Integer deliverySecondLastNpsScore;
	private Integer deliveryThirdLastNpsScore;

	private Date deliveryLastNpsScoreDate;
	private Date deliverySecondLastNpsScoreDate;
	private Date deliveryThirdLastNpsScoreDate;

	private Integer deliveryLastNpsScoreOrderId;
	private Integer deliverySecondLastNpsScoreOrderId;
	private Integer deliveryThirdLastNpsScoreOrderId;

	private String deliveryLastNpsScoreOrderSource;
	private String deliverySecondLastNpsScoreOrderSource;
	private String deliveryThirdLastNpsScoreOrderSource;

	private String deliveryLastNpsScoreCallBackRequested;
	private String deliverySecondLastNpsScoreCallBackRequested;
	private String deliveryThirdLastNpsScoreCallBackRequested;

	private Integer deliveryLastNpsScoreChannelPartner;
	private Integer deliverySecondLastNpsScoreChannelPartner;
	private Integer deliveryThirdLastNpsScoreChannelPartner;

	private Integer deliveryLastNpsScoreUnitId;
	private Integer deliverySecondLastNpsScoreUnitId;
	private Integer deliveryThirdLastNpsScoreUnitId;

	private Integer lastFeedbackAmbienceScore;
	private Integer lastFeedbackFoodScore;
	private Integer lastFeedbackBeverageScore;
	private Integer lastFeedbackServiceScore;
	private Integer lastFeedbackDeliveryScore;

	private Integer secondLastFeedbackAmbienceScore;
	private Integer secondLastFeedbackFoodScore;
	private Integer secondLastFeedbackBeverageScore;
	private Integer secondLastFeedbackServiceScore;
	private Integer secondLastFeedbackDeliveryScore;

	private Integer thirdLastFeedbackAmbienceScore;
	private Integer thirdLastFeedbackFoodScore;
	private Integer thirdLastFeedbackBeverageScore;
	private Integer thirdLastFeedbackServiceScore;
	private Integer thirdLastFeedbackDeliveryScore;

	//
	private Integer totalNps;
	private Integer totalCafeNps;
	private Integer totalDeliveryNps;

	private BigDecimal avgNps;
	private BigDecimal avgCafeNps;
	private BigDecimal avgDeliveryNps;

	private Integer totalNpsInLast180Days;
	private Integer totalCafeNpsInLast180Days;
	private Integer totalDeliveryNpsInLast180Days;

	private BigDecimal avgNpsInLast180Days;
	private BigDecimal avgCafeNpsInLast180Days;
	private BigDecimal avgDeliveryNpsInLast180Days;

	@Column(name = "BUSINESS_DATE")
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Id
	@Column(name = "CUSTOMER_ID")
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "IS_LATEST")
	public String getIsLatest() {
		return isLatest;
	}

	public void setIsLatest(String isLatest) {
		this.isLatest = isLatest;
	}

	@Column(name = "IS_LAST_FEEDBACK_OF_LAST_ORDER")
	public String getIsLastFeedbackOfLastOrder() {
		return isLastFeedbackOfLastOrder;
	}

	public void setIsLastFeedbackOfLastOrder(String isLastFeedbackOfLastOrder) {
		this.isLastFeedbackOfLastOrder = isLastFeedbackOfLastOrder;
	}

	@Column(name = "IS_LAST_CAFE_FEEDBACK_OF_LAST_CAFE_ORDER")
	public String getIsLastCafeFeedbackOfLastOrder() {
		return isLastCafeFeedbackOfLastOrder;
	}

	public void setIsLastCafeFeedbackOfLastOrder(String isLastCafeFeedbackOfLastOrder) {
		this.isLastCafeFeedbackOfLastOrder = isLastCafeFeedbackOfLastOrder;
	}

	@Column(name = "IS_LAST_DELIVERY_FEEDBACK_OF_LAST_DELIVERY_ORDER")
	public String getIsLastDeliveryFeedbackOfLastOrder() {
		return isLastDeliveryFeedbackOfLastOrder;
	}

	public void setIsLastDeliveryFeedbackOfLastOrder(String isLastDeliveryFeedbackOfLastOrder) {
		this.isLastDeliveryFeedbackOfLastOrder = isLastDeliveryFeedbackOfLastOrder;
	}

	@Column(name = "LAST_NPS_SURVEY_ID")
	public Integer getLastNpsSurveyId() {
		return lastNpsSurveyId;
	}

	public void setLastNpsSurveyId(Integer lastNpsSurveyId) {
		this.lastNpsSurveyId = lastNpsSurveyId;
	}

	@Column(name = "SECOND_LAST_NPS_SURVEY_ID")
	public Integer getSecondLastNpsSurveyId() {
		return secondLastNpsSurveyId;
	}

	public void setSecondLastNpsSurveyId(Integer secondLastNpsSurveyId) {
		this.secondLastNpsSurveyId = secondLastNpsSurveyId;
	}

	@Column(name = "THIRD_LAST_NPS_SURVEY_ID")
	public Integer getThirdLastNpsSurveyId() {
		return thirdLastNpsSurveyId;
	}

	public void setThirdLastNpsSurveyId(Integer thirdLastNpsSurveyId) {
		this.thirdLastNpsSurveyId = thirdLastNpsSurveyId;
	}

	@Column(name = "LAST_NPS_SCORE")
	public Integer getLastNpsScore() {
		return lastNpsScore;
	}

	public void setLastNpsScore(Integer lastNpsScore) {
		this.lastNpsScore = lastNpsScore;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE")
	public Integer getSecondLastNpsScore() {
		return secondLastNpsScore;
	}

	public void setSecondLastNpsScore(Integer secondLastNpsScore) {
		this.secondLastNpsScore = secondLastNpsScore;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE")
	public Integer getThirdLastNpsScore() {
		return thirdLastNpsScore;
	}

	public void setThirdLastNpsScore(Integer thirdLastNpsScore) {
		this.thirdLastNpsScore = thirdLastNpsScore;
	}

	@Column(name = "LAST_NPS_SCORE_DATE")
	public Date getLastNpsScoreDate() {
		return lastNpsScoreDate;
	}

	public void setLastNpsScoreDate(Date lastNpsScoreDate) {
		this.lastNpsScoreDate = lastNpsScoreDate;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_DATE")
	public Date getSecondLastNpsScoreDate() {
		return secondLastNpsScoreDate;
	}

	public void setSecondLastNpsScoreDate(Date secondLastNpsScoreDate) {
		this.secondLastNpsScoreDate = secondLastNpsScoreDate;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_DATE")
	public Date getThirdLastNpsScoreDate() {
		return thirdLastNpsScoreDate;
	}

	public void setThirdLastNpsScoreDate(Date thirdLastNpsScoreDate) {
		this.thirdLastNpsScoreDate = thirdLastNpsScoreDate;
	}

	@Column(name = "LAST_NPS_SCORE_ORDER_DATE")
	public Date getLastNpsScoreOrderDate() {
		return lastNpsScoreOrderDate;
	}

	public void setLastNpsScoreOrderDate(Date lastNpsScoreOrderDate) {
		this.lastNpsScoreOrderDate = lastNpsScoreOrderDate;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_ORDER_DATE")
	public Date getSecondLastNpsScoreOrderDate() {
		return secondLastNpsScoreOrderDate;
	}

	public void setSecondLastNpsScoreOrderDate(Date secondLastNpsScoreOrderDate) {
		this.secondLastNpsScoreOrderDate = secondLastNpsScoreOrderDate;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_ORDER_DATE")
	public Date getThirdLastNpsScoreOrderDate() {
		return thirdLastNpsScoreOrderDate;
	}

	public void setThirdLastNpsScoreOrderDate(Date thirdLastNpsScoreOrderDate) {
		this.thirdLastNpsScoreOrderDate = thirdLastNpsScoreOrderDate;
	}

	@Column(name = "LAST_NPS_SCORE_TIME_GAP")
	public Integer getLastNpsScoreTimeGap() {
		return lastNpsScoreTimeGap;
	}

	public void setLastNpsScoreTimeGap(Integer lastNpsScoreTimeGap) {
		this.lastNpsScoreTimeGap = lastNpsScoreTimeGap;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_TIME_GAP")
	public Integer getSecondLastNpsScoreTimeGap() {
		return secondLastNpsScoreTimeGap;
	}

	public void setSecondLastNpsScoreTimeGap(Integer secondLastNpsScoreTimeGap) {
		this.secondLastNpsScoreTimeGap = secondLastNpsScoreTimeGap;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_TIME_GAP")
	public Integer getThirdLastNpsScoreTimeGap() {
		return thirdLastNpsScoreTimeGap;
	}

	public void setThirdLastNpsScoreTimeGap(Integer thirdLastNpsScoreTimeGap) {
		this.thirdLastNpsScoreTimeGap = thirdLastNpsScoreTimeGap;
	}

	@Column(name = "LAST_NPS_SCORE_ORDER_ID")
	public Integer getLastNpsScoreOrderId() {
		return lastNpsScoreOrderId;
	}

	public void setLastNpsScoreOrderId(Integer lastNpsScoreOrderId) {
		this.lastNpsScoreOrderId = lastNpsScoreOrderId;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_ORDER_ID")
	public Integer getSecondLastNpsScoreOrderId() {
		return secondLastNpsScoreOrderId;
	}

	public void setSecondLastNpsScoreOrderId(Integer secondLastNpsScoreOrderId) {
		this.secondLastNpsScoreOrderId = secondLastNpsScoreOrderId;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_ORDER_ID")
	public Integer getThirdLastNpsScoreOrderId() {
		return thirdLastNpsScoreOrderId;
	}

	public void setThirdLastNpsScoreOrderId(Integer thirdLastNpsScoreOrderId) {
		this.thirdLastNpsScoreOrderId = thirdLastNpsScoreOrderId;
	}

	@Column(name = "LAST_NPS_SCORE_ORDER_SOURCE")
	public String getLastNpsScoreOrderSource() {
		return lastNpsScoreOrderSource;
	}

	public void setLastNpsScoreOrderSource(String lastNpsScoreOrderSource) {
		this.lastNpsScoreOrderSource = lastNpsScoreOrderSource;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getSecondLastNpsScoreOrderSource() {
		return secondLastNpsScoreOrderSource;
	}

	public void setSecondLastNpsScoreOrderSource(String secondLastNpsScoreOrderSource) {
		this.secondLastNpsScoreOrderSource = secondLastNpsScoreOrderSource;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getThirdLastNpsScoreOrderSource() {
		return ThirdLastNpsScoreOrderSource;
	}

	public void setThirdLastNpsScoreOrderSource(String thirdLastNpsScoreOrderSource) {
		ThirdLastNpsScoreOrderSource = thirdLastNpsScoreOrderSource;
	}

	@Column(name = "LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getLastNpsScoreCallBackRequested() {
		return lastNpsScoreCallBackRequested;
	}

	public void setLastNpsScoreCallBackRequested(String lastNpsScoreCallBackRequested) {
		this.lastNpsScoreCallBackRequested = lastNpsScoreCallBackRequested;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getSecondLastNpsScoreCallBackRequested() {
		return secondLastNpsScoreCallBackRequested;
	}

	public void setSecondLastNpsScoreCallBackRequested(String secondLastNpsScoreCallBackRequested) {
		this.secondLastNpsScoreCallBackRequested = secondLastNpsScoreCallBackRequested;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getThirdLastNpsScoreCallBackRequested() {
		return thirdLastNpsScoreCallBackRequested;
	}

	public void setThirdLastNpsScoreCallBackRequested(String thirdLastNpsScoreCallBackRequested) {
		this.thirdLastNpsScoreCallBackRequested = thirdLastNpsScoreCallBackRequested;
	}

	@Column(name = "LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getLastNpsScoreChannelPartner() {
		return lastNpsScoreChannelPartner;
	}

	public void setLastNpsScoreChannelPartner(Integer lastNpsScoreChannelPartner) {
		this.lastNpsScoreChannelPartner = lastNpsScoreChannelPartner;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getSecondLastNpsScoreChannelPartner() {
		return secondLastNpsScoreChannelPartner;
	}

	public void setSecondLastNpsScoreChannelPartner(Integer secondLastNpsScoreChannelPartner) {
		this.secondLastNpsScoreChannelPartner = secondLastNpsScoreChannelPartner;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getThirdLastNpsScoreChannelPartner() {
		return thirdLastNpsScoreChannelPartner;
	}

	public void setThirdLastNpsScoreChannelPartner(Integer thirdLastNpsScoreChannelPartner) {
		this.thirdLastNpsScoreChannelPartner = thirdLastNpsScoreChannelPartner;
	}

	@Column(name = "LAST_NPS_SCORE_UNIT_ID")
	public Integer getLastNpsScoreUnitId() {
		return lastNpsScoreUnitId;
	}

	public void setLastNpsScoreUnitId(Integer lastNpsScoreUnitId) {
		this.lastNpsScoreUnitId = lastNpsScoreUnitId;
	}

	@Column(name = "SECOND_LAST_NPS_SCORE_UNIT_ID")
	public Integer getSecondLastNpsScoreUnitId() {
		return secondLastNpsScoreUnitId;
	}

	public void setSecondLastNpsScoreUnitId(Integer secondLastNpsScoreUnitId) {
		this.secondLastNpsScoreUnitId = secondLastNpsScoreUnitId;
	}

	@Column(name = "THIRD_LAST_NPS_SCORE_UNIT_ID")
	public Integer getThirdLastNpsScoreUnitId() {
		return thirdLastNpsScoreUnitId;
	}

	public void setThirdLastNpsScoreUnitId(Integer thirdLastNpsScoreUnitId) {
		this.thirdLastNpsScoreUnitId = thirdLastNpsScoreUnitId;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE")
	public Integer getCafeLastNpsScore() {
		return cafeLastNpsScore;
	}

	public void setCafeLastNpsScore(Integer cafeLastNpsScore) {
		this.cafeLastNpsScore = cafeLastNpsScore;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE")
	public Integer getCafeSecondLastNpsScore() {
		return cafeSecondLastNpsScore;
	}

	public void setCafeSecondLastNpsScore(Integer cafeSecondLastNpsScore) {
		this.cafeSecondLastNpsScore = cafeSecondLastNpsScore;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE")
	public Integer getCafeThirdLastNpsScore() {
		return cafeThirdLastNpsScore;
	}

	public void setCafeThirdLastNpsScore(Integer cafeThirdLastNpsScore) {
		this.cafeThirdLastNpsScore = cafeThirdLastNpsScore;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE_DATE")
	public Date getCafeLastNpsScoreDate() {
		return cafeLastNpsScoreDate;
	}

	public void setCafeLastNpsScoreDate(Date cafeLastNpsScoreDate) {
		this.cafeLastNpsScoreDate = cafeLastNpsScoreDate;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE_DATE")
	public Date getCafeSecondLastNpsScoreDate() {
		return cafeSecondLastNpsScoreDate;
	}

	public void setCafeSecondLastNpsScoreDate(Date cafeSecondLastNpsScoreDate) {
		this.cafeSecondLastNpsScoreDate = cafeSecondLastNpsScoreDate;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE_DATE")
	public Date getCafeThirdLastNpsScoreDate() {
		return cafeThirdLastNpsScoreDate;
	}

	public void setCafeThirdLastNpsScoreDate(Date cafeThirdLastNpsScoreDate) {
		this.cafeThirdLastNpsScoreDate = cafeThirdLastNpsScoreDate;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE_ORDER_ID")
	public Integer getCafeLastNpsScoreOrderId() {
		return cafeLastNpsScoreOrderId;
	}

	public void setCafeLastNpsScoreOrderId(Integer cafeLastNpsScoreOrderId) {
		this.cafeLastNpsScoreOrderId = cafeLastNpsScoreOrderId;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE_ORDER_ID")
	public Integer getCafeSecondLastNpsScoreOrderId() {
		return cafeSecondLastNpsScoreOrderId;
	}

	public void setCafeSecondLastNpsScoreOrderId(Integer cafeSecondLastNpsScoreOrderId) {
		this.cafeSecondLastNpsScoreOrderId = cafeSecondLastNpsScoreOrderId;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE_ORDER_ID")
	public Integer getCafeThirdLastNpsScoreOrderId() {
		return cafeThirdLastNpsScoreOrderId;
	}

	public void setCafeThirdLastNpsScoreOrderId(Integer cafeThirdLastNpsScoreOrderId) {
		this.cafeThirdLastNpsScoreOrderId = cafeThirdLastNpsScoreOrderId;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getCafeLastNpsScoreOrderSource() {
		return cafeLastNpsScoreOrderSource;
	}

	public void setCafeLastNpsScoreOrderSource(String cafeLastNpsScoreOrderSource) {
		this.cafeLastNpsScoreOrderSource = cafeLastNpsScoreOrderSource;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getCafeSecondLastNpsScoreOrderSource() {
		return cafeSecondLastNpsScoreOrderSource;
	}

	public void setCafeSecondLastNpsScoreOrderSource(String cafeSecondLastNpsScoreOrderSource) {
		this.cafeSecondLastNpsScoreOrderSource = cafeSecondLastNpsScoreOrderSource;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getCafeThirdLastNpsScoreOrderSource() {
		return cafeThirdLastNpsScoreOrderSource;
	}

	public void setCafeThirdLastNpsScoreOrderSource(String cafeThirdLastNpsScoreOrderSource) {
		this.cafeThirdLastNpsScoreOrderSource = cafeThirdLastNpsScoreOrderSource;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getCafeLastNpsScoreCallBackRequested() {
		return cafeLastNpsScoreCallBackRequested;
	}

	public void setCafeLastNpsScoreCallBackRequested(String cafeLastNpsScoreCallBackRequested) {
		this.cafeLastNpsScoreCallBackRequested = cafeLastNpsScoreCallBackRequested;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getCafeSecondLastNpsScoreCallBackRequested() {
		return cafeSecondLastNpsScoreCallBackRequested;
	}

	public void setCafeSecondLastNpsScoreCallBackRequested(String cafeSecondLastNpsScoreCallBackRequested) {
		this.cafeSecondLastNpsScoreCallBackRequested = cafeSecondLastNpsScoreCallBackRequested;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getCafeThirdLastNpsScoreCallBackRequested() {
		return cafeThirdLastNpsScoreCallBackRequested;
	}

	public void setCafeThirdLastNpsScoreCallBackRequested(String cafeThirdLastNpsScoreCallBackRequested) {
		this.cafeThirdLastNpsScoreCallBackRequested = cafeThirdLastNpsScoreCallBackRequested;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getCafeLastNpsScoreChannelPartner() {
		return cafeLastNpsScoreChannelPartner;
	}

	public void setCafeLastNpsScoreChannelPartner(Integer cafeLastNpsScoreChannelPartner) {
		this.cafeLastNpsScoreChannelPartner = cafeLastNpsScoreChannelPartner;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getCafeSecondLastNpsScoreChannelPartner() {
		return cafeSecondLastNpsScoreChannelPartner;
	}

	public void setCafeSecondLastNpsScoreChannelPartner(Integer cafeSecondLastNpsScoreChannelPartner) {
		this.cafeSecondLastNpsScoreChannelPartner = cafeSecondLastNpsScoreChannelPartner;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getCafeThirdLastNpsScoreChannelPartner() {
		return cafeThirdLastNpsScoreChannelPartner;
	}

	public void setCafeThirdLastNpsScoreChannelPartner(Integer cafeThirdLastNpsScoreChannelPartner) {
		this.cafeThirdLastNpsScoreChannelPartner = cafeThirdLastNpsScoreChannelPartner;
	}

	@Column(name = "CAFE_LAST_NPS_SCORE_UNIT_ID")
	public Integer getCafeLastNpsScoreUnitId() {
		return cafeLastNpsScoreUnitId;
	}

	public void setCafeLastNpsScoreUnitId(Integer cafeLastNpsScoreUnitId) {
		this.cafeLastNpsScoreUnitId = cafeLastNpsScoreUnitId;
	}

	@Column(name = "CAFE_SECOND_LAST_NPS_SCORE_UNIT_ID")
	public Integer getCafeSecondLastNpsScoreUnitId() {
		return cafeSecondLastNpsScoreUnitId;
	}

	public void setCafeSecondLastNpsScoreUnitId(Integer cafeSecondLastNpsScoreUnitId) {
		this.cafeSecondLastNpsScoreUnitId = cafeSecondLastNpsScoreUnitId;
	}

	@Column(name = "CAFE_THIRD_LAST_NPS_SCORE_UNIT_ID")
	public Integer getCafeThirdLastNpsScoreUnitId() {
		return cafeThirdLastNpsScoreUnitId;
	}

	public void setCafeThirdLastNpsScoreUnitId(Integer cafeThirdLastNpsScoreUnitId) {
		this.cafeThirdLastNpsScoreUnitId = cafeThirdLastNpsScoreUnitId;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE")
	public Integer getDeliveryLastNpsScore() {
		return deliveryLastNpsScore;
	}

	public void setDeliveryLastNpsScore(Integer deliveryLastNpsScore) {
		this.deliveryLastNpsScore = deliveryLastNpsScore;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE")
	public Integer getDeliverySecondLastNpsScore() {
		return deliverySecondLastNpsScore;
	}

	public void setDeliverySecondLastNpsScore(Integer deliverySecondLastNpsScore) {
		this.deliverySecondLastNpsScore = deliverySecondLastNpsScore;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE")
	public Integer getDeliveryThirdLastNpsScore() {
		return deliveryThirdLastNpsScore;
	}

	public void setDeliveryThirdLastNpsScore(Integer deliveryThirdLastNpsScore) {
		this.deliveryThirdLastNpsScore = deliveryThirdLastNpsScore;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE_DATE")
	public Date getDeliveryLastNpsScoreDate() {
		return deliveryLastNpsScoreDate;
	}

	public void setDeliveryLastNpsScoreDate(Date deliveryLastNpsScoreDate) {
		this.deliveryLastNpsScoreDate = deliveryLastNpsScoreDate;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE_DATE")
	public Date getDeliverySecondLastNpsScoreDate() {
		return deliverySecondLastNpsScoreDate;
	}

	public void setDeliverySecondLastNpsScoreDate(Date deliverySecondLastNpsScoreDate) {
		this.deliverySecondLastNpsScoreDate = deliverySecondLastNpsScoreDate;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE_DATE")
	public Date getDeliveryThirdLastNpsScoreDate() {
		return deliveryThirdLastNpsScoreDate;
	}

	public void setDeliveryThirdLastNpsScoreDate(Date deliveryThirdLastNpsScoreDate) {
		this.deliveryThirdLastNpsScoreDate = deliveryThirdLastNpsScoreDate;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE_ORDER_ID")
	public Integer getDeliveryLastNpsScoreOrderId() {
		return deliveryLastNpsScoreOrderId;
	}

	public void setDeliveryLastNpsScoreOrderId(Integer deliveryLastNpsScoreOrderId) {
		this.deliveryLastNpsScoreOrderId = deliveryLastNpsScoreOrderId;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_ID")
	public Integer getDeliverySecondLastNpsScoreOrderId() {
		return deliverySecondLastNpsScoreOrderId;
	}

	public void setDeliverySecondLastNpsScoreOrderId(Integer deliverySecondLastNpsScoreOrderId) {
		this.deliverySecondLastNpsScoreOrderId = deliverySecondLastNpsScoreOrderId;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_ID")
	public Integer getDeliveryThirdLastNpsScoreOrderId() {
		return deliveryThirdLastNpsScoreOrderId;
	}

	public void setDeliveryThirdLastNpsScoreOrderId(Integer deliveryThirdLastNpsScoreOrderId) {
		this.deliveryThirdLastNpsScoreOrderId = deliveryThirdLastNpsScoreOrderId;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getDeliveryLastNpsScoreOrderSource() {
		return deliveryLastNpsScoreOrderSource;
	}

	public void setDeliveryLastNpsScoreOrderSource(String deliveryLastNpsScoreOrderSource) {
		this.deliveryLastNpsScoreOrderSource = deliveryLastNpsScoreOrderSource;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getDeliverySecondLastNpsScoreOrderSource() {
		return deliverySecondLastNpsScoreOrderSource;
	}

	public void setDeliverySecondLastNpsScoreOrderSource(String deliverySecondLastNpsScoreOrderSource) {
		this.deliverySecondLastNpsScoreOrderSource = deliverySecondLastNpsScoreOrderSource;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_SOURCE")
	public String getDeliveryThirdLastNpsScoreOrderSource() {
		return deliveryThirdLastNpsScoreOrderSource;
	}

	public void setDeliveryThirdLastNpsScoreOrderSource(String deliveryThirdLastNpsScoreOrderSource) {
		this.deliveryThirdLastNpsScoreOrderSource = deliveryThirdLastNpsScoreOrderSource;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getDeliveryLastNpsScoreCallBackRequested() {
		return deliveryLastNpsScoreCallBackRequested;
	}

	public void setDeliveryLastNpsScoreCallBackRequested(String deliveryLastNpsScoreCallBackRequested) {
		this.deliveryLastNpsScoreCallBackRequested = deliveryLastNpsScoreCallBackRequested;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getDeliverySecondLastNpsScoreCallBackRequested() {
		return deliverySecondLastNpsScoreCallBackRequested;
	}

	public void setDeliverySecondLastNpsScoreCallBackRequested(String deliverySecondLastNpsScoreCallBackRequested) {
		this.deliverySecondLastNpsScoreCallBackRequested = deliverySecondLastNpsScoreCallBackRequested;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE_CALLBACK_REQUESTED")
	public String getDeliveryThirdLastNpsScoreCallBackRequested() {
		return deliveryThirdLastNpsScoreCallBackRequested;
	}

	public void setDeliveryThirdLastNpsScoreCallBackRequested(String deliveryThirdLastNpsScoreCallBackRequested) {
		this.deliveryThirdLastNpsScoreCallBackRequested = deliveryThirdLastNpsScoreCallBackRequested;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getDeliveryLastNpsScoreChannelPartner() {
		return deliveryLastNpsScoreChannelPartner;
	}

	public void setDeliveryLastNpsScoreChannelPartner(Integer deliveryLastNpsScoreChannelPartner) {
		this.deliveryLastNpsScoreChannelPartner = deliveryLastNpsScoreChannelPartner;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getDeliverySecondLastNpsScoreChannelPartner() {
		return deliverySecondLastNpsScoreChannelPartner;
	}

	public void setDeliverySecondLastNpsScoreChannelPartner(Integer deliverySecondLastNpsScoreChannelPartner) {
		this.deliverySecondLastNpsScoreChannelPartner = deliverySecondLastNpsScoreChannelPartner;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER")
	public Integer getDeliveryThirdLastNpsScoreChannelPartner() {
		return deliveryThirdLastNpsScoreChannelPartner;
	}

	public void setDeliveryThirdLastNpsScoreChannelPartner(Integer deliveryThirdLastNpsScoreChannelPartner) {
		this.deliveryThirdLastNpsScoreChannelPartner = deliveryThirdLastNpsScoreChannelPartner;
	}

	@Column(name = "DELIVERY_LAST_NPS_SCORE_UNIT_ID")
	public Integer getDeliveryLastNpsScoreUnitId() {
		return deliveryLastNpsScoreUnitId;
	}

	public void setDeliveryLastNpsScoreUnitId(Integer deliveryLastNpsScoreUnitId) {
		this.deliveryLastNpsScoreUnitId = deliveryLastNpsScoreUnitId;
	}

	@Column(name = "DELIVERY_SECOND_LAST_NPS_SCORE_UNIT_ID")
	public Integer getDeliverySecondLastNpsScoreUnitId() {
		return deliverySecondLastNpsScoreUnitId;
	}

	public void setDeliverySecondLastNpsScoreUnitId(Integer deliverySecondLastNpsScoreUnitId) {
		this.deliverySecondLastNpsScoreUnitId = deliverySecondLastNpsScoreUnitId;
	}

	@Column(name = "DELIVERY_THIRD_LAST_NPS_SCORE_UNIT_ID")
	public Integer getDeliveryThirdLastNpsScoreUnitId() {
		return deliveryThirdLastNpsScoreUnitId;
	}

	public void setDeliveryThirdLastNpsScoreUnitId(Integer deliveryThirdLastNpsScoreUnitId) {
		this.deliveryThirdLastNpsScoreUnitId = deliveryThirdLastNpsScoreUnitId;
	}

	@Column(name = "LAST_FEEDBACK_AMBIENCE_SCORE")
	public Integer getLastFeedbackAmbienceScore() {
		return lastFeedbackAmbienceScore;
	}

	public void setLastFeedbackAmbienceScore(Integer lastFeedbackAmbienceScore) {
		this.lastFeedbackAmbienceScore = lastFeedbackAmbienceScore;
	}

	@Column(name = "LAST_FEEDBACK_FOOD_SCORE")
	public Integer getLastFeedbackFoodScore() {
		return lastFeedbackFoodScore;
	}

	public void setLastFeedbackFoodScore(Integer lastFeedbackFoodScore) {
		this.lastFeedbackFoodScore = lastFeedbackFoodScore;
	}

	@Column(name = "LAST_FEEDBACK_BEVERAGE_SCORE")
	public Integer getLastFeedbackBeverageScore() {
		return lastFeedbackBeverageScore;
	}

	public void setLastFeedbackBeverageScore(Integer lastFeedbackBeverageScore) {
		this.lastFeedbackBeverageScore = lastFeedbackBeverageScore;
	}

	@Column(name = "LAST_FEEDBACK_SERVICE_SCORE")
	public Integer getLastFeedbackServiceScore() {
		return lastFeedbackServiceScore;
	}

	public void setLastFeedbackServiceScore(Integer lastFeedbackServiceScore) {
		this.lastFeedbackServiceScore = lastFeedbackServiceScore;
	}

	@Column(name = "LAST_FEEDBACK_DELIVERY_SCORE")
	public Integer getLastFeedbackDeliveryScore() {
		return lastFeedbackDeliveryScore;
	}

	public void setLastFeedbackDeliveryScore(Integer lastFeedbackDeliveryScore) {
		this.lastFeedbackDeliveryScore = lastFeedbackDeliveryScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_AMBIENCE_SCORE")
	public Integer getSecondLastFeedbackAmbienceScore() {
		return secondLastFeedbackAmbienceScore;
	}

	public void setSecondLastFeedbackAmbienceScore(Integer secondLastFeedbackAmbienceScore) {
		this.secondLastFeedbackAmbienceScore = secondLastFeedbackAmbienceScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_FOOD_SCORE")
	public Integer getSecondLastFeedbackFoodScore() {
		return secondLastFeedbackFoodScore;
	}

	public void setSecondLastFeedbackFoodScore(Integer secondLastFeedbackFoodScore) {
		this.secondLastFeedbackFoodScore = secondLastFeedbackFoodScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_BEVERAGE_SCORE")
	public Integer getSecondLastFeedbackBeverageScore() {
		return secondLastFeedbackBeverageScore;
	}

	public void setSecondLastFeedbackBeverageScore(Integer secondLastFeedbackBeverageScore) {
		this.secondLastFeedbackBeverageScore = secondLastFeedbackBeverageScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_SERVICE_SCORE")
	public Integer getSecondLastFeedbackServiceScore() {
		return secondLastFeedbackServiceScore;
	}

	public void setSecondLastFeedbackServiceScore(Integer secondLastFeedbackServiceScore) {
		this.secondLastFeedbackServiceScore = secondLastFeedbackServiceScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_DELIVERY_SCORE")
	public Integer getSecondLastFeedbackDeliveryScore() {
		return secondLastFeedbackDeliveryScore;
	}

	public void setSecondLastFeedbackDeliveryScore(Integer secondLastFeedbackDeliveryScore) {
		this.secondLastFeedbackDeliveryScore = secondLastFeedbackDeliveryScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_AMBIENCE_SCORE")
	public Integer getThirdLastFeedbackAmbienceScore() {
		return thirdLastFeedbackAmbienceScore;
	}

	public void setThirdLastFeedbackAmbienceScore(Integer thirdLastFeedbackAmbienceScore) {
		this.thirdLastFeedbackAmbienceScore = thirdLastFeedbackAmbienceScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_FOOD_SCORE")
	public Integer getThirdLastFeedbackFoodScore() {
		return thirdLastFeedbackFoodScore;
	}

	public void setThirdLastFeedbackFoodScore(Integer thirdLastFeedbackFoodScore) {
		this.thirdLastFeedbackFoodScore = thirdLastFeedbackFoodScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_BEVERAGE_SCORE")
	public Integer getThirdLastFeedbackBeverageScore() {
		return thirdLastFeedbackBeverageScore;
	}

	public void setThirdLastFeedbackBeverageScore(Integer thirdLastFeedbackBeverageScore) {
		this.thirdLastFeedbackBeverageScore = thirdLastFeedbackBeverageScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_SERVICE_SCORE")
	public Integer getThirdLastFeedbackServiceScore() {
		return thirdLastFeedbackServiceScore;
	}

	public void setThirdLastFeedbackServiceScore(Integer thirdLastFeedbackServiceScore) {
		this.thirdLastFeedbackServiceScore = thirdLastFeedbackServiceScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_DELIVERY_SCORE")
	public Integer getThirdLastFeedbackDeliveryScore() {
		return thirdLastFeedbackDeliveryScore;
	}

	public void setThirdLastFeedbackDeliveryScore(Integer thirdLastFeedbackDeliveryScore) {
		this.thirdLastFeedbackDeliveryScore = thirdLastFeedbackDeliveryScore;
	}

	@Column(name = "TOTAL_NPS")
	public Integer getTotalNps() {
		return totalNps;
	}

	public void setTotalNps(Integer totalNps) {
		this.totalNps = totalNps;
	}

	@Column(name = "TOTAL_CAFE_NPS")
	public Integer getTotalCafeNps() {
		return totalCafeNps;
	}

	public void setTotalCafeNps(Integer totalCafeNps) {
		this.totalCafeNps = totalCafeNps;
	}

	@Column(name = "TOTAL_DELIVERY_NPS")
	public Integer getTotalDeliveryNps() {
		return totalDeliveryNps;
	}

	public void setTotalDeliveryNps(Integer totalDeliveryNps) {
		this.totalDeliveryNps = totalDeliveryNps;
	}

	@Column(name = "AVG_NPS")
	public BigDecimal getAvgNps() {
		return avgNps;
	}

	public void setAvgNps(BigDecimal avgNps) {
		this.avgNps = avgNps;
	}

	@Column(name = "AVG_CAFE_NPS")
	public BigDecimal getAvgCafeNps() {
		return avgCafeNps;
	}

	public void setAvgCafeNps(BigDecimal avgCafeNps) {
		this.avgCafeNps = avgCafeNps;
	}

	@Column(name = "AVG_DELIVERY_NPS")
	public BigDecimal getAvgDeliveryNps() {
		return avgDeliveryNps;
	}

	public void setAvgDeliveryNps(BigDecimal avgDeliveryNps) {
		this.avgDeliveryNps = avgDeliveryNps;
	}

	@Column(name = "TOTAL_NPS_IN_LAST_180_DAYS")
	public Integer getTotalNpsInLast180Days() {
		return totalNpsInLast180Days;
	}

	public void setTotalNpsInLast180Days(Integer totalNpsInLast180Days) {
		this.totalNpsInLast180Days = totalNpsInLast180Days;
	}

	@Column(name = "TOTAL_CAFE_NPS_IN_LAST_180_DAYS")
	public Integer getTotalCafeNpsInLast180Days() {
		return totalCafeNpsInLast180Days;
	}

	public void setTotalCafeNpsInLast180Days(Integer totalCafeNpsInLast180Days) {
		this.totalCafeNpsInLast180Days = totalCafeNpsInLast180Days;
	}

	@Column(name = "TOTAL_DELIVERY_NPS_IN_LAST_180_DAYS")
	public Integer getTotalDeliveryNpsInLast180Days() {
		return totalDeliveryNpsInLast180Days;
	}

	public void setTotalDeliveryNpsInLast180Days(Integer totalDeliveryNpsInLast180Days) {
		this.totalDeliveryNpsInLast180Days = totalDeliveryNpsInLast180Days;
	}

	@Column(name = "AVG_NPS_IN_LAST_180_DAYS")
	public BigDecimal getAvgNpsInLast180Days() {
		return avgNpsInLast180Days;
	}

	public void setAvgNpsInLast180Days(BigDecimal avgNpsInLast180Days) {
		this.avgNpsInLast180Days = avgNpsInLast180Days;
	}

	@Column(name = "AVG_CAFE_NPS_IN_LAST_180_DAYS")
	public BigDecimal getAvgCafeNpsInLast180Days() {
		return avgCafeNpsInLast180Days;
	}

	public void setAvgCafeNpsInLast180Days(BigDecimal avgCafeNpsInLast180Days) {
		this.avgCafeNpsInLast180Days = avgCafeNpsInLast180Days;
	}

	@Column(name = "AVG_DELIVERY_NPS_IN_LAST_180_DAYS")
	public BigDecimal getAvgDeliveryNpsInLast180Days() {
		return avgDeliveryNpsInLast180Days;
	}

	public void setAvgDeliveryNpsInLast180Days(BigDecimal avgDeliveryNpsInLast180Days) {
		this.avgDeliveryNpsInLast180Days = avgDeliveryNpsInLast180Days;
	}

}
