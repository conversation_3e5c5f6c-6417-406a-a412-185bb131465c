package com.stpl.tech.kettle.partner.reporting.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.partner.reporting.dao.PartnerReportDao;
import com.stpl.tech.util.AppUtils;

@Repository
public class PartnerReportDaoImpl extends AbstractDaoImpl implements PartnerReportDao {
	
	private static final Logger LOG = LoggerFactory.getLogger(PartnerReportDaoImpl.class);
	
	@Override
	public List<OrderDetail> getOrderDetailsOfUnit(int unitId, Date businessDate) {
		Query query = manager.createQuery(
				"FROM OrderDetail E where E.unitId = :unitId and E.billingServerTime >= :startTime and E.billingServerTime <= :endTime and E.orderStatus <> :status  and E.settledAmount <> 0 ");
		query.setParameter("unitId", unitId);
		query.setParameter("startTime", AppUtils.getStartOfBusinessDay(businessDate));
		query.setParameter("endTime", AppUtils.getEndOfBusinessDay(businessDate));
		query.setParameter("status", OrderStatus.CANCELLED.name());
		
		return query.getResultList();
	}
	
	@Override
	public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate) {
		Query query = manager.createQuery(
				"FROM UnitClosureDetails E where E.businessDate = :businessDate and currentStatus <> :currentStatus");
		query.setParameter("businessDate", businessDate);
		query.setParameter("currentStatus", ClosureState.CANCELLED.name());
		@SuppressWarnings("unchecked")
		List<UnitClosureDetails> unitClosureDetails = query.getResultList();
		return unitClosureDetails;
	}
	
	

}
