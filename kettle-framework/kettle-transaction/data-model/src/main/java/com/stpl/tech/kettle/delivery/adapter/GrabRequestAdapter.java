/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.GrabCancellationRequest;
import com.stpl.tech.kettle.delivery.model.GrabOrderRequest;
import com.stpl.tech.kettle.delivery.model.GrabRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class GrabRequestAdapter implements RequestAdapter<GrabRequest, OrderInfo> {

	private enum GrabDeliveryType {
		ONLINE_ASYNC,COM,CC,COD
	}

	private static final Logger LOG = LoggerFactory.getLogger(GrabRequestAdapter.class);

	private static final String PREP_TIME= "20";
	private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
	private static final String CLIENT_ID="62";

	@Override
	public GrabRequest adaptCreate(OrderInfo data) {
		LOG.info("Inside adapt grab order method");
		GrabOrderRequest grabRequest = new GrabOrderRequest();

		Order order = data.getOrder();
		Customer customer = data.getCustomer();
		grabRequest.setClientId(CLIENT_ID);
		grabRequest.setMerchantId(String.valueOf(data.getUnit().getId()));
		grabRequest.setClientOrderId(order.getGenerateOrderId());
		grabRequest.setPrepTime(PREP_TIME);
		grabRequest.setDttm(AppUtils.getFormattedTime(order.getBillingServerTime(), DATE_FORMAT));
		grabRequest.setCustomerPhone(customer.getContactNumber());
		grabRequest.setCustomerName(customer.getFirstName());
		Address customerAddress = TransactionUtils.getAddressForOrder(order.getDeliveryAddress(),
				customer.getAddresses());
		if (customerAddress != null) {
			grabRequest.setCustomerAddressLine1(customerAddress.getLine1());
			grabRequest.setCustomerAddressLine2(customerAddress.getLine2());
		}
		grabRequest.setBillAmount(order.getTransactionDetail().getPaidAmount().toString());
		grabRequest.setBillNo(order.getGenerateOrderId());

		GrabDeliveryType deliveryType = getDeliveryType(order.getSettlements());
		grabRequest.setOrderType(deliveryType.toString());

		if(deliveryType.equals(GrabDeliveryType.COD)){
			grabRequest.setAmountCollectedInCash(order.getTransactionDetail().getPaidAmount().floatValue());
			grabRequest.setAmountPaidByClient(order.getTransactionDetail().getPaidAmount().floatValue());
		}else{
			grabRequest.setAmountCollectedInCash(0.0f);
			grabRequest.setAmountPaidByClient(0.0f);
		}
		grabRequest.setComments("");
		grabRequest.setCustomerAddressLandMark("NA");
		return grabRequest;
	}

	private GrabDeliveryType getDeliveryType(List<Settlement> settlements) {
		LOG.info("Inside adapt grab order type method");
		GrabDeliveryType deliveryType = GrabDeliveryType.ONLINE_ASYNC;
		if (settlements.size() == 1) {
			deliveryType = settlements.get(0).getMode() == 1
					? GrabDeliveryType.COD : GrabDeliveryType.ONLINE_ASYNC;
		}
		return deliveryType;
	}

	@Override
	public GrabRequest adaptCancel(OrderInfo orderInfo, String taskId) {
		LOG.info("Inside adapt grab cancellation method");
		GrabCancellationRequest cancellationRequest = new GrabCancellationRequest();
		cancellationRequest.setDttm(AppUtils.getFormattedTime(orderInfo.getOrder().getBillingServerTime(),DATE_FORMAT));
		cancellationRequest.setClientId(CLIENT_ID);
		cancellationRequest.setOrderId(taskId);
		cancellationRequest.setClientOrderId(orderInfo.getOrder().getGenerateOrderId());
		cancellationRequest.setComment("Cancellation Request");
		return cancellationRequest;
	}
}
