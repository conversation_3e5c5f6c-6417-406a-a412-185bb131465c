package com.stpl.tech.kettle.customer.service;

import com.stpl.tech.kettle.core.data.vo.FeedbackCount;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerUpdateResponse;
import com.stpl.tech.kettle.data.model.ProfileUpdateResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLogList;
import com.stpl.tech.kettle.domain.model.CustomerInfoDineIn;
import com.stpl.tech.kettle.domain.model.CustomerLoyaltyEntryList;
import com.stpl.tech.kettle.domain.model.CustomerWalletEntryList;
import com.stpl.tech.kettle.domain.model.EmailResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;

import java.util.Date;
import java.util.List;

public interface CustomerInfoService {

    public CustomerInfoDineIn getCustomerInfoForDineIn(int customerId);

    Customer getCustomerInfo(int customerId) throws DataNotFoundException;

    public CustomerInfo getCustomerInfoById(int customerId) throws DataNotFoundException;

    Boolean updateCustomerInfo(int customerId, String name, String acquisitionSource, String acquisitionToken)throws DataUpdationException;

    String updateCustomerBasicDetail(int customerId, CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException;

    CustomerWalletEntryList getWalletLedger(int customerId, int pageNo, int pageSize);

    CustomerLoyaltyEntryList getLoyaltyLedger(int customerId, int pageNo, int pageSize);

    CustomerCashPacketLogList getCashLedger(int customerId, int pageNo, int pageSize);

    CustomerCashPacketLogList getCashExpLedger(int customerId, Date date);

    EmailResponse verifyCustomerEmailById(int customerId, String email) throws DataNotFoundException, DataUpdationException;

    public boolean sendVerificationEmail(String email, String oldEmail, String contact, String customerName,Integer brandId);

    EmailResponse verifyCustomerEmail(int customerId,int brandId) throws DataNotFoundException, DataUpdationException;
    
	boolean addCustomerProductFeedback(int customerId, int productId, int rating, Integer sourceId, String source);

	boolean removeCustomerProductFeedback(int customerId, int productId, String source);

	List<FeedbackCount> getProductSpecificFeedbackCount();

	List<FeedbackCount> getCustomerSpecificFeedbackCount(int customerId, String source);

	String deleteCustomerData(Integer customerId) throws DataNotFoundException;
    public ProfileUpdateResponse updateCustomerBasicDetailAndGenerateCoupon(int customerId, CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException;

    public ProfileUpdateResponse generateCouponForProfileCompletion(ProfileUpdateResponse profileUpdateResponse,String contactNumber,
                                                                    Integer customerId);
}
