/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.delivery.model.CallbackObject;
import com.stpl.tech.kettle.delivery.model.DeliveryPartnerErrorResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryPartnerResponse;

public interface ResponseAdapter<T extends DeliveryPartnerResponse, Q extends DeliveryPartnerErrorResponse, C extends CallbackObject, R> {
	public R adapt(T data, String orderId);

	public R adaptError(Q data, String orderId);

	public R adaptCallback(C data);
	
	public R adaptCancel(String orderId, String taskId);

}
