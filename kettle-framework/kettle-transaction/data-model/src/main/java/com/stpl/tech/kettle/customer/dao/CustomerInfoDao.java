package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackCount;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLog;
import com.stpl.tech.kettle.domain.model.CustomerInfoDineIn;
import com.stpl.tech.kettle.domain.model.CustomerLoyaltyEntry;
import com.stpl.tech.kettle.domain.model.CustomerWalletEntry;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface CustomerInfoDao extends AbstractDao {

    public CustomerInfoDineIn getCustomerInfoForDineIn(int customerId);

    CustomerCardInfo getCashCardAmount(int customerId);

    CustomerInfo getCustomerInfoById(int customerId) throws DataNotFoundException;

    Customer getCustomerById(int customerId) throws DataNotFoundException;

    List<CustomerWalletEntry> getWalletPurchaseEntry(int customerId);

    List<CustomerWalletEntry> getWalletPaidEntry(int customerId);

    List<CustomerLoyaltyEntry> getLoyaltyLedger(int customerId);

    List<CustomerLoyaltyEntry> getLoyaltyGiftingLedger(int customerId);

   // List<CashPacketLogData> getCashLedger(int customerId);

    List<CustomerCashPacketLog> getCashExpLedger(int customerId, Date expDate);

    List<CustomerCashPacketLog> getLatestCashExpLedger(int customerId);

    Boolean updateCustomerInfo(int customerId, String name, String acquisitionSource, String acquisitionToken) throws DataUpdationException;

    Boolean updateCustomerBasicDetail(int customerId, CustomerInfo customerInfo,CustomerInfo oldCustomer) throws DataUpdationException;

    Boolean updateCustomerEmail(int customerId, String email) throws DataUpdationException;
    
	boolean addCustomerProductFeedback(int customerId, int productId, int rating, Integer sourceId, String source);

	boolean removeCustomerProductFeedback(int customerId, int productId, String source);

	List<FeedbackCount> getProductSpecificFeedbackCount();

    List<CustomerCashPacketLog> getCashReferalLedger(int customerId);

	List<FeedbackCount> getCustomerSpecificFeedbackCount(int customerId, String source);

    Boolean updateCustomerAddress(int customerId, CustomerAddressInfo customerAddressInfo) throws DataUpdationException;

    List<String> getOldMappedContactNumbersList();

    CustomerContactInfoMapping getCustomerContactInfoMapping(String contactNumber);

    List<CustomerAddressInfo> getCustomerAddressInfoById(Integer customerId);

    List<Object[]> getCustomerInfoByContactList(List<String> contactNumbers);

    void updateCustomerOptIn(List<String> customerContacts);

    boolean getCustomerOrderDetails(int customerId);
    List<CustomerInfo> getCustomersWithDOBorAnniversary();
    public Integer getKettleCustomerId(String partnerCustomerId);


}
