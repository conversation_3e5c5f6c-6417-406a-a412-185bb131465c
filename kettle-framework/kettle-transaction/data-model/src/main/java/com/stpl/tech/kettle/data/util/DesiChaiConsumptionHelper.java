/**
 *
 */
package com.stpl.tech.kettle.data.util;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumableCodeColors;
import com.stpl.tech.master.domain.model.ConsumptionCodeData;
import com.stpl.tech.master.domain.model.ConsumptionMetadata;
import com.stpl.tech.master.domain.model.ExtendedConsumable;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public abstract class DesiChaiConsumptionHelper {

	private static final Logger LOG = LoggerFactory.getLogger(DesiChaiConsumptionHelper.class);

	private static final Map<String, DesiChaiConsumptionHelper> INSTANCES = new HashMap<>();

	public static final int HOT_CUP_SCM_PRODUCT_ID = 100184;

	private static final String PROFILE_INSTANCE_CREATOR = "PROFILE_INSTANCE_CREATOR";

	private static final String PROFILE_INSTANCE_CREATOR_DOUBLE_CHECK = "PROFILE_INSTANCE_CREATOR_DOUBLE_CHECK";

	private static final String REGULAR_MILK = "Regular";
	private static final String DASH_MILK = "Dash";
	private static final String DOUBLE_DASH_MILK = "Double Dash";
	private static final String FULL_DOODH_MILK = "Full Doodh";
	private static final String DESI_DOODH_KAM_MILK = "Doodh Kum";
	private static final String DESI_PAANI_KAM_MILK = "Paani Kum";
	/**
	 * 
	 */
	protected static final int KULHAD_CHAI = 80;
	/**
	 * 
	 */
	protected static final int DESI_PAANI_KAM = 50;
	/**
	 * 
	 */
	protected static final int CUTTING_CHAI = 30;
	/**
	 * 
	 */
	protected static final int DESI_DOODH_KAM = 12;
	/**
	 * 
	 */
	protected static final int FULL_DOODH = 11;
	/**
	 * 
	 */
	public static final int DESI_CHAI = 10;
	/**
	 * 
	 */
	protected static final int DESI_CHAI_DASH_MILK = 14;
	/**
	 * 
	 */
	protected static final int DESI_CHAI_DOUBLE_DASH_MILK = 15;
	/**
	 * 
	 */
	protected static final int LEMON_GRASS_GINGER_FULL_DOODH = 1292;
	/**
	 * 
	 */
	protected static final int LEMON_GRASS_GINGER_PAANI_KUM = 1293;
	/**
	 * 
	 */
	protected static final int LEMON_GRASS_GINGER_DOODH_KUM = 1294;
	protected static final Integer PLACEHOLDER_LEMON_GRASS_GINGER = 1376;
	/**
	 * 
	 */
	protected static final int LEMON_GRASS_GINGER = 1282;
	protected static final Integer  PLACEHOLDER_DESI_CHAI = 1375;

	/**
	 * 
	 */
	public static final String PATTI = "Patti";
	/**
	 * 
	 */
	public static final String SUGAR = "Sugar";
	/**
	 * 
	 */
	public static final String REGULAR_SUGAR = "Regular Sugar";
	/**
	 * 
	 */
	public static final String REGULAR_PATTI = "Regular Patti";
	/**
	 * 
	 */
	public static final String KADAK = "Kadak";
	/**
	 * 
	 */
	protected static final String KADAK_NO_SUGAR = "Kadak - No Sugar";
	/**
	 * 
	 */
	protected static final String KADAK_REGULAR_SUGAR = "Kadak - Regular Sugar";
	/**
	 * 
	 */
	protected static final String REGULAR_PATTI_NO_SUGAR = "Regular Patti - No Sugar";
	/**
	 * 
	 */
	protected static final String REGULAR_PATTI_REGULAR_SUGAR = "Regular Patti - Regular Sugar";
	/**
	 * 
	 */
	public static final String NONE = "None";
	/**
	 * 
	 */
	public static final String BADI_KETLI = "BadiKetli";
	/**
	 * 
	 */
	public static final String CHOTI_KETLI = "ChotiKetli";
	/**
	 * 
	 */
	public static final String MINI_KETLI = "MiniKetli";

	/**
	 * 
	 */
	public static final String FULL = "Full";
	/**
	 * 
	 */
	public static final String REGULAR = "Regular";
	public static final String REGULAR_LOWER_CASE = "regular";

	public static final String KADAK_LOWER_CASE = "kadak";

	public static final String NO_SUGAR = "No Sugar";
	public static final String SUGAR_LOWER_CASE = "sugar";
	public static final String TEA_FOR_2 = "TF2";
	public static final String TEA_FOR_4 = "TF4";

	public static final Integer SCM_PRODUCT_MILK = 100234;

	protected static final Set<Integer> products;
	protected static final Map<Integer, Integer> productsMap;
	protected static final Map<Integer, Integer> placeHolderProducts;
	protected static final Map<Integer, Map<String, Integer>> productsLookupMap;
	@Getter
	protected static final Map<Integer, String> consumableCodeColorMap;
	protected static Map<Integer, ExtendedConsumable> scmProductsMap;
	@Getter
	protected static Map<String, ConsumptionMetadata> gurConsMetadataMap;
	@Getter
	protected static Map<String, ConsumptionMetadata> honeyConsMetadataMap;
	@Getter
	protected static Map<String, ConsumptionMetadata> sugarFreeConsMetadataMap;
	@Getter
	protected static final List<String> milkVariantsList = Arrays.asList("", "O_");

	static {
		products = new HashSet<>();
		products.add(DESI_CHAI);
		products.add(DESI_CHAI_DASH_MILK);
		products.add(DESI_CHAI_DOUBLE_DASH_MILK);
		products.add(FULL_DOODH);
		products.add(DESI_DOODH_KAM);
		products.add(CUTTING_CHAI);
		products.add(DESI_PAANI_KAM);
		products.add(KULHAD_CHAI);
		products.add(LEMON_GRASS_GINGER);
		products.add(LEMON_GRASS_GINGER_DOODH_KUM);
		products.add(LEMON_GRASS_GINGER_FULL_DOODH);
		products.add(LEMON_GRASS_GINGER_PAANI_KUM);
		productsMap = new HashMap<>();
		productsMap.put(DESI_CHAI, DESI_CHAI);
		productsMap.put(DESI_CHAI_DASH_MILK, DESI_CHAI);
		productsMap.put(DESI_CHAI_DOUBLE_DASH_MILK, DESI_CHAI);
		productsMap.put(FULL_DOODH, DESI_CHAI);
		productsMap.put(DESI_DOODH_KAM, DESI_CHAI);
		productsMap.put(DESI_PAANI_KAM, DESI_CHAI);
		productsMap.put(LEMON_GRASS_GINGER, LEMON_GRASS_GINGER);
		productsMap.put(LEMON_GRASS_GINGER_DOODH_KUM, LEMON_GRASS_GINGER);
		productsMap.put(LEMON_GRASS_GINGER_FULL_DOODH, LEMON_GRASS_GINGER);
		productsMap.put(LEMON_GRASS_GINGER_PAANI_KUM, LEMON_GRASS_GINGER);
		
		placeHolderProducts = new HashMap<Integer, Integer>();
		placeHolderProducts.put(PLACEHOLDER_DESI_CHAI, SCM_PRODUCT_MILK);
		placeHolderProducts.put(PLACEHOLDER_LEMON_GRASS_GINGER, SCM_PRODUCT_MILK);
		productsLookupMap = new HashMap<>();
		productsLookupMap.put(PLACEHOLDER_DESI_CHAI, new HashMap<>());
		productsLookupMap.put(PLACEHOLDER_LEMON_GRASS_GINGER, new HashMap<>());

		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(REGULAR_MILK, DESI_CHAI);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DASH_MILK, DESI_CHAI_DASH_MILK);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DOUBLE_DASH_MILK, DESI_CHAI_DOUBLE_DASH_MILK);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DESI_DOODH_KAM_MILK, DESI_DOODH_KAM);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DESI_PAANI_KAM_MILK, DESI_PAANI_KAM);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(FULL_DOODH_MILK, FULL_DOODH);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(REGULAR_MILK, LEMON_GRASS_GINGER);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(DESI_DOODH_KAM_MILK, LEMON_GRASS_GINGER_DOODH_KUM);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(DESI_PAANI_KAM_MILK, LEMON_GRASS_GINGER_PAANI_KUM);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(FULL_DOODH_MILK, LEMON_GRASS_GINGER_FULL_DOODH);

		consumableCodeColorMap = new HashMap<>();
		consumableCodeColorMap.put(101715, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(101716, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(101717, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(101718, ConsumableCodeColors.YELLOW.getName());
		consumableCodeColorMap.put(101719, ConsumableCodeColors.GREEN.getName());
		consumableCodeColorMap.put(101720, ConsumableCodeColors.GREEN.getName());
		consumableCodeColorMap.put(101721, ConsumableCodeColors.GREEN.getName());
		consumableCodeColorMap.put(101722, ConsumableCodeColors.BLUE.getName());
		consumableCodeColorMap.put(101723, ConsumableCodeColors.BLUE.getName());
		consumableCodeColorMap.put(101724, ConsumableCodeColors.BLUE.getName());
		consumableCodeColorMap.put(101725, ConsumableCodeColors.PINK.getName());
		consumableCodeColorMap.put(101726, ConsumableCodeColors.PINK.getName());
		consumableCodeColorMap.put(101727, ConsumableCodeColors.PINK.getName());
		consumableCodeColorMap.put(101728, ConsumableCodeColors.PINK.getName());
		consumableCodeColorMap.put(103467, ConsumableCodeColors.GREEN.getName());
		consumableCodeColorMap.put(103468, ConsumableCodeColors.GREEN.getName());
		consumableCodeColorMap.put(102597, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(102599, ConsumableCodeColors.YELLOW.getName());
		consumableCodeColorMap.put(102551, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(102550, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(100699, ConsumableCodeColors.YELLOW.getName());
		consumableCodeColorMap.put(100834, ConsumableCodeColors.GREEN.getName());
		consumableCodeColorMap.put(100895, ConsumableCodeColors.BLUE.getName());
		consumableCodeColorMap.put(100298, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(103483, ConsumableCodeColors.ORANGE.getName());
		consumableCodeColorMap.put(100333, ConsumableCodeColors.WHITE.getName());
		consumableCodeColorMap.put(100123, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100171, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100144, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100120, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100166, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100237, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100194, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100332, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100330, ConsumableCodeColors.NO_COLOR.getName());
		consumableCodeColorMap.put(100331, ConsumableCodeColors.YELLOW.getName());
		consumableCodeColorMap.put(102792, ConsumableCodeColors.BROWN.getName());
		createGurConsumptionMetadata();
		createHoneyConsumptionMetadata();
		createSugarFreeConsumptionMetadata();
	}

	private static void createSugarFreeConsumptionMetadata() {
		sugarFreeConsMetadataMap = new HashMap<>();
		sugarFreeConsMetadataMap.put(REGULAR, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(CHOTI_KETLI, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(BADI_KETLI, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(MINI_KETLI, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(TEA_FOR_2, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(TEA_FOR_4, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(FULL, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.put(NONE, new ConsumptionMetadata());
		sugarFreeConsMetadataMap.get(REGULAR).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(1), getDimensionChar(REGULAR, 100331)));
		sugarFreeConsMetadataMap.get(CHOTI_KETLI).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(3), getDimensionChar(CHOTI_KETLI, 100331)));
		sugarFreeConsMetadataMap.get(BADI_KETLI).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(6), getDimensionChar(BADI_KETLI, 100331)));
		sugarFreeConsMetadataMap.get(MINI_KETLI).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(2), getDimensionChar(MINI_KETLI, 100331)));
		sugarFreeConsMetadataMap.get(TEA_FOR_2).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(2), getDimensionChar(TEA_FOR_2, 100331)));
		sugarFreeConsMetadataMap.get(TEA_FOR_4).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(3), getDimensionChar(TEA_FOR_4, 100331)));
		sugarFreeConsMetadataMap.get(FULL).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(6), getDimensionChar(FULL, 100331)));
		sugarFreeConsMetadataMap.get(NONE).getSugarFree().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.YELLOW.getName(),
				AppConstants.SUGAR_FREE, 100331, new BigDecimal(2), getDimensionChar(NONE, 100331)));
	}

	private static void createGurConsumptionMetadata() {
		//Product id values, qty are hardcoded for now
		gurConsMetadataMap = new HashMap<>();
		gurConsMetadataMap.put(REGULAR, new ConsumptionMetadata());
		gurConsMetadataMap.put(CHOTI_KETLI, new ConsumptionMetadata());
		gurConsMetadataMap.put(BADI_KETLI, new ConsumptionMetadata());
		gurConsMetadataMap.put(MINI_KETLI, new ConsumptionMetadata());
		gurConsMetadataMap.put(TEA_FOR_2, new ConsumptionMetadata());
		gurConsMetadataMap.put(TEA_FOR_4, new ConsumptionMetadata());
		gurConsMetadataMap.put(FULL, new ConsumptionMetadata());
		gurConsMetadataMap.put(NONE, new ConsumptionMetadata());
		gurConsMetadataMap.get(REGULAR).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000204, new BigDecimal(3), getDimensionChar(REGULAR, 1000204)));
		gurConsMetadataMap.get(CHOTI_KETLI).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000206, new BigDecimal(6), getDimensionChar(CHOTI_KETLI, 1000206)));
		gurConsMetadataMap.get(BADI_KETLI).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000207, new BigDecimal(15), getDimensionChar(BADI_KETLI, 1000207)));
		gurConsMetadataMap.get(MINI_KETLI).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000205, new BigDecimal(4), getDimensionChar(MINI_KETLI, 1000205)));
		gurConsMetadataMap.get(TEA_FOR_2).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000205, new BigDecimal(4), getDimensionChar(TEA_FOR_2, 1000205)));
		gurConsMetadataMap.get(TEA_FOR_4).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000205, new BigDecimal(6), getDimensionChar(TEA_FOR_4, 1000205)));
		gurConsMetadataMap.get(FULL).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000204, new BigDecimal(6), getDimensionChar(FULL, 1000204)));
		gurConsMetadataMap.get(NONE).getGur().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.BROWN_GUR.getName(),
				AppConstants.GUR, 1000204, new BigDecimal(3), getDimensionChar(NONE, 1000204)));
	}

	private static void createHoneyConsumptionMetadata() {
		//Product id values, qty are hardcoded for now
		honeyConsMetadataMap = new HashMap<>();
		honeyConsMetadataMap.put(REGULAR, new ConsumptionMetadata());
		honeyConsMetadataMap.put(CHOTI_KETLI, new ConsumptionMetadata());
		honeyConsMetadataMap.put(BADI_KETLI, new ConsumptionMetadata());
		honeyConsMetadataMap.put(MINI_KETLI, new ConsumptionMetadata());
		honeyConsMetadataMap.put(TEA_FOR_2, new ConsumptionMetadata());
		honeyConsMetadataMap.put(TEA_FOR_4, new ConsumptionMetadata());
		honeyConsMetadataMap.put(FULL, new ConsumptionMetadata());
		honeyConsMetadataMap.put(NONE, new ConsumptionMetadata());
		honeyConsMetadataMap.get(REGULAR).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000208, new BigDecimal(1), getDimensionChar(REGULAR, 1000208)));
		honeyConsMetadataMap.get(CHOTI_KETLI).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000210, new BigDecimal(2), getDimensionChar(CHOTI_KETLI, 1000210)));
		honeyConsMetadataMap.get(BADI_KETLI).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000211, new BigDecimal(5), getDimensionChar(BADI_KETLI, 1000211)));
		honeyConsMetadataMap.get(MINI_KETLI).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000209, new BigDecimal(1), getDimensionChar(MINI_KETLI, 1000209)));
		honeyConsMetadataMap.get(TEA_FOR_2).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000209, new BigDecimal(1), getDimensionChar(TEA_FOR_2, 1000209)));
		honeyConsMetadataMap.get(TEA_FOR_4).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000209, new BigDecimal(2), getDimensionChar(TEA_FOR_4, 1000209)));
		honeyConsMetadataMap.get(FULL).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000208, new BigDecimal(2), getDimensionChar(FULL, 1000208)));
		honeyConsMetadataMap.get(NONE).getHoney().put(REGULAR, new ConsumptionCodeData(ConsumableCodeColors.HONEY.getName(),
				AppConstants.HONEY, 1000208, new BigDecimal(1), getDimensionChar(NONE, 1000208)));
	}

	/**
	 * @return
	 */
	public static DesiChaiConsumptionHelper getInstance(String profile) {
		if (!INSTANCES.containsKey(profile)) {
			synchronized (PROFILE_INSTANCE_CREATOR) {
				if (!INSTANCES.containsKey(profile)) {
					synchronized (PROFILE_INSTANCE_CREATOR_DOUBLE_CHECK) {

						switch (profile) {
							case AppConstants.RECIPE_PROFILE_P0:
								INSTANCES.put(profile, new DesiChaiConsumptionHelperP0());
								break;
							case AppConstants.RECIPE_PROFILE_P1:
							case AppConstants.RECIPE_PROFILE_P2:
							case AppConstants.RECIPE_PROFILE_PP2:
								INSTANCES.put(profile, new DesiChaiConsumptionHelperP2());
								break;
							case AppConstants.RECIPE_PROFILE_P3:
							case AppConstants.RECIPE_PROFILE_PP3:
								INSTANCES.put(profile, new DesiChaiConsumptionHelperP3());
								break;
							case AppConstants.RECIPE_PROFILE_CC1:
							case AppConstants.RECIPE_PROFILE_PC1:
								INSTANCES.put(profile, new DesiChaiConsumptionHelperCC1());
								break;
							case AppConstants.RECIPE_PROFILE_DC0:
								INSTANCES.put(profile, new DesiChaiConsumptionHelperP0());
								break;
							case AppConstants.RECIPE_PROFILE_OP0:
								INSTANCES.put(profile, new OatMilkConsumptionHelperP0());
								break;
							case AppConstants.RECIPE_PROFILE_OP2:
								INSTANCES.put(profile, new OatMilkConsumptionHelperP2());
								break;
							case AppConstants.RECIPE_PROFILE_OP3:
								INSTANCES.put(profile, new OatMilkConsumptionHelperP3());
								break;
							case AppConstants.RECIPE_PROFILE_OCC1:
								INSTANCES.put(profile, new OatMilkConsumptionHelperCC1());
								break;
							default:
								return INSTANCES.get(AppConstants.DEFAULT_RECIPE_PROFILE);
						}
					}
				}
			}
		}
		return INSTANCES.get(profile);
	}

	/**
	 * @return
	 */
	public Set<Integer> getProducts() {
		return products;
	}

	public static Integer getMappedProduct(int productId) {
		return productsMap.containsKey(productId) ? productsMap.get(productId) : null;
	}

	public static boolean isPlaceholderProduct(int productId) {
		return placeHolderProducts.containsKey(productId);
	}

	public static Integer placeholderIdentifier(int productId) {
		return placeHolderProducts.get(productId) ;
	}
	
	public static Integer getActualProduct(int productId, String identifier) {
		return productsLookupMap.containsKey(productId) ? (productsLookupMap.get(productId).containsKey(identifier)
				? productsLookupMap.get(productId).get(identifier)
				: productsLookupMap.get(productId).get(REGULAR_MILK)) : productId;
	}

	/**
	 * @param item
	 * @param variants
	 * @return
	 */
	public List<Consumable> getConsumption(OrderItem item, List<IngredientVariantDetail> variants) {
		Map<Integer, ExtendedConsumable> scmProducts = scmProducts();
		Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables = consumables();
		List<Consumable> list = new ArrayList<>();
		int productId = item.getProductId();
		String dimension = item.getDimension();
		String sugar = REGULAR_SUGAR;
		String patti = REGULAR_PATTI;
		for (IngredientVariantDetail variant : variants) {
			if (variant.getAlias() != null && variant.getAlias().contains(SUGAR)) {
				sugar = variant.getAlias();
			}
			if (variant.getAlias() != null
					&& (variant.getAlias().contains(PATTI) || variant.getAlias().contains(KADAK))) {
				patti = variant.getAlias();
			}
		}
		String variation = patti + " - " + sugar;
		List<Pair<Integer, BigDecimal>> consumptions = consumables.get(productId) != null
				&& consumables.get(productId).get(dimension) != null
						? consumables.get(productId).get(dimension).get(variation)
						: null;

		if (consumptions == null) {
			LOG.error(String.format("Error in getting consumption for %s [%s] - %s - %s", item.getProductName(),
					item.getDimension(), patti, sugar));
		} else {
			for (Pair<Integer, BigDecimal> record : consumptions) {
				Consumable c = new Consumable();
				Consumable r = scmProducts.get(record.getKey());
				c.setName(r.getName());
				c.setProductId(r.getProductId());
				c.setUom(r.getUom());
				c.setQuantity(AppUtils.multiplyWithScale10(new BigDecimal(item.getQuantity()), record.getValue()));
				list.add(c);
			}
		}
		return list;
	}

	/**
	 * @param item
	 * @return
	 */
	public boolean hasKulhadAddon(OrderItem item) {
		for (String p : item.getComposition().getOptions()) {
			if (p.equalsIgnoreCase("Additional Kulhad")) {
				return true;
			}
		}
		return false;
	}

	public static Map<Integer, ExtendedConsumable> getScmProducts() {
		if (Objects.isNull(scmProductsMap)) {
			scmProductsMap = new HashMap<>();
			scmProductsMap.putAll(getInstance(AppConstants.RECIPE_PROFILE_CC1).scmProducts());
			getInstance(AppConstants.RECIPE_PROFILE_P2).scmProducts().forEach(scmProductsMap::putIfAbsent);
			getInstance(AppConstants.RECIPE_PROFILE_P3).scmProducts().forEach(scmProductsMap::putIfAbsent);
			getInstance(AppConstants.RECIPE_PROFILE_P0).scmProducts().forEach(scmProductsMap::putIfAbsent);

			getInstance(AppConstants.RECIPE_PROFILE_OCC1).scmProducts().forEach(scmProductsMap::putIfAbsent);
			getInstance(AppConstants.RECIPE_PROFILE_OP2).scmProducts().forEach(scmProductsMap::putIfAbsent);
			getInstance(AppConstants.RECIPE_PROFILE_OP3).scmProducts().forEach(scmProductsMap::putIfAbsent);
			getInstance(AppConstants.RECIPE_PROFILE_OP0).scmProducts().forEach(scmProductsMap::putIfAbsent);
		}
		return scmProductsMap;
	}

	public abstract Map<Integer, ExtendedConsumable> scmProducts();

	public abstract Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables();

	protected ExtendedConsumable getConsumable(int productId, String name, String uom, String type) {
		ExtendedConsumable c = new ExtendedConsumable();
		c.setName(name);
		c.setProductId(productId);
		c.setUom(uom);
		c.setType(type);
		return c;
	}

	public static Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> consumableCodes() {
		Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> output = new HashMap<>();
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapCC1 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_CC1, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapP0 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_P0, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapP2 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_P2, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapP3 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_P3, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapOCC1 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_OCC1, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapOP0 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_OP0, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapOP2 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_OP2, output);
		Map<Integer, Map<String, ConsumptionMetadata>> consumableCodeMapOP3 = createConsumableCodeMap(AppConstants.RECIPE_PROFILE_OP3, output);
		output.put(AppConstants.RECIPE_PROFILE_CC1, consumableCodeMapCC1);
		output.put(AppConstants.RECIPE_PROFILE_PC1, consumableCodeMapCC1);
		output.put(AppConstants.RECIPE_PROFILE_P0, consumableCodeMapP0);
		output.put(AppConstants.RECIPE_PROFILE_DC0, consumableCodeMapP0);
		output.put(AppConstants.RECIPE_PROFILE_P1, consumableCodeMapP2);
		output.put(AppConstants.RECIPE_PROFILE_P2, consumableCodeMapP2);
		output.put(AppConstants.RECIPE_PROFILE_PP2, consumableCodeMapP2);
		output.put(AppConstants.RECIPE_PROFILE_P3, consumableCodeMapP3);
		output.put(AppConstants.RECIPE_PROFILE_PP3, consumableCodeMapP3);

		output.put(AppConstants.RECIPE_PROFILE_OCC1, consumableCodeMapOCC1);
		output.put(AppConstants.RECIPE_PROFILE_OPC1, consumableCodeMapOCC1);
		output.put(AppConstants.RECIPE_PROFILE_OP0, consumableCodeMapOP0);
		output.put(AppConstants.RECIPE_PROFILE_ODC0, consumableCodeMapOP0);
		output.put(AppConstants.RECIPE_PROFILE_OP1, consumableCodeMapOP2);
		output.put(AppConstants.RECIPE_PROFILE_OP2, consumableCodeMapOP2);
		output.put(AppConstants.RECIPE_PROFILE_OPP2, consumableCodeMapOP2);
		output.put(AppConstants.RECIPE_PROFILE_OP3, consumableCodeMapOP3);
		output.put(AppConstants.RECIPE_PROFILE_OPP3, consumableCodeMapOP3);
		return output;
	}

	private static Map<Integer, Map<String, ConsumptionMetadata>> createConsumableCodeMap(String profile, Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> output) {
		DesiChaiConsumptionHelper instance = DesiChaiConsumptionHelper.getInstance(profile);
		Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> map = instance.consumables();
		Map<Integer, ExtendedConsumable> scmProducts = instance.scmProducts();

		if (!output.containsKey(profile)) {
			output.put(profile, new HashMap<>());
		}
		for (Integer productId : map.keySet()) {
			if (!output.get(profile).containsKey(productId)) {
				output.get(profile).put(productId, new HashMap<>());
			}
			Map<String, Map<String, List<Pair<Integer, BigDecimal>>>> dimensionMap = map.get(productId);
			for (String dimension : dimensionMap.keySet()) {
				if (!output.get(profile).get(productId).containsKey(dimension)) {
					output.get(profile).get(productId).put(dimension, new ConsumptionMetadata());
				}
				Map<String, List<Pair<Integer, BigDecimal>>> consumptionMap = dimensionMap.get(dimension);
				for (String combination : consumptionMap.keySet()) {
					ConsumptionMetadata meta = output.get(profile).get(productId).get(dimension);
					List<Pair<Integer, BigDecimal>> consumptions = consumptionMap.get(combination);
					String[] parse = combination.split(" - ");
					if (parse[0].equals(REGULAR_PATTI) && (!meta.getPatti().containsKey(REGULAR))) {
						Pair<Integer, BigDecimal> consumableProductPair = getMappedConsumableProductPair(scmProducts, consumptions, PATTI);
						if (Objects.nonNull(consumableProductPair)) {
							ConsumptionCodeData codeData = new ConsumptionCodeData(consumableCodeColorMap.get(consumableProductPair.getKey()),
									scmProducts.get(consumableProductPair.getKey()).getName(), consumableProductPair.getKey(),
									consumableProductPair.getValue(), getDimensionChar(dimension, productId));
							meta.getPatti().put(REGULAR, codeData);
							logSachetAddData(profile, productId, dimension, REGULAR_PATTI, codeData);
						}
					}
					if (parse[0].equals(KADAK) && (!meta.getPatti().containsKey(PATTI))) {
						Pair<Integer, BigDecimal> consumableProductPair = getMappedConsumableProductPair(scmProducts, consumptions, PATTI);
						if (Objects.nonNull(consumableProductPair)) {
							ConsumptionCodeData codeData = new ConsumptionCodeData(consumableCodeColorMap.get(consumableProductPair.getKey()),
									scmProducts.get(consumableProductPair.getKey()).getName(), consumableProductPair.getKey(),
									consumableProductPair.getValue(), getDimensionChar(dimension, productId));
							meta.getPatti().put(KADAK, codeData);
							logSachetAddData(profile, productId, dimension, KADAK, codeData);
						}
					}
					if (parse[1].equals(REGULAR_SUGAR) && (!meta.getSugar().containsKey(REGULAR))) {
						Pair<Integer, BigDecimal> consumableProductPair = getMappedConsumableProductPair(scmProducts, consumptions, SUGAR);
						if (Objects.nonNull(consumableProductPair)) {
							ConsumptionCodeData codeData = new ConsumptionCodeData(consumableCodeColorMap.get(consumableProductPair.getKey()),
									scmProducts.get(consumableProductPair.getKey()).getName(), consumableProductPair.getKey(),
									consumableProductPair.getValue(), getDimensionChar(dimension, productId));
							meta.getSugar().put(REGULAR, codeData);
							logSachetAddData(profile, productId, dimension, REGULAR_SUGAR, codeData);
						}
					}
				}
				addPaidAddonSachets(output.get(profile).get(productId).get(dimension), dimension);
			}
		}
		return output.get(profile);
	}

	private static void addPaidAddonSachets(ConsumptionMetadata consumptionMetadata, String dimension) {
		consumptionMetadata.setGur(gurConsMetadataMap.get(dimension).getGur());
		consumptionMetadata.setHoney(honeyConsMetadataMap.get(dimension).getHoney());
		consumptionMetadata.setSugarFree(sugarFreeConsMetadataMap.get(dimension).getSugarFree());
	}

	private static Pair<Integer, BigDecimal> getMappedConsumableProductPair(Map<Integer, ExtendedConsumable> scmProducts, List<Pair<Integer, BigDecimal>> consumptions, String type) {
        if(scmProducts.get(consumptions.get(0).getKey()) == null){
			LOG.info("key : {} " , consumptions.get(0).getKey());
			return consumptions.get(1);
		}
		if (type.equals(scmProducts.get(consumptions.get(0).getKey()).getType())) {
			return consumptions.get(0);
		} else if (consumptions.size() > 1) {
			return consumptions.get(1);
		}
		return null;
	}

	private static void logSachetAddData(String profile, Integer productId, String dimension, String type, ConsumptionCodeData codeData) {
		LOG.info(String.format("CONSUMABLE CODE SACHET:: %s, %d, %s :::: %s: %s, %s, %s",
				profile, productId, dimension, type, codeData.getName(), codeData.getColor(), codeData.getQty()));
	}

	public static String getDimensionChar(String dimension, Integer productId) {
		if (CUTTING_CHAI == productId && NONE.equalsIgnoreCase(dimension)) {
			return "N";
		} else if (MINI_KETLI.equalsIgnoreCase(dimension) || TEA_FOR_2.equalsIgnoreCase(dimension)
				|| TEA_FOR_4.equalsIgnoreCase(dimension)) {
			return "MK";
		} else {
			return "R";
		}
	}
}
