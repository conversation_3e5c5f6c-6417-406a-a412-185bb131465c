package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.data.model.CashBackOfferData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface CashBackOfferDao extends JpaRepository<CashBackOfferData, Integer> {
    @Query("SELECT c FROM CashBackOfferData c WHERE c.unitId=:unitId " +
            "AND c.offerStatus=:offerStatus AND c.offerStartDate<=:date AND c.offerEndDate>=:date ORDER BY c.id DESC")
    public CashBackOfferData findAllByOfferScope(@Param("unitId") Integer unitId, @Param("offerStatus") String offerStatus, @Param("date") Date date);

    @Query("SELECT c FROM CashBackOfferData c WHERE c.unitId IN (:unitIds)")
    public List<CashBackOfferData> findAllByUnitIdIn(@Param("unitIds") List<Integer> unitIds);
}
