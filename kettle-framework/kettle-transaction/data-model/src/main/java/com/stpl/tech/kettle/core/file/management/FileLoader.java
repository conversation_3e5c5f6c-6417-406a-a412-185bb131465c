/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.management;

import java.io.IOException;
import java.util.List;

import com.google.api.services.drive.model.File;

public interface FileLoader {

	public List<File> getFiles(String userAccount, String searchText, String mimeType) throws IOException;

	public File getFile(String userAccount, String fileName, String mimeType, String parentDirName) throws IOException;

	public void downloadFile(String userAccount, String fileId, String mimeType, String destinationFileName)
			throws IOException;

    File getFileForDashboardRefresh(String userAccount, String fileName, String mimeType, String parentDirName) throws IOException;

    public void uploadExpenseReport(String userAccount, String filePath, String fileName, String mimeType,
                                    String targetDir, String parentDir) throws IOException;

	/**
	 * @param accountName
	 * @param spreadsheetId
	 * @param range
	 * @param values
	 * @throws IOException
	 */
	void writeRowsSheet(String accountName, String spreadsheetId, String range, List<List<Object>> values)
			throws IOException;
}

