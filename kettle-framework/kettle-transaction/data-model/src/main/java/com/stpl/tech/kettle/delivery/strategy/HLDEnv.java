/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

public enum HLDEnv {

	TEST("https://yaocm9m1dh.execute-api.us-east-1.amazonaws.com/test/"), PRODUCTION(
			"https://p4dj8eemsj.execute-api.us-east-1.amazonaws.com/ODx/");

	private String value;
	private static HLDEnv[] values;

	HLDEnv(String env) {
		this.value = env;
	}

	public String getValue() {
		return this.value;
	}

	public static HLDEnv[] environments() {
		if (values == null) {
			values = HLDEnv.values();
		}

		return values;
	}

}
