/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.ReportStatusDao;
import com.stpl.tech.kettle.data.model.ReportStatusEventData;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;

@Repository
public class ReportStatusDaoImpl extends AbstractDaoImpl implements ReportStatusDao {

	private static final Logger LOG = LoggerFactory.getLogger(ReportStatusDaoImpl.class);

	@Override
	public ReportStatusEvent addReportStatusEvent(ReportStatusEvent event) {
		ReportStatusEventData eventData = DataConverter.convert(event);
		try {

			manager.persist(eventData);
			manager.flush();
			return DataConverter.convert(eventData);
		} catch (Exception e) {
			LOG.error("ERROR while saving Report Status Event", e);
		}
		return null;
	}

	@Override
	public ReportStatusEvent getPeviousReportStatusEvent(int unitId, int terminalId) {
		ReportStatusEventData eventData = null;
		Query query = manager.createQuery(
				"FROM ReportStatusEventData e WHERE e.unitId = :unitId and e.terminalId = :terminalId and e.eventStatus = :eventStatus ORDER BY e.eventId DESC");
		query.setParameter("unitId", unitId);
		query.setParameter("terminalId", terminalId);
		query.setParameter("eventStatus", "CLOSED");
		try {
			@SuppressWarnings("rawtypes")
			List resultSet = query.setMaxResults(1).getResultList();
			eventData = resultSet.isEmpty() ? null : (ReportStatusEventData) resultSet.get(0);
		} catch (NoResultException nre) {
			LOG.error("Previous Report Status Event Not found", nre);
		}

		return eventData == null ? null : DataConverter.convert(eventData);
	}

	@Override
	public boolean updateReportStatusEvent(ReportStatusEvent event) {
		try {
			ReportStatusEventData updateEvent = manager.find(ReportStatusEventData.class, event.getId());
			updateEvent.setEventStatus(event.getEventStatus());
			manager.flush();
			return true;
		} catch (Exception e) {
			LOG.error("ERROR while saving Report Status Event", e);
		}
		return false;
	}

}
