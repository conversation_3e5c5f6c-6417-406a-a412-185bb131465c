/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.core.cache.UnitDeliverySelectionCache;

@Component
public class SelectionStrategyImpl implements SelectionStrategy<UnitDeliverySelectionCache, Integer> {

	@Override
	public Integer getSelectionForTicket(UnitDeliverySelectionCache unitDeliveryMappings) {
		return unitDeliveryMappings.getNextPartnerFromCache();
	}

	@Override
	public boolean refreshDeliveryCache(UnitDeliverySelectionCache unitDeliveryMappings) {
		try{
			unitDeliveryMappings.refreshCurrentCache();
			return true;
		}catch (Exception e){
			return false;
		}
	}
}
