/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;

public interface OfferActionStrategy {

	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems) throws OfferValidationException;

	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException;

	DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
			BigDecimal paidAmount, BigDecimal maxDiscountValue);

}
