package com.stpl.tech.kettle.clevertap.data.model;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Created by Chaayos on 21-08-2021.
 */
@Entity
@Table(name = "CLEVERTAP_PROFILE_PUSH_TRACK")
public class CleverTapProfilePushTrack {

    private Integer id;
    private Integer customerId;
    private String status;
    private String updateType;
    private Date updateAt;
    private Date publishTime;
    private Date processStartTime;
    private Date processEndTime;
    private Long totalProcessSec;
    private Long clevertapResponseTime;

    public CleverTapProfilePushTrack() {

    }


    public CleverTapProfilePushTrack(Integer customerId, String status, String updateType) {
        this.customerId = customerId;
        this.status = status;
        this.updateType = updateType;
        this.updateAt = AppUtils.getCurrentTimestamp();
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CUSTOMER_ID", nullable = false)
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "UPDATE_TYPE", nullable = false)
    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    @Column(name = "UPDATED_AT", nullable = false)
    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Column(name = "PUBLISH_TIME",nullable = true)
    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    @Column(name = "PROCESS_START_TIME",nullable = true)
    public Date getProcessStartTime() {
        return processStartTime;
    }

    public void setProcessStartTime(Date processStartTime) {
        this.processStartTime = processStartTime;
    }

    @Column(name = "PROCESS_END_TIME",nullable = true)
    public Date getProcessEndTime() {
        return processEndTime;
    }

    public void setProcessEndTime(Date processEndTime) {
        this.processEndTime = processEndTime;
    }

    @Column(name = "TOTAL_PROCESS_TIME",nullable = true)
    public Long getTotalProcessSec() {
        return totalProcessSec;
    }

    public void setTotalProcessSec(Long totalProcessSec) {
        this.totalProcessSec = totalProcessSec;
    }

    @Column(name = "CLEVERTAP_RESPONSE_TIME",nullable = true)
    public Long getClevertapResponseTime() {
        return clevertapResponseTime;
    }

    public void setClevertapResponseTime(Long clevertapResponseTime) {
        this.clevertapResponseTime = clevertapResponseTime;
    }
}
