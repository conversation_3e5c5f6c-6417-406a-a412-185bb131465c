/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.delivery.model.*;
import com.stpl.tech.util.AppUtils;

public class SFXResponseAdapter
		implements ResponseAdapter<SFXResponse, SFXErrorResponse, SFXCallbackObject, DeliveryResponse> {

	private static final Integer PARTNER_ID = 3;

	@Override
	public DeliveryResponse adapt(SFXResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		SFXOrderResponse responseData = data.getData();
		response.setGeneratedOrderId(orderId);
		SFXRider riderDetails = responseData.getRider_Details();
		if (riderDetails != null) {
			response.setDeliveryBoyName(riderDetails.getRider_Name());
			response.setDeliveryBoyPhoneNum(riderDetails.getRider_Name());
		}
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryTaskId(String.valueOf(responseData.getSfx_Order_Id()));
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryStatus(getDeliveryStatusForSFX(responseData.getStatus()));
		return response;
	}

	private int getDeliveryStatusForSFX(String status) {
		if (status.equalsIgnoreCase("ALLOTTED")) {
			return 1;
		} else if (status.equalsIgnoreCase("DISPATCHED")) {
			return 6;
		}
		return DeliveryStatus.valueOf(status.toUpperCase()).getDeliveryStatus();
	}

	@Override
	public DeliveryResponse adaptError(SFXErrorResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setGeneratedOrderId(orderId);
		response.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		response.setFailureMessage(data != null ? data.getMessage() : null);
		return response;
	}

	@Override
	public DeliveryResponse adaptCallback(SFXCallbackObject data) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryBoyPhoneNum(data.getRiderContact());
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryStatus(getDeliveryStatusForSFX(data.getOrderStatus()));
		response.setDeliveryTaskId(data.getSfxOrderId());
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryBoyName(data.getRiderName());
		response.setFailureCode(data.getCancelReason());
		response.setFailureMessage(getCancellationMesssage(data.getCancelReason()));
		return response;
	}

	private String getCancellationMesssage(String cancelReason) {

		if (cancelReason != null) {
			Integer check = Integer.parseInt(cancelReason);
			switch (check) {
			case 0:
				return "delivery boy not allotted";
			case 1:
				return "delivery boy not reached";
			case 2:
				return "delivery boy not misbehaved";
			case 3:
				return "cash amount not returned";
			case 4:
				return "delivery delayed";
			case 5:
				return "others";
			default:
				return "others";
			}
		}
		return null;
	}

	@Override
	public DeliveryResponse adaptCancel(String orderId, String taskId) {
		return null;
	}

}
