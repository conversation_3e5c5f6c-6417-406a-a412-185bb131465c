package com.stpl.tech.kettle.webengage.data.dao;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderMetadataDetail;
import com.stpl.tech.kettle.webengage.data.model.OrderDataPushTrack;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by Chaayos on 02-05-2017.
 */
public interface WebEngageDataPushDao extends AbstractDao {

    public List<CustomerInfo> getCustomerBatch(Integer startId, Integer batchSize);

    public List<Integer> getSettledKettleOrderBatch(Integer startOrderId, Integer batchSize);

    public List<OrderDataPushTrack> findLastOrderTrack();

    public List<OrderDetail> findOrdersFromBizDate(Date bizDate);

    public List<OrderMetadataDetail> getOrderMetadataDetail(Integer orderId);

    public List<LoyaltyScore> getCustomerLoyaltyScores(Set<Integer> customerIds);
}
