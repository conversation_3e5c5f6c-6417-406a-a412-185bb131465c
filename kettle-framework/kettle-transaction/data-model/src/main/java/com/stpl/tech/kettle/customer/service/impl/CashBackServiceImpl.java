package com.stpl.tech.kettle.customer.service.impl;

import com.stpl.tech.kettle.core.ChaayosCashSmsPayload;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.service.CashBackOfferCache;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.service.CashBackService;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderMetadata;
import com.stpl.tech.kettle.offer.model.CashBackOfferDTO;
import com.stpl.tech.kettle.referral.dao.CashManagerDao;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class CashBackServiceImpl implements CashBackService {

    private static final Logger LOG = LoggerFactory.getLogger(CashBackServiceImpl.class);

    @Autowired
    CashManagerDao cashDao;

    @Autowired
    CustomerDao crmDao;

    @Autowired
    EnvironmentProperties props;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private CashBackOfferCache cashBackOfferCache;

    @Autowired
    private OfferManagementDao offerManagementDao;

    @Override
    public Date getStartDate() {
    	return props.getCashBackCardStartDate();
    }

    @Override
    public void checkCashBack(Order order) {
        if (order.getBrandId().equals(AppConstants.DOHFUL_BRAND_ID) &&
                AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode()) && order.getCashRedeemed() != null
                && order.getCashRedeemed().intValue() > 0){
            order.setCashBackReceived(false);
            return;
        }
        CashBackOfferDTO cashBackOfferDTO = cashBackOfferCache.getOfferDataForUnit(order.getUnitId());
        BigDecimal amount = order.getTransactionDetail().getTaxableAmount();
        BigDecimal cashBackAwarded = BigDecimal.ZERO;
        cashBackAwarded = cashBackAwarded.setScale(0, RoundingMode.HALF_UP);
        if (Objects.isNull(cashBackOfferDTO)) {
            LOG.info("No valid cashback offer running for unit id : {}", order.getUnitId());
        }else{
            cashBackAwarded = AppUtils.percentOf(amount, props.getCashBackPercentage());
            cashBackAwarded = cashBackAwarded.setScale(0, RoundingMode.HALF_UP);
        }
        boolean isGiftCardOrder = hasGiftCard(order);
        if (!TransactionUtils.isCODOrder(order.getSource()) && !isGiftCardOrder
                && cashBackAwarded.compareTo(BigDecimal.ONE) >= 0 && AppUtils.isBetweenDatesAbs(
                AppUtils.getBusinessDate(), props.getCashBackStartDate(), props.getCashBackEndDate())) {
            order.setCashBackAwarded(cashBackAwarded);
            order.setCashBackReceived(true);
            order.setCashBackStartDate(props.getCashBackStartDate());
            order.setCashBackEndDate(props.getCashBackEndDate());
            //OrderMetadata orderMetadata = new OrderMetadata();
            OrderMetadata cashBack = new OrderMetadata();
            cashBack.setAttributeName("CASHBACK_AWARD");
            cashBack.setAttributeValue(cashBackAwarded.toString());
          //  order.getMetadataList().add(orderMetadata);
            order.getMetadataList().add(cashBack);
        } else {
           // OrderMetadata orderMetadata = new OrderMetadata();
           // order.getMetadataList().add(orderMetadata);
            order.setCashBackReceived(false);
        }
    }

    private Integer checkCashBackOfferAndGetValue(String offerCode){
        String prefix = props.getCashBackCouponCodePrefix();
        if(offerCode.startsWith(prefix)){
            String digits = offerCode.substring(prefix.length());
            if(digits.matches("\\d+")){
                return Integer.parseInt(digits);
            }
        }
        return 0;
    }

    private Integer checkCashBackOfferForNewCustomerAndGetValue(String offerCode){
        String prefix = props.getCashBackCouponCodeForNewCustomerPrefix();
        try {
            if (offerCode.startsWith(prefix)) {
                String digits = offerCode.substring(prefix.length());
                if (digits.matches("\\d+")) {
                    return Integer.parseInt(digits);
                }
            }
        }catch (Exception e){
            LOG.error("Error in Validating cashback Coupon For new Customer",e);
        }
        return 0;
    }

    private boolean isValidCityForCashBack(int unitId){
        try {
            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
            List<String> validUnitList = props.getValidUnitList();
            if(Objects.nonNull(validUnitList) && !validUnitList.isEmpty()){
                for(String unit : validUnitList){
                    if(unitBasicDetail.getCity().toLowerCase().equals(unit.toLowerCase())){
                        return true;
                    }
                }
            }
        }catch (Exception e){
            LOG.error("Error in validating city for cashback ",e);
        }
        return false;
    }
    public void awardCashBackOffer(Order order) {
        if (order.getBrandId().equals(AppConstants.DOHFUL_BRAND_ID) &&
                AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode()) && order.getCashRedeemed() != null
                && order.getCashRedeemed().intValue() > 0){
            order.setCashBackReceived(false);
            return;
        }
        try{
            if(order.getUnitId() == 0){
                LOG.info("Invalid unit found while awarding cashback offer");
            }
            BigDecimal amount = order.getTransactionDetail().getTaxableAmount();
            BigDecimal cashBackAwarded = BigDecimal.ZERO;
            Integer cashBackValue = 0;
            int validityInDays = 0;
            int lagDays = 0;
            if(Objects.nonNull(order.getOfferCode()) && !order.getOfferCode().isEmpty()){
                cashBackValue = checkCashBackOfferAndGetValue(order.getOfferCode());
            }
            else if(order.isNewCustomer() && isValidCityForCashBack(order.getUnitId())){
                if(Objects.nonNull(props.getCashBackCouponCodeForNewCustomer()) && !props.getCashBackCouponCodeForNewCustomer().isEmpty()) {
                    cashBackValue = checkCashBackOfferForNewCustomerAndGetValue(props.getCashBackCouponCodeForNewCustomer());
                    if(cashBackValue>0) {
                        order.setOfferCode(props.getCashBackCouponCodeForNewCustomer());
                    }
                }
            }
            if(cashBackValue!=0){
                cashBackAwarded = AppUtils.percentOf(amount,BigDecimal.valueOf(cashBackValue));
                validityInDays = props.getValidityForCashBackCoupon();
                lagDays = 0;
            }else {
                CashBackOfferDTO cashBackOfferDTO = cashBackOfferCache.getOfferDataForUnit(order.getUnitId());
                if (Objects.isNull(cashBackOfferDTO)) {
                    LOG.info("No valid cashback offer running for unit id : {}", order.getUnitId());
                    return;
                } else {
                    if((order.getBrandId().intValue() == AppConstants.DOHFUL_BRAND_ID
                            && amount.compareTo(cashBackOfferDTO.getMinOrderValue())>0)
                            || AppConstants.DOHFUL_BRAND_ID != order.getBrandId().intValue()) {
                        cashBackAwarded = AppUtils.percentOf(amount, cashBackOfferDTO.getCashbackPercentage());
                        validityInDays = cashBackOfferDTO.getValidityInDays();
                        lagDays = cashBackOfferDTO.getLagDays();
                    }
                }
            }
            boolean isGiftCardOrder = hasGiftCard(order);
            cashBackAwarded = cashBackAwarded.setScale(0, RoundingMode.HALF_UP);
            if (!TransactionUtils.isCODOrder(order.getSource()) && !isGiftCardOrder
                    && cashBackAwarded.compareTo(BigDecimal.ONE) >= 0) {
                order.setCashBackAwarded(cashBackAwarded);
                order.setCashBackReceived(true);
                Date startDate = AppUtils.addDays(AppUtils.getBusinessDate(), lagDays);
                order.setCashBackStartDate(startDate);
                order.setCashBackEndDate(AppUtils.addDays(startDate, validityInDays));
                order.setCashBackLagDays(lagDays);
                OrderMetadata cashBack = new OrderMetadata();
                cashBack.setAttributeName("CASHBACK_OFFER");
                cashBack.setAttributeValue(cashBackAwarded.toString());
                order.getMetadataList().add(cashBack);
            } else {
                LOG.info("Unable to award cash back");
                order.setCashBackReceived(false);
            }
        }catch (Exception e){
            LOG.info("Error while allowing cash back for generated id : {}",order.getGenerateOrderId());
            LOG.error("error",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void invalidateCashBack(Order order) {
        cashDao.invalidateCashBack(order);
    }

    @Override
    public Date getEndDate() {
    	return props.getCashBackCardEndDate();
    }


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean allotCashBack(BigDecimal amount, Integer customerId, int orderId, Date activationTime,
			Date expirationDate, int lagDays) {
		String refCode = crmDao.getCustomerRefCode(customerId);
		return cashDao.addCashForCashBack(customerId, refCode, orderId, amount, activationTime, expirationDate, lagDays);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CashPacketData allotCashAsFreeGift(BigDecimal amount, String contact, Integer validityInDays, Integer lagDays,Customer info) throws JMSException, IOException {
        if(Objects.isNull(info)){
            info = crmDao.getCustomer(contact);
        }
        String refCode = crmDao.getCustomerRefCode(info.getId());
        Date creationDate = AppUtils.getCurrentTimestamp();
        Date expirationDate = AppUtils.addDays(creationDate,validityInDays+lagDays);
        CashPacketData cashPacketData = cashDao.addCashAsFreeGift(info.getId(),refCode,-1,amount,creationDate,expirationDate, lagDays);
//        ChaayosCashSmsPayload smsPayload= new ChaayosCashSmsPayload();
//        smsPayload.setCustomerName(info.getFirstName());
//        smsPayload.setAmount(cashPacketData.getInitialAmount());
//        smsPayload.setExpiryDate(AppUtils.getSMSTemplateDate(cashPacketData.getExpirationDate()));
//        if(Objects.nonNull(cashPacketData)){
//            Map<String,String> map =new HashMap<>();
//            map.put("amount",cashPacketData.getInitialAmount().toString());
//            map.put("name",info.getFirstName());
//            map.put("validTill",AppUtils.getDateString(cashPacketData.getExpirationDate(),AppUtils.DATE_FORMAT_STRING));
//            SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(masterDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID));
//            notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_CASH_GIFT.name(), CustomerSMSNotificationType.CHAAYOS_CASH_GIFT.getMessage(smsPayload), info.getContactNumber(),
//                    smsWebServiceClient, props.getAutomatedNPSSMS(),
//                    getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_CASH_GIFT, info, map, -1));
//        }
        return cashPacketData;
    }

    public boolean hasGiftCard(Order order) {
        for (OrderItem item : order.getOrders()) {
            if (AppUtils.isGiftCard(item.getCode())) {
                return true;
            }
        }
        return false;
    }

    private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
                                                       Map<String, String> map, Integer orderId) {
        try {
            NotificationPayload load = new NotificationPayload();
            if (Objects.nonNull(customer)) {
                load.setCustomerId(customer.getId());
            }
            load.setContactNumber(customer.getContactNumber());
            load.setOrderId(orderId);
            load.setMessageType(type.name());
            load.setSendWhatsapp(type.isWhatsapp());
            if (Objects.nonNull(customer.getOptWhatsapp())) {
                load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }

            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(map);
            return load;
        } catch (Exception e) {
            LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    orderId);
            return null;
        }
    }

}
