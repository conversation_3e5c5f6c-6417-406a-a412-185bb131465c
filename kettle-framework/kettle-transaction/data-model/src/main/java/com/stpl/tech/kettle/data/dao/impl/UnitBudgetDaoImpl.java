/**
 *
 */
package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetKey;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.DirectCost;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.core.data.budget.vo.SalesCommissionData;
import com.stpl.tech.kettle.core.data.budget.vo.ServiceAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.WastageAggregate;
import com.stpl.tech.kettle.data.dao.ExpenseManagementDao;
import com.stpl.tech.kettle.data.dao.PosMetadataDao;
import com.stpl.tech.kettle.data.dao.UnitBudgetDao;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class UnitBudgetDaoImpl extends AbstractDaoImpl implements UnitBudgetDao {

    private static final Logger LOG = LoggerFactory.getLogger(UnitBudgetDaoImpl.class);

    @Autowired
    private ExpenseManagementDao expenseDao;

    @Autowired
    private PosMetadataDao posMetadataDao;

    @Override
    public Map<Integer, RevenueData> getRevenueData(Date businessDate, List<Integer> unitIds) {
        return null;
    }

    @Override
    public Map<Integer, SalesCommissionData> getSalesCommissionData(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, InventoryAggregate> getInventoryAggregate(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, WastageAggregate> getWastageAggregate(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, ConsumablesAggregate> getConsumablesAggregate(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, ExpenseAggregate> getExpenseAggregate(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, ElectricityAggregate> getElectricityAggregate(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, RentAggregate> getRentAggregate(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Map<Integer, DirectCost> getDirectCost(Date businessDate, List<Integer> unitIds) {
        // TODO Auto-generated method stub
        return null;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.budget.dao.BudgetMetadataDao#
     * getCreditCardPercentageForCurrentMonth(int)
     */
    @Override
    public BigDecimal getCreditCardPercentageForCurrentMonth(int unitId, Date businessDate) {
        try {
            Object data = null;
            Query query = manager.createQuery(
                    "select creditCardTransactionPercentage From UnitBudgetoryDetail where unitId= :unitId and year = :year and month = :month");
            query.setParameter("unitId", unitId);
            query.setParameter("year", AppUtils.getYear(businessDate));
            query.setParameter("month", AppUtils.getMonth(businessDate));
            data = query.getSingleResult();
            if (data != null && data instanceof BigDecimal) {
                return (BigDecimal) data;
            }
        } catch (Exception e) {
        }
        return null;

    }

    @Override
    public UnitExpenditureDetail getExpenditureForUnit(Date businessDate, Integer unitId, CalculationStatus status,
                                                       CalculationType type) {
        Query query = manager.createQuery(
                " From UnitExpenditureDetail where unitId= :unitId and businessDate = :businessDate and status = :status and calculation = :calculation ");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        query.setParameter("status", status.name());
        query.setParameter("calculation", type.name());
        List<UnitExpenditureDetail> list = query.getResultList();
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public UnitExpenditureAggregateDetail getAggregateForUnit(Integer unitExpenditureDetailId) {
        Query query = manager.createQuery(
                " From UnitExpenditureAggregateDetail where unitExpenditureDetailId= :unitExpenditureDetailId ");
        query.setParameter("unitExpenditureDetailId", unitExpenditureDetailId);
        if (query.getResultList().size() > 0) {
            return (UnitExpenditureAggregateDetail) query.getResultList().get(0);
        }
        return null;
    }

    @Override
    public List<UnitExpenditureAggregateDetail> getExpenditureAggregateForUnit(List<Date> businessDates, Integer unitId,
                                                                               CalculationStatus status, CalculationType type) {
        Query query = manager.createQuery(
                " From UnitExpenditureAggregateDetail where unitId= :unitId and businessDate IN :businessDates and status = :status and calculation = :calculation order by businessDate");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDates", businessDates);
        query.setParameter("status", status.name());
        query.setParameter("calculation", type.name());
        List<UnitExpenditureAggregateDetail> list = query.getResultList();
        return list;

    }

    @Override
    public UnitExpenditureAggregateDetail getExpenditureAggregateForUnit(Date businessDates, Integer unitId,
                                                                         CalculationStatus status, CalculationType type) {
        Query query = manager.createQuery(
                " From UnitExpenditureAggregateDetail where unitId= :unitId and businessDate = :businessDates and status = :status and calculation = :calculation ");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDates", businessDates);
        query.setParameter("status", status.name());
        query.setParameter("calculation", type.name());
        List<UnitExpenditureAggregateDetail> list = query.getResultList();
        if (list.size() > 0) {
            return list.get(0);
        }
        return new UnitExpenditureAggregateDetail();

    }

    @Override
    public List<UnitExpenditureDetail> getExpenditureForUnit(List<Date> businessDates, Integer unitId,
                                                             CalculationStatus status, CalculationType type) {
        Query query = manager.createQuery(
                " From UnitExpenditureDetail where unitId= :unitId and businessDate in :businessDates and status = :status and calculation = :calculation ");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDates", businessDates);
        query.setParameter("status", status.name());
        query.setParameter("calculation", type.name());
        return query.getResultList();
    }

    @Override
    public UnitBudgetoryDetail getUnitBudgetDetail(int unitId, int month, int year) {
        try {
            Query query = manager.createQuery(
                    "From UnitBudgetoryDetail where unitId= :unitId and year = :year and month = :month and status =:status");
            query.setParameter("unitId", unitId);
            query.setParameter("year", year);
            query.setParameter("month", month);
            query.setParameter("status", AppConstants.ACTIVE);
            return (UnitBudgetoryDetail) query.getSingleResult();
        } catch (Exception e) {
        }
        return null;

    }

    @Override
    public List<UnitExpenditureDetail> getUnitExpenditureDetail(Date businessDate, CalculationStatus status,
                                                                CalculationType calculation) {
        try {
            Query query = manager.createQuery(
                    "From UnitExpenditureDetail where status= :status and businessDate = :businessDate and calculation = :calculation");
            query.setParameter("businessDate", businessDate);
            query.setParameter("status", status.name());
            query.setParameter("calculation", calculation.name());
            return query.getResultList();
        } catch (Exception e) {

        }
        return new ArrayList<>();

    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.data.dao.UnitBudgetDao#savePnL(com.stpl.tech.kettle.
     * core.data.budget.vo.BudgetDetail)
     */
    @Override
    public int savePnL(BudgetDetail b) {
        UnitExpenditureDetail d = new UnitExpenditureDetail();
        setKey(d, b.getKey());
        setRevenue(d, b.getRevenue());
        setCommissions(d, b.getCommissions());
        manager.persist(d);
        manager.flush();
        return d.getDetailId();
    }

	@Override
    public boolean savePnL(Collection<BudgetDetail> budgets) {
        try {
            int count=0;
            for (BudgetDetail b : budgets) {
                count++;
                LOG.info("Creating UnitExpenditureDetail for unit {}", b.getKey().getUnitId());
                UnitExpenditureDetail d = new UnitExpenditureDetail();
                setKey(d, b.getKey());
                setRevenue(d, b.getRevenue());
                setCommissions(d, b.getCommissions());
                manager.persist(d);
                if(count==20){
                    manager.flush();
                    count=0;
                }
            }
            if(count>=0){
                manager.flush();
            }
            return true;
        } catch (Exception e) {
            LOG.error("Exception Caught While Saving PnL", e);
            return false;
        }
    }

    @Override
    public UnitExpenditureDetail saveSCMPnL(UnitExpenditureDetail d, BudgetDetail budgetDetail, CalculationStatus status, CalculationType type) {
        if (budgetDetail.getWastage() != null) {
            setWastageAggregate(d, budgetDetail.getWastage());
        } else {
            LOG.info("Wastage is NULL for {} for day {} , month {}", budgetDetail.getKey().getUnitName(),
                    budgetDetail.getKey().getDay(), budgetDetail.getKey().getMonth());
        }
        if (budgetDetail.getInventory() != null) {
            setInventoryAggregate(d, budgetDetail.getInventory(), type);
        } else {
            LOG.info("Inventory is NULL for {} for day {} , month {}", budgetDetail.getKey().getUnitName(),
                    budgetDetail.getKey().getDay(), budgetDetail.getKey().getMonth());
        }
        if (budgetDetail.getConsumables() != null) {
            setConsumablesAggregate(d, budgetDetail.getConsumables());
        } else {
            LOG.info("Consumables is NULL for {} for day {} , month {}", budgetDetail.getKey().getUnitName(),
                    budgetDetail.getKey().getDay(), budgetDetail.getKey().getMonth());
        }

        if (budgetDetail.getServices() != null) {
            setServiceAggregate(d, budgetDetail.getServices());
        } else {
            LOG.info("Service Order is NULL for {} for day {} , month {}", budgetDetail.getKey().getUnitName(),
                    budgetDetail.getKey().getDay(), budgetDetail.getKey().getMonth());
        }
        if (!CalculationType.FINALIZED.equals(type)) {
            d.setStatus(CalculationStatus.PENDING_MTD_CALCULATION.name());
        }
        d.setSumoClosureId(budgetDetail.getKey().getSumoClosureId());
        manager.merge(d);
        return d;
    }

    private void setRentAggregate(UnitExpenditureDetail d, RentAggregate b) {
        if (b == null) {
            return;
        }
        d.setOnRevenueShare(AppConstants.getValue(b.isOnRevenueShare()));
        d.setPropertyFixRent(b.getPropertyFixRent());
        d.setRevenueShare(b.getRevenueShare());
        d.setRevenueShareDineIn(b.getRevenueShareDineIn());
        d.setRevenueShareDelivery(b.getRevenueShareDelivery());
    }

    private void setWastageAggregate(UnitExpenditureDetail d, WastageAggregate b) {
        d.setUnsatifiedCustomerCost(b.getUnsatifiedCustomerCost());
        d.setPPECost(b.getPPECost());
        d.setExpiryWastage(b.getExpiryWastage());
        d.setWastageOther(b.getWastageOther());
        d.setMarketingAndSampling(b.getMarketingAndSampling());
        d.setTrainingCogs(b.getTrainingCogs());

        d.setUnsatifiedCustomerCostTax(b.getUnsatifiedCustomerCostTax());
        d.setPPECostTax(b.getPPECostTax());
        d.setExpiryWastageTax(b.getExpiryWastageTax());
        d.setWastageOtherTax(b.getWastageOtherTax());
        d.setMarketingAndSamplingTax(b.getMarketingAndSamplingTax());
        d.setTrainingCogsTax(b.getTrainingCogsTax());

    }

    private void setInventoryAggregate(UnitExpenditureDetail d, InventoryAggregate b, CalculationType type) {
        d.setDineInCogs(b.getDineInCogs());
        d.setDeliveryCogs(b.getDeliveryCogs());
        d.setEmployeeMealCogs(b.getEmployeeMealCogs());
        d.setStockVariance(b.getStockVariance());
        d.setVarianceZero(b.getZeroVariance());
        d.setVariancePCC(b.getVariancePCC());
        d.setVarianceYC(b.getVarianceYC());

        d.setDineInCogsTax(b.getDineInCogsTax());
        d.setDeliveryCogsTax(b.getDeliveryCogsTax());
        d.setEmployeeMealCogsTax(b.getEmployeeMealCogsTax());
        d.setStockVarianceTax(b.getStockVarianceTax());
        d.setVarianceZeroTax(b.getZeroVarianceTax());
        d.setVariancePCCTax(b.getVariancePCCTax());
        d.setVarianceYCTax(b.getVarianceYCTax());

    }

    private void setConsumablesAggregate(UnitExpenditureDetail d, ConsumablesAggregate b) {
        d.setConsumable(b.getConsumable());
//		d.setFixedAssets(b.getFixedAssets());
        d.setFixedAssetsCapex(b.getFixedAssetsCapex());
        d.setConsumableMarketing(b.getConsumableMarketing());
        d.setConsumableOthers(b.getConsumableOthers());

        d.setDepreciationOfFurnitureFixture(b.getDepreciationFurnitureFixture());
        d.setDepreciationOfOfficeEquipment(b.getDepreciationOfficeEquipment());
        d.setDepreciationOfKitchenEquipment(b.getDepreciationKitchenEquipment());
        d.setDepreciationOfEquipment(b.getDepreciationEquipment());
        d.setDepreciationOfIt(b.getDepreciationIt());
        d.setDepreciationOfVehicle(b.getDepreciationVehicle());
        d.setDepreciationOfOthers(b.getDepreciationOthers());

        d.setConsumableTax(b.getConsumableTax());
        d.setFixedAssetsTax(b.getFixedAssetsTax());
        d.setConsumableMarketingTax(b.getConsumableMarketingTax());
        d.setConsumableOthersTax(b.getConsumableOthersTax());
        d.setFixedAssetsCapexTax(b.getFixedAssetsCapexTax());

        d.setFixedAssetsEquipmentTax(b.getFixedAssetsEquipmentTax());
        d.setFixedAssetFurnitureTax(b.getFixedAssetFurnitureTax());
        d.setFixedAssetsItTax(b.getFixedAssetsItTax());
        d.setFixedAssetsKitchenEquipmentTax(b.getFixedAssetsKitchenEquipmentTax());
        d.setFixedAssetsOfficeEquipmentTax(b.getFixedAssetsOfficeEquipmentTax());
        d.setFixedAssetsVehicleTax(b.getFixedAssetsVehicleTax());
        d.setFixedAssetsOthersSubCategoryCafeTax(b.getFixedAssetsOthersSubCategoryCafeTax());
        d.setFixedAssetsEquipmentHq(b.getFixedAssetsEquipmentHq());
        d.setFixedAssetFurnitureHq(b.getFixedAssetFurnitureHq());
        d.setFixedAssetsItHq(b.getFixedAssetsItHq());
        d.setFixedAssetsKitchenEquipmentHq(b.getFixedAssetsKitchenEquipmentHq());
        d.setFixedAssetsOfficeEquipmentHq(b.getFixedAssetsOfficeEquipmentHq());
        d.setFixedAssetsVehicleHq(b.getFixedAssetsVehicleHq());
        d.setFixedAssetsOthersSubCategoryHq(b.getFixedAssetsOthersSubCategoryHq());
        d.setFixedAssetsEquipmentHqTax(b.getFixedAssetsEquipmentHqTax());
        d.setFixedAssetFurnitureHqTax(b.getFixedAssetFurnitureHqTax());
        d.setFixedAssetsItHqTax(b.getFixedAssetsItHqTax());
        d.setFixedAssetsKitchenEquipmentHqTax(b.getFixedAssetsKitchenEquipmentHqTax());
        d.setFixedAssetsOfficeEquipmentHqTax(b.getFixedAssetsOfficeEquipmentHqTax());
        d.setFixedAssetsVehicleHqTax(b.getFixedAssetsVehicleHqTax());
        d.setFixedAssetsOthersSubCategoryHqTax(b.getFixedAssetsOthersSubCategoryHqTax());

        d.setFixedAssetsEquipment(b.getFixedAssetsEquipment());
        d.setFixedAssetFurniture(b.getFixedAssetFurniture());
        d.setFixedAssetsIT(b.getFixedAssetsIT());
        d.setFixedAssetsKitchenEquipment(b.getFixedAssetsKitchenEquipment());
        d.setFixedAssetsOfficeEquipment(b.getFixedAssetsOfficeEquipment());
        d.setFixedAssetsVehicle(b.getFixedAssetsVehicle());
        d.setFixedAssetsOthersSubCategory(b.getFixedAssetsOthersSubCategory());

        d.setConsumableCutlery(b.getConsumableCutlery());
        d.setConsumableEquipment(b.getConsumableEquipment());
        d.setConsumableStationary(b.getConsumableStationary());
        d.setConsumableUniform(b.getConsumableUniform());
        d.setConsumableUtility(b.getConsumableUtility());
        d.setConsumableDisposable(b.getConsumableDisposable());

        d.setConsumableLhi(b.getConsumableLhi());
        d.setConsumableIt(b.getConsumableIt());
        d.setConsumableMaintenance(b.getConsumableMaintenance());
        d.setConsumableOfficeEquipment(b.getConsumableOfficeEquipment());
        d.setConsumableChaiMonk(b.getConsumableChaiMonk());
        d.setConsumableKitchenEquipment(b.getConsumableKitchenEquipment());

        d.setFixedAssetsLost(b.getFixedAssetLost());
        d.setFixedAssetsDamage(b.getFixedAssetDamage());
        d.setFixedAssetsDepreciation(b.getFixedAssetDepreciation());

        d.setConsumableUtilityTax(b.getConsumableUtilityTax());
        d.setConsumableStationaryTax(b.getConsumableStationaryTax());
        d.setConsumableUniformTax(b.getConsumableUniformTax());
        d.setConsumableEquipmentTax(b.getConsumableEquipmentTax());
        d.setConsumableCutleryTax(b.getConsumableCutleryTax());
        d.setConsumableDisposableTax(b.getConsumableDisposableTax());

    }

    private void setServiceAggregate(UnitExpenditureDetail d, ServiceAggregate b) {
        d.setSecurityGuardCharges(b.getSecurityGuardCharges());
        d.setOtherServiceCharges(b.getOtherServiceCharges());
        d.setFuelCharges(b.getFuelCharges());
        d.setLogisticCharges(b.getLogisticCharges());
        d.setCommunicationInternet(b.getCommunicationInternet());
        d.setCommunicationTelephone(b.getCommunicationTelephone());
        d.setCommunicationILL(b.getCommunicationILL());
        d.setPayrollProcessingFee(b.getPayrollProcessingFee());
        d.setNewsPaper(b.getNewsPaper());
        d.setStaffWelfareExpenses(b.getStaffWelfareExpenses());
        d.setCourierCharges(b.getCourierCharges());
        d.setPrintingAndStationary(b.getPrintingAndStationary());
        d.setBusinessPromotion(b.getBusinessPromotion());
        d.setLegalCharges(b.getLegalCharges());
        d.setProfessionalCharges(b.getProfessionalCharges());
        d.setCleaningCharges(b.getCleaningCharges());
        d.setPestControlCharges(b.getPestControlCharges());
        d.setTechnologyTraining(b.getTechologyTraining());
        d.setCorporateMarketingDigital(b.getCorporateMarketingDigital());
        d.setCorporateMarketingAdvOffline(b.getCorporateMarketingAdvOffline());
        d.setCorporateMarketingChannelPartner(b.getCorporateMarketingChannelPartner());
		d.setCorporateMarketingAdvOnline(b.getCorporateMarketingAdvOnline());
        d.setCorporateMarketingOutdoor(b.getCorporateMarketingOutdoor());
        d.setCorporateMarketingAgencyFees(b.getCorporateMarketingAgencyFees());
        d.setDeliveryChargesVariable(b.getDeliveryChargesVariable());
//		d.setConveyanceMarketing(b.getConveyanceMarketing());
//		d.setConveyanceOperations(b.getConveyanceOperations());
//		d.setConveyanceOthers(b.getConveyanceOthers());
        d.setAuditFee(b.getAuditFee());
        d.setAuditFeeOutOfPocket(b.getAuditFeeOutOfPocket());
        d.setBrokerage(b.getBrokerage());
        d.setCharityAndDonations(b.getCharityAndDonations());
        d.setDomesticTicketsAndHotels(b.getDomesticTicketsAndHotels());
        d.setInternationalTicketsAndHotels(b.getInternationalTicketsAndHotels());
        d.setHouseKeepingCharges(b.getHouseKeepingCharges());
        d.setLateFeeCharges(b.getLateFeeCharges());
//		d.setMarketingDataAnalysis(b.getMarketingDataAnalysis());
        d.setMiscellaneousExpenses(b.getMiscellaneousExpenses());
        d.setPenalty(b.getPenalty());
        d.setPhotoCopyExpenses(b.getPhotoCopyExpenses());
        d.setQcrExpense(b.getQcrExpense());
        d.setRecuritmentConsultants(b.getRecuritmentConsultants());
        d.setRocFees(b.getRocFees());
        d.setDebitCreditWrittenOff(b.getDebitCreditWrittenOff());
        d.setDifferenceInExchange(b.getDifferenceInExchange());
        d.setRnDEngineeringExpenses(b.getRnDEngineeringExpenses());
        d.setCapitalImprovementExpenses(b.getCapitalImprovementExpenses());
        d.setLeaseHoldImprovements(b.getLeaseHoldImprovements());
        d.setMarketingLaunch(b.getMarketingLaunch());
        d.setCorporateMarketingPhotography(b.getCorporateMarketingPhotography());
        d.setOdcRental(b.getOdcRental());
//		d.setCogsOthers(b.getCogsOthers());
        d.setCommissionChange(b.getCommissionChange());

        d.setVehicleRegularMaintenanceHq(b.getVehicleRegularMaintenanceHq());
        d.setBuildingMaintenanceHq(b.getBuildingMaintenanceHq());
        d.setComputerItMaintenanceHq(b.getComputerItMaintenanceHq());
        d.setEquipmentMaintenanceHq(b.getEquipmentMaintenanceHq());
        d.setMarketingNpiHq(b.getMarketingNpiHq());
        d.setLicenseExpenses(b.getLicenseExpenses());
        d.setCorporateMarketingAtlRadio(b.getCorporateMarketingAtlRadio());
        d.setCorporateMarketingAtlTv(b.getCorporateMarketingAtlTv());
        d.setCorporateMarketingAtlPrintAd(b.getCorporateMarketingAtlPrintAd());
        d.setCorporateMarketingAtlCinema(b.getCorporateMarketingAtlCinema());
        d.setCorporateMarketingAtlDigital(b.getCorporateMarketingAtlDigital());
        d.setLogisticInterstateColdVehicle(b.getLogisticInterstateColdVehicle());
        d.setLogisticInterstateNonColdVehicle(b.getLogisticInterstateNonColdVehicle());
        d.setLogisticInterstateAir(b.getLogisticInterstateAir());
        d.setLogisticInterstateRoad(b.getLogisticInterstateRoad());
        d.setLogisticInterstateTrain(b.getLogisticInterstateTrain());
        d.setAirConditionerAmc(b.getAirConditionerAmc());
        d.setCorporateMarketingSms(b.getCorporateMarketingSms());
        d.setEmployeeFacilitationExpenses(b.getEmployeeFacilitationCharges());
        d.setProntoAMC(b.getProntoAMC());
        d.setOthersMaintenance(b.getOthersMaintenance());
        d.setTechnologyPlatformCharges(b.getTechnologyPlatformCharges());
        d.setInterestOnTermLoan(b.getInterestOnTermLoan());
        d.setTechnologyOthers(b.getTechnologyOthers());
        d.setSystemRental(b.getSystemRental());
        d.setRoRental(b.getRoRental());
        d.setInsurnaceAccidental(b.getInsuranceAccidental());
        d.setDgRental(b.getDgRental());
        d.setOthersAMC(b.getOthersAMC());
        d.setMusicRentals(b.getMusicRentals());
//        mtd.setVoucherTransactionCharges(b.getVoucherTransactionCharges());
        d.setInsuranceAssets(b.getInsuranceAssets());
        d.setInsuranceCGL(b.getInsuranceCGL());
        d.setInsurnaceMedical(b.getInsuranceMedical());
//        mtd.setPropertyFixRent(b.getPropertyFixRent());
        d.setPettyCashRentals(b.getPettyCashRentals());
//        mtd.setEnergyDGRunningCafe();
        d.setEdcRental(b.getEdcRental());

    }

    /**
     * @param d
     * @param b
     */
    private void setCommissions(UnitExpenditureDetail d, SalesCommissionData b) {
        d.setCreditCardTransactionCharges(b.getCreditCardTransactionCharges());
        d.setVoucherTransactionCharges(b.getVoucherTransactionCharges());
        d.setWalletsTransactionCharges(b.getWalletsTransactionCharges());
        d.setCommissionChannelPartners(b.getCommissionChannelPartners());
        // d.setCommissionOthers(b.getCommissionOthers());
    }

    private void setKey(UnitExpenditureDetail d, BudgetKey b) {
        d.setUnitId(b.getUnitId());
        d.setUnitName(b.getUnitName());
        d.setDayClosureId(b.getDayClosureId());
        d.setCalculation(b.getCalculation());
        d.setBusinessDate(b.getBusinessDate());
        d.setYear(b.getYear());
        d.setMonth(b.getMonth());
        d.setDay(b.getDay());
        d.setStatus(CalculationStatus.CREATED.name());
    }

    private void setRevenue(UnitExpenditureDetail d, RevenueData b) {
        d.setNetSales(b.getNetSales());
        d.setNetRevenue(b.getNetRevenue());
        d.setRevenue(b.getRevenue());
        d.setApc(b.getApc());
        d.setTicket(b.getTicket());
        d.setDiscount(b.getDiscount());
        d.setDiscountLoyalty(b.getDiscountLoyalty());
        d.setDiscountMarketing(b.getDiscountMarketing());
        d.setDiscountOps(b.getDiscountOps());
        d.setDiscountBd(b.getDiscountBd());
        d.setDiscountEmployeeFico(b.getDiscountEmployeeFico());

        d.setEmpDiscountLoyalty(b.getEmpDiscountLoyalty());
        d.setEmpDiscountMarketing(b.getEmpDiscountMarketing());
        d.setEmpDiscountOps(b.getEmpDiscountOps());
        d.setEmpDiscountBd(b.getEmpDiscountBd());
        d.setEmpDiscountEmployeeFico(b.getEmpDiscountEmployeeFico());

        d.setGmv(b.getGmv());
        d.setDeliverySales(b.getDeliverySales());
        d.setDeliveryApc(b.getDeliveryApc());
        d.setDeliveryTicket(b.getDeliveryTicket());
        d.setDeliveryDiscount(b.getDeliveryDiscount());
        d.setDeliveryDiscountLoyalty(b.getDeliveryDiscountLoyalty());
        d.setDeliveryDiscountMarketing(b.getDeliveryDiscountMarketing());
        d.setDeliveryDiscountOps(b.getDeliveryDiscountOps());
        d.setDeliveryDiscountBd(b.getDeliveryDiscountBd());
        d.setDeliveryDiscountEmployeeFico(b.getDeliveryDiscountEmployeeFico());
        d.setDeliveryGmv(b.getDeliveryGmv());
        d.setDineInSales(b.getDineInSales());
        d.setDineInApc(b.getDineInApc());
        d.setDineInTicket(b.getDineInTicket());
        d.setDineInDiscount(b.getDineInDiscount());
        d.setDineInDiscountLoyalty(b.getDineInDiscountLoyalty());
        d.setDineInDiscountMarketing(b.getDineInDiscountMarketing());
        d.setDineInDiscountOps(b.getDineInDiscountOps());
        d.setDineInDiscountBd(b.getDineInDiscountBd());
        d.setDineInDiscountEmployeeFico(b.getDineInDiscountEmployeeFico());
        d.setDineInGmv(b.getDineInGmv());
        d.setEmployeeMealTicket(b.getEmployeeMealTicket());
        d.setEmployeeMealSales(b.getEmployeeMealSales());
        d.setEmployeeMealGmv(b.getEmployeeMealGmv());
        d.setGiftCardNetSale(b.getGiftCardNetSale());
        d.setGiftCardRedemption(b.getGiftCardRedemption());
        d.setGiftCardSale(b.getGiftCardSale());

        d.setDeliveryGiftCardNetSale(b.getDeliveryGiftCardNetSale());
        d.setDeliveryGiftCardRedemption(b.getDeliveryGiftCardRedemption());
        d.setDeliveryGiftCardSale(b.getDeliveryGiftCardSale());
        d.setDeliveryNetSales(b.getDeliveryNetSales());

        d.setDineInGiftCardNetSale(b.getDineInGiftCardNetSale());
        d.setDineInGiftCardRedemption(b.getDineInGiftCardRedemption());
        d.setDineInGiftCardSale(b.getDineInGiftCardSale());
        d.setDineInNetSales(b.getDineInNetSales());

    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.UnitBudgetDao#markPnLAsCancelled(int)
     */
    @Override
    public void changePnLStatus(int pnlDetailId, CalculationStatus status) {
        UnitExpenditureDetail d = manager.find(UnitExpenditureDetail.class, pnlDetailId);
        d.setStatus(status.name());
        manager.flush();
    }

    @Override
    public int getPnlDetailFromUnitAndDayCloseId(int unitId, int dayClosureId) {
        Query query = manager.createQuery(
                "FROM UnitExpenditureDetail u WHERE u.unitId = :unitId AND u.dayClosureId = :dayClosureId");
        query.setParameter("unitId", unitId);
        query.setParameter("dayClosureId", dayClosureId);
        try {
            return ((UnitExpenditureDetail) query.getSingleResult()).getDetailId();
        } catch (NoResultException | NonUniqueResultException e) {
            return 0;
        }
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.data.dao.UnitBudgetDao#saveKettleCalculatedExpenses(
     * int, com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail)
     */
    @Override
    public void saveKettleCalculatedExpenses(int detailId, BudgetDetail detail) {
        UnitExpenditureDetail d = manager.find(UnitExpenditureDetail.class, detailId);
        d.setStatus(CalculationStatus.PENDING_SUMO_CALCULATION.name());
        BudgetUtils.setDirectCost(d, detail.getUploadCost());
        // BudgetUtils.setElectricityAggregate(d, detail.getElectricity());
        BudgetUtils.setExpenseAggregate(d, detail.getExpenses());
        BudgetUtils.setAdjustmentAggregate(d, detail.getAdjustment());
        setRentAggregate(d, detail.getRentData());
        // calculated in MTD only
        // BudgetUtils.setCalculatedData(d, null);
        expenseDao.setPnLDetailId(d.getUnitId(), d.getDetailId(), d.getMonth(), d.getYear());
        manager.flush();
    }

    @Override
    public void updatePnLInVoucherData(int detailId, Date businessDate, List<VoucherData> voucherDataList)
            throws DataUpdationException {
        UnitExpenditureDetail d = manager.find(UnitExpenditureDetail.class, detailId);
        for (VoucherData voucherData : voucherDataList) {
            voucherData.setPnlDetailId(d.getDetailId());
            voucherData.setPnlInclusionDate(businessDate);
            voucherData = update(voucherData);
            if (voucherData == null) {
                throw new DataUpdationException("Error updating PnL id in vouchers.");
            }
        }
        manager.flush();
    }

    @Override
    public UnitExpenditureDetail getLatestUnitExpenditureDetail(int unitId, int month, int year,
                                                                CalculationStatus status, CalculationType calculation) {
        Integer detailId = null;
        try {
            Object data = null;
            Query query = manager.createQuery(
                    "select max(detailId) From UnitExpenditureDetail where unitId= :unitId and year = :year and month = :month and status = :status and calculation = :calculation");
            query.setParameter("unitId", unitId);
            query.setParameter("year", year);
            query.setParameter("month", month);
            query.setParameter("status", status.name());
            query.setParameter("calculation", calculation.name());
            data = query.getSingleResult();
            if (data != null && data instanceof Integer) {
                detailId = (Integer) data;
            }
        } catch (Exception e) {
        }
        if (detailId != null) {
            return manager.find(UnitExpenditureDetail.class, detailId);
        }
        return null;

    }

    @Override
    public UnitExpenditureAggregateDetail getLatestUnitExpenditureAggregateDetail(int unitId, int month, int year,
                                                                                  CalculationStatus status, CalculationType calculation) {
        Integer detailId = null;
        try {
            Object data = null;
            Query query = manager.createQuery("select max(detailId) From UnitExpenditureAggregateDetail "
                    + "where unitId= :unitId and year = :year and month = :month and status = :status and calculation = :calculation");
            query.setParameter("unitId", unitId);
            query.setParameter("year", year);
            query.setParameter("month", month);
            query.setParameter("status", status.name());
            query.setParameter("calculation", calculation.name());
            data = query.getSingleResult();
            if (data != null && data instanceof Integer) {
                detailId = (Integer) data;
            }
        } catch (Exception e) {
        }
        if (detailId != null) {
            return manager.find(UnitExpenditureAggregateDetail.class, detailId);
        }
        return null;

    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.data.dao.UnitBudgetDao#savePnL(com.stpl.tech.kettle.
     * data.model.UnitExpenditureDetail)
     */
    @Override
    public int savePnL(UnitExpenditureDetail expenseDetail) {
        manager.persist(expenseDetail);
        manager.flush();
        return expenseDetail.getDetailId();
    }

    @Override
    public List<Integer> getPnlUnitIds(Date d, CalculationStatus status, CalculationType type) {
        Query query = manager.createQuery(
                "select unitId From UnitExpenditureDetail where status= :status and businessDate = :businessDate and calculation = :calculation");
        query.setParameter("businessDate", d);
        query.setParameter("status", status.name());
        query.setParameter("calculation", type.name());
        return query.getResultList();
    }

    @Override
    public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(Date businessDate, CalculationType type) {
        try {
            Query query = manager.createQuery(
                    "From UnitExpenditureDetail where year = :year AND month = :month AND day = :day AND calculation = :calculation order by unitId");
            Calendar c = AppUtils.getCalendar(businessDate);
            query.setParameter("year", c.get(Calendar.YEAR));
            query.setParameter("month", c.get(Calendar.MONTH) + 1);
            query.setParameter("day", c.get(Calendar.DATE));
            query.setParameter("calculation", type.name());
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("ERROR while getting UnitExpenditureDetail", e);
        }
        return new ArrayList<>();

    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.data.dao.UnitBudgetDao#getUnitExpenditureDetail(int,
     * java.util.Date, com.stpl.tech.kettle.core.CalculationType)
     */
    @Override
    public List<UnitExpenditureDetail> getUnitExpenditureDetail(int unitId, Date businessDate,
                                                                CalculationType calculation) {
        try {
            Query query = manager.createQuery(
                    "From UnitExpenditureDetail where unitId= :unitId and businessDate = :businessDate and calculation = :calculation");
            query.setParameter("businessDate", businessDate);
            query.setParameter("unitId", unitId);
            query.setParameter("calculation", calculation.name());
            return query.getResultList();
        } catch (Exception e) {

        }
        return new ArrayList<>();

    }

    @Override
    public List<Integer> getUnitsWithFinalizedEntries(Date businessDate) {
        try {
            Query query = manager.createQuery(
                    "SELECT unitId FROM UnitExpenditureDetail WHERE month =:month AND year = :year AND calculation = :calculation"
                            + " AND status<> :status");
            query.setParameter("month", AppUtils.getMonth(businessDate) - 1);
            query.setParameter("year", AppUtils.getYear(businessDate));
            query.setParameter("calculation", CalculationType.FINALIZED.name());
            query.setParameter("status", CalculationStatus.CANCELLED.name());
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error", e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<Integer> getUnitsWithClosedEntries(Date businessDate) {
        try {
            Query query = manager.createQuery(
                    "SELECT unitId FROM UnitExpenditureDetail WHERE month =:month AND year = :year AND calculation = :calculation"
                            + " AND status<> :status");
            query.setParameter("month", AppUtils.getMonth(businessDate) - 1);
            query.setParameter("year", AppUtils.getYear(businessDate));
            query.setParameter("calculation", CalculationType.CLOSED.name());
            query.setParameter("status", CalculationStatus.CANCELLED.name());
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error", e);
        }
        return new ArrayList<>();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.UnitBudgetDao#getAllUnitExpenditureDetail(
     * int, java.util.Date)
     */
    @Override
    public List<UnitExpenditureDetail> getAllUnitExpenditureDetail(int unitId, Date businessDate) {
        try {
            Query query = manager
                    .createQuery("From UnitExpenditureDetail where unitId= :unitId and businessDate = :businessDate");
            query.setParameter("businessDate", businessDate);
            query.setParameter("unitId", unitId);
            return query.getResultList();
        } catch (Exception e) {

        }
        return new ArrayList<>();

    }

    @Override
    public List<UnitBudgetExceededData> getTodayEntry(UnitBudgetExceededData overflow, Date currentDate) {
        StringBuilder queryStr = new StringBuilder(
                " from UnitBudgetExceededData m where m.unitId = :unitId and m.createdOn > :currentDate and m.notificationType = :notificationType and m.budgetCategory = :budgetCategory");
        Query query = manager.createQuery(queryStr.toString());

        query.setParameter("unitId", overflow.getUnitId());
        query.setParameter("currentDate", currentDate);
        query.setParameter("notificationType", overflow.getNotificationType());
        query.setParameter("budgetCategory", overflow.getBudgetCategory());

        return query.getResultList();
    }

    @Override
    public List<CashCardDetail> getCashCardsForUnitForDay(int unitId, Date businessDate) {
        UnitClosure ucd = posMetadataDao.getUnitsClosure(unitId, businessDate);
        if (ucd != null) {
            StringBuilder queryStr = new StringBuilder(
                    "SELECT c FROM CashCardDetail c, OrderDetail od WHERE c.purchaseOrderId = od.orderId "
                            + " AND od.unitId = :unitId AND od.orderId > :startOrderId AND od.orderId <= :endOrderId"
                            + " AND od.orderStatus <> :orderStatus");

            Query query = manager.createQuery(queryStr.toString());
            query.setParameter("unitId", unitId);
            query.setParameter("startOrderId", ucd.getStartOrderId());
            query.setParameter("endOrderId", ucd.getLastOrderId());
            query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
            return query.getResultList();
        }
        return new ArrayList<>();
    }

    @Override
    public List<UnitExpenditureAggregateDetail> getAllUnitExpenditureAggregateDetail(Integer unitId, Date businessDate){
        try {
            Query query = manager
                .createQuery("From UnitExpenditureAggregateDetail where unitId= :unitId and businessDate = :businessDate");
            query.setParameter("businessDate", businessDate);
            query.setParameter("unitId", unitId);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error caught while fetching Aggregate Details");
            return new ArrayList<>();
        }
    }

    @Override
    public void changePnLAggregateStatus(int detailId, CalculationStatus status){
        try {
            UnitExpenditureAggregateDetail d = manager.find(UnitExpenditureAggregateDetail.class, detailId);
            d.setStatus(status.name());
            manager.flush();
        }
        catch (Exception e){
            LOG.error("Error caught while updating Aggregate Details");
        }
    }

    @Override
    public void cancelAllUnitExpenditureAggregateDetail(List<Integer> unitId, Date businessDate){
        try {
            Query query = manager
                    .createQuery("UPDATE UnitExpenditureAggregateDetail set status=:status where unitId in (:unitId) and day = :day and month= :month and year = :year");
            query.setParameter("day", AppUtils.getDay(businessDate));
            query.setParameter("month", AppUtils.getMonth(businessDate));
            query.setParameter("year", AppUtils.getYear(businessDate));
            query.setParameter("unitId", unitId);
            query.setParameter("status", CalculationStatus.CANCELLED.name());
        } catch (Exception e) {
            LOG.error("Error caught while updating Aggregate Details for businessDate {}",businessDate,e);
        }
    }

    @Override
    public void cancelAllUnitExpenditureDetail(List<Integer> unitId, Date businessDate){
        try {
            Query query = manager
                    .createQuery("UPDATE UnitExpenditureDetail set status=:status where unitId in (:unitId) and day = :day and month= :month and year = :year");
            query.setParameter("day", AppUtils.getDay(businessDate));
            query.setParameter("month", AppUtils.getMonth(businessDate));
            query.setParameter("year", AppUtils.getYear(businessDate));
            query.setParameter("unitId", unitId);
            query.setParameter("status", CalculationStatus.CANCELLED.name());
        } catch (Exception e) {
            LOG.error("Error caught while updating Expenditure Details for businessDate {}",businessDate,e);
        }
    }

}
