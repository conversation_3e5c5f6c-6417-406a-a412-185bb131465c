package com.stpl.tech.kettle.customer.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.jms.JMSException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.ChaayosCashSmsPayload;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.ReferralService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.model.CashData;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.kettle.data.model.ReferralStatus;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.referral.dao.CashManagerDao;
import com.stpl.tech.kettle.referral.dao.ReferralDao;
import com.stpl.tech.kettle.referral.model.AllotChaayosCashRequestBody;
import com.stpl.tech.kettle.referral.model.ReferentInfo;
import com.stpl.tech.kettle.referral.model.ReferralRequest;
import com.stpl.tech.kettle.referral.model.ReferralResponse;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;

@Service
public class ReferralServiceImpl implements ReferralService {

	private static final Logger LOG = LoggerFactory.getLogger(ReferralServiceImpl.class);

	@Autowired
	private ReferralDao referralDao;

	@Autowired
	private CashManagerDao cashManager;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	EnvironmentProperties props;

	@Autowired
	private CashManagerDao cashManagerDao;

	@Autowired
	private CleverTapDataPushService cleverTapDataPushService;

	@Autowired
	private CustomerServiceImpl customerServiceImpl;

	@Autowired
	private LoyaltyDao loyaltyDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ReferralResponse submitReferralByCode(ReferralRequest request, boolean isValidation) {
		String code = request.getSignUpRefCode();
		String contact = request.getContact();
		try {
			// get referrer
			Customer referrer = customerService.getCustomerByRefCode(code);
			if (referrer == null) {
				// no referrer exists
				return new ReferralResponse("Incorrect Referral Code");
			}
			if (referrer.isBlacklisted()) {
				return new ReferralResponse("Referrer is Blacklisted");
			}
			if (referrer.isInternal()) {
				return new ReferralResponse("Referral Program not available for Employees");
			}

			// check if new customer already exists
			Customer newCustomer = customerService.getCustomer(contact);

			// we should add a entry for tree update
			if (isValidation) {
				createReferralMappingData(request, referrer, newCustomer);
				if (!AppUtils.getStatus(referrer.getIsRefSubscriber())) {
					return new ReferralResponse(
							"Incorrect Referral Code, customer not does not have access to referral service");
				}
			}
			if (newCustomer == null) {
				// Create Customer here
				if (!isValidation) {
					LOG.info("ReferralServiceImpl BRAND CHECK " );
					customerService.addCustomerUnchecked(getCustomerObject(request));
				}
			} else {
				// return customer already exists
				return new ReferralResponse("Customer Already Signed Up");
			}
		} catch (DataUpdationException e) {
			LOG.info("Error while adding customer by Referral");
			return new ReferralResponse(e.getMessage());
		}
		return new ReferralResponse("Success!", true);
	}

	private ReferralMappingData createReferralMappingData(ReferralRequest request, Customer referrer,
			Customer newCustomer) throws DataUpdationException {

		// if referral already exists
		ReferralMappingData data = referralDao.getReffralMapping(request.getContact(), referrer.getId());

		if (data != null) {
			LOG.info("Referral Already Exists , {}", JSONSerializer.toJSON(data));
			return data;
		}

		// in case we have new referral
		data = new ReferralMappingData();
		data.setCreationTime(AppUtils.getCurrentTimestamp());
		data.setContactNumber(request.getContact());
		data.setReferentName(request.getName());
		data.setCampaignId(request.getCampaign());
		data.setReferralSource(request.getSource());
		// TODO add these if required
		// data.setReferralSourceCategory(null);
		// data.setReferralSourceURL(null);

		if (referrer != null) {
			data.setReferrerId(referrer.getId());
			data.setReferralCode(referrer.getRefCode());
		}

		// other status are updated while adding customer
		if (newCustomer != null) {
			data.setReferentId(newCustomer.getId());
			data.setReferralStatus(ReferralStatus.EXISTING_CUSTOMER.name());
		} else {
			data.setReferralStatus(ReferralStatus.INITIATED.name());
		}

		// TODO URL Parameters and other metadata to be added
		data.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		return (ReferralMappingData) referralDao.update(data);
	}

	private final Customer getCustomerObject(ReferralRequest request) {
		Customer customer = new Customer();
		customer.setFirstName(request.getName());
		customer.setContactNumber(request.getContact());
		customer.setEmailId(null);
		customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
		customer.setRegistrationUnitId(0);
		customer.setAcquisitionSource("REFERRAL");
		customer.setAcquisitionToken("CHAAYOS_REFERRAL");
		customer.setOrderCount(0);
		customer.setSignUpRefCode(request.getSignUpRefCode());
		customer.setContactNumberVerified(true);
		customer.setRefAcquisitionSource(request.getSource());
		customer.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
		customer.setChaayosCustomer(true);
		return customer;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public synchronized Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> activateCashPackets() {
		return cashManager.activateCashPackets();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Long countExpiringPackets() {
		return cashManager.countExpiringPackets();
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public synchronized void expireCashPackets(int limit) {
		cashManager.expireCashPackets(limit);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void generateRefCodes() {
		customerService.gererateRefCodes();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendReferralSMS(List<String> contactNumbers, String campaign, String source) {
		cashManager.sendRefferalSMS(contactNumbers, campaign, source);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public void notifyRefSuccess(Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> customerMap) {
		cashManager.notifyRefSuccess(customerMap);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ReferentInfo> getCustomerReferents(int customerId) {
		try {
			LOG.info("Enter getCustomerReferents customerId is :::" + customerId);
			List<ReferralMappingData> referralMappings = referralDao.getReferralMappings(customerId);
			if(AppUtils.isNonEmptyList(referralMappings)){
				List<Integer> referentIds = referralMappings.stream()
						.map(ReferralMappingData::getReferentId)
						.filter(Objects::nonNull)
						.collect(Collectors.toList());
				//LOG.info("referentIds:::" + JSONSerializer.toJSON(referentIds));
				List<LoyaltyScore> referentLoyaltyScores = new ArrayList<>();
				if(AppUtils.isNonEmptyList(referentIds)){
					referentLoyaltyScores = customerService.getCustomersLoyaltyScore(referentIds);
				}
				//LOG.info("referentLoyaltyScores :::" + JSONSerializer.toJSON(referentLoyaltyScores));
				return DataConverter.convert(referralMappings, referentLoyaltyScores);
			}
		}catch (Exception ex){
			LOG.error(ex.getMessage());
		}
		return null;
	}


	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
													   Map<String, String> map, Integer orderId) {
		try {
			NotificationPayload load = new NotificationPayload();
			if (Objects.nonNull(customer)) {
				load.setCustomerId(customer.getId());
			}
			load.setContactNumber(customer.getContactNumber());
			load.setOrderId(orderId);
			load.setMessageType(type.name());
			load.setSendWhatsapp(type.isWhatsapp());
			if (Objects.nonNull(customer.getOptWhatsapp())) {
				load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}

			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(map);
			return load;
		} catch (Exception e) {
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
					orderId);
			return null;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean allotChaayosCash(int customerId, int validityInDays, int lagDays, BigDecimal amount,
			Date creationDate, Date expirationDate, String cashMetadataType, String cashTransactionCode,
			boolean sendNotification, boolean publishEvent, String comment)
			throws DataNotFoundException, IOException, JMSException, CardValidationException {
		CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
		Customer customer = customerService.getCustomer(customerId);
		expirationDate = AppUtils.getEndOfDayIST(expirationDate);
		CashPacketData cashPacketData = cashManagerDao.uploadChaayosCashForAllotment(customerId, amount, lagDays,
				creationDate, expirationDate, cashMetadataType, cashTransactionCode);
		if (sendNotification && Objects.nonNull(cashPacketData)) {
				ChaayosCashSmsPayload chaayosCashSmsPayload = new ChaayosCashSmsPayload();
				chaayosCashSmsPayload.setCustomerName(customer.getFirstName());
				chaayosCashSmsPayload.setAmount(cashPacketData.getInitialAmount());
				chaayosCashSmsPayload.setExpiryDate(AppUtils.getSMSTemplateDate(cashPacketData.getExpirationDate()));
				Map<String, String> map = new HashMap<>();
				map.put("amount", cashPacketData.getInitialAmount().toString());
				map.put("name", customer.getFirstName());
				map.put("validTill",
						AppUtils.getDateString(cashPacketData.getExpirationDate(), AppUtils.DATE_FORMAT_STRING));
				SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient
						.getTransactionalClient(masterDataCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID));
				notificationService.sendNotification(CustomerSMSNotificationType.RECEIVED_CHAAYOS_CASH.name(),
						CustomerSMSNotificationType.RECEIVED_CHAAYOS_CASH.getMessage(chaayosCashSmsPayload),
						customer.getContactNumber(), smsWebServiceClient, props.getAutomatedNPSSMS(),
						getNotificationPayload(CustomerSMSNotificationType.RECEIVED_CHAAYOS_CASH, customer, map, -1));

		}
		if (publishEvent && Objects.nonNull(cashPacketData)) {
			BigDecimal walletBalance = customerServiceImpl.getWalletBalance(customerId);
			walletBalance = walletBalance == null ? BigDecimal.ZERO : walletBalance;
			LoyaltyScore loyaltyScore = loyaltyDao.getScore(customerId);
			Integer loyaltyBalance = loyaltyScore == null ? 0: loyaltyScore.getAcquiredPoints();
			CashData cashData = cashManagerDao.getCustomerCashData(customerId);
			BigDecimal chaayosCash = cashData == null ? BigDecimal.ZERO : cashData.getCurrentAmount();

			cleverTapPushResponse = cleverTapDataPushService.publishGenericEvent(customerId,
					CleverTapEvents.RECEIVED_CHAAYOS_CASH, cashMetadataType, cashTransactionCode, creationDate,
					expirationDate, amount, sendNotification, CustomerSMSNotificationType.RECEIVED_CHAAYOS_CASH,
					walletBalance, loyaltyBalance, chaayosCash, comment);
			CleverTapPushResponse cleverTapPushResponsePushUserToCLV = cleverTapDataPushService
					.pushUserToCleverTap(customerId);

			for (EventPushTrack eventPushTrack : cleverTapPushResponse.getEvents()) {
				if (!eventPushTrack.getStatus().equals("success")) {
					return false;
				}
			}
		}

		return true;
	}
	
}
