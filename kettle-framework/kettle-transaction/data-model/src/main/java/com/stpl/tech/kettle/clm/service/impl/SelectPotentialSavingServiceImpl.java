package com.stpl.tech.kettle.clm.service.impl;

import com.stpl.tech.kettle.clm.dao.SelectPotentialSavingDao;
import com.stpl.tech.kettle.clm.service.SelectPotentialSavingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SelectPotentialSavingServiceImpl implements SelectPotentialSavingService {

    @Autowired
    private SelectPotentialSavingDao selectPotentialSavingDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Object[] getPotentialSavingData(Integer customerId, Integer brandId){
        return selectPotentialSavingDao.getPotentialSavingData(customerId, brandId);
    }
}
