/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.core.DeliveryConstants;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.master.domain.model.Unit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DeliveryRouter {

	private static final Logger LOG = LoggerFactory.getLogger(DeliveryRouter.class);
	private static final String DT_EXECUTOR_CHECK = "DTExecutor";

	@Autowired
	private EnvironmentProperties props;

	DeliveryOperationExecutor operationExecutor = null;

	public DeliveryResponse createDeliveryInSystem(OrderInfo orderObject, Map<String, String> partnerIdMap)
			throws CloneNotSupportedException {
		LOG.info("Inside creation Request DeliveryRouter class");
		operationExecutor = getExecutor(partnerIdMap, DeliveryConstants.CREATE_ENDPOINT,orderObject.getOrder().getUnitId());
		DeliveryResponse returnResponse = operationExecutor.executeCreateDelivery(orderObject, props);
		return returnResponse != null ? returnResponse : createErrorResponse();
	}

	public String createMerchantIdInSystem(Unit unit, Map<String, String> partnerIdMap) throws JsonProcessingException {
		return getExecutor(partnerIdMap, DeliveryConstants.REGISTER_MERCHANT,unit.getId()).createMerchantWithPartner(unit, props);
	}

	public DeliveryResponse cancelDelivery(String deliveryTaskId, OrderInfo orderInfo, Map<String, String> partnerIdMap) {
		LOG.info("Inside cancellation request DeliveryRouter class");
		operationExecutor = getExecutor(partnerIdMap, DeliveryConstants.CANCEL_ENDPOINT, orderInfo.getOrder().getUnitId());
		DeliveryResponse returnResponse = operationExecutor.executeCancelDelivery(deliveryTaskId, orderInfo, props);
		return returnResponse != null ? returnResponse : createErrorResponse();
	}

	private DeliveryResponse createErrorResponse() {
		DeliveryResponse error = new DeliveryResponse();
		error.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		error.setFailureCode(HttpStatus.BAD_REQUEST.toString());
		error.setFailureMessage("Invalid request");
		return error;
	}

	@SuppressWarnings("unchecked")
	private DeliveryOperationExecutor getExecutor(Map<String, String> partnerIdMap, String requestType, Integer unitId) {

		String endpoint = partnerIdMap.get(requestType);
		LOG.info("requestType is {} and its value is {}", requestType, endpoint);
		String executorName = partnerIdMap.get(DeliveryConstants.EXECUTOR);
		String token = null;
		String tokenSecret = null;
		if(!executorName.contains(DT_EXECUTOR_CHECK)){
			token = partnerIdMap.get(DeliveryConstants.AUTHORIZATION_TOKEN);
			tokenSecret = partnerIdMap.get(DeliveryConstants.AUTHORIZATION_TOKEN_SECRET);
		}else{
			if(unitId!=null){
				token = partnerIdMap.get(String.valueOf(unitId));
			}
		}

		AuthorizationObject authorization = new AuthorizationObject(token, tokenSecret);
		DeliveryExecutionStrategy strategyObject;
		try {

			Class<? extends DeliveryExecutionStrategy> strategyClass = (Class<? extends DeliveryExecutionStrategy>) Class
					.forName(executorName);

			strategyObject = strategyClass.newInstance();
			strategyObject.setAuthorizationObject(authorization);
			operationExecutor = DeliveryOperationExecutor.getInstance(strategyObject);
			operationExecutor.setEndpoint(endpoint);
			return operationExecutor;

		} catch (InstantiationException e) {
			LOG.error("Failed to instantiate", e);
		} catch (IllegalAccessException e) {
			LOG.info("Illegal Access for this access", e);
		} catch (ClassNotFoundException e) {
			LOG.error("No class found with name {}", executorName, e);
		} catch (CloneNotSupportedException e) {
			LOG.error("Clone not supported for {}", executorName, e);
		}
		return null;
	}

}
