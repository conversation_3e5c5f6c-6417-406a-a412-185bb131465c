/**
 *
 */
package com.stpl.tech.kettle.core.data.vo;

import com.stpl.tech.kettle.data.model.SubscriptionPlan;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class CreateOrderResult {

	private int orderId;
	private List<String> giftCard;
	private boolean generateQRCode;
	private int feedbackId;
	private boolean generateInAppFeedback;
	private SubscriptionPlan subscriptionPlan;

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public List<String> getGiftCard() {
		return giftCard;
	}

	public void setGiftCard(List<String> giftCard) {
		this.giftCard = giftCard;
	}

	public boolean isGenerateQRCode() {
		return generateQRCode;
	}

	public void setGenerateQRCode(boolean generateQRCode) {
		this.generateQRCode = generateQRCode;
	}

	public int getFeedbackId() {
		return feedbackId;
	}

	public void setFeedbackId(int feedbackId) {
		this.feedbackId = feedbackId;
	}

	public boolean isGenerateInAppFeedback() {
		return generateInAppFeedback;
	}

	public void setGenerateInAppFeedback(boolean generateInAppFeedback) {
		this.generateInAppFeedback = generateInAppFeedback;
	}

	public SubscriptionPlan getSubscriptionPlan() {
		return subscriptionPlan;
	}

	public void setSubscriptionPlan(SubscriptionPlan subscriptionPlan) {
		this.subscriptionPlan = subscriptionPlan;
	}
}
