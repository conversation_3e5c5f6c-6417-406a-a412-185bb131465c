package com.stpl.tech.kettle.clevertap.domain.model;

import java.math.BigDecimal;

public class ClevertapOrderItem {

    private Integer quantity;

    private Boolean taxable;

    private Integer productId;

    private Boolean loyalty;

    private BigDecimal discount;

    private BigDecimal tax;

    private Boolean hasVariant;

    private String inventory;

    private String taxCode;

    private Integer lPoint;

    private String productName;

    private BigDecimal promo;

    private Boolean hasAddOn;

    private BigDecimal discountPercentage;

    private BigDecimal total;

    private Boolean hasPaidAddOns;

    private BigDecimal discountCode;

    private BigDecimal price;

    private String supo;

    private String discountReason;

    private BigDecimal totalDiscount;

    private String dimension;

    private BigDecimal paidAmount;

    private String title;

    private String vendor;

    private Integer complimentaryTypeId;
    private String isComplimentary;
    private Integer comboParentId;
    private boolean comboConstituent;
    private String complimentaryReason;

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Boolean getLoyalty() {
        return loyalty;
    }

    public void setLoyalty(Boolean loyalty) {
        this.loyalty = loyalty;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Boolean getHasVariant() {
        return hasVariant;
    }

    public void setHasVariant(Boolean hasVariant) {
        this.hasVariant = hasVariant;
    }

    public String getInventory() {
        return inventory;
    }

    public void setInventory(String inventory) {
        this.inventory = inventory;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getPromo() {
        return promo;
    }

    public void setPromo(BigDecimal promo) {
        this.promo = promo;
    }

    public BigDecimal getDiscountPercentage() {
        return discountPercentage;
    }

    public void setDiscountPercentage(BigDecimal discountPercentage) {
        this.discountPercentage = discountPercentage;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getDiscountCode() {
        return discountCode;
    }

    public void setDiscountCode(BigDecimal discountCode) {
        this.discountCode = discountCode;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getSupo() {
        return supo;
    }

    public void setSupo(String supo) {
        this.supo = supo;
    }

    public String getDiscountReason() {
        return discountReason;
    }

    public void setDiscountReason(String discountReason) {
        this.discountReason = discountReason;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public Integer getlPoint() {
        return lPoint;
    }

    public void setlPoint(Integer lPoint) {
        this.lPoint = lPoint;
    }

    public Boolean getHasAddOn() {
        return hasAddOn;
    }

    public void setHasAddOn(Boolean hasAddOn) {
        this.hasAddOn = hasAddOn;
    }

    public Boolean getHasPaidAddOns() {
        return hasPaidAddOns;
    }

    public void setHasPaidAddOns(Boolean hasPaidAddOns) {
        this.hasPaidAddOns = hasPaidAddOns;
    }

    public Integer getComplimentaryTypeId() {
        return complimentaryTypeId;
    }

    public void setComplimentaryTypeId(Integer complimentaryTypeId) {
        this.complimentaryTypeId = complimentaryTypeId;
    }

    public String getIsComplimentary() {
        return isComplimentary;
    }

    public void setIsComplimentary(String isComplimentary) {
        this.isComplimentary = isComplimentary;
    }

    public Integer getComboParentId() {
        return comboParentId;
    }

    public void setComboParentId(Integer comboParentId) {
        this.comboParentId = comboParentId;
    }

    public boolean isComboConstituent() {
        return comboConstituent;
    }

    public void setComboConstituent(boolean comboConstituent) {
        this.comboConstituent = comboConstituent;
    }

    public String getComplimentaryReason() {
        return complimentaryReason;
    }

    public void setComplimentaryReason(String complimentaryReason) {
        this.complimentaryReason = complimentaryReason;
    }

    @Override
    public String toString() {
        return "ClevertapOrderItem{" +
            "quantity=" + quantity +
            ", taxable=" + taxable +
            ", productId=" + productId +
            ", loyalty=" + loyalty +
            ", discount=" + discount +
            ", tax=" + tax +
            ", hasVariant=" + hasVariant +
            ", inventory='" + inventory + '\'' +
            ", taxCode='" + taxCode + '\'' +
            ", lPoint=" + lPoint +
            ", productName='" + productName + '\'' +
            ", promo=" + promo +
            ", hasAddOn=" + hasAddOn +
            ", discountPercentage=" + discountPercentage +
            ", total=" + total +
            ", hasPaidAddOns=" + hasPaidAddOns +
            ", discountCode=" + discountCode +
            ", price=" + price +
            ", supo='" + supo + '\'' +
            ", discountReason='" + discountReason + '\'' +
            ", totalDiscount=" + totalDiscount +
            ", dimension='" + dimension + '\'' +
            ", paidAmount=" + paidAmount +
            ", title='" + title + '\'' +
            ", vendor='" + vendor + '\'' +
            '}';
    }
}
