package com.stpl.tech.kettle.customer.service;

import com.stpl.tech.master.util.CustomerEventType;

import java.util.ArrayList;
import java.util.List;

public interface CustomerEventService {
    public boolean addCustomerEventMonth(String contactNumber, Integer eventMonth, Integer eventDate, String acquisitionSource, CustomerEventType customerEventType, String forceUpdate);

    public List<Integer> getCustomerEventOfferIds(CustomerEventType customerEventType);

    }
