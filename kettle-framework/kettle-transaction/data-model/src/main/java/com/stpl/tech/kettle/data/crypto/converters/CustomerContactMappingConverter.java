package com.stpl.tech.kettle.data.crypto.converters;

import javax.persistence.AttributeConverter;

import com.stpl.tech.spring.crypto.DataEncrypter;
import com.stpl.tech.spring.crypto.DataEncrypterFailureHandler;
import com.stpl.tech.spring.service.util.SpringBeanProvider;
import com.stpl.tech.util.AppConstants;

public class CustomerContactMappingConverter implements AttributeConverter<String, String> {

	@Override
	public String convertToDatabaseColumn(String attribute) {
		DataEncrypter dataEncrypter = SpringBeanProvider.getBean("dataEncrypter", DataEncrypter.class);
		return dataEncrypter.encryptData(attribute);
	}

	@Override
	public String convertToEntityAttribute(String dbData) {
		DataEncrypter dataEncrypter = SpringBeanProvider.getBean("dataEncrypter", DataEncrypter.class);
		DataEncrypterFailureHandler dataEncrypterFailureHandler = SpringBeanProvider
				.getBean("dataEncrypterFailureHandler", DataEncrypterFailureHandler.class);
		return dataEncrypter.decryptData(dbData, dataEncrypterFailureHandler, AppConstants.CONACT_NUMBER_DATA, dbData,
				AppConstants.CONACT_NUMBER, AppConstants.CUSTOMER_CONTACT_INFO_MAPPING);
	}
}
