package com.stpl.tech.kettle.clevertap.data.model;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Created by Chaayos on 21-08-2021.
 */
@Entity
@Table(name = "EVENT_PUSH_TRACK")
public class EventPushTrack {

    private Integer id;
    private String eventName;

    private Integer orderId;
    private String status;
    private String updateType;
    private Date updatedAt;
    private String trackerType;
    private Date publishTime;
    private Date processStartTime;
    private Date processEndTime;
    private Long totalProcessSec;
    private Long clevertapResponseTime;

    public EventPushTrack() {
    }

    public EventPushTrack(String eventName, Integer orderId, String status, String updateType, String trackerType) {
        this.orderId = orderId;
        this.eventName = eventName;
        this.status = status;
        this.updateType = updateType;
        this.updatedAt = AppUtils.getCurrentTimestamp();
        this.trackerType = trackerType;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Column(name = "UPDATE_TYPE", nullable = false)
    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    @Column(name = "EVENT_NAME", nullable = false)
    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    @Column(name = "ORDER_ID", nullable = false)
    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Column(name = "UPDATED_AT", nullable = false)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "TRACKER_TYPE", nullable = false)
    public String getTrackerType() {
        return trackerType;
    }

    public void setTrackerType(String trackerType) {
        this.trackerType = trackerType;
    }

    @Column(name = "PUBLISH_TIME",nullable = true)
    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    @Column(name = "PROCESS_START_TIME",nullable = true)
    public Date getProcessStartTime() {
        return processStartTime;
    }

    public void setProcessStartTime(Date processStartTime) {
        this.processStartTime = processStartTime;
    }

    @Column(name = "PROCESS_END_TIME",nullable = true)
    public Date getProcessEndTime() {
        return processEndTime;
    }

    public void setProcessEndTime(Date processEndTime) {
        this.processEndTime = processEndTime;
    }

    @Column(name = "TOTAL_PROCESS_TIME",nullable = true)
    public Long getTotalProcessSec() {
        return totalProcessSec;
    }

    public void setTotalProcessSec(Long totalProcessSec) {
        this.totalProcessSec = totalProcessSec;
    }

    @Column(name = "CLEVERTAP_RESPONSE_TIME",nullable = true)
    public Long getClevertapResponseTime() {
        return clevertapResponseTime;
    }

    public void setClevertapResponseTime(Long clevertapResponseTime) {
        this.clevertapResponseTime = clevertapResponseTime;
    }
}
