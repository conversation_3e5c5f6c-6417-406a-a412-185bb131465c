/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.util.AppUtils;

import org.apache.poi.ss.formula.eval.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FlatItemStrategy extends AbstractItemStrategy implements OfferActionStrategy {
	private static final Logger LOG = LoggerFactory.getLogger(FlatItemStrategy.class);

	StringBuffer offerMessage = new StringBuffer();
	String messageTemplate = "Applied Flat discount of Rs.%d on %s <br/>";

	@Override
	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems) throws OfferValidationException {
		return applyDiscountStrategy(coupon, order, cache);
	}

	@Override
	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache,
			Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException {
		if (offerValue == null) {
			return applyStrategy(order, coupon, cache, foundItems);
		} else {
			throw new NotImplementedException("Not Implemented the method for Flat Item Strategy");
		}
	}

	@Override
	public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {

		LOG.info("addDiscountDetails of flat item strategy");

		BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
		int discountQuantity = counter < orderItem.getQuantity()
				? counter
				: orderItem.getQuantity();
		BigDecimal valueDiscount = discountInOffer.multiply(BigDecimal.valueOf(discountQuantity));
		if (valueDiscount.compareTo(totalAmount) > 0) {
			valueDiscount = totalAmount;
		}
		BigDecimal percentageDiscount = AppUtils.percentage(valueDiscount, totalAmount);
		if (Objects.nonNull(offer.getLoyaltyBurnPoints()) && offer.getLoyaltyBurnPoints() > 0) {
			orderItem.setLoyaltyBurned(true);
			orderItem.setLoyaltyBurnPoints(discountQuantity * offer.getLoyaltyBurnPoints());
		}

		offerMessage.append(String.format(messageTemplate, discountInOffer.intValueExact(), orderItem.getProductName()));
		return getModifiedItem(orderItem, percentageDiscount, valueDiscount);

	}

	@Override
	public String getOfferMessage() {
		return offerMessage.toString();
	}

	@Override
	public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
			BigDecimal paidAmount, BigDecimal maxDiscountValue){
		return null;
	}

}
