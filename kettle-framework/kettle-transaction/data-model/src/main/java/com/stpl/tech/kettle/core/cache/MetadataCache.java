/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.util.AppConstants;

@Repository
public class MetadataCache {
	private static final Logger LOG = LoggerFactory.getLogger(MetadataCache.class);

	@Autowired
	private PosMetadataService metadataDao;

	private final Map<ListTypes, Map<Integer, IdCodeName>> listData = new HashMap<ListTypes, Map<Integer, IdCodeName>>();
	private final Map<Integer, ListData> listCategoryData = new TreeMap<Integer, ListData>();
	private List<IdCodeName> creditAccounts = new ArrayList<IdCodeName>();

	public MetadataCache() {

	}

	@PostConstruct
	public void loadCache() throws DataNotFoundException {
		LOG.info("POST-CONSTRUCT MetadataCache - STARTED");
		Stopwatch watch1 = Stopwatch.createUnstarted();
    	watch1.start();
    	Stopwatch watch = Stopwatch.createUnstarted();
    	watch.start();
    	
		for (ListTypes type : ListTypes.values()) {
			updateListData(type);
		}
		LOG.info("Inside POSTCONSTRUCT - MetadataCache updateListData : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
    	watch.reset();
    	watch.start();
		refreshCreditAccounts();
		LOG.info("Inside POSTCONSTRUCT - MetadataCache refreshCreditAccounts : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
    	watch.reset();
    	watch.start();
		LOG.info("Inside POSTCONSTRUCT - MetadataCache OVERALL : took {} ms", watch1.stop().elapsed(TimeUnit.MILLISECONDS));

	}


	public IdCodeName getDeliveryPartner(int id) {
		return listData.get(ListTypes.DELIVERY_PARTNERS).get(id);
	}

	public Collection<IdCodeName> getAllDeliveryPartners() {
		return listData.get(ListTypes.DELIVERY_PARTNERS).values();
	}

	public IdCodeName getComplimentaryCode(int id) {
		return listData.get(ListTypes.COMPLIMENTARY_CODES).get(id);
	}

	public Collection<IdCodeName> getAllComplimentaryCodes() {
		return listData.get(ListTypes.COMPLIMENTARY_CODES).values();
	}

	public ListData getProductCategory(int id) {
		return listCategoryData.get(id);
	}

	public Collection<ListData> getAllProductCategories() {
		return listCategoryData.values();
	}



	public void updateListData(ListTypes type) throws DataNotFoundException {

		switch (type) {
		case DELIVERY_PARTNERS:
			listData.put(ListTypes.DELIVERY_PARTNERS, new TreeMap<Integer, IdCodeName>());
			for (IdCodeName data : metadataDao.getAllDeliveryPartner()) {
				listData.get(ListTypes.DELIVERY_PARTNERS).put(data.getId(), data);
			}
			break;
		case COMPLIMENTARY_CODES:
			listData.put(ListTypes.COMPLIMENTARY_CODES, new TreeMap<Integer, IdCodeName>());
			for (IdCodeName data : metadataDao.getComplimentaryCodes(false).getContent()) {
				listData.get(ListTypes.COMPLIMENTARY_CODES).put(data.getId(), data);
			}
			break;
		default:
			break;
		}
	}



	public List<IdCodeName> getCreditAccounts() {
		return creditAccounts;
	}

	public void refreshCreditAccounts() {
		creditAccounts = new ArrayList<>();
		creditAccounts.addAll(metadataDao.getAllCreditAccounts(AppConstants.ACTIVE).stream().map(DataConverter::convert)
				.collect(Collectors.toList()));
	}

	@Override
	public String toString() {
		return "MetadataCache{" +
				"listData=" + listData.size() +
				", listCategoryData=" + listCategoryData.size() +
				", creditAccounts=" + creditAccounts.size() +
				'}';
	}

	public static void main(String[] args) {
		MetadataCache cache = new MetadataCache();
		System.out.println(cache);
	}
}
