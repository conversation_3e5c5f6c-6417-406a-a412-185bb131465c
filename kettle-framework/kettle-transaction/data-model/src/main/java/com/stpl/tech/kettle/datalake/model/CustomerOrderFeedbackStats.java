package com.stpl.tech.kettle.datalake.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CUSTOMER_ONE_VIEW_ORDER_FEEDBACK_STATS")
public class CustomerOrderFeedbackStats {

	private Date businessDate;
	private Integer customerId; // @Id

	private String isLatest;
	private String isLastFeedbackOfLastOrder;
	private String isLastCafeFeedbackOfLastOrder;
	private String isLastDeliveryFeedbackOfLastOrder;

	private Integer lastOrderSurveyId;
	private Integer secondLastOrderSurveyId;
	private Integer thirdLastOrderSurveyId;

	private Integer lastOrderScore;
	private Integer secondLastOrderScore;
	private Integer thirdLastOrderScore;

	private Date lastOrderScoreDate;
	private Date secondLastOrderScoreDate;
	private Date thirdLastOrderScoreDate;

	private Date lastOrderScoreOrderDate;
	private Date secondLastOrderScoreOrderDate;
	private Date thirdLastOrderScoreOrderDate;

	private Integer lastOrderScoreTimeGap;
	private Integer secondLastOrderScoreTimeGap;
	private Integer thirdLastOrderScoreTimeGap;

	private Integer lastOrderScoreOrderId;
	private Integer secondLastOrderScoreOrderId;
	private Integer thirdLastOrderScoreOrderId;

	private String lastOrderScoreOrderSource;
	private String secondLastOrderScoreOrderSource;
	private String ThirdLastOrderScoreOrderSource;

	private String lastOrderScoreCallBackRequested;
	private String secondLastOrderScoreCallBackRequested;
	private String thirdLastOrderScoreCallBackRequested;

	private Integer lastOrderScoreChannelPartner;
	private Integer secondLastOrderScoreChannelPartner;
	private Integer thirdLastOrderScoreChannelPartner;

	private Integer lastOrderScoreUnitId;
	private Integer secondLastOrderScoreUnitId;
	private Integer thirdLastOrderScoreUnitId;

	// cafe
	private Integer cafeLastOrderScore;
	private Integer cafeSecondLastOrderScore;
	private Integer cafeThirdLastOrderScore;

	private Date cafeLastOrderScoreDate;
	private Date cafeSecondLastOrderScoreDate;
	private Date cafeThirdLastOrderScoreDate;

	private Integer cafeLastOrderScoreOrderId;
	private Integer cafeSecondLastOrderScoreOrderId;
	private Integer cafeThirdLastOrderScoreOrderId;

	private String cafeLastOrderScoreOrderSource;
	private String cafeSecondLastOrderScoreOrderSource;
	private String cafeThirdLastOrderScoreOrderSource;

	private String cafeLastOrderScoreCallBackRequested;
	private String cafeSecondLastOrderScoreCallBackRequested;
	private String cafeThirdLastOrderScoreCallBackRequested;

	private Integer cafeLastOrderScoreChannelPartner;
	private Integer cafeSecondLastOrderScoreChannelPartner;
	private Integer cafeThirdLastOrderScoreChannelPartner;

	private Integer cafeLastOrderScoreUnitId;
	private Integer cafeSecondLastOrderScoreUnitId;
	private Integer cafeThirdLastOrderScoreUnitId;

	// delivery
	private Integer deliveryLastOrderScore;
	private Integer deliverySecondLastOrderScore;
	private Integer deliveryThirdLastOrderScore;

	private Date deliveryLastOrderScoreDate;
	private Date deliverySecondLastOrderScoreDate;
	private Date deliveryThirdLastOrderScoreDate;

	private Integer deliveryLastOrderScoreOrderId;
	private Integer deliverySecondLastOrderScoreOrderId;
	private Integer deliveryThirdLastOrderScoreOrderId;

	private String deliveryLastOrderScoreOrderSource;
	private String deliverySecondLastOrderScoreOrderSource;
	private String deliveryThirdLastOrderScoreOrderSource;

	private String deliveryLastOrderScoreCallBackRequested;
	private String deliverySecondLastOrderScoreCallBackRequested;
	private String deliveryThirdLastOrderScoreCallBackRequested;

	private Integer deliveryLastOrderScoreChannelPartner;
	private Integer deliverySecondLastOrderScoreChannelPartner;
	private Integer deliveryThirdLastOrderScoreChannelPartner;

	private Integer deliveryLastOrderScoreUnitId;
	private Integer deliverySecondLastOrderScoreUnitId;
	private Integer deliveryThirdLastOrderScoreUnitId;

	private Integer lastFeedbackAmbienceScore;
	private Integer lastFeedbackFoodScore;
	private Integer lastFeedbackBeverageScore;
	private Integer lastFeedbackServiceScore;
	private Integer lastFeedbackDeliveryScore;

	private Integer secondLastFeedbackAmbienceScore;
	private Integer secondLastFeedbackFoodScore;
	private Integer secondLastFeedbackBeverageScore;
	private Integer secondLastFeedbackServiceScore;
	private Integer secondLastFeedbackDeliveryScore;

	private Integer thirdLastFeedbackAmbienceScore;
	private Integer thirdLastFeedbackFoodScore;
	private Integer thirdLastFeedbackBeverageScore;
	private Integer thirdLastFeedbackServiceScore;
	private Integer thirdLastFeedbackDeliveryScore;

	//
	private Integer totalOrderFeedback;
	private Integer totalCafeOrder;
	private Integer totalDeliveryOrder;

	private BigDecimal avgOrderFeeback;
	private BigDecimal avgCafeOrderFeedback;
	private BigDecimal avgDeliveryOrderFeedback;

	private Integer totalOrderFeedbackInLast180Days;
	private Integer totalCafeOrderInLast180Days;
	private Integer totalDeliveryOrderInLast180Days;

	private BigDecimal avgOrderFeebackInLast180Days;
	private BigDecimal avgCafeOrderFeedbackInLast180Days;
	private BigDecimal avgDeliveryOrderFeedbackInLast180Days;

	@Column(name = "BUSINESS_DATE")
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Id
	@Column(name = "CUSTOMER_ID")
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "IS_LATEST")
	public String getIsLatest() {
		return isLatest;
	}

	public void setIsLatest(String isLatest) {
		this.isLatest = isLatest;
	}

	@Column(name = "IS_LAST_FEEDBACK_OF_LAST_ORDER")
	public String getIsLastFeedbackOfLastOrder() {
		return isLastFeedbackOfLastOrder;
	}

	public void setIsLastFeedbackOfLastOrder(String isLastFeedbackOfLastOrder) {
		this.isLastFeedbackOfLastOrder = isLastFeedbackOfLastOrder;
	}

	@Column(name = "IS_LAST_CAFE_FEEDBACK_OF_LAST_CAFE_ORDER")
	public String getIsLastCafeFeedbackOfLastOrder() {
		return isLastCafeFeedbackOfLastOrder;
	}

	public void setIsLastCafeFeedbackOfLastOrder(String isLastCafeFeedbackOfLastOrder) {
		this.isLastCafeFeedbackOfLastOrder = isLastCafeFeedbackOfLastOrder;
	}

	@Column(name = "IS_LAST_DELIVERY_FEEDBACK_OF_LAST_DELIVERY_ORDER")
	public String getIsLastDeliveryFeedbackOfLastOrder() {
		return isLastDeliveryFeedbackOfLastOrder;
	}

	public void setIsLastDeliveryFeedbackOfLastOrder(String isLastDeliveryFeedbackOfLastOrder) {
		this.isLastDeliveryFeedbackOfLastOrder = isLastDeliveryFeedbackOfLastOrder;
	}

	@Column(name = "LAST_ORDER_SURVEY_ID")
	public Integer getLastOrderSurveyId() {
		return lastOrderSurveyId;
	}

	public void setLastOrderSurveyId(Integer lastOrderSurveyId) {
		this.lastOrderSurveyId = lastOrderSurveyId;
	}

	@Column(name = "SECOND_LAST_ORDER_SURVEY_ID")
	public Integer getSecondLastOrderSurveyId() {
		return secondLastOrderSurveyId;
	}

	public void setSecondLastOrderSurveyId(Integer secondLastOrderSurveyId) {
		this.secondLastOrderSurveyId = secondLastOrderSurveyId;
	}

	@Column(name = "THIRD_LAST_ORDER_SURVEY_ID")
	public Integer getThirdLastOrderSurveyId() {
		return thirdLastOrderSurveyId;
	}

	public void setThirdLastOrderSurveyId(Integer thirdLastOrderSurveyId) {
		this.thirdLastOrderSurveyId = thirdLastOrderSurveyId;
	}

	@Column(name = "LAST_ORDER_SCORE")
	public Integer getLastOrderScore() {
		return lastOrderScore;
	}

	public void setLastOrderScore(Integer lastOrderScore) {
		this.lastOrderScore = lastOrderScore;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE")
	public Integer getSecondLastOrderScore() {
		return secondLastOrderScore;
	}

	public void setSecondLastOrderScore(Integer secondLastOrderScore) {
		this.secondLastOrderScore = secondLastOrderScore;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE")
	public Integer getThirdLastOrderScore() {
		return thirdLastOrderScore;
	}

	public void setThirdLastOrderScore(Integer thirdLastOrderScore) {
		this.thirdLastOrderScore = thirdLastOrderScore;
	}

	@Column(name = "LAST_ORDER_SCORE_DATE")
	public Date getLastOrderScoreDate() {
		return lastOrderScoreDate;
	}

	public void setLastOrderScoreDate(Date lastOrderScoreDate) {
		this.lastOrderScoreDate = lastOrderScoreDate;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_DATE")
	public Date getSecondLastOrderScoreDate() {
		return secondLastOrderScoreDate;
	}

	public void setSecondLastOrderScoreDate(Date secondLastOrderScoreDate) {
		this.secondLastOrderScoreDate = secondLastOrderScoreDate;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_DATE")
	public Date getThirdLastOrderScoreDate() {
		return thirdLastOrderScoreDate;
	}

	public void setThirdLastOrderScoreDate(Date thirdLastOrderScoreDate) {
		this.thirdLastOrderScoreDate = thirdLastOrderScoreDate;
	}

	@Column(name = "LAST_ORDER_SCORE_ORDER_DATE")
	public Date getLastOrderScoreOrderDate() {
		return lastOrderScoreOrderDate;
	}

	public void setLastOrderScoreOrderDate(Date lastOrderScoreOrderDate) {
		this.lastOrderScoreOrderDate = lastOrderScoreOrderDate;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_ORDER_DATE")
	public Date getSecondLastOrderScoreOrderDate() {
		return secondLastOrderScoreOrderDate;
	}

	public void setSecondLastOrderScoreOrderDate(Date secondLastOrderScoreOrderDate) {
		this.secondLastOrderScoreOrderDate = secondLastOrderScoreOrderDate;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_ORDER_DATE")
	public Date getThirdLastOrderScoreOrderDate() {
		return thirdLastOrderScoreOrderDate;
	}

	public void setThirdLastOrderScoreOrderDate(Date thirdLastOrderScoreOrderDate) {
		this.thirdLastOrderScoreOrderDate = thirdLastOrderScoreOrderDate;
	}

	@Column(name = "LAST_ORDER_SCORE_TIME_GAP")
	public Integer getLastOrderScoreTimeGap() {
		return lastOrderScoreTimeGap;
	}

	public void setLastOrderScoreTimeGap(Integer lastOrderScoreTimeGap) {
		this.lastOrderScoreTimeGap = lastOrderScoreTimeGap;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_TIME_GAP")
	public Integer getSecondLastOrderScoreTimeGap() {
		return secondLastOrderScoreTimeGap;
	}

	public void setSecondLastOrderScoreTimeGap(Integer secondLastOrderScoreTimeGap) {
		this.secondLastOrderScoreTimeGap = secondLastOrderScoreTimeGap;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_TIME_GAP")
	public Integer getThirdLastOrderScoreTimeGap() {
		return thirdLastOrderScoreTimeGap;
	}

	public void setThirdLastOrderScoreTimeGap(Integer thirdLastOrderScoreTimeGap) {
		this.thirdLastOrderScoreTimeGap = thirdLastOrderScoreTimeGap;
	}

	@Column(name = "LAST_ORDER_SCORE_ORDER_ID")
	public Integer getLastOrderScoreOrderId() {
		return lastOrderScoreOrderId;
	}

	public void setLastOrderScoreOrderId(Integer lastOrderScoreOrderId) {
		this.lastOrderScoreOrderId = lastOrderScoreOrderId;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getSecondLastOrderScoreOrderId() {
		return secondLastOrderScoreOrderId;
	}

	public void setSecondLastOrderScoreOrderId(Integer secondLastOrderScoreOrderId) {
		this.secondLastOrderScoreOrderId = secondLastOrderScoreOrderId;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getThirdLastOrderScoreOrderId() {
		return thirdLastOrderScoreOrderId;
	}

	public void setThirdLastOrderScoreOrderId(Integer thirdLastOrderScoreOrderId) {
		this.thirdLastOrderScoreOrderId = thirdLastOrderScoreOrderId;
	}

	@Column(name = "LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getLastOrderScoreOrderSource() {
		return lastOrderScoreOrderSource;
	}

	public void setLastOrderScoreOrderSource(String lastOrderScoreOrderSource) {
		this.lastOrderScoreOrderSource = lastOrderScoreOrderSource;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getSecondLastOrderScoreOrderSource() {
		return secondLastOrderScoreOrderSource;
	}

	public void setSecondLastOrderScoreOrderSource(String secondLastOrderScoreOrderSource) {
		this.secondLastOrderScoreOrderSource = secondLastOrderScoreOrderSource;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getThirdLastOrderScoreOrderSource() {
		return ThirdLastOrderScoreOrderSource;
	}

	public void setThirdLastOrderScoreOrderSource(String thirdLastOrderScoreOrderSource) {
		ThirdLastOrderScoreOrderSource = thirdLastOrderScoreOrderSource;
	}

	@Column(name = "LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getLastOrderScoreCallBackRequested() {
		return lastOrderScoreCallBackRequested;
	}

	public void setLastOrderScoreCallBackRequested(String lastOrderScoreCallBackRequested) {
		this.lastOrderScoreCallBackRequested = lastOrderScoreCallBackRequested;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getSecondLastOrderScoreCallBackRequested() {
		return secondLastOrderScoreCallBackRequested;
	}

	public void setSecondLastOrderScoreCallBackRequested(String secondLastOrderScoreCallBackRequested) {
		this.secondLastOrderScoreCallBackRequested = secondLastOrderScoreCallBackRequested;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getThirdLastOrderScoreCallBackRequested() {
		return thirdLastOrderScoreCallBackRequested;
	}

	public void setThirdLastOrderScoreCallBackRequested(String thirdLastOrderScoreCallBackRequested) {
		this.thirdLastOrderScoreCallBackRequested = thirdLastOrderScoreCallBackRequested;
	}

	@Column(name = "LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getLastOrderScoreChannelPartner() {
		return lastOrderScoreChannelPartner;
	}

	public void setLastOrderScoreChannelPartner(Integer lastOrderScoreChannelPartner) {
		this.lastOrderScoreChannelPartner = lastOrderScoreChannelPartner;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getSecondLastOrderScoreChannelPartner() {
		return secondLastOrderScoreChannelPartner;
	}

	public void setSecondLastOrderScoreChannelPartner(Integer secondLastOrderScoreChannelPartner) {
		this.secondLastOrderScoreChannelPartner = secondLastOrderScoreChannelPartner;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getThirdLastOrderScoreChannelPartner() {
		return thirdLastOrderScoreChannelPartner;
	}

	public void setThirdLastOrderScoreChannelPartner(Integer thirdLastOrderScoreChannelPartner) {
		this.thirdLastOrderScoreChannelPartner = thirdLastOrderScoreChannelPartner;
	}

	@Column(name = "LAST_ORDER_SCORE_UNIT_ID")
	public Integer getLastOrderScoreUnitId() {
		return lastOrderScoreUnitId;
	}

	public void setLastOrderScoreUnitId(Integer lastOrderScoreUnitId) {
		this.lastOrderScoreUnitId = lastOrderScoreUnitId;
	}

	@Column(name = "SECOND_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getSecondLastOrderScoreUnitId() {
		return secondLastOrderScoreUnitId;
	}

	public void setSecondLastOrderScoreUnitId(Integer secondLastOrderScoreUnitId) {
		this.secondLastOrderScoreUnitId = secondLastOrderScoreUnitId;
	}

	@Column(name = "THIRD_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getThirdLastOrderScoreUnitId() {
		return thirdLastOrderScoreUnitId;
	}

	public void setThirdLastOrderScoreUnitId(Integer thirdLastOrderScoreUnitId) {
		this.thirdLastOrderScoreUnitId = thirdLastOrderScoreUnitId;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE")
	public Integer getCafeLastOrderScore() {
		return cafeLastOrderScore;
	}

	public void setCafeLastOrderScore(Integer cafeLastOrderScore) {
		this.cafeLastOrderScore = cafeLastOrderScore;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE")
	public Integer getCafeSecondLastOrderScore() {
		return cafeSecondLastOrderScore;
	}

	public void setCafeSecondLastOrderScore(Integer cafeSecondLastOrderScore) {
		this.cafeSecondLastOrderScore = cafeSecondLastOrderScore;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE")
	public Integer getCafeThirdLastOrderScore() {
		return cafeThirdLastOrderScore;
	}

	public void setCafeThirdLastOrderScore(Integer cafeThirdLastOrderScore) {
		this.cafeThirdLastOrderScore = cafeThirdLastOrderScore;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE_DATE")
	public Date getCafeLastOrderScoreDate() {
		return cafeLastOrderScoreDate;
	}

	public void setCafeLastOrderScoreDate(Date cafeLastOrderScoreDate) {
		this.cafeLastOrderScoreDate = cafeLastOrderScoreDate;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE_DATE")
	public Date getCafeSecondLastOrderScoreDate() {
		return cafeSecondLastOrderScoreDate;
	}

	public void setCafeSecondLastOrderScoreDate(Date cafeSecondLastOrderScoreDate) {
		this.cafeSecondLastOrderScoreDate = cafeSecondLastOrderScoreDate;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE_DATE")
	public Date getCafeThirdLastOrderScoreDate() {
		return cafeThirdLastOrderScoreDate;
	}

	public void setCafeThirdLastOrderScoreDate(Date cafeThirdLastOrderScoreDate) {
		this.cafeThirdLastOrderScoreDate = cafeThirdLastOrderScoreDate;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getCafeLastOrderScoreOrderId() {
		return cafeLastOrderScoreOrderId;
	}

	public void setCafeLastOrderScoreOrderId(Integer cafeLastOrderScoreOrderId) {
		this.cafeLastOrderScoreOrderId = cafeLastOrderScoreOrderId;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getCafeSecondLastOrderScoreOrderId() {
		return cafeSecondLastOrderScoreOrderId;
	}

	public void setCafeSecondLastOrderScoreOrderId(Integer cafeSecondLastOrderScoreOrderId) {
		this.cafeSecondLastOrderScoreOrderId = cafeSecondLastOrderScoreOrderId;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getCafeThirdLastOrderScoreOrderId() {
		return cafeThirdLastOrderScoreOrderId;
	}

	public void setCafeThirdLastOrderScoreOrderId(Integer cafeThirdLastOrderScoreOrderId) {
		this.cafeThirdLastOrderScoreOrderId = cafeThirdLastOrderScoreOrderId;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getCafeLastOrderScoreOrderSource() {
		return cafeLastOrderScoreOrderSource;
	}

	public void setCafeLastOrderScoreOrderSource(String cafeLastOrderScoreOrderSource) {
		this.cafeLastOrderScoreOrderSource = cafeLastOrderScoreOrderSource;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getCafeSecondLastOrderScoreOrderSource() {
		return cafeSecondLastOrderScoreOrderSource;
	}

	public void setCafeSecondLastOrderScoreOrderSource(String cafeSecondLastOrderScoreOrderSource) {
		this.cafeSecondLastOrderScoreOrderSource = cafeSecondLastOrderScoreOrderSource;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getCafeThirdLastOrderScoreOrderSource() {
		return cafeThirdLastOrderScoreOrderSource;
	}

	public void setCafeThirdLastOrderScoreOrderSource(String cafeThirdLastOrderScoreOrderSource) {
		this.cafeThirdLastOrderScoreOrderSource = cafeThirdLastOrderScoreOrderSource;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getCafeLastOrderScoreCallBackRequested() {
		return cafeLastOrderScoreCallBackRequested;
	}

	public void setCafeLastOrderScoreCallBackRequested(String cafeLastOrderScoreCallBackRequested) {
		this.cafeLastOrderScoreCallBackRequested = cafeLastOrderScoreCallBackRequested;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getCafeSecondLastOrderScoreCallBackRequested() {
		return cafeSecondLastOrderScoreCallBackRequested;
	}

	public void setCafeSecondLastOrderScoreCallBackRequested(String cafeSecondLastOrderScoreCallBackRequested) {
		this.cafeSecondLastOrderScoreCallBackRequested = cafeSecondLastOrderScoreCallBackRequested;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getCafeThirdLastOrderScoreCallBackRequested() {
		return cafeThirdLastOrderScoreCallBackRequested;
	}

	public void setCafeThirdLastOrderScoreCallBackRequested(String cafeThirdLastOrderScoreCallBackRequested) {
		this.cafeThirdLastOrderScoreCallBackRequested = cafeThirdLastOrderScoreCallBackRequested;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getCafeLastOrderScoreChannelPartner() {
		return cafeLastOrderScoreChannelPartner;
	}

	public void setCafeLastOrderScoreChannelPartner(Integer cafeLastOrderScoreChannelPartner) {
		this.cafeLastOrderScoreChannelPartner = cafeLastOrderScoreChannelPartner;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getCafeSecondLastOrderScoreChannelPartner() {
		return cafeSecondLastOrderScoreChannelPartner;
	}

	public void setCafeSecondLastOrderScoreChannelPartner(Integer cafeSecondLastOrderScoreChannelPartner) {
		this.cafeSecondLastOrderScoreChannelPartner = cafeSecondLastOrderScoreChannelPartner;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getCafeThirdLastOrderScoreChannelPartner() {
		return cafeThirdLastOrderScoreChannelPartner;
	}

	public void setCafeThirdLastOrderScoreChannelPartner(Integer cafeThirdLastOrderScoreChannelPartner) {
		this.cafeThirdLastOrderScoreChannelPartner = cafeThirdLastOrderScoreChannelPartner;
	}

	@Column(name = "CAFE_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getCafeLastOrderScoreUnitId() {
		return cafeLastOrderScoreUnitId;
	}

	public void setCafeLastOrderScoreUnitId(Integer cafeLastOrderScoreUnitId) {
		this.cafeLastOrderScoreUnitId = cafeLastOrderScoreUnitId;
	}

	@Column(name = "CAFE_SECOND_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getCafeSecondLastOrderScoreUnitId() {
		return cafeSecondLastOrderScoreUnitId;
	}

	public void setCafeSecondLastOrderScoreUnitId(Integer cafeSecondLastOrderScoreUnitId) {
		this.cafeSecondLastOrderScoreUnitId = cafeSecondLastOrderScoreUnitId;
	}

	@Column(name = "CAFE_THIRD_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getCafeThirdLastOrderScoreUnitId() {
		return cafeThirdLastOrderScoreUnitId;
	}

	public void setCafeThirdLastOrderScoreUnitId(Integer cafeThirdLastOrderScoreUnitId) {
		this.cafeThirdLastOrderScoreUnitId = cafeThirdLastOrderScoreUnitId;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE")
	public Integer getDeliveryLastOrderScore() {
		return deliveryLastOrderScore;
	}

	public void setDeliveryLastOrderScore(Integer deliveryLastOrderScore) {
		this.deliveryLastOrderScore = deliveryLastOrderScore;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE")
	public Integer getDeliverySecondLastOrderScore() {
		return deliverySecondLastOrderScore;
	}

	public void setDeliverySecondLastOrderScore(Integer deliverySecondLastOrderScore) {
		this.deliverySecondLastOrderScore = deliverySecondLastOrderScore;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE")
	public Integer getDeliveryThirdLastOrderScore() {
		return deliveryThirdLastOrderScore;
	}

	public void setDeliveryThirdLastOrderScore(Integer deliveryThirdLastOrderScore) {
		this.deliveryThirdLastOrderScore = deliveryThirdLastOrderScore;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE_DATE")
	public Date getDeliveryLastOrderScoreDate() {
		return deliveryLastOrderScoreDate;
	}

	public void setDeliveryLastOrderScoreDate(Date deliveryLastOrderScoreDate) {
		this.deliveryLastOrderScoreDate = deliveryLastOrderScoreDate;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE_DATE")
	public Date getDeliverySecondLastOrderScoreDate() {
		return deliverySecondLastOrderScoreDate;
	}

	public void setDeliverySecondLastOrderScoreDate(Date deliverySecondLastOrderScoreDate) {
		this.deliverySecondLastOrderScoreDate = deliverySecondLastOrderScoreDate;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE_DATE")
	public Date getDeliveryThirdLastOrderScoreDate() {
		return deliveryThirdLastOrderScoreDate;
	}

	public void setDeliveryThirdLastOrderScoreDate(Date deliveryThirdLastOrderScoreDate) {
		this.deliveryThirdLastOrderScoreDate = deliveryThirdLastOrderScoreDate;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getDeliveryLastOrderScoreOrderId() {
		return deliveryLastOrderScoreOrderId;
	}

	public void setDeliveryLastOrderScoreOrderId(Integer deliveryLastOrderScoreOrderId) {
		this.deliveryLastOrderScoreOrderId = deliveryLastOrderScoreOrderId;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getDeliverySecondLastOrderScoreOrderId() {
		return deliverySecondLastOrderScoreOrderId;
	}

	public void setDeliverySecondLastOrderScoreOrderId(Integer deliverySecondLastOrderScoreOrderId) {
		this.deliverySecondLastOrderScoreOrderId = deliverySecondLastOrderScoreOrderId;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE_ORDER_ID")
	public Integer getDeliveryThirdLastOrderScoreOrderId() {
		return deliveryThirdLastOrderScoreOrderId;
	}

	public void setDeliveryThirdLastOrderScoreOrderId(Integer deliveryThirdLastOrderScoreOrderId) {
		this.deliveryThirdLastOrderScoreOrderId = deliveryThirdLastOrderScoreOrderId;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getDeliveryLastOrderScoreOrderSource() {
		return deliveryLastOrderScoreOrderSource;
	}

	public void setDeliveryLastOrderScoreOrderSource(String deliveryLastOrderScoreOrderSource) {
		this.deliveryLastOrderScoreOrderSource = deliveryLastOrderScoreOrderSource;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getDeliverySecondLastOrderScoreOrderSource() {
		return deliverySecondLastOrderScoreOrderSource;
	}

	public void setDeliverySecondLastOrderScoreOrderSource(String deliverySecondLastOrderScoreOrderSource) {
		this.deliverySecondLastOrderScoreOrderSource = deliverySecondLastOrderScoreOrderSource;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE_ORDER_SOURCE")
	public String getDeliveryThirdLastOrderScoreOrderSource() {
		return deliveryThirdLastOrderScoreOrderSource;
	}

	public void setDeliveryThirdLastOrderScoreOrderSource(String deliveryThirdLastOrderScoreOrderSource) {
		this.deliveryThirdLastOrderScoreOrderSource = deliveryThirdLastOrderScoreOrderSource;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getDeliveryLastOrderScoreCallBackRequested() {
		return deliveryLastOrderScoreCallBackRequested;
	}

	public void setDeliveryLastOrderScoreCallBackRequested(String deliveryLastOrderScoreCallBackRequested) {
		this.deliveryLastOrderScoreCallBackRequested = deliveryLastOrderScoreCallBackRequested;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getDeliverySecondLastOrderScoreCallBackRequested() {
		return deliverySecondLastOrderScoreCallBackRequested;
	}

	public void setDeliverySecondLastOrderScoreCallBackRequested(String deliverySecondLastOrderScoreCallBackRequested) {
		this.deliverySecondLastOrderScoreCallBackRequested = deliverySecondLastOrderScoreCallBackRequested;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE_CALLBACK_REQUESTED")
	public String getDeliveryThirdLastOrderScoreCallBackRequested() {
		return deliveryThirdLastOrderScoreCallBackRequested;
	}

	public void setDeliveryThirdLastOrderScoreCallBackRequested(String deliveryThirdLastOrderScoreCallBackRequested) {
		this.deliveryThirdLastOrderScoreCallBackRequested = deliveryThirdLastOrderScoreCallBackRequested;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getDeliveryLastOrderScoreChannelPartner() {
		return deliveryLastOrderScoreChannelPartner;
	}

	public void setDeliveryLastOrderScoreChannelPartner(Integer deliveryLastOrderScoreChannelPartner) {
		this.deliveryLastOrderScoreChannelPartner = deliveryLastOrderScoreChannelPartner;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getDeliverySecondLastOrderScoreChannelPartner() {
		return deliverySecondLastOrderScoreChannelPartner;
	}

	public void setDeliverySecondLastOrderScoreChannelPartner(Integer deliverySecondLastOrderScoreChannelPartner) {
		this.deliverySecondLastOrderScoreChannelPartner = deliverySecondLastOrderScoreChannelPartner;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE_CHANNEL_PARTNER")
	public Integer getDeliveryThirdLastOrderScoreChannelPartner() {
		return deliveryThirdLastOrderScoreChannelPartner;
	}

	public void setDeliveryThirdLastOrderScoreChannelPartner(Integer deliveryThirdLastOrderScoreChannelPartner) {
		this.deliveryThirdLastOrderScoreChannelPartner = deliveryThirdLastOrderScoreChannelPartner;
	}

	@Column(name = "DELIVERY_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getDeliveryLastOrderScoreUnitId() {
		return deliveryLastOrderScoreUnitId;
	}

	public void setDeliveryLastOrderScoreUnitId(Integer deliveryLastOrderScoreUnitId) {
		this.deliveryLastOrderScoreUnitId = deliveryLastOrderScoreUnitId;
	}

	@Column(name = "DELIVERY_SECOND_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getDeliverySecondLastOrderScoreUnitId() {
		return deliverySecondLastOrderScoreUnitId;
	}

	public void setDeliverySecondLastOrderScoreUnitId(Integer deliverySecondLastOrderScoreUnitId) {
		this.deliverySecondLastOrderScoreUnitId = deliverySecondLastOrderScoreUnitId;
	}

	@Column(name = "DELIVERY_THIRD_LAST_ORDER_SCORE_UNIT_ID")
	public Integer getDeliveryThirdLastOrderScoreUnitId() {
		return deliveryThirdLastOrderScoreUnitId;
	}

	public void setDeliveryThirdLastOrderScoreUnitId(Integer deliveryThirdLastOrderScoreUnitId) {
		this.deliveryThirdLastOrderScoreUnitId = deliveryThirdLastOrderScoreUnitId;
	}

	@Column(name = "LAST_FEEDBACK_AMBIENCE_SCORE")
	public Integer getLastFeedbackAmbienceScore() {
		return lastFeedbackAmbienceScore;
	}

	public void setLastFeedbackAmbienceScore(Integer lastFeedbackAmbienceScore) {
		this.lastFeedbackAmbienceScore = lastFeedbackAmbienceScore;
	}

	@Column(name = "LAST_FEEDBACK_FOOD_SCORE")
	public Integer getLastFeedbackFoodScore() {
		return lastFeedbackFoodScore;
	}

	public void setLastFeedbackFoodScore(Integer lastFeedbackFoodScore) {
		this.lastFeedbackFoodScore = lastFeedbackFoodScore;
	}

	@Column(name = "LAST_FEEDBACK_BEVERAGE_SCORE")
	public Integer getLastFeedbackBeverageScore() {
		return lastFeedbackBeverageScore;
	}

	public void setLastFeedbackBeverageScore(Integer lastFeedbackBeverageScore) {
		this.lastFeedbackBeverageScore = lastFeedbackBeverageScore;
	}

	@Column(name = "LAST_FEEDBACK_SERVICE_SCORE")
	public Integer getLastFeedbackServiceScore() {
		return lastFeedbackServiceScore;
	}

	public void setLastFeedbackServiceScore(Integer lastFeedbackServiceScore) {
		this.lastFeedbackServiceScore = lastFeedbackServiceScore;
	}

	@Column(name = "LAST_FEEDBACK_DELIVERY_SCORE")
	public Integer getLastFeedbackDeliveryScore() {
		return lastFeedbackDeliveryScore;
	}

	public void setLastFeedbackDeliveryScore(Integer lastFeedbackDeliveryScore) {
		this.lastFeedbackDeliveryScore = lastFeedbackDeliveryScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_AMBIENCE_SCORE")
	public Integer getSecondLastFeedbackAmbienceScore() {
		return secondLastFeedbackAmbienceScore;
	}

	public void setSecondLastFeedbackAmbienceScore(Integer secondLastFeedbackAmbienceScore) {
		this.secondLastFeedbackAmbienceScore = secondLastFeedbackAmbienceScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_FOOD_SCORE")
	public Integer getSecondLastFeedbackFoodScore() {
		return secondLastFeedbackFoodScore;
	}

	public void setSecondLastFeedbackFoodScore(Integer secondLastFeedbackFoodScore) {
		this.secondLastFeedbackFoodScore = secondLastFeedbackFoodScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_BEVERAGE_SCORE")
	public Integer getSecondLastFeedbackBeverageScore() {
		return secondLastFeedbackBeverageScore;
	}

	public void setSecondLastFeedbackBeverageScore(Integer secondLastFeedbackBeverageScore) {
		this.secondLastFeedbackBeverageScore = secondLastFeedbackBeverageScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_SERVICE_SCORE")
	public Integer getSecondLastFeedbackServiceScore() {
		return secondLastFeedbackServiceScore;
	}

	public void setSecondLastFeedbackServiceScore(Integer secondLastFeedbackServiceScore) {
		this.secondLastFeedbackServiceScore = secondLastFeedbackServiceScore;
	}

	@Column(name = "SECOND_LAST_FEEDBACK_DELIVERY_SCORE")
	public Integer getSecondLastFeedbackDeliveryScore() {
		return secondLastFeedbackDeliveryScore;
	}

	public void setSecondLastFeedbackDeliveryScore(Integer secondLastFeedbackDeliveryScore) {
		this.secondLastFeedbackDeliveryScore = secondLastFeedbackDeliveryScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_AMBIENCE_SCORE")
	public Integer getThirdLastFeedbackAmbienceScore() {
		return thirdLastFeedbackAmbienceScore;
	}

	public void setThirdLastFeedbackAmbienceScore(Integer thirdLastFeedbackAmbienceScore) {
		this.thirdLastFeedbackAmbienceScore = thirdLastFeedbackAmbienceScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_FOOD_SCORE")
	public Integer getThirdLastFeedbackFoodScore() {
		return thirdLastFeedbackFoodScore;
	}

	public void setThirdLastFeedbackFoodScore(Integer thirdLastFeedbackFoodScore) {
		this.thirdLastFeedbackFoodScore = thirdLastFeedbackFoodScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_BEVERAGE_SCORE")
	public Integer getThirdLastFeedbackBeverageScore() {
		return thirdLastFeedbackBeverageScore;
	}

	public void setThirdLastFeedbackBeverageScore(Integer thirdLastFeedbackBeverageScore) {
		this.thirdLastFeedbackBeverageScore = thirdLastFeedbackBeverageScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_SERVICE_SCORE")
	public Integer getThirdLastFeedbackServiceScore() {
		return thirdLastFeedbackServiceScore;
	}

	public void setThirdLastFeedbackServiceScore(Integer thirdLastFeedbackServiceScore) {
		this.thirdLastFeedbackServiceScore = thirdLastFeedbackServiceScore;
	}

	@Column(name = "THIRD_LAST_FEEDBACK_DELIVERY_SCORE")
	public Integer getThirdLastFeedbackDeliveryScore() {
		return thirdLastFeedbackDeliveryScore;
	}

	public void setThirdLastFeedbackDeliveryScore(Integer thirdLastFeedbackDeliveryScore) {
		this.thirdLastFeedbackDeliveryScore = thirdLastFeedbackDeliveryScore;
	}

	@Column(name = "TOTAL_ORDER_FEEDBACK")
	public Integer getTotalOrderFeedback() {
		return totalOrderFeedback;
	}

	public void setTotalOrderFeedback(Integer totalOrderFeedback) {
		this.totalOrderFeedback = totalOrderFeedback;
	}

	@Column(name = "TOTAL_CAFE_ORDER_FEEDBACK")
	public Integer getTotalCafeOrder() {
		return totalCafeOrder;
	}

	public void setTotalCafeOrder(Integer totalCafeOrder) {
		this.totalCafeOrder = totalCafeOrder;
	}

	@Column(name = "TOTAL_DELIVERY_ORDER_FEEDBACK")
	public Integer getTotalDeliveryOrder() {
		return totalDeliveryOrder;
	}

	public void setTotalDeliveryOrder(Integer totalDeliveryOrder) {
		this.totalDeliveryOrder = totalDeliveryOrder;
	}

	@Column(name = "AVG_ORDER_FEEDBACK")
	public BigDecimal getAvgOrderFeeback() {
		return avgOrderFeeback;
	}

	public void setAvgOrderFeeback(BigDecimal avgOrderFeeback) {
		this.avgOrderFeeback = avgOrderFeeback;
	}

	@Column(name = "AVG_CAFE_ORDER_FEEDBACK")
	public BigDecimal getAvgCafeOrderFeedback() {
		return avgCafeOrderFeedback;
	}

	public void setAvgCafeOrderFeedback(BigDecimal avgCafeOrderFeedback) {
		this.avgCafeOrderFeedback = avgCafeOrderFeedback;
	}

	@Column(name = "AVG_DELIVERY_ORDER_FEEDBACK")
	public BigDecimal getAvgDeliveryOrderFeedback() {
		return avgDeliveryOrderFeedback;
	}

	public void setAvgDeliveryOrderFeedback(BigDecimal avgDeliveryOrderFeedback) {
		this.avgDeliveryOrderFeedback = avgDeliveryOrderFeedback;
	}

	@Column(name = "TOTAL_ORDER_FEEDBACK_IN_LAST_180_DAYS")
	public Integer getTotalOrderFeedbackInLast180Days() {
		return totalOrderFeedbackInLast180Days;
	}

	public void setTotalOrderFeedbackInLast180Days(Integer totalOrderFeedbackInLast180Days) {
		this.totalOrderFeedbackInLast180Days = totalOrderFeedbackInLast180Days;
	}

	@Column(name = "TOTAL_CAFE_ORDER_FEEDBACK_IN_LAST_180_DAYS")
	public Integer getTotalCafeOrderInLast180Days() {
		return totalCafeOrderInLast180Days;
	}

	public void setTotalCafeOrderInLast180Days(Integer totalCafeOrderInLast180Days) {
		this.totalCafeOrderInLast180Days = totalCafeOrderInLast180Days;
	}

	@Column(name = "TOTAL_DELIVERY_ORDER_FEEDBACK_IN_LAST_180_DAYS")
	public Integer getTotalDeliveryOrderInLast180Days() {
		return totalDeliveryOrderInLast180Days;
	}

	public void setTotalDeliveryOrderInLast180Days(Integer totalDeliveryOrderInLast180Days) {
		this.totalDeliveryOrderInLast180Days = totalDeliveryOrderInLast180Days;
	}

	@Column(name = "AVG_ORDER_FEEDBACK_IN_LAST_180_DAYS")
	public BigDecimal getAvgOrderFeebackInLast180Days() {
		return avgOrderFeebackInLast180Days;
	}

	public void setAvgOrderFeebackInLast180Days(BigDecimal avgOrderFeebackInLast180Days) {
		this.avgOrderFeebackInLast180Days = avgOrderFeebackInLast180Days;
	}

	@Column(name = "AVG_CAFE_ORDER_FEEDBACK_IN_LAST_180_DAYS")
	public BigDecimal getAvgCafeOrderFeedbackInLast180Days() {
		return avgCafeOrderFeedbackInLast180Days;
	}

	public void setAvgCafeOrderFeedbackInLast180Days(BigDecimal avgCafeOrderFeedbackInLast180Days) {
		this.avgCafeOrderFeedbackInLast180Days = avgCafeOrderFeedbackInLast180Days;
	}

	@Column(name = "AVG_DELIVERY_ORDER_FEEDBACK_IN_LAST_180_DAYS")
	public BigDecimal getAvgDeliveryOrderFeedbackInLast180Days() {
		return avgDeliveryOrderFeedbackInLast180Days;
	}

	public void setAvgDeliveryOrderFeedbackInLast180Days(BigDecimal avgDeliveryOrderFeedbackInLast180Days) {
		this.avgDeliveryOrderFeedbackInLast180Days = avgDeliveryOrderFeedbackInLast180Days;
	}

}
