package com.stpl.tech.kettle.data.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.dao.ExternalPartnerCardDao;
import com.stpl.tech.kettle.data.model.ExternalPartnerCardDetail;

@Repository
public class ExternalPartnerCardDaoImpl extends AbstractDaoImpl implements ExternalPartnerCardDao {
	@Override
	public List<ExternalPartnerCardDetail> getPartnerCardDetail(String cardNumber, String partnerCode, String status) {
		StringBuilder queryBuilder = new StringBuilder("FROM ExternalPartnerCardDetail E where E.partnerCardNumber = :cardNumber and E.partnerCode =:partnerCode ");
		if (status != null) {
			queryBuilder.append(" and E.requestStatus = :status");
		}
		Query query = manager.createQuery(queryBuilder.toString());
		query.setParameter("cardNumber", cardNumber);
		query.setParameter("partnerCode", partnerCode);
		if (status != null) {
			query.setParameter("status", status);
		}
		return query.getResultList();

	}

	@Override
	public List<ExternalPartnerCardDetail> getUniquePartnerBillNumber(String billNo) {
		Query query = manager.createQuery("FROM ExternalPartnerCardDetail E where E.externalOrderId = :billNo");
		query.setParameter("billNo", billNo);

		return query.getResultList();
	}
}
