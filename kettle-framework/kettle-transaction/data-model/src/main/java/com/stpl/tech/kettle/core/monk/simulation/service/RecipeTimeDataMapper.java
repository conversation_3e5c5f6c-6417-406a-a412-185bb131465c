/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.monk.simulation.service;

import org.apache.poi.ss.usermodel.Cell;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.stpl.tech.kettle.core.file.load.AbstractRowMapper;
import com.stpl.tech.kettle.core.monk.simulation.model.RecipeProductionData;

public class RecipeTimeDataMapper extends AbstractRowMapper<RecipeProductionData, Cell> {

	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("dd-MM-yyyy");

	@Override
	public void setData(RecipeProductionData expense, Cell nextCell) {
		try {
			int columnIndex = nextCell.getColumnIndex();
			switch (columnIndex) {
			// 1 Unit Id
			case 0:
				expense.productData.productId = ((Double) getCellValue(nextCell)).intValue();
				break;
			// 2 Unit Name - not required
			case 1:
				expense.productName = (String) getCellValue(nextCell);
				break;
			case 2:
				expense.productData.dimension = (String) getCellValue(nextCell);
				break;
			case 3:
				expense.productData.quantity = ((Double) getCellValue(nextCell)).intValue();
				break;
			case 5:
				expense.timeWithFluctuation = ((Double) getCellValue(nextCell)).intValue();
				break;
			case 6:
				expense.timeWithoutFluctuation = ((Double) getCellValue(nextCell)).intValue();
				break;
			case 7:
				int d = ((Double) getCellValue(nextCell)).intValue();
				expense.steepingTime = d == 0 ? 10 : d;
				expense.hasTransition = d > 0;
				break;
			case 8:
				expense.recipeString = (String) getCellValue(nextCell);
				break;
			default:
				break;
			}
		} catch (Exception e) {
			addError(nextCell, e.getMessage());
		}
	}

	@Override
	public RecipeProductionData createNewInstance() {
		return new RecipeProductionData();
	}

}
