package com.stpl.tech.kettle.clm.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.clm.dao.CLMDao;
import com.stpl.tech.kettle.core.NamedQueryDefinition;

@Repository
public class CLMDaoImpl extends CLMDataAbstractDaoImpl implements CLMDao {

	@Override
	public int updateCommunicationAttributes(String optInWhatsapp, String optInSms, int customerId) {
		Query query = manager
				.createNativeQuery(NamedQueryDefinition.CLM_CUSTOMER_ONE_VIEW_UPDATE_COMMUNICATIONS.getQuery());
		query.setParameter("optInSms", optInSms);
		query.setParameter("optInWhatsapp", optInWhatsapp);
		query.setParameter("customerId", customerId);
		return query.executeUpdate();
	}

}
