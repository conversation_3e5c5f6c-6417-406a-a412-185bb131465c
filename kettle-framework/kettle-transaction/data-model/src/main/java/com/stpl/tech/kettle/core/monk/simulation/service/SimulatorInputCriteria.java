/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.service;

import java.math.BigDecimal;

import com.stpl.tech.kettle.core.monk.simulation.model.SimulationInputData;

/**
 * <AUTHOR>
 *
 */
public class SimulatorInputCriteria {

	/**
	 * @param unitId
	 */
	private final int unitId;
	private final String unitName;

	public SimulatorInputCriteria(int unitId, String unitName) {
		this.unitId = unitId;
		this.unitName = unitName;
	}

	public static void main(String[] args) {

	}

	public SimulationInputData getInput() {
		SimulationInputData data = new SimulationInputData();
		data.bulkOrderValue = new BigDecimal("3000.0");
		data.skipBulkOrder = false;
		data.countOfMonks = 6;
		data.noOfMonths = 2;
		data.strategy = "fifo";
		data.unitId = unitId;
		data.unitName = unitName;
		data.transitionTime = 30;
		return data;
	}
}
