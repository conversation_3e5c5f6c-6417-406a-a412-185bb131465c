package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PnLAdjustmentDao;
import com.stpl.tech.kettle.data.model.ComplimentaryCode;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.domain.model.PnlAdjustment;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PnlAdjustmentStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;


@Repository
public class PnLAdjustmentDaoImpl extends AbstractDaoImpl implements PnLAdjustmentDao {


    @Override
    public List<PnlAdjustment> getAdjustments(PnlAdjustmentStatus status, List<Integer> unitIds, int startMonth, int startYear) {
        List<PnlAdjustment> list = new ArrayList<>();
        Query query = manager.createQuery("FROM PnlAdjustmentDetail E Where E.unitId IN :unitId and E.status =:status and E.month= :startMonth" +
//                " and E.year= :startYear and E.month<= :endMonth and E.year <=:endYear");
                " and E.year= :startYear");
        query.setParameter("unitId", unitIds);
        query.setParameter("startMonth",startMonth);
        query.setParameter("startYear",startYear);
//        query.setParameter("endMonth",endMonth);
//        query.setParameter("endYear",endYear);
        query.setParameter("status",status.value());
        @SuppressWarnings("unchecked")
        List<PnlAdjustmentDetail> resultList = query.getResultList();
        Optional.of(resultList).ifPresent(p -> p.forEach(q -> list.add(DataConverter.convert(q))));
        return list;
    }
}