package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.util.AppUtils;

public class PercentageBillMaxCapStrategy extends PercentageBillStrategy implements OfferActionStrategy {

	@Override
	public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache,
			Map<String, OrderItem> foundItems) {

		BigDecimal orderValue = offerOrder.getOrder().getTransactionDetail().getTotalAmount();
		BigDecimal discountPercentage = new BigDecimal(coupon.getOffer().getOfferValue());
		Optional<BigDecimal> maxCappedDiscount = Optional.ofNullable(coupon.getOffer().getMaxDiscountAmount());

		BigDecimal calculatedMaxDiscount = null;

		if (maxCappedDiscount.isPresent()) {
			TransactionDetail trans = offerOrder.getOrder().getTransactionDetail();
			BigDecimal taxRate = trans.getPaidAmount().divide(trans.getTaxableAmount(), 10, BigDecimal.ROUND_HALF_UP);
			calculatedMaxDiscount = maxCappedDiscount.get().divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
		}

		BigDecimal discountValue = AppUtils.percentOf(discountPercentage, orderValue);

		/*
		 * when we have a cap value, calculated MaxDiscount is max discount after
		 * deducting tax. this is done to get max discount as total savings amounts
		 */
		if (maxCappedDiscount.isPresent() && discountValue.compareTo(calculatedMaxDiscount) >= 0) {
			discountPercentage = AppUtils.percentageWithScale10(calculatedMaxDiscount, orderValue);
		}

		return applyStrategy(offerOrder, coupon, cache, foundItems, discountPercentage);

	}
	
	@Override
	public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
			BigDecimal paidAmount, BigDecimal maxDiscountValue) {
		
		BigDecimal discountPercentage = discount;
		BigDecimal calculatedMaxDiscount = null;

		if (maxDiscountValue != null) {
			BigDecimal taxRate = paidAmount.divide(totalAmount, 10, BigDecimal.ROUND_HALF_UP);
			calculatedMaxDiscount = maxDiscountValue.divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
		}

		BigDecimal discountValue = AppUtils.percentOf(discountPercentage, totalAmount);

		/*
		 * when we have a cap value, calculated MaxDiscount is max discount after
		 * deducting tax. this is done to get max discount as total savings amounts
		 */
		if (maxDiscountValue != null && discountValue.compareTo(calculatedMaxDiscount) >= 0) {
			discountPercentage = AppUtils.percentageWithScale10(calculatedMaxDiscount, totalAmount);
		}
		
		return getOrderDiscount(couponCode, discountPercentage, totalAmount);
	}

}
