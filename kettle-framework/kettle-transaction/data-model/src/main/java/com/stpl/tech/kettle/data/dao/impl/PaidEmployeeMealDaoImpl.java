/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaidEmployeeMealDao;
import com.stpl.tech.kettle.data.model.EmployeeMealAllowanceData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.util.AppUtils;

@Repository
public class PaidEmployeeMealDaoImpl extends AbstractDaoImpl implements PaidEmployeeMealDao {

	@Autowired
	private EnvironmentProperties env;

	@Override
	public void addPaidEmployeeMeal(OrderDetail detail, Order order) {
		EmployeeMealAllowanceData a = new EmployeeMealAllowanceData();
		a.setOrderId(detail.getOrderId());
		a.setOrdertime(detail.getBillingServerTime());
		a.setEmployeeId(order.getEmployeeIdForMeal());
		a.setAmount(detail.getSettledAmount());
		add(a);
	}

	@Override
	public BigDecimal getAvailableAllownaceLimit(Integer employeeId) {
		BigDecimal spent = redeemedMealAllowance(employeeId);
		long days = AppUtils.numberOfMealDays(env.getEmployeeMealMonthlyStartDate());
		/*
		 * long total = 365; // random number just to initialize // need method to get
		 * employee details here // can add it to cache EmployeeDetail e =
		 * manager.find(EmployeeDetail.class, employeeId); if(e.getJoiningDate() !=
		 * null) { total = AppUtils.numberOfMealDaysFromDate(e.getJoiningDate()); }
		 * 
		 * // actual < calculated if(total < days) { days = total; }
		 */

		// 26 < 27
		if (env.getEmployeeMealDayLimit().longValue() < days) {
			days = env.getEmployeeMealDayLimit().longValue();
		}
		BigDecimal perDayLimit = env.getEmployeeMealPerDayAmountLimit();

		BigDecimal available = (new BigDecimal(days).multiply(perDayLimit)).subtract(spent);

		/*if (available.intValue() > perDayLimit.intValue()) {
			available = perDayLimit;
		}*/

		BigDecimal spentToday = redeemedMealAllowanceToday(employeeId);

		return available.subtract(spentToday);
	}

	private BigDecimal redeemedMealAllowance(Integer employeeId) {
		Query query = manager.createQuery("SELECT SUM(E.amount) FROM EmployeeMealAllowanceData E"
				+ " WHERE E.employeeId = :employeeId AND E.ordertime > :mealStartDate "
				+ " AND E.ordertime <= :lastBusinessDate GROUP BY E.employeeId");
		query.setParameter("employeeId", employeeId);
		query.setParameter("mealStartDate",
				AppUtils.getPaidEmployeeMealStartDate(env.getEmployeeMealMonthlyStartDate()));
		query.setParameter("lastBusinessDate", AppUtils.getStartOfBusinessDay(AppUtils.getBusinessDate()));
		try {
			return (BigDecimal) query.getSingleResult();
		} catch (NoResultException nre) {
			return BigDecimal.ZERO;
		}
	}

	private BigDecimal redeemedMealAllowanceToday(Integer employeeId) {
		Query query = manager.createQuery("SELECT SUM(E.amount) FROM EmployeeMealAllowanceData E"
				+ " WHERE E.employeeId = :employeeId AND E.ordertime > :businessDate GROUP BY E.employeeId");
		query.setParameter("employeeId", employeeId);
		query.setParameter("businessDate", AppUtils.getStartOfBusinessDay(AppUtils.getBusinessDate()));
		try {
			return (BigDecimal) query.getSingleResult();
		} catch (NoResultException nre) {
			return BigDecimal.ZERO;
		}
	}

	@Override
	public Set<Integer> getEmployeeMealOrders(int userId) {
		Query query = manager.createQuery("SELECT DISTINCT(E.orderId) FROM EmployeeMealAllowanceData E"
				+ " WHERE E.employeeId = :employeeId AND E.ordertime > :mealStartDate");
		query.setParameter("employeeId", userId);
		query.setParameter("mealStartDate",
				AppUtils.getPaidEmployeeMealStartDate(env.getEmployeeMealMonthlyStartDate()));
		@SuppressWarnings("unchecked")
		List<Integer> list = query.getResultList();
		return Optional.ofNullable(list).map(Collection::stream).orElse(Stream.empty()).collect(Collectors.toSet());
	}

}
