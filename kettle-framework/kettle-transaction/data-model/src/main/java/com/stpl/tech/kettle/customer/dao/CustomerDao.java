/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.core.CampaignOfferDetail;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerTransactionData;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerTransactionDetail;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.master.OfferLastRedemptionView;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Pair;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CustomerDao extends AbstractDao {

    public Customer getCustomer(int customerId) throws DataNotFoundException;

    CustomerTransactionDetail getCustomerTransactionDetail(int customerId, int brandId);

    public Customer getCustomer(String contactNumber);

    public boolean isCustomerOld(String contactNumber, int beforeYear);

    public Customer getCustomer(String code, String contactNumber) throws DataNotFoundException;

    public Customer addCustomer(Customer customer) throws DataUpdationException;

    public boolean updateCustomer(Customer customer) throws DataUpdationException;

    public boolean updateCustomerAppAction(Customer customer) throws DataUpdationException;

    public boolean updateShopifyCustomer(Customer customer) throws DataUpdationException;

    boolean updateCustomerEmail(Customer customer,String flag) throws DataUpdationException;

    //public boolean updateBasicCustomerInfo(Customer customer);

    public void verifyContactNumber(String contactNumber);

    public void verifyEmailAddress(String contactNumber);

    public List<CustomerOffer> getOfferDetail(int customerId, String offerCode);

    public int addAddress(int customerId, Address address) throws DataUpdationException;

    public CustomerTransactionData getCustomerTransactionInfo(String contactNumber,Customer customer) throws DataNotFoundException;

    public CustomerInfo getCustomerByDeliveryAddress(int deliveryAddressId);

    public void addOfferDetail(int customerId, String offerCode, int orderId);

    public Customer addCustomerUnchecked(Customer customer) throws DataUpdationException;

    public void overrideContactVerificationStatus(String contactNumber, boolean contactVerified);

    public boolean eligibleForSignupOffer(int customerId);

    public List<CustomerInfo> getCustomersWithPendingLoyalty(Date startDate, Date endDate);

    public List<CustomerInfo> getNewCustomers(String firstOrderSource, Date startDate, Date endDate);

    public LoyaltyScore getLoyaltyScore(int customerId);

    public Customer viewCustomer(String contactNumber) throws DataNotFoundException;

    public Address addAddress(String contact, Address address) throws DataUpdationException;

    public List<Address> getNewAddress(int id, List<Integer> addressIds);

    public boolean removeInvalidEmails(List<String> invalidEmails) throws DataUpdationException;

    public void updateMigrationPoints(CustomerInfo customer, String emailId);

    public void updateLastNPSTime(Date updateTime, int customerId);

    public void markAsInternalCustomers(List<String> contactNumbers);

    public boolean existsContactNumber(String number);

    public Customer getCustomerByRefCode(String refCode);

    public BigDecimal getAvailableCash(Integer customerId);

    public String getUniqueRefCode(String name, String contact);

    public void updateRefCodeInCashData(int customerId, String refCode);

    public List<Integer> getCustomersWithoutRefCode();

    public CustomerInfo getCustomerInfo(String contact);

    public List<CustomerInfo> getCustomerWithReferralCode(List<String> contacts);

    public Customer getCustomerByFaceId(String faceId);

    public boolean mapCustomerByFaceId(String contact, String faceId);

    String optOutOfFaceIt(String contactNumber, boolean flag);

    String optOutOfFaceIt(int customerId);

    public void updateIsChaayosCustomer(int customerId, String flag);

    public OrderDetail getOrderDetail(int orderId);

    public String getCustomerRefCode(int customerId);

    public List<Integer> getCustomerIds(int customerId, int batchSize);

    public List<Integer> getCustomerIdsWithPendingSignupOffer(int noOfDays);

    void expireSignupOffer(List<Integer> customerIds);

    void expireSignupOffer(int noOfDays);

    public BigDecimal getAvailableCashback(Integer customerId);

    List<LoyaltyScore> getCustomersLoyaltyScore(List<Integer> customerIds);

    Long checkCustomerAdditionalDetail(Integer customerId, String type);

    boolean hasOrdersForOrderSource(String contactNumber, String orderSource);

    public boolean updateCrmScreenUrl(CrmAppScreenDetail detail);

    boolean updateCrmScreenStatus(List<CrmAppScreenDetail> details);

	public List<OrderDetail> customerVisit(Integer customerId);

	public OrderDetail getFirstZomatoOrder(Integer customerId);

    List<FeedbackDetail> feedbackDetails(Integer orderId, Integer customerId);

    public List<Integer> removeAllFaceIdsWithGivenFaceId(String faceId);

	CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	List<CustomerCampaignOfferDetail> getNextBestOfferDetail(String type, Integer brandId);

    CustomerInfo getCustomerInfoById(Integer cid);

	List<CustomerCampaignOfferDetail> getAllNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate);

	List<CustomerCampaignOfferDetail> getUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate);

	List<CustomerCampaignOfferDetail> getNotUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate);

    Map<String, Pair<Boolean, Boolean>> getNotificationFlags(List<String> customerContact);

    boolean updateWhatsappOptInOut(CustomerResponse customerResponse);

	public Integer checkCouponUsage(int customerId, String code);

    CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

    CustomerEmailData getCustomerEmailData(int customerId, Integer brandId);

	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	public Map<String, List<CustomerCampaignOfferDetail>> getNotUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate);

	Map<String, List<CustomerCampaignOfferDetail>> getUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate);

    OfferLastRedemptionView getOrderDetailViaOffer(Integer customerId, String couponCode, Date businessDate, Integer dailyFreqCount);

    CustomerContactInfoMapping getCustomerContactInfoMapping(Integer customerId);

    CustomerContactInfoMapping getCustomerContactInfoMappingFromOldContact(String oldContact);

    List<CampaignOfferDetail> getNotUsedNextOfferDetails(Integer brandId);

    List<CustomerCampaignOfferDetail> getPendingNextJourneyEligibleOffer(Integer brandId, Date nextOfferDate);

    List<CustomerInfo> getEmpDiscount() throws Exception;

   Integer deactivateEmpDiscount(List<Integer> customerIds) throws Exception;

    void deactivateOldSubscriptionPlans();

    void deactivateOldSubscriptionPlanEvents();

	Integer getCustomerId(String contactNumber);

    public UnitTableMappingDetail findOpenTableByCustomerId(Integer customerId);

}
