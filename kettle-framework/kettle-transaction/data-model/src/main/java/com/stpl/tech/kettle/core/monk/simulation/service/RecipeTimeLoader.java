/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.core.file.load.ExcelParser;
import com.stpl.tech.kettle.core.monk.simulation.model.RecipeKey;
import com.stpl.tech.kettle.core.monk.simulation.model.RecipeProductionData;

/**
 * <AUTHOR>
 *
 */
public class RecipeTimeLoader {

	private static final Logger LOG = LoggerFactory.getLogger(RecipeTimeLoader.class);
	private final String recipeFilePath;
	private static final Map<RecipeKey, RecipeProductionData> recipeData = new HashMap<>();

	public RecipeTimeLoader(String recipeFilePath) {
		super();
		this.recipeFilePath = recipeFilePath;
	}

	public void loadData() {
		ExcelParser<RecipeProductionData> parser = new ExcelParser<>(new RecipeTimeDataMapper());
		LOG.info("Starting to load the recipe times");
		List<RecipeProductionData> expenses = new ArrayList<>();
		try {
			expenses = parser.parseExcel(recipeFilePath, 0, 1, 0, 1);
			if (parser.hasErrors()) {
				LOG.error("Errors in excel sheet " + parser.getErrors());
				throw new RuntimeException("Errors in excel sheet " + parser.getErrors());
			}
			expenses.forEach((item) -> {
				recipeData.put(item.productData, item);
			});
			LOG.info("Finished Loading Recipe Times");
		} catch (IOException e) {
			LOG.error("Failed to load the stored excel file ", e);
			throw new RuntimeException("Failed to load the stored excel file ", e);
		}
	}
	
	public RecipeProductionData get(RecipeKey key){
		return recipeData.get(key);
	}
}
