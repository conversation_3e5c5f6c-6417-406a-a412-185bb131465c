package com.stpl.tech.kettle.clevertap.data.dao;

import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.List;

/**
 * Created by Chaayos on 02-05-2017.
 */
public interface CleverTapDataPushDao extends AbstractDao {

    List<Integer> getCustomerIdsBatch(int batchSize, Integer lastCustomerId,List<Integer> excludeCustomerIds);

    List<Integer> getOrderBatch(Integer startOrderId, Integer batchSize, List<Integer> excludingPartnerIds,List<Integer> excludeCustomerIds );

}
