package com.stpl.tech.kettle.clevertap.domain.model;

import java.util.List;

import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.data.model.CleverTapProfilePushTrack;

/**
 * Created by Chaayos on 08-05-2017.
 */
public class CleverTapPushResponse {
    private String status;
    private int processed;
    private List<Object> unprocessed;
    private List<CleverTapProfilePushTrack> profiles;
    private List<EventPushTrack> events;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getProcessed() {
        return processed;
    }

    public void setProcessed(int processed) {
        this.processed = processed;
    }

    public List<Object> getUnprocessed() {
        return unprocessed;
    }

    public void setUnprocessed(List<Object> unprocessed) {
        this.unprocessed = unprocessed;
    }


    public List<CleverTapProfilePushTrack> getProfiles() {
        return profiles;
    }

    public void setProfiles(List<CleverTapProfilePushTrack> profiles) {
        this.profiles = profiles;
    }

    public List<EventPushTrack> getEvents() {
        return events;
    }

    public void setEvents(List<EventPushTrack> events) {
        this.events = events;
    }
}
