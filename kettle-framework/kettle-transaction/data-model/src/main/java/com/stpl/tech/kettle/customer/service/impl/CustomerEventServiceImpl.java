package com.stpl.tech.kettle.customer.service.impl;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.customer.dao.CustomerBirthdayDetailDao;
import com.stpl.tech.kettle.customer.service.CustomerEventService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.data.model.CustomerEventDetailData;
import com.stpl.tech.master.util.CustomerEventType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class CustomerEventServiceImpl implements CustomerEventService {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private OfferManagementService offerManagementService;
    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;
    @Autowired
    private OfferManagementDao offerDao;

    @Autowired
    private CustomerBirthdayDetailDao customerBirthdayDetailDao;
    @Autowired
    private EnvironmentProperties properties;

    private static final String BIRTHDAY_FREE_DESI_CHAI = "Free Desi Chai";
    private static final String BIRTHDAY_DISCOUNT = "20% off on MoV 999";
    private static final Long BUFFER_TIME = 10L;
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addCustomerEventMonth(String contactNumber, Integer eventMonth, Integer eventDate, String acquisitionSource, CustomerEventType customerEventType, String forceUpdate){
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        try{
            Integer customerId = customerService.getCustomerId(contactNumber);
            if(Objects.nonNull(customerId)){
                Map<String, Object> userProfiles = new HashMap<>();
                Map<String, Object> eventData = new HashMap<>();
                CustomerEventDetailData customerEventDetailData = customerBirthdayDetailDao.findByCustomerIdAndEventType(customerId,customerEventType);
                if(Objects.isNull(customerEventDetailData) || AppConstants.YES.equals(forceUpdate)) {
                    if(Objects.isNull(customerEventDetailData)){
                        CustomerEventDetailData data = CustomerEventDetailData.builder()
                                .customerId(customerId)
                                .contactNumber(contactNumber)
                                .acquisitionSource(acquisitionSource)
                                .customerEventType(customerEventType)
                                .eventMonth(eventMonth)
                                .eventDate(eventDate)
                                .createdAt(AppUtils.getCurrentTimestamp())
                                .build();
                        try{
                            customerEventDetailData = customerBirthdayDetailDao.add(data);
                        }catch (Exception e){
                            log.info("Error in Saving Customer Event Data");
                        }

                    }else if(customerEventDetailData.getAcquisitionSource().equals(acquisitionSource) || acquisitionSource.equals(AppConstants.WHATSAPP)){
                        customerEventDetailData.setEventMonth(eventMonth);
                        customerEventDetailData.setCustomerEventType(customerEventType);
                        customerEventDetailData.setEventDate(eventDate);
                        customerEventDetailData.setUpdatedAt(AppUtils.getCurrentTimestamp());
                        customerEventDetailData = customerBirthdayDetailDao.update(customerEventDetailData);
                    }

                    userProfiles.put("Customer"+customerEventType.toString()+"Month", eventMonth);
                    userProfiles.put("Customer"+customerEventType.toString()+"Date",eventDate);

                    if(eventMonth == AppUtils.getCurrentMonth()){
                        // todo generate coupon code according to event
                        List<Integer> offerIds = getCustomerEventOfferIds(customerEventType);
                        String cloneCoupon1 = offerDao.getCouponByOfferId(offerIds.get(0));
                        String cloneCoupon2 = offerDao.getCouponByOfferId(offerIds.get(1));

                        Integer validityInDays = AppUtils.getDaysInMonth(eventMonth);
                        validityInDays = validityInDays - AppUtils.getCurrentDayofMonth() + 1;

                        // todo generate coupon code according to event
                        String coupon1 = offerManagementService.generateCoupon(validityInDays,contactNumber,cloneCoupon1,customerEventType.getCouponPrefix()).getMappings().get(contactNumber).getCoupon();
                        String coupon2 = offerManagementService.generateCoupon(validityInDays,contactNumber,cloneCoupon2,customerEventType.getCouponPrefix()).getMappings().get(contactNumber).getCoupon();

                        userProfiles.put("Customer"+customerEventType.toString()+"Offer1", coupon1);
                        userProfiles.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
                        userProfiles.put("Customer"+customerEventType.toString()+"Offer2",coupon2);
                        userProfiles.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);

                        eventData.put("Customer"+customerEventType.toString()+"Month", eventMonth);
                        eventData.put("Customer"+customerEventType.toString()+"Date",eventDate);
                        eventData.put("Customer"+customerEventType.toString()+"Offer1", coupon1);
                        eventData.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
                        eventData.put("Customer"+customerEventType.toString()+"Offer2",coupon2);
                        eventData.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);


                        customerEventDetailData.setCouponGenerationAt(AppUtils.getCurrentTimestamp());
                        customerEventDetailData.setEventOffer1(coupon1);
                        customerEventDetailData.setEventOffer2(coupon2);
                        customerBirthdayDetailDao.update(customerEventDetailData);
                    }
                    try{
                        cleverTapDataPushService.uploadProfileAttributes(customerId,AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                                , CleverTapConstants.REGULAR,userProfiles);
                    }catch (Exception e){
                        log.info("Error in Pushing UserProfile Attribute to clever tap");
                    }
                    try{
                        if(eventMonth == AppUtils.getCurrentMonth()) {
                            if(customerEventType.equals(CustomerEventType.Birthday)){
                                cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.BIRTHDAY_MONTH,
                                        AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);

                            }else if(customerEventType.equals(CustomerEventType.Anniversary)){
                                cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.ANNIVERSARY_MONTH,
                                        AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
                            }
                            else{
                                cleverTapDataPushService.publishCustomEvent(customerId, customerEventType.toString().concat("_Month"),
                                        AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);

                            }
                        }
                    }catch (Exception e){
                        log.info("Error in Pushing Event to clever tap");
                    }
                    return true;
                }
                else{
                    log.info("Customer {} month Already Exist for customerId : {}",customerEventType, customerId);
                }
            }
            else{
                log.info("Not a Chaayos Customer");
            }
        }catch (Exception e){
            log.info("Error in add {} Month : {}",customerEventType,e);
        }
        return false;
    }

    @Override
    public List<Integer> getCustomerEventOfferIds(CustomerEventType customerEventType){
        switch (customerEventType) {
            case Birthday:
                return properties.getBirthdayMonthOfferIds();
            case Anniversary:
                return properties.getAnniversaryMonthOfferIds();
            default:
                return properties.getotherEventMonthOfferIds();
        }
    }



}
