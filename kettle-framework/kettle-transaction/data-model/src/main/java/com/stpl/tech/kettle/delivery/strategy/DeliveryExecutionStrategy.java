/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.Unit;

public interface DeliveryExecutionStrategy {
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props);

	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo, EnvironmentProperties props);

	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props);

	public void setAuthorizationObject(AuthorizationObject authorization);

}
