package com.stpl.tech.kettle.google.service.impl;

import com.google.ads.googleads.lib.GoogleAdsClient;
import com.google.ads.googleads.v12.common.StoreSalesMetadata;
import com.google.ads.googleads.v12.enums.OfflineUserDataJobTypeEnum;
import com.google.ads.googleads.v12.errors.GoogleAdsError;
import com.google.ads.googleads.v12.errors.GoogleAdsException;
import com.google.ads.googleads.v12.errors.GoogleAdsFailure;
import com.google.ads.googleads.v12.resources.OfflineUserDataJob;
import com.google.ads.googleads.v12.services.AddOfflineUserDataJobOperationsRequest;
import com.google.ads.googleads.v12.services.AddOfflineUserDataJobOperationsResponse;
import com.google.ads.googleads.v12.services.ClickConversionResult;
import com.google.ads.googleads.v12.services.ConversionUploadServiceClient;
import com.google.ads.googleads.v12.services.CreateOfflineUserDataJobResponse;
import com.google.ads.googleads.v12.services.OfflineUserDataJobOperation;
import com.google.ads.googleads.v12.services.OfflineUserDataJobServiceClient;
import com.google.ads.googleads.v12.services.UploadClickConversionsRequest;
import com.google.ads.googleads.v12.services.UploadClickConversionsResponse;
import com.google.ads.googleads.v12.utils.ErrorUtils;
import com.google.ads.googleads.v12.utils.ResourceNames;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.GoogleDataPushNotification;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.google.converter.GoogleDataConverter;
import com.stpl.tech.kettle.google.data.dao.GoogleDataPushDao;
import com.stpl.tech.kettle.google.domain.model.GoogleCustomerOrderData;
import com.stpl.tech.kettle.google.service.GoogleDataPushService;
import com.stpl.tech.kettle.google.util.GoogleConstants;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
public class GoogleDataPushServiceImpl implements GoogleDataPushService {

    @Autowired
    private GoogleDataPushDao googleDataPushDao;

    @Autowired
    private EnvironmentProperties props;

    /**
     * Create google ads client using credentials.
     */
    private GoogleAdsClient createGoogleAdsClientWithCredentials() {
        try {
            //alternate method for ads.properties configuration file contents.
            ClassLoader classLoader = GoogleDataPushServiceImpl.class.getClassLoader();
            URL resource = classLoader.getResource(GoogleConstants.SECRET_KEY_RELATIVE_PATH);
            File tempSecretJsonFile = null;
            if (Objects.isNull(resource)) {
                throw new IllegalArgumentException("File not found!");
            } else {
                tempSecretJsonFile = new File(resource.getFile());
            }
            log.info("PATH IS {} ", tempSecretJsonFile.getPath());

            Properties adsProperties = new Properties();
            adsProperties.put(GoogleAdsClient.Builder.ConfigPropertyKey.SERVICE_ACCOUNT_SECRETS_PATH.getPropertyKey(), tempSecretJsonFile.getPath());
            adsProperties.put(GoogleAdsClient.Builder.ConfigPropertyKey.SERVICE_ACCOUNT_USER.getPropertyKey(), GoogleConstants.SERVICE_ACCOUNT_USER);
            adsProperties.put(GoogleAdsClient.Builder.ConfigPropertyKey.DEVELOPER_TOKEN.getPropertyKey(), props.getDeveloperTokenForGoogleAdsPush());
            adsProperties.put(GoogleAdsClient.Builder.ConfigPropertyKey.LOGIN_CUSTOMER_ID.getPropertyKey(), props.getLoginCustomerIdForGoogleAdsPush());

            GoogleAdsClient googleAdsClient = GoogleAdsClient.newBuilder()
                    .setLoginCustomerId(props.getLoginCustomerIdForGoogleAdsPush())
                    .fromProperties(adsProperties)
                    .build();

            log.info("Created GoogleAdsClient");
            return googleAdsClient;

        } catch (Throwable e) {
            log.info("Failed to create GoogleAdsClient. Exception: ", e);
            return null;
        }
    }

    /**
     * Creates an offline user data job for uploading store sales transactions.
     *
     * @return the resource name of the created job.
     */
    private String createGoogleOfflineDataJob(OfflineUserDataJobServiceClient offlineUserDataJobServiceClient,
                                              long customerId, OfflineUserDataJobTypeEnum.OfflineUserDataJobType offlineUserDataJobType,
                                              String customKey) {

        StoreSalesMetadata.Builder storeSalesMetadataBuilder = StoreSalesMetadata.newBuilder()
                .setLoyaltyFraction(0.85)
                .setTransactionUploadFraction(0.8);

        //custom key and values created in Google Ads UI
        if (customKey != null && !customKey.isEmpty()) {
            storeSalesMetadataBuilder.setCustomKey(customKey);
        }

        // Creates a new offline user data job.
        OfflineUserDataJob.Builder offlineUserDataJobBuilder = OfflineUserDataJob.newBuilder()
                .setType(offlineUserDataJobType)
                .setStoreSalesMetadata(storeSalesMetadataBuilder);

        // Issues a request to create the offline user data job.
        CreateOfflineUserDataJobResponse createOfflineUserDataJobResponse =
                offlineUserDataJobServiceClient.createOfflineUserDataJob(
                        Long.toString(customerId), offlineUserDataJobBuilder.build());
        String offlineUserDataJobResourceName = createOfflineUserDataJobResponse.getResourceName();
        log.info("Created an offline user data job with resource name: {}", offlineUserDataJobResourceName);
        return offlineUserDataJobResourceName;
    }

    /**
     * Adds operations (converted transactions) to the job.
     * Issues a (POST) request to add the operations to the offline user data job.
     */
    private void addTransactionsToOfflineUserDataJob(
            OfflineUserDataJobServiceClient offlineUserDataJobServiceClient,
            List<OfflineUserDataJobOperation> userDataJobOperations,
            String offlineUserDataJobResourceName) {

        AddOfflineUserDataJobOperationsResponse response =
                offlineUserDataJobServiceClient.addOfflineUserDataJobOperations(
                        AddOfflineUserDataJobOperationsRequest.newBuilder()
                                .setResourceName(offlineUserDataJobResourceName)
                                .setEnablePartialFailure(true)
                                .setEnableWarnings(true)
                                .addAllOperations(userDataJobOperations)
                                .build());

        // Prints the status message if any partial failure error is returned the example HandlePartialFailure.java to learn more.
        if (response.hasPartialFailureError()) {
            GoogleAdsFailure googleAdsFailure =
                    ErrorUtils.getInstance().getGoogleAdsFailure(response.getPartialFailureError());
            googleAdsFailure.getErrorsList()
                    .forEach(e -> log.error("Partial failure occurred: ", e.getMessage()));
            log.error("Encountered {} partial failure errors while adding {} operations to the offline user "
                            + "data job: '{}'. Only the successfully added operations will be executed when "
                            + "the Google job runs.",
                    ErrorUtils.getInstance().getFailedOperationIndices(googleAdsFailure).size(),
                    userDataJobOperations.size(),
                    response.getPartialFailureError().getMessage());

            if (response.hasWarning()) {
                GoogleAdsFailure warningsFailure = ErrorUtils.getInstance()
                        .getGoogleAdsFailure(response.getWarning());
                log.info("Encountered {} warning(s).", warningsFailure.getErrorsCount());
            }
        } else {
            log.info("Successfully added {} operations to the offline user data job.", userDataJobOperations.size());
        }
    }

    /**
     * @param businessDate getting orders for (dt - 1) 5:30 A.M to (dt) 5:30 A.M
     * @param brandId
     */
    @Override
    public void uploadGoogleOfflineConversionDataBulk(Date businessDate, Integer brandId, String brandCustomerId) {
        Date startTime = AppUtils.getUpdatedTimeInDate(5, 30, 0, AppUtils.getPreviousDate(businessDate));
        Date endTime = AppUtils.getUpdatedTimeInDate(5, 30, 0, businessDate);
        List<GoogleCustomerOrderData> orderCustomerDataJoinList = googleDataPushDao.getCustomerOrdersBulk(startTime, endTime, brandId);

        MessageDigest sha256Digest;
        try {
            sha256Digest = MessageDigest.getInstance(AppConstants.SHA256_HASH);
        } catch (NoSuchAlgorithmException e) {
            log.error("Hashing algorithm implementation missing");
            return;
        }
//        TODO change customValue for brand
        Long customerId = Long.parseLong(brandCustomerId);
        List<OfflineUserDataJobOperation> ordersConvertedList = new ArrayList<>();
        for (GoogleCustomerOrderData orderData : orderCustomerDataJoinList) {
            OfflineUserDataJobOperation userDataJobOperation = GoogleDataConverter.convertOrderDataToOperation(
                    orderData.getOrderTaxableAmount(), orderData.getTransactionTime(), orderData.getContactNumber(),
                    customerId,
                    Long.valueOf(GoogleConstants.CONVERSION_ACTION_ID_STORE_SALES_CONVERSION),
                    null, "CHAAYOS", sha256Digest,
                    orderData.getGeneratedOrderId());

            if (Objects.nonNull(userDataJobOperation)) {
                ordersConvertedList.add(userDataJobOperation);
            }
        }

        GoogleAdsClient googleAdsClient = createGoogleAdsClientWithCredentials();
        if (!Objects.nonNull(googleAdsClient)) {
            return;
        }

        String offlineUserDataJobResourceName;
        try (OfflineUserDataJobServiceClient offlineUserDataJobServiceClient =
                     googleAdsClient.getLatestVersion().createOfflineUserDataJobServiceClient()) {

            offlineUserDataJobResourceName = createGoogleOfflineDataJob(
                    offlineUserDataJobServiceClient,
                    customerId,
                    OfflineUserDataJobTypeEnum.OfflineUserDataJobType.valueOf(GoogleConstants.USER_DATA_JOB_TYPE),
                    null);
            log.info("GoogleUserDataJob created with name: {}", offlineUserDataJobResourceName);

            //created new thread and adding timeout to it
            ExecutorService executor = Executors.newSingleThreadExecutor();
            Runnable task = () -> {
                //adds transactions to the job.
                addTransactionsToOfflineUserDataJob(offlineUserDataJobServiceClient, ordersConvertedList, offlineUserDataJobResourceName);
            };

            Future executionTask = executor.submit(task);
            try {
                executionTask.get(5, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                executionTask.cancel(true);
                String error = "Timeout Exception occurred while uploading data for Google Store Sales Conversion";
                throw new Exception(error, e);
            } catch (Exception e) {
                throw new Exception("Unknown exception at Google Click Conversion", e);
            } finally {
                executor.shutdownNow();
            }

            //issues an asynchronous request to run the offline user data job.
            offlineUserDataJobServiceClient.runOfflineUserDataJobAsync(offlineUserDataJobResourceName);
            log.info("Sent request to asynchronously run offline user data job: {}", offlineUserDataJobResourceName);
            log.info("***********google data pushed***************");
            new GoogleDataPushNotification("Successfully pushed Offline Store Sales Data For Chaayos :: " +
                    ordersConvertedList.size() + " Customers",
                    new ArrayList<>(), props.getEnvironmentType()).sendEmail();

        } catch (GoogleAdsException gae) {
            log.error("Request ID {} failed due to GoogleAdsException. Underlying errors: ", gae.getRequestId());
            int i = 0;
            for (GoogleAdsError googleAdsError : gae.getGoogleAdsFailure().getErrorsList()) {
                log.info("Error {}: {}", i++, googleAdsError);
            }
            new ErrorNotification("Failed in sending data to Google for Offline Conversions", gae.getMessage(), gae,
                    props.getEnvironmentType()).sendEmail();
        } catch (Exception e) {
            log.error("Failed in sending data to Google for orders ranging :Start Time {} to End Time {}", startTime, endTime);
            log.info("Exception while pushing data to google ", e);
            new ErrorNotification("Failed in sending data to Google for Offline Conversions", e.getMessage(), e,
                    props.getEnvironmentType()).sendEmail();
        }
    }

    /**
     * @param brandCustomerId the client customer ID.
     */
    @Override
    public void uploadDataForConversionsForLeads(Date businessDate, Integer brandId, String brandCustomerId) {
        try {

            Date startTime = AppUtils.getUpdatedTimeInDate(5, 30, 0, AppUtils.getPreviousDate(businessDate));
            Date endTime = AppUtils.getUpdatedTimeInDate(5, 30, 0, businessDate);
            Long customerId = Long.parseLong(brandCustomerId);

            //conversions -> find the action -> in url ctid=conversionActionId
            Long conversionActionId = Long.parseLong(GoogleConstants.CONVERSION_ACTION_ID_OFFLINE_CONVERSION);
            String conversionActionResourceName = ResourceNames.conversionAction(customerId, conversionActionId);

            MessageDigest sha256Digest = MessageDigest.getInstance(AppConstants.SHA256_HASH);

            UploadClickConversionsRequest.Builder requestData = UploadClickConversionsRequest.newBuilder()
                    .setCustomerId(Long.toString(customerId))
                    .setPartialFailure(true);

            List<GoogleCustomerOrderData> orderCustomerDataJoinList = googleDataPushDao.getGooglePushDataForLeads(startTime, endTime, brandId);
            for (GoogleCustomerOrderData data : orderCustomerDataJoinList) {
                try {
                    requestData.addConversions(GoogleDataConverter.convertOrderDataToClickConversionUploadSpec(
                            conversionActionResourceName,
                            null,
                            data.getTransactionTime(),
                            data.getOrderTaxableAmount().doubleValue(),
                            data.getGeneratedOrderId(),
                            data.getContactNumber(),
                            data.getGclid(),
                            sha256Digest));
                } catch (Exception e) {
                    log.error("Error while converting Customer Order Data to Google specification", e);
                }
            }

            GoogleAdsClient googleAdsClient = createGoogleAdsClientWithCredentials();
            if (Objects.isNull(googleAdsClient)) {
                throw new Exception("Could not create Google Ads Client");
            }

            sendUploadClickConversionRequestToGoogle(googleAdsClient, requestData.build());

        } catch (NoSuchAlgorithmException e) {
            String errorText = "Could not create 256hash digest";
            log.error(errorText, e);
            sendErrorNotification(errorText, e);
        } catch (Exception e) {
            log.error(":::: Error while uploading offline data for Google Leads", e);
            sendErrorNotification("!! Exception occurred while uploading data for Google Leads", e);
        }
    }

    private void sendUploadClickConversionRequestToGoogle(GoogleAdsClient googleAdsClient, UploadClickConversionsRequest requestData) throws Exception {

        final UploadClickConversionsResponse[] response = new UploadClickConversionsResponse[1];
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Runnable task = () -> {
            try (ConversionUploadServiceClient conversionUploadServiceClient =
                         googleAdsClient.getLatestVersion().createConversionUploadServiceClient()) {
                // Uploads the click conversion
                response[0] = conversionUploadServiceClient.uploadClickConversions(requestData);
            }
        };

        Future executionTask = executor.submit(task);

        try {
            executionTask.get(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            executionTask.cancel(true);
            String error = "TimeOut Exception occurred while uploading data for Google Leads";
            throw new Exception(error, e);
        } catch (Exception e) {
            throw new Exception("Unknown exception at Google Click Conversion", e);
        } finally {
            executor.shutdownNow();
        }

        if (response[0].hasPartialFailureError()) {
            GoogleAdsFailure googleAdsFailure =
                    ErrorUtils.getInstance().getGoogleAdsFailure(response[0].getPartialFailureError());
            StringBuilder errorList = new StringBuilder();
            googleAdsFailure
                    .getErrorsList()
                    .forEach(e -> {
                        log.error("Partial failure occurred: " + e.getMessage() + " " + e.getTrigger());
                        errorList.append(e.getMessage()).append(" ").append(e.getErrorCode()).append(" ").append(e.getTrigger());
                    });
            sendErrorNotification("Partial Errors while uploading data for Google Leads. " +
                    "Errors List:: \n" + errorList, new Exception("Partial Errors Occurred"));
        }

        List<ClickConversionResult> resultsList = response[0].getResultsList();
        // Only prints valid results.
        if (Objects.nonNull(resultsList)) {
            List<String> msgList = new ArrayList<>();
            resultsList.forEach(result -> {
                if (result.hasConversionDateTime()) {
                    log.info("Uploaded conversion that occurred at {} to {}.", result.getConversionDateTime(),
                            result.getConversionAction());
                }
                if (result.hasGclid()) {
                    log.info("Uploaded conversion that occurred at {} from Google Click ID {} to {}",
                            result.getConversionDateTime(), result.getGclid(), result.getConversionAction());
                    msgList.add(result.getGclid());
                }
            });
            new GoogleDataPushNotification("Success", msgList, props.getEnvironmentType()).sendEmail();
        }
    }

    private void sendErrorNotification(String errorBody, Exception exception) {
        try {
            new ErrorNotification("Errors while sending data to Google for Offline Conversions",
                    errorBody, exception, props.getEnvironmentType()).sendEmail();
        } catch (Exception e) {
            log.error("Error while sending error mail for Google Data Push ", e);
        }
    }

}
