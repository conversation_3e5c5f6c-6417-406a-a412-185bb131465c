/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

public class OrderUnitMapping {

	private int unitId;
	private Integer terminalId;
	private String orderSource;
	private int employeeId;

	public OrderUnitMapping(int unitId, Integer terminalId, String orderSource, int employeeId) {
		super();
		this.unitId = unitId;
		this.terminalId = terminalId;
		this.orderSource = orderSource;
		this.employeeId = employeeId;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + employeeId;
		result = prime * result + ((orderSource == null) ? 0 : orderSource.hashCode());
		result = prime * result + ((terminalId == null) ? 0 : terminalId.hashCode());
		result = prime * result + unitId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		OrderUnitMapping other = (OrderUnitMapping) obj;
		if (employeeId != other.employeeId) {
			return false;
		}
		if (orderSource == null) {
			if (other.orderSource != null) {
				return false;
			}
		} else if (!orderSource.equals(other.orderSource)) {
			return false;
		}
		if (terminalId == null) {
			if (other.terminalId != null) {
				return false;
			}
		} else if (!terminalId.equals(other.terminalId)) {
			return false;
		}
		if (unitId != other.unitId) {
			return false;
		}
		return true;
	}

	@Override
	public String toString() {
		return "OrderUnitMapping [unitId=" + unitId + ", terminalId=" + terminalId + ", orderSource=" + orderSource
				+ ", employeeId=" + employeeId + "]";
	}

}