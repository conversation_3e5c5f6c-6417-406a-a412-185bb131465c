package com.stpl.tech.kettle.partner.reporting.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderItemTaxDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.domain.model.OrderItemOHC;
import com.stpl.tech.kettle.domain.model.OrderOHC;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.OrderSummaryOHC;
import com.stpl.tech.kettle.domain.model.PaymentsOHC;
import com.stpl.tech.kettle.domain.model.TaxesOHC;
import com.stpl.tech.kettle.partner.reporting.dao.PartnerReportDao;
import com.stpl.tech.kettle.partner.reporting.service.PartnerReportService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.partnerReport.dao.ExternalPartnerReportDao;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.util.AppUtils;

@Component
public class PartnerReportServiceImpl implements PartnerReportService {
	@Autowired
	private PartnerReportDao partnerReportDao;
	
	@Autowired 
	private ExternalPartnerReportDao externalDao;

	private static final String SALE = "Sale";

	private static final String NOTSALE = "Void";

	private static final Logger LOG = LoggerFactory.getLogger(PartnerReportServiceImpl.class);

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public OrderSummaryOHC getOrderReportOHC(Date businessDate, int unitId, String storeId) throws DataNotFoundException {

		LOG.info(" businessDate : " + businessDate);
		List<UnitClosureDetails> closureList = partnerReportDao.getClosureForBusinessDate(businessDate);

		if (closureList.size() == 0) {
			LOG.info(" Day closure not done so report is not generated : " + businessDate);
			return null;
		}

		List<OrderDetail> list = partnerReportDao.getOrderDetailsOfUnit(unitId, businessDate);
		OrderSummaryOHC orderSummaryOHC = new OrderSummaryOHC();
		BigDecimal grossSaleAmt = BigDecimal.ZERO;
		BigDecimal totalNetTaxAmt = BigDecimal.ZERO;
		BigDecimal totalNetSaleAmt = BigDecimal.ZERO;
		BigDecimal cashAmt = BigDecimal.ZERO;
		BigDecimal cardAmt = BigDecimal.ZERO;
		BigDecimal miscPayAmt = BigDecimal.ZERO;
		int transCount = 0;

		orderSummaryOHC.setPOSDate(AppUtils.generateSimpleDateFormat(businessDate));
		if(list.size() >0){
			orderSummaryOHC.setFileId(list.get(0).getUnitOrderId());
		}
		orderSummaryOHC.setStoreId(storeId);

		for (OrderDetail order : list) {
			if ((order.getOrderType() != null && !isOnlyGiftCardOrder(order)
					&& (order.getOrderType().equals("order") || order.getOrderType().equals("paid-employee-meal")))) {
				transCount++;
				OrderOHC orderOHC = new OrderOHC();
				if (order.getOrderStatus().equals(OrderStatus.SETTLED.name())) {
					orderOHC.setTransType(SALE);
				} else {
					orderOHC.setTransType(NOTSALE);
				}
				if (order.getOrderSource().equals("CAFE")) {
					orderOHC.setServedAt("In-Store");
				} else {
					orderOHC.setServedAt("HomeDelivery");
				}

				orderOHC.setBillNo(String.valueOf(order.getOrderId()));
				orderOHC.setBillDate(AppUtils.generateSimpleDateFormat(order.getBillingServerTime()));
				orderOHC.setTotalBillAmt(order.getTaxableAmount());
				totalNetSaleAmt = AppUtils.add(totalNetSaleAmt, orderOHC.getTotalBillAmt());

				orderOHC.setPayableAmt(order.getSettledAmount());
				grossSaleAmt = AppUtils.add(grossSaleAmt, orderOHC.getPayableAmt());

				orderOHC.setRoundAmt(order.getRoundOffAmount());
				orderOHC.setTaxAmt(order.getTaxAmount());
				totalNetTaxAmt = AppUtils.add(totalNetTaxAmt, orderOHC.getTaxAmt());

				for (OrderItem item : order.getOrderItems()) {

					OrderItemOHC itemOHC = new OrderItemOHC();
					itemOHC.setInventory(true);
					
					ProductDetail productDetail = externalDao.getProductDetail(item.getProductId());
					itemOHC.setCode(productDetail.getShortCode());
					itemOHC.setDesc(item.getProductName());
					itemOHC.setProductCategory(productDetail.getProductType().getRtlName());
					itemOHC.setMrp(item.getPrice());
					itemOHC.setUnitSellPrice(item.getPrice());
					itemOHC.setUnits(BigDecimal.valueOf(item.getQuantity()));
					itemOHC.setDiscountUnits(BigDecimal.ZERO);

					itemOHC.setSaleAmt(AppUtils.add(item.getPaidAmount(), item.getTaxAmount()));
					itemOHC.setTaxableAmt(item.getPaidAmount());
					itemOHC.setNetItemAmt(item.getTotalAmount());
					itemOHC.setNetDiscAmt(item.getDiscountAmount());
					itemOHC.setNetTaxAmt(item.getTaxAmount());
					itemOHC.setTaxInclusive(true);// for gift card what would
													// me the value
					itemOHC.setLineItemRoundOff(BigDecimal.ZERO);

					for (OrderItemTaxDetail taxes : item.getOrderItemTaxes()) {
						TaxesOHC taxesOHC = new TaxesOHC();
						taxesOHC.setTaxCategory(taxes.getTaxType());
						taxesOHC.setTaxCode(taxes.getTaxCode().split("/")[0]);
						taxesOHC.setTaxPercent(taxes.getTaxPercentage());
						taxesOHC.setTaxableAmount(taxes.getTaxableAmount());
						taxesOHC.setTaxAmount(taxes.getTotalTax());

						itemOHC.getTaxes().add(taxesOHC);
					}

					orderOHC.getItems().add(itemOHC);
				}

				for (OrderSettlement settlements : order.getOrderSettlements()) {
					PaymentsOHC paymentsOHC = new PaymentsOHC();
					
					PaymentMode paymentMode = externalDao.getPaymentMode(settlements.getPaymentModeId());
					paymentsOHC.setPaymentType(paymentMode.getModeType());
					paymentsOHC.setPayCode(paymentMode.getModeName());
					paymentsOHC.setPayAmount(settlements.getAmountPaid());
					paymentsOHC.setMode("T");
					switch (paymentsOHC.getPayCode()) {
					case "CreditDebitCard":
						cardAmt = AppUtils.add(cardAmt, paymentsOHC.getPayAmount());
						break;
					case "Cash":
						cashAmt = AppUtils.add(cashAmt, paymentsOHC.getPayAmount());
						break;
					default:
						miscPayAmt = AppUtils.add(miscPayAmt, paymentsOHC.getPayAmount());
						break;
					}
					orderOHC.getPayments().add(paymentsOHC);
				}
				orderSummaryOHC.getBills().add(orderOHC);
			}
		}

		orderSummaryOHC.setTransCount(transCount);
		orderSummaryOHC.setGrossSaleAmt(grossSaleAmt);
		orderSummaryOHC.setTotalNetTaxAmt(totalNetTaxAmt);
		orderSummaryOHC.setTotalNetSaleAmt(totalNetSaleAmt);
		orderSummaryOHC.setEdc1Ammt(cardAmt);
		orderSummaryOHC.setCashAmt(cashAmt);
		orderSummaryOHC.setMiscPayModes(miscPayAmt);

		return orderSummaryOHC;
	}
	

	private boolean isOnlyGiftCardOrder(OrderDetail order) {
		BigDecimal giftCardAmount = BigDecimal.ZERO;
		for (com.stpl.tech.kettle.data.model.OrderItem item : order.getOrderItems()) {
			if (item.getTaxCode() != null && AppUtils.isGiftCard(item.getTaxCode())) {
				giftCardAmount = AppUtils.add(giftCardAmount, AppUtils.multiply(new BigDecimal(item.getQuantity()),item.getPrice()));
			}
		}
		if(order.getSettledAmount().equals(giftCardAmount)){
			return true;
		}
		return false;
	}

}
