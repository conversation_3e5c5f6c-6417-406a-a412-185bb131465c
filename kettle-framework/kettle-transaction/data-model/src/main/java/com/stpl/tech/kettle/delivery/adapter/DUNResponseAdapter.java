/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import java.util.Date;

import com.stpl.tech.kettle.delivery.model.*;
import com.stpl.tech.util.AppUtils;

public class DUNResponseAdapter
		implements ResponseAdapter<DUNResponse, DUNErrorResponse, DUNCallbackObject, DeliveryResponse> {

	private static final Integer PARTNER_ID = 10;

	@Override
	public DeliveryResponse adapt(DUNResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		
		response.setGeneratedOrderId(orderId);
		
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryTaskId(data.getTask_id());
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryStatus(getDeliveryStatusForDUN(data.getState()));
		return response;
	}

	private int getDeliveryStatusForDUN(String status) {
		if (status.equalsIgnoreCase("created")) {
			return DeliveryStatus.ACCEPTED.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("queued")) {
			return DeliveryStatus.ACCEPTED.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("runner_cancelled")) {
			return DeliveryStatus.ACCEPTED.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("runner_accepted")) {
			return DeliveryStatus.ASSIGNED.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("reached_for_pickup")) {
			return DeliveryStatus.ARRIVED.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("pickup_complete")) {
			return DeliveryStatus.PICKEDUP.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("started_for_delivery")) {
			return DeliveryStatus.OUT_FOR_DELIVERY.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("reached_for_delivery")) {
			return DeliveryStatus.OUT_FOR_DELIVERY.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("delivered")) {
			return DeliveryStatus.DELIVERED.getDeliveryStatus();
		} else if (status.equalsIgnoreCase("cancelled")) {
			return DeliveryStatus.CANCELLED.getDeliveryStatus();
		}
		return DeliveryStatus.valueOf(status.toUpperCase()).getDeliveryStatus();
	}

	@Override
	public DeliveryResponse adaptError(DUNErrorResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setGeneratedOrderId(orderId);
		response.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		response.setFailureMessage(data != null ? data.getMessage() : null);
		return response;
	}

	@Override
	public DeliveryResponse adaptCallback(DUNCallbackObject data) {
		DeliveryResponse response = new DeliveryResponse();
		if(data.getEta() != null) {
			
		}
		if(data.getRunner() != null) {
			response.setDeliveryBoyPhoneNum(data.getRunner().getPhone_number());
			response.setDeliveryBoyName(data.getRunner().getName());
		}
		
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryStatus(getDeliveryStatusForDUN(data.getState()));
		response.setDeliveryTaskId(data.getTask_id());
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		
		if(data.getCancelled_by() != null) {
			response.setFailureCode(data.getCancelled_by());
			response.setFailureMessage(data.getCancellation_reason());
		}
		response.setStatusUpdateTime(new Date(data.getRequest_timestamp()));
		
		return response;
	}

	@Override
	public DeliveryResponse adaptCancel(String orderId, String taskId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setGeneratedOrderId(orderId);
		response.setDeliveryPartnerId(10);
		response.setDeliveryTaskId(taskId);
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryStatus(DeliveryStatus.CANCELLED.getDeliveryStatus());
		return response;
	}

	

}
