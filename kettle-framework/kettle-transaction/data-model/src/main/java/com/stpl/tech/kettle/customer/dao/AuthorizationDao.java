/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.data.model.AuthorizationRequest;

public interface AuthorizationDao {

	public String createEmailAuthorizationRequest(String emailId, String code, String text);

	public String createSMSAuthorizationRequest(String contactNumber, String code, String text);

	public boolean exists(String email, String token);

	public void expireAuthorizationRequest(String email);

    public AuthorizationRequest findAuthorizationToken(String email);
}
