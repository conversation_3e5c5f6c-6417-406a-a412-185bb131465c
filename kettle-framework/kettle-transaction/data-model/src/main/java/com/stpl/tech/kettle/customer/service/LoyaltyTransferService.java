package com.stpl.tech.kettle.customer.service;


import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.LoyaltyTransfer;
import com.stpl.tech.kettle.domain.model.webengage.survey.LoyaltyGiftingSMSToken;
import com.stpl.tech.kettle.loyaltyTransfer.model.LoyaltyTransferRequestBody;

import java.util.List;

public interface LoyaltyTransferService {
    public LoyaltyTransfer initiateLoyaltyTransfer(LoyaltyTransferRequestBody loyaltyTransferRequestBody, CustomerInfo customerInfo);

    public LoyaltyTransfer createLoyaltyTransfer(LoyaltyTransferRequestBody loyaltyTransferRequestBody, LoyaltyScore loyaltyScore, LoyaltyTransfer loyaltyTransfer);

    public LoyaltyScore getLoyalityScore(int customerId);

    public  String getCustomerFullName(CustomerInfo customerInfo);

    public CustomerInfo getCustomerInfoByContactNumber(String contactNumber);

    public  LoyaltyTransfer getLoyalityTransferByEventId(int eventId);

    public void addLoyaltyTransferStatusLog(String fromStatus,String toStatus, int eventId);

    public List<LoyaltyTransfer> getAllSentGift(int customerId);

    public List<LoyaltyTransfer> getAllReceivedGift(String customerNumber);

    public List<LoyaltyTransfer> getNotClaimedGift(int customerId);

    public List<LoyaltyTransfer> getNotClaimedReceivedGift(String contactNumber);

    public List<LoyaltyTransfer> getClaimedGift(int customerId);

    public List<LoyaltyTransfer> getClaimedReceivedGift(String contactNumber);

    public LoyaltyTransfer cancelGift(int eventId, LoyaltyScore loyaltyScore);

    public LoyaltyTransfer claimGift(LoyaltyTransfer loyaltyTransfer, LoyaltyScore loyaltyScoreSender);

    public void markExpired();

    public boolean sendLoyaltyGiftingReminderSMS(LoyaltyGiftingSMSToken loyaltyGiftingSMSToken, String contactNumber);
}
