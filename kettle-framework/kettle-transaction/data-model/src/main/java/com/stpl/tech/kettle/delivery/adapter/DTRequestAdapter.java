/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.DTCancellationRequest;
import com.stpl.tech.kettle.delivery.model.DTOrderRequest;
import com.stpl.tech.kettle.delivery.model.DTRequest;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Unit;
import org.apache.commons.lang.WordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class DTRequestAdapter implements RequestAdapter<DTRequest, OrderInfo> {

	private static final Logger LOG = LoggerFactory.getLogger(DTRequestAdapter.class);

	private static final String USER_TYPE = "Restaurant";

	private enum DTDeliveryType {
		Prepaid, Postpaid;
	}

	@Override
	public DTRequest adaptCreate(OrderInfo data) {

		LOG.info("Inside adapt create method DT EXECUTOR");
		DTOrderRequest request = new DTOrderRequest();
		Order order = data.getOrder();
		Customer customer = data.getCustomer();
		Address customerAddress = TransactionUtils.getAddressForOrder(order.getDeliveryAddress(), customer.getAddresses());
		Unit unit = data.getUnit();
		DTDeliveryType type = getDeliveryType(order.getSettlements());
		String amount = type.equals(DTDeliveryType.Postpaid)
				? order.getTransactionDetail().getPaidAmount().toString()
				: String.valueOf(0);

		request.setPaymentmode(type.toString());
		request.setAddress(customerAddress.toString());
		request.setAmount(amount);
		request.setCustomernumber(customer.getContactNumber());
		request.setLat(unit.getAddress().getLatitude());
		request.setLon(unit.getAddress().getLongitude());
		request.setName(customer.getFirstName());
		request.setOrdername("Chaayos Delivery");
		request.setPincode(unit.getAddress().getZipCode());
		request.setCity(unit.getAddress().getCity());
		request.setRestname(unit.getName());
		request.setStatus("Pending");
		request.setArea(customerAddress.getLocality());
		request.setRestaurantcontact(unit.getAddress().getContact1());
		request.setRestAddress(unit.getAddress().toString());
		return request;
	}

	private DTDeliveryType getDeliveryType(List<Settlement> settlements) {
		LOG.info("Inside getDeliveryType method DT EXECUTOR");
		DTDeliveryType deliveryType = DTDeliveryType.Postpaid;
		if (settlements.size() == 1) {
			deliveryType = settlements.get(0).getMode() == 1
					? DTDeliveryType.Postpaid : DTDeliveryType.Prepaid;
		}
		return deliveryType;
	}

	@Override
	public DTRequest adaptCancel(OrderInfo orderInfo, String taskId) {
		LOG.info("Inside adaptCancel method DT EXECUTOR");

		DTCancellationRequest cancellationRequest = new DTCancellationRequest();
		cancellationRequest.setStatus(WordUtils.capitalize(DeliveryStatus.CANCELLED.toString()));
		cancellationRequest.setOrderId(taskId);
		cancellationRequest.setUsertype(USER_TYPE);

		return cancellationRequest;
	}
}
