package com.stpl.tech.kettle.customer.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.CashCardStatus;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.GiftOffer;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.customer.dao.CashCardDao;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardOffer;
import com.stpl.tech.kettle.domain.model.CashCardEventStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.GiftCardActivationRequest;
import com.stpl.tech.util.AppUtils;

@Service
public class CardServiceImpl implements CardService {

	@Autowired
	private CashCardDao cardDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CashCardDetail createCashCardDetail(BigDecimal amount, int customerId, int orderId,
											   Date startDate, Date endDate){
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CashCardDetail getCardDetail(int customerId, String cardNumber, boolean runValidations)
			throws CardValidationException {
		return cardDao.getCardDetail(customerId, cardNumber, runValidations);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<GiftOffer> getCurrentCardOffer(int unitId, Integer partnerId) {
		return cardDao.getCurrentCardOffer(unitId, partnerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CashCardDetail> getCardDetails(int customerId) throws CardValidationException {
		return cardDao.getCardDetails(customerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CashCardDetail getCardDetail(String code) throws CardValidationException {
		return cardDao.getCardDetail(0, code, false);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CashCardDetail getCardDetailBySerial(String serial) throws CardValidationException {
		return cardDao.getCardDetail(serial);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean activateCashCard(GiftCardActivationRequest giftCardActivationRequest)
			throws CardValidationException, DataUpdationException {
		CashCardDetail cashCardDetail = null;
		if (giftCardActivationRequest.getRequestingEmployee() != 0 && giftCardActivationRequest.getReason() != null
				&& (giftCardActivationRequest.getCardNumber() != null
						|| giftCardActivationRequest.getCardSerial() != null)) {
			cashCardDetail = cardDao.getCardDetailForActivation(giftCardActivationRequest);
			if (cashCardDetail.getCardStatus().equals(CashCardStatus.INITIATED.name())) {
				cashCardDetail.setCardStatus(CashCardStatus.READY_FOR_ACTIVATION.name());
				cashCardDetail.setActivationTime(AppUtils.getCurrentTimestamp());
				cashCardDetail = (CashCardDetail) cardDao.update(cashCardDetail);
				cardDao.logCashCardEvent(cashCardDetail, CashCardEventStatus.CARD___ACTIVATION___SUCCESS,
						giftCardActivationRequest);
				return true;
			} else {
				cardDao.logCashCardEvent(cashCardDetail, CashCardEventStatus.CARD___ACTIVATION___FAILED,
						giftCardActivationRequest);
				throw new CardValidationException("Card is not valid!");
			}
		} else {
			throw new CardValidationException("Please enter valid employee, unit and card details.");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void deactivateCashCards() {
		cardDao.deactivateCashCards();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CashCardDetail getCardDetail(int cardId) {
		return cardDao.getCardDetail(cardId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerCardInfo getCashCardAmount(int customerId) {
		return cardDao.getCashCardAmount(customerId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.CardService#getCardsByPurchaseOrderId(java.
	 * lang.Integer)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CashCardDetail> getCardsByPurchaseOrderId(Integer orderId) {
		return cardDao.getCardsByPurchaseOrderId(orderId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.CardService#getUnitsCurrentsOffer()
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getUnitsWithCardOffersForToday() {
		return cardDao.getUnitsWithCardOffersForToday();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CashCardDetail> getActiveCashCards(int customerId) {
		return cardDao.getActiveCashCards(customerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CashCardOffer> getAllCashCardOffers(Date businessDate) {
		return cardDao.getAllCashCardOffers(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CashCardOffer> addCashCardOffers(List<CashCardOffer> list) throws CardValidationException {
		return cardDao.addCashCardOffers(list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CashCardOffer> changeStatusAllCashCardOffers(List<CashCardOffer> list) {
		return cardDao.changeStatusAllCashCardOffers(list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CashCardOffer> getCashCardOffersForDate(Date startDate, Date endDate, Integer partnerId) {
		return cardDao.getCashCardOffersForDate(startDate, endDate, partnerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public BigDecimal getGiftCardOffer(Integer purchaseOrderId) {
		return cardDao.getGiftCardOffer(purchaseOrderId);
	}

}
