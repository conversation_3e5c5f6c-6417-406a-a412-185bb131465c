package com.stpl.tech.kettle.data.converter;

import java.util.Map;
import java.util.Objects;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.data.model.CustomerDataLookup;
import com.stpl.tech.kettle.domain.model.DataEncryptionRequest;
import com.stpl.tech.spring.crypto.DataEncrypter;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CustomerInfoDataConverter {

	@Autowired
	private DataEncrypter dataEncrypter;

	public CustomerDataLookup convert(CustomerDataLookup lookup, Map<String, Object> customer,
			DataEncryptionRequest request, Cipher cipher) throws IllegalBlockSizeException, BadPaddingException {
		if (Objects.isNull(lookup)) {
			lookup = new CustomerDataLookup();
		}
		String idAttr = request.getIdAttr();
		String emailAttr = request.getEmailAttr();
		String contactAttr = request.getContactAttr();
		lookup.setId((int) customer.get(idAttr));
		lookup.setContactNumber((String) customer.get(contactAttr));
		lookup.setEmailId((String) customer.get(emailAttr));
		lookup.setType(request.getType());
		boolean refresh = request.getRefresh();
		if (Objects.nonNull(customer.get(contactAttr))) {
			if (refresh) {
				lookup.setContactNumberData(dataEncrypter.encryptData((String) customer.get(contactAttr),
						(int) customer.get(idAttr), cipher));
			} else {
				lookup.setContactNumberData(dataEncrypter.encryptData((String) customer.get(contactAttr)));
			}
		}
		if (Objects.nonNull(customer.get(emailAttr))) {
			if (refresh) {
				lookup.setEmailIdData(dataEncrypter.encryptData((String) customer.get(emailAttr),
						(int) customer.get(idAttr), cipher));
			} else {
				lookup.setEmailIdData(dataEncrypter.encryptData((String) customer.get(emailAttr)));
			}
		}
		return lookup;
	}

}
