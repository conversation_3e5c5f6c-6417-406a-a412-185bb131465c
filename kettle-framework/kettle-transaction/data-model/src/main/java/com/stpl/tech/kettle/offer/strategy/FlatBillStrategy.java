/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.util.AppUtils;

public class FlatBillStrategy extends AbstractBillStrategy implements OfferActionStrategy {

	StringBuffer offerMessage = new StringBuffer();
	String messageTemplate = "Applied Flat discount of Rs.%d on order <br/>";

	/**
	 * FLAT BILL = Apply flat value discount to this order.
	 * <p>
	 * Here we will update the discount details of the order. Rest of the
	 * calculations will be done by the UI before punching of the order.
	 * 
	 * @throws OfferValidationException
	 * 
	 */
	@Override
	public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems)
			throws OfferValidationException {

		BigDecimal flatDiscount = new BigDecimal(coupon.getOffer().getOfferValue());

		return applyStrategy(String.format(messageTemplate, flatDiscount.intValue()), flatDiscount, offerOrder, coupon, cache);

	}

	@Override
	public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems, BigDecimal offerValue)
			throws OfferValidationException {

		return applyStrategy(String.format(messageTemplate, offerValue.intValue()), offerValue, offerOrder, coupon, cache);

	}

	protected OfferOrder applyStrategy(String message, BigDecimal flatDiscount, OfferOrder offerOrder, CouponDetail coupon,
			MasterDataCache cache) {
		TransactionDetail trans = offerOrder.getOrder().getTransactionDetail();

		if (flatDiscount.compareTo(trans.getPaidAmount()) > 0) {
			flatDiscount = trans.getPaidAmount();
		}

		BigDecimal taxRate = trans.getPaidAmount().divide(trans.getTaxableAmount(), 10, BigDecimal.ROUND_HALF_UP);
		BigDecimal calculatedDiscount = flatDiscount.divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
		BigDecimal discountPercentage = AppUtils.percentage(calculatedDiscount, trans.getTaxableAmount(), 10);
		offerMessage.append(message);
		updateOrderDiscount(offerOrder, coupon.getCode(), discountPercentage);
		updateOrderItemDiscount(offerOrder, coupon.getCode(), discountPercentage);
		offerOrder.setAppliedOfferMessage(getOfferMessage());
		return offerOrder;
	}

	@Override
	public String getOfferMessage() {
		return offerMessage.toString();
	}

	@Override
	public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
			BigDecimal paidAmount, BigDecimal maxDiscountValue){
		if (discount.compareTo(paidAmount) > 0) {
			discount = paidAmount;
		}

		BigDecimal taxRate = paidAmount.divide(totalAmount, 10, BigDecimal.ROUND_HALF_UP);
		BigDecimal calculatedDiscount = discount.divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
		BigDecimal discountPercentage = AppUtils.percentage(calculatedDiscount, totalAmount, 10);
		return getOrderDiscount(couponCode, discountPercentage, totalAmount);
	}

}
