/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.util.AppUtils;

import org.apache.poi.ss.formula.eval.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PercentageItemStrategy extends AbstractItemStrategy implements OfferActionStrategy {
	private static final Logger LOG = LoggerFactory.getLogger(PercentageItemStrategy.class);

	StringBuffer offerMessage = new StringBuffer();
	String messageTemplate = "Applied discount of %.2f on %s <br/>";

	@Override
	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems) throws OfferValidationException {
		LOG.info("Applying percentage discount to the offer order");
		return applyDiscountStrategy(coupon, order, cache);
	}

	@Override
	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache,
			Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException {
		if (offerValue == null) {
			return applyStrategy(order, coupon, cache, foundItems);
		} else {
			throw new NotImplementedException("Not Implemented the method for Percentage Item Strategy");
		}
	}

	@Override
	public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {
		LOG.info("add DiscountDetails of percent item strategy");
		BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
		int discountQuantity = counter < orderItem.getQuantity()
								? counter
								: orderItem.getQuantity();
		BigDecimal discountOnPrice = orderItem.getPrice().multiply(BigDecimal.valueOf(discountQuantity))
				.setScale(2, BigDecimal.ROUND_HALF_UP);
		BigDecimal valueDiscount = AppUtils.percentOf(discountInOffer, discountOnPrice);

		BigDecimal effectivePercentage = AppUtils.percentage(valueDiscount, totalAmount);
		offerMessage.append(String.format(messageTemplate, valueDiscount.floatValue(), orderItem.getProductName()));
		return getModifiedItem(orderItem, effectivePercentage, valueDiscount);
	}

	public StringBuffer getMessageTemplate(){
	    return offerMessage;
    }

	@Override
	public String getOfferMessage() {
		return offerMessage.toString();
	}

	@Override
	public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
			BigDecimal paidAmount, BigDecimal maxDiscountValue) {
		return null;
	}

}
