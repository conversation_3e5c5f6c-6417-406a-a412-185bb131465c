/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.data.vo;

import com.stpl.tech.kettle.domain.model.AttributeType;

public class ReportParameter {

	private String name;
	private String code;
	private String value;
	private AttributeType type;

	public ReportParameter(String name, String code, String value, AttributeType type) {
		super();
		this.name = name;
		this.code = code;
		this.value = value;
		this.type = type;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public void setType(AttributeType type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}

	public String getValue() {
		return value;
	}

	public AttributeType getType() {
		return type;
	}

}
