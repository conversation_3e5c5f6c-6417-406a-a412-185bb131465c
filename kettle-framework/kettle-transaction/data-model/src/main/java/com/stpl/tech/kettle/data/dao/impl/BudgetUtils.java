/**
 *
 */
package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.core.data.budget.vo.AdjustmentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.DirectCost;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ScoreCardCategory;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.UnitExpenditure;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.Column;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class BudgetUtils {

    private static final Logger LOG = LoggerFactory.getLogger(BudgetUtils.class);

    public static void setExpenseAggregate(UnitExpenditureDetail d, ExpenseAggregate b) {
        d.setVehicleRegularMaintenance(b.getVehicleRegularMaintenance());
        d.setParkingCharges(b.getParkingCharges());
        d.setEnergyDGRunningCafe(b.getEnergyDGRunningCafe());
        d.setMarketingNPI(b.getMarketingNPI());
        d.setTravellingExpense(b.getTravellingExpense());
        d.setWaterChargesCafe(b.getWaterChargesCafe());
        d.setMaintenancePestControlCafe(b.getMaintenancePestControlCafe());
        d.setConveyanceOdc(b.getConveyanceOdc());
//		d.setConveyanceOdc(b.getTravellingExpenseODC());
        d.setExpenseOthers(b.getExpenseOthers());
        d.setBuildingMaintenance(b.getBuildingMaintenance());
        d.setComputerMaintenance(b.getComputerMaintenance());
        d.setEquipmentMaintenance(b.getEquipmentMaintenance());
        d.setCommissionChangeCafe(b.getCommissionChangeCafe());
        d.setFuelChargesCafe(b.getFuelChargesCafe());
        d.setPhotoCopyExpensesCafe(b.getPhotoCopyExpensesCafe());
        d.setPrintingAndStationaryCafe(b.getPrintingAndStationaryCafe());
        d.setStaffWelfareExpensesCafe(b.getStaffWelfareExpensesCafe());
        d.setCleaningChargesCafe(b.getCleaningChargesCafe());
        d.setBusinessPromotionCafe(b.getBusinessPromotionCafe());
        d.setCourierChargesCafe(b.getCourierChargesCafe());
        d.setNewsPaperCafe(b.getNewsPaperCafe());

        d.setCogsOthers(b.getCogsOthers());
        d.setConveyanceOperations(b.getConveyanceOperation());
        d.setMarketingDataAnalysis(b.getMarketingDataAnalysis());
        d.setConveyanceOthers(b.getMarketingDataAnalysis());
        d.setConveyanceMarketing(b.getConveyanceMarketing());

    }

    public static void setElectricityAggregate(UnitExpenditureDetail d, ElectricityAggregate b) {
        d.setEnergyElectricity(b.getEnergyElectricity());
    }

    public static Map<String, Map<String, UnitExpenditure>> getAnnotations(Class<?> aClass) {
        Map<String, Map<String, UnitExpenditure>> categoryMap = new HashMap<>();
        for (Field field : aClass.getDeclaredFields()) {
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;

                if ((expenseField.category() == ScoreCardCategory.SALES
                        || expenseField.category() == ScoreCardCategory.KEY_FIELDS
                        || expenseField.category() == ScoreCardCategory.PROFIT)
                        && expenseField.orderLabel().getLabel().length() > 0) {
                    if (categoryMap.get(expenseField.category().name()) == null) {
                        Map<String, UnitExpenditure> detailMap = new HashMap<>();
                        categoryMap.put(expenseField.category().name(), detailMap);
                    }
                    Map<String, UnitExpenditure> detailMap = categoryMap.get(expenseField.category().name());
                    UnitExpenditure expenditure = copyAttributes(expenseField);
                    List<String> list = new ArrayList<>();
                    list.add(field.getName());
                    expenditure.setEntitys(list);
                    detailMap.put(expenseField.detail(), expenditure);
                } else if (expenseField.orderLabel().getLabel().length() > 0) {
                    if (categoryMap.get(expenseField.category().name()) == null) {
                        Map<String, UnitExpenditure> detailMap = new HashMap<>();
                        UnitExpenditure expenditure = copyAttributes(expenseField);
                        List<String> list = new ArrayList<>();
                        list.add(field.getName());
                        expenditure.setEntitys(list);
                        detailMap.put(expenseField.category().name(), expenditure);
                        categoryMap.put(expenseField.category().name(), detailMap);
                    } else {
                        Map<String, UnitExpenditure> detailMap = categoryMap.get(expenseField.category().name());
                        detailMap.get(expenseField.category().name()).getEntitys().add(field.getName());
                    }
                }
            }
        }
        return categoryMap;
    }

    public static void main(String[] args) {
        Class<?> aClass = UnitExpenditureDetail.class;
        for (Field field : aClass.getDeclaredFields()) {
            System.out.print(field.getName() + ", ");
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;
                System.out.print(expenseField.detail() + ", " + expenseField.category() + ", ");
            }
            for (Method m : aClass.getDeclaredMethods()) {
                if (m.getName().replace("get", "").equalsIgnoreCase(field.getName())) {
                    Annotation annotation2 = m.getDeclaredAnnotation(Column.class);
                    if (annotation2 instanceof Column) {
                        Column col = (Column) annotation2;
                        System.out.print(col.name() + ", ");
                    }
                }
            }
            System.out.println("");
        }
    }

    private static UnitExpenditure copyAttributes(ExpenseField field) {
        UnitExpenditure expenditure = new UnitExpenditure();
        expenditure.setCalculationType(field.calculationType().name());
        expenditure.setCategory(field.category().name());
        expenditure.setLabel(field.orderLabel().getLabel());
        expenditure.setSplit(field.orderLabel().isSplit());
        expenditure.setOrder(field.order());
        return expenditure;
    }

    public static List<UnitExpenditure> getDetails(UnitExpenditureDetail current, UnitExpenditureDetail detail,
                                                   UnitBudgetoryDetail ubd, boolean skipCalculatedFields) {
        List<UnitExpenditure> resultList = new ArrayList<>();
        if (detail == null) {
            return resultList;
        }
        Map<String, Map<String, UnitExpenditure>> resultMap = BudgetUtils.getAnnotations(UnitExpenditureDetail.class);
        for (Map.Entry<String, Map<String, UnitExpenditure>> map : resultMap.entrySet()) {
            if (skipCalculatedFields && map.getKey().equals("PROFIT")) {
                continue;
            }
            switch (map.getKey()) {
                case "SALES":
                    for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                        UnitExpenditure expenditure = salesMap.getValue();
                        expenditure.setValue(getReadMethodValue(expenditure.getEntitys().get(0), detail));
                        if (current != null) {
                            expenditure.setCurrent(getReadMethodValue(expenditure.getEntitys().get(0), current));
                        }
                        if (ubd != null) {
                            expenditure.setBudget(getReadMethodValue(expenditure.getEntitys().get(0), ubd));
                        }
                        resultList.add(expenditure);
                    }
                    break;
                case "KEY_FIELDS":
                    for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                        UnitExpenditure expenditure = salesMap.getValue();
                        expenditure.setValue(getReadMethodValue(expenditure.getEntitys().get(0), detail));
                        if (current != null) {
                            expenditure.setCurrent(getReadMethodValue(expenditure.getEntitys().get(0), current));
                        }
                        if (ubd != null) {
                            expenditure.setBudget(getReadMethodValue(expenditure.getEntitys().get(0), ubd));
                        }
                        resultList.add(expenditure);
                    }
                    break;
                case "PROFIT":
                    for (Map.Entry<String, UnitExpenditure> salesMap : map.getValue().entrySet()) {
                        UnitExpenditure expenditure = salesMap.getValue();
                        expenditure.setValue(getReadMethodValue(expenditure.getEntitys().get(0), detail));
                        if (current != null) {
                            expenditure.setCurrent(getReadMethodValue(expenditure.getEntitys().get(0), current));
                        }
                        if (ubd != null) {
                            expenditure.setBudget(getReadMethodValue(expenditure.getEntitys().get(0), ubd));
                        }
                        resultList.add(expenditure);
                    }
                    break;
                default:

                    UnitExpenditure expenditure = map.getValue().get(map.getKey());
                    BigDecimal value = BigDecimal.ZERO;
                    BigDecimal currentValue = BigDecimal.ZERO;
                    BigDecimal budget = BigDecimal.ZERO;
                    for (String method : expenditure.getEntitys()) {
                        value = AppUtils.add(value, (BigDecimal) getReadMethodValue(method, detail));
                        if (current != null) {
                            currentValue = AppUtils.add(currentValue, (BigDecimal) getReadMethodValue(method, current));
                        }
                        if (ubd != null) {
                            budget = AppUtils.add(budget, (BigDecimal) getReadMethodValue(method, ubd));
                        }
                    }
                    expenditure.setValue(value);
                    expenditure.setCurrent(currentValue);
                    expenditure.setBudget(budget);
                    resultList.add(expenditure);
                    break;
            }
        }
        return resultList;

    }

    public static BigDecimal getBudgetAmount(String budgetCategory, UnitBudgetoryDetail ubd) {
        List<String> fieldName = getFieldslist(ExpenseField.ExpenseRecordCategory.valueOf(budgetCategory));
        BigDecimal budget = BigDecimal.ZERO;
        for (String method : fieldName) {
            budget = AppUtils.add(budget, (BigDecimal) getReadMethodValue(method, ubd));
        }
        return budget;
    }

    public static Map<String, String> getBudgetRestrictionType(ExpenseField.ExpenseRecordCategory recordCategory) {
        Class<UnitExpenditureDetail> aClass = UnitExpenditureDetail.class;
        Map<String, String> map = new HashMap<>();
        for (Field field : aClass.getDeclaredFields()) {
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;
                if (expenseField.expense().equals(recordCategory)) {
                    map.put("restrictionType", expenseField.budgetType().name());
                    map.put("label", expenseField.orderLabel().getLabel());
                }
            }
        }
        return map;
    }

    public static UnitExpenditure getAnnotationsValueOfCategory(ExpenseField.ScoreCardCategory category) {
        Class<UnitExpenditureDetail> aClass = UnitExpenditureDetail.class;
        UnitExpenditure expenditure = new UnitExpenditure();
        for (Field field : aClass.getDeclaredFields()) {
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;
                if (expenseField.category() == category) {
                    expenditure.setCategory(expenseField.category().name());
                    expenditure.setLabel(expenseField.orderLabel().getLabel());
                    expenditure.setSplit(expenseField.orderLabel().isSplit());
                    expenditure.setRestrictionType(expenseField.budgetType().name());
                    break;
                }
            }

        }
        return expenditure;
    }

    public static BigDecimal getReadMethodValue(String fieldName, ConsumablesAggregate aggregate) {
        Object result = null;
        try {
            result = new PropertyDescriptor(fieldName, ConsumablesAggregate.class).getReadMethod().invoke(aggregate);
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException
                | IntrospectionException e) {
            LOG.info(e.getMessage());
        }
        return result == null ? BigDecimal.ZERO : (BigDecimal) result;
    }

    private static List<String> getFieldslist(ExpenseField.ExpenseRecordCategory recordCategory) {
        List<String> list = new ArrayList<>();
        Class<UnitExpenditureDetail> aClass = UnitExpenditureDetail.class;
        for (Field field : aClass.getDeclaredFields()) {
            Annotation annotation = field.getDeclaredAnnotation(ExpenseField.class);
            if (annotation instanceof ExpenseField) {
                ExpenseField expenseField = (ExpenseField) annotation;
                if (expenseField.expense().equals(recordCategory)) {
                    list.add(field.getName());
                }
            }
        }
        return list;
    }

    public static void setCalculatedData(UnitExpenditureDetail detail, UnitBudgetoryDetail ubd) {
        BigDecimal netRevenue = detail.getNetRevenue();
        BigDecimal netCost = detail.grossCostValue();
        BigDecimal totalCost = detail.totalCostValue();
        BigDecimal grossProfit = AppUtils.subtract(netRevenue, netCost);
        BigDecimal netProfit = AppUtils.subtract(netRevenue, totalCost);
        BigDecimal depreciation = detail.getFixedAssetsDepreciation();
        BigDecimal netProfitAfterDepreciation = AppUtils.subtract(netProfit, depreciation);

        detail.setGrossCost(netCost);
        detail.setGrossProfit(grossProfit);
        detail.setGrossProfitPercentage(AppUtils.percentage(grossProfit, netRevenue));
        detail.setTotalCost(totalCost);
        detail.setNetProfit(netProfit);
        detail.setNetProfitPercentage(AppUtils.percentage(netProfit, netRevenue));
        detail.setNetProfitWithDepreciation(netProfitAfterDepreciation);
        detail.setNetProfitPercentageWithDepreciation(AppUtils.percentage(netProfitAfterDepreciation, netRevenue));
    }

    public static UnitExpenditureAggregateDetail setAggregateCalculatedData(UnitExpenditureDetail detail) {
        UnitExpenditureAggregateDetail aggregateDetail = new UnitExpenditureAggregateDetail();

        aggregateDetail.setTotalTickets(aggregateDetail.totalTickets(detail));
        aggregateDetail.setTotalSales(aggregateDetail.totalSales(detail));
        aggregateDetail.setTotalApc(aggregateDetail.totalApc(detail));
        aggregateDetail.setTotalGmv(aggregateDetail.totalGmv(detail));
        aggregateDetail.setTotalDiscount(aggregateDetail.totalDiscount(detail));
        aggregateDetail.setTotalDiscountLoyalTea(aggregateDetail.totalDiscountLoyalTea(detail));
        aggregateDetail.setTotalDiscountMarketing(aggregateDetail.totalDiscountMarketing(detail));
        aggregateDetail.setTotalDiscountBd(aggregateDetail.totalDiscountBd(detail));
        aggregateDetail.setTotalDiscountOps(aggregateDetail.totalDiscountOps(detail));
        aggregateDetail.setTotalDiscountEmployeeFico(aggregateDetail.totalDiscountEmployeeFico(detail));
        aggregateDetail.setDineInSales(aggregateDetail.dineInSale(detail));
        aggregateDetail.setDineInDiscount(aggregateDetail.dineInDiscount(detail));
        aggregateDetail.setDineInApc(aggregateDetail.dineInApc(detail));
        aggregateDetail.setDeliveryDiscount(aggregateDetail.deliveryDiscount(detail));
        aggregateDetail.setDeliverySales(aggregateDetail.deliverySales(detail));
        aggregateDetail.setDeliveryApc(aggregateDetail.deliveryApc(detail));
        aggregateDetail.setEmployeeMealSales(aggregateDetail.employeeMealSales(detail));
        aggregateDetail.setDirectVariableCogs(aggregateDetail.directVariableCogs(detail));
        aggregateDetail.setCogs(aggregateDetail.cogs(detail));
        aggregateDetail.setWastageExpiry(aggregateDetail.wastageExpiry(detail));
        aggregateDetail.setStockVariance(aggregateDetail.stockVariance(detail));
        aggregateDetail.setDirectVariableOthers(aggregateDetail.directVariableOthers(detail));
        aggregateDetail.setInDirectVariableCost(aggregateDetail.inDirectVariableCost(detail));
        aggregateDetail.setDirectFixedCost(aggregateDetail.directFixedCost(detail));
        aggregateDetail.setEmployeeMeal(aggregateDetail.employeeMeal(detail));
        aggregateDetail.setManpowerFixed(aggregateDetail.manpowerFixed(detail));
        aggregateDetail.setManpowerIncentive(aggregateDetail.manpowerIncentive(detail));
        aggregateDetail.setFacilitiesProperty(aggregateDetail.facilitiesProperty(detail));
        aggregateDetail.setInDirectFixedCost(aggregateDetail.inDirectFixedCost(detail));
        aggregateDetail.setConsumable(aggregateDetail.consumable(detail));
        aggregateDetail.setFacilitiesFixed(aggregateDetail.facilitiesFixed(detail));
        aggregateDetail.setSupportCommWh(aggregateDetail.supportCommWh(detail));
        aggregateDetail.setSupportOpsManagement(aggregateDetail.supportOpsManagement(detail));
        aggregateDetail.setLogistics(aggregateDetail.logistics(detail));
        aggregateDetail.setFacilitiesVariable(aggregateDetail.facilitiesVariable(detail));
        aggregateDetail.setCommissionCardsWallets(aggregateDetail.commissionCardWallets(detail));
        aggregateDetail.setCommissionCp(aggregateDetail.commissionCp(detail));
        aggregateDetail.setDeliveryCharges(aggregateDetail.deliveryCharges(detail));
        aggregateDetail.setAnyOtherVariable(aggregateDetail.anyOtherVariable(detail));
        aggregateDetail.setMaintenance(aggregateDetail.maintenance(detail));
        aggregateDetail.setMarketingLs(aggregateDetail.marketingLs(detail));
        aggregateDetail.setGrowthPnl(aggregateDetail.growthPnl(detail));
        aggregateDetail.setMarketingCorp(aggregateDetail.marketingCorp(detail));
        aggregateDetail.setAllocation(aggregateDetail.allocation(detail));
        aggregateDetail.setSupportHq(aggregateDetail.supportHq(detail));
        aggregateDetail.setFicoPayouts(aggregateDetail.ficoPayouts(detail));
        aggregateDetail.setTechnology(aggregateDetail.technology(detail));
        aggregateDetail.setGrowthCapex(aggregateDetail.growthCapex(detail));
        aggregateDetail.setCapexFixedAssets(aggregateDetail.capexFixedAssets(detail));
        aggregateDetail.setIndirectIncome(aggregateDetail.inDirectIncome(detail));
        aggregateDetail.setAmortisationDepreciation(aggregateDetail.amortisationAndDepreciation(detail));
        aggregateDetail.setGrowthSd(aggregateDetail.growthSd(detail));
        aggregateDetail.setEmployeeDiscount(aggregateDetail.employeeDiscount(detail));
        aggregateDetail.setEmpMealApc(aggregateDetail.empMealApc(detail));
        aggregateDetail.calculateAggregates();
        return aggregateDetail;
    }

    public static Object getReadMethodValue(String fieldName, UnitExpenditureDetail ud) {
        Object result = null;
        try {
            result = new PropertyDescriptor(fieldName, UnitExpenditureDetail.class).getReadMethod().invoke(ud);
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException
                | IntrospectionException e) {
            LOG.info(e.getMessage());
        }
        return result;
    }

    public static Object getReadMethodValue(String fieldName, UnitBudgetoryDetail ubd) {
        Object result = null;
        try {
            result = new PropertyDescriptor(fieldName, UnitBudgetoryDetail.class).getReadMethod().invoke(ubd);
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException
                | IntrospectionException e) {
            LOG.info(e.getMessage());
        }
        return result;
    }

    public static void setDirectCost(UnitExpenditureDetail d, DirectCost b) {
        d.setSalary(b.getSalary());
//        d.setInsurnaceMedical(b.getInsurnaceMedical());
        d.setSalaryIncentive(b.getSalaryIncentive());
        //d.setSecurityGuardCharges(b.getSecurityGuardCharges());
        d.setSalesIncentive(b.getSalesIncentive());
        d.setDepreciationOfBike(b.getDepreciationOfBike());
        //d.setLogisticCharges(b.getLogisticCharges());
        d.setWaterCharges(b.getWaterCharges());
        d.setEnergyDGRunning(b.getEnergyDGRunning());

        d.setPropertyTax(b.getPropertyTax());
        d.setOpeningLicencesFees(b.getOpeningLicencesFees());
//        d.setRegistrationCharges(b.getRegistrationCharges());
//        d.setStampDutyCharges(b.getStampDutyCharges());
        d.setDesigningFees(b.getDesigningFees());
//        d.setProntoAMC(b.getProntoAMC());
//        d.setDgRental(b.getDgRental());
//        d.setEdcRental(b.getEdcRental());
//        d.setSystemRental(b.getSystemRental());
//        d.setRoRental(b.getRoRental());
//        d.setInsuranceAssets(b.getInsuranceAssets());
//        d.setInsuranceCGL(b.getInsuranceCGL());
        d.setInsuranceDnO(b.getInsuranceDnO());

        d.setFixCAM(b.getFixCAM());
        d.setChillingCharges(b.getChillingCharges());
        d.setMarketingCharges(b.getMarketingCharges());
//        d.setPettyCashRentals(b.getPettyCashRentals());
//        d.setMusicRentals(b.getMusicRentals());
        d.setInternetPartnerRental(b.getInternetPartnerRental());
        d.setSupportOpsManagement(b.getSupportOpsManagement());
//        d.setTechnologyPlatformCharges(b.getTechologyPlatformCharges());

//        d.setTechnologyOthers(b.getTechologyOthers());
        d.setTechnologyVariable(b.getTechnologyVariable());

        d.setEnergyElectricity(b.getEnergyElectricity());
        d.setEnergyDGRunning(b.getEnergyDGRunning());

//        d.setBadDebtsWrittenOff(b.getBadDebtsWrittenOff());

        d.setServiceChargesPaid(b.getServiceChargesPaid());
        d.setInsuranceVehicle(b.getInsuranceVehicle());
//        d.setOthersAMC(b.getOthersAMC());
//        d.setOthersMaintenance(b.getOthersMaintenance());
        d.setByodCharges(b.getByodCharges());
        d.setCarLease(b.getCarLease());
        d.setDriverSalary(b.getDriverSalary());
        d.setGratuity(b.getGratuity());
//        d.setInsurnaceAccidental(b.getInsurnaceAccidental());
//        d.setInsurnaceMedical(b.getInsurnaceMedical());
        d.setSupportsOpsTurnover(b.getSupportsOpsTurnover());
//        d.setEmployeeFacilitationExpenses(b.getEmployeeFacilitationExpenses());
        d.setTelephoneSR(b.getTelephoneSR());
        d.setVehicleRunningAndMaintSR(b.getVehicleRunningAndMaintSR());
        d.setEmployeeStockOptionExpense(b.getEmployeeStockOptionExpense());
        d.setEmployerContributionLWF(b.getEmployerContributionLWF());
        d.setEsicEmployerCont(b.getEsicEmployerCont());
        d.setLeaveTravelReimbursement(b.getLeaveTravelReimbursement());
        d.setPfAdministrationCharges(b.getPfAdministrationCharges());
        d.setPfEmployerCont(b.getPfEmployerCont());
        d.setQuarterlyIncentive(b.getQuarterlyIncentive());
//        d.setSupportAudit(b.getSupportAudit());
        d.setSupportCCC(b.getSupportCCC());
        d.setSupportIT(b.getSupportIT());
//        d.setSupportMaintenance(b.getSupportMaintenance());
        d.setSupportCommWH(b.getSupportCommWH());

        d.setPreOpeningConsumable(b.getPreOpeningConsumable());
        d.setPreOpeningOthers(b.getPreOpeningOthers());
        d.setPreOpeningRent(b.getPreOpeningRent());
        d.setPreOpeningSalary(b.getPreOpeningSalary());
        d.setBankCharges(b.getBankCharges());
        d.setInterestOnLoan(b.getInterestOnLoan());
        d.setIntrestOnTDSorGST(b.getIntrestOnTDSorGST());
        d.setInterestOnFDR(b.getInterestOnFDR());
        d.setProfitSaleMutualFunds(b.getProfitSaleMutualFunds());
        d.setInterestIncomeTaxRefund(b.getInterestIncomeTaxRefund());
        d.setMiscIncome(b.getMiscIncome());
        d.setDiscountReceived(b.getDiscountReceived());
        d.setInteriorDesigningCharge(b.getInteriorDesigningCharge());
        d.setScrape(b.getScrape());
        d.setServiceCharges(b.getServiceCharges());
        d.setServiceChargesFICO(b.getServiceChargesFICO());

        d.setBusinessPromotion(b.getBusinessPromotion());
        d.setBusinessPromotionSR(b.getBusinessPromotionSR());
        d.setRoundedOff(b.getRoundedOff());
//        d.setShortAndExcess(b.getShortAndExcess());

        d.setRoAMC(b.getRoAMC());
//        d.setInsuranceAssets(b.getInsuranceAssets());
//        d.setInsuranceCGL(b.getInsuranceCGL());
        d.setInsuranceDnO(b.getInsuranceDnO());
        d.setFuelChargesCafe(b.getFuelChargesCafe());

        d.setFixedParkingCharges(b.getFixedParkingCharges());
        d.setCogsLogistics(b.getCogsLogistics());
        d.setPreOpeningCamEleWater(b.getPreOpeningCamEleWater());
        d.setPreOpeningRegistrationCharges(b.getPreOpeningRegistrationCharges());
        d.setPreOpeningStampDutyCharges(b.getPreOpeningStampDutyCharges());
        d.setPreOpeningConsumableTax(b.getPreOpeningConsumableTax());
        d.setSupport(b.getSupport());
//        d.setCorporateMarketingChannelPartner(b.getCorporateMarketingChannelPartner());
        d.setPreOpeningEleWater(b.getPreOpeningEleWater());
        d.setPreOpeningCam(b.getPreOpeningCam());
        d.setInterestOnFixedDepositFICO(b.getInterestOnFixedDepositFICO());
//        d.setInterestOnTermLoan(b.getInterestOnTermLoan());
        d.setLiabilityNoLongerRequiredWrittenBack(b.getLiabilityNoLongerRequiredWrittenBack());
        d.setAmortizationOfIntangibleAssets(b.getAmortizationOfIntangibleAssets());

    }


    public static void setAdjustmentAggregate(UnitExpenditureDetail d, AdjustmentAggregate b) {
        // TODO Auto-generated method stub
        d.setRevenueAdjustment(b.getRevenueAdjustment());
        d.setCostAdjustment(b.getCostAdjustment());
    }

}
