package com.stpl.tech.kettle.clm.model.FavChai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Key implements Serializable {

    private static final long serialVersionUID= 2799938716314159019L;
    @Column(name = "CUSTOMER_ID", nullable = false)
    private Integer customerId;

    @Column(name = "BRAND_ID", nullable = false)
    private Integer brandId;

}