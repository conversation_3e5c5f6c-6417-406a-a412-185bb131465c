package com.stpl.tech.kettle.data.expense.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "VOUCHER_STATUS_DATA")
public class VoucherStatusData {
	private Integer id;
	private VoucherData voucherData;
	private String fromStatus;
	private String toStatus;
	private Integer generatedBy;
	private String actionComment;
	private Date actionTime;
	private String transitionStatus;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "VOUCHER_ID", nullable = false)
	public VoucherData getVoucherData() {
		return voucherData;
	}

	public void setVoucherData(VoucherData voucherData) {
		this.voucherData = voucherData;
	}

	@Column(name = "FROM_STATUS", nullable = false)
	public String getFromStatus() {
		return fromStatus;
	}

	public void setFromStatus(String fromStatus) {
		this.fromStatus = fromStatus;
	}

	@Column(name = "TO_STATUS", nullable = false)
	public String getToStatus() {
		return toStatus;
	}

	public void setToStatus(String toStatus) {
		this.toStatus = toStatus;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public Integer getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(Integer generatedBy) {
		this.generatedBy = generatedBy;
	}

	@Column(name = "ACTION_COMMENT", nullable = true)
	public String getActionComment() {
		return actionComment;
	}

	public void setActionComment(String actionComment) {
		this.actionComment = actionComment;
	}

	@Column(name = "ACTION_TIME", nullable = true)
	public Date getActionTime() {
		return actionTime;
	}

	public void setActionTime(Date actionTime) {
		this.actionTime = actionTime;
	}

	@Column(name = "TRANSITION_STATUS", nullable = false)
	public String getTransitionStatus() {
		return transitionStatus;
	}

	public void setTransitionStatus(String transitionStatus) {
		this.transitionStatus = transitionStatus;
	}

}
