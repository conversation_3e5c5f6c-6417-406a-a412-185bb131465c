package com.stpl.tech.kettle.referral.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.model.CashData;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface CashManagerDao extends AbstractDao {

	public CashData getCustomerCashData(Integer customerId);

	public CashData createCashData(Customer customer) throws DataUpdationException;

	public void addCashToReferrerOnSuccess(ReferralMappingData data, String customerRefCode, CustomerInfo info);

	public void addCashOnReferentOnSuccess(ReferralMappingData data, CustomerInfo customerRefCode);

	public void redeemCash(Integer customerId, BigDecimal cashRedeemed, Integer orderId) throws DataUpdationException;

	public void activateReferralCashPacket(Integer referentId, Integer referrerId, Integer orderId);

	public Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> activateCashPackets();

	public void expireCashPackets(int limit);

	public Long countExpiringPackets();

	public void updateRefCodeInCashData(int customerId, String refCode);

	public void sendRefferalSMS(List<String> contactNumbers, String campaign, String source);

	public void notifyRefSuccess(Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> customerMap);

	CashPacketData addCashAsFreeGift(int customerId, String customerRefCode, int orderId, BigDecimal cashBack, Date creation,
									 Date expiration, Integer lagDays);
	boolean addCashForCashBack(int customerId, String customerRefCode, int orderId, BigDecimal cashBack, Date creation,
			Date expiration, int lagDays);

    void invalidateCashBack(Order order);

	BigDecimal getAvailableCashback(Integer customerId);

	CashPacketData uploadChaayosCashForAllotment(int customerId, BigDecimal amount, int lagDays, Date activationTime,
			Date expiration, String cashMetaData, String cashTransactionCode);

}
