/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.SignatureException;
import java.util.Base64;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.DecoderException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;

import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.master.core.WebServiceHelper;

public class RequestSigner {
	private static final Logger LOG = LoggerFactory.getLogger(RequestSigner.class);
	private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";
	private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
	private static final String UTF8_CHARSET = "UTF-8";

	public static String getSignedHeader(HttpMethod method, String host, String uri, AuthorizationObject credentials,
			String sortedQS) throws SignatureException {

		String stringToSign = method + "\n" + host.substring(host.indexOf("://") + 3, host.length()) + "\n" + uri + "\n"
				+ credentials.getToken() + "\n" + sortedQS + "\n" + "&SignatureVersion=1\n"
				+ "&SignatureMethod=HmacSHA1";

		LOG.info("String to be signed is ::::: {}", stringToSign);

		return "Opinio " + credentials.getToken() + ":" + sign(stringToSign, credentials.getTokenSecret());
	}

	private static String sign(String data, String key) throws java.security.SignatureException {
		String result;
		try {

			// Get an hmac_sha256 key from the raw key bytes.
			SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(UTF8_CHARSET), HMAC_SHA1_ALGORITHM);

			// Get an hmac_sha1 Mac instance and initialize with the signing
			// key.
			Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
			mac.init(signingKey);

			// Compute the hmac on input data bytes.
			byte[] rawHmac = mac.doFinal(data.getBytes(UTF8_CHARSET));

			// Base64-encode the hmac
			result = Base64.getEncoder().encodeToString(rawHmac);

		} catch (Exception e) {
			throw new SignatureException("Failed to generate HMAC : " + e.getMessage());
		}
		return result;
	}

	public static String canonicalize(SortedMap<String, String> sortedParamMap) {
		if (sortedParamMap.isEmpty()) {
			return "";
		} else {
			StringBuilder builder = new StringBuilder();
			// int count=0;
			for (Map.Entry<String, String> stringStringEntry : sortedParamMap.entrySet()) {
				builder.append("&");
				builder.append(percentEncodeRfc3986(stringStringEntry.getKey()));
				builder.append("=");
				builder.append(percentEncodeRfc3986(stringStringEntry.getValue()));
				// count++;
			}
			LOG.info("Param string is :::::::::::::::::::::::::::::::::::::::::::::::::::: {}",builder.toString());
			return builder.toString();
		}
	}

	private static String percentEncodeRfc3986(Object o) {
		if (o == null) {
			return "";
		}

		String s = String.valueOf(o);
		String out;
		try {
			out = URLEncoder.encode(s, UTF8_CHARSET).replace("+", "%20").replace("*", "%2A").replace("%7E", "~");
		} catch (UnsupportedEncodingException e) {
			out = s;
		}
		return out;
	}

	private static String percentDecodeRfc3986(String o) {
		if (o == null) {
			return "";
		}

		String s = String.valueOf(o);
		StringBuilder out = new StringBuilder("{");
		try {
			String decodedString = URLDecoder.decode(s, UTF8_CHARSET).replace("\b\t\n", "").replace("+", " ")
					.replace("=", "\":\"").replace("&", ",").replaceAll("[^\\\\,]+", "\"$0\"");

			out.append(decodedString).append("}");

		} catch (UnsupportedEncodingException e) {
			out = out.append(s);
		}
		return out.toString();
	}

	@SuppressWarnings("unchecked")
	public static String paramterizeValues(Object request) {
		Map<String, String> paramMap = WebServiceHelper.convert(request, Map.class);
		SortedMap<String, String> sortedParamMap = new TreeMap<String, String>(paramMap);
		return canonicalize(sortedParamMap);
	}

	public static String decodeValues(String urlEncodedValues) throws UnsupportedEncodingException, DecoderException {
		String result = URLDecoder.decode(urlEncodedValues, UTF8_CHARSET);
		// LOG.info("before decoding::: {}",result);
		result = percentDecodeRfc3986(result);
		LOG.info("result after decoding is :::: {}", result);

		try {
			result = WebServiceHelper.convertToString(WebServiceHelper.convert(result, Map.class));
			LOG.error("result after complete decoding :::: {}", result);
			return result;
		} catch (IOException e) {
			LOG.error("", e);
		}

		LOG.info("result after decoding is :::: {}", result);
		return result;
	}


	/*public static String calculateHash(String baseString,String keyString){
		byte[] keyBytes = keyString.getBytes();
		SecretKey secretKey = new SecretKeySpec(keyBytes, HMAC_SHA256_ALGORITHM);
		Hex hexEncoder = new Hex();
		try {

			Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
			mac.init(secretKey);
			byte[] text = hexEncoder.encode(baseString.getBytes());
			return new String(text).trim();

		} catch (NoSuchAlgorithmException e) {
			LOG.error("No such algorithm exception occurred",e);
		} catch (InvalidKeyException e) {
			LOG.error("Invalid Key Exception occurred", e);
		}
		return null;
	}*/


	public static String calculateHash(String contentString, String privateKey) {
		String generated = null;
		try {
			Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
			SecretKeySpec secret = new SecretKeySpec(privateKey.getBytes(UTF8_CHARSET),HMAC_SHA256_ALGORITHM);
			mac.init(secret);
			byte[] digest = mac.doFinal(contentString.getBytes(UTF8_CHARSET));
			StringBuffer hash = new StringBuffer();
			for (int i = 0; i < digest.length; i++) {
				String hex = Integer.toHexString(0xFF & digest[i]);
				if (hex.length() == 1) {
					hash.append('0');
				}
				hash.append(hex);
			}
			generated = hash.toString();
		} catch (Exception e) {
			LOG.error("Error while creating hash for request",e);
		}
		return generated;
	}


}
