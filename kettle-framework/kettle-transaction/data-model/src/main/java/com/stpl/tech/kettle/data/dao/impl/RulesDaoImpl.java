/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Query;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.dao.RulesDao;
import com.stpl.tech.kettle.data.model.RulesData;
import com.stpl.tech.kettle.data.model.RulesEventData;
import com.stpl.tech.kettle.data.model.RulesOptionData;
import com.stpl.tech.kettle.data.model.RulesOptionResultData;
import com.stpl.tech.kettle.offer.model.Option;
import com.stpl.tech.kettle.offer.model.OptionResponseData;
import com.stpl.tech.kettle.offer.model.RecommendationDetail;
import com.stpl.tech.kettle.offer.model.RuleData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class RulesDaoImpl extends AbstractDaoImpl implements RulesDao {

	private static final Logger LOG = LoggerFactory.getLogger(RulesDaoImpl.class);
	
	@Autowired
	private MasterDataCache masterCache;

	@Override
	public void createRule(RuleData rule) {
		RulesData data = findByName(rule.name());
		if (data == null) {
			data = new RulesData();
			data.setDaySlot(rule.getSlot().name());
			data.setStatus(AppConstants.ACTIVE);
			data.setRuleDescription(rule.getDesc());
			data.setRuleName(rule.name());
			manager.persist(data);
			manager.flush();
		}
		if (data.getOptions() == null || data.getOptions().size() == 0) {
			List<RulesOptionData> options = new ArrayList<>();
			for (Option option : rule.getOptions()) {
				options.add(createRuleOption(data, option));
			}
			data.setOptions(options);
		}
	}

	@Override
	public void updateRuleRecommendationData(int optionResultDataId, boolean hasDiscount) {
		RulesOptionResultData resultData = manager.find(RulesOptionResultData.class, optionResultDataId);
		if (hasDiscount) {
			resultData.setCountWithDiscount(resultData.getCountWithDiscount() + 1);
		} else {
			resultData.setCountWithoutDiscount(resultData.getCountWithoutDiscount() + 1);
		}
		manager.flush();
	}

	@Override
	public void updateRuleAvailedData(int optionResultDataId, boolean hasDiscount) {
		RulesOptionResultData resultData = manager.find(RulesOptionResultData.class, optionResultDataId);
		if (hasDiscount) {
			resultData.setAvailedWithDiscount(resultData.getAvailedWithDiscount() + 1);
		} else {
			resultData.setAvailedWithoutDiscount(resultData.getAvailedWithoutDiscount() + 1);
		}
		manager.flush();
	}

	private RulesData findByName(String name) {
		Query query = manager.createQuery("FROM RulesData E where E.ruleName = :ruleName");
		query.setParameter("ruleName", name);
		RulesData rulesData = null;
		try {
			rulesData = (RulesData) query.getSingleResult();
		} catch (Exception e) {
			return null;
		}
		return rulesData;
	}

	@Override
	public List<RulesData> getRules(String status) {
		Query query = manager.createQuery("FROM RulesData E where E.status = :status");
		query.setParameter("status", status);
		return query.getResultList();
	}

	private RulesOptionData createRuleOption(RulesData rule, Option option) {
		RulesOptionData optionData = new RulesOptionData();
		optionData.setOptionId(option.getOptionId());
		optionData.setOptionType(option.getType().name());
		optionData.setOption1ProductId(option.getOption1());
		optionData.setOption2ProductId(option.getOption2());
		optionData.setRule(rule);
		manager.persist(optionData);
		manager.flush();
		return optionData;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.kettle.data.dao.RulesDao#getOrRulesOptionResultData(int,
	 * com.stpl.tech.kettle.offer.model.RuleData,
	 * com.stpl.tech.kettle.offer.model.Option)
	 */
	@Override
	public RulesOptionResultData getOrCreateRulesOptionResultData(int unitId, RuleData rule, Option option) {
		LOG.info("Looking up for RulesOptionResultData for {} {} {}", unitId, rule.name(), option.getOptionId());
		Query query = manager.createQuery(
				"FROM RulesOptionResultData E where E.resultCategory = :resultCategory and optionData.optionId = :optionId and optionData.rule.ruleName = :ruleName order by optionResultDataId desc");
		query.setParameter("resultCategory", String.valueOf(unitId));
		query.setParameter("optionId", option.getOptionId());
		query.setParameter("ruleName", rule.name());

		List<RulesOptionResultData> list = query.getResultList();
		if (list == null || list.size() == 0) {
			LOG.info("Not Found RulesOptionResultData for {} {} {}", unitId, rule.name(), option.getOptionId());
			Query query1 = manager.createQuery(
					"FROM RulesOptionData E where E.optionId = :optionId and E.rule.ruleName = :ruleName order by optionDataId desc");
			query1.setParameter("optionId", option.getOptionId());
			query1.setParameter("ruleName", rule.name());
			RulesOptionData optionData = (RulesOptionData) query1.getSingleResult();
			return createRulesOptionResultData(unitId, optionData);
		} else {
			return list.get(0);
		}
	}

	private RulesOptionResultData createRulesOptionResultData(int unitId, RulesOptionData optionData) {
		LOG.info("Creating RulesOptionResultData for {} {} {}", unitId, optionData.getRule().getRuleName(), optionData.getOptionId());
		RulesOptionResultData result = new RulesOptionResultData();
		result.setAvailedWithDiscount(0);
		result.setAvailedWithoutDiscount(0);
		result.setCountWithDiscount(0);
		result.setCountWithoutDiscount(0);
		result.setOptionData(optionData);
		result.setResultCategory(String.valueOf(unitId));
		manager.persist(result);
		manager.flush();
		return result;
	}

	@Override
	public RecommendationDetail create(OptionResponseData data) {
		RulesEventData event = new RulesEventData();
		event.setCouponCode(data.getCouponCode());
		event.setEventTime(AppUtils.getCurrentTimestamp());
		event.setNewCustomer(AppConstants.getValue(data.getDetail().isNewCustomer()));
		event.setOptionResultDataId(data.getOptionResultId());
		event.setProductList(data.getDetail().getProductIds() != null && data.getDetail().getProductIds().size() > 0
				? StringUtils.join(data.getDetail().getProductIds(), ",") : "");
		event.setStockOutProductList(data.getStockedOutProductIds() != null && data.getStockedOutProductIds().size() > 0
				? StringUtils.join(data.getStockedOutProductIds(), ",") : "");
		manager.persist(event);
		manager.flush();
		updateRuleRecommendationData(data.getOptionResultId(), data.getCouponCode() != null);
		RecommendationDetail detail = new RecommendationDetail();
		detail.setCouponCode(data.getCouponCode());
		detail.setId(data.getOption().getOption1());
		if (data.getOption().getOption1() != null) {
			Product product = masterCache.getProduct(data.getOption().getOption1());
			detail.setName(product.getName());
			detail.setDesc(product.getDescription());
		}
		if (data.getCouponCode() != null) {
			detail.setOffer(true);
			// TODO Mohit Fix this . This is hardcoded as of now.
			detail.setDiscount(10);
		}
		detail.setOptionResultEventId(event.getRulesEventDataId());
		detail.setOptionResultId(data.getOptionResultId());
		return detail;
	}

	@Override
	public void update(RecommendationDetail data) {
		RulesEventData resultData = manager.find(RulesEventData.class, data.getOptionResultEventId());
		if (data.isAvailed()) {
			updateRuleAvailedData(data.getOptionResultId(), data.isOffer());
		}
		resultData.setAvailed(AppConstants.getValue(data.isAvailed()));
		resultData.setTriggeredBy(data.getAppliedBy());
		resultData.setQuantity(data.getQuantity());
		manager.flush();
	}

	@Override
	public void attach(int optionResultEventId, int orderId) {
		RulesEventData resultData = manager.find(RulesEventData.class, optionResultEventId);
		if (resultData != null) {
			resultData.setOrderId(orderId);
			manager.flush();
		}
	}

}
