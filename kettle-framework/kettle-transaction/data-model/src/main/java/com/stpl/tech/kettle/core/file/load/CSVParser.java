/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.load;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CSVParser<T> {

	private static final Logger LOG = LoggerFactory.getLogger(CSVParser.class);
	private RowMapper<T, String> mapper;

	public CSVParser(RowMapper<T, String> mapper) {
		super();
		this.mapper = mapper;
	}

	public List<T> parseCsv(String filePath, int skipRows, int skipColumns) throws IOException {

		List<T> list = new ArrayList<>();
		String cvsSplitBy = ",";

		int rowCount = 0;
		String line = "";
		try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
			while ((line = br.readLine()) != null) {
				String[] row = line.split(cvsSplitBy);
				rowCount++;
				if (rowCount <= skipRows) {
					continue;
				}
				T object = mapper.createNewInstance();
				for (int i = 0; i < row.length; i++) {
					String cellValue = row[i];
					if (i <= skipColumns) {
						continue;
					}
					mapper.setData(object, cellValue, i);
				}
				list.add(object);
			}

		} catch (IOException e) {
			LOG.error("Error while parsing the file", e);
		}
		return list;
	}

	public boolean hasErrors() {
		return mapper.getErrors() != null && mapper.getErrors().size() > 0;
	}

	public List<String> getErrors() {
		return mapper.getErrors();
	}
}
