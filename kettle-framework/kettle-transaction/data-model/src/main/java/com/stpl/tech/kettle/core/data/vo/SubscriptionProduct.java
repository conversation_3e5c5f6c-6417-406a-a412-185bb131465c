package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;

public class SubscriptionProduct {

	private Integer productId;
	private String productName;
	private String subscriptionCode;
	private String dimensionCode;
	private BigDecimal price;
	private Integer validityInDays;

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSubscriptionCode() {
		return subscriptionCode;
	}

	public void setSubscriptionCode(String subscriptionCode) {
		this.subscriptionCode = subscriptionCode;
	}

	public String getDimensionCode() {
		return dimensionCode;
	}

	public void setDimensionCode(String dimensionCode) {
		this.dimensionCode = dimensionCode;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getValidityInDays() {
		return validityInDays;
	}

	public void setValidityInDays(Integer validityInDays) {
		this.validityInDays = validityInDays;
	}

	@Override
	public String toString() {
		return "SubscriptionProduct [productId=" + productId + ", productName=" + productName + ", subscriptionCode="
				+ subscriptionCode + ", dimensionCode=" + dimensionCode + ", price=" + price + ", validityInDays="
				+ validityInDays + "]";
	}

}
