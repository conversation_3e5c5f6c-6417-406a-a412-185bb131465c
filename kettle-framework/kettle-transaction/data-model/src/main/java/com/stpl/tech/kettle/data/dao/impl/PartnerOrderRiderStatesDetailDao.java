package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.model.PartnerOrderRiderStatesDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PartnerOrderRiderStatesDetailDao extends JpaRepository<PartnerOrderRiderStatesDetail,Integer> {
      Optional<PartnerOrderRiderStatesDetail> findByPartnerOrderId(String partnerOrderId);

      Optional<PartnerOrderRiderStatesDetail> findBykettleOrderId(Integer orderId);

}
