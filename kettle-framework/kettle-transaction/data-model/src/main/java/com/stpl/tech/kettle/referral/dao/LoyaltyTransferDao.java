package com.stpl.tech.kettle.referral.dao;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyLogHistory;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.LoyaltyTransfer;
import com.stpl.tech.kettle.domain.model.webengage.survey.LoyaltyGiftingSMSToken;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.List;


public interface LoyaltyTransferDao extends AbstractDao {

    public LoyaltyScore getLoyalityScore(int customerId);

    public  LoyaltyTransfer getLoyalityByEventId(int eventId);

    public  CustomerInfo getCustomerInfoByCustomerId(int customerId);

    public CustomerInfo getCustomerInfoByContactNumber(String contactNumber);

    public List<LoyaltyTransfer> getAllSentGift(int customerId);

    public List<LoyaltyTransfer> getAllReceivedGift(String contactNumber);

    public List<LoyaltyTransfer> getNotClaimedGift(int customerId);

    public List<LoyaltyTransfer> getNotClaimedReceivedGift(String contactNumber);

    public List<LoyaltyTransfer> getClaimedGift(int customerId);

    public List<LoyaltyTransfer> getClaimedReceivedGift(String contactNumber);

    public List<LoyaltyTransfer> allExpiredLoyaltyTransfers();

    public boolean sendLoyaltyGiftingReminderSMS(LoyaltyGiftingSMSToken loyaltyGiftingSMSToken, String contactNumber);
    public LoyaltyEvents getLoyaltyEventByEventId(Integer eventId);
    public List<LoyaltyLogHistory> getLoyaltyLogHistoryByTransactionEventId(Integer eventId);
    public List<LoyaltyEvents> getLoyaltyEventByCustomerIdExcludeGiftingEvent(Integer customerId);
}
