/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.Unit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DeliveryOperationExecutor implements Cloneable {
	private static Logger LOG = LoggerFactory.getLogger(DeliveryOperationExecutor.class);

	private DeliveryExecutionStrategy strategy;
	private String endpoint;

	private DeliveryOperationExecutor() {
	}

	public static DeliveryOperationExecutor getInstance(DeliveryExecutionStrategy strategy)
			throws CloneNotSupportedException {
		DeliveryOperationExecutor executor = null;
		if (executor == null) {
			executor = new DeliveryOperationExecutor();
		}
		executor = (DeliveryOperationExecutor) executor.clone();
		executor.setStrategy(strategy);
		LOG.info("Name of strategy being used is {}", strategy.getClass().getSimpleName());
		return executor;

	}

	public DeliveryResponse executeCreateDelivery(OrderInfo order, EnvironmentProperties props) {
		LOG.info("Inside executeCreateDelivery with endpoint {} and orderId {}", endpoint,
				order.getOrder().getOrderId());
		return getStrategy().createDelivery(endpoint, order, props);
	}

	public DeliveryResponse executeCancelDelivery(String deliveryTaskId, OrderInfo orderInfo, EnvironmentProperties props) {
		LOG.info("Inside executeCancelDelivery with endpoint {} and orderId {}", endpoint, orderInfo);
		return getStrategy().cancelDelivery(endpoint, deliveryTaskId, orderInfo, props);
	}

	public String createMerchantWithPartner(Unit unit, EnvironmentProperties props) {
		return getStrategy().registerMerchant(endpoint, unit, props);
	}

	public DeliveryExecutionStrategy getStrategy() {
		return strategy;
	}

	private void setStrategy(DeliveryExecutionStrategy strategy) {
		this.strategy = strategy;
	}

	public String getEndpoint() {
		return endpoint;
	}

	public void setEndpoint(String endpoint) {
		this.endpoint = endpoint;
	}
}
