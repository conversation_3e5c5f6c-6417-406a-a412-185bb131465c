/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.delivery.model.DTCallback;
import com.stpl.tech.kettle.delivery.model.DTOrderResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.util.AppUtils;

public class DTResponseAdapter
		implements ResponseAdapter<DTOrderResponse, DTOrderResponse, DTCallback, DeliveryResponse> {

	private static final Integer PARTNER_ID = 7;

	@Override
	public DeliveryResponse adapt(DTOrderResponse data, String orderId) {

		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryStatus(DeliveryStatus.ACCEPTED.getDeliveryStatus());
		response.setDeliveryTaskId(data.getOrderId());
		response.setGeneratedOrderId(orderId);
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		return response;
	}

	@Override
	public DeliveryResponse adaptError(DTOrderResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		response.setFailureMessage(data != null ? data.getMessage() : "Request Failed");
		response.setFailureCode(data!=null ? data.getResult(): "504");
		return response;
	}


	@Override
	public DeliveryResponse adaptCallback(DTCallback data) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryStatus(getStatus(data.getStatus()).getDeliveryStatus());
		response.setDeliveryTaskId(data.getOrderId());
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryBoyName(data.getDriverName());
		response.setDeliveryBoyPhoneNum(data.getDriverNumber());
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		return response;
	}

	private DeliveryStatus getStatus(String status) {
		DeliveryStatus returnStatus = null;
		if(status.equalsIgnoreCase("accepted")){
			returnStatus = DeliveryStatus.ACCEPTED;
		}else if(status.equalsIgnoreCase("arrived at location")){
			returnStatus = DeliveryStatus.ARRIVED;
		}else if(status.equalsIgnoreCase("pickedup")){
			returnStatus = DeliveryStatus.PICKEDUP;
		}else if(status.equalsIgnoreCase("delivered")){
			returnStatus = DeliveryStatus.DELIVERED;
		}else{
			returnStatus = DeliveryStatus.REQUEST_DECLINED;
		}
		return returnStatus;
	}

	@Override
	public DeliveryResponse adaptCancel(String orderId, String taskId) {
		
		return null;
	}

}
