/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.model;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class SimulationTimeData {
	public Date startTime;
	public Date endTime;
	public int timeTaken;
	public int waitTimeInSecs;
	public int monkNumber;
	public int idealMonks;

	@Override
	public String toString() {
		return "SimulationTimeData [startTime=" + startTime + ", endTime=" + endTime + ", timeTaken=" + timeTaken
				+ ", waitTimeInSecs=" + waitTimeInSecs + ", monkNumber=" + monkNumber + ", idealMonks=" + idealMonks
				+ "]";
	}

}
