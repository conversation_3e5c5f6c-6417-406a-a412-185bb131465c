
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.adapter.DUNRequestAdapter;
import com.stpl.tech.kettle.delivery.adapter.DUNResponseAdapter;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DUNErrorResponse;
import com.stpl.tech.kettle.delivery.model.DUNGenericRequest;
import com.stpl.tech.kettle.delivery.model.DUNRequest;
import com.stpl.tech.kettle.delivery.model.DUNResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;

import org.apache.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;

public class DUNExecutor extends AbstractDeliveryStrategy implements DeliveryExecutionStrategy {

	private static Logger LOG = LoggerFactory.getLogger(DUNExecutor.class);

	private DUNGenericRequest request = new DUNRequest();

	private DUNRequestAdapter requestAdapter = new DUNRequestAdapter();

	private DUNResponse response = new DUNResponse();

	private DUNResponseAdapter responseAdapter = new DUNResponseAdapter();

	private DUNErrorResponse error = new DUNErrorResponse();


	@Override
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props) {

		request = requestAdapter.adaptCreate(order);
//		creationEndpoint = DUNEnv.TEST.getValue() + creationEndpoint + "?test=true";
		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			LOG.info("environment type is {}", props.getEnvironmentType());
			creationEndpoint = DUNEnv.TEST.getValue() + creationEndpoint + "?test=true";
		} else {
			creationEndpoint = DUNEnv.PRODUCTION.getValue() + creationEndpoint;
		}

		HttpResponse responseFromRequest = createRequest(creationEndpoint, request);
		return readResponse(responseFromRequest, order.getOrder().getGenerateOrderId());
	}

	@Override
	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo,
										   EnvironmentProperties props) {

//		cancelationEndpoint = DUNEnv.TEST.getValue() + "/" + taskId + "/" + cancelationEndpoint + "?test=true";
		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			LOG.info("environment type is {}", props.getEnvironmentType());
			cancelationEndpoint = DUNEnv.TEST.getValue() + "/" + taskId + "/" + cancelationEndpoint + "?test=true";
		} else {
			cancelationEndpoint = DUNEnv.PRODUCTION.getValue() + "/" + taskId + "/" + cancelationEndpoint;
		}
		request = requestAdapter.adaptCancel(orderInfo,taskId);
		LOG.info("Cancellation endpoint is :::: {}", cancelationEndpoint);
		return readResponse(createRequest(cancelationEndpoint, request), orderInfo.getOrder().getGenerateOrderId(), taskId);
	}

	@Override
	public void setAuthorizationObject(AuthorizationObject authorization) {
		this.authorization = authorization;
	}

	@Override
	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props) {
		// TODO integrate API level unit on boarding for ShadowFax
		return null;
	}

	@Override
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId) {

		try {
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()) {
				response = WebServiceHelper.convertResponse(responseFromRequest, DUNResponse.class);
				return responseAdapter.adapt(response, orderId);
			} else {
				error = WebServiceHelper.convertResponse(responseFromRequest, DUNErrorResponse.class);
				return responseAdapter.adaptError(error, orderId);
			}
		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}

		return null;
	}
	
	
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId, String taskId) {

		try {
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()) {
//				response = WebServiceHelper.convertResponse(responseFromRequest, DUNResponse.class);
				return responseAdapter.adaptCancel(orderId, taskId);
			} else {
				error = WebServiceHelper.convertResponse(responseFromRequest, DUNErrorResponse.class);
				return responseAdapter.adaptError(error, orderId);
			}
		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}

		return null;
	}

}


