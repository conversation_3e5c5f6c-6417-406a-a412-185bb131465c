package com.stpl.tech.kettle.customer.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.GiftOffer;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardOffer;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.GiftCardActivationRequest;

public interface CardService {


    CashCardDetail createCashCardDetail(BigDecimal amount, int customerId, int orderId,
                                        Date startDate, Date endDate);

    public CashCardDetail getCardDetail(int customerId, String cardNumber, boolean runValidations) throws CardValidationException;

    public List<CashCardDetail> getCardDetails(int customerId) throws CardValidationException;

    public CashCardDetail getCardDetail(int cardId);

    public CashCardDetail getCardDetail(String code) throws CardValidationException;

    public CashCardDetail getCardDetailBySerial(String serial) throws CardValidationException;

    public void deactivateCashCards();

    public boolean activateCashCard(GiftCardActivationRequest giftCardActivationRequest) throws CardValidationException, DataUpdationException;

    public CustomerCardInfo getCashCardAmount(int customerId);

    public List<GiftOffer> getCurrentCardOffer(int unitId, Integer partnerId);

    public List<Integer> getUnitsWithCardOffersForToday();

    public List<CashCardDetail> getCardsByPurchaseOrderId(Integer orderId);

	public List<CashCardDetail> getActiveCashCards(int customerId);

	public List<CashCardOffer> getAllCashCardOffers(Date businessDate);

	public List<CashCardOffer> addCashCardOffers(List<CashCardOffer> list) throws CardValidationException;

	public List<CashCardOffer> changeStatusAllCashCardOffers(List<CashCardOffer> list);

    List<CashCardOffer> getCashCardOffersForDate(Date startDate, Date endDate, Integer partnerId);

    public BigDecimal getGiftCardOffer(Integer purchaseOrderId) ;

    }
