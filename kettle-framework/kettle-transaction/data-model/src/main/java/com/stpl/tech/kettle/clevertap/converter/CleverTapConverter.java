package com.stpl.tech.kettle.clevertap.converter;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.stpl.tech.master.domain.model.Unit;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.clevertap.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapChargedEventItemData;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapOfferData;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapReceivedChaayosCashData;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapSubscriptionEventData;
import com.stpl.tech.kettle.clevertap.domain.model.CustomerProfileCleverTap;
import com.stpl.tech.kettle.clevertap.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.clevertap.domain.model.NextBestOfferEventData;
import com.stpl.tech.kettle.clevertap.domain.model.ProfileData;
import com.stpl.tech.kettle.clevertap.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.clm.service.ClevertapAttributesService;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Service
public class CleverTapConverter {

    private static final Logger LOG = LoggerFactory.getLogger(CleverTapConverter.class);

	private static final Long BUFFER_TIME = 20L;
	private static final Long BUFFER_TIME_FOR_NEW_PROFILE = 300L;


	@Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ClevertapAttributesService clevertapAttributesService;

    private static final String CLEVERTAP_DATE_PREFIX = "$D_";

    public ProfileUploadRequest convert(Customer customer, CustomerCardInfo cardInfo) {
		LOG.info("converting data for user profile push in clevertap");
		ProfileUploadRequest profileRequest = new ProfileUploadRequest();
		profileRequest.setIdentity(customer.getId());
		profileRequest.setType(CleverTapConstants.PROFILE);
		profileRequest.setTs(customer.getAddTime().getTime() / 1000);
		ProfileData data = new ProfileData();
		data.setTz(AppUtils.getTimeZone());
		data.setName(customer.getFirstName());
		data.setEmail(customer.getEmailId());
		data.setGender(customer.getGender());
		data.setPhone(customer.getCountryCode() + customer.getContactNumber());
		data.setAvailedSignupOffer(customer.isAvailedSignupOffer());
		data.setCustomerId(String.valueOf(customer.getId()));
		if (customer.getDateOfBirth() != null) {
			data.setDob(CLEVERTAP_DATE_PREFIX + customer.getDateOfBirth().getTime() / 1000);
		}
		data.setReferral(customer.getRefCode());
		if (cardInfo.getCardAmount() != null) {
			data.setCardAmount(cardInfo.getCardAmount().toString());
			data.setHasCard(AppConstants.YES);
		} else {
			data.setHasCard(AppConstants.NO);
		}
		if (customer.getChaayosCash() != null) {
			data.setChaayosCash(customer.getChaayosCash().toString());
		}
		if (customer.getLastOrderId() != null) {
			data.setLastOrderId(customer.getLastOrderId().toString());
		}
		if (customer.getLastOrderTime() != null) {
			data.setLastOrderItem(customer.getLastOrderTime().toString());
		}
		data.setLoyaltyPoints(String.valueOf(customer.getLoyaltyPoints()));
		if (customer.getOrderCount() != null) {
			data.setOrderCount(customer.getOrderCount().toString());
		}
		data.setRefCode(customer.getRefCode());
		data.setSignupOfferAvailed(customer.isAvailedSignupOffer());
		data.setSignupOfferExpired(customer.isSignUpOfferExpired());
		if (customer.getSignupOfferExpiryTime() != null) {
			data.setSignupOfferExpiryTime(customer.getSignupOfferExpiryTime().toString());
		}
		if (customer.getOrderCount() != null) {
			data.setOrdersCount(customer.getOrderCount().toString());
		}
		data.setFirstName(customer.getFirstName());
		data.setLastName(customer.getLastName());
		data.setCustomerAppId(customer.getCustomerAppId());
		data.setLastUpdateTime(AppUtils.getCurrentTimeISTString());
		profileRequest.setProfileData(data);
		return profileRequest;
	}

	public CustomerProfileCleverTap convert(Customer customer) {
		LOG.info("converting data for user profile push in clevertap");
		Map<String, Object> attributeMap = clevertapAttributesService.getProfileAttributes(customer);
		CustomerProfileCleverTap data = new CustomerProfileCleverTap();
		data.setAddTime(CLEVERTAP_DATE_PREFIX + customer.getAddTime().toInstant().getEpochSecond());
		data.setName(customer.getFirstName());
		data.setEmail(customer.getEmailId());
		data.setPhone(customer.getCountryCode() + customer.getContactNumber());
		data.setCustomerId(customer.getId());
		data.setLoyaltyRedeemedCount(((BigDecimal) attributeMap.get("loyaltyRedeemedCount")).toBigInteger().intValue());
		data.setCountryCode(customer.getCountryCode());
		data.setIsNumberVerified(boolToString(customer.isContactNumberVerified()));
		data.setIsEmailVerified(boolToString(customer.isEmailVerified()));
		data.setIsBlacklisted(boolToString(customer.isBlacklisted()));
		data.setIsDnd(boolToString(customer.isDND()));
		data.setMsg_sms(boolToString(customer.isSmsSubscriber()));
		data.setOptInSms(customer.isSmsSubscriber());
		data.setMsg_whatsapp(customer.getOptWhatsapp());
		if(Objects.nonNull(customer.getDateOfBirth())){
			data.setBirthday(AppUtils.getDay(customer.getDateOfBirth()));
			data.setBirthdayMonth(AppUtils.getMonthName(AppUtils.getMonth(customer.getDateOfBirth())));
			data.setDob(CLEVERTAP_DATE_PREFIX + customer.getDateOfBirth().toInstant().getEpochSecond());
		}
		if(Objects.nonNull(customer.getAnniversary())){
			data.setAnniversary(AppUtils.getDay(customer.getAnniversary()));
			data.setAnniversaryMonth(AppUtils.getMonthName(AppUtils.getMonth(customer.getAnniversary())));
		}
		if (Objects.nonNull(customer.getOrderCount())) {
			data.setOrderCount(customer.getOrderCount());
		} else {
			data.setOrderCount(0);
		}
		data.setAcquisitionSource(customer.getAcquisitionSource());
		if(customer.getAddTime().after(AppUtils.getBusinessDate()) || customer.getAddTime().equals(AppUtils.getBusinessDate())){
			data.setCommunicationName(customer.getFirstName());
		}
		if(Objects.nonNull(data.getLoyaltyPointsBalance())) {
			data.setLoyalteaAvailable(Math.floorDiv(data.getLoyaltyPointsBalance(), 60));
		}
		data.setLastUpdatedTime(CLEVERTAP_DATE_PREFIX + AppUtils.getCurrentTimestamp().getTime());
		if (StringUtils.isNotBlank(customer.getOptWhatsapp())) {
			data.setOptInWhatsapp(customer.getOptWhatsapp().equalsIgnoreCase(AppConstants.YES));
		}
		try {
			data.setTotalSpent((BigDecimal) attributeMap.get("totalSpent"));
		} catch (Exception e) {
			LOG.info("Casting Exception for totalSpent in Clevertap profile for customer {}",data.getCustomerId());
		}
		try {
			data.setGcBalance((BigDecimal) attributeMap.get("gcBalance"));
		} catch (Exception e) {
			LOG.info("Casting Exception for gcBalance in Clevertap profile for customer {}",data.getCustomerId());
		}
		try {
			data.setChyCashBalance((BigDecimal) attributeMap.get("chyCashBalance"));
		} catch (Exception e) {
			LOG.info("Casting Exception for chyCashBalance in Clevertap profile  for customer {}",data.getCustomerId());
		}
		try {
			data.setLoyaltyPointsBalance((Integer) attributeMap.get("loyaltyPointsBalance"));
		} catch (Exception e) {
			LOG.info("Casting Exception for loyaltyPointsBalance in Clevertap profile  for customer {}",data.getCustomerId());
		}
		try {
			data.setLoyaltyRedeemedCount( ((BigDecimal) attributeMap.get("loyaltyRedeemedCount")).intValue());
		} catch (Exception e) {
			LOG.info("Casting Exception for loyaltyRedeemedCount in Clevertap profile  for customer {}",data.getCustomerId());
		}
		try {
			data.setNboAvailableFlag(((BigInteger) attributeMap.get("nboAvailableFlag")).intValue());
		} catch (Exception e) {
			LOG.info("Casting Exception for nboAvailableFlag in Clevertap profile  for customer {}",data.getCustomerId());
		}
		try {
			data.setDnboAvailableFlag(((BigInteger) attributeMap.get("dnboAvailableFlag")).intValue());
		} catch (Exception e) {
			LOG.info("Casting Exception for dnboAvailableFlag in Clevertap profile  for customer {}",data.getCustomerId());
		}
		try {
			data.setSubscriptionActiveFlag(((BigInteger) attributeMap.get("subscriptionActiveFlag")).intValue());
		} catch (Exception e) {
			LOG.info("Casting Exception for subscriptionActiveFlag in Clevertap profile  for customer {}", data.getCustomerId());
		}

		if(Objects.nonNull(data.getLoyaltyPointsBalance())){
			data.setFiftyLoyaltyThreshold(data.getLoyaltyPointsBalance() == 50 ? AppConstants.YES : AppConstants.NO);
		}else{
			data.setFiftyLoyaltyThreshold(AppConstants.NO);
		}
		return data;
	}

	public String boolToString(boolean val) {
		return val ? AppConstants.YES : AppConstants.NO;
	}

    public String boolToString(Boolean val) {
        return val.equals(Boolean.TRUE) ? AppConstants.YES : AppConstants.NO;
    }

    public EventUploadRequest convert(OrderDetail order, String evtName, Object data) {
        EventUploadRequest eventRequest = new EventUploadRequest();
        eventRequest.setIdentity(order.getCustomerId());
        eventRequest.setEvtName(evtName);
		try {
			if (data instanceof ClevertapChargedEventData) {
				ClevertapChargedEventData d = (ClevertapChargedEventData) data;
				if (AppConstants.YES.equals(d.getIsNewCustomer())) {
					eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME_FOR_NEW_PROFILE);
				}
				else{
					eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME);
				}
			} else {
				eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME);
			}
		}catch (Exception e){
			LOG.error("Error in checking for new Customer");
			eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME);
		}
        eventRequest.setType(CleverTapConstants.EVENT);
        eventRequest.setEvtData(data);
        return eventRequest;

    }

    public EventUploadRequest convertReceivedChaayosCashData(Integer customerId,String evtName, Object data){

        EventUploadRequest request=new EventUploadRequest();
        Date currentTime=AppUtils.getCurrentTimestamp();
        request.setTs(currentTime.toInstant().getEpochSecond());
        request.setIdentity(customerId);
        request.setEvtName(evtName);
        request.setType("event");
        request.setEvtData(data);
        return request;
    }

    public NextBestOfferEventData convertNextBestOfferEventData(OrderDetail order, CustomerInfo customer,
                                                                String nextOfferEvent) {
        NextBestOfferEventData evt = new NextBestOfferEventData();
        evt.setSource(order.getOrderSource() == null ? CleverTapConstants.NULL : order.getOrderSource());
        evt.setCustomerId(customer.getCustomerId());
        evt.setUnitId(order.getUnitId());
        evt.setChannelPartnerId(order.getChannelPartnerId());
        if (nextOfferEvent != null) {
            evt.setCustomerType(nextOfferEvent);
        }
        return evt;
    }

	public ClevertapChargedEventData convertChargedEventOrderData(OrderDetail order, CustomerInfo customer,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		LOG.info("Inside Converting Charged Event Data ");
		ClevertapChargedEventData orderData = new ClevertapChargedEventData();
		List<ClevertapChargedEventItemData> itemList = new ArrayList<>();
		Map<String, Object> attributeMap = clevertapAttributesService.getEventAttributes(order, customer);
		Map<Integer, Product> productDetails = masterDataCache.getProductDetails();
		calculateAll(order, productDetails, itemList, orderData);
		orderData.setItems(itemList);
		orderData.setUnitId(order.getUnitId());
		Unit unit = masterDataCache.getUnit(order.getUnitId());
		if(Objects.nonNull(unit)){
			orderData.setUnitCity(unit.getAddress().getCity());
			orderData.setUnitRegion(unit.getRegion());
			orderData.setUnitSubCat(unit.getSubCategory().name());
		}
		orderData.setBrandId(order.getBrandId());
		orderData.setOrderId(order.getOrderId());
		orderData.setOfferCode(order.getOfferCode());
		orderData.setUnitName(masterDataCache.getUnit(order.getUnitId()).getName());
		orderData.setOrderType(order.getOrderType());
		orderData.setCustomerId(order.getCustomerId());
		orderData.setOrderSource(order.getOrderSource());
		orderData.setOrderStatus(order.getOrderStatus());
		orderData.setTotalAmount(order.getTotalAmount());
		orderData.setBusinessDate(CLEVERTAP_DATE_PREFIX + AppUtils.getBusinessDate().toInstant().getEpochSecond());
		orderData.setDiscountRate(order.getDiscountPercent());
		orderData.setTaxableAmount(order.getTaxableAmount());
		orderData.setDiscountAmount(order.getDiscountAmount());
		orderData.setPointsRedeemed(order.getPointsRedeemed() * (-1));
		orderData.setOrderSourceVersion(order.getSourceVersion());
		if (order.getPointsRedeemed() > 0) {
			orderData.setOfferClass("LOYALTEA");
		}
		orderData.setChannelPartnerId(order.getChannelPartnerId());
		orderData.setBillingServerTime(
				CLEVERTAP_DATE_PREFIX + order.getBillingServerTime().toInstant().getEpochSecond());
		orderData.setDeliveryPartnerId(order.getDeliveryPartnerId());
		try {
			if(Objects.nonNull(attributeMap.get("offerDetailId"))) {
				orderData.setOfferDetailId((Integer) attributeMap.get("offerDetailId"));
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for offerDetailId in Clevertap charged event  for order {}", orderData.getOrderId());

		}
		try {
			if(Objects.nonNull(attributeMap.get("previousOrderId"))){
				orderData.setPreviousOrderId((Integer) attributeMap.get("previousOrderId"));
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for previousOrderId in Clevertap charged event for order {}", orderData.getOrderId());

		}
		try {
			if(Objects.nonNull(attributeMap.get("previousSourceOrderId"))) {
				orderData.setPreviousSourceOrderId((Integer) attributeMap.get("previousSourceOrderId"));
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for previousSourceOrderId in Clevertap charged event for order {}", orderData.getOrderId());

		}
		try {
			if(Objects.nonNull(attributeMap.get("previousBillingServerTime"))) {
				orderData.setPreviousBillingServerTime(
						CLEVERTAP_DATE_PREFIX + ((Timestamp) attributeMap.get("previousBillingServerTime")).getTime());
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for previousBillingServerTime in Clevertap charged event for order {}", orderData.getOrderId());

		}
		try {
			if(Objects.nonNull(attributeMap.get("previousSourceBillingServerTime"))) {
				orderData.setPreviousSourceBillingServerTime(
						CLEVERTAP_DATE_PREFIX + ((Timestamp) attributeMap.get("previousSourceBillingServerTime")).getTime());
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for previousSourceBillingServerTime in Clevertap charged event for order {}", orderData.getOrderId());
        }
        try {
            orderData.setPreviousBillingServerTime("$D_" + ((Timestamp) attributeMap.get("previousBillingServerTime")).getTime());
        } catch (Exception e) {
            LOG.info("Casting Exception for previousBillingServerTime in Clevertap charged event for order {}", orderData.getOrderId());

        }
        try {
            orderData.setPreviousSourceBillingServerTime("$D_" + ((Timestamp) attributeMap.get("previousSourceBillingServerTime")).getTime());
        } catch (Exception e) {
            LOG.info("Casting Exception for previousSourceBillingServerTime in Clevertap charged event for order {}", orderData.getOrderId());

        }
        try {
			if(Objects.nonNull(attributeMap.get("previousOrderTimeDiff"))){
				orderData.setPreviousOrderTimediff(((BigInteger) attributeMap.get("previousOrderTimeDiff")));
			}
        } catch (Exception e) {
            LOG.info("Casting Exception for previousOrderTimeDiff in Clevertap charged event for order {}", orderData.getOrderId());

        }
        try {
			if(Objects.nonNull(attributeMap.get("previousSourceOrderTimeDiff"))){
				orderData.setPreviousSourceOrderTimediff(((BigInteger) attributeMap.get("previousSourceOrderTimeDiff")));
			}
        } catch (Exception e) {
            LOG.info("Casting Exception for previousSourceOrderTimeDiff in Clevertap charged event for order {}", orderData.getOrderId());

        }
        try {
            orderData.setOverallOrderCount(attributeMap.get("overallOrderCount"));
        } catch (Exception e) {
            LOG.info("Casting Exception for overallOrderCount in Clevertap charged event for order {}", orderData.getOrderId());
		}
		try {
			orderData.setOrderSourceOrderCount(attributeMap.get("orderSourceOrderCount"));
		} catch (Exception e) {
			LOG.info("Casting Exception for orderSourceOrderCount in Clevertap charged event for order {}", orderData.getOrderId());
		}
		if(AppConstants.COD.equals(order.getOrderSource()) && AppConstants.CHANNEL_PARTNER_ZOMATO == order.getChannelPartnerId()){
			setChargedEventNotificationPayloadForPartner(order,customer,orderNotification,orderData);
		} else if (Objects.nonNull(orderNotification)) {
			setChargedEventNotificationPayload(orderData, orderNotification);
		}
		try {
			orderData.setWalletPendingAmount(((BigDecimal) attributeMap.get("walletPendingAmount")).intValue());
		}catch (Exception e){
			LOG.info("Casting Exception for walletPendingAmount during BigDecimal conversion in Clevertap charged event for order {} and Error is : {}",
					orderData.getOrderId(),e);
			try{
				orderData.setWalletPendingAmount(((BigInteger) attributeMap.get("walletPendingAmount")).intValue());
			}catch (Exception ex){
				LOG.info("Casting Exception for walletPendingAmount during BigInteger conversion in Clevertap charged event for order {} and Error is : {}",
						orderData.getOrderId(),e);
			}
		}
		LOG.info("Charged Event Data Converter : took {} ms", System.currentTimeMillis()-startTime);
		if(Objects.nonNull(orderData.getOverallOrderCount()))
		{
			try {
				if(attributeMap.get("overallOrderCount") instanceof Long){
					orderData.setIsNewCustomer(boolToString((((Long) attributeMap.get("overallOrderCount"))<= 0 )));
				} else{
					orderData.setIsNewCustomer(boolToString((((BigInteger) attributeMap.get("overallOrderCount")).compareTo(BigInteger.ONE) <= 0 )));
				}
			} catch (Exception e) {
				LOG.info("Casting Exception for overallOrderCount in Clevertap charged event for order {}", orderData.getOrderId());
			}		}
		return orderData;
	}

	public void setChargedEventNotificationPayloadForPartner(OrderDetail order, CustomerInfo customer,
															 OrderNotification orderNotification,
															 ClevertapChargedEventData orderData){
		Map<String,Object> profileAttribute = clevertapAttributesService.getProfileAttributes(customer.getCustomerId());
		long startTime = System.currentTimeMillis();
		try {
			orderData.setCustomerName(customer.getFirstName());
			orderData.setChannelPartner(orderNotification.getChannelPartner());
			try {
				orderData.setTotalLoyalTeaPoint((Integer) profileAttribute.get("loyaltyPointsBalance"));
			}catch (Exception e){
				LOG.info("Error in casting totalLoyaltyPoints for order with orderId : {} and error is : {}",order.getOrderId(),e);
			}
			orderData.setOrderAmt(orderNotification.getOrderAmt());
			orderData.setSavingAmt(orderNotification.getSavingAmt());
			orderData.setSavingText(orderNotification.getSavingText());
			orderData.setGenerateOrderId(orderNotification.getGenerateOrderId());
			orderData.setItemCode(orderNotification.getItemCode());
			try {
				orderData.setCashPendingAmount(((BigDecimal)profileAttribute.get("chyCashBalance")).toString());
			}catch (Exception e){
				LOG.info("Error in casting cashPendingAmount for order with orderId : {} and error is : {}",order.getOrderId(),e);
			}
			if(Objects.nonNull(customer.getOptWhatsapp()) && AppConstants.YES.equalsIgnoreCase(customer.getOptWhatsapp())){
				orderData.setWhatsAppOptIn(true);
			}
			if((Objects.isNull(customer.getOptWhatsapp()) ||(Objects.nonNull(customer.getOptWhatsapp()) && AppConstants.NO.equalsIgnoreCase(customer.getOptWhatsapp())))){
				orderData.setSmsSubscriber(true);
			}
			orderData.setCustomerContactNumber(customer.getContactNumber());
		}catch (Exception e){
			LOG.error(
					"Exception faced while converting order  payload to clevertap charged event for orderId :{}",
					orderData.getOrderId(), e);
		}
		LOG.info("Charged Event Notification Data Converter : took {} ms", System.currentTimeMillis() - startTime);
	}

	public  ClevertapReceivedChaayosCashData convertReceivedChaayosCashData(String cashMetadataType,
			String cashTransactionCode, Date creationDate, Date expirationDate, BigDecimal amount,
			Boolean sendNotification, CustomerSMSNotificationType customerSMSNotificationType, BigDecimal walletBalance,
			Integer loyaltyBalance, BigDecimal chaayosCash, String comment) {
		ClevertapReceivedChaayosCashData clevertapReceivedChaayosCashData = new ClevertapReceivedChaayosCashData();

		clevertapReceivedChaayosCashData.setCashMetadataType(cashMetadataType);
		clevertapReceivedChaayosCashData.setCashTransactionCode(cashTransactionCode);
		clevertapReceivedChaayosCashData
				.setCreationDate(CLEVERTAP_DATE_PREFIX + creationDate.toInstant().getEpochSecond());
		clevertapReceivedChaayosCashData
				.setExpirationDate(CLEVERTAP_DATE_PREFIX + expirationDate.toInstant().getEpochSecond());
		clevertapReceivedChaayosCashData.setAmount(amount);
		clevertapReceivedChaayosCashData.setNotify(false);
		clevertapReceivedChaayosCashData.setMessageType(customerSMSNotificationType.name());
		clevertapReceivedChaayosCashData.setSendNotification(sendNotification);
		clevertapReceivedChaayosCashData.setWalletBalance(walletBalance);
		clevertapReceivedChaayosCashData.setLoyaltyBalance(loyaltyBalance);
		clevertapReceivedChaayosCashData.setChaayosCash(chaayosCash);
		clevertapReceivedChaayosCashData.setComment(comment);

		return clevertapReceivedChaayosCashData;
	}

	private void setChargedEventNotificationPayload(ClevertapChargedEventData orderData,
			OrderNotification orderNotification) {
        long startTime = System.currentTimeMillis();
            try {
                orderData.setSubscriptionName(orderNotification.getSubscriptionName());
                orderData.setOfferDescription(orderNotification.getOfferDescription());
                orderData.setValidDays(orderNotification.getValidDays());
                orderData.setPlanEndDate(orderNotification.getPlanEndDate());
                orderData.setCustomerName(orderNotification.getCustomerName());
                orderData.setSelectOverallSaving(orderNotification.getSelectOverallSaving());
                orderData.setSelectSavingAmount(orderNotification.getSelectSavingAmount());
                orderData.setNextOfferText(orderNotification.getNextOfferText());
                orderData.setValidityTill(orderNotification.getValidityTill());
                orderData.setChannelPartner(orderNotification.getChannelPartner());
                orderData.setLoyalTeaTotalCount(orderNotification.getLoyalTeaTotalCount());
                orderData.setTotalLoyalTeaPoint(orderNotification.getTotalLoyalTeaPoint());
                orderData.setLoyalTeaPoints(orderNotification.getLoyalTeaPoints());
                orderData.setLoyalTeaCount(orderNotification.getLoyalTeaCount());
                orderData.setOrderAmt(orderNotification.getOrderAmt());
                orderData.setSavingAmt(orderNotification.getSavingAmt());
                orderData.setSavingText(orderNotification.getSavingText());
                orderData.setOrderFeedBackUrl(orderNotification.getOrderFeedBackUrl());
                orderData.setOrderRecieptUrl(orderNotification.getOrderRecieptUrl());
                orderData.setIsSubscriptionUsed(orderNotification.getIsSubscriptionUsed());
                orderData.setSubscriptionValidityInDays(orderNotification.getSubscriptionValidityInDays());
                orderData.setWalletPurchaseAmount(Objects.isNull(orderNotification.getWalletPurchaseAmt())?0:orderNotification.getWalletPurchaseAmt());
                orderData.setWalletPendingAmount(Objects.isNull(orderNotification.getWalletPendingAmount())?0:orderNotification.getWalletPendingAmount());
                orderData.setIsWalletPurchased(orderNotification.getIsWalletPurchased());
                orderData.setWalletSavingAmount(orderNotification.getWalletSavingAmount());
                orderData.setWalletExtraAmount(Objects.isNull(orderNotification.getWalletExtraAmount())?BigDecimal.ZERO:orderNotification.getWalletExtraAmount());
                orderData.setWalletUsed(orderNotification.getWalletUsed());
                orderData.setGenerateOrderId(orderNotification.getGenerateOrderId());
                orderData.setItemCode(orderNotification.getItemCode());
                orderData.setCashPendingAmount(orderNotification.getCashPendingAmount());
                orderData.setVoucherCode(orderNotification.getVoucherCode());
                orderData.setSmsTemplateDate(orderNotification.getSmsTemplateDate());
                orderData.setUsedAmount(orderNotification.getUsedAmount());
                orderData.setCashBackAmount(orderNotification.getCashBackAmount());
                orderData.setCashBackAllotmentStartDate(orderNotification.getCashBackAllotmentStartDate());
                orderData.setCashBackAllotmentEndDate(orderNotification.getCashBackAllotmentEndDate());
                orderData.setRefundAmount(orderNotification.getRefundAmount());
                orderData.setSmsSubscriber(orderNotification.isSmsSubscriber());
                orderData.setWhatsAppOptIn(orderNotification.isWhatsAppOptIn());
                orderData.setCustomerContactNumber(orderNotification.getCustomerContactNumber());
                orderData.setIsLoyaltyUnlocked(orderNotification.getIsLoyaltyUnlocked());
                orderData.setSubscriptionValidityDaysLeft(orderNotification.getDaysLeft());
                orderData.setIsSubscriptionPurchased(orderNotification.getIsSubscriptionPurched());
            } catch (Exception e) {
                LOG.error(
                        "Exception faced while converting order notification payload to clevertap charged event for orderId :{}",
                        orderData.getOrderId(), e);
            }
            LOG.info("Charged Event Notification Data Converter : took {} ms", System.currentTimeMillis() - startTime);
        }
    private void calculateAll(OrderDetail order, Map<Integer, Product> productDetails,
                              List<ClevertapChargedEventItemData> itemList, ClevertapChargedEventData orderData) {
        Map<Integer, Map<String,Object>> tempItemMap = new HashMap<>();
        int beveragePax = 0;
        int foodPax = 0;
        int beverageQuantity = 0;
        int foodQuantity = 0;
        BigDecimal beverageAmount = BigDecimal.ZERO;
        BigDecimal foodAmount = BigDecimal.ZERO;
        int quantity = 0;
		int pax = 0;
        Map<Integer, ClevertapChargedEventItemData> orderItemMap = new HashMap<>();
        for (OrderItem item : order.getOrderItems()) {
            Product product = productDetails.get(item.getProductId());
            checkSubscriptionOrder(product,orderData);
            quantity += item.getQuantity();
            if (Objects.isNull(tempItemMap.get(item.getProductId()))) {
                tempItemMap.put(item.getProductId(), new HashMap<>());
            }
            Map<String, Object> itemAttributes = tempItemMap
                    .get(item.getProductId());
            ClevertapChargedEventItemData currentOrderItem = orderItemMap.get(item.getProductId());
			currentOrderItem =initializeOrderItem(currentOrderItem,itemList,item,product,itemAttributes);
            String foodClass = Objects.nonNull(currentOrderItem) ? currentOrderItem.getFoodClass() : "";
			pax = currentOrderItem.getPaxCount();
            if (foodClass.equalsIgnoreCase("FOOD")) {
                foodPax += pax;
                foodQuantity += item.getQuantity();
                foodAmount = foodAmount.add(item.getPaidAmount());
            } else if (foodClass.equalsIgnoreCase("BEV")) {
                beveragePax += pax;
                beverageQuantity += item.getQuantity();
                beverageAmount = beverageAmount.add(item.getPaidAmount());
            }
            orderItemMap.put(item.getProductId(), currentOrderItem);
        }
	    setBeverageAndFoodQuantity(beverageQuantity,foodQuantity,orderData,order);
		orderData.setPax(Math.max(beveragePax, foodPax));
        orderData.setFoodAmount(foodAmount);
        orderData.setBeverageAmount(beverageAmount);
        orderData.setFoodQuantity(foodQuantity);
        orderData.setBeverageQuantity(beverageQuantity);
        orderData.setTotalQuantity(quantity);
    }

	public ClevertapChargedEventData convertWalletEventOrderData(OrderDetail order, CustomerInfo customer,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		LOG.info("Inside wallet purchase event data converter");
		ClevertapChargedEventData orderData = new ClevertapChargedEventData();
		Map<String, Object> attributeMap = clevertapAttributesService.getEventAttributes(order, customer);
		try {
			if(attributeMap.get("overallOrderCount") instanceof Long){
				orderData.setIsNewCustomer(boolToString((((Long) attributeMap.get("overallOrderCount"))<= 0 )));
			} else{
				orderData.setIsNewCustomer(boolToString((((BigInteger) attributeMap.get("overallOrderCount")).compareTo(BigInteger.ONE) <= 0 )));
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for overallOrderCount in Clevertap charged event for order {}", orderData.getOrderId());
		}
		orderData.setUnitId(order.getUnitId());
		orderData.setBrandId(order.getBrandId());
		orderData.setOrderId(order.getOrderId());
		orderData.setChannelPartnerId(order.getChannelPartnerId());
		orderData.setOrderSourceVersion(order.getSourceVersion());
		orderData.setUnitName(masterDataCache.getUnit(order.getUnitId()).getName());
		orderData.setCustomerId(order.getCustomerId());
		orderData.setOrderSource(order.getOrderSource());
		orderData.setBusinessDate(CLEVERTAP_DATE_PREFIX+ AppUtils.getBusinessDate().toInstant().getEpochSecond());
		orderData.setTaxableAmount(order.getTaxableAmount());
		orderData.setBillingServerTime(
				CLEVERTAP_DATE_PREFIX + order.getBillingServerTime().toInstant().getEpochSecond());
		if (Objects.nonNull(orderNotification)) {
			setChargedEventNotificationPayload(orderData, orderNotification);
		}
		LOG.info("wallet purchase event data converter : took {} ms", System.currentTimeMillis()-startTime);
		return orderData;
	}

	public ClevertapChargedEventData convertSubscriptionEventOrderData(OrderDetail order, CustomerInfo customer,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		LOG.info("Inside subscription purchase event data converter");
		ClevertapSubscriptionEventData orderData = new ClevertapSubscriptionEventData();
		Map<String, Object> attributeMap = clevertapAttributesService.getSubscriptionAttributes(order, customer);
		orderData.setUnitId(order.getUnitId());
		orderData.setBrandId(order.getBrandId());
		orderData.setChannelPartnerId(order.getChannelPartnerId());
		orderData.setOrderId(order.getOrderId());
		orderData.setUnitName(masterDataCache.getUnit(order.getUnitId()).getName());
		orderData.setCustomerId(order.getCustomerId());
		orderData.setOrderSource(order.getOrderSource());
		orderData.setOrderStatus(order.getOrderStatus());
		orderData.setTotalAmount(order.getTotalAmount());
		orderData.setBusinessDate(CLEVERTAP_DATE_PREFIX + AppUtils.getBusinessDate().toInstant().getEpochSecond());
		orderData.setDiscountRate(order.getDiscountPercent());
		orderData.setTaxableAmount(order.getTaxableAmount());
		orderData.setDiscountAmount(order.getDiscountAmount());
		orderData.setOrderSourceVersion(order.getSourceVersion());
		orderData.setBillingServerTime(
				CLEVERTAP_DATE_PREFIX + order.getBillingServerTime().toInstant().getEpochSecond());
		try {
			if(attributeMap.get("overallOrderCount") instanceof Long){
				orderData.setIsNewCustomer(boolToString((((Long) attributeMap.get("overallOrderCount"))<= 0 )));
			} else{
				orderData.setIsNewCustomer(boolToString((((BigInteger) attributeMap.get("overallOrderCount")).compareTo(BigInteger.ONE) <= 0 )));
			}
		} catch (Exception e) {
			LOG.info("Casting Exception for isNewCustomer in Clevertap charged event for order {}", orderData.getOrderId());
		}
		order.getOrderSettlements().forEach(orderSettlement -> {
			if (orderSettlement.getPaymentModeId() == 10)
				orderData.setGcPaymentFlag(1);
		});
		try {
			orderData.setSubscriptionPlanCode((String) attributeMap.get("planCode"));
		} catch (Exception e) {
			LOG.info("Casting Exception for planCode in Clevertap subscription purchase event for order {}", orderData.getOrderId());
		}
		try {
			if (Objects.nonNull(attributeMap.get("planStartDate")))
				orderData.setPlanStartDate(CLEVERTAP_DATE_PREFIX + ( attributeMap.get("planStartDate")).toString());
		} catch (Exception e) {
			LOG.info("Casting Exception for planStartDate in Clevertap subscription purchase event for order {}", orderData.getOrderId());
		}
		try {
			if (Objects.nonNull(attributeMap.get("planEndDate")))
				orderData.setSubscriptionPlanEndDate(CLEVERTAP_DATE_PREFIX + ( attributeMap.get("planEndDate")).toString());
		} catch (Exception e) {
			LOG.info("Casting Exception for subscriptionPlanEndDate in Clevertap subscription purchase event for order {}", orderData.getOrderId());
		}
		try {
			orderData.setEventType((String) attributeMap.get("eventType"));
		} catch (Exception e) {
			LOG.info("Casting Exception for eventType in Clevertap subscription purchase event for order {}", orderData.getOrderId());
		}
		if (Objects.nonNull(orderNotification)) {
			setChargedEventNotificationPayload(orderData, orderNotification);
		}
		LOG.info("subscription purchase event data converter : took {} ms",
				System.currentTimeMillis()-startTime);
		return orderData;

	}

	private void checkSubscriptionOrder(Product product,ClevertapChargedEventData orderData){
		if (product.getSubType() == 3810) {
			orderData.setSubscriptionPurchaseFlag(1);
			orderData.setOfferClass("SUBSCRIPTION");
		}
	}

	private ClevertapChargedEventItemData initializeOrderItem(ClevertapChargedEventItemData currentOrderItem,List<ClevertapChargedEventItemData> itemList,OrderItem item,Product product,Map<String, Object> itemAttributes) {
		String foodClass ;
		int previousPax = 0;
		if (Objects.isNull(currentOrderItem)) {
			currentOrderItem = new ClevertapChargedEventItemData();
			itemList.add(currentOrderItem);
			currentOrderItem.setProductId(item.getProductId());
			currentOrderItem.setProductName(item.getProductName());
			currentOrderItem.setItemTotalAmount(item.getTotalAmount());
			currentOrderItem.setItemAmountPaid(item.getPaidAmount());
			currentOrderItem.setQuantity(item.getQuantity());
			currentOrderItem
					.setProductType(masterDataCache.getProductCategory(product.getType()).getDetail().getName());
			currentOrderItem
					.setProductSubType(masterDataCache.getProductSubCategory(product.getSubType()).getName());
			int type = product.getType();
			if (type == 5 || type == 6 || type == 54)
				foodClass = "BEV";
			else if (AppConstants.CLEVERTAP_FOOD_PRODUCT_TYPE.contains(type)
					&& !AppConstants.CLEVERTAP_FOOD_PRODUCT_IDS.contains(product.getId())) {
				foodClass = "FOOD";
			} else {
				foodClass = "OTHERS";
			}

		} else {
			currentOrderItem.setItemTotalAmount(item.getTotalAmount().add(currentOrderItem.getItemTotalAmount()));
			currentOrderItem.setItemAmountPaid(item.getPaidAmount().add(currentOrderItem.getItemAmountPaid()));
			currentOrderItem.setQuantity(item.getQuantity() + currentOrderItem.getQuantity());
			foodClass = currentOrderItem.getFoodClass();
			previousPax = currentOrderItem.getPaxCount();
		}
		return  calculatePAX(previousPax,item,foodClass,currentOrderItem,itemAttributes);
	}

		private ClevertapChargedEventItemData calculatePAX(int previousPax,OrderItem item,String foodClass,ClevertapChargedEventItemData currentOrderItem,Map<String, Object> itemAttributes){
			String dimension = item.getDimension().trim();
			if (StringUtils.isNotEmpty(dimension)) {
				if (dimension.equalsIgnoreCase("CHOTIKETLI") || dimension.equalsIgnoreCase("FAMILY")) {
					itemAttributes.put("PAX", item.getQuantity() * 2 + previousPax);
					currentOrderItem.setPaxCount(item.getQuantity() * 2 + previousPax);
				} else if (dimension.equalsIgnoreCase("BADIKETLI")) {
					currentOrderItem.setPaxCount(item.getQuantity() * 5 + previousPax);
				} else {
					currentOrderItem.setPaxCount(item.getQuantity() + previousPax);
				}
			} else {
				currentOrderItem.setPaxCount(item.getQuantity() + previousPax);
			}
			currentOrderItem.setFoodClass(foodClass);
			return currentOrderItem;
		}

		private void setBeverageAndFoodQuantity(int beverageQuantity , int foodQuantity ,ClevertapChargedEventData orderData,OrderDetail order ){
			if (beverageQuantity > 0 && foodQuantity == 0) {
				orderData.setOrderFoodClass("BEV_ONLY");
			} else if (beverageQuantity == 0 && foodQuantity > 0) {
				orderData.setOrderFoodClass("FOOD_ONLY");
			} else if (beverageQuantity > 0 && foodQuantity > 0) {
				orderData.setOrderFoodClass("FOOD_BEV");
			} else {
				orderData.setOrderFoodClass("OTHERS");
			}
			orderData.setOrderStatus(order.getOrderStatus());
			orderData.setGcPurchaseFlag(order.getGiftCardOrder().equalsIgnoreCase("Y") ? 1 : 0);
			orderData.setDiscountAmount(order.getTotalAmount().subtract(order.getTaxableAmount()));
			orderData.setDiscountRate(order.getDiscountPercent());
			order.getOrderSettlements().forEach(orderSettlement -> {
				if (orderSettlement.getPaymentModeId() == 10)
					orderData.setGcPaymentFlag(1);
			});
		}

		public ProfileUploadRequest convert(Integer customerId, long epochSeconds, Object data) {
			ProfileUploadRequest request = new ProfileUploadRequest();
			request.setIdentity(customerId);
			request.setTs(epochSeconds);
			request.setType(CleverTapConstants.PROFILE);
			request.setProfileData(data);
			return request;

		}

		public ClevertapOfferData convert(NextOffer offer, String offerType,
				CustomerCampaignOfferDetail customerCampaignOfferDetail) {
			ClevertapOfferData clevertapOfferData = new ClevertapOfferData();
			clevertapOfferData.setName(customerCampaignOfferDetail.getFirstName());
			clevertapOfferData.setPhone(
					customerCampaignOfferDetail.getCountryCode() + customerCampaignOfferDetail.getContactNumber());

			switch (offerType) {
			case CleverTapEvents.NEXT_BEST_OFFER:
				clevertapOfferData.setNBOCode(offer.getOfferCode());
				clevertapOfferData.setNBOValidityFrom(offer.getValidityFrom());
				clevertapOfferData.setNBOValidityTill(offer.getValidityTill());
				clevertapOfferData.setNBOCreatedAt(CLEVERTAP_DATE_PREFIX
						+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli()/1000);
				clevertapOfferData.setNBOOfferText(offer.getText());
				break;
			case CleverTapEvents.DELIVERY_NEXT_BEST_OFFER:
				clevertapOfferData.setDNBOCode(offer.getOfferCode());
				clevertapOfferData.setDNBOValidityFrom(offer.getValidityFrom());
				clevertapOfferData.setDNBOValidityTill(offer.getValidityTill());
				clevertapOfferData.setDNBOCreatedAt(CLEVERTAP_DATE_PREFIX
						+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli()/1000);
				clevertapOfferData.setDNBOOfferText(offer.getText());
				break;
			case CleverTapEvents.GENERAL_OFFER:
				clevertapOfferData.setGenOfferCode(offer.getOfferCode());
				clevertapOfferData.setGenOfferValidityFrom(offer.getValidityFrom());
				clevertapOfferData.setGenOfferValidityTill(offer.getValidityTill());
				clevertapOfferData.setGenCreatedAt(CLEVERTAP_DATE_PREFIX
						+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli()/1000);
				clevertapOfferData.setGenOfferText(offer.getText());
				break;
			case CleverTapEvents.DELIVERY_GENERAL_OFFER:
				clevertapOfferData.setDeliveryGenCode(offer.getOfferCode());
				clevertapOfferData.setDeliveryGenValidityFrom(offer.getValidityFrom());
				clevertapOfferData.setDeliveryGenValidityTill(offer.getValidityTill());
				clevertapOfferData.setDeliveryGenCreatedAt(CLEVERTAP_DATE_PREFIX
						+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli()/1000);
				clevertapOfferData.setDeliveryGenOfferText(offer.getText());
				break;
			default:
				LOG.info("unknown offer type for customer {}", offer.getCustomerId());
				break;
			}
			return clevertapOfferData;
		}

    }
