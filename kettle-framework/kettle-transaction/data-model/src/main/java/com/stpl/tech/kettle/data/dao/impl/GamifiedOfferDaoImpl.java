package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.clm.dao.impl.CLMDataAbstractDaoImpl;
import com.stpl.tech.kettle.data.dao.GamifiedOfferDao;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class GamifiedOfferDaoImpl extends CLMDataAbstractDaoImpl implements GamifiedOfferDao {

    @Autowired
    private EnvironmentProperties properties;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DroolsCustomerProperties getCustomerProperties(int id, int brandId) {
        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_DROOL_DECISION(:customerId, :brandId)");
            if(!AppUtils.isProd(properties.getEnvironmentType())){
                query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_DROOL_DECISION_TEST(:customerId, :brandId)");
            }
            query.setParameter("customerId", id);
            query.setParameter("brandId",brandId);
            List<Object[]> data = query.getResultList();
            if(data.isEmpty()){
                return getProperties(null);
            }
            return getProperties(data.get(0));
        }catch (Exception e){
            log.error("Error while getting customer properties from one view for customer id: {}, brand id: {}",
                    id, brandId,e);
        }
        return null;
    }

    private DroolsCustomerProperties getProperties(Object[] data){
        if(Objects.isNull(data)){
            return DroolsCustomerProperties.builder().isNewCustomer(AppConstants.YES).build();
        }
        return DroolsCustomerProperties.builder()
                .dineInFlag(Integer.parseInt(String.valueOf(data[0])))
                .deliveryFlag(Integer.parseInt(String.valueOf(data[1])))
                .visitsCount(Integer.parseInt(String.valueOf(data[2])))
                .dineInVisitsCount(Integer.parseInt(String.valueOf(data[3])))
                .visitsCount30Days(Integer.parseInt(String.valueOf(data[4])))
                .visitsCount90Days(Integer.parseInt(String.valueOf(data[5])))
                .netSales(Integer.parseInt(String.valueOf(data[6])))
                .latestOrderDayDiff(Objects.nonNull(data[7]) ? AppUtils.getActualDayDifference(
                        AppUtils.getDate(String.valueOf(data[7]),AppUtils.DATE_FORMAT_STRING),
                        AppUtils.getCurrentDate()): -1000000)
                .giftCardBalance(new BigDecimal(String.valueOf(data[8])))
                .subscriptionExpiryDayDiff(Objects.nonNull(data[9]) ? AppUtils.getActualDayDifference(AppUtils.getCurrentDate(),
                        AppUtils.getDate(String.valueOf(data[9]),AppUtils.DATE_FORMAT_STRING)) : -1000000)
                .loyaltyPointsBalance(Integer.parseInt(String.valueOf(data[10])))
                .numberOfPax(new BigDecimal(String.valueOf(data[11])))
                .apc(new BigDecimal(String.valueOf(data[12])))
                .isNewCustomer(AppConstants.NO)
                .build();
    }
}
