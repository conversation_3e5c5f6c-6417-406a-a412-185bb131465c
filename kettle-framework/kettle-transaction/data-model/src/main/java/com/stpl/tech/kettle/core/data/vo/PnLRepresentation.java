package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.master.core.exception.DataUpdationException;

public class PnLRepresentation {

    List<PnlRecord> records;

    public List<PnlRecord> getRecords() {
        return records;
    }

    public void setRecords(List<PnlRecord> records) {
        this.records = records;
    }

    public static class PnLRepresentationBuilder {
        List<PnlRecord> records;
        PnlRecord current;

        public PnLRepresentationBuilder add() throws DataUpdationException {
            if (this.current == null) {
                throw new DataUpdationException("Cannot Call add before adding key and value method call");
            }
            if (this.records == null) {
                this.records = new ArrayList<>();
            }
            this.records.add(this.current);
            this.current = null;
            return this;
        }

        public PnLRepresentationBuilder key(String key) throws DataUpdationException {
            if (this.current != null) {
                throw new DataUpdationException("Cannot Call key before add method");
            }
            this.current = new PnlRecord(key);
            return this;
        }

        public PnLRepresentationBuilder currentValue(String value) throws DataUpdationException {
            if (this.current == null) {
                throw new DataUpdationException("Cannot Call Value before key method");
            }
            this.current.setCurrentValue(value);
            return this;
        }

        public PnLRepresentationBuilder currentValue(BigDecimal value) throws DataUpdationException {
            return currentValue(value == null ? "0.00" : value.toString());
        }

        public PnLRepresentationBuilder currentValue(Integer value) throws DataUpdationException {
            return currentValue(value == null ? "0" : value.toString());
        }

        public PnLRepresentationBuilder mtdValue(List<String> value) throws DataUpdationException {
            if (this.current == null) {
                throw new DataUpdationException("Cannot Call Value before key method");
            }
            this.current.setMtdValue(value);
            return this;
        }

//        public PnLRepresentationBuilder mtdValue(List<BigDecimal> value) throws DataUpdationException {
//            return mtdValue(value.get(0) == null ? "0.00" : value.toString());
//        }
//
//        public PnLRepresentationBuilder mtdValue(Integer value) throws DataUpdationException {
//            return mtdValue(value == null ? "0" : value.toString());
//        }

        public PnLRepresentationBuilder budget(String value) throws DataUpdationException {
            if (this.current == null) {
                throw new DataUpdationException("Cannot Call Value before key method");
            }
            this.current.setBudget(value);
            return this;
        }

        public PnLRepresentationBuilder budget(Integer value) throws DataUpdationException {
            return budget(value == null ? "0" : value.toString());
        }

        public PnLRepresentationBuilder budget(BigDecimal value) throws DataUpdationException {
            return budget(value == null ? "0.00" : value.toString());
        }

        public PnLRepresentationBuilder budgetMtd(String value) throws DataUpdationException {
            if (this.current == null) {
                throw new DataUpdationException("Cannot Call Value before key method");
            }
            this.current.setBudgetMtd(value);
            return this;
        }

        public PnLRepresentationBuilder budgetMtd(Integer value) throws DataUpdationException {
            return budgetMtd(value == null ? "0" : value.toString());
        }

        public PnLRepresentationBuilder budgetMtd(BigDecimal value) throws DataUpdationException {
            return budgetMtd(value == null ? "0.00" : value.toString());
        }


        public PnLRepresentation build() {
            PnLRepresentation r = new PnLRepresentation();
            r.records = this.records;
            return r;
        }

        public PnLRepresentationBuilder drilldown(String key, String currentValue, List<String> mtdValue, String budget,String budgetMtd) throws DataUpdationException {
            if (this.current == null) {
                throw new DataUpdationException("Cannot Call drilldown before adding key and value method call");
            }
            this.current.getDrilldowns().add(new PnlRecord(key, currentValue, mtdValue, budget,budgetMtd));
            return this;
        }
    }
}
