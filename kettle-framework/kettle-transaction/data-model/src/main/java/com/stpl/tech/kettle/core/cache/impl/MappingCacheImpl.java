package com.stpl.tech.kettle.core.cache.impl;

import com.stpl.tech.kettle.core.cache.MappingCache;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import com.stpl.tech.kettle.core.cache.MappingCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class MappingCacheImpl implements MappingCache {

	private static final Logger LOG = LoggerFactory.getLogger(MappingCacheImpl.class);

	@Autowired
	private MappingCacheService service;

	private static final String MAPPING_CACHE_OFFER_LOCK = "MAPPING_CACHE_OFFER_LOCK";

	private Map<String, SubscriptionOfferInfoDetail> subscriptionOfferInfoDetailMap= new HashMap<>() ;


	@Override
	public SubscriptionOfferInfoDetail getSubscriptionInfoDetail(SubscriptionPlan subscriptionPlan){
		if (!subscriptionOfferInfoDetailMap.containsKey(subscriptionPlan.getSubscriptionPlanCode())) {
			LOG.info("Fetching Offer Detail For Coupon Code ::: {}",subscriptionPlan.getSubscriptionPlanCode());
			synchronized (MAPPING_CACHE_OFFER_LOCK) {
				SubscriptionOfferInfoDetail subscriptionOfferInfoDetail = service.findSubscriptionDetail(subscriptionPlan.getSubscriptionPlanCode());
				if(Objects.nonNull(subscriptionOfferInfoDetail)){
					subscriptionOfferInfoDetailMap.put(subscriptionOfferInfoDetail.getSubscriptionCode(), subscriptionOfferInfoDetail);
				}
				return subscriptionOfferInfoDetail;
			}
		}
		else {
			return subscriptionOfferInfoDetailMap.get(subscriptionPlan.getSubscriptionPlanCode());
		}
	}

	@Override
	public void removeAllSubscription(){
		subscriptionOfferInfoDetailMap.clear();
	}

}
