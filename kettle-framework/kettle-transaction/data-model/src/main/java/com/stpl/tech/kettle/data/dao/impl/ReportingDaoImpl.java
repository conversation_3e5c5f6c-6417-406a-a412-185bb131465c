/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.data.vo.RevenueCertificateData;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.ReportingDao;
import com.stpl.tech.kettle.data.model.ExpenseUpdateEventData;
import com.stpl.tech.kettle.data.model.ReportDefinition;
import com.stpl.tech.kettle.data.model.ReportExecutionDetail;
import com.stpl.tech.kettle.data.model.UnitExpenseDetail;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.domain.model.ReportDef;
import com.stpl.tech.kettle.domain.model.UnitExpense;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.util.CollectionUtils;

@Repository
public class ReportingDaoImpl extends AbstractDaoImpl implements ReportingDao {

	private static final Logger LOG = LoggerFactory.getLogger(ReportingDaoImpl.class);

	public ReportDef getReportDefinition(int reportId) throws DataNotFoundException {

		try {
			ReportDefinition definition = manager.find(ReportDefinition.class, reportId);
			return DataConverter.convert(definition);
		} catch (NoResultException e) {
			throw new DataNotFoundException(String.format("Did not find Report Definition with ID : %d", reportId), e);
		}
	}

	public ReportExecutionDetail createReportExecutionDetail(int unitId, int reportDefId) {
		ReportDefinition definition = manager.find(ReportDefinition.class, reportDefId);
		ReportExecutionDetail executionDetail = new ReportExecutionDetail(definition, ReportStatus.CREATED.name());

		manager.persist(executionDetail);
		LOG.info(String.format("Added Report execution request for unit %d and report def %d", unitId, reportDefId));
		return executionDetail;
	}

	public void updateStatus(int executionDetailId, ReportStatus status) throws DataUpdationException {
		try {
			ReportExecutionDetail executionDetail = manager.find(ReportExecutionDetail.class, executionDetailId);
			executionDetail.setCurrentStatus(status.name());
		} catch (Exception e) {
			throw new DataUpdationException(String.format(
					"Error while updatting status for the report with detail productId : %d", executionDetailId), e);
		}
	}

	public ReportExecutionDetail getReportExecutionDetail(int id) {

		return manager.find(ReportExecutionDetail.class, id);
	}

	@Override
	public List<UnitExpense> addUnitExpenses(ExpenseUpdateEvent event, List<UnitExpense> unitExpenses) {
		// Deactivate entries filling this criteria
		deactivatePreviousUnitExpenseDetail(event);
		for (UnitExpense expense : unitExpenses) {
			addUnitExpense(event, expense);
		}
		return getUnitExpense(event.getEventId());
	}

	public void addUnitExpense(ExpenseUpdateEvent event, UnitExpense unitExpense) {
		// Add new Entry
		UnitExpenseDetail expenseDetail = new UnitExpenseDetail();
		setValues(unitExpense, expenseDetail, event);
		manager.persist(expenseDetail);
		manager.flush();
	}

	private void deactivatePreviousUnitExpenseDetail(ExpenseUpdateEvent event) {
		Query query = manager.createQuery(
				"UPDATE UnitExpenseDetail U SET U.status = :status WHERE U.year = :year AND U.iterationNumber = :iterationNumber AND U.type = :type");
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("year", event.getYear());
		query.setParameter("iterationNumber", event.getIterationNumber());
		query.setParameter("type", event.getType().name());
		query.executeUpdate();
	}

	private void setValues(UnitExpense unitExpense, UnitExpenseDetail expenseDetail, ExpenseUpdateEvent event) {

		// Event Fields
		expenseDetail.setType(event.getType().name());
		expenseDetail.setYear(event.getYear());
		expenseDetail.setUpdatedBy(event.getUpdatedByUserName());
		expenseDetail.setIterationNumber(event.getIterationNumber());
		expenseDetail.setExpenseUpdateEventId(event.getEventId());

		// Expense Fields
		expenseDetail.setAmexCardCharges(unitExpense.getAmexCardCharges());
		expenseDetail.setCamCharges(unitExpense.getCamCharges());
		expenseDetail.setChangeCommission(unitExpense.getChangeCommission());
		expenseDetail.setChannelPartnerCharges(unitExpense.getChannelPartnerCharges());
		expenseDetail.setChargesDG(unitExpense.getChargesDG());
		expenseDetail.setCleaningCharges(unitExpense.getCleaningCharges());
		expenseDetail.setCogs(unitExpense.getCogs());
		expenseDetail.setComments(unitExpense.getComments());
		expenseDetail.setConsumablesAndUtilities(unitExpense.getConsumablesAndUtilities());
		expenseDetail.setConsumablesCutlery(unitExpense.getConsumablesCutlery());
		expenseDetail.setConsumablesEquipment(unitExpense.getConsumablesEquipment());
		expenseDetail.setConsumablesStationary(unitExpense.getConsumablesStationary());
		expenseDetail.setConsumablesUniform(unitExpense.getConsumablesUniform());
		expenseDetail.setConvenyance(unitExpense.getConvenyance());
		expenseDetail.setCourier(unitExpense.getCourier());
		expenseDetail.setCreditCardCharges(unitExpense.getCreditCardCharges());
		expenseDetail.setCustomerCareCost(unitExpense.getCustomerCareCost());
		expenseDetail.setDeliveryCost(unitExpense.getDeliveryCost());
		expenseDetail.setEbitdaPercentage(unitExpense.getEbitdaPercentage());
		expenseDetail.setEdcMachine(unitExpense.getEdcMachine());
		expenseDetail.setElectricity(unitExpense.getElectricity());
		expenseDetail.setEmployeeMeal(unitExpense.getEmployeeMeal());
		expenseDetail.setFixedRent(unitExpense.getFixedRent());
		expenseDetail.setFreightOutward(unitExpense.getFreightOutward());
		expenseDetail.setGmvAmount(unitExpense.getGmvAmount());
		expenseDetail.setInsurance(unitExpense.getInsurance());
		expenseDetail.setInternet(unitExpense.getInternet());
		expenseDetail.setItTeamCost(unitExpense.getItTeamCost());
		expenseDetail.setKitchenCostTotal(unitExpense.getKitchenCostTotal());
		expenseDetail.setKitchenCostPercentage(unitExpense.getKitchenCostPercentage());
		expenseDetail.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		expenseDetail.setManpower(unitExpense.getManpower());
		expenseDetail.setMarketingAndSampling(unitExpense.getMarketingAndSampling());
		expenseDetail.setUnsatifiedCustomerCost(unitExpense.getUnsatifiedCustomerCost());
		expenseDetail.setPPECost(unitExpense.getPPECost());
		expenseDetail.setManualAdjustments(unitExpense.getManualAdjustments());
		expenseDetail.setMaintenanceTeamCost(unitExpense.getMaintenanceTeamCost());
		expenseDetail.setMiscExp(unitExpense.getMiscExp());
		expenseDetail.setMsp(unitExpense.getMsp());
		expenseDetail.setNetSalesAmount(unitExpense.getNetSalesAmount());
		expenseDetail.setNetTickets(unitExpense.getNetTickets());
		expenseDetail.setNewspaper(unitExpense.getNewspaper());
		expenseDetail.setOpsCostTotal(unitExpense.getOpsCostTotal());
		expenseDetail.setOpsCostPrecentage(unitExpense.getOpsCostPercentage());
		expenseDetail.setParkingCharges(unitExpense.getParkingCharges());
		expenseDetail.setPrintingAndStationery(unitExpense.getPrintingAndStationery());
		expenseDetail.setRent(unitExpense.getRent());
		expenseDetail.setRentDG(unitExpense.getRentDG());
		expenseDetail.setRentPercentage(unitExpense.getRentPercentage());
		expenseDetail.setRepairAndMaintenanceMajor(unitExpense.getRepairAndMaintenanceMajor());
		expenseDetail.setRepairAndMaintenanceMinor(unitExpense.getRepairAndMaintenanceMinor());
		expenseDetail.setScmRental(unitExpense.getScmRental());
		expenseDetail.setSodexoCharges(unitExpense.getSodexoCharges());
		expenseDetail.setStaffWelfare(unitExpense.getStaffWelfare());
		expenseDetail.setStatus(AppConstants.ACTIVE);
		expenseDetail.setSystemRent(unitExpense.getSystemRent());
		expenseDetail.setTelephone(unitExpense.getTelephone());
		expenseDetail.setTotalTickets(unitExpense.getTotalTickets());
		expenseDetail.setTktRestaurantCharges(unitExpense.getTktRestaurantCharges());
		expenseDetail.setTotalCost(unitExpense.getTotalCost());
		expenseDetail.setTrainingTeamCost(unitExpense.getTrainingTeamCost());
		expenseDetail.setUnitId(unitExpense.getUnitId());
		expenseDetail.setUnitName(unitExpense.getUnitName());
		expenseDetail.setWastageAndExpired(unitExpense.getWastageAndExpired());
		expenseDetail.setWater(unitExpense.getWater());

	}

	@Override
	public List<UnitExpense> getUnitExpenseForWeek(int year, int week) {
		return getUnitExpense(year, week, IterationType.WOW);
	}

	@Override
	public List<UnitExpense> getUnitExpenseForMonth(int year, int month) {
		return getUnitExpense(year, month, IterationType.MOM);
	}

	private List<UnitExpense> getUnitExpense(int year, int iteration, IterationType type) {
		Query query = manager.createQuery(
				"FROM UnitExpenseDetail U WHERE U.year = :year AND U.iterationNumber = :iterationNumber AND U.type = :type AND U.status= :status");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("year", year);
		query.setParameter("iterationNumber", iteration);
		query.setParameter("type", type.name());
		List<UnitExpense> output = new ArrayList<>();
		@SuppressWarnings("unchecked")
		List<UnitExpenseDetail> list = query.getResultList();
		if (list == null) {
			return output;
		}
		for (UnitExpenseDetail expenseDetail : list) {
			output.add(DataConverter.convert(expenseDetail));
		}
		return output;
	}

	private List<UnitExpense> getUnitExpense(int eventId) {
		Query query = manager.createQuery("FROM UnitExpenseDetail U WHERE U.expenseUpdateEventId = :eventId");
		query.setParameter("eventId", eventId);
		List<UnitExpense> output = new ArrayList<>();
		@SuppressWarnings("unchecked")
		List<UnitExpenseDetail> list = query.getResultList();
		if (list == null) {
			return output;
		}
		for (UnitExpenseDetail expenseDetail : list) {
			output.add(DataConverter.convert(expenseDetail));
		}
		return output;
	}

	@Override
	public ExpenseUpdateEvent addExpenseUpdateEvent(ExpenseUpdateEvent event) {
		ExpenseUpdateEventData data = new ExpenseUpdateEventData();
		data.setAddedByUserId(event.getAddedByUserId());
		data.setAddedByUserName(event.getAddedByUserName());
		data.setUpdatedByUserId(event.getUpdatedByUserId());
		data.setUpdatedByUserName(event.getUpdatedByUserName());
		data.setEventTimestamp(event.getEventTimestamp());
		data.setEventDescription(event.getDescription());
		data.setErrorMessage(event.getErrorMessage());
		data.setYear(event.getYear());
		data.setInputFileName(event.getInputFileName());
		data.setIterationNumber(event.getIterationNumber());
		data.setNoOfRows(event.getNoOfRows());
		data.setStatus(event.getStatus());
		data.setStoredFileName(event.getStoredFileName());
		data.setType(event.getType().name());
		data.setStartOrderId(event.getStartOrderId());
		data.setEndOrderId(event.getEndOrderId());
		data.setStartDate(event.getStartDate());
		data.setEndDate(event.getEndDate());
		manager.persist(data);
		manager.flush();
		event.setEventId(data.getEventId());
		return event;
	}

	@Override
	public ExpenseUpdateEvent getExpenseUpdateEvent(int eventId) {
		ExpenseUpdateEventData data = manager.find(ExpenseUpdateEventData.class, eventId);
		return data == null ? null : DataConverter.convert(data);
	}

	@Override
	public int getLastOrderIdForBusinessDate(Date businessDate) {
		Query query = manager.createQuery(
				"select max(lastOrderId) FROM UnitClosureDetails E where E.businessDate <= :businessDate and currentStatus <> :currentStatus");
		query.setParameter("businessDate", businessDate);
		query.setParameter("currentStatus", ClosureState.CANCELLED.name());
		Object o = query.getSingleResult();
		return o == null ? 0 : (Integer) o;
	}

	@Override
	public void updateExpenseResultData(int expenseUpdateEventId) {
		Query query = manager.createNativeQuery("CALL SP_UPDATE_EXPENSE_DETAIL_DATA(:expenseUpdateEventId)");
		query.setParameter("expenseUpdateEventId", expenseUpdateEventId);
		query.executeUpdate();
	}

	@Override
	public List<UnitExpense> getUnitExpensesForEvent(ExpenseUpdateEvent event) {
		return getUnitExpense(event.getEventId());
	}

	@Override
	public List<Integer> getUnitWithSales(int month, int year) {
		Query query = manager
				.createQuery("SELECT DISTINCT(E.unitId) FROM UnitClosureDetails E WHERE MONTH(E.businessDate) = :month "
						+ " AND YEAR(E.businessDate) = :year AND currentStatus <> :currentStatus ");
		query.setParameter("month", month);
		query.setParameter("currentStatus", ClosureState.CANCELLED.name());
		query.setParameter("year", year);
		return query.getResultList();
	}

	@Override
	public List<RevenueCertificateData> getRevenueCertificateData(int month, int year,Integer unitId,List<Integer> exclusionList) {

		Date startDate = AppUtils.getStartOfMonth2(year, month);
		Date endDate = AppUtils.getEndOfMonth(year, month);

		if(Objects.nonNull(unitId)) {
			if (unitId == 26255) {
				Integer prevoiusMonth = AppUtils.getPreviousMonthFromDate(AppUtils.getCurrentDate());
				Integer thisYear = AppUtils.getCurrentYear();
				if (prevoiusMonth == 11) {
					thisYear = AppUtils.getPreviousYear(AppUtils.getCurrentDate()).getYear();
				}
				startDate = AppUtils.getDate(26, prevoiusMonth, thisYear);
				endDate = AppUtils.getDate(25, month, year);
				exclusionList.remove(unitId);

			}
		}

		List<RevenueCertificateData> list = new ArrayList<>();
		List<String> orderTypeList = new ArrayList<>();
		orderTypeList.add(AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL);
		orderTypeList.add(AppConstants.ORDER_TYPE_REGULAR);
		StringBuilder queryString = new StringBuilder(
				"SELECT E.orderDetail.unitId, E.orderDetail.businessDate, SUM(E.discountAmount + E.promotionalDiscount),"
						+ " SUM(E.taxAmount) , SUM(E.paidAmount)"
						+ " FROM OrderItem E WHERE E.orderDetail.businessDate >= :startDate  "
						+ " AND E.orderDetail.businessDate <= :endDate AND E.orderDetail.orderStatus <> :currentStatus "
						+ " AND E.orderDetail.orderType IN :orderType AND E.taxCode <> :giftCard");
		if(unitId!=null){
			queryString.append(" AND E.orderDetail.unitId = :unitId");
		}
		if(!CollectionUtils.isEmpty(exclusionList)){
			queryString.append(" And E.orderDetail.unitId not in :exclusionUnits ");
		}
		queryString.append(" GROUP BY E.orderDetail.businessDate, E.orderDetail.unitId");

		Query query = manager.createQuery(queryString.toString());
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		query.setParameter("currentStatus", ClosureState.CANCELLED.name());
		query.setParameter("orderType", orderTypeList);
		query.setParameter("giftCard", AppConstants.GIFT_CARD_TAX_CODE);
		if(!CollectionUtils.isEmpty(exclusionList)){
			query.setParameter("exclusionUnits",exclusionList);
		}
		if(unitId!=null){
			query.setParameter("unitId",unitId);
		}



		List<Object[]> resultSet = query.getResultList();
		for (Object[] record : resultSet) {
			// new RevenueCertificateData(unitId, date, discount, taxes, netSale)
			list.add(new RevenueCertificateData((Integer) record[0], (Date) record[1],
					record[2] == null ? BigDecimal.ZERO : (BigDecimal) record[2], (BigDecimal) record[3],
					(BigDecimal) record[4], BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));
		}

		StringBuilder queryString2 = new StringBuilder("SELECT E.unitId, E.businessDate, SUM(E.discountAmount + E.promotionalDiscount)"
						+ " FROM OrderDetail E WHERE E.businessDate >= :startDate  "
						+ " AND E.businessDate <= :endDate AND E.orderStatus <> :currentStatus "
						+ " AND E.orderType IN :orderType");
		if(unitId != null){
			queryString2.append(" AND E.unitId = :unitId");
		}
		if(!CollectionUtils.isEmpty(exclusionList)){
			queryString.append(" And E.unitId not in :exclusionUnits ");
		}
		queryString2.append(" GROUP BY E.businessDate, E.unitId");
		Query query2 = manager.createQuery(queryString2.toString());

		query2.setParameter("startDate", startDate);
		query2.setParameter("endDate", endDate);
		query2.setParameter("currentStatus", ClosureState.CANCELLED.name());
		query2.setParameter("orderType", orderTypeList);
		if(unitId!=null){
			query2.setParameter("unitId",unitId);
		}

		List<Object[]> resultSet2 = query2.getResultList();
		for (Object[] record : resultSet2) {
			for (RevenueCertificateData data : list) {
				if (data.getUnitId() == (Integer) record[0] && data.getDate().equals((Date) record[1])) {
					data.setDiscount(record[2] == null ? BigDecimal.ZERO : (BigDecimal) record[2]);
					data.setGrossSales(
							AppUtils.add(AppUtils.add(data.getNetSale(), data.getTaxes()), data.getDiscount())
									.setScale(0, BigDecimal.ROUND_HALF_UP));
					data.setSalesAfterServiceCharge(data.getGrossSales());
				}
			}
		}
        StringBuilder queryString3 = new StringBuilder(
		"SELECT E.orderDetail.businessDate, E.orderDetail.unitId, " +
						"E.orderDetail.brandId, E.orderDetail.orderSource, SUM(E.paidAmount)" +
				" FROM OrderItem E WHERE E.orderDetail.businessDate >= :startDate" +
				" AND E.orderDetail.businessDate <= :endDate" +
				" AND E.orderDetail.orderStatus <> :currentStatus" +
				" AND E.orderDetail.orderType IN :orderType" +
				" AND E.taxCode <> :giftCard");
		if(unitId!=null){
			queryString3.append(" AND E.orderDetail.unitId = :unitId");
		}
		if(!CollectionUtils.isEmpty(exclusionList)){
			queryString.append(" And E.orderDetail.unitId not in :exclusionUnits ");
		}
		queryString3.append(" GROUP BY E.orderDetail.businessDate, E.orderDetail.unitId, E.orderDetail.brandId, E.orderDetail.orderSource");

		Query query3 = manager.createQuery(queryString3.toString());

		query3.setParameter("startDate", startDate);
		query3.setParameter("endDate", endDate);
		query3.setParameter("currentStatus", ClosureState.CANCELLED.name());
		query3.setParameter("orderType", orderTypeList);
		query3.setParameter("giftCard", AppConstants.GIFT_CARD_TAX_CODE);

		if(unitId!=null){
			query3.setParameter("unitId",unitId);
		}

		List<Object[]> resultSet3 = query3.getResultList();
		for (Object[] record : resultSet3) {
			for (RevenueCertificateData data : list) {
				if (data.getUnitId() == (Integer) record[1] && data.getDate().equals((Date) record[0])) {
					String orderSource = (String) record[3];
					if((Integer) record[2] == AppConstants.CHAAYOS_BRAND_ID) {
						if(orderSource.equals(AppConstants.COD)) {
							data.setChaayosDeliverySales(((BigDecimal) record[4]).setScale(0, BigDecimal.ROUND_HALF_UP));
						} else {
							BigDecimal prevSales = data.getChaayosDineInSales();
							BigDecimal currSales = (BigDecimal) record[4];
							data.setChaayosDineInSales(AppUtils.add(currSales, prevSales).setScale(0, BigDecimal.ROUND_HALF_UP));
						}
					} else if((Integer) record[2] == AppConstants.DESI_CANTEEN_BRAND_ID) {
						if(orderSource.equals(AppConstants.COD)) {
							data.setDesiCanteenDeliverySales(((BigDecimal) record[4]).setScale(0, BigDecimal.ROUND_HALF_UP));
						} else {
							BigDecimal prevSales = data.getDesiCanteenDineInSales();
							BigDecimal currSales = (BigDecimal) record[4];
							data.setDesiCanteenDineInSales(AppUtils.add(currSales, prevSales).setScale(0, BigDecimal.ROUND_HALF_UP));
						}
					} else if((Integer) record[2] == AppConstants.GNT_BRAND_ID) {
						if(orderSource.equals(AppConstants.COD)) {
							data.setGntDeliverySales(((BigDecimal) record[4]).setScale(0, BigDecimal.ROUND_HALF_UP));
						} else {
							BigDecimal prevSales = data.getGntDineInSales();
							BigDecimal currSales = (BigDecimal) record[4];
							data.setGntDineInSales(AppUtils.add(currSales, prevSales).setScale(0, BigDecimal.ROUND_HALF_UP));
						}
					}
				}
			}
		}

		return list;
	}

}
