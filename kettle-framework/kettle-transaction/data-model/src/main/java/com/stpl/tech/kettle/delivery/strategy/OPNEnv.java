/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

public enum OPNEnv {
	TEST("http://test.deliver.opinioapp.com"), PRODUCTION("http://deliver.opinioapp.com");

	private String value;
	private static OPNEnv[] values;

	OPNEnv(String env) {
		this.value = env;
	}

	public String getValue() {
		return this.value;
	}

	public static OPNEnv[] environments() {
		if (values == null) {
			values = OPNEnv.values();
		}
		return values;
	}
}