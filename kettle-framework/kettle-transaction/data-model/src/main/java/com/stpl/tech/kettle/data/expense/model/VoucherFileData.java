package com.stpl.tech.kettle.data.expense.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "VOUCHER_FILE_DATA")
public class VoucherFileData {
    private Integer id;
    private VoucherData voucherData;
    private String filePath;
    private String fileName;
    private String fileType;
    private String status;
    private Date createdOn;
    private String contentType;
    private String storageType;
    private String s3Bucket;
    private String s3Key;
    private String S3Url;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VOUCHER_ID", nullable = false)
    public VoucherData getVoucherData() {
        return voucherData;
    }

    public void setVoucherData(VoucherData voucherData) {
        this.voucherData = voucherData;
    }


    @Column(name = "FILE_PATH", nullable = true)
    public String getFilePath() {
        return filePath;
    }


    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Column(name = "FILE_NAME", nullable = false)
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Column(name = "FILE_TYPE")
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_ON", nullable = true)
    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    @Column(name = "CONTENT_TYPE", nullable = false)
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    @Column(name = "STORAGE_TYPE", nullable = false)
    public String getStorageType() {
        return storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    @Column(name = "S3_BUCKET", nullable = true)
    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    @Column(name = "S3_KEY", nullable = true)
    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }

    @Column(name = "S3_URL", nullable = true)
    public String getS3Url() {
        return S3Url;
    }

    public void setS3Url(String s3Url) {
        S3Url = s3Url;
    }

}
