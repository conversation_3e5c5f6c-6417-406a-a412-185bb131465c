package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.util.AppUtils;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 09-04-2018.
 */
public class ComboOfferStrategy extends PercentageItemStrategy implements OfferActionStrategy {


    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon,
                                    MasterDataCache cache, Map<String, OrderItem> foundItems) {

        OfferDetail offerDetail = coupon.getOffer();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal prepaidAmount = offerDetail.getPrepaidAmount();
        if (offerDetail.isPrepaid() && prepaidAmount!=null){
            for(OrderItem item : foundItems.values()) {
                totalAmount = totalAmount.add(getTotalAmountOfItemWithTax(item.getPrice(),item.getQuantity(), item.getTaxes()));
            }
            if(prepaidAmount.compareTo(totalAmount)>=0){
                totalAmount = totalAmount.setScale(0, RoundingMode.HALF_UP);
                order.setPrepaidAmount(totalAmount);
                getMessageTemplate().append(String.format("Prepaid Mode added of value %.2f",totalAmount.floatValue()));
            }else{
                BigDecimal discountValue = AppUtils.subtract(totalAmount, prepaidAmount);
                BigDecimal percentDiscount = AppUtils.percentageWithScale10(discountValue, totalAmount);
                for(OrderItem item : foundItems.values()) {
                    addDiscountDetails(item,percentDiscount,offerDetail,item.getQuantity());
                }
                order.setPrepaidAmount(prepaidAmount);
            }
        }
        order.setAppliedOfferMessage(getOfferMessage());
        return order;
    }

    private BigDecimal getTotalAmountOfItemWithTax(BigDecimal price, int quantity, List<TaxDetail> taxes) {
        BigDecimal totalPrice = getTotalAmountOfItem(price,quantity);
        Double totalTax = taxes.stream().mapToDouble(value -> value.getValue().doubleValue()).sum();
        return totalPrice.add(BigDecimal.valueOf(totalTax));
    }

}
