/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.DUNAddress;
import com.stpl.tech.kettle.delivery.model.DUNAddressDetails;
import com.stpl.tech.kettle.delivery.model.DUNCancelRequest;
import com.stpl.tech.kettle.delivery.model.DUNGenericRequest;
import com.stpl.tech.kettle.delivery.model.DUNRequest;
import com.stpl.tech.kettle.delivery.model.DUNUserDetails;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.AppUtils;

import java.math.BigDecimal;
import java.util.List;

public class DUNRequestAdapter implements RequestAdapter<DUNGenericRequest, OrderInfo> {

	@Override
	public DUNGenericRequest adaptCreate(OrderInfo orderInfo) {
		DUNRequest dunRequest = new DUNRequest();

		Order order = orderInfo.getOrder();
		Customer customer = orderInfo.getCustomer();
		
		Address customerAddress = TransactionUtils.getAddressForOrder(order.getDeliveryAddress(),
				customer.getAddresses());
		dunRequest.setRequest_id(order.getOrderId().toString());
		
		if (customerAddress != null) {
			String pinCode = customerAddress.getZipCode() != null ? customerAddress.getZipCode() : "";
			BigDecimal dropLocationLat = new BigDecimal(customerAddress.getLatitude());
			BigDecimal dropLocationLng = new BigDecimal(customerAddress.getLongitude());
			
			dunRequest.setDrop_details(
					new DUNAddressDetails( dropLocationLat, dropLocationLng, 
							new DUNAddress(customerAddress.getLine1(),customerAddress.getLine2(),customerAddress.getLine3(),
									customerAddress.getLandmark(),customerAddress.getCity(),customerAddress.getState(),
									pinCode, customerAddress.getCountry())));
		}
		dunRequest.setReceiver_details(
				new DUNUserDetails(customer.getFirstName(),AppUtils.removeCountryCode(customer.getContactNumber())));
		
		Address storeAddress = orderInfo.getUnit().getAddress();
		BigDecimal pickLocationLat = new BigDecimal(storeAddress.getLatitude());
		BigDecimal pickLocationLng = new BigDecimal(storeAddress.getLongitude());
		
		dunRequest.setPickup_details(
				new DUNAddressDetails(pickLocationLat, pickLocationLng,
						new DUNAddress(storeAddress.getLine1(),storeAddress.getLine2(),storeAddress.getLine3(),
									storeAddress.getLandmark(),storeAddress.getCity(),storeAddress.getState(),
									storeAddress.getZipCode(), storeAddress.getCountry())));
		dunRequest.setSender_details(
				new DUNUserDetails("",AppUtils.removeCountryCode(customer.getContactNumber())));
		String unitContact = orderInfo.getUnit().getAddress().getContact1();
		if (unitContact != null) {
			unitContact = AppUtils.removeCountryCode(unitContact);
			dunRequest.setSender_details(
					new DUNUserDetails(orderInfo.getUnit().getManagerName(),AppUtils.removeCountryCode(customer.getContactNumber())));
		} else {
			dunRequest.setSender_details(
					new DUNUserDetails(orderInfo.getUnit().getManagerName(),""));
		}
		dunRequest.setPackage_content(new String[] {"Food | Flowers"});
		return dunRequest;
	}

	@Override
	public DUNGenericRequest adaptCancel(OrderInfo order, String taskId) {
		
		DUNCancelRequest cancelRequest = new DUNCancelRequest();
		cancelRequest.setCancellation_reason(DeliveryStatus.CANCELLED.toString());
		return cancelRequest;
	}
}
