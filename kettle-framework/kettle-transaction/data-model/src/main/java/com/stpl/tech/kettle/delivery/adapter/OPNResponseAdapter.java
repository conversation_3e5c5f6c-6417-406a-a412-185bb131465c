/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.delivery.model.*;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OPNResponseAdapter
		implements ResponseAdapter<OPNResponse, OPNErrorResponse, OPNCallbackObject, DeliveryResponse> {

	private static final Logger LOG = LoggerFactory.getLogger(OPNResponseAdapter.class);
	private static final Integer PARTNER_ID = 4;

	@Override
	public DeliveryResponse adapt(OPNResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryTaskId(data.getOrder_Code());
		response.setDeliveryBoyName(data.getPilot_Name());
		response.setDeliveryBoyPhoneNum(data.getPilot_Phone());
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setGeneratedOrderId(orderId);
		response.setDeliveryStatus(Integer.parseInt(data.getState()));
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		return response;
	}

	@Override
	public DeliveryResponse adaptError(OPNErrorResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setGeneratedOrderId(orderId);
		response.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		response.setFailureMessage(data != null ? data.getMessage() : null);
		return response;
	}

	@Override
	public DeliveryResponse adaptCallback(OPNCallbackObject data) {
		LOG.info("received object after conversion ::::: " + data.toString());

		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryBoyName(data.getPilotName());
		response.setDeliveryBoyPhoneNum(data.getPilotPhone());
		response.setDeliveryStatus(Integer.parseInt(data.getState()));
		response.setDeliveryTaskId(data.getOrderCode());
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryPartnerId(PARTNER_ID);
		return response;
	}

	@Override
	public DeliveryResponse adaptCancel(String orderId, String taskId) {
		return null;
	}

}
