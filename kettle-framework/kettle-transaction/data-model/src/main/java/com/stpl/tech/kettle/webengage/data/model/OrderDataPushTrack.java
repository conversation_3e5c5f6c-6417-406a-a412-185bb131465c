package com.stpl.tech.kettle.webengage.data.model;

import javax.persistence.*;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Entity
@Table(name = "ORDER_DATA_PUSH_TRACK")
public class OrderDataPushTrack {

    private Integer id;
    //private Date businessDate;
    private Integer lastOrderId;
    private String status;

    public OrderDataPushTrack(){}

    public OrderDataPushTrack(Integer id, Integer lastOrderId, String status) {
        this.id = id;
        this.lastOrderId = lastOrderId;
        this.status = status;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    /*@Column(name = "BUSINESS_DATE", nullable = false)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }*/

    @Column(name = "ORDER_ID", nullable = false)
    public Integer getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(Integer lastOrderId) {
        this.lastOrderId = lastOrderId;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
