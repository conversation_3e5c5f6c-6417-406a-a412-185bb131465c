package com.stpl.tech.kettle.clevertap.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GameLeaderBoardDTO implements Serializable {
    private static final long serialVersionUID = -6462079647212856496L;

    private String refCode;
    private Integer gameScore;
    private Integer refScore;
    private Integer totalScore;
    private String name;
}
