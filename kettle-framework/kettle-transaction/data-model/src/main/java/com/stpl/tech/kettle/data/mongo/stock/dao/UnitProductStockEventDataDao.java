package com.stpl.tech.kettle.data.mongo.stock.dao;

import com.stpl.tech.kettle.domain.model.UnitProductsStockEventData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UnitProductStockEventDataDao extends MongoRepository<UnitProductsStockEventData, String> {
    List<UnitProductsStockEventData> findAllByUnitIdAndEventTimeStampBetweenOrderByProductIdAsc
            (Integer unitId,Date startDate,Date endDate);

    List<UnitProductsStockEventData> findAllByUnitIdAndEventTimeStampBetweenOrderByProductIdAscDimensionAscEventTimeStampAsc
            (Integer unitId,Date startDate,Date endDate);

    List<UnitProductsStockEventData> findAllByUnitIdAndBrandIdAndEventTimeStampBetweenOrderByProductIdAscDimensionAscEventTimeStampAsc
            (Integer unitId,Integer brandId,Date startDate,Date endDate);
}
