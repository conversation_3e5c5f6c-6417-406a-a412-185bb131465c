/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.core.monitoring.TempCodeCache;
import com.stpl.tech.kettle.core.monitoring.TempCodeData;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.TempAccessDao;
import com.stpl.tech.kettle.data.model.TempAccessCode;
import com.stpl.tech.kettle.data.model.TempAccessCodeUsageData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.RandomStringGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;

@Repository
public class TempAccessDaoImpl extends AbstractDaoImpl implements TempAccessDao {

	@Autowired
	private TempCodeCache cache;

	@Autowired
	private EnvironmentProperties props;

	private RandomStringGenerator randomGenerator = new RandomStringGenerator();

	@Override
	public String generateAccessCode(int orderId) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		long miliSecond = props.getAccessCodeExpirationTime() * 60000;
		String code = randomGenerator.getRandomCode(5);
		TempAccessCode accessCode = new TempAccessCode();
		accessCode.setCodeStatus(AppConstants.ACTIVE);
		accessCode.setCreationTime(currentTime);
		accessCode.setExpirationTime(AppUtils.getTimeOfDay(currentTime, props.getAccessCodeExpirationTime()));
		accessCode.setOrderId(orderId);
		accessCode.setTempCode(code);
		manager.persist(accessCode);
		manager.flush();
		TempCodeData data = new TempCodeData(code, accessCode.getTempAccessCodeId(), miliSecond);
		cache.addToCache(data);
		return data.getCode();
	}

	@Override
	public TempAccessCodeUsageData grantAccess(String tempCode, String contactNumber) {
		Query query = manager.createQuery(
				"FROM TempAccessCode E where E.tempCode = :tempCode AND E.creationTime > :startOfDay order by E.creationTime desc");
		query.setParameter("tempCode", tempCode);
		query.setParameter("startOfDay", AppUtils.getStartOfDay(AppUtils.getCurrentDate()));
		@SuppressWarnings("unchecked")
		List<TempAccessCode> accessCodes = query.getResultList();
		if (accessCodes == null || accessCodes.size() == 0) {
			return createAccess(null, contactNumber, false, "INVALID_ACCCESS_CODE");
		} else {
			TempAccessCode accessCode = accessCodes.get(0);
			if (accessCode.getCodeStatus().equals(AppConstants.ACTIVE)) {
				return createAccess(accessCode, contactNumber, true, null);
			} else {
				return createAccess(accessCode, contactNumber, false, "CODE_EXPIRED");
			}
		}
	}

	private TempAccessCodeUsageData createAccess(TempAccessCode accessCode, String contactNumber, boolean accessGranted, String reason) {
		TempAccessCodeUsageData data = new TempAccessCodeUsageData();
		data.setAccessCode(accessCode);
		data.setAccessGranted(AppConstants.getValue(accessGranted));
		data.setReasonForDenial(reason);
		data.setContactNumber(contactNumber);
		manager.persist(data);
		manager.flush();
		return data;
	}

	@Override
	public void upadteStatus(int tempAccessCodeId, String status) {
		TempAccessCode data = manager.find(TempAccessCode.class, tempAccessCodeId);
		data.setCodeStatus(status);
		manager.flush();
	}

}
