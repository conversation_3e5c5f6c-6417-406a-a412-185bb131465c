/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.dao.OrderSearchDao;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.DeliveryStatusEvent;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedback;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.PartnerType;
import com.stpl.tech.util.AppUtils;

@Repository
public class DeliveryDaoImpl extends AbstractDaoImpl implements DeliveryDao {

	private static final Logger LOG = LoggerFactory.getLogger(DeliveryDaoImpl.class);

	@Autowired
	OrderSearchDao orderDao;

	@Autowired
	OrderManagementDao orderManagementDao;

	@Override
	public String getDeliveryPartnerMapping(Integer partnerId, String mappingType) {
		String mappingValue = null;
		Query query = manager.createQuery(
				"SELECT e.mappingValue FROM PartnerAttributes e where e.partnerId = :partnerId and e.mappingType = :mappingType");
		query.setParameter("partnerId", partnerId);
		query.setParameter("mappingType", mappingType);
		try {
			String o = (String) query.getSingleResult();
			mappingValue = o != null ? o : null;
		} catch (NoResultException nre) {
			LOG.error("Invalid request for deliveryRequest ", nre);
		}
		return mappingValue;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PartnerAttributes> getObjectMappings() {
		Query query = manager.createQuery("FROM PartnerAttributes WHERE partnerType = :partnerType");
		try {
			query.setParameter("partnerType", "DELIVERY");
			List<PartnerAttributes> returnList = query.getResultList();
			LOG.info("size of PartnerAttributes :::::: {}", returnList.size());
			return returnList;
		} catch (NoResultException nre) {
			LOG.error("Invalid request for deliveryRequest ", nre);
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<UnitToDeliveryPartnerMappings> getDeliveryPartnerPriorityForUnits() {
		Query query = manager.createQuery("FROM UnitToDeliveryPartnerMappings where priority > :priority");
		query.setParameter("priority", 0);
		try {
			List<UnitToDeliveryPartnerMappings> returnList = query.getResultList();
			LOG.info("size of UnitToDeliveryPartnerMappings :::::: {}", returnList.size());
			return returnList;
		} catch (NoResultException nre) {
			LOG.error("Invalid request for deliveryRequest ", nre);
		}
		return null;
	}

	@Override
	public synchronized DeliveryResponse saveDeliveryDetails(int unitId, DeliveryResponse response) {
		List<DeliveryDetail> details = getDeliveryDetail(response.getGeneratedOrderId());
		DeliveryDetail detail = null;
		if (details != null && details.size() > 0) {
			markAdditionalDetailsAsCancelled(details);
			detail = details.get(0);
			updateDetails(detail, response);
		} else {
			detail = DataConverter.convert(response);
			generateDeliveryStatusEvent(response, DeliveryStatus.valueOf(detail.getDeliveryStatus()),
					AppUtils.getCurrentTimestamp(), TransitionStatus.SUCCESS);
			LOG.info("deliveryStatus is {} and value of enum is {}", response.getDeliveryStatus(),
					DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
			if (response.getDeliveryStatus() != DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus()) {
				detail = manager.merge(detail);

			}
		}
		manager.flush();
		return DataConverter.convert(unitId, detail);
	}

	@Override
	public void markAdditionalDetailsAsCancelled(List<DeliveryDetail> details) {
		if (details != null && details.size() > 1) {
			for (int i = 1; i < details.size(); i++) {
				DeliveryDetail d = details.get(i);
				d.setDeliveryStatus(DeliveryStatus.CANCELLED.name());
			}
		}
	}

	private void generateDeliveryStatusEvent(DeliveryResponse response, DeliveryStatus previousStatus,
			Date previousUpdateTime, TransitionStatus transition) {
		DeliveryStatusEvent deliveryStatusEvent = new DeliveryStatusEvent();
		deliveryStatusEvent.setDeliveryPartnerId(response.getDeliveryPartnerId());
		deliveryStatusEvent.setFromStatus(DeliveryStatus.get(previousStatus.getDeliveryStatus()).toString());
		deliveryStatusEvent.setDeliveryTaskId(response.getDeliveryTaskId());
		deliveryStatusEvent.setToStatus(DeliveryStatus.get(response.getDeliveryStatus()).toString());
		deliveryStatusEvent.setUpdateTimeStamp(AppUtils.getCurrentTimestamp());
		deliveryStatusEvent.setStatusStartTime(previousUpdateTime);
		deliveryStatusEvent.setOrderId(response.getOrderId());
		deliveryStatusEvent.setTransitionStatus(transition.name());
		manager.persist(deliveryStatusEvent);
		manager.flush();
	}

	@Override
	public String getDeliveryDetailTaskId(Integer orderId) {
		String taskId = null;
		Query query = manager.createQuery("SELECT e.deliveryTaskId FROM DeliveryDetail e WHERE e.orderId = :orderId");
		try {
			query.setParameter("orderId", orderId);
			taskId = (String) query.getSingleResult();
			LOG.info("taskId of orderId :::::: {}", taskId);
		} catch (NoResultException nre) {
			LOG.error("Invalid request for deliveryRequest ", nre);
		}
		return taskId;
	}

	@Override
	public List<DeliveryDetail> getDeliveryDetail(Integer orderId) {
		Query query = manager.createQuery(
				"SELECT e FROM DeliveryDetail e WHERE e.orderId = :orderId AND e.deliveryStatus <> :deliveryStatus order by e.statusUpdateTime desc");
		try {
			query.setParameter("orderId", orderId);
			query.setParameter("deliveryStatus", DeliveryStatus.CANCELLED.toString());
			List<DeliveryDetail> details = query.getResultList();
			if (details != null && details.size() > 0) {
				LOG.info("no of delivery details :::::: {}", details.size());
				LOG.info("taskId of generatedOrderId :::::: {}", details.get(0).getDeliveryTaskId());
			}
			return details;
		} catch (NoResultException nre) {
			LOG.info("Did not find external delivery details for the orderId :::: {}", orderId);
		}
		return null;
	}

	@Override
	public List<DeliveryDetail> getDeliveryDetail(String generatedOrderId) {
		Query query = manager.createQuery(
				"SELECT e FROM DeliveryDetail e WHERE e.generatedOrderId = :generatedOrderId AND e.deliveryStatus <> :deliveryStatus order by e.statusUpdateTime desc");
		try {
			query.setParameter("generatedOrderId", generatedOrderId);
			query.setParameter("deliveryStatus", DeliveryStatus.CANCELLED.toString());
			List<DeliveryDetail> details = query.getResultList();
			if (details != null && details.size() > 0) {
				LOG.info("no of delivery details :::::: {}", details.size());
				LOG.info("taskId of generatedOrderId :::::: {}", details.get(0).getDeliveryTaskId());
			}
			return details;
		} catch (NoResultException nre) {
			LOG.info("Did not find external delivery details for the orderId :::: {}", generatedOrderId);
		}
		return null;
	}

	public DeliveryDetail getDeliveryDetail(String deliveryTaskId, int deliveryPartnerId) {
		LOG.info(
				":::::::::::::::::::::::::::::::::::::::::::Got delivery Task id {} and Delivery partner id {} :::::::::::::::::::::::::::",
				deliveryTaskId, deliveryPartnerId);
		Query query = manager.createQuery(
				"SELECT e FROM DeliveryDetail e WHERE e.deliveryPartnerId = :deliveryPartnerId AND e.deliveryTaskId = :deliveryTaskId");
		try {
			query.setParameter("deliveryTaskId", deliveryTaskId);
			query.setParameter("deliveryPartnerId", deliveryPartnerId);
			DeliveryDetail detail = (DeliveryDetail) query.getSingleResult();
			LOG.info("taskId of orderId :::::: {}", detail.getDeliveryTaskId());
			return detail;
		} catch (NoResultException nre) {
			LOG.error("Invalid request for deliveryRequest with productId ::::: {} ", deliveryTaskId);
		}
		return null;
	}

	@Override
	public void mergeCancellationDetails(DeliveryResponse response) {
		List<DeliveryDetail> details = getDeliveryDetail(response.getGeneratedOrderId());
		if (details != null && details.size() > 0) {
			markAdditionalDetailsAsCancelled(details);
			DeliveryDetail detail = details.get(0);
			generateDeliveryStatusEvent(response, DeliveryStatus.valueOf(detail.getDeliveryStatus()),
					detail.getStatusUpdateTime(), TransitionStatus.SUCCESS);
			detail.setDeliveryStatus(DeliveryStatus.get(response.getDeliveryStatus()).toString());
			detail.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
			manager.merge(detail);
			manager.flush();
		}
	}

	@Override
	public DeliveryDetail updateDelivery(DeliveryResponse updateObject) {
		DeliveryDetail details = null;
		if (updateObject != null) {
			details = getDeliveryDetail(updateObject.getDeliveryTaskId(), updateObject.getDeliveryPartnerId());

			if (details != null) {
				updateObject.setOrderId(details.getOrderId());

				TransitionStatus transition = validateTransition(
						DeliveryStatus.valueOf(details.getDeliveryStatus()).getDeliveryStatus(),
						updateObject.getDeliveryStatus());
				generateDeliveryStatusEvent(updateObject, DeliveryStatus.valueOf(details.getDeliveryStatus()),
						details.getStatusUpdateTime(), transition);

				if (!transition.equals(TransitionStatus.FAILURE)) {
					updateDetails(details, updateObject);
				} else {
					LOG.info("fromStatus is {} and toStatus is {}", updateObject.getDeliveryStatus(),
							DeliveryStatus.valueOf(details.getDeliveryStatus()).getDeliveryStatus());
				}

				details = manager.merge(details);
				manager.flush();
			}
		}
		return details;
	}

	private void updateDetails(DeliveryDetail details, DeliveryResponse updateObject) {

		details.setDeliveryBoyName(updateObject.getDeliveryBoyName());
		details.setDeliveryBoyPhoneNum(updateObject.getDeliveryBoyPhoneNum());
		details.setDeliveryBoyId(updateObject.getDeliveryBoyId());
		details.setStatusUpdateTime(updateObject.getStatusUpdateTime());
		details.setDeliveryStatus(DeliveryStatus.get(updateObject.getDeliveryStatus()).name());
		details.setAllotedNo(updateObject.getAllotedNo());
	}

	private TransitionStatus validateTransition(int fromDeliveryStatus, int toDeliveryStatus) {
		LOG.info("fromStatus {} and toStastus {}", fromDeliveryStatus, toDeliveryStatus);
		if (toDeliveryStatus <= fromDeliveryStatus) {
			LOG.info("inside if clause of transistion check");
			return TransitionStatus.FAILURE;
		} else {
			LOG.info("inside else clause of transistion check");
			return TransitionStatus.SUCCESS;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PartnerAttributes> getPartnerAttributeList(int partnerId, String partnerType) {
		Query query = manager.createQuery(
				"FROM PartnerAttributes p WHERE p.partnerId = :partnerId and p.partnerType = :partnerType");
		try {
			query.setParameter("partnerId", partnerId);
			query.setParameter("partnerType", partnerType);
			List<PartnerAttributes> returnList = query.getResultList();
			return returnList;
		} catch (NoResultException nre) {
			LOG.error("Invalid request for Attribute Mapping List ", nre);
		}
		return null;
	}

	@Override
	public Map<Integer, String> getAutomatedPartnerMap() {
		Map<Integer, String> partnerMap = new HashMap<Integer, String>();
		Query query = manager.createQuery(
				"SELECT p.partnerId, p.automated FROM DeliveryPartner p WHERE p.partnerType = :partnerType");
		try {
			query.setParameter("partnerType", PartnerType.EXTERNAL.toString());
			@SuppressWarnings("unchecked")
			List<Object[]> returnList = query.getResultList();
			if (returnList != null && !returnList.isEmpty()) {
				for (Object[] object : returnList) {
					if (object[0] != null && object[1] != null) {
						partnerMap.put(Integer.parseInt(object[0].toString()), object[1].toString());
					}
				}
			}
		} catch (NoResultException nre) {
			LOG.error("Invalid request for Attribute Mapping List ", nre);
		}
		return partnerMap;
	}

	@Override
	public boolean updateOrder(Order order) throws DataUpdationException {
		return orderManagementDao.updateOrder(order);
	}

	@Override
	public List<OrderFeedback> getOrderListForFeedback(String deliveryPersonContact, String feedbackStatus) {
		int lastOrderId = orderDao.getLastOrderOfLastBusinessDate();
		Query query = manager.createQuery(
				"FROM DeliveryDetail d where d.deliveryBoyPhoneNum = :deliveryPersonContact and d.orderId > :lastOrderId and d.deliveryStatus <> :cancelledStatus and d.feedbackStatus = :feedbackStatus");
		query.setParameter("deliveryPersonContact", "+91" + deliveryPersonContact);
		query.setParameter("lastOrderId", lastOrderId);
		query.setParameter("feedbackStatus", feedbackStatus);
		query.setParameter("cancelledStatus", "CANCELLED");
		List<DeliveryDetail> deliveryDetails = query.getResultList();
		List<OrderFeedback> orderFeedbacks = new ArrayList<OrderFeedback>();
		if (deliveryDetails != null && deliveryDetails.size() > 0) {
			for (DeliveryDetail dd : deliveryDetails) {
				OrderFeedback orderFeedback = new OrderFeedback();
				query = manager.createQuery("FROM OrderDetail o where o.generatedOrderId = :generatedOrderId");
				query.setParameter("generatedOrderId", dd.getGeneratedOrderId());
				OrderDetail orderDetail = (OrderDetail) query.getSingleResult();
				query = manager.createQuery("from CustomerInfo ci where ci.customerId = :customerId");
				query.setParameter("customerId", orderDetail.getCustomerId());
				CustomerInfo customerInfo = (CustomerInfo) query.getSingleResult();
				orderFeedback.setDeliveryId(String.valueOf(dd.getId()));
				orderFeedback.setGeneratedOrderId(dd.getGeneratedOrderId());
				orderFeedback.setCustomerContact(customerInfo.getContactNumber());
				String name = customerInfo.getFirstName();
				if (customerInfo.getMiddleName() != null) {
					name += " " + customerInfo.getMiddleName();
				}
				if (customerInfo.getLastName() != null) {
					name += " " + customerInfo.getLastName();
				}
				orderFeedback.setCustomerName(name);
				orderFeedback.setBillAmount(orderDetail.getTotalAmount().floatValue());
				orderFeedbacks.add(orderFeedback);
			}
		}
		return orderFeedbacks;
	}

	@Override
	public String submitOrderDeliveryFeedback(int deliveryId, String code) {
		DeliveryDetail deliveryDetail = manager.find(DeliveryDetail.class, deliveryId);
		if (deliveryDetail.getPositiveFeedbackCode() != null && deliveryDetail.getNegativeFeedbackCode() != null) {
			if (deliveryDetail.getPositiveFeedbackCode().equals(code)
					|| deliveryDetail.getNegativeFeedbackCode().equals(code)) {
				deliveryDetail.setFeedbackCodeReceived(code);
				deliveryDetail.setFeedbackStatus("Y");
				manager.merge(deliveryDetail);
				manager.flush();
				return "Status submitted successfully.";
			} else {
				return "Code submitted is not valid.";
			}
		}
		return "Something is wrong. Please try again Later.";
	}

	@Override
	public List<PartnerAttributes> defaultDeliveryPartner(String mappingType) {
		Query query = manager.createQuery("FROM PartnerAttributes e where e.mappingType = :mappingType");
		query.setParameter("mappingType", mappingType);
		return query.getResultList();

	}

	@Override
	public List<Integer> getHourlyReportPartners() {
		Query query = manager.createQuery(
				"SELECT e.partnerId FROM PartnerAttributes e where e.mappingType = :mappingType AND e.mappingValue = :mappingValue");
		query.setParameter("mappingType", "HOURLY");
		query.setParameter("mappingValue", "REQUIRED");
		return query.getResultList();
	}

}
