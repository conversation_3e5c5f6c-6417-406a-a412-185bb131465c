package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.master.core.OfferCategoryType;

/**
 * Created by starman on 31/7/19.
 */
public class OfferStrategyHelper {

    public static Class<? extends OfferActionStrategy> getStrategy(OfferCategoryType type) {
        switch (type) {
            case PERCENTAGE_BILL_STRATEGY:

                return PercentageBillStrategy.class;

            case FLAT_BILL_STRATEGY:

                return FlatBillStrategy.class;

            case PERCENTAGE_ITEM_STRATEGY:

                return PercentageItemStrategy.class;

            case FLAT_ITEM_STRATEGY:

                return FlatItemStrategy.class;

            case OFFER_WITH_FREE_ITEM_STRATEGY:

                return OfferWithFreeItemStrategy.class;

            case COMBO_STRATEGY:

                return ComboOfferStrategy.class;

            case FIXED_VALUE_BILL_STRATEGY:

                return FixedValueBillStrategy.class;

            case PERCENTAGE_BILL_MAX_CAP_STRATEGY:

                return PercentageBillMaxCapStrategy.class;

            case PERCENTAGE_ITEM_BOGO_STRATEGY:

                return PercentageItemBogoStrategy.class;

            case SLICE_ITEM_PRICE_STRATEGY:

                return SliceItemPriceStrategy.class;

            case PERCENTAGE_ITEM_MAX_CAP_STRATEGY:

                return PercentageItemMaxCapStrategy.class;
            case PERCENTAGE_ITEM_BOGO_SLICE_STRATEGY:

                return PercentageItemBogoSliceStrategy.class;
            case FREEBIE_STRATEGY_PERCENTAGE:

                return FreeBieStrategy.class;
            case FREEBIE_STRATEGY_FLAT:

                return FreeBieStrategyFlat.class;
            case PERCENTAGE_ITEM_MULTIPLE_STRATEGY:

                return PercentageItemMultipleStrategy.class;
            case PERCENTAGE_ITEM_MULTIX_MULTIY_STRATEGY:

                return PercentageItemMultiXMultiYStrategy.class;
            case PRICE_DESCENDING_PERCENTAGE_ITEM_STRATEGY:
                return PriceDescendingPercentageItemStrategy.class;
            default:
                break;
        }
        return null;
    }
}
