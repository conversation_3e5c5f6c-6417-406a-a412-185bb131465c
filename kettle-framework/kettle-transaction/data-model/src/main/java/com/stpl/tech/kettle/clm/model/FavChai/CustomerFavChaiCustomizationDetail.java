package com.stpl.tech.kettle.clm.model.FavChai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "CUSTOMER_CHAI_CUSTOMIZATION_DETAIL")
public class CustomerFavChaiCustomizationDetail {
    @EmbeddedId
    private Key key;
    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;
    @Column(name = "RECIPE_ID")
    private Integer recipeId;
    @Column(name = "CLM_EVENT_ID")
    private Integer clmEventId;
    @Column(name = "RECIPE_PROFILE", length = 50)
    private String recipeProfile;
    @Column(columnDefinition = "text",name = "CUST1")
    private String customizationIds;
    @Column(columnDefinition = "text",name = "CUST2")
    private String customizations;

    @Column(name = "CHANNEL_PARTNER_ID")
    private Integer channelPartnerId;

    @Column(name = "SOURCE_VERSION")
    private String sourceVersion;

    @Column(name = "ADDED_TO_PROD", nullable = false)
    private String addedToProd;

    @Column(name = "QUANTITY")
    private Integer quantity;

    @Column(name = "DIMENSION")
    private String dimension;
    
}
