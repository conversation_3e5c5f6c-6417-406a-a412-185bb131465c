/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.kettle.core.LoyaltyEventStatus;
import com.stpl.tech.kettle.core.LoyaltyEventTransactionType;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyLogHistory;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.domain.model.LoyaltyFailedReason;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.transaction.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class LoyaltyDaoImpl extends AbstractDaoImpl implements LoyaltyDao {
    private static final String LOYALTY_EVENT_SUCCESS_STATUS = "SUCCESS";
    private static final String LOYALTY_EVENT_FAILED_STATUS = "FAILED";
    private static final Logger LOG = LoggerFactory.getLogger(LoyaltyDaoImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvironmentProperties properties;

    public boolean updateScore(int customerId, LoyaltyEventType type, int points, int cumulativePoints, Integer orderId,
                               boolean hasSignupOffer, boolean updateLastOrderId) {
        return createEventAndUpdateScore(customerId, type, points, cumulativePoints, true, orderId, hasSignupOffer, updateLastOrderId);
    }

    public boolean updateScore(int customerId, LoyaltyEventType type, int points, Integer orderId,
                               boolean hasSignupOffer, boolean updateLastOrderId) {
        return createEventAndUpdateScore(customerId, type, points, points, true, orderId, hasSignupOffer, updateLastOrderId);
    }

    private boolean createEventAndUpdateScore(int customerId, LoyaltyEventType type, int points, int cumulativePoints,
                                              boolean updateScore, Integer orderId, boolean hasSignupOffer, boolean updateLastOrderId) {

        LoyaltyScore score = getScore(customerId);
        if (score != null) {
            if (LoyaltyEventType.OUTLET_VISIT.equals(type)) {
                if (score.getCumulativePoints().compareTo(Integer.valueOf(0)) > 0 &&
                        TransactionUtils.checkWithinTripThresholdTime(score.getLastOrderTime(),masterDataCache)) {
                    createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_FAILED_STATUS, orderId,score.getAcquiredPoints(),
                            score.getAcquiredPoints(), LoyaltyFailedReason.WITHIN_TRIP_THRESHOLD_TIME.name());
                    return false;
                }
            }
            int finalAcquiredPoints = score.getAcquiredPoints() + points;
            if (finalAcquiredPoints >= 0) {
                createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_SUCCESS_STATUS, orderId,finalAcquiredPoints,
                        score.getAcquiredPoints(),null);
                if (updateScore) {
                    score.setAcquiredPoints(finalAcquiredPoints);
                    if(points < 0){
                        Integer redeemedPoints = Math.abs(points);
                        score.setTotalRedeemedPoints(score.getTotalRedeemedPoints()!=null?
                                score.getTotalRedeemedPoints() +redeemedPoints: 0+redeemedPoints);
                    }
                    if (LoyaltyEventType.OUTLET_VISIT.equals(type) && updateLastOrderId) {
                        score.setLastOrderId(orderId);
                        score.setLastOrderTime(AppUtils.getCurrentTimestamp());
                        score.setOrderCount(score.getOrderCount() == null ? 1 : score.getOrderCount() + 1);
                        //LOG.info("New order count , " + score.getOrderCount());
                        if (hasSignupOffer || score.getOrderCount() >= 2) {
                            //LOG.info("Setting signupOffer availed to true where order count is " + score.getOrderCount() + " and hasSignupOffer is " + hasSignupOffer );
                            score.setAvailedSignupOffer(AppConstants.getValue(true));
                            score.setSignupOfferStatus(SignupOfferStatus.REDEEMED.name());
                            score.setRedemptionOrderId(orderId);
                        }
                    }else if (LoyaltyEventType.OUTLET_VISIT.equals(type) && !updateLastOrderId) {
                        score.setLastOrderTime(AppUtils.getCurrentTimestamp());
                    }
                    LOG.info(String.format("Updated loyalty points of the customer %d to %d points", customerId,
                        score.getAcquiredPoints()));
                    LOG.info(String.format("Updated cumulative points of the customer %d from %d to %d points",
                        customerId, score.getCumulativePoints(),
                        score.getCumulativePoints() + cumulativePoints));
                    if(!LoyaltyEventType.REGULAR_REDEMPTION.equals(type)){
                        score.setCumulativePoints(score.getCumulativePoints() + cumulativePoints);
                    }
                    }
                return true;
            }
        }
        createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_FAILED_STATUS, orderId, Objects.nonNull(score) &&
                Objects.nonNull(score.getAcquiredPoints()) ? score.getAcquiredPoints() : 0 , 0,LoyaltyFailedReason.INVALID_POINTS.name());
        return false;

    }

    @Override
    public boolean createLoyaltyEvent(int customerId, int points, LoyaltyEventType type, String status, Integer orderId, int finalAcquiredPoints,
                                       Integer acquiredPoints,String reason) {
        try {
            Integer daysAfterLoyaltyExpire = properties.getDaysAfterLoyaltyExpire();
            LoyaltyEvents event = new LoyaltyEvents(customerId, points > 0 ? LoyaltyEventTransactionType.DEBIT.name()
                    : LoyaltyEventTransactionType.CREDIT.name());
            String loyaltyEventStatus = points<0 ? LoyaltyEventStatus.CLAIMED.name():
                    (LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status) ? LoyaltyEventStatus.ACTIVE.name() :
                            LoyaltyEventStatus.IN_ACTIVE.name());
            event.setTransactionCode(type.name());
            event.setTransactionCodeType(type.getType());
            event.setTransactionStatus(status);
            event.setTransactionPoints(points);
            event.setTransactionTime(AppUtils.getCurrentTimestamp());
            event.setOrderId(orderId);
            event.setOpeningBalance(acquiredPoints);
            event.setClosingBalance(finalAcquiredPoints);
            event.setReason(reason);
            event.setLoyaltyEventStatus(loyaltyEventStatus);
            if(points>0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status)){
                Date expirationDate = AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(),daysAfterLoyaltyExpire);
                event.setExpirationTime(AppUtils.getLastDayOfMonth(expirationDate));
                event.setRedeemedPoints(0);
                event.setExpiredPoints(0);
            }
            manager.persist(event);
            LOG.info("Loyalty Event Created - " + event.toString());
            Boolean isCustomerAddedIsNew = false;
            try {
                isCustomerAddedIsNew = isCustomerAddedIsNew(customerId);
                LOG.info("isCustomerAddedIsNew : {}",isCustomerAddedIsNew);
            }catch (Exception e){
                LOG.info("Error in checking customer add time for customer id : {} and error is : {}",
                        customerId,e);
            }
            if(points<0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status) && isCustomerAddedIsNew){
                updateLoyaltyEvents(customerId,points,orderId,event.getLoyaltyEventsId(),LoyaltyEventStatus.REDEEMED.name());
            }
            manager.flush();
            return true;
        } catch (Exception e){
            LOG.error("Encountered error while persisting loyalty event", e);
            return false;
        }
    }

    public boolean createEventAndUpdate(int customerId, LoyaltyEventType type, int points, Integer orderId,
                                        boolean hasSignupOffer, boolean updateLastOrderId) {
        return createEventAndUpdateScore(customerId, type, points, points, false, orderId, hasSignupOffer, updateLastOrderId);
    }

    public boolean createLoyaltyEvent(int customerId, LoyaltyEventType type, int points, Integer orderId) {
        try {
            createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_SUCCESS_STATUS, orderId,null);
        } catch (Exception e) {
            LOG.error("Encountered error while persisting loyalty event", e);
            return false;
        }
        return true;
    }

    public LoyaltyScore getScore(int customerId) {
        try {
            Query query = manager.createQuery("From LoyaltyScore l where l.customerId = :customerId");
            query.setParameter("customerId", customerId);
            LoyaltyScore score = (LoyaltyScore) query.getSingleResult();
            return score;
        } catch (NoResultException e) {
            LOG.info(String.format("In getScore() Did not find Customer Loyalty Score with Contact ID : %d", customerId), e);
            return null;
        }
    }

    private void createLoyaltyEvent(int customerId, int points, LoyaltyEventType type, String status, Integer orderId
    ,String reason) {
        Integer daysAfterLoyaltyExpire = properties.getDaysAfterLoyaltyExpire();
        LoyaltyEvents event = new LoyaltyEvents(customerId, points > 0 ? LoyaltyEventTransactionType.DEBIT.name()
                : LoyaltyEventTransactionType.CREDIT.name());
        String loyaltyEventStatus = points<0 ? LoyaltyEventStatus.CLAIMED.name():
                (LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status) ? LoyaltyEventStatus.ACTIVE.name() :
                        LoyaltyEventStatus.IN_ACTIVE.name());
        event.setTransactionCode(type.name());
        event.setTransactionCodeType(type.getType());
        event.setTransactionStatus(status);
        event.setTransactionPoints(points);
        event.setTransactionTime(AppUtils.getCurrentTimestamp());
        event.setOrderId(orderId);
        event.setReason(reason);
        event.setLoyaltyEventStatus(loyaltyEventStatus);
        if(points>0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status)){
            Date expirationDate = AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(),daysAfterLoyaltyExpire);
            event.setExpirationTime(AppUtils.getLastDayOfMonth(expirationDate));
            event.setRedeemedPoints(0);
            event.setExpiredPoints(0);
        }
        manager.persist(event);
        LOG.info("Loyalty Event Created - " + event.toString());
        Boolean isCustomerAddedIsNew = false;
        try {
            isCustomerAddedIsNew = isCustomerAddedIsNew(customerId);
            LOG.info("isCustomerAddedIsNew : {}",isCustomerAddedIsNew);
        }catch (Exception e){
            LOG.info("Error in checking customer add time for customer id : {} and error is : {}",
                    customerId,e);
        }
        if(points<0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status) && isCustomerAddedIsNew){
            updateLoyaltyEvents(customerId,points,orderId,event.getLoyaltyEventsId(),LoyaltyEventStatus.REDEEMED.name());
        }
        manager.flush();
        LOG.info("Loyalty Event Created - " + event.toString());
    }

	@Override
	public boolean isLoyaltyAwarded(int customerId, Integer orderId) {
        Query query = manager.createQuery("From LoyaltyEvents l where l.customerId = :customerId and l.orderId = :orderId and l.transactionCodeType = :addition and "
        		+ "l.transactionCode = :outletVisit and l.transactionStatus = :success");
        query.setParameter("customerId", customerId);
        query.setParameter("orderId", orderId);
        query.setParameter("addition", LoyaltyEventType.OUTLET_VISIT.getType());
        query.setParameter("outletVisit", LoyaltyEventType.OUTLET_VISIT.name());
        query.setParameter("success", LOYALTY_EVENT_SUCCESS_STATUS);
        try {
            Object score = query.getSingleResult();
            return score != null;
        }catch (Exception e) {
        	return false;
        }
    }

	@Override
	public void updateCustomerId(int customerId, Integer orderId) {
        Query query = manager.createQuery("update OrderDetail l set l.customerId = :customerId where l.orderId = :orderId ");
        query.setParameter("customerId", customerId);
        query.setParameter("orderId", orderId);
        query.executeUpdate();
	}

    @Override
    public LoyaltyEvents getTransactionPointsByOrderId(Integer orderId) {
        Query query=manager.createQuery("from LoyaltyEvents le where le.orderId= :orderId and le.transactionType= :transactionType " +
                "and le.transactionCodeType= :transactionCodeType and le.transactionStatus= :transactionStatus");
        query.setParameter("orderId", orderId);
        query.setParameter("transactionType", TransactionType.DEBIT.name());
        query.setParameter("transactionCodeType", "Addition");
        query.setParameter("transactionStatus", LOYALTY_EVENT_SUCCESS_STATUS);
        LoyaltyEvents loyaltyEvents;
        try {
            loyaltyEvents=(LoyaltyEvents) query.getSingleResult();
            return  loyaltyEvents;
        }catch (NoResultException e){
            //LOG.info("No result found for order id: {}",orderId);
        }
        loyaltyEvents=new LoyaltyEvents();
        loyaltyEvents.setTransactionPoints(0);
        return loyaltyEvents;
    }

	@Override
	public boolean isLoyaltyAwarded(int customerId, LoyaltyEventType emailVerification) {
		try {
			Query query = manager.createQuery(
					"from LoyaltyEvents le where le.customerId = :customerId and le.transactionCode= :emailVerification and le.transactionStatus= :transactionStatus");
			query.setParameter("customerId", customerId);
			query.setParameter("emailVerification", emailVerification.name());
			query.setParameter("transactionStatus", LOYALTY_EVENT_SUCCESS_STATUS);
			List<LoyaltyEvents> events = query.getResultList();
			if (events == null || events.size() == 0) {
				return false;
			} else {
				return true;
			}
		} catch (Exception e) {
			LOG.error("Error in fetching loyalty events for {} and {}", customerId, emailVerification.name());
			return false;
		}
	}

    @Override
    public List<LoyaltyEvents> getLoyaltyEventsForCustomerId(String customerId, Integer limit){
        try {
            Query query = manager.createQuery("FROM LoyaltyEvents le where le.customerId = :customerId order by 1 desc");
            query.setParameter("customerId", Integer.valueOf(customerId));
            query.setMaxResults(limit);
            return query.getResultList();
        }catch (Exception e){
            LOG.error("Error while getting loyalty events for customer with customer id ::: {}",customerId,e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateLoyaltyEvents(int customerId,int points,Integer orderId,Integer transactionEventId,String loyaltyEventStatus){
        List<LoyaltyEvents> loyaltyEventsList = null;
        if(LoyaltyEventStatus.GIFTED.name().equalsIgnoreCase(loyaltyEventStatus)){
            loyaltyEventsList = findAllLoyaltyEventForCustomerExpectGiftingEvent(customerId,LoyaltyEventStatus.ACTIVE.name(),
                    LOYALTY_EVENT_SUCCESS_STATUS);
        }else {
            loyaltyEventsList = findAllByCustomerIdAndLoyaltyEventStatusAndTransactionStatus(customerId, LoyaltyEventStatus.ACTIVE.name(),
                    LOYALTY_EVENT_SUCCESS_STATUS);
        }
        int pointsToBeRedeemed = Math.abs(points);
        if(!CollectionUtils.isEmpty(loyaltyEventsList)){
            for(LoyaltyEvents event : loyaltyEventsList){
                Integer availablePoints = Math.abs(Math.abs(event.getTransactionPoints())-Math.abs(event.getRedeemedPoints()));
                Integer redeemedPoints;
                boolean isMaintainAuditEventLog = false;
                Integer redeemedPointsInEvent;
                if(availablePoints > pointsToBeRedeemed){
                    redeemedPoints = event.getRedeemedPoints() + pointsToBeRedeemed;
                    event.setRedeemedPoints(redeemedPoints);
                    availablePoints = Math.abs(Math.abs(event.getTransactionPoints())-Math.abs(redeemedPoints));
                    if(availablePoints==0){
                        event.setLoyaltyEventStatus(loyaltyEventStatus);
                    }
                    redeemedPointsInEvent = pointsToBeRedeemed;
                    pointsToBeRedeemed=0;
                    isMaintainAuditEventLog = true;
                }else{
                    if(Objects.nonNull(event.getRedeemedPoints()) && event.getRedeemedPoints()!=0){
                        isMaintainAuditEventLog=true;
                    }
                    redeemedPoints = event.getRedeemedPoints() + availablePoints;
                    if(redeemedPoints.equals(event.getTransactionPoints())) {
                        event.setLoyaltyEventStatus(loyaltyEventStatus);
                    }
                    event.setRedeemedPoints(redeemedPoints);
                    redeemedPointsInEvent = availablePoints;
                    pointsToBeRedeemed = Math.abs(pointsToBeRedeemed-availablePoints);
                }
                event.setRedemptionTime(AppUtils.getCurrentTimestamp());
                manager.persist(event);
                if(isMaintainAuditEventLog || LoyaltyEventStatus.GIFTED.name().equalsIgnoreCase(loyaltyEventStatus)) {
                    saveLoyaltyEventLog(event.getLoyaltyEventsId(), -1 * redeemedPointsInEvent,
                            loyaltyEventStatus, TransactionType.CREDIT.name(), event.getTransactionStatus(), orderId, transactionEventId);
                }
                if(pointsToBeRedeemed == 0){
                    break;
                }
            }
        }
    }

    @Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void expireLoyaltyPoints(){
        LOG.info("CRON RUNNING TO EXPIRE LOYALTY AT : {}",AppUtils.getCurrentTimestamp());
        Date currentdate = AppUtils.getCurrentDate();
        List<LoyaltyEvents> loyaltyEventsList = findAllByExpirationTimeLessThanEqualAndLoyaltyEventStatus(
                currentdate,LoyaltyEventStatus.ACTIVE.name());
        if(!CollectionUtils.isEmpty(loyaltyEventsList)){
            for(LoyaltyEvents event : loyaltyEventsList){
                Integer expiredPoints = Math.abs(Math.abs(event.getTransactionPoints())-Math.abs(event.getRedeemedPoints()));
                event.setExpiredPoints(expiredPoints);
                event.setLoyaltyEventStatus(LoyaltyEventStatus.EXPIRED.name());
                LoyaltyScore score = getScore(event.getCustomerId());
                Integer acquiredPoints = score.getAcquiredPoints();
                Integer finalAcquiredPoints = Math.max(0,Math.abs(acquiredPoints - expiredPoints));
                LoyaltyEvents expirationEvent = getExpiredLoyaltyEvent(event.getCustomerId(),expiredPoints,acquiredPoints,finalAcquiredPoints);
                score.setAcquiredPoints(finalAcquiredPoints);
                score.setTotalExpiredPoints(score.getTotalExpiredPoints()!=null?
                        score.getTotalExpiredPoints() +expiredPoints: 0+expiredPoints);
                manager.persist(score);
                manager.persist(event);
                manager.persist(expirationEvent);
                saveLoyaltyEventLog(event.getLoyaltyEventsId(),-1*expiredPoints,
                        LoyaltyEventStatus.EXPIRED.name(),TransactionType.CREDIT.name(), event.getTransactionStatus(),-1,
                        expirationEvent.getLoyaltyEventsId());
            }
        }
    }

    @Override
    public LoyaltyEvents getExpiredLoyaltyEvent(Integer customerId,Integer expiredPoints,Integer acquiredPoints,Integer finalAcquiredPoints){
        LoyaltyEvents expirationEvent = new LoyaltyEvents(customerId, LoyaltyEventTransactionType.CREDIT.name());
        expirationEvent.setTransactionCode(LoyaltyEventType.EXPIRATION.name());
        expirationEvent.setTransactionCodeType(LoyaltyEventType.EXPIRATION.getType());
        expirationEvent.setTransactionStatus(LOYALTY_EVENT_SUCCESS_STATUS);
        expirationEvent.setTransactionPoints((-1*expiredPoints));
        expirationEvent.setTransactionTime(AppUtils.getCurrentTimestamp());
        expirationEvent.setOrderId(-1);
        expirationEvent.setOpeningBalance(acquiredPoints);
        expirationEvent.setClosingBalance(finalAcquiredPoints);
        expirationEvent.setLoyaltyEventStatus(LoyaltyEventStatus.EXPIRED.name());
        return expirationEvent;
    }


    @Override
    public void saveLoyaltyEventLog(Integer loyaltyEventId,Integer points,String transactionCodeType,
                                    String transactionType, String status,Integer orderId,Integer transactionEventId){
        LoyaltyLogHistory logHistory = new LoyaltyLogHistory();
        logHistory.setLoyaltyEventId(loyaltyEventId);
        logHistory.setTransactionStatus(status);
        logHistory.setTransactionPoints(points);
        logHistory.setTransactionType(transactionType);
        logHistory.setOrderId(orderId);
        logHistory.setTransactionCodeType(transactionCodeType);
        logHistory.setTransactionTime(AppUtils.getCurrentTimestamp());
        logHistory.setTransactionEventId(transactionEventId);
        manager.persist(logHistory);
    }

    @Override
    public List<LoyaltyEvents> findAllByCustomerIdAndLoyaltyEventStatusAndTransactionStatus(Integer customerId,
                                                                                            String loyaltyEventStatus,
                                                                                            String transactionStatus){
        try {
            Query query = manager.createQuery("FROM LoyaltyEvents le where le.customerId = :customerId "+
                    "and le.loyaltyEventStatus = :loyaltyEventStatus and le.transactionStatus = :transactionStatus");
            query.setParameter("customerId", Integer.valueOf(customerId));
            query.setParameter("loyaltyEventStatus",loyaltyEventStatus);
            query.setParameter("transactionStatus",transactionStatus);
            return query.getResultList();
        }catch (Exception e){
            LOG.error("Error while getting loyalty events for customer with customer id ::: {}",customerId,e);
            return null;
        }
    }

    public List<LoyaltyEvents> findAllByExpirationTimeLessThanEqualAndLoyaltyEventStatus(Date currentDate,
                                                                                            String loyaltyEventStatus){
        try {
            Query query = manager.createQuery("FROM LoyaltyEvents le where le.expirationTime <= :currentDate "+
                    "and le.loyaltyEventStatus = :loyaltyEventStatus");
            query.setParameter("currentDate", currentDate);
            query.setParameter("loyaltyEventStatus",loyaltyEventStatus);
            return query.getResultList();
        }catch (Exception e){
            LOG.error("Error while getting loyalty events for current date ::: {}",currentDate,e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean isCustomerAddedIsNew(Integer customerId){
        try {
            String date = properties.getDateToCheckNewCustomer();
            Query query = manager.createQuery("SELECT CASE WHEN ci.addTime >= :addTime THEN true ELSE false END " +
                    "FROM CustomerInfo ci WHERE ci.customerId = :customerId");
            query.setParameter("customerId", customerId);
            query.setParameter("addTime",AppUtils.getDate(AppUtils.parseDate(date)));
            return (Boolean) query.getSingleResult();
        }catch (Exception e){
            LOG.info("Error in checking add time of customer with customerId : {}",customerId);
        }
        return false;
    }

    @Override
    public List<LoyaltyEvents> findAllLoyaltyEventForCustomerExpectGiftingEvent(Integer customerId,
                                                                                            String loyaltyEventStatus,
                                                                                            String transactionStatus){
        try {
            Query query = manager.createQuery("FROM LoyaltyEvents le where le.customerId = :customerId "+
                    "and le.loyaltyEventStatus = :loyaltyEventStatus and le.transactionStatus = :transactionStatus "+
                    "and le.transactionCode <> :transactionCode");
            query.setParameter("customerId", Integer.valueOf(customerId));
            query.setParameter("loyaltyEventStatus",loyaltyEventStatus);
            query.setParameter("transactionStatus",transactionStatus);
            query.setParameter("transactionCode",LoyaltyEventType.LOYALTY_GIFTING.name());
            return query.getResultList();
        }catch (Exception e){
            LOG.error("Error while getting loyalty events for customer with customer id ::: {}",customerId,e);
            return null;
        }
    }

}
