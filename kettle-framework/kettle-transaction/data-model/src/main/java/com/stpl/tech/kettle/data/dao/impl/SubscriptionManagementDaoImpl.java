/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.core.NamedQueryDefinition;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.SubscriptionEventItemType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.notification.SubscriptionInfo;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.dao.SubscriptionManagementDao;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.SubscriptionDetail;
import com.stpl.tech.kettle.data.model.SubscriptionEventDetail;
import com.stpl.tech.kettle.data.model.SubscriptionEventItem;
import com.stpl.tech.kettle.data.model.SubscriptionItem;
import com.stpl.tech.kettle.data.model.SubscriptionItemAddon;
import com.stpl.tech.kettle.data.model.SubscriptionItemTaxDetail;
import com.stpl.tech.kettle.data.model.SubscriptionSettlement;
import com.stpl.tech.kettle.data.model.SubscriptionStatusEvent;
import com.stpl.tech.kettle.data.model.SubscriptionTaxDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionEvent;
import com.stpl.tech.kettle.domain.model.SubscriptionEventSource;
import com.stpl.tech.kettle.domain.model.SubscriptionEventStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionStatusEvents;
import com.stpl.tech.kettle.domain.model.SubscriptionType;
import com.stpl.tech.kettle.domain.model.SubscriptionViewData;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.tax.model.TaxationDetailDao;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class SubscriptionManagementDaoImpl extends AbstractDaoImpl implements SubscriptionManagementDao {

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private MetadataCache metadataCache;

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private OrderManagementDao orderDao;

	@Autowired
	private EnvironmentProperties props;


	private static final Logger LOG = LoggerFactory.getLogger(SubscriptionManagementDaoImpl.class);

	public Order getOrder(int subscriptionId, boolean anyStatus) throws DataNotFoundException {
		SubscriptionDetail detail = manager.find(SubscriptionDetail.class, subscriptionId);
		if (anyStatus || SubscriptionStatus.CREATED.name().equals(detail.getSubscriptionStatus())) {
			return DataConverter.convert(masterCache, detail, null);
		}
		return null;
	}

	@Override
	public List<SubscriptionEventDetail> getOrdersToBeCreated(Date currentTimestamp) {
		LOG.info(String.format("Finding subscriptions events at " + currentTimestamp));
		Query query = manager.createQuery(
				"FROM SubscriptionEventDetail E where E.eventStatus = :eventStatus and E.eventTime <= :eventTime order by subscriptionDetail.subscriptionId");
		query.setParameter("eventTime", currentTimestamp);
		query.setParameter("eventStatus", SubscriptionEventStatus.CREATED.name());
		@SuppressWarnings("unchecked")
		List<SubscriptionEventDetail> list = query.getResultList();
		if (list == null) {
			LOG.info(String.format("Did not find any subscriptions events at time %s", currentTimestamp));
		} else {
			LOG.info(String.format("Found %d subscriptions ", list.size()));
		}
		return list;
	}

	@Override
	public String createSubscription(Order subscription) throws DataUpdationException, DataNotFoundException {
		Date currentTimestamp = AppUtils.getCurrentTimestamp();
		SubscriptionDetail detail = addSubscription(subscription, currentTimestamp);
		if(!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(subscription.getCustomerId())
				&& !TransactionUtils.isCODOrder(subscription.getSource())){
			orderDao.generateOrderEmailEvent(OrderEmailEntryType.SUBSCRIPTION, detail.getSubscriptionId(), 1,
					customerDao.getCustomer(detail.getCustomerInfo().getCustomerId()), true, currentTimestamp);
		}
		Date currentDate = AppUtils.getCurrentDate();
		LOG.info("Current Date " + currentDate);
		LOG.info("Start Date " + AppUtils.getDate(detail.getStartDate()));
		LOG.info("Whether Equal : " + currentDate.equals(AppUtils.getDate(detail.getStartDate())));
		if (currentDate.equals(AppUtils.getDate(detail.getStartDate()))) {
			LOG.info(String.format("Creating events for today for the new subscriptions %d as it starts from today %s",
					detail.getSubscriptionId(), currentDate));
			createAllSubscriptionsForADay(currentDate, detail.getSubscriptionId(), true);
		}
		// return detail.getSubscriptionId();
		return detail.getGeneratedSubscriptionId();
	}

	@Override
	public Subscription updateSubscription(Subscription subscription) {
		SubscriptionDetail detail = manager.find(SubscriptionDetail.class, subscription.getSubscriptionId());
		if (AppUtils.getCurrentDate().before(detail.getStartDate())
				&& !detail.getStartDate().equals(subscription.getStartDate())) {
			LOG.info(String.format("Start date for the subscription with productId %d is modified from %s to %s",
					detail.getSubscriptionId(), detail.getStartDate(), subscription.getStartDate()));
			detail.setStartDate(subscription.getStartDate());
		}
		if (subscription.getEndDate() != null && AppUtils.getCurrentDate().before(detail.getEndDate())
				&& !detail.getEndDate().equals(subscription.getEndDate())) {
			LOG.info(String.format("End date for the subscription with productId %d is modified from %s to %s",
					detail.getSubscriptionId(), detail.getEndDate(), subscription.getEndDate()));
			detail.setEndDate(AppUtils.getNextDate(subscription.getEndDate()));
		}
		if (subscription.getEndDate() == null) {
			LOG.info(String.format("End date for the subscription with productId %d is modified from %s to %s",
					detail.getSubscriptionId(), detail.getEndDate(), subscription.getEndDate()));
			detail.setEndDate(AppUtils.getInfiniteDate());
		}

		detail.setSmsNotification(AppConstants.getValue(subscription.isSmsNotification()));
		detail.setEmailNotification(AppConstants.getValue(subscription.isEmailNotification()));
		detail.setAutomatedDelivery(AppConstants.getValue(subscription.isAutomatedDelivery()));
		detail.getEventItems().addAll(
				addEventItems(detail, SubscriptionEventItemType.DAY_OF_MONTH, subscription.getDaysOfTheMonth()));
		detail.getEventItems()
				.addAll(addEventItems(detail, SubscriptionEventItemType.DAY_OF_WEEK, subscription.getDaysOfTheWeek()));
		detail.getEventItems()
				.addAll(addEventItems(detail, SubscriptionEventItemType.TIME_OF_DAY, subscription.getTimeOfTheDay()));

		detail.setSubscriptionType(subscription.getType().name());
		manager.flush();
		return DataConverter.convert(detail);
	}

	private SubscriptionDetail addSubscription(Order order, Date currentTimestamp) throws DataUpdationException {
		if (order.getOrderId() != null && order.getOrderId() != 0) {
			throw new DataUpdationException(String
					.format("Cannot create the subscription that already exists with ID %d ", order.getOrderId()));

		}
		SubscriptionDetail detail = new SubscriptionDetail();
		detail.setSubscriptionCreationTime(currentTimestamp);
		detail.setGeneratedSubscriptionId(order.getGenerateOrderId());
		detail.setOrderSourceId(order.getSourceId());
		detail.setChannelPartnerId(order.getChannelPartner());
		setTransactionDetail(detail, order.getTransactionDetail());
		detail.setEmpId(order.getEmployeeId());
		detail.setUnitId(order.getUnitId());
		detail.setSettlementType(order.getSettlementType().name());
		detail.setDeliveryAddress(order.getDeliveryAddress());
		detail.setOrderRemark(order.getOrderRemark());
		detail.setCustomerInfo(manager.find(CustomerInfo.class, order.getCustomerId()));
		detail.setStartDate(order.getSubscriptionDetail().getStartDate());
		if (order.getSubscriptionDetail().getEndDate() == null) {
			detail.setEndDate(AppUtils.getInfiniteDate());
		} else {
			detail.setEndDate(AppUtils.getNextDate(order.getSubscriptionDetail().getEndDate()));
		}
		detail.setSubscriptionStatus(order.getSubscriptionDetail().getSubscriptionStatus().name());
		detail.setSubscriptionType(order.getSubscriptionDetail().getType().name());
		detail.setEmailNotification(AppConstants.getValue(order.getSubscriptionDetail().isEmailNotification()));
		detail.setSmsNotification(AppConstants.getValue(order.getSubscriptionDetail().isSmsNotification()));
		detail.setAutomatedDelivery(AppConstants.getValue(order.getSubscriptionDetail().isAutomatedDelivery()));
		detail = add(detail);
		detail.getSubscriptionItems().addAll(addItems(detail, order.getOrders()));
		detail.getSubscriptionSettlements().addAll(addSettlements(detail, order.getSettlements()));
		detail.getEventItems().addAll(addEventItems(detail, SubscriptionEventItemType.DAY_OF_MONTH,
				order.getSubscriptionDetail().getDaysOfTheMonth()));
		detail.getEventItems().addAll(addEventItems(detail, SubscriptionEventItemType.DAY_OF_WEEK,
				order.getSubscriptionDetail().getDaysOfTheWeek()));
		detail.getEventItems().addAll(addEventItems(detail, SubscriptionEventItemType.TIME_OF_DAY,
				order.getSubscriptionDetail().getTimeOfTheDay()));
		setTaxDetail(detail, order.getTransactionDetail().getTaxes());
		return detail;

	}

	private List<SubscriptionEventItem> addEventItems(SubscriptionDetail order, SubscriptionEventItemType type,
			List<Integer> events) {
		if (order.getEventItems() != null) {
			deactivateExistingItems(order, type, events);
		}
		List<SubscriptionEventItem> objects = new ArrayList<SubscriptionEventItem>();
		for (Integer item : events) {
			SubscriptionEventItem data = create(order, type, item);
			if (data != null) {
				objects.add(data);
			}
		}
		return objects;
	}

	@SuppressWarnings("unchecked")
	private void deactivateExistingItems(SubscriptionDetail order, SubscriptionEventItemType type,
			List<Integer> events) {
		Query query = null;
		if (events == null || events.size() == 0) {
			query = manager.createQuery(
					"FROM SubscriptionEventItem E where E.subscriptionDetail.subscriptionId = :subscriptionId and E.eventItemType = :eventItemType and E.eventItemStatus = :eventItemStatus");

		} else {
			query = manager.createQuery(
					"FROM SubscriptionEventItem E where E.subscriptionDetail.subscriptionId = :subscriptionId and E.eventItemType = :eventItemType and E.eventItemStatus = :eventItemStatus and E.eventItemValue NOT IN (:eventItemValue)");
			query.setParameter("eventItemValue", events);

		}

		query.setParameter("subscriptionId", order.getSubscriptionId());
		query.setParameter("eventItemType", type.name());
		query.setParameter("eventItemStatus", AppConstants.ACTIVE);
		List<SubscriptionEventItem> details = query.getResultList();
		if (details != null && details.size() > 0) {
			for (SubscriptionEventItem detail : details) {
				detail.setEventItemStatus(AppConstants.IN_ACTIVE);
			}
		}
		manager.flush();
	}

	private SubscriptionEventItem create(SubscriptionDetail detail, SubscriptionEventItemType type, int value) {
		Query query = manager.createQuery(
				"FROM SubscriptionEventItem E where E.subscriptionDetail.subscriptionId = :subscriptionId and E.eventItemType = :eventItemType and E.eventItemValue = :eventItemValue");
		query.setParameter("eventItemValue", value);
		query.setParameter("subscriptionId", detail.getSubscriptionId());
		query.setParameter("eventItemType", type.name());
		try {
			Object data = query.getSingleResult();
			if (data != null) {
				((SubscriptionEventItem) data).setEventItemStatus(AppConstants.ACTIVE);
				manager.flush();
			}
		} catch (NoResultException e) {
			SubscriptionEventItem eventItem = new SubscriptionEventItem();
			eventItem.setEventItemStatus(AppConstants.ACTIVE);
			eventItem.setEventItemType(type.name());
			eventItem.setEventItemValue(value);
			eventItem.setSubscriptionDetail(detail);
			manager.persist(eventItem);
			manager.flush();
			return eventItem;
		}
		return null;
	}

	private List<SubscriptionItem> addItems(SubscriptionDetail order,
			List<com.stpl.tech.kettle.domain.model.OrderItem> items) {
		List<SubscriptionItem> objects = new ArrayList<SubscriptionItem>();
		if (items != null && items.size() > 0) {
			for (com.stpl.tech.kettle.domain.model.OrderItem item : items) {
				SubscriptionItem parent = addItem(order, item, false, null);
				setTaxDetail(parent, item.getTaxes());
				objects.add(parent);
				if (item.getComposition() != null && item.getComposition().getMenuProducts() != null
						&& item.getComposition().getMenuProducts().size() > 0) {
					for (com.stpl.tech.kettle.domain.model.OrderItem menuItem : item.getComposition()
							.getMenuProducts()) {
						objects.add(addItem(order, menuItem, true, parent));
					}
				}
			}
		}
		return objects;
	}

	private SubscriptionItem addItem(SubscriptionDetail order, com.stpl.tech.kettle.domain.model.OrderItem item,
			boolean isComboConsituent, SubscriptionItem parent) {
		SubscriptionItem info = new SubscriptionItem();
		setData(order, info, item, isComboConsituent, parent);
		manager.persist(info);
		addAddons(info, item.getComposition());
		return info;
	}

	private void setData(SubscriptionDetail order, SubscriptionItem info,
			com.stpl.tech.kettle.domain.model.OrderItem item, boolean isComboConsituent, SubscriptionItem parent) {
		info.setBillType(item.getBillType() == null ? null : item.getBillType().name());
		info.setTaxCode(item.getCode());
		info.setDimension(item.getDimension());
		if (item.getDiscountDetail() != null) {
			setDiscount(info, item);
			info.setDiscountReason(item.getDiscountDetail().getDiscountReason());
			info.setDiscountReasonId(item.getDiscountDetail().getDiscountCode());
		}
		info.setHasAddon(AppConstants
				.getValue(item.getComposition().getAddons() != null && item.getComposition().getAddons().size() > 0));
		info.setPrice(item.getPrice() == null ? BigDecimal.ZERO : item.getPrice());
		info.setProductId(item.getProductId());
		info.setParentItemId(parent != null ? parent.getSubscriptionItemId() : null);
		info.setProductName(item.getProductName());
		info.setQuantity(parent != null ? item.getQuantity() * parent.getQuantity() : item.getQuantity());
		info.setTotalAmount(item.getPrice() == null ? BigDecimal.ZERO
				: item.getPrice().multiply(new BigDecimal(info.getQuantity())));
		if (isComboConsituent) {
			info.setPaidAmount(BigDecimal.ZERO);
		} else {
			info.setPaidAmount(item.getAmount() == null ? BigDecimal.ZERO : item.getAmount());
		}
		info.setComboConstituent(AppConstants.getValue(isComboConsituent));
		info.setSubscriptionDetail(order);
		info.setTaxAmount(item.getTax());
		info.setTakeAway(AppConstants.getValue(item.getTakeAway()));

	}

	private void setData(SubscriptionDetail order, SubscriptionItem info,
			com.stpl.tech.kettle.domain.model.OrderItem item) {
		info.setBillType(item.getBillType().name());
		info.setTaxCode(item.getCode());
		info.setDimension(item.getDimension());
		if (item.getDiscountDetail() != null) {
			setDiscount(info, item);
			info.setDiscountReason(item.getDiscountDetail().getDiscountReason());
			info.setDiscountReasonId(item.getDiscountDetail().getDiscountCode());
		}
		info.setHasAddon(AppConstants
				.getValue(item.getComposition().getAddons() != null && item.getComposition().getAddons().size() > 0));
		info.setPrice(item.getPrice());
		info.setProductId(item.getProductId());
		info.setProductName(item.getProductName());
		info.setQuantity(item.getQuantity());
		info.setTotalAmount(item.getPrice().multiply(new BigDecimal(item.getQuantity())));
		info.setPaidAmount(item.getAmount());
		info.setSubscriptionDetail(order);
		info.setTakeAway(AppConstants.getValue(item.getTakeAway()));

	}

	private void setDiscount(SubscriptionItem info, com.stpl.tech.kettle.domain.model.OrderItem item) {
		if (item.getDiscountDetail() == null || item.getDiscountDetail().getDiscount() == null) {
			return;
		}
		info.setDiscountAmount(item.getDiscountDetail().getDiscount().getValue());
		info.setDiscountPercent(item.getDiscountDetail().getDiscount().getPercentage());
	}

	private List<SubscriptionSettlement> addSettlements(SubscriptionDetail order, List<Settlement> items) {
		List<SubscriptionSettlement> objects = new ArrayList<SubscriptionSettlement>();
		if (items != null && items.size() > 0) {
			for (Settlement item : items) {
				objects.add(addSettlement(order, item));
			}
		}
		return objects;
	}

	private SubscriptionSettlement addSettlement(SubscriptionDetail order, Settlement item) {
		SubscriptionSettlement info = new SubscriptionSettlement();
		setData(order, info, item);
		manager.persist(info);
		return info;
	}

	private void setData(SubscriptionDetail order, SubscriptionSettlement info, Settlement item) {
		info.setAmountPaid(item.getAmount());
		info.setPaymentModeId(item.getMode());
		info.setSubscriptionDetail(order);
	}

	private void addAddons(SubscriptionItem order, OrderItemComposition composition) {
		order.getSubscriptionItemAddons().addAll(addAddon(order, composition));
	}

	private SubscriptionItemAddon addAddon(SubscriptionItem order, String name) {
		SubscriptionItemAddon info = new SubscriptionItemAddon(order, -1, name, ProductClassification.FREE_OPTION.name(), ProductSource.OPTION.name(), "None", "PC",
				new BigDecimal(order.getQuantity()), "N");
		manager.persist(info);
		return info;
	}
	private List<SubscriptionItemAddon> addAddon(SubscriptionItem order, OrderItemComposition composition) {
		List<SubscriptionItemAddon> list = new ArrayList<>();

		if (composition != null) {
			if (composition.getVariants() != null && composition.getVariants().size() > 0) {
				for (IngredientVariantDetail detail : composition.getVariants()) {
					list.add(addAddon(order, ProductSource.SCM, ProductClassification.VARIANT, detail));
				}
			}
			if (composition.getProducts() != null && composition.getProducts().size() > 0) {
				for (IngredientProductDetail detail : composition.getProducts()) {
					list.add(addAddon(order, ProductSource.SCM, ProductClassification.PRODUCT_VARIANT, detail));
				}
			}
			if (composition.getAddons() != null && composition.getAddons().size() > 0) {
				for (IngredientProductDetail detail : composition.getAddons()) {
					list.add(addAddon(order, ProductSource.MENU, detail.getProduct().getClassification(), detail));
				}
			}
			if (composition.getOptions() != null && composition.getOptions().size() > 0) {
				for (String detail : composition.getOptions()) {
					list.add(addAddon(order, detail));
				}
			}
		}
		return list;
	}

	private SubscriptionItemAddon addAddon(SubscriptionItem order, ProductSource source, ProductClassification type,
			IngredientProductDetail detail) {
		SubscriptionItemAddon info = new SubscriptionItemAddon(order, detail.getProduct().getProductId(),
				detail.getProduct().getName(), type == null ? null : type.name(), source.name(),
				detail.getDimension() == null ? null : detail.getDimension().getName(),
				detail.getUom() == null ? null : detail.getUom().name(), detail.getQuantity(),
				AppConstants.getValue(detail.isDefaultSetting()));
		manager.persist(info);
		return info;
	}

	private SubscriptionItemAddon addAddon(SubscriptionItem order, ProductSource source, ProductClassification type,
			IngredientVariantDetail detail) {
		SubscriptionItemAddon info = new SubscriptionItemAddon(order, detail.getProductId(), detail.getAlias(),
				type.name(), source.name(), null, detail.getUom() == null ? null : detail.getUom().name(),
				detail.getQuantity(), AppConstants.getValue(detail.isDefaultSetting()));
		manager.persist(info);
		return info;
	}

	private void setTransactionDetail(SubscriptionDetail detail, TransactionDetail transaction) {
		if (transaction.getDiscountDetail() != null) {
			if (transaction.getDiscountDetail().getDiscount() != null) {
				detail.setDiscountAmount(transaction.getDiscountDetail().getDiscount().getValue());
				detail.setDiscountPercent(transaction.getDiscountDetail().getDiscount().getPercentage());
			}
			if (transaction.getDiscountDetail().getPromotionalOffer() != null) {
				detail.setPromotionalDiscount(transaction.getDiscountDetail().getPromotionalOffer());
			}
			detail.setDiscountReason(transaction.getDiscountDetail().getDiscountReason());
			detail.setDiscountReasonId(transaction.getDiscountDetail().getDiscountCode());
		}

		detail.setRoundOffAmount(transaction.getRoundOffValue());
		detail.setTaxableAmount(transaction.getTaxableAmount());
		// TODO Service charge and GST are not yet set. We will set them here
		// when we have their support
		detail.setSettledAmount(transaction.getPaidAmount());
		detail.setTotalAmount(transaction.getTotalAmount());
		detail.setSaleAmount(transaction.getTotalAmount()
				.subtract(transaction.getDiscountDetail() != null
						&& transaction.getDiscountDetail().getPromotionalOffer() != null
								? transaction.getDiscountDetail().getPromotionalOffer() : new BigDecimal(0.0D)));
		//setTaxDetail(detail, transaction.getTaxes());
	}

	@Override
	public Order getOrder(int subscriptionEventId) throws DataNotFoundException {
		SubscriptionEventDetail detail = manager.find(SubscriptionEventDetail.class, subscriptionEventId);
		return DataConverter.convert(masterCache, detail.getSubscriptionDetail(), detail);
	}

	@Override
	public boolean updateEventStatus(Integer subscriptionEventDetailId, Integer orderId,
			SubscriptionEventStatus status) {
		SubscriptionEventDetail detail = manager.find(SubscriptionEventDetail.class, subscriptionEventDetailId);
		detail.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		detail.setEventStatus(status.name());
		if (orderId != null) {
			detail.setOrderDetail(manager.find(OrderDetail.class, orderId));
		}
		manager.flush();
		return true;
	}

	@Override
	public boolean cancelEvent(Integer subscriptionEventDetailId, SubscriptionEventStatus status, String reason) {
		SubscriptionEventDetail detail = manager.find(SubscriptionEventDetail.class, subscriptionEventDetailId);
		if (isActiveEvent(detail.getEventStatus())) {
			detail.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			detail.setEventStatus(status.name());
			detail.setReasonText(reason);
			manager.flush();
			return true;
		}

		return false;
	}

	private boolean isActiveEvent(String status) {
		return SubscriptionEventStatus.CREATED.name().equals(status);
	}

	@Override
	public SubscriptionEventDetail cloneEventStatus(Integer cloneEventDetailId, SubscriptionEventStatus status) {
		SubscriptionEventDetail detail = manager.find(SubscriptionEventDetail.class, cloneEventDetailId);
		detail.setEventStatus(SubscriptionEventStatus.FAILED.name());
		SubscriptionEventDetail eventDetail = createSubscriptionEventDetail(detail, status);
		manager.persist(eventDetail);
		manager.flush();
		return eventDetail;
	}

	private SubscriptionEventDetail createSubscriptionEventDetail(SubscriptionEventDetail detail,
			SubscriptionEventStatus status) {
		SubscriptionEventDetail eventDetail = new SubscriptionEventDetail();
		eventDetail.setAddTime(AppUtils.getCurrentTimestamp());
		eventDetail.setEventSource(detail.getEventSource());
		eventDetail.setEventStatus(status.name());
		eventDetail.setOrderDetail(detail.getOrderDetail());
		eventDetail.setEventDate(detail.getEventDate());
		eventDetail.setEventTime(detail.getEventTime());
		eventDetail.setReasonText(detail.getReasonText());
		eventDetail.setRegularScheduleChanged(detail.getRegularScheduleChanged());
		eventDetail.setRemark(detail.getRemark());
		eventDetail.setSubscriptionDetail(detail.getSubscriptionDetail());
		eventDetail.setEventId(detail.getEventId());
		eventDetail.setRetryCount(detail.getRetryCount() + 1);
		return eventDetail;
	}

	private SubscriptionEventDetail createSubscriptionEventDetail(Date currentDate, SubscriptionEventItem detail,
			SubscriptionEventStatus status, boolean onlyFutureEvents) {
		Date eventTime = AppUtils.getTimeOfDay(currentDate, (detail.getEventItemValue() * 15) - 45);
		SubscriptionEventDetail eventDetail = new SubscriptionEventDetail();
		if (onlyFutureEvents && eventTime.before(AppUtils.getCurrentTimestamp())) {
			eventDetail.setEventStatus(SubscriptionEventStatus.ON_HOLD.name());
		} else {
			eventDetail.setEventStatus(status.name());
		}
		eventDetail.setAddTime(AppUtils.getCurrentTimestamp());
		eventDetail.setEventSource(SubscriptionEventSource.AUTOMATED.name());

		eventDetail.setEventDate(currentDate);
		eventDetail.setEventTime(eventTime);
		eventDetail.setRegularScheduleChanged(AppConstants.NO);
		eventDetail.setRemark(detail.getSubscriptionDetail().getOrderRemark());
		eventDetail.setSubscriptionDetail(detail.getSubscriptionDetail());
		eventDetail.setEventId(detail.getEventItemValue());
		eventDetail.setRetryCount(1);
		manager.persist(eventDetail);
		return eventDetail;
	}

	@Override
	public void cancelSubscriptionsForToday() {
		Query query = manager.createQuery(
				"FROM SubscriptionStatusEvent E where E.subscriptionDetail.subscriptionStatus <> :subscriptionStatus and E.eventType = :eventType and E.eventStatus = :eventStatus and E.eventStartDate = :today");
		query.setParameter("today", AppUtils.getDate(AppUtils.getCurrentDate()));
		query.setParameter("subscriptionStatus", SubscriptionStatus.CANCELLED.name());
		query.setParameter("eventType", SubscriptionEventStatus.CANCELLED.name());
		query.setParameter("eventStatus", AppConstants.ACTIVE);
		@SuppressWarnings("unchecked")
		List<SubscriptionStatusEvent> details = query.getResultList();
		if (details != null && details.size() > 0) {
			for (SubscriptionStatusEvent detail : details) {
				cancelSubscription(detail.getSubscriptionDetail().getSubscriptionId(), detail.getReasonText(),
						detail.getGeneratedBy(), detail.getAddTime());
			}
		}

	}

	@Override
	public void takeSubscriptionsOffHoldForToday() {
		Query query = manager.createQuery(
				"FROM SubscriptionStatusEvent E where E.subscriptionDetail.subscriptionStatus = :subscriptionStatus and E.eventType = :eventType and E.eventStatus = :eventStatus and E.eventEndDate = :today");
		query.setParameter("today", AppUtils.getDate(AppUtils.getCurrentDate()));
		query.setParameter("subscriptionStatus", SubscriptionStatus.ON_HOLD.name());
		query.setParameter("eventType", SubscriptionEventStatus.ON_HOLD.name());
		query.setParameter("eventStatus", AppConstants.ACTIVE);
		@SuppressWarnings("unchecked")
		List<SubscriptionStatusEvent> details = query.getResultList();
		List<Integer> subscriptionIds = new ArrayList<>();
		if (details != null && details.size() > 0) {
			for (SubscriptionStatusEvent detail : details) {
				subscriptionIds.add(detail.getSubscriptionDetail().getSubscriptionId());
			}
		}
		offHoldSubscriptions(subscriptionIds);

	}

	@Override
	public void putSubscriptionsOnHoldForToday() {
		Query query = manager.createQuery(
				"FROM SubscriptionStatusEvent E where E.subscriptionDetail.subscriptionStatus = :subscriptionStatus and E.eventType = :eventType and E.eventStatus = :eventStatus and E.eventStartDate = :today");
		query.setParameter("today", AppUtils.getDate(AppUtils.getCurrentDate()));
		query.setParameter("subscriptionStatus", SubscriptionStatus.CREATED.name());
		query.setParameter("eventType", SubscriptionEventStatus.ON_HOLD.name());
		query.setParameter("eventStatus", AppConstants.ACTIVE);
		@SuppressWarnings("unchecked")
		List<SubscriptionStatusEvent> details = query.getResultList();
		List<Integer> subscriptionIds = new ArrayList<>();
		if (details != null && details.size() > 0) {
			for (SubscriptionStatusEvent detail : details) {
				subscriptionIds.add(detail.getSubscriptionDetail().getSubscriptionId());
			}
		}
		holdSubscriptions(subscriptionIds);

	}

	@Override
	public void holdSubscriptions(List<Integer> subscriptionIds) {
		if (subscriptionIds == null || subscriptionIds.size() == 0) {
			return;
		}
		Query query = manager.createQuery(
				"FROM SubscriptionDetail E where E.subscriptionId IN (:subscriptionIds) and E.subscriptionStatus <> :subscriptionStatus");
		Date updateTime = AppUtils.getCurrentTimestamp();
		query.setParameter("subscriptionIds", subscriptionIds);
		query.setParameter("subscriptionStatus", SubscriptionStatus.ON_HOLD.name());
		@SuppressWarnings("unchecked")
		List<SubscriptionDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			for (SubscriptionDetail detail : details) {
				if (!detail.getSubscriptionStatus().equals(SubscriptionStatus.CANCELLED.name())) {
					detail.setSubscriptionStatus(SubscriptionStatus.ON_HOLD.name());
					detail.setLastUpdateTime(updateTime);
				}
			}
		}
		manager.flush();
	}

	@Override
	public void offHoldSubscriptions(List<Integer> subscriptionIds) {
		if (subscriptionIds == null || subscriptionIds.size() == 0) {
			return;
		}
		Query query = manager.createQuery(
				"FROM SubscriptionDetail E where E.subscriptionId IN (:subscriptionIds) and E.subscriptionStatus = :subscriptionStatus");
		Date updateTime = AppUtils.getCurrentTimestamp();
		query.setParameter("subscriptionIds", subscriptionIds);
		query.setParameter("subscriptionStatus", SubscriptionStatus.ON_HOLD.name());
		@SuppressWarnings("unchecked")
		List<SubscriptionDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			for (SubscriptionDetail detail : details) {
				if (!detail.getSubscriptionStatus().equals(SubscriptionStatus.CANCELLED.name())) {
					detail.setSubscriptionStatus(SubscriptionStatus.CREATED.name());
					detail.setLastUpdateTime(updateTime);
				}
			}
		}
		manager.flush();
	}

	@Override
	public void cancelSubscription(int subscriptionId, String cancelationReason, Integer cancelledBy,
			Date cancellationTime) {
		SubscriptionDetail detail = manager.find(SubscriptionDetail.class, subscriptionId);
		Date updateTime = AppUtils.getCurrentTimestamp();
		if (!detail.getSubscriptionStatus().equals(SubscriptionStatus.CANCELLED.name())) {
			detail.setSubscriptionStatus(SubscriptionStatus.CANCELLED.name());
			detail.setCancellationReason(cancelationReason);
			detail.setCancellationTime(cancellationTime);
			detail.setCancelledBy(cancelledBy);
			detail.setLastUpdateTime(updateTime);
		}
		manager.flush();
	}

	@Override
	public List<SubscriptionEventDetail> createAllSubscriptionsADay(Date date, boolean onlyFutureEvents) {
		List<SubscriptionEventDetail> events = new ArrayList<>();
		List<SubscriptionDetail> subscriptions = getAllSubscriptionsForADay(date);
		for (SubscriptionDetail detail : subscriptions) {
			List<SubscriptionEventDetail> eventData = createAllSubscriptionsForToday(date, detail, onlyFutureEvents);
			if (eventData != null && eventData.size() > 0) {
				events.addAll(eventData);
			}
		}
		return events;
	}

	private List<SubscriptionDetail> getAllSubscriptionsForADay(Date currentDate) {
		LOG.info(String.format("Creating all subscriptions for date %s ", currentDate));
		Query query = manager.createQuery(
				"FROM SubscriptionDetail E where E.subscriptionStatus = :subscriptionStatus and E.startDate <= :today and E.endDate > :today");
		query.setParameter("today", currentDate);
		query.setParameter("subscriptionStatus", SubscriptionStatus.CREATED.name());
		@SuppressWarnings("unchecked")
		List<SubscriptionDetail> details = query.getResultList();
		return details;

	}

	@Override
	public List<SubscriptionEventDetail> createAllSubscriptionsForADay(Date date, int subscriptionId,
			boolean onlyFutureEvents) {
		SubscriptionDetail detail = manager.find(SubscriptionDetail.class, subscriptionId);
		return createAllSubscriptionsForToday(date, detail, onlyFutureEvents);
	}

	private List<SubscriptionEventDetail> createAllSubscriptionsForToday(Date date, SubscriptionDetail detail,
			boolean onlyFutureEvents) {
		if (detail.getSubscriptionType().equals(SubscriptionType.MONTHLY.name())) {
			return createSubscriptionsForMonthlyType(date, detail, onlyFutureEvents);
		} else if (detail.getSubscriptionType().equals(SubscriptionType.WEEKLY.name())) {
			return createSubscriptionsForWeeklyType(date, detail, onlyFutureEvents);
		}
		return null;
	}

	private List<SubscriptionEventDetail> createSubscriptionsForMonthlyType(Date date, SubscriptionDetail detail,
			boolean onlyFutureEvents) {
		@SuppressWarnings("deprecation")
		int dayOfTheMonth = date.getDate();
		for (SubscriptionEventItem item : detail.getEventItems()) {
			if (item.getEventItemStatus().equals(AppConstants.ACTIVE)
					&& item.getEventItemType().equals(SubscriptionEventItemType.DAY_OF_MONTH.name())
					&& item.getEventItemValue() == dayOfTheMonth) {
				return createSubscriptionsForAllEvents(date, detail.getEventItems(), onlyFutureEvents);
			}
		}
		return null;
	}

	private List<SubscriptionEventDetail> createSubscriptionsForWeeklyType(Date date, SubscriptionDetail detail,
			boolean onlyFutureEvents) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int dayOfTheWeek = cal.get(Calendar.DAY_OF_WEEK);
		for (SubscriptionEventItem item : detail.getEventItems()) {
			if (item.getEventItemStatus().equals(AppConstants.ACTIVE)
					&& item.getEventItemType().equals(SubscriptionEventItemType.DAY_OF_WEEK.name())
					&& item.getEventItemValue() == dayOfTheWeek) {
				return createSubscriptionsForAllEvents(date, detail.getEventItems(), onlyFutureEvents);
			}
		}
		return null;
	}

	private List<SubscriptionEventDetail> createSubscriptionsForAllEvents(Date date,
			List<SubscriptionEventItem> eventItems, boolean onlyFutureEvents) {
		List<SubscriptionEventDetail> events = new ArrayList<>();
		for (SubscriptionEventItem datas : eventItems) {
			if (datas.getEventItemStatus().equals(AppConstants.ACTIVE)
					&& datas.getEventItemType().equals(SubscriptionEventItemType.TIME_OF_DAY.name())) {
				if (!hasPublishedEvent(datas.getSubscriptionDetail().getSubscriptionId(), datas.getEventItemValue(),
						date)) {
					SubscriptionEventDetail event = createSubscriptionEventDetail(date, datas,
							SubscriptionEventStatus.CREATED, onlyFutureEvents);
					if (event != null) {
						events.add(event);
					}
				}
			}
		}
		return events;

	}

	private boolean hasPublishedEvent(int subscriptionId, int eventId, Date date) {

		LOG.info(String.format(
				"finding if a subscription event exists for date %s, subscription productId %d and event productId %d ",
				date, subscriptionId, eventId));
		Query query = manager.createQuery(
				"FROM SubscriptionEventDetail E where E.subscriptionDetail.subscriptionId = :subscriptionId and E.eventDate = :today and E.eventId = :eventId");
		query.setParameter("today", date);
		query.setParameter("subscriptionId", subscriptionId);
		query.setParameter("eventId", eventId);
		@SuppressWarnings("unchecked")
		List<SubscriptionEventDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			LOG.info(String.format(
					"Subscription event exists for date %s, subscription productId %d and event productId %d ", date,
					subscriptionId, eventId));
			return true;
		}
		return false;

	}

	public SubscriptionInfo getSubscriptionInfo(int subscriptionId) throws DataNotFoundException {
		Order order = DataConverter.convert(masterCache, manager.find(SubscriptionDetail.class, subscriptionId), null);
		Unit unit = masterCache.getUnit(order.getUnitId());
		Customer customer = customerDao.getCustomer(order.getCustomerId());
		if (customer.getEmailId() == null) {
			customer.setEmailId(props.getUndeliveredEmail());
		}
		SubscriptionInfo info = new SubscriptionInfo(props.getEnvironmentType(), order, customer, unit,
				masterCache.getChannelPartner(order.getChannelPartner()));
		return info;
	}

	public boolean createSubscriptionStatusEvent(int subscriptionId, SubscriptionStatus status, Date startDate,
			Date endDate, int generatedBy, String reasonText, boolean effectiveImmediate) {
		SubscriptionStatusEvent event = new SubscriptionStatusEvent();
		event.setAddTime(AppUtils.getCurrentTimestamp());
		event.setEventEndDate(AppUtils.getNextDate(endDate));
		event.setEventStartDate(startDate);
		event.setEventStatus(AppConstants.ACTIVE);
		event.setEventType(status.name());
		event.setGeneratedBy(generatedBy);
		event.setReasonText(reasonText);
		event.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		event.setSubscriptionDetail(manager.find(SubscriptionDetail.class, subscriptionId));
		if (effectiveImmediate && AppUtils.getCurrentDate().equals(startDate)) {
			SubscriptionDetail detail = event.getSubscriptionDetail();
			detail.setSubscriptionStatus(status.name());
		}
		manager.persist(event);
		manager.flush();
		return true;
	}

	@Override
	public boolean cancelAllSubscriptionsForADay(int subscriptionId, Date startDate, Date endDate, String reason,
			boolean onlyFutureEvents) {

		LOG.info(String.format(
				"finding if a subscription event exists between start date %s and end date %s and subscription productId %d ",
				startDate, endDate, subscriptionId));
		Query query = manager.createQuery(
				"FROM SubscriptionEventDetail E where E.subscriptionDetail.subscriptionId = :subscriptionId and E.eventDate >= :startDate and E.eventDate <= :endDate and E.eventStatus = :eventStatus");
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		query.setParameter("subscriptionId", subscriptionId);
		query.setParameter("eventStatus", SubscriptionEventStatus.CREATED.name());
		@SuppressWarnings("unchecked")
		List<SubscriptionEventDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			LOG.info(String.format(
					"Subscription event exists between start date %s and end date %s and subscription productId %d ",
					startDate, endDate, subscriptionId));
			for (SubscriptionEventDetail eventDetail : details) {
				cancelEvent(eventDetail.getSubscriptionEventDetailId(), SubscriptionEventStatus.ON_HOLD, reason);
			}
		}
		return true;
	}

	@Override
	public boolean cancelHold(int subscriptionEventDetailId) {

		SubscriptionStatusEvent event = manager.find(SubscriptionStatusEvent.class, subscriptionEventDetailId);
		if (event.getEventStartDate().after(AppUtils.getCurrentDate())
				&& event.getEventStatus().equals(AppConstants.ACTIVE)) {
			event.setEventStatus(AppConstants.IN_ACTIVE);
			event.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			manager.flush();
			return true;
		}
		return false;
	}

	@Override
	public boolean updateHold(int subscriptionEventDetailId, Date endDate, boolean withImmediateEffect) {

		SubscriptionStatusEvent event = manager.find(SubscriptionStatusEvent.class, subscriptionEventDetailId);
		if (event.getEventStatus().equals(AppConstants.ACTIVE)) {
			event.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			if (endDate.equals(AppUtils.getCurrentDate())) {
				event.setEventEndDate(endDate);
				if (withImmediateEffect) {
					createAllSubscriptionsForADay(endDate, event.getSubscriptionDetail().getSubscriptionId(), true);
				}
				event.getSubscriptionDetail().setSubscriptionStatus(SubscriptionStatus.CREATED.name());
				event.getSubscriptionDetail().setLastUpdateTime(AppUtils.getCurrentTimestamp());
				manager.flush();
				return true;
			} else {
				event.setEventEndDate(AppUtils.getNextDate(endDate));
				manager.flush();
				return true;
			}
		}
		return false;
	}

	@Override
	public boolean updateHold(int subscriptionEventDetailId, Date startDate, Date endDate, boolean withImmediateEffect,
			String reason) {

		SubscriptionStatusEvent event = manager.find(SubscriptionStatusEvent.class, subscriptionEventDetailId);
		if (event.getEventStatus().equals(AppConstants.ACTIVE)) {
			event.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			if (startDate.equals(AppUtils.getCurrentDate())) {
				if (withImmediateEffect) {
					cancelAllSubscriptionsForADay(event.getSubscriptionDetail().getSubscriptionId(), startDate, endDate,
							reason, true);
				}
				event.setEventStartDate(startDate);
				event.setEventEndDate(endDate);
				event.setReasonText(reason);
				event.getSubscriptionDetail().setSubscriptionStatus(SubscriptionStatus.ON_HOLD.name());
				event.getSubscriptionDetail().setLastUpdateTime(AppUtils.getCurrentTimestamp());
				manager.flush();
				return true;
			} else {
				event.setEventStartDate(startDate);
				event.setEventEndDate(AppUtils.getNextDate(endDate));
				event.setReasonText(reason);
				manager.flush();
				return true;
			}
		}
		return false;
	}

	@Override
	public boolean updateCancel(int subscriptionEventDetailId, Date startDate, String reason) {

		SubscriptionStatusEvent event = manager.find(SubscriptionStatusEvent.class, subscriptionEventDetailId);
		if (event.getEventStatus().equals(AppConstants.ACTIVE)) {
			event.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			event.setEventStartDate(startDate);
			event.setReasonText(reason);
			manager.flush();
			return true;
		}
		return false;
	}

	@Override
	public List<Order> getSubscriptions(String contactNumber) throws DataNotFoundException {
		List<Order> subscriptions = new ArrayList<>();
		LOG.info(String.format("finding subscriptions for the contact number %s", contactNumber));
		Query query = manager
				.createQuery("FROM SubscriptionDetail E where E.customerInfo.contactNumber = :contactNumber");
		query.setParameter("contactNumber", contactNumber);
		@SuppressWarnings("unchecked")
		List<SubscriptionDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			LOG.info(String.format("Found %d subscriptions for %s contact number", details.size(), contactNumber));
			for (SubscriptionDetail eventDetail : details) {
				Order order = getOrder(eventDetail.getSubscriptionId(), true);
				if (order != null) {
					subscriptions.add(order);
				}
			}
		}

		return subscriptions;
	}

	@Override
	public List<SubscriptionEvent> getSubscriptionOrders(String contactNumber) throws DataNotFoundException {
		List<SubscriptionEvent> subscriptions = new ArrayList<>();
		LOG.info(String.format("finding subscription events for the contact number %s", contactNumber));
		Query query = manager.createQuery(
				"FROM SubscriptionEventDetail E where E.subscriptionDetail.customerInfo.contactNumber = :contactNumber and E.eventDate >= :today");
		query.setParameter("contactNumber", contactNumber);
		query.setParameter("today", AppUtils.getCurrentDate());
		@SuppressWarnings("unchecked")
		List<SubscriptionEventDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			LOG.info(
					String.format("Found %d subscription events for %s contact number", details.size(), contactNumber));
			for (SubscriptionEventDetail eventDetail : details) {
				subscriptions.add(getEventData(eventDetail));
			}
		}
		return subscriptions;
	}

	@Override
	public List<SubscriptionEvent> getSubscriptionOrders(int unitId) throws DataNotFoundException {
		List<SubscriptionEvent> subscriptions = new ArrayList<>();
		LOG.info(String.format("finding subscription events for the unit %d", unitId));
		Query query = manager.createQuery(
				"FROM SubscriptionEventDetail E where E.subscriptionDetail.unitId = :unitId and E.eventDate >= :today");
		query.setParameter("unitId", unitId);
		query.setParameter("today", AppUtils.getCurrentDate());
		@SuppressWarnings("unchecked")
		List<SubscriptionEventDetail> details = query.getResultList();
		if (details != null && details.size() > 0) {
			LOG.info(String.format("Found %d subscription events for %d unit Id", details.size(), unitId));
			for (SubscriptionEventDetail eventDetail : details) {
				subscriptions.add(getEventData(eventDetail));
			}
		}
		return subscriptions;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<SubscriptionStatusEvents> getStatusEventsForSubscription(Integer subscriptionId)
			throws DataNotFoundException {
		Query query = manager.createQuery(
				"FROM SubscriptionStatusEvent E where E.subscriptionDetail.subscriptionId = :subscriptionId "
						+ "and E.eventEndDate >= :today and E.eventStatus = :eventStatus");
		query.setParameter("subscriptionId", subscriptionId);
		// query.setParameter("eventType", SubscriptionStatus.ON_HOLD.name());
		query.setParameter("today", AppUtils.getCurrentDate());
		query.setParameter("eventStatus", AppConstants.ACTIVE);
		List<SubscriptionStatusEvent> detail = new ArrayList<>();
		detail = query.getResultList();
		return getStatusEvent(detail);
	}

	private SubscriptionEvent getEventData(SubscriptionEventDetail eventDetail) throws DataNotFoundException {
		SubscriptionEvent event = new SubscriptionEvent();
		Order order = getOrder(eventDetail.getSubscriptionDetail().getSubscriptionId(), true);
		if (order != null) {
			event.setSubscriptionDetail(order);
		}
		event.setAddTime(eventDetail.getAddTime());
		event.setEventId(eventDetail.getEventId());
		event.setEventDate(eventDetail.getEventDate());
		event.setEventSource(SubscriptionEventSource.valueOf(eventDetail.getEventSource()));
		event.setEventTime(eventDetail.getEventTime());
		event.setLastUpdateTime(eventDetail.getLastUpdateTime());
		if (eventDetail.getOrderDetail() != null) {
//			                     OrderDetail orderDetail = dao.find(OrderDetail.class, eventDetail.getOrderDetail().getOrderId());
			if(Objects.nonNull((eventDetail.getOrderDetail().getInvoiceId()))){
				try {
					OrderInvoiceDetail orderInvoiceDetail = getInvoiceDetail((eventDetail.getOrderDetail().getGeneratedOrderId()));
					(eventDetail.getOrderDetail()).setInvoiceDetail(orderInvoiceDetail);
				}
				catch(Exception e){
					LOG.info("Unable to fetch invoice Id for this order");
				}
			}
			event.setOrderDetail(DataConverter.convert(masterCache, eventDetail.getOrderDetail(), null,recipeCache,props));
		}
		event.setOriginalDeliveryTime(AppUtils.getTimeOfDay(eventDetail.getEventDate(), eventDetail.getEventId() * 15));
		event.setActualDeliveryTime(AppUtils.getTimeOfDay(eventDetail.getEventTime(), 45));
		event.setReason(eventDetail.getReasonText());
		event.setRemark(eventDetail.getRemark());
		event.setRetryCount(eventDetail.getRetryCount());
		event.setStatus(SubscriptionEventStatus.valueOf(eventDetail.getEventStatus()));
		event.setSubscriptionEventId(eventDetail.getSubscriptionEventDetailId());
		event.setTimeUpdated(AppConstants.getValue(eventDetail.getRegularScheduleChanged()));
		return event;
	}

	public OrderInvoiceDetail getInvoiceDetail(String generatedOrderId){
		Query query = manager.createQuery("FROM OrderInvoiceDetail E where E.orderId =:generatedOrderId ");
		query.setParameter("orderId", generatedOrderId);
		try {
			OrderInvoiceDetail orderInvoiceDetail= (OrderInvoiceDetail) query.getSingleResult();
			LOG.info("***************************************{}",orderInvoiceDetail);
			return orderInvoiceDetail;
		} catch (Exception e) {
			LOG.info("Error while fetching order invoice detail for order id");
		}
		return null;
	}
	@Override
	public SubscriptionEvent updateEventData(SubscriptionEvent event)
			throws DataUpdationException, DataNotFoundException {
		SubscriptionEventDetail detail = manager.find(SubscriptionEventDetail.class, event.getSubscriptionEventId());
		if (detail.getOrderDetail() != null) {
			throw new DataUpdationException(String.format(
					"Cannot update subscription event of subscription %d as the order has been already created with productId %d",
					detail.getSubscriptionDetail().getSubscriptionId(), detail.getOrderDetail().getOrderId()));
		}

		detail.setRegularScheduleChanged(AppConstants.getValue(event.isRegularTimeChanged()));
		// detail.setEventTime(event.getEventTime());
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(event.getOriginalDeliveryTime().getTime());
		cal.add(Calendar.MINUTE, -45);
		detail.setEventTime(cal.getTime());
		detail.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		detail.setReasonText(event.getReason());
		detail.setRemark(event.getRemark());
		detail.setEventStatus(event.getStatus().name());
		manager.flush();
		return getEventData(detail);
	}

	private List<SubscriptionStatusEvents> getStatusEvent(List<SubscriptionStatusEvent> eventList) {
		List<SubscriptionStatusEvents> events = new ArrayList<>();
		for (SubscriptionStatusEvent event : eventList) {
			com.stpl.tech.kettle.domain.model.SubscriptionStatusEvents ev = new com.stpl.tech.kettle.domain.model.SubscriptionStatusEvents();
			ev.setSubscriptionEventId(event.getSubscriptionStatusEventId());
			ev.setAddTime(event.getAddTime());
			ev.setEventEndDate(event.getEventEndDate());
			ev.setEventStartDate(event.getEventStartDate());
			ev.setEventStatus(event.getEventStatus());
			ev.setEventType(event.getEventType());
			ev.setGeneratedBy(event.getGeneratedBy());
			ev.setLastUpdateTime(event.getLastUpdateTime());
			ev.setReasonText(event.getReasonText());
			ev.setSubscriptionDetail(event.getSubscriptionDetail().getSubscriptionId());
			events.add(ev);
		}
		return events;
	}

	private void setTaxInfo(TaxationDetailDao taxDetail, TaxDetail tax) {
		taxDetail.setTaxCode(tax.getCode());
		taxDetail.setTaxType(tax.getType());
		taxDetail.setTotalTax(tax.getValue());
		taxDetail.setTaxPercentage(tax.getPercentage());
		taxDetail.setTotalAmount(tax.getTotal());
		taxDetail.setTaxableAmount(tax.getTaxable());
	}

	private void setTaxDetail(SubscriptionDetail detail, List<TaxDetail> taxes) {
		if (taxes == null || taxes.isEmpty()) {
			return;
		}
		for (TaxDetail tax : taxes) {
			SubscriptionTaxDetail taxDetail = new SubscriptionTaxDetail();
			setTaxInfo(taxDetail, tax);
			taxDetail.setSubscriptionDetail(detail);
			manager.persist(taxDetail);
		}
		manager.flush();

	}

	private void setTaxDetail(SubscriptionItem detail, List<TaxDetail> taxes) {
		if (taxes == null || taxes.isEmpty()) {
			return;
		}
		for (TaxDetail tax : taxes) {
			SubscriptionItemTaxDetail taxDetail = new SubscriptionItemTaxDetail();
			setTaxInfo(taxDetail, tax);
			taxDetail.setSubscriptionItem(detail);
			manager.persist(taxDetail);
		}
		manager.flush();
	}

	@Override
	public List<SubscriptionViewData> getAllNotUsedSubscriptionNthDay(Date businessDate, int nthDay) {
		try {
			LOG.info("CHAAYOS_SUBSCRIPTION_REMINDER ### Looking for customer not used subscription from {}", businessDate);
			Query nq = this.manager.createNativeQuery(NamedQueryDefinition.SUBSCRIPTION_NOT_USED_NTH_DAY.getQuery(), "SubscriptionViewData");
			nq.setParameter("businessDate", AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()));
			nq.setParameter("nthDay", nthDay);
			nq.setParameter("mthDay", nthDay-1);
			List<SubscriptionViewData> subscriptionViewData = nq.getResultList();
			if (Objects.isNull(subscriptionViewData) || subscriptionViewData.isEmpty()) {
				LOG.info("CHAAYOS_SUBSCRIPTION_REMINDER ### Did Not Find customer which not have used subscription from {}", businessDate);
				return null;
			} else {
				LOG.info("CHAAYOS_SUBSCRIPTION_REMINDER ### found data with size :: {}",subscriptionViewData.size());
				return subscriptionViewData;
			}
		} catch (Exception e) {
			LOG.error("CHAAYOS_SUBSCRIPTION_REMINDER ###  Exception Faced while retrieving customer not used subscription {} ", businessDate, e);
			return null;
		}
	}

	@Override
	public List<SubscriptionViewData> getAllExpiringSubscriptionOnNthDay(Date businessDate) {
		try {
			businessDate = AppUtils.getDayBeforeOrAfterDay(businessDate, -1);
			LOG.info("CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER ### Looking for customer subscription expiring on {}", businessDate);
			Query query = manager.createNativeQuery("CALL GET_EXPIRING_SUBSCRIPTION_DATA(:businessDate)");
			query.setParameter("businessDate", businessDate);
			List<Object[]> objs = query.getResultList();
			if(objs.isEmpty()){
				LOG.info("CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER ### no data found to send expiry reminder for date : {}",businessDate);
			}else{
				LOG.info("CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER ### data found with size : {} to send expiry reminder for date : {}",objs.size(),businessDate);
			}
			return convertToViewData(objs);
		} catch (Exception e) {
			LOG.error("CHAAYOS_SUBSCRIPTION_REMINDER ###  Exception Faced while retrieving customer subscription expiring on {} ", businessDate, e);
			return null;
		}
	}

	private List<SubscriptionViewData> convertToViewData(List<Object[]> objs){
		List<SubscriptionViewData> subscriptionViewDataList = new ArrayList<>();
		for(Object[] obj : objs){
			SubscriptionViewData viewData = new SubscriptionViewData();
			viewData.setCustomerId((Integer) obj[0]);
			viewData.setCustomerName((String) obj[1]);
			viewData.setCustomerNumber((String) obj[2]);
			viewData.setSubscriptionName((String) obj[3]);
			viewData.setPrice((BigDecimal) obj[4]);
			viewData.setSubscriptionPlanCode((String) obj[5]);
			subscriptionViewDataList.add(viewData);
		}
		return subscriptionViewDataList;
	}
}
