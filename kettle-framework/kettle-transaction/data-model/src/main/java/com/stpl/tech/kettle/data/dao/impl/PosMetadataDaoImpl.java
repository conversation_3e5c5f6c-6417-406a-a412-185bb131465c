/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.core.ReportDefinitionEnum;
import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PosMetadataDao;
import com.stpl.tech.kettle.data.dao.ReportingDao;
import com.stpl.tech.kettle.data.model.ClosurePaymentDetails;
import com.stpl.tech.kettle.data.model.ClosureStatus;
import com.stpl.tech.kettle.data.model.CreditAccountDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.ItemConsumptionEstimate;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.PullDetail;
import com.stpl.tech.kettle.data.model.PullDetailReasons;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitProductStockEventDataDetail;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.data.model.UnitTokenSequence;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.PullPacketStatus;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.scm.UnitDayWiseItemData;
import com.stpl.tech.master.domain.model.scm.UnitDayWiseItemKey;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimate;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimateRequest;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import java.sql.Time;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class PosMetadataDaoImpl extends AbstractDaoImpl implements PosMetadataDao {

    private static final Logger LOG = LoggerFactory.getLogger(PosMetadataDaoImpl.class);

    @Autowired
    private ReportingDao reportDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvironmentProperties props;

    public TransactionMetadata getTransactionData() throws DataNotFoundException {
        TransactionMetadata metadata = new TransactionMetadata();
        metadata.setComplimentaryCodes(getComplimentaryCodes(false));
        List<ChannelPartnerDetail> partners = masterDataCache.getAllChannelPartners().stream().filter(channelPartnerDetail ->
            AppConstants.ACTIVE.equalsIgnoreCase(channelPartnerDetail.getStatus())).collect(Collectors.toList());
        metadata.getChannelPartner().addAll(partners);
        metadata.getDeliveryPartner().addAll(getAllDeliveryPartner());
        metadata.setServiceTaxExemptRule(props.getServiceTaxExemptRule());
        metadata.setSubscriptionProductId(props.getSubscriptionProductId());
        return metadata;
    }

    public TransactionMetadata getTransactionData(UnitCategory category) throws DataNotFoundException {
        TransactionMetadata metadata = new TransactionMetadata();
        metadata.setComplimentaryCodes(getComplimentaryCodes(false));
        List<ChannelPartnerDetail> partners = masterDataCache.getAllChannelPartners().stream().filter(channelPartnerDetail ->
            AppConstants.ACTIVE.equalsIgnoreCase(channelPartnerDetail.getStatus())).collect(Collectors.toList());
        List<IdCodeName> data = new ArrayList<>();
        for (IdCodeName d : partners) {
            if (UnitCategory.COD.equals(category)
                && !d.getCode().equals(TransactionConstants.CHANNEL_PARTNER_CHAAYOS)) {
                data.add(d);
            } else if (!UnitCategory.COD.equals(category)
                && (d.getCode().equals(TransactionConstants.CHANNEL_PARTNER_CHAAYOS_TAKE_AWAY)
                || d.getCode().equals(TransactionConstants.CHANNEL_PARTNER_CHAAYOS))) {
                data.add(d);
            }
        }
        metadata.getChannelPartner().addAll(data);
        metadata.getDeliveryPartner().addAll(getAllDeliveryPartner());
        metadata.setServiceTaxExemptRule(props.getServiceTaxExemptRule());
        Collection<CancellationReason> cancellations = masterDataCache.getCancellationReason(category);
        if (cancellations != null && cancellations.size() > 0) {
            metadata.getCancellationReasons().addAll(cancellations);
        }
        metadata.setSubscriptionProductId(props.getSubscriptionProductId());
        metadata.setDisableCafeToOperate(props.isCafeOperationDisable());
        return metadata;
    }

    public ListData getComplimentaryCodes(boolean getAll) throws DataNotFoundException {
        Query query = manager.createQuery("FROM ComplimentaryCode C WHERE C.status = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        if (getAll) {
            query = manager.createQuery("FROM ComplimentaryCode C");
        }
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.kettle.data.model.ComplimentaryCode> list = query.getResultList();
        return DataConverter.convertToListData(list);
    }

    public List<UnitDetail> getDSRConfigpartnerId() {
        Query query = manager.createQuery("from UnitDetail E");
        List<UnitDetail> results = query.getResultList();
        return results;
    }

    public List<PartnerAttributes> getDSRConfig(Integer partnerId) {
        Query query = manager.createQuery("FROM PartnerAttributes E where E.partnerId=:partnerId");
        query.setParameter("partnerId", partnerId);
        List<PartnerAttributes> results = query.getResultList();
        return results;
    }

    public List<IdCodeName> getAllDeliveryPartner() throws DataNotFoundException {
        List<IdCodeName> results = new ArrayList<IdCodeName>();
        Query query = manager.createQuery("FROM DeliveryPartner E order by E.partnerId");
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.kettle.data.model.DeliveryPartner> list = query.getResultList();
        for (com.stpl.tech.kettle.data.model.DeliveryPartner partner : list) {
            results.add(DataConverter.convert(partner));
        }
        return results;
    }

    public int closeDay(int unitId, int employeeId, String comment, int startOrderId, int lastOrderId, Date currentDate)
        throws DataUpdationException {
        try {
            if (hasClosure(unitId, currentDate)) {
                throw new DataUpdationException(String.format(
                    "Day Closure for unit with productId %d for business date %s is already completed", unitId,
                    currentDate));
            }
            UnitClosureDetails closureDetails = new UnitClosureDetails(unitId, currentDate,
                AppUtils.getCurrentTimestamp(), employeeId, ClosureState.INITIATED.name());
            closureDetails.setClosureComment(comment);
            closureDetails.setStartOrderId(startOrderId);
            closureDetails.setLastOrderId(lastOrderId);
            closureDetails.setReportStatus(ReportStatus.PENDING.name());
            // TODO set recon Status
            manager.persist(closureDetails);
            ClosureStatus statusObj = new ClosureStatus(closureDetails, ClosureState.INITIATED.name(),
                AppUtils.getCurrentTimestamp());
            manager.persist(statusObj);
            closureDetails.getClosureStatuses().add(statusObj);
            reportDao.createReportExecutionDetail(unitId,
                ReportDefinitionEnum.UNIT_DAILY_MANAGER_REPORT.getReportDefId());
            resetTokenNumber(unitId);
            setBusinessDate(unitId, startOrderId, lastOrderId, currentDate);

            return closureDetails.getClosureId();
        } catch (Exception e) {
            throw new DataUpdationException(
                String.format("Error while closing the day for unit %d and date %s", unitId, currentDate), e);
        }

    }

    public void callProductEsimateUpdateProc(int unitId, Date currentDate) {
        try {
            LOG.info("calling DAY_CLOSE_ESTIMATE_DATA_PROC procedure :::::::::::");
            updateSuggestiveConsumptionEstimates(currentDate, unitId);
        } catch (Exception e) {
            String message = String.format("DAY_CLOSE_SUGGESTIVE_PROCEDURE  error for UNIT: %s and BUSINESS_DATE: %s",
                unitId, currentDate);
            LOG.error(message, e);
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle",
                SlackNotification.ORDERING_ERRORS, message);
        }
    }
    public void markDayCloseAsCancelled(int dayClosureId) {
        try {
            UnitClosureDetails closureDetails = manager.find(UnitClosureDetails.class, dayClosureId);
            closureDetails.setCurrentStatus(ClosureState.CANCELLED.name());
            removeBusinessDate(closureDetails.getUnitId(), closureDetails.getStartOrderId(), closureDetails.getLastOrderId());
            List<ClosurePaymentDetails> closurePaymentDetails = closureDetails.getClosurePaymentDetailses();
            closurePaymentDetails.forEach(closurePaymentDetail -> {
                closurePaymentDetail.setReconciliationStatus(AppConstants.CANCELLED);
                markPullDetailsCancelled(closurePaymentDetail);
            });
        }catch (Exception e){
            LOG.error("Exception Caught While Cancelling the Data Close");
        }
        manager.flush();
    }

    public void markPullDetailsCancelled(ClosurePaymentDetails paymentDetails) {
        Query query = manager.createQuery("FROM PullDetail P WHERE P.pullDate = :businessDate AND P.unitId = :unitId");
        query.setParameter("businessDate", paymentDetails.getUnitClosureDetails().getBusinessDate());
        query.setParameter("unitId", paymentDetails.getUnitClosureDetails().getUnitId());
        List<PullDetail> allPullDetails = query.getResultList();
        if(allPullDetails.size() > 0) {
            for(PullDetail singlePullDetail: allPullDetails) {
                try {
                    PullDetail pd = manager.find(PullDetail.class, singlePullDetail.getId());
                    pd.setStatus(PullPacketStatus.CANCELLED.value());
                    manager.persist(pd);
                }catch (Exception e){
                    LOG.error("Exception Caught While Cancelling Pull Detail For Unit {}",paymentDetails.getUnitClosureDetails().getUnitId());
                }
            }
        }
    }

    private void resetTokenNumber(int unitId) {
        Query query = manager.createQuery("FROM UnitTokenSequence E where E.unitId = :unitId");
        query.setParameter("unitId", unitId);
        UnitTokenSequence sequence = null;
        try {
            sequence = (UnitTokenSequence) query.getSingleResult();
        } catch (NoResultException e) {
            // No problem it will be created while ordering
            return;
        }
        sequence.setNextValue(1);
        manager.flush();
    }

    private void setBusinessDate(int unitId, int startOrderId, int endOrderId, Date businessDate) {
        Query query = manager.createQuery("UPDATE OrderDetail E set E.businessDate = :businessDate "
            + "where E.unitId = :unitId and E.orderId > :startOrderId and E.orderId <= :endOrderId");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        query.setParameter("startOrderId", startOrderId);
        query.setParameter("endOrderId", endOrderId);
        query.executeUpdate();
        manager.flush();
    }

    private void removeBusinessDate(int unitId, int startOrderId, int endOrderId) {
        try {
            Query query = manager.createQuery("UPDATE OrderDetail E set E.businessDate = NULL "
                    + "where E.unitId = :unitId and E.orderId > :startOrderId and E.orderId <= :endOrderId");
            query.setParameter("unitId", unitId);
            query.setParameter("startOrderId", startOrderId);
            query.setParameter("endOrderId", endOrderId);
            query.executeUpdate();
            manager.flush();
        }catch (Exception e){
            LOG.error("Exception Caught While Removing Date From Order Details for unit {}",unitId);
        }
    }

    private boolean hasClosure(int unitId, Date date) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.unitId = :unitId and E.businessDate = :businessDate and E.currentStatus IN (:status)");
        query.setParameter("unitId", unitId);
        query.setParameter("status", Arrays.asList(ClosureState.INITIATED.name(), ClosureState.IN_PROGRESS.name()));
        query.setParameter("businessDate", date);
        @SuppressWarnings("unchecked")
        List<Object> o = query.getResultList();
        return o != null && o.size() > 0;
    }

    @Override
    public UnitClosure getUnitsClosure(int unitId, Date date) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.unitId = :unitId and E.businessDate = :businessDate and E.currentStatus != :status order by closureId desc");
        query.setParameter("unitId", unitId);
        query.setParameter("status", ClosureState.FAILED.name());
        query.setParameter("businessDate", date);
        List<UnitClosureDetails> o = query.getResultList();
        return o != null && o.size() > 0 ? DataConverter.convert(o.get(0)) : null;
    }

    public boolean isDayClosed(int unitId, Date date) throws DataUpdationException {
        return hasClosure(unitId, date);
    }

    public List<UnitClosureDetails> getClosureFromBusinessDate(Date businessDate) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.businessDate >= :businessDate and currentStatus <> :currentStatus");
        query.setParameter("businessDate", businessDate);
        query.setParameter("currentStatus", ClosureState.CANCELLED.name());
        @SuppressWarnings("unchecked")
        List<UnitClosureDetails> unitClosureDetails = query.getResultList();
        return unitClosureDetails;
    }

    @Override
    public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.businessDate = :businessDate and currentStatus <> :currentStatus");
        query.setParameter("businessDate", businessDate);
        query.setParameter("currentStatus", ClosureState.CANCELLED.name());
        @SuppressWarnings("unchecked")
        List<UnitClosureDetails> unitClosureDetails = query.getResultList();
        return unitClosureDetails;
    }

    @Override
    public List<UnitClosureDetails> getClosures(Date businessDate, ReportStatus status) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.businessDate = :businessDate and currentStatus <> :currentStatus and reportStatus = :reportStatus");
        query.setParameter("businessDate", businessDate);
        query.setParameter("currentStatus", ClosureState.CANCELLED.name());
        query.setParameter("reportStatus", status.name());
        @SuppressWarnings("unchecked")
        List<UnitClosureDetails> unitClosureDetails = query.getResultList();
        return unitClosureDetails;
    }


    public List<UnitClosureDetails> getClosureForBusinessDate(Date businessDate, Set<Integer> units) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.businessDate = :businessDate and currentStatus <> :currentStatus and E.unitId IN (:units)");
        query.setParameter("businessDate", businessDate);
        query.setParameter("currentStatus", ClosureState.CANCELLED.name());
        query.setParameter("units", units);
        @SuppressWarnings("unchecked")
        List<UnitClosureDetails> unitClosureDetails = query.getResultList();
        return unitClosureDetails;
    }

    @Override
    public List<UnitClosureDetails> getPendingPnLClosureForBusinessDate(Date businessDate) {
        Query query = manager.createQuery(
            "FROM UnitClosureDetails E where E.businessDate = :businessDate and currentStatus <> :currentStatus and E.pnlGenerated = :pnlGenerated");
        query.setParameter("businessDate", businessDate);
        query.setParameter("currentStatus", ClosureState.CANCELLED.name());
        query.setParameter("pnlGenerated", AppConstants.NO);
        @SuppressWarnings("unchecked")
        List<UnitClosureDetails> unitClosureDetails = query.getResultList();
        return unitClosureDetails;
    }

    public boolean updateDayCloseDate(int closureId, Date businessDate) throws DataUpdationException {
        try {
            UnitClosureDetails closureDetails = manager.find(UnitClosureDetails.class, closureId);
            closureDetails.setBusinessDate(businessDate);
            manager.flush();
        } catch (Exception e) {
            throw new DataUpdationException(
                String.format("Error while cancelling the day close for Unit Closure Id %d", closureId), e);
        }
        return true;
    }

    @Override
    public boolean updateReportStatus(int closureId, ReportStatus status) {
        UnitClosureDetails closureDetails = manager.find(UnitClosureDetails.class, closureId);
        closureDetails.setReportStatus(status.name());
        manager.flush();
        return true;
    }

    public void updateSalesData(Date businessDate) {
        Query query = manager.createNativeQuery("CALL SP_INSERT_SALES_DATA(:businessDate)");
        query.setParameter("businessDate", businessDate);
        query.executeUpdate();
    }

    public void updateDailySalesData(Date businessDate) {
        Query query = manager.createNativeQuery("CALL SP_INSERT_DAY_WISE_SALES_DATA(:businessDate)");
        query.setParameter("businessDate", businessDate);
        query.executeUpdate();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<UnitToDeliveryPartnerMappings> getAllDeliveryPartnerMappings() {
        Query query = manager.createQuery("FROM UnitToDeliveryPartnerMappings");
        return query.getResultList();
    }

    public CreditAccountDetail addCreditAccount(CreditAccountDetail detail) {
        manager.persist(detail);
        manager.flush();
        return detail;
    }

    public PartnerAttributes addPartnerAttributes(PartnerAttributes partner) {
        PartnerAttributes data = manager.find(PartnerAttributes.class, partner.getId());
        if (data == null) {
            manager.persist(partner);
        } else {
            data.setPartnerId(partner.getPartnerId());
            data.setMappingType(partner.getMappingType());
            data.setMappingValue(partner.getMappingValue());
            data.setPartnerType("SALES_REPORT");
        }
        manager.flush();
        return partner;
    }

    public PartnerAttributes updatePartnerAttributes(PartnerAttributes partner) {

        String mappingType = partner.getMappingType();
        Integer partnerId = partner.getPartnerId();
        Query query = manager.createQuery("FROM PartnerAttributes E where E.partnerId = :partnerId and E.mappingType = :mappingType");
        query.setParameter("partnerId", partnerId);
        query.setParameter("mappingType", mappingType);
        try{
            PartnerAttributes partnerAttributes = (PartnerAttributes) query.getSingleResult();
            partnerAttributes.setMappingValue(partner.getMappingValue());
            partnerAttributes.setPartnerType("SALES_REPORT");
            manager.persist(partnerAttributes);
            manager.flush();
        }
       catch (NoResultException e){
           LOG.info("Exception Caught :", e);

           PartnerAttributes partnerAttributes = new PartnerAttributes();
           partnerAttributes.setPartnerId(partnerId);
           partnerAttributes.setMappingType(mappingType);
           partnerAttributes.setMappingValue(partner.getMappingValue());
           partnerAttributes.setPartnerType(partner.getMappingValue());
           manager.persist(partnerAttributes);
           manager.flush();
       }
       return partner;
    }

    public CreditAccountDetail updateCreditAccount(CreditAccountDetail detail) {
        CreditAccountDetail data = manager.find(CreditAccountDetail.class, detail.getCreditAccountDetailId());
        if (data == null) {
            manager.persist(detail);
        } else {
            data.setAccountContactPerson(detail.getAccountContactPerson());
            data.setAccountContactPersonEmail(detail.getAccountContactPersonEmail());
            data.setAccountContactPersonNumber(detail.getAccountContactPersonNumber());
            data.setAccountStatus(detail.getAccountStatus());
            data.setAddress(detail.getAddress());
            data.setBankDetail(detail.getBankDetail());
            data.setCertificateOfIncorporation(detail.getCertificateOfIncorporation());
            data.setCompanyContact(detail.getCompanyContact());
            data.setContactPerson(detail.getContactPerson());
            data.setContactPersonEmail(detail.getContactPersonEmail());
            data.setContactPersonNumber(detail.getContactPersonNumber());
            data.setCreditDays(detail.getCreditDays());
            data.setLegalName(detail.getLegalName());
            data.setDisplayName(detail.getDisplayName());
            data.setPanNumber(detail.getPanNumber());
            data.setTanNumber(detail.getTanNumber());
        }
        manager.flush();
        return detail;
    }

    @SuppressWarnings("unchecked")
    public List<CreditAccountDetail> getAllCreditAccounts(String status) {
        if (status == null) {
            Query query = manager.createQuery("FROM CreditAccountDetail");
            return query.getResultList();
        } else {
            Query query = manager.createQuery("FROM CreditAccountDetail where accountStatus = :accountStatus");
            query.setParameter("accountStatus", status);
            return query.getResultList();

        }
    }

    @SuppressWarnings("unchecked")
    public List<ItemConsumptionEstimate> getAllActiveItemConsumptionEstimate() {
        Query query = manager.createQuery("FROM ItemConsumptionEstimate where itemStatus = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<ItemConsumptionEstimate> getAllActiveItemConsumptionEstimate(UnitWiseDayOfWeekWiseItemEstimateRequest request) {
        Query query = manager.createQuery("FROM ItemConsumptionEstimate where unitId = :unitId and dayOfWeek in (:dayOfWeeks) and itemStatus = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        query.setParameter("unitId", request.getUnitId());
        query.setParameter("dayOfWeeks", request.getDaysOfWeek());
        return query.getResultList();
    }

    @Override
    public void updateConsumptionEstimates(Date businessDate) {
        Query query = manager.createNativeQuery("CALL ITEM_CONSUMPTION_ESTIMATE_CALCULATE(:businessDate)");
        query.setParameter("businessDate", businessDate);
        query.executeUpdate();
    }

    @Override
    public void updateSuggestiveConsumptionEstimates(Date businessDate, int unitId) {
        LOG.info("Day Close :" + businessDate + " for :" + unitId);
        Query query = manager.createNativeQuery("CALL DAY_CLOSE_ESTIMATE_DATA_PROC(:unitId,:businessDate,:maxTaxAbleAmount)");
        query.setParameter("businessDate", businessDate);
        query.setParameter("unitId", unitId);
        query.setParameter("maxTaxAbleAmount", props.getMaxTaxableAmount());
        query.executeUpdate();
    }

    @Override
    public void updateUptsForPreviousDate(Date businessDate, int unitId) {
        Query query = manager.createNativeQuery("CALL DAY_CLOSE_ESTIMATE_DATA_PROC(:unitId,:businessDate,:maxTaxAbleAmount)");
        query.setParameter("businessDate", businessDate);
        query.setParameter("unitId", unitId);
        query.setParameter("maxTaxAbleAmount", props.getMaxTaxableAmount());
        query.executeUpdate();
    }


	@Override
	public UnitWiseDayOfWeekWiseItemEstimate getConsumptionEstimates(UnitWiseDayOfWeekWiseItemEstimateRequest request) {
		UnitWiseDayOfWeekWiseItemEstimate estimates = new UnitWiseDayOfWeekWiseItemEstimate();
		estimates.setUnitId(request.getUnitId());
		List<ItemConsumptionEstimate> list = getAllActiveItemConsumptionEstimate(request);
		if (list != null) {
			for (ItemConsumptionEstimate estimate : list) {
				if (!estimates.getEstimates().containsKey(estimate.getDayOfWeek())) {
					estimates.getEstimates().put(estimate.getDayOfWeek(), new HashMap<>());
				}
				if (!estimates.getEstimates().get(estimate.getDayOfWeek()).containsKey(estimate.getProductId())) {
					estimates.getEstimates().get(estimate.getDayOfWeek()).put(estimate.getProductId(), new HashMap<>());
				}
				UnitDayWiseItemKey key = new UnitDayWiseItemKey(estimate.getUnitId(), estimate.getDayOfWeek(),
						estimate.getProductId(), estimate.getDimension());
				estimates.getEstimates().get(estimate.getDayOfWeek()).get(estimate.getProductId()).put(
						estimate.getDimension(),
						new UnitDayWiseItemData(key, estimate.getQuantity(), estimate.getQuantityDineIn(),
								estimate.getQuantityDelivery(), estimate.getQuantityTakeaway()));

			}
		}
		return estimates;
	}

    @Override
    public CrmAppScreenDetail markPreviousCrmScreenInactive(CrmAppScreenDetail detail) {

        Query query = manager.createQuery("FROM CrmAppScreenDetail WHERE status = :status AND cityType = :cityType AND unitId =:unitId ");

        query.setParameter("status", AppConstants.ACTIVE);
        query.setParameter("cityType", detail.getCityType());
        query.setParameter("unitId", detail.getUnitId());
//        query.setParameter("contentType",detail.getContentType());

        List<CrmAppScreenDetail> crmScreenDetail = query.getResultList();
        if (crmScreenDetail == null || crmScreenDetail.size() == 0) {
            crmScreenDetail = new ArrayList<>();
        }
        for (CrmAppScreenDetail crm : crmScreenDetail) {
            crm.setStatus("INACTIVE");
            manager.flush();
        }
        return detail;

    }

    @Override
    public List<CrmAppScreenDetail> getCrmScreenUrl(String data) throws DataNotFoundException {
        Query query = manager.createQuery("From CrmAppScreenDetail c where city = :city and status = :status");
        query.setParameter("city", data);
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public UnitExpenditureAggregateDetail getPnLMap(Integer unitId) throws DataNotFoundException, NoResultException {
        Query query = manager.createQuery("From UnitExpenditureAggregateDetail where businessDate = :businessDate and unitId = :unitId and calculation = :calculation");
        query.setParameter("businessDate", AppUtils.getPreviousDate());
        query.setParameter("unitId", unitId);
        query.setParameter("calculation", "MTD");
        try {
            UnitExpenditureAggregateDetail result = (UnitExpenditureAggregateDetail) query.getSingleResult();
            return result;
        } catch (NoResultException e) {
            return new UnitExpenditureAggregateDetail();
        }
    }

    @Override
    public List<UnitExpenditureDetail> getAllPnlListForClosureId(Integer dayClosureId) {
        List<UnitExpenditureDetail> unitExpenditureDetails = new ArrayList<>();
        Query query = manager.createQuery("From UnitExpenditureDetail where dayClosureId =:dayClosureId");
        query.setParameter("dayClosureId", dayClosureId);
        try {
            unitExpenditureDetails = query.getResultList();
            return unitExpenditureDetails;
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public boolean resetMeterReading(Integer unitId) {
        if (unitId != null) {
            Query query = manager.createQuery("UPDATE MeterDetailsData SET status = : newStatus WHERE unitId = :unitId AND status = :oldStatus");
            query.setParameter("unitId", unitId);
            query.setParameter("oldStatus", "ACTIVE");
            query.setParameter("newStatus", "IN_ACTIVE");
            return query.executeUpdate() > 0;
        }
        return false;
    }

    @Override
    public List<CrmAppScreenDetail> getCrmScreenUrlForUnit(Integer unitId) {
        try {
            Query query = manager.createQuery("FROM CrmAppScreenDetail WHERE unitId = :unitId AND status = :status");
            query.setParameter("unitId", unitId);
            query.setParameter("status", AppConstants.ACTIVE);
            return  query.getResultList();
        } catch (Exception e) {
            LOG.error("Exception Found while fetching Url ", e);
            return null;
        }
    }

    @Override
    public List<CrmAppScreenDetail> getCrmScreenUrlForUnitV1(Integer unitId) {
        try {
            Query query = manager.createQuery("FROM CrmAppScreenDetail WHERE unitId = :unitId AND status = :status");
            query.setParameter("unitId", unitId);
            query.setParameter("status", AppConstants.ACTIVE);
            return  query.getResultList();
        } catch (Exception e) {
            LOG.error("Exception Found while fetching Url ", e);
            return null;
        }
    }

    @Override
    public List<CrmAppScreenDetail> getCrmAppScreenDetail(String region) {
        try {
            if (region.equals("ALL")) {
                Query query = manager.createQuery("FROM CrmAppScreenDetail");
                return query.getResultList();

            } else {
                Query query = manager.createQuery("FROM CrmAppScreenDetail WHERE city= :region");
                query.setParameter("region", region);
                return query.getResultList();
            }
        } catch (Exception e) {
            LOG.info("Exception Caught", e);
            return null;
        }
    }

    @Override
    public boolean cloneCrmAppScreenDetail(IdCodeName detail) {
        try {
            Query query = manager.createQuery("FROM CrmAppScreenDetail WHERE unitId= :unitId and status= :status");
            query.setParameter("unitId", Integer.parseInt(detail.getCode()));
            query.setParameter("status", AppConstants.ACTIVE);
            List<CrmAppScreenDetail> crmAppScreenDetails = query.getResultList();
            LOG.info("size of original Unit data is : {}",crmAppScreenDetails.size());
            Query query2 = manager.createQuery("FROM CrmAppScreenDetail WHERE unitId= :unitId and status= :status");
            query2.setParameter("unitId", detail.getId());
            query2.setParameter("status", AppConstants.ACTIVE);
            List<CrmAppScreenDetail> cloneCRMAppScreenDetails = query2.getResultList();
            LOG.info("size of Clone Unit data is : {}",cloneCRMAppScreenDetails.size());
            if(cloneCRMAppScreenDetails.size() == 0){
                return  false;
            }
            List<CrmAppScreenDetail> newAppScreenDetails = new ArrayList<>();
            if(crmAppScreenDetails.size() == 0) {
                for (CrmAppScreenDetail data : cloneCRMAppScreenDetails) {
                    CrmAppScreenDetail screenDetail = new CrmAppScreenDetail(data);
                    screenDetail.setUnitId(Integer.parseInt(detail.getCode()));
                    screenDetail.setCity(detail.getType());
                    screenDetail.setCityType(detail.getType()+"_"+data.getScreenType());
                    screenDetail.setUnitName(detail.getName());
                    newAppScreenDetails.add(screenDetail);
                    manager.persist(screenDetail);
                }
                newAppScreenDetails = addAll(newAppScreenDetails);
                return newAppScreenDetails != null && !newAppScreenDetails.isEmpty();
            }
            else{
                LOG.info("There are some ACTIVE entries .. INACTIVATING All");
                for (CrmAppScreenDetail crmAppScreenDetail : crmAppScreenDetails) {
                    crmAppScreenDetail.setStatus("INACTIVE");
                    manager.persist(crmAppScreenDetail);
                }
                for (CrmAppScreenDetail data : cloneCRMAppScreenDetails) {
                    CrmAppScreenDetail screenDetail = new CrmAppScreenDetail(data);
                    screenDetail.setUnitId(Integer.parseInt(detail.getCode()));
                    screenDetail.setCity(detail.getType());
                    screenDetail.setCityType(detail.getType()+"_"+data.getScreenType());
                    screenDetail.setUnitName(detail.getName());
                    newAppScreenDetails.add(screenDetail);
                    manager.persist(screenDetail);
                }
                newAppScreenDetails = addAll(newAppScreenDetails);
                return newAppScreenDetails != null && !newAppScreenDetails.isEmpty();
            }
        }
        catch (Exception e) {
            LOG.info("Exception Caught while Cloning CrmAppScreenDetail ::: ", e);
            return false;
        }
    }

    @Override
    public void createPreviousDateStockOutEntry(Date previousDate) {
        Map<Integer, List<Date>> unitTimeMap = getUnitClosingTimeMap(AppUtils.getPreviousDate());
        for (UnitBasicDetail unit : masterDataCache.getAllUnits()) {
            if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
                    && UnitCategory.CAFE.equals(unit.getCategory())) {
                UnitHours unitHourDetail = masterDataCache.getUnit(unit.getId()).getOperationalHours()
                        .get(AppUtils.getWeekDayNumber(AppUtils.getCurrentDate()));
                Time openingTime = leastOpeningTime(leastOpeningTime(unitHourDetail.getDineInOpeningTime(), unitHourDetail.getDeliveryOpeningTime()),
                        unitHourDetail.getTakeAwayOpeningTime());                  
                Date openingDate = convert(openingTime.toLocalTime());
                Query query = manager.createQuery("FROM UnitProductStockEventDataDetail WHERE unitId = :unitId  and eventTimeStamp between :openingTime and :closingTime " +
                        "ORDER BY eventTimeStamp");
                query.setParameter("unitId", unit.getId());
                query.setParameter("openingTime", AppUtils.getStartOfBusinessDay(previousDate));
                query.setParameter("closingTime", AppUtils.getStartOfBusinessDay(AppUtils.getDayBeforeOrAfterDay(previousDate, 1)));

                List<UnitProductStockEventDataDetail> result = query.getResultList();

                List<UnitProductStockEventDataDetail> processData = processStockOutData(result);
                for (UnitProductStockEventDataDetail data : processData) {
                    UnitProductStockEventDataDetail resultant = new UnitProductStockEventDataDetail();
                    resultant.setEventTimeStamp(openingDate);
                    resultant.setProductId(data.getProductId());
                    resultant.setProductName(data.getProductName());
                    resultant.setUnitId(data.getUnitId());
                    resultant.setUnitName(data.getUnitName());
                    resultant.setStatus(data.getStatus());
                    resultant.setDimension(data.getDimension());
                    resultant.setEventType(data.getEventType());
                    resultant.setBrandId(data.getBrandId());
                    manager.persist(resultant);
                }
            }
        }
        manager.flush();
    }

    private List<UnitProductStockEventDataDetail> processStockOutData(List<UnitProductStockEventDataDetail> result) {
        Map<IdCodeName, UnitProductStockEventDataDetail> map = new HashMap<>();
        for (UnitProductStockEventDataDetail data : result) {
            if (!map.containsKey(new IdCodeName(data.getProductId(), data.getDimension(), data.getStatus()))) {
                if (data.getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)) {
                    map.put(new IdCodeName(data.getProductId(), data.getDimension(), data.getStatus()), data);
                }
            } else {
                if (data.getStatus().equals(AppConstants.STOCK_IN_CONSTANT)) {
                    map.remove(new IdCodeName(data.getProductId(), data.getDimension(), AppConstants.STOCK_OUT_CONSTANT));
                }
            }
        }
        List<UnitProductStockEventDataDetail> output = new ArrayList<>();
        for (IdCodeName val : map.keySet()) {
            output.add(map.get(val));
        }
        return output;
    }

    @Override
    public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate) {
        try{
            LOG.info("Getting Unit Timing");
            Query query = manager.createNativeQuery("SELECT OD.UNIT_ID,UCD.BUSINESS_DATE, OD.BILLING_SERVER_TIME " +
                    "FROM UNIT_CLOSURE_DETAILS UCD INNER JOIN ORDER_DETAIL OD ON OD.ORDER_ID = UCD.LAST_ORDER_ID " +
                    "WHERE UCD.BUSINESS_DATE = :startDate AND UCD.CURRENT_STATUS = :status");
            query.setParameter("startDate", AppUtils.getSQLFormattedDate(previousDate)).setParameter("status", AppConstants.INITIATED);
            Map<Integer, List<Date>> map = new HashMap<>();
            List<Object[]> result = query.getResultList();
            for (Object[] obj : result) {
                try {
                    UnitHours unitHourDetail = masterDataCache.getUnit((Integer) obj[0]).getOperationalHours()
                            .get(AppUtils.getWeekDayNumber((Date) obj[1])-1);
                    Date openingDate = leastOpeningTime(leastOpeningTime(unitHourDetail.getDineInOpeningTime(), unitHourDetail.getDeliveryOpeningTime()),
                            unitHourDetail.getTakeAwayOpeningTime());
                    if(openingDate==null){
                        continue;
                    }
                    List<Date> time = new ArrayList<>();
                    time.add(AppUtils.setTimeToDate(previousDate, openingDate));
                    time.add((Date) obj[2]);
                    map.put((Integer) obj[0], time);
                }catch (Exception e){
                    LOG.error("Exception Caught :::{}",(Integer) obj[0],e);
                }
            }
            return map;
        } catch (Exception e){
            LOG.error("Exception Caught!!:::",e);
            return null;
        }
    }

    public static Date leastOpeningTime(Date a, Date b) {
        if(a == null && b == null){
            return null;
        }
        else if(a == null){
            return b;
        }
        else if(b == null){
            return a;
        }
        else {
            return (a.before(b) ? a : b);
        }
    }
    
    public static Time leastOpeningTime(Time a, Time b) {
        if(a == null && b == null){
            return null;
        }
        else if(a == null){
            return b;
        }
        else if(b == null){
            return a;
        }
        else {
            return (a.before(b) ? a : b);
        }
    }
    
	public static Date convert(LocalTime time) {
		Calendar calendar = AppUtils.getCalender();
		if (Objects.nonNull(time)) {
			calendar.set(Calendar.HOUR_OF_DAY, time.getHour());
			calendar.set(Calendar.MINUTE, time.getMinute());
			calendar.set(Calendar.SECOND, time.getSecond());
		}
		return calendar.getTime();
	}

    @Override
    public List<PullDetailReasons> getPullDetailReasons(){
        Query query = manager.createQuery("FROM PullDetailReasons");
        try {
            return query.getResultList();
        }
        catch (Exception e){
            return new ArrayList<>();
        }
    }

    @Override
    public List<UnitPullDetail> getPendingUnitPullDetails(int unitId){
        List<String> statusList = new ArrayList<>();
        statusList.add("INITIATED");
        statusList.add("CREATED");
        int paymentModeId=1;
        Query query = manager.createQuery("FROM PullDetail  p WHERE p.paymentModeId= :paymentModeId AND p.unitId= :unitId AND p.status IN(:statusList)");
        query.setParameter("paymentModeId",paymentModeId);
        query.setParameter("unitId",unitId);
        query.setParameter("statusList",statusList);
        try {
            List<PullDetail> results = query.getResultList();
            List<UnitPullDetail> res = new ArrayList<>();
            results.forEach(pullDetail -> {
                UnitPullDetail obj = new UnitPullDetail();
                obj.setId(pullDetail.getId());
                obj.setCreatedBy(pullDetail.getCreatedBy());
                obj.setWitnessedBy(pullDetail.getWitnessedBy());
                obj.setPaymentModeId(pullDetail.getPaymentModeId());
                obj.setPullAmount(pullDetail.getPullAmount());
                obj.setStatus(pullDetail.getStatus());
                obj.setComment(pullDetail.getComment());
                obj.setPendingReason(pullDetail.getPendingReason());
                obj.setSource(pullDetail.getSource());
                obj.setUnitId(pullDetail.getUnitId());
                res.add(obj);
            });
            return res;
        }
        catch (Exception e){
            return new ArrayList<>();
        }
    }

    @Override
    public UnitClosureDetails getKettleDayClose(Integer unitId, Date previousDate){
        Query query = manager.createQuery(
                "FROM UnitClosureDetails E where E.unitId = :unitId and E.businessDate = :businessDate and E.currentStatus =:status");
        query.setParameter("unitId", unitId);
        query.setParameter("status", ClosureState.INITIATED.name());
        query.setParameter("businessDate", previousDate);
        List<UnitClosureDetails> unitClosures = query.getResultList();
        return Objects.nonNull(unitClosures) && unitClosures.size()>0 ? unitClosures.get(0) : null;
    }

    @Override
    public UnitClosureDetails getLastClosureDetail(int unitId){
        Query query = manager.createQuery(
                "FROM UnitClosureDetails E where E.unitId = :unitId and E.currentStatus =:status" +
                        " order by E.closureId DESC");
        query.setParameter("unitId", unitId);
        query.setParameter("status", ClosureState.INITIATED.name());
        query.setMaxResults(1);
        List<UnitClosureDetails> unitClosures = query.getResultList();
        return Objects.nonNull(unitClosures) && unitClosures.size()>0 ? unitClosures.get(0) : null;
    }

    @Override
    public Map<String, PartnerUnitProductStockData> getPreviousDayStockData(int unitId, Date businessDate) {
        Query query = manager.createQuery("FROM PartnerUnitProductStockData P where P.unitId =:unitId and P.businessDate = :businessDate order by P.key DESC");
        query.setParameter("unitId",unitId);
        query.setParameter("businessDate",businessDate);
        List<PartnerUnitProductStockData> partnerUnitProductStockDataList = query.getResultList();
        Map<String, PartnerUnitProductStockData> stockDataMap = new HashMap<>();
        for(PartnerUnitProductStockData pupsd : partnerUnitProductStockDataList){
            if(!stockDataMap.containsKey(pupsd.getProductId()+pupsd.getDimension())){
                stockDataMap.put(pupsd.getProductId()+pupsd.getDimension(), pupsd);
            }
        }
        return stockDataMap;
    }
}
