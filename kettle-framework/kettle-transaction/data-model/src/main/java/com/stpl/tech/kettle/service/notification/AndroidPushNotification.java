package com.stpl.tech.kettle.service.notification;

import com.stpl.tech.master.core.external.notification.FireStoreNotificationType;
import com.stpl.tech.master.core.external.notification.FirebaseNotification;

import java.util.HashMap;
import java.util.Map;

public class AndroidPushNotification implements FirebaseNotification {
    private Map<String, String> message;
    private String topic;
    private String sendToAndroid;
    public AndroidPushNotification(Integer keyId, Integer unitId, String changeType, String assemblyFirestoreUnits,
                                   String isAssemblyFirestoreEnabledForAll, FireStoreNotificationType fireStoreNotificationType){
        this.message = new HashMap<>();
        this.message.put("keyId",keyId.toString());
        this.message.put("unitId",unitId.toString());
        this.message.put("androidNotificationType",changeType);
        this.message.put("isAssemblyFirestoreEnabledForAll",isAssemblyFirestoreEnabledForAll);
        this.message.put("assemblyFirestoreUnits",assemblyFirestoreUnits);
        this.message.put("fireStoreNotificationType", fireStoreNotificationType.name());
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public Object getData() {
        return this.message;
    }

    @Override
    public String getTitle() {
        return "Notification Received";
    }

    @Override
    public String getMessage() {
        return "Notification Message Received";
    }

    @Override
    public String sendToAndroid() {
        return this.sendToAndroid;
    }

    public void setSendToAndroid(String sendToAndroid) {
        this.sendToAndroid = sendToAndroid;
    }
}
