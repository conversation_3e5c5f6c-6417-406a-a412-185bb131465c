package com.stpl.tech.kettle.stock.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.UnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventAggregate;
import com.stpl.tech.kettle.stock.dao.UnitProductStockEventAggregateDao;
import com.stpl.tech.util.AppUtils;

@Repository
public class UnitProductStockEventAggragateDaoImpl extends AbstractDaoImpl
		implements UnitProductStockEventAggregateDao {
	@Override
	public List<UnitProductStockEventAggregate> getStockEventArg(Integer unitId, Date calculationDate,
			Integer dayDiff) {
		deleteData(calculationDate, unitId);
		Query query = manager.createQuery(
				"FROM UnitProductStockData WHERE unitId = :unitId AND calculationDate = :calculationDate ");
		query.setParameter("unitId", unitId).setParameter("calculationDate", calculationDate);
		List<UnitProductStockData> results = query.getResultList();
		return getStockEventArg(results, dayDiff);
	}

	@Override
	public List<UnitProductStockEventAggregate> getStockEventArg(List<UnitProductStockData> results,
			Integer dayDiff) {
		Map<String, UnitProductStockEventAggregate> aggregates = new HashMap<String, UnitProductStockEventAggregate>();
		for (UnitProductStockData result : results) {
			add(aggregates, result, dayDiff+1);
		}
		return new ArrayList<>(aggregates.values());
	}

	private void add(Map<String, UnitProductStockEventAggregate> aggregates, UnitProductStockData dataEntry,
			Integer dayDiff) {
		String key = getKey(dataEntry);
		if (!aggregates.containsKey(key)) {
			UnitProductStockEventAggregate unitProductStockEventAggregate = new UnitProductStockEventAggregate(
					dataEntry.getBusinessDate(), dataEntry.getCalculationDate(), dataEntry.getCafeOpening(),
					dataEntry.getCafeClosing(), dataEntry.getUnitId(), dataEntry.getProductId(),
					dataEntry.getProductName(), dataEntry.getDownTime(),
					AppUtils.getMinDiffernce(dataEntry.getCafeOpening(), dataEntry.getCafeClosing()) * dayDiff, 1);
			aggregates.put(key, unitProductStockEventAggregate);
		} else {
			UnitProductStockEventAggregate aggregate = aggregates.get(key);
			aggregate.setInstance(aggregate.getInstance() + 1);
			aggregate.setDownTime(dataEntry.getDownTime() + aggregate.getDownTime());
			aggregate.setPercentage((aggregate.getDownTime() * 100.0f) / aggregate.getOperationTime());
		}
	}

	private String getKey(UnitProductStockData result) {
		return result.getUnitId() + "_" + result.getProductId();
	}

	List<UnitProductStockEventAggregate> calculateAggregate(List<UnitProductStockData> unitProductStockData,
			Integer dayDiff) {

		List<UnitProductStockEventAggregate> unitProductStockEventAggregates = new ArrayList<>();
		HashMap<Integer, HashMap<Integer, UnitProductStockEventAggregate>> mapAggregate = new HashMap<>();
		Integer instance = 1;
		for (UnitProductStockData dataEntry : unitProductStockData) {
			UnitProductStockEventAggregate unitProductStockEventAggregate = new UnitProductStockEventAggregate(
					dataEntry.getBusinessDate(), dataEntry.getCalculationDate(), dataEntry.getCafeOpening(),
					dataEntry.getCafeClosing(), dataEntry.getUnitId(), dataEntry.getProductId(),
					dataEntry.getProductName(), dataEntry.getDownTime(),
					AppUtils.getMinDiffernce(dataEntry.getCafeOpening(), dataEntry.getCafeClosing()) * dayDiff,
					instance);

			if (!mapAggregate.containsKey(dataEntry.getUnitId())) {
				mapAggregate.put(dataEntry.getUnitId(), new HashMap<>());
			}
			if (!mapAggregate.get(dataEntry.getUnitId()).containsKey(dataEntry.getProductId())) {
				mapAggregate.get(dataEntry.getUnitId()).put(dataEntry.getProductId(), unitProductStockEventAggregate);
			} else if (mapAggregate.get(dataEntry.getUnitId()).containsKey(dataEntry.getProductId())) {
				UnitProductStockEventAggregate aggregate = mapAggregate.get(dataEntry.getUnitId())
						.get(dataEntry.getProductId());
				aggregate.setInstance(aggregate.getInstance() + 1);
				aggregate.setDownTime(dataEntry.getDownTime() + aggregate.getDownTime());
				aggregate.setPercentage((aggregate.getDownTime() * 100.0f) / aggregate.getOperationTime());
				mapAggregate.get(dataEntry.getUnitId()).put(dataEntry.getProductId(), aggregate);
			}
		}
		for (Map.Entry<Integer, HashMap<Integer, UnitProductStockEventAggregate>> map1 : mapAggregate.entrySet()) {
			for (Map.Entry<Integer, UnitProductStockEventAggregate> map2 : map1.getValue().entrySet()) {
				unitProductStockEventAggregates.add(map2.getValue());
			}
		}
		return unitProductStockEventAggregates;
	}

	@Override
	public void deleteData(Date calculationDate, Integer unitId) {
		Query query = manager.createQuery(
				"DELETE FROM UnitProductStockEventAggregate U WHERE U.calculationDate = :calculationDate AND U.unitId=:unitId");
		query.setParameter("calculationDate", calculationDate);
		query.setParameter("unitId", unitId);
		query.executeUpdate();
		manager.flush();
	}
}