package com.stpl.tech.kettle.webengage.data.dao.impl;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderMetadataDetail;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.webengage.data.dao.WebEngageDataPushDao;
import com.stpl.tech.kettle.webengage.data.model.OrderDataPushTrack;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Repository
public class WebEngageDataPushDaoImpl extends AbstractDaoImpl implements WebEngageDataPushDao {

    @Override
    public List<CustomerInfo> getCustomerBatch(Integer startId, Integer batchSize){
        Query query = manager.createQuery("FROM CustomerInfo c WHERE c.customerId > :startId");
        query.setParameter("startId", startId);
        query.setMaxResults(batchSize);
        return query.getResultList();
    }

    @Override
    public List<LoyaltyScore> getCustomerLoyaltyScores(Set<Integer> customerIds){
        Query query = manager.createQuery("FROM LoyaltyScore c WHERE c.customerId in (:customerIds)");
        query.setParameter("customerIds", customerIds);
        return query.getResultList();
    }

    @Override
    public List<Integer> getSettledKettleOrderBatch(Integer startOrderId, Integer batchSize){
        Query query = manager.createQuery("SELECT o.orderId FROM OrderDetail o WHERE o.orderId > :startOrderId AND o.orderStatus != :cancelled AND o.orderStatus != :cancelledRequested order by o.orderId");
        query.setParameter("startOrderId", startOrderId);
        query.setParameter("cancelled", "CANCELLED");
        query.setParameter("cancelledRequested", "CANCELLED_REQUESTED");
        query.setMaxResults(batchSize);
        return query.getResultList();
    }


    @Override
    public List<OrderDataPushTrack> findLastOrderTrack() {
        Query query = manager.createQuery("FROM OrderDataPushTrack o ORDER BY o.id DESC");
        query.setMaxResults(1);
        return query.getResultList();
    }

    @Override
    public List<OrderDetail> findOrdersFromBizDate(Date bizDate){
        Query query = manager.createQuery("FROM OrderDetail o WHERE o.billingServerTime = :bizDate AND o.orderStatus != :cancelRequested AND o.orderStatus != :cancelled");
        query.setParameter("bizDate", bizDate);
        query.setParameter("cancelRequested", OrderStatus.CANCELLED_REQUESTED.value());
        query.setParameter("cancelled", OrderStatus.CANCELLED.value());
        return query.getResultList();
    }

    @Override
    public List<OrderMetadataDetail> getOrderMetadataDetail(Integer orderId){
        Query query = manager.createQuery("FROM OrderMetadataDetail o WHERE o.orderId = :orderId");
        query.setParameter("orderId", orderId);
        return query.getResultList();
    }
}
