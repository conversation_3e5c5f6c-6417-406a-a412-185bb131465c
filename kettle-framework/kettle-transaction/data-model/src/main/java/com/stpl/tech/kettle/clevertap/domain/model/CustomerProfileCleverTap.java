

package com.stpl.tech.kettle.clevertap.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class CustomerProfileCleverTap implements Serializable {

    private static final long serialVersionUID = 3207055473205009525L;

    @JsonProperty("Name")
    @SerializedName("Name")
    private String name;
    @JsonProperty("Email")
    @SerializedName("Email")
    private String email;
    @JsonProperty("Phone")
    @SerializedName("Phone")
    private String phone;
    @JsonProperty("CustomerId")
    @SerializedName("CustomerId")
    private Integer customerId;
    @JsonProperty("AddTime")
    @SerializedName("AddTime")
    private String addTime;
    @JsonProperty("LoyaltyRedeemedCount")
    @SerializedName("LoyaltyRedeemedCount")
    private Integer loyaltyRedeemedCount;
    @JsonProperty("CountryCode")
    @SerializedName("CountryCode")
    private String countryCode;
    @JsonProperty("IsNumberVerified")
    @SerializedName("IsNumberVerified")
    private String isNumberVerified;
    @JsonProperty("IsEmailVerified")
    @SerializedName("IsEmailVerified")
    private String isEmailVerified;
    @JsonProperty("IsBlacklisted")
    @SerializedName("IsBlacklisted")
    private String isBlacklisted;
    @JsonProperty("IsDnd")
    @SerializedName("IsDnd")
    private String isDnd;
    @JsonProperty("MSG_sms")
    @SerializedName("MSG_sms")
    private String msg_sms;
    @JsonProperty("MSG_whatsapp")
    @SerializedName("MSG_whatsapp")
    private String msg_whatsapp;
    @JsonProperty("GcBalance")
    @SerializedName("GcBalance")
    private BigDecimal gcBalance;
    @JsonProperty("ChyCashBalance")
    @SerializedName("ChyCashBalance")
    private BigDecimal chyCashBalance;
    @JsonProperty("LoyaltyPointsBalance")
    @SerializedName("LoyaltyPointsBalance")
    private Integer loyaltyPointsBalance;
    @JsonProperty("NboAvailableFlag")
    @SerializedName("NboAvailableFlag")
    private Integer nboAvailableFlag;
    @JsonProperty("DnboAvailableFlag")
    @SerializedName("DnboAvailableFlag")
    private Integer dnboAvailableFlag;
    @JsonProperty("SubscriptionActiveFlag")
    @SerializedName("SubscriptionActiveFlag")
    private Integer subscriptionActiveFlag;
    @JsonProperty("MSG-whatsapp")
    @SerializedName("MSG-whatsapp")
    private Boolean optInWhatsapp;
    @JsonProperty("MSG-sms")
    @SerializedName("MSG-sms")
    private Boolean optInSms;
    @JsonProperty("OrderCount")
    @SerializedName("OrderCount")
    private Integer orderCount;
    @JsonProperty("LoyalteaAvailable")
    @SerializedName("LoyalteaAvailable")
    private Integer loyalteaAvailable;
    @JsonProperty("LastUpdatedTime")
    @SerializedName("LastUpdatedTime")
    private String lastUpdatedTime;
    @JsonProperty("TotalSpent")
    @SerializedName("TotalSpent")
    private BigDecimal totalSpent;
    @JsonProperty("CommunicationName")
    @SerializedName("CommunicationName")
    private String communicationName;
    @JsonProperty("AcquisitionSource")
    @SerializedName("AcquisitionSource")
    private String AcquisitionSource;

    @JsonProperty("BirthdayDate")
    @SerializedName("BirthdayDate")
    private Integer Birthday;

    @JsonProperty("BirthdayMonth")
    @SerializedName("BirthdayMonth")
    private String BirthdayMonth;

    @JsonProperty("Anniversary")
    @SerializedName("Anniversary")
    private Integer Anniversary;

    @JsonProperty("AnniversaryMonth")
    @SerializedName("AnniversaryMonth")
    private String AnniversaryMonth;

    @JsonProperty("SignupOfferStatus")
    @SerializedName("SignupOfferStatus")
    private String signupOfferStatus;
    
    @JsonProperty("DOB")
	@SerializedName("DOB")
	private String dob;

    @JsonProperty("FiftyLoyaltyThreshold")
    @SerializedName("FiftyLoyaltyThreshold")
    private String fiftyLoyaltyThreshold;

    @JsonProperty("SignupOfferExpiryDate")
    @SerializedName("SignupOfferExpiryDate")
    private String signupOfferExpiryDate;
}
