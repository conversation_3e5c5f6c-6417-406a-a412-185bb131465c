/**
 * 
 */
package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class CustomerCardInfo {

	private boolean hasCard;
	private BigDecimal cardAmount;

	public CustomerCardInfo() {
		super();
	}

	public CustomerCardInfo(Object cardAmount) {
		super();
		this.hasCard = cardAmount != null;
		this.cardAmount = cardAmount == null ? BigDecimal.ZERO : (BigDecimal) cardAmount;
	}

	public boolean isHasCard() {
		return hasCard;
	}

	public void setHasCard(boolean hasCard) {
		this.hasCard = hasCard;
	}

	public BigDecimal getCardAmount() {
		return cardAmount;
	}

	public void setCardAmount(BigDecimal cardAmount) {
		this.cardAmount = cardAmount;
	}

}
