package com.stpl.tech.kettle.clm.dao;


import com.stpl.tech.kettle.clm.model.FavChai.CustomerFavChaiCustomizationDetail;
import com.stpl.tech.kettle.clm.model.FavChai.Key;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CustomerFavChaiIncrementalDataDao extends JpaRepository<CustomerFavChaiCustomizationDetail , Key> {
    @Query(value = "SELECT * FROM CUSTOMER_CHAI_CUSTOMIZATION_DETAIL c WHERE c.ADDED_TO_PROD = :addedToProd AND c.RECIPE_ID IS NOT NULL AND c.RECIPE_PROFILE IS NOT NULL AND c.DIMENSION IS NOT NULL LIMIT :limit ", nativeQuery = true)
    List<CustomerFavChaiCustomizationDetail> findByAddedToProdAndRecipeIdIsNotNull(@Param("addedToProd") String addedToProd, @Param("limit") int limit);



}

