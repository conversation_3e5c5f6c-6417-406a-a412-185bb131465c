/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.load;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExcelParser<T> {

	private RowMapper<T, Cell> mapper;

	public ExcelParser(RowMapper<T, Cell> mapper) {
		super();
		this.mapper = mapper;
	}

	public List<T> parseExcel(String filePath, int sheetNo, int skipRows, int skipColumns, int keyColumn)
			throws IOException {

		List<T> list = new ArrayList<>();
		FileInputStream inputStream = new FileInputStream(new File(filePath));

		Workbook workbook = new XSSFWorkbook(inputStream);
		Sheet firstSheet = workbook.getSheetAt(sheetNo);
		Iterator<Row> iterator = firstSheet.iterator();
		int rowCount = 0;
		while (iterator.hasNext()) {
			Row nextRow = iterator.next();
			rowCount++;
			if (rowCount <= skipRows) {
				continue;
			}
			Cell cell = nextRow.getCell(keyColumn);
			if (cell == null || cell.getCellType() == CellType.BLANK) {
				continue;
			}
			T object = mapper.createNewInstance();
			Iterator<Cell> cellIterator = nextRow.cellIterator();
			Cell nextCell = null;
			int coulmnCount = 0;
			while (cellIterator.hasNext()) {
				nextCell = cellIterator.next();
				coulmnCount++;
				if (coulmnCount <= skipColumns) {
					continue;
				}
				mapper.setData(object, nextCell);
			}
			list.add(object);
		}
		workbook.close();
		inputStream.close();
		return list;
	}

	public boolean hasErrors() {
		return mapper.getErrors() != null && mapper.getErrors().size() > 0;
	}

	public List<String> getErrors() {
		return mapper.getErrors();
	}
}
