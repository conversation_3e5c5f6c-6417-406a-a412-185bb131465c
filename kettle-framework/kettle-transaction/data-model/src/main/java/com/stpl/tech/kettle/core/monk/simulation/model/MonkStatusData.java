/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.model;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class MonkStatusData implements Comparable<MonkStatusData> {

	public int monkId;
	public Date freeTime;
	public String status;

	public MonkStatusData(int monkId, String status) {
		super();
		this.monkId = monkId;
		this.status = status;
		freeTime = new Date(1, 1, 1, 1, 1);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Comparable#compareTo(java.lang.Object)
	 */
	@Override
	public int compareTo(MonkStatusData o) {
		return this.freeTime.compareTo(o.freeTime);
	}

}
