package com.stpl.tech.kettle.core.data.vo;

public class CustomerFeedbackData {
	Integer customerId;
	Integer totalFeedback;
	String feedbackCategory;
	Integer lastFeedback;
	Integer secondFeedback;
	Integer thirdFeedback;
	String result;
	String isLastFeedbackOfLastOrder;

	public String getLastFeedbackOfLastOrder() {
		return isLastFeedbackOfLastOrder;
	}

	public void setLastFeedbackOfLastOrder(String lastFeedbackOfLastOrder) {
		isLastFeedbackOfLastOrder = lastFeedbackOfLastOrder;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getTotalFeedback() {
		return totalFeedback;
	}

	public void setTotalFeedback(Integer totalFeedback) {
		this.totalFeedback = totalFeedback;
	}

	public String getFeedbackCategory() {
		return feedbackCategory;
	}

	public void setFeedbackCategory(String feedbackCategory) {
		this.feedbackCategory = feedbackCategory;
	}

	public Integer getLastFeedback() {
		return lastFeedback;
	}

	public void setLastFeedback(Integer lastFeedback) {
		this.lastFeedback = lastFeedback;
	}

	public Integer getSecondFeedback() {
		return secondFeedback;
	}

	public void setSecondFeedback(Integer secondFeedback) {
		this.secondFeedback = secondFeedback;
	}

	public Integer getThirdFeedback() {
		return thirdFeedback;
	}

	public void setThirdFeedback(Integer thirdFeedback) {
		this.thirdFeedback = thirdFeedback;
	}

	public String getIsLastFeedbackOfLastOrder() {
		return isLastFeedbackOfLastOrder;
	}

	public void setIsLastFeedbackOfLastOrder(String isLastFeedbackOfLastOrder) {
		this.isLastFeedbackOfLastOrder = isLastFeedbackOfLastOrder;
	}

}
