package com.stpl.tech.kettle.data.dao.impl;


import com.stpl.tech.kettle.data.model.DroolsDecisionTableData;
import com.stpl.tech.kettle.data.dao.DroolsDecisionDao;
import com.stpl.tech.master.core.external.offer.dao.impl.OfferManagementDaoImpl;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;


@Repository
@Slf4j
public class DroolsDecisionDaoImpl extends AbstractDaoImpl implements DroolsDecisionDao {

    @Override
    public DroolsDecisionTableData getDecisionTableData(String type) {
        try {
            Query query =  manager.createQuery("FROM DroolsDecisionTableData d where d.type = :type order by d.creationTime desc, d.id desc");
            query.setParameter("type",type);
            query.setMaxResults(1);
            return (DroolsDecisionTableData) query.getSingleResult();
        }catch (NoResultException e){
            log.error("No decision table data found for type : {} and status :{}", type);
        }catch (Exception e){
            log.error("Exception occurred while fetching decision table data for type : {}", type);
        }
        return null;
    }

    @Override
    public void deactivateOther(String type) {
        try {
            Query query =  manager.createQuery("UPDATE DroolsDecisionTableData d SET d.status = :inActive where d.type = :type and d.status = :active");
            query.setParameter("type",type);
            query.setParameter("active", AppConstants.ACTIVE);
            query.setParameter("inActive", AppConstants.IN_ACTIVE);
            query.setMaxResults(1);
            query.executeUpdate();
        }catch (Exception e){
            log.error("Exception occurred while deactivating other decision table data for type : {} ", type);
            log.error("Exception in deactivating",e);
        }
    }

    @Override
    public void activateCurrentFile(String type, String fileName) {
        try {
            Query query =  manager.createQuery("UPDATE DroolsDecisionTableData d SET d.status = :active where d.type = :type and d.fileName = :fileName");
            query.setParameter("type",type);
            query.setParameter("active", AppConstants.ACTIVE);
            query.setParameter("fileName", fileName);
            query.setMaxResults(1);
            query.executeUpdate();
        }catch (Exception e){
            log.error("Exception occurred while activating decision table data for type : {} and fileName : {} ", type,fileName);
            log.error("Exception in activating",e);
        }
    }

    @Override
    public List<DroolsDecisionTableData> fetchAllFileByType(String fileType) {
        try {
            Query query =  manager.createQuery("FROM DroolsDecisionTableData d where d.type = :type and d.status <> :status order by d.id desc");
            query.setParameter("type",fileType);
            query.setParameter("status",AppConstants.DECLINE);
            return query.getResultList();
        }catch (NoResultException e){
            log.error("No decision table data found for type : {}", fileType);
        }catch (Exception e){
            log.error("Exception occurred while fetching decision table data for type : {} ", fileType);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DroolsDecisionTableData> getDecisionTableDataByStatus(String type){
        try {
            List<String> status = Arrays.asList(AppConstants.ACTIVE, AppConstants.PROCESSING);
            Query query =  manager.createQuery("FROM DroolsDecisionTableData d where d.type = :type and d.status in (:status) order by d.creationTime desc");
            query.setParameter("type",type);
            query.setParameter("status",status);
            return query.getResultList();
        }catch (NoResultException e){
            log.error("No decision table data found for type : {} and status :{}", type);
        }catch (Exception e){
            log.error("Exception occurred while fetching decision table data for type : {}", type);
        }
        return null;
    }

    @Override
    public void declineExistingProcessingFile(String type,String filename){
        try {
            Query query =  manager.createQuery("UPDATE DroolsDecisionTableData d SET d.status = :decline where d.type = :type and d.status = :processing and d.fileName <> :fileName");
            query.setParameter("type",type);
            query.setParameter("fileName" ,filename);
            query.setParameter("processing", AppConstants.PROCESSING);
            query.setParameter("decline", AppConstants.DECLINE);
            query.setMaxResults(1);
            query.executeUpdate();
        }catch (Exception e){
            log.error("Exception occurred while deactivating other decision table data for type : {} ", type);
            log.error("Exception in declining",e);
        }
    }

    @Override
    public void findByTypeAndVersionAndSetStatusAndIsDefault(String type, String version, String status, String isDefault){
        try {
            Query query =  manager.createQuery("UPDATE DroolsDecisionTableData d SET d.status= :status,d.isDefault = :default where d.type = :type and d.version =:version");
            query.setParameter("type",type);
            query.setParameter("version" ,version);
            query.setParameter("status",status);
            query.setParameter("default",isDefault);
            query.executeUpdate();
        }catch (Exception e){
            log.error("Exception while getting drool file data ::::::: {}",e);
        }
    }

    @Override
    public void findByTypeAndVersionAndSetIsDefault(String type, String version, String isDefault){
        try {
            Query query =  manager.createQuery("UPDATE DroolsDecisionTableData d SET d.isDefault = :isDefault where d.type = :type and d.status = :status");
            query.setParameter("type",type);
            query.setParameter("status" ,AppConstants.ACTIVE);
            query.setParameter("isDefault",AppConstants.NO);
            query.executeUpdate();

            Query droolquery =  manager.createQuery("UPDATE DroolsDecisionTableData d SET d.isDefault = :isDefault where d.type = :type and d.version =:version");
            droolquery.setParameter("type",type);
            droolquery.setParameter("version" ,version);
            droolquery.setParameter("isDefault",isDefault);
            droolquery.executeUpdate();
        }catch (Exception e){
            log.error("Exception while getting drool file data ::::::: {}",e);
        }
    }
}
