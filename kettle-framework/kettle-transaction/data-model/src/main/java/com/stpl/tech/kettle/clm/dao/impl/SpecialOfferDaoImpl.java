package com.stpl.tech.kettle.clm.dao.impl;

import com.stpl.tech.kettle.clm.dao.SpecialOfferDao;
import com.stpl.tech.util.AppConstants;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class SpecialOfferDaoImpl extends CLMDataAbstractDaoImpl implements SpecialOfferDao {

    private static final Logger LOG = LoggerFactory.getLogger(SpecialOfferDaoImpl.class);

	@Override
	public List<String> getOfferString(Integer brandId, Integer customerId, String utmSource, String previousUtmSource,
			String utmMedium, String previousUtmMedium, boolean hasSubscription) {
		try {
			Query query = manager.createNativeQuery(
					"CALL GET_SPECIAL_OFFER_STRING_UPDATED(:brandId,:customerId,:utmSource,:utmPreviousSource,"
							+ " :utmMedium, :utmPreviousMedium, :subscriptionFlag)");
			query.setParameter("brandId", brandId);
			query.setParameter("customerId", customerId);
			query.setParameter("utmSource", utmSource);
			query.setParameter("utmPreviousSource", previousUtmSource);
			query.setParameter("utmMedium", utmMedium);
			query.setParameter("utmPreviousMedium", previousUtmMedium);
			query.setParameter("subscriptionFlag", AppConstants.getValue(hasSubscription));
			List<Object[]> objs = query.getResultList();
			Map<Integer, List<String>> offerMap = new HashMap<>();
			List<String> offerStrings = new ArrayList<>();
			offerStrings.add((String) objs.get(0)[1]);
			offerStrings.add((String) objs.get(0)[2]);
			return offerStrings;
		} catch (Exception e) {
			LOG.error("Error while getting offer string for contact : {}", customerId, e);
		}
		return null;
	}
}
