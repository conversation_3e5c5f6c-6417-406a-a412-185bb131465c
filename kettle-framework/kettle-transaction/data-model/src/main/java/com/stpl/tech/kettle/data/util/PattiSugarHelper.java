/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.util;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;

public class PattiSugarHelper {
    public static String getPattiText(OrderItem itemDto, String consumptionHelperProfile) {
        if (Objects.nonNull(itemDto.getComposition()) && !CollectionUtils.isEmpty(itemDto.getComposition().getVariants())) {
            if (DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile).getProducts().contains(itemDto.getProductId())) {
                for (IngredientVariantDetail variantDetail : itemDto.getComposition().getVariants()) {
                    String codeString = variantDetail.getAlias();
                    if (Objects.nonNull(codeString))
                        if (codeString.toLowerCase().contains(DesiChaiConsumptionHelper.KADAK_LOWER_CASE)) {
                            return AppConstants.K;
                        } else if (codeString.equalsIgnoreCase(DesiChaiConsumptionHelper.REGULAR_PATTI)) {
                            return AppConstants.R;
                        }
                }
                return AppConstants.R;
            }
        }
        return "";
    }


    public static String getSugarText(OrderItem orderItem, String consumptionHelperProfile) {
        if (Objects.nonNull(orderItem.getComposition()) && !CollectionUtils.isEmpty(orderItem.getComposition().getVariants())) {
            for (IngredientVariantDetail variantDetail : orderItem.getComposition().getVariants()) {
                String codeString = variantDetail.getAlias();
                if (codeString.toLowerCase().contains(DesiChaiConsumptionHelper.SUGAR_LOWER_CASE)) {
                    if (!codeString.toLowerCase().contains(DesiChaiConsumptionHelper.REGULAR_LOWER_CASE) && variantDetail.getQuantity() != null && variantDetail.getQuantity().longValue() == 0L) {
                        return AppConstants.NS;
                    } else {
                        return AppConstants.R;
                    }
                }
            }

            if (DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile).getProducts().contains(orderItem.getProductId())) {
                return "R";
            }
        }
        return "";
    }
}
