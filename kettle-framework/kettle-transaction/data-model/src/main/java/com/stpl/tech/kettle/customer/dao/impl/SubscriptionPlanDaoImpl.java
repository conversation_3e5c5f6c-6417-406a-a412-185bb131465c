package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.kettle.core.SubscriptionEventType;
import com.stpl.tech.kettle.core.data.vo.SubscriptionRequest;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.data.model.SubscriptionPlanEvent;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.FrequencyOfferType;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class SubscriptionPlanDaoImpl extends AbstractDaoImpl implements SubscriptionPlanDao {

	private static final Logger LOG = LoggerFactory.getLogger(SubscriptionPlanDaoImpl.class);

	@Override
	public SubscriptionPlan addSubscription(SubscriptionRequest request, String source, Integer campaignId) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		SubscriptionPlan plan = new SubscriptionPlan();
		plan.setCustomerId(request.getCustomerId());
		setPlan(request, plan, currentTime);
		manager.persist(plan);
		Date startDate = plan.getPlanStartDate();
		SubscriptionPlanEvent event = createEvent(request, currentTime, plan.getSubscriptionPlanId(), startDate, source, campaignId);
		plan.setLastRenewalEventId(event.getSubscriptionPlanEventId());
		manager.flush();
		return plan;
	}

	@Override
	public SubscriptionPlan getActiveSubscription(Integer customerId) {
		try {
			Query query = manager.createQuery("From SubscriptionPlan l where l.customerId = :customerId and l.status = :active");
			query.setParameter("customerId", customerId);
			query.setParameter("active", AppConstants.ACTIVE);
			SubscriptionPlan score = (SubscriptionPlan) query.getSingleResult();
			return score;
		} catch (NoResultException e) {
			LOG.info(String.format("No Subscription Found For Customer with ID : %d", customerId));
			return null;
		}
	}

	@Override
	public SubscriptionPlan getActiveSubscription(Integer customerId, String code) {
		try {
			Query query = manager.createQuery("From SubscriptionPlan l where l.customerId = :customerId and l.status = :active and l.subscriptionPlanCode = :code");
			query.setParameter("customerId", customerId);
			query.setParameter("active", AppConstants.ACTIVE);
			query.setParameter("code", code);
			SubscriptionPlan score = (SubscriptionPlan) query.getSingleResult();
			return score;
		} catch (NoResultException e) {
			LOG.info(String.format("No Subscription Found For Customer with ID : %d", customerId));
			return null;
		}
	}

	@Override
	public SubscriptionPlanEvent getActiveSubscriptionEvent(SubscriptionPlan plan){
		try {
			Query query = manager.createQuery("FROM SubscriptionPlanEvent S where S.customerId =:customerId and S.subscriptionPlanId = :subscriptionPlanId " +
					"and S.planStartDate <= :currentDate and S.planEndDate >= :currentDate and status = :status");
			query.setParameter("currentDate",(AppUtils.getCurrentDate()));
			query.setParameter("customerId",plan.getCustomerId());
			query.setParameter("status",AppConstants.ACTIVE);
			query.setParameter("subscriptionPlanId",plan.getSubscriptionPlanId());

			return (SubscriptionPlanEvent) query.getSingleResult();
		}
		catch (Exception e){
			LOG.error("Error in fetching data");
			return null;
		}
	}

	@Override
	public SubscriptionPlan getLastSubscriptionPlanForCustomer(Integer customerId,String code){
		try {
			Query query = manager.createQuery("FROM SubscriptionPlan S where S.customerId =:customerId " +
					"and S.status IN :status and S.subscriptionPlanCode =:code ");
			query.setParameter("customerId",customerId);
			query.setParameter("status", Arrays.asList(AppConstants.ACTIVE,AppConstants.IN_ACTIVE));
			query.setParameter("code",code);
			return (SubscriptionPlan) query.getSingleResult();
		}
		catch (NoResultException e){
			LOG.error("No result found for customerId :{}", customerId, e);
			return null;
		}
	}

	@Override
	public List<SubscriptionPlan> getAllSubscriptionPlanForCustomer(int customerId) {
		try{
			Query query = manager.createQuery("From SubscriptionPlan l where l.customerId = :customerId and l.eventType <> :cancelSubscription order by l.subscriptionPlanId desc");
			query.setParameter("customerId", customerId);
			query.setParameter("cancelSubscription", SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
			List<SubscriptionPlan> subscriptionPlanList= (List<SubscriptionPlan>)query.getResultList();
			return subscriptionPlanList;
		}catch(NoResultException e){
			LOG.error("No subscription found for customer with id ::{}",customerId,e);
			return null;
		}
	}

	@Override
	public SubscriptionPlan getSubscription(Integer customerId) {
		try {
			Query query = manager.createQuery("From SubscriptionPlan l where l.customerId = :customerId and l.eventType <> :cancelSubscription order by l.subscriptionPlanId desc");
			query.setParameter("customerId", customerId);
			query.setParameter("cancelSubscription", SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
			query.setMaxResults(1);
			List<SubscriptionPlan> subscriptionPlanList= query.getResultList();
			return subscriptionPlanList.get(0);
		}
		catch (NoResultException | IndexOutOfBoundsException e){
			LOG.info(String.format("No Subscription Found For Customer with ID : %d", customerId));
			return null;
		}
	}

	@Override
	public SubscriptionPlan getChaayosPrepaidSubscription(Integer customerId) {
		try {
			Query query = manager.createQuery("From SubscriptionPlan l where l.customerId = :customerId and l.subscriptionPlanCode = :subscriptionPlanCode and l.planEndDate > :planEndDate order by l.subscriptionPlanId desc");
			query.setParameter("customerId", customerId);
			query.setParameter("subscriptionPlanCode", AppConstants.CHAI_PREPAID);
			query.setParameter("planEndDate", AppUtils.getCurrentTimestamp());
			query.setMaxResults(1);
			List<SubscriptionPlan> subscriptionPlanList= query.getResultList();
			return subscriptionPlanList.get(0);
		}
		catch (NoResultException | IndexOutOfBoundsException e){
			LOG.info(String.format("No Subscription Found For Customer with ID : %d", customerId));
			return null;
		}
	}

	@Override
	public SubscriptionPlan updateSubscriptionData(SubscriptionRequest request, String source, Integer campaignId) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		SubscriptionPlan plan = manager.find(SubscriptionPlan.class, request.getPlanId());
		Date endDate = plan.getPlanEndDate();
		setPlan(request, plan, currentTime);
		manager.flush();
		SubscriptionPlanEvent event = createEvent(request, currentTime, plan.getSubscriptionPlanId(),AppUtils.getNextDate(endDate),source,campaignId);
		plan.setLastRenewalEventId(event.getSubscriptionPlanEventId());
		manager.flush();
		return plan;
	}

	@Override
	public void addSubscriptionSaving(int customerId, Order order, Pair<CouponDetail, Product> subscriptionObj){
		SubscriptionPlan subscriptionPlan = getActiveSubscription(customerId);
		SubscriptionPlanEvent subscriptionPlanEvent = manager.find(SubscriptionPlanEvent.class, subscriptionPlan.getLastRenewalEventId());
		subscriptionPlan.setOverAllSaving(AppUtils.add(order.getTransactionDetail().getSavings(),subscriptionPlan.getOverAllSaving()));
		if(FrequencyOfferType.TIME_BASED.name().equals(subscriptionPlan.getFrequencyStrategy())
				|| FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(subscriptionPlan.getFrequencyStrategy())){
			subscriptionPlan.setOverAllFrequency(AppUtils.add(subscriptionPlan.getOverAllFrequency(),BigDecimal.ONE));
		} else if(FrequencyOfferType.QUANTITY_BASED.name().equals(subscriptionPlan.getFrequencyStrategy())){
			subscriptionPlan.setOverAllFrequency(AppUtils.add(subscriptionPlan.getOverAllFrequency(),getOrderWithSubscriptionCode(order,subscriptionPlan.getSubscriptionPlanCode())));
		}
		if(Objects.nonNull(subscriptionPlan.getOverAllFrequency()) && subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit())>=0){
			subscriptionPlan.setStatus(AppConstants.IN_ACTIVE);
			subscriptionPlanEvent.setStatus(AppConstants.IN_ACTIVE);
		}
		manager.persist(subscriptionPlanEvent);
		manager.persist(subscriptionPlan);
		manager.flush();
	}

	private BigDecimal getOrderWithSubscriptionCode(Order order, String subscriptionPlanCode) {
		BigDecimal frequencyUsed = BigDecimal.ZERO;
		for (OrderItem orderItem : order.getOrders()){
			if(Objects.nonNull(orderItem.getDiscountDetail().getDiscountReason()) && 
					orderItem.getDiscountDetail().getDiscountReason().equals(subscriptionPlanCode)){
				frequencyUsed = AppUtils.add(frequencyUsed,AppUtils.divide(
						AppUtils.subtract(
								AppUtils.multiply(orderItem.getPrice(),
										BigDecimal.valueOf(orderItem.getQuantity())),orderItem.getAmount()),orderItem.getPrice()));
//				((price*quantity)-amt_paid)/price

			}
		}
		return frequencyUsed;
	}

	@Override
	public SubscriptionPlanEvent getSubscriptionPlanEvent(Integer subscriptionPlanId){
		try {
			SubscriptionPlanEvent planEvent = manager.find(SubscriptionPlanEvent.class, subscriptionPlanId);
			return planEvent;
		}catch (NoResultException e){
			LOG.error("No Subscription Found For subscription plan with ID : %d",subscriptionPlanId);
		}
		return null;
	}

	@Override
	public void addSubscriptionEventSaving(int customerId, BigDecimal subscriptionSavings) {
		try {
			Query query = manager.createQuery("FROM SubscriptionPlanEvent S where S.customerId = :customerId " +
					"and S.planStartDate <= :currentDate and S.planEndDate >= :currentDate and status = :status");
			query.setParameter("currentDate",AppUtils.getCurrentDate());
			query.setParameter("customerId",customerId);
			query.setParameter("status",AppConstants.ACTIVE);
			SubscriptionPlanEvent subscriptionPlanEvent = (SubscriptionPlanEvent) query.getSingleResult();
			subscriptionPlanEvent.setSubscriptionSavings(AppUtils.add(subscriptionSavings,subscriptionPlanEvent.getSubscriptionSavings()));
			manager.persist(subscriptionPlanEvent);
			manager.flush();
		}
		catch (Exception e){
			LOG.error("Can not find data",e);
		}

	}


	private void setPlan(SubscriptionRequest request, SubscriptionPlan plan, Date currentTime) {
		plan.setPlanStartDate(request.getStartDate());
		plan.setPlanEndDate(request.getEndDate());
		plan.setRenewalTime(currentTime);
		plan.setStatus(AppConstants.ACTIVE);
		plan.setSubscriptionPlanCode(request.getOfferDescription());
		plan.setEventType(request.getType().name());
		plan.setLastRenewalEventId(-1);
		plan.setFrequencyStrategy(request.getFrequencyStrategy());
		plan.setOverAllFrequency(request.getOverAllFrequency());
		plan.setFrequencyLimit(request.getFrequencyLimit());
	}

	private SubscriptionPlanEvent createEvent(SubscriptionRequest request, Date currentTime, Integer planId, Date startDate, String source, Integer campaignId) {
		SubscriptionPlanEvent event = new SubscriptionPlanEvent();
		event.setSubscriptionPlanId(planId);
		event.setCustomerId(request.getCustomerId());
		event.setPlanStartDate(startDate);
		event.setPlanEndDate(request.getEndDate());
		event.setRenewalTime(currentTime);
		event.setStatus(AppConstants.ACTIVE);
		event.setSubscriptionPlanCode(request.getOfferDescription());
		event.setEventType(request.getType().name());
		event.setDimensionCode(request.getDimension());
		event.setOrderId(request.getOrderId());
		event.setOrderItemId(request.getOrderItemId());
		event.setProductId(request.getProductId());
		event.setPrice(request.getPrice());
		event.setValidityInDays(request.getValidityInDays());
		event.setSubscriptionSource(source);
		event.setCampaignId(campaignId);
		manager.persist(event);
		return event;
	}
}
