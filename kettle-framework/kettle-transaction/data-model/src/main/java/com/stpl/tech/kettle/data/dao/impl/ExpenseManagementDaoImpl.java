package com.stpl.tech.kettle.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.data.budget.vo.ElectricityStaticData.ElectricityBillType;
import com.stpl.tech.kettle.data.dao.ExpenseManagementDao;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.ExpenseDetailData;
import com.stpl.tech.kettle.data.model.MeterDetailsData;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.domain.model.CalculationIndexStatus;
import com.stpl.tech.kettle.domain.model.MeterDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class ExpenseManagementDaoImpl extends AbstractDaoImpl implements ExpenseManagementDao {

	@Override
	public List<ExpenseDetailData> getExpenseDetail(Integer unitId, String expenseCategory, String expenseType,
			Date startDate, Date endDate, String status) {
		StringBuilder queryString = new StringBuilder("FROM ExpenseDetailData e WHERE ");
		if (unitId != null && unitId != 0) {
			queryString.append("e.unitId = :unitId ");
		} else {
			queryString.append(" 1 = 1 ");
		}

		if (expenseCategory != null) {
			queryString.append("and e.expenseCategory = :expenseCategory ");
		}

		if (expenseType != null) {
			queryString.append("and e.expenseType = :expenseType ");
		}
		if (status != null) {
			queryString.append("and e.status = :status ");
		}

		if (startDate != null) {
			queryString.append("and e.createdOn >= :startDate ");
		}

		if (endDate != null) {
			queryString.append("and e.createdOn <= :endDate ");
		}

		queryString.append("order by e.createdOn desc");

		Query query = manager.createQuery(queryString.toString());

		if (unitId != null && unitId != 0) {
			query.setParameter("unitId", unitId);
		}

		if (expenseCategory != null) {
			query.setParameter("expenseCategory", expenseCategory);
		}

		if (expenseType != null) {
			query.setParameter("expenseType", expenseType);
		}

		if (status != null) {
			query.setParameter("status", status);
		}

		if (startDate != null) {
			query.setParameter("startDate", startDate);
		}

		if (endDate != null) {
			query.setParameter("endDate", endDate);
		}

		return query.getResultList();
	}

	@Override
	public List<Object[]> getLastMeterReading(int unitId) {
		String queryElectricity = " SELECT meterNo,max(m.currentUnit) as lastReading,m.billType,m.calculationIndex from MeterDetailsData m "
				+ "where m.unitId = :unitId  and m.billType =:billType and m.status = :status group by m.meterNo";
		Query query = manager.createQuery(queryElectricity);

		query.setParameter("unitId", unitId);
		query.setParameter("billType", ElectricityBillType.ELECTRICITY.name());
		query.setParameter("status", AppConstants.ACTIVE);
		
		List<Object[]> list = (List<Object[]>) query.getResultList();

		String queryStr = " SELECT meterNo,max(m.currentUnit) as lastReading,m.billType,m.calculationIndex from MeterDetailsData m "
				+ "where m.unitId = :unitId  and m.billType =:billType and m.status = :status group by m.meterNo";
		Query queryDG = manager.createQuery(queryStr);
		queryDG.setParameter("unitId", unitId);
		queryDG.setParameter("billType", ElectricityBillType.DG.name());
		queryDG.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> listDG = (List<Object[]>) queryDG.getResultList();
		list.addAll(listDG);

		return list;
	}

	@Override
	public List<MeterDetailsData> getMeterDetailList(int unitId, Date currentDate, String entryType,
			String calculationIndex) {
		StringBuilder queryStr = new StringBuilder(
				" from MeterDetailsData m where m.status = :status and m.unitId = :unitId and m.createdOn > :currentDate ");
		if (entryType != null) {
			queryStr.append("and m.entryType = :entryType ");
		}
		if (calculationIndex != null) {
			queryStr.append("and m.calculationIndex = :calculationIndex ");
		}
		Query query = manager.createQuery(queryStr.toString());
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("unitId", unitId);
		query.setParameter("currentDate", currentDate);
		if (entryType != null) {
			query.setParameter("entryType", entryType);
		}
		if (calculationIndex != null) {
			query.setParameter("calculationIndex", calculationIndex);
		}
		return query.getResultList();
	}

	@Override
	public void updateCalculationIndex(int unitId, Date currentDate, String calculationIndex) {
		String queryStr = "UPDATE MeterDetailsData m SET m.calculationIndex = :calculationIndex WHERE  m.unitId = :unitId and m.createdOn > :currentDate "
				+ "and m.calculationIndex = :prevCalculationIndex ";
		Query query = manager.createQuery(queryStr);
		query.setParameter("unitId", unitId);
		query.setParameter("prevCalculationIndex", CalculationIndexStatus.LAST.name());
		query.setParameter("calculationIndex", calculationIndex);
		query.setParameter("currentDate", currentDate);

		query.executeUpdate();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.data.dao.ExpenseManagementDao#
	 * getPnlAccountableExpenses(int, int, int)
	 */
	@Override
	public List<ExpenseDetailData> getPnlAccountableExpenses(int unitId, int month, int year) {
		Query query = manager.createQuery(
				"from ExpenseDetailData where unitId = :unitId and month(createdOn) = :month and year(createdOn) = :year "
						+ "and status =:status and accountableInPnL = :accountableInPnL");
		query.setParameter("unitId", unitId);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("accountableInPnL", AppConstants.YES);
		return query.getResultList();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.data.dao.ExpenseManagementDao#
	 * getPnlAccountableExpenses(int, java.util.Date)
	 */
	@Override
	public List<ExpenseDetailData> getUnAccountedPnlExpenses(int unitId, int month, int year) {
		Query query = manager.createQuery("from ExpenseDetailData where unitId = :unitId "
				+ "and status =:status and accountableInPnL = :accountableInPnL and pnlDetailId is null and month(createdOn) = :month and year(createdOn) = :year");
		query.setParameter("unitId", unitId);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("accountableInPnL", AppConstants.YES);
		return query.getResultList();
	}

	@Override
	public List<PnlAdjustmentDetail> getAdjustmentAggregate(int unitId, int month, int year, Date businessDate) {
		Query query= manager.createQuery("SELECT P.adjustmentType,SUM(P.adjustmentValue) FROM PnlAdjustmentDetail P " +
				"where P.approvalTime >= :businessDate and P.approvalTime <= :incrementBusinessDate " +
				"and P.status = :status and P.month= :month and P.year = :year and P.unitId= :unitId GROUP BY P.adjustmentType");
		query.setParameter("unitId",unitId);
		query.setParameter("businessDate",businessDate);
		query.setParameter("incrementBusinessDate",AppUtils.getNextDate(businessDate));
		query.setParameter("month",month);
		query.setParameter("year",year);
		query.setParameter("status",AppConstants.APPROVED);
		List<Object[]> list = query.getResultList();
		List<PnlAdjustmentDetail> pnlAdjustmentDetails= new ArrayList<>();
		if (list != null) {
			for (Object[] detail : list) {
				PnlAdjustmentDetail d = new PnlAdjustmentDetail();
				d.setAdjustmentType(detail[0].toString());
				d.setAdjustmentValue(new BigDecimal(detail[1].toString()));
				pnlAdjustmentDetails.add(d);
			}
		}
		return pnlAdjustmentDetails;
	}

	@Override
	public List<PnlAdjustmentDetail> getMTDAdjustmentAggregate(int unitId, int month, int year) {
		Query query= manager.createQuery("SELECT P.adjustmentType,SUM(P.adjustmentValue) FROM PnlAdjustmentDetail P " +
				"where P.status = :status and P.month= :month and P.year = :year and P.unitId= :unitId GROUP BY P.adjustmentType");
		query.setParameter("unitId",unitId);
		query.setParameter("month",month);
		query.setParameter("year",year);
		query.setParameter("status",AppConstants.APPROVED);
		List<Object[]> list = query.getResultList();
		List<PnlAdjustmentDetail> pnlAdjustmentDetails= new ArrayList<>();
		if (list != null) {
			for (Object[] detail : list) {
				PnlAdjustmentDetail d = new PnlAdjustmentDetail();
				d.setAdjustmentType(detail[0].toString());
				d.setAdjustmentValue(new BigDecimal(detail[1].toString()));
				pnlAdjustmentDetails.add(d);
			}
		}
		return pnlAdjustmentDetails;
	}

	@Override
	public List<VoucherData> getUnAccountedPnlVoucherExpenses(Integer unitId) {
		Query query = manager.createQuery("from VoucherData where accountNo = :unitId AND accountType = :accountType"
				+ " AND currentStatus IN (:statuses) AND accountableInPNL = :accountableInPnL AND pnlDetailId IS NULL AND expenseCategory = :expenseCategory");
		query.setParameter("unitId", unitId.toString());
		query.setParameter("accountType", "UNIT");
		query.setParameter("expenseCategory", "PETTY_CASH");
		query.setParameter("statuses", Arrays.asList("APPROVED", "SETTLED"));
 		query.setParameter("accountableInPnL", AppConstants.YES);
		return query.getResultList();
	}

	public List<VoucherData> getAccountedPnlVoucherExpenses(Integer unitId, int month, int year) {

		Query query = manager.createQuery("from VoucherData where accountNo = :unitId AND accountType = :accountType"
				+ " AND currentStatus IN (:statuses) AND accountableInPNL = :accountableInPnL AND pnlDetailId IS NOT NULL AND expenseCategory = :expenseCategory "
				+ "AND MONTH(pnlInclusionDate) = :month AND YEAR(pnlInclusionDate) = :year");
		query.setParameter("unitId", unitId.toString());
		query.setParameter("accountType", "UNIT");
		query.setParameter("expenseCategory", "PETTY_CASH");
		query.setParameter("statuses", Arrays.asList("APPROVED", "SETTLED"));
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("accountableInPnL", AppConstants.YES);
		return query.getResultList();

	}

	@Override
	public List<MeterDetailsData> getAllMeterDetailList(int unitId) {
		StringBuilder queryStr = new StringBuilder(
				" from MeterDetailsData m where m.unitId = :unitId and m.status = :status order by m.createdOn desc");
		Query query = manager.createQuery(queryStr.toString());
		query.setParameter("unitId", unitId);
		query.setParameter("status", AppConstants.ACTIVE);
		query.setMaxResults(20);

		return query.getResultList();
	}

	@Override
	public boolean resetAllMeterReadingForUnit(int unitId, int userId) {
		try {
			Query query = manager
					.createQuery("update MeterDetailsData set status =:newstatus, resetBy= :resetBy, resetOn= :resetTime where unitId = :unitId "
							+ "and status =:status");
			query.setParameter("resetBy", userId);
			query.setParameter("resetTime", AppUtils.getCurrentTimestamp());
			query.setParameter("unitId", unitId);
			query.setParameter("newstatus", AppConstants.IN_ACTIVE);
			query.setParameter("status", AppConstants.ACTIVE);
			query.executeUpdate();
			return true;
		} catch (Exception e) {
		}
		return false;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.data.dao.ExpenseManagementDao#
	 * getPnlAccountableExpenses(int, java.util.Date)
	 */
	@Override
	public void setPnLDetailId(int unitId, int pnlDetailId, int month, int year) {
		Query query = manager
				.createQuery("update ExpenseDetailData set pnlDetailId= :pnlDetailId where unitId = :unitId "
						+ "and status =:status and accountableInPnL = :accountableInPnL and pnlDetailId is null and month(createdOn) = :month and year(createdOn) = :year");
		query.setParameter("unitId", unitId);
		query.setParameter("pnlDetailId", pnlDetailId);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("accountableInPnL", AppConstants.YES);
		query.executeUpdate();
		manager.flush();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.data.dao.ExpenseManagementDao#getMeterReading(int,
	 * int, int)
	 */
	@Override
	public Map<ElectricityBillType, Map<Integer, Integer>> getMeterReading(int unitId, int month, int year) {
		Map<ElectricityBillType, Map<Integer, Integer>> resultMap = new HashMap<>();
		Query query = manager.createQuery("from MeterDetailsData where unitId = :unitId "
				+ "and status =:status and (calculationIndex = :first or calculationIndex = :last) and month(createdOn) = :month and year(createdOn) = :year");

		query.setParameter("unitId", unitId);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("first", CalculationIndexStatus.FIRST.name());
		query.setParameter("last", CalculationIndexStatus.LAST.name());

		List<MeterDetailsData> datas = query.getResultList();
		List<MeterDetailsData> lastData = new ArrayList<>();
		List<MeterDetailsData> firstData = new ArrayList<>();
		for (MeterDetailsData data : datas) {
			if (data.getCalculationIndex().equals(CalculationIndexStatus.LAST.name())) {
				lastData.add(data);
			} else {
				firstData.add(data);
			}
		}

		for (MeterDetailsData data : firstData) {
			if (resultMap.get(ElectricityBillType.valueOf(data.getBillType())) == null) {
				Map<Integer, Integer> map = new HashMap<>();
				resultMap.put(ElectricityBillType.valueOf(data.getBillType()), map);
			}
			for (MeterDetailsData ldata : lastData) {

				if (data.getBillType().equals(ldata.getBillType()) && data.getMeterNo() == ldata.getMeterNo()) {

					Map<Integer, Integer> rMap = resultMap.get(ElectricityBillType.valueOf(data.getBillType()));
					rMap.put(data.getMeterNo(), (ldata.getCurrentUnit() - data.getCurrentUnit()));
					resultMap.put(ElectricityBillType.valueOf(data.getBillType()), rMap);
				}
			}
		}
		return resultMap;
	}

	@Override
	public BigDecimal getAllExpenses(int unitId, int month, int year, String category) {
		Query query = manager
				.createQuery("SELECT sum(amount) as totalExpense from ExpenseDetailData where unitId = :unitId "
						+ "and status =:status and budgetCategory = :budgetCategory and month(createdOn) = :month and year(createdOn) = :year");
		query.setParameter("unitId", unitId);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("budgetCategory", category);
		List<Object> datas = query.getResultList();
		if (datas.size() > 0) {
			return datas.get(0) == null ? BigDecimal.ZERO : (BigDecimal) datas.get(0);
		}
		return BigDecimal.ZERO;
	}

	@Override
	public List<UnitBudgetExceededData> getBudgetExceededDetails(int unitId, String notificationType, Date tillDate) {
		StringBuilder queryString = new StringBuilder(
				" from UnitBudgetExceededData m where m.unitId = :unitId  and m.createdOn < :tillDate ");
		if (notificationType != null) {
			queryString.append(" and m.notificationType = :notificationType ");
		}
		queryString.append(" order by m.createdOn desc");

		Query query = manager.createQuery(queryString.toString());
		query.setParameter("unitId", unitId);
		query.setParameter("tillDate", tillDate);
		if (notificationType != null) {
			query.setParameter("notificationType", notificationType);
		}

		return query.getResultList();
	}

	@Override
	public List<MeterDetailsData> getLastMeterDetailList(MeterDetail detail) {
		StringBuilder queryStr = new StringBuilder(
				" from MeterDetailsData m where m.unitId = :unitId and m.billType = :billType and m.meterNo = :meterNo and m.status = :status  order by m.createdOn desc");

		Query query = manager.createQuery(queryStr.toString());

		query.setParameter("unitId", detail.getUnitId());
		query.setParameter("billType", detail.getBillType());
		query.setParameter("meterNo", detail.getMeterNo());
		query.setParameter("status", AppConstants.ACTIVE);
		
		query.setMaxResults(2);

		return query.getResultList();
	}

	@Override
	public void unMarkPnlAccountableExpenses(int pnlDetailId) {
		Query query = manager
				.createQuery("UPDATE ExpenseDetailData SET pnlDetailId = null WHERE pnlDetailId = :pnlDetailId ");
		query.setParameter("pnlDetailId", pnlDetailId);
		query.executeUpdate();
		manager.flush();
	}

}
