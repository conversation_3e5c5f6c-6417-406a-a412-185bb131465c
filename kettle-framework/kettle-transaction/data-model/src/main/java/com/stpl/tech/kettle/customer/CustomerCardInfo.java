package com.stpl.tech.kettle.customer;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerCardInfo {

        private String  customerId;
        private String firstName;
        private String middleName;
        private String lastName;
        private String countryCode;
        private String contactNumber;
        private String emailId;
        private String isNumberVerified;
        private Date numberVerificationTime;

        private String subscriptionPlanCode;
        private Date planStartDate;
        private Date planEndDate;
        private String cardCode;
        private String offerDescription;

        private BigDecimal walletBalance;
        private Integer loyaltyBalance;


        public CustomerCardInfo() {
        }


    public CustomerCardInfo(String customerId, String firstName, String middleName, String lastName, String countryCode, String contactNumber, String emailId, String isNumberVerified, Date numberVerificationTime, String subscriptionPlanCode, Date planStartDate, Date planEndDate, String cardCode, String offerDescription, BigDecimal walletBalance, Integer loyaltyBalance) {
        this.customerId = customerId;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.countryCode = countryCode;
        this.contactNumber = contactNumber;
        this.emailId = emailId;
        this.isNumberVerified = isNumberVerified;
        this.numberVerificationTime = numberVerificationTime;
        this.subscriptionPlanCode = subscriptionPlanCode;
        this.planStartDate = planStartDate;
        this.planEndDate = planEndDate;
        this.cardCode = cardCode;
        this.offerDescription = offerDescription;
        this.walletBalance = walletBalance;
        this.loyaltyBalance = loyaltyBalance;
    }

    public String getCustomerId() {
            return customerId;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getMiddleName() {
            return middleName;
        }

        public void setMiddleName(String middleName) {
            this.middleName = middleName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getContactNumber() {
            return contactNumber;
        }

        public void setContactNumber(String contactNumber) {
            this.contactNumber = contactNumber;
        }

        public String getEmailId() {
            return emailId;
        }

        public void setEmailId(String emailId) {
            this.emailId = emailId;
        }

        public String getIsNumberVerified() {
            return isNumberVerified;
        }

        public void setIsNumberVerified(String isNumberVerified) {
            this.isNumberVerified = isNumberVerified;
        }

        public String getSubscriptionPlanCode() {
            return subscriptionPlanCode;
        }

        public void setSubscriptionPlanCode(String subscriptionPlanCode) {
            this.subscriptionPlanCode = subscriptionPlanCode;
        }

        public Date getPlanStartDate() {
            return planStartDate;
        }

        public void setPlanStartDate(Date planStartDate) {
            this.planStartDate = planStartDate;
        }

        public Date getPlanEndDate() {
            return planEndDate;
        }

        public void setPlanEndDate(Date planEndDate) {
            this.planEndDate = planEndDate;
        }

        public Date getNumberVerificationTime() {
            return numberVerificationTime;
        }

        public void setNumberVerificationTime(Date numberVerificationTime) {
            this.numberVerificationTime = numberVerificationTime;
        }

        public BigDecimal getWalletBalance() {
            return walletBalance;
        }

        public void setWalletBalance(BigDecimal walletBalance) {
            this.walletBalance = walletBalance;
        }

        public Integer getLoyaltyBalance() {
            return loyaltyBalance;
        }

        public void setLoyaltyBalance(Integer loyaltyBalance) {
            this.loyaltyBalance = loyaltyBalance;
        }

        public String getCardCode() {
            return cardCode;
        }

        public void setCardCode(String cardCode) {
            this.cardCode = cardCode;
        }

        public String getOfferDescription() {
            return offerDescription;
        }

        public void setOfferDescription(String offerDescription) {
            this.offerDescription = offerDescription;
        }
}


