package com.stpl.tech.kettle.referral.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface ReferralDao extends AbstractDao {

	public ReferralMappingData searchByCode(String refCode, String contactNumber);

	public void updateReferralStatus(CustomerInfo info, String refCode);

	public List<ReferralMappingData> searchByContactNumber(String contactNumber);

	public ReferralMappingData getReffralMapping(String contact, int referrerId);

	List<ReferralMappingData> getReferralMappings(int referrerId);

}
