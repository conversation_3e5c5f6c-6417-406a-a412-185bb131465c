package com.stpl.tech.kettle.datalake.dao.impl;

import static java.lang.Math.min;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.datalake.dao.CustomerReportDao;
import com.stpl.tech.kettle.datalake.model.CustomerNPSFeedbackStats;
import com.stpl.tech.kettle.datalake.model.CustomerOrderFeedbackStats;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.AppConstants;

@Repository
public class CustomerReportDaoImpl extends AbstractDataLakeDaoImpl implements CustomerReportDao {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerReportDaoImpl.class);

    @Override
    public void calculateNpsForBusinessDate(Date fromDate, Date tillDate) {
        LOG.info(" update CustomerID For BusinessDate " + fromDate + " to " + tillDate);
        Query query = manager.createNativeQuery("CALL CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE(:fromDate,:tillDate)");
        query.setParameter("tillDate", tillDate);
        query.setParameter("fromDate", fromDate);
        query.executeUpdate();

    }

    @Override
    public void calculateCustomerOneViewFeedbackStats(Date fromDate, Date tillDate) {
        LOG.info(" update Customer One View FeedbackStats " + fromDate + " to " + tillDate);
        Query query = manager.createNativeQuery("CALL CUSTOMER_ONE_VIEW_FEEDBACK_STATS(:fromDate,:tillDate)");
        query.setParameter("tillDate", tillDate);
        query.setParameter("fromDate", fromDate);
        query.executeUpdate();

    }

    @Override
    public void calculateFeedbackForBusinessDate(Date fromDate, Date tillDate) {
        LOG.info(" update Order Feedback CustomerID For BusinessDate " + fromDate + " to " + tillDate);
        Query query = manager.createNativeQuery("CALL CUSTOMER_IDS_FOR_FEEDBACK_FOR_A_BUSINESS_DATE(:fromDate,:tillDate)");
        query.setParameter("tillDate", tillDate);
        query.setParameter("fromDate", fromDate);
        query.executeUpdate();

    }

    @Override
    public void calculateCustomerOneViewOrderFeedbackStats(Date fromDate, Date tillDate) {
        LOG.info(" update Customer One View Order FeedbackStats " + fromDate + " to " + tillDate);
        Query query = manager.createNativeQuery("CALL CUSTOMER_ONE_VIEW_ORDER_FEEDBACK_STATS(:fromDate,:tillDate)");
        query.setParameter("tillDate", tillDate);
        query.setParameter("fromDate", fromDate);
        query.executeUpdate();

    }

    
    @Override
    public CustomerNPSFeedbackStats getNpsForCustomerDetail(Integer customerId) throws DataNotFoundException {
        try {
            CustomerNPSFeedbackStats customerNPSFeedbackStats;
            Query query = manager.createQuery(" FROM CustomerNPSFeedbackStats e WHERE e.customerId =:customerId and e.isLatest=:isLatest ");
            query.setParameter("customerId", customerId);
            query.setParameter("isLatest", AppConstants.YES);
            customerNPSFeedbackStats = (CustomerNPSFeedbackStats) query.getSingleResult();
            return customerNPSFeedbackStats;
        }  catch (Exception e) {
            LOG.info("Did not find customer NPS  Details with ID {}", customerId);
            return null;
        }

    }

    @Override
    public CustomerOrderFeedbackStats getFeedbackForCustomerDetail(Integer customerId) throws DataNotFoundException {
        try {
        	CustomerOrderFeedbackStats customerNPSFeedbackStats;
            Query query = manager.createQuery(" FROM CustomerOrderFeedbackStats e WHERE e.customerId =:customerId and e.isLatest=:isLatest ");
            query.setParameter("customerId", customerId);
            query.setParameter("isLatest", AppConstants.YES);
            customerNPSFeedbackStats = (CustomerOrderFeedbackStats) query.getSingleResult();
            return customerNPSFeedbackStats;
        }  catch (Exception e) {
            LOG.info("Did not find customer Feedback  Details with ID {}", customerId);
            return null;
        }

    }

    @Override
    public List<Integer> customerRecommendedProduct(Integer customerId) {
        LOG.info("finding RecommendedProduct in dao ");
        List<Integer> finalResult=new ArrayList<>();
        String recommendationSegmentTea = "RFM_WITH_DESI_CHAI_WITHOUT_COOKIES";
        String recommendationSegmentFood = "RFM_WITHOUT_DESI_CHAI_WITHOUT_COOKIES";
        List<Integer> result = findListWithSegment(recommendationSegmentTea, customerId); // 2 products
        if(!result.isEmpty()){
          finalResult.addAll(result.subList(0,min(result.size()-1,2)));
        }
        List<Integer> result1=(findListWithSegment(recommendationSegmentFood, customerId));  // 5 products
        if(!result1.isEmpty()){
           finalResult.addAll(result1.subList(0,min(result1.size()-1,5)));
        }
        LOG.info("return size of result{} ", finalResult.size());
        return finalResult;
    }

    private List<Integer> findListWithSegment(String recommendationSegment, Integer customerId) {
        Query query = manager.createQuery("SELECT e.productID FROM RFMForActiveCustomers e where e.customerId =:customerId\n" +
            "and e.recommendationSegment =:recommendationSegment\n" +
            "ORDER BY e.lastUpdateDate DESC, e.RFMScore DESC, e.recency DESC, e.frequency DESC ");
        query.setParameter("recommendationSegment", recommendationSegment);
        query.setParameter("customerId", customerId);
        List<Integer> result = query.getResultList(); // product ids
        return result;
    }

}
