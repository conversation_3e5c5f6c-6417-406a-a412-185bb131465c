/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.xml.datatype.DatatypeConfigurationException;

import com.google.common.base.Stopwatch;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.service.CashManagementService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.CashManagementDao;
import com.stpl.tech.kettle.data.model.ClosurePaymentDetails;
import com.stpl.tech.kettle.data.model.ClosurePaymentTaxDetails;
import com.stpl.tech.kettle.data.model.PullDetail;
import com.stpl.tech.kettle.data.model.SettlementDetail;
import com.stpl.tech.kettle.domain.model.PullPacket;
import com.stpl.tech.kettle.domain.model.PullPacketDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDetail;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.tax.model.Taxation;

@Service
public class CashManagementServiceImpl implements CashManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(CashManagementServiceImpl.class);

	@Autowired
	private CashManagementDao dao;

	@Autowired
	private MasterDataCache cache;

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullPacket> getOpenPullsForUnit(Integer unitId, Integer paymentModeId, Integer resultCount,
			List<String> statusList) throws DataNotFoundException {
		List<PullDetail> pullDetails = dao.getOpenPullsForUnit(unitId, paymentModeId, resultCount, statusList);
		List<PullPacket> pullPackets = new ArrayList<>();
		for (PullDetail pull : pullDetails) {
			pullPackets.add(DataConverter.convert(pull, pull.getCreatedBy(), pull.getWitnessedBy(), cache));
		}
		return pullPackets;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullPacket> getUnitPullsForTransfer(Integer unitId) throws DataNotFoundException {
		List<PullDetail> pullDetails = dao.getUnitPullsForTransfer(unitId);
		List<PullPacket> pullPackets = new ArrayList<>();
		for (PullDetail pull : pullDetails) {
			pullPackets.add(DataConverter.convert(pull, pull.getCreatedBy(), pull.getWitnessedBy(), cache));
		}
		return pullPackets;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PullPacket submitPull(PullPacket pullPacket) throws DataNotFoundException {
		PaymentMode mode = cache.getPaymentModesMap().get(pullPacket.getPaymentMode().getId());
		PullDetail pullDetail = dao.submitPull(mode, pullPacket);
		return DataConverter.convert(pullDetail, pullDetail.getCreatedBy(),
				cache.getEmployee(pullDetail.getCreatedBy()), pullDetail.getWitnessedBy(), cache,
				pullDetail.getPullDenominations());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PullSettlementDetail transferPull(PullSettlementDetail pullSettlementDetail) throws DataNotFoundException {
		if(pullSettlementDetail.getSettlementServiceProvider().equalsIgnoreCase(com.stpl.tech.kettle.domain.model.SettlementType.BANK_PICKUP.name())){
			pullSettlementDetail.setSettlementServiceProvider(SettlementType.BANK_PICKUP.name());
		}else if(pullSettlementDetail.getSettlementServiceProvider().equalsIgnoreCase(SettlementType.MANUAL_DEPOSIT.name())){
			pullSettlementDetail.setSettlementServiceProvider(SettlementType.MANUAL_DEPOSIT.name());
		}
		pullSettlementDetail = dao.transferPull(pullSettlementDetail);
		return pullSettlementDetail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullSettlementDenomination> getPullSettlementDenominationsForPaymentMode(PaymentMode paymentMode) {
		return DataConverter
				.convertSettlementDenomination(cache.getPaymentMode(paymentMode.getId()).getDenominations());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullSettlementDetail> getOpenPullSettlements(Integer unitId, Integer settlementId)
			throws DataNotFoundException, DatatypeConfigurationException {
		List<SettlementDetail> settlementDetails = dao.getOpenPullSettlements(unitId, settlementId);
		List<PullSettlementDetail> psds = new ArrayList<>();
		for (SettlementDetail settlementDetail : settlementDetails) {
			PullSettlementDetail psd = DataConverter.convert(settlementDetail, cache, true, true);
			psds.add(psd);
		}
		return psds;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullSettlementDetail> getOpenPullSettlementsByType(Integer settlementTypeId)
			throws DataNotFoundException, DatatypeConfigurationException {
		List<SettlementDetail> settlementDetails = dao.getOpenPullSettlementsByType(settlementTypeId);
		List<PullSettlementDetail> psds = new ArrayList<>();
		for (SettlementDetail settlementDetail : settlementDetails) {
			PullSettlementDetail psd = DataConverter.convert(settlementDetail, cache, true, true);
			psds.add(psd);
		}
		return psds;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullSettlementDetail> getPullSettlements(Date startDate, Date endDate, int unitId, boolean fetchPulls, boolean fetchDenominations,int start,int batchSize)
			throws DataNotFoundException, DatatypeConfigurationException {
		List<SettlementDetail> settlementDetails = dao.getPullSettlements(startDate, endDate, unitId,start,batchSize);
		List<PullSettlementDetail> psds = new ArrayList<>();
		for (SettlementDetail settlementDetail : settlementDetails) {
			PullSettlementDetail psd = DataConverter.convert(settlementDetail, cache, fetchPulls, fetchDenominations);
			psds.add(psd);
		}
		return psds;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullSettlementDetail> getPullSettlementsByType(Date startDate, Date endDate, int settlementTypeId,int start,int batchSize)
			throws DataNotFoundException, DatatypeConfigurationException {
		List<SettlementDetail> settlementDetails = dao.getPullSettlementsByType(startDate, endDate, settlementTypeId,start,batchSize);
		List<PullSettlementDetail> psds = new ArrayList<>();
		for (SettlementDetail settlementDetail : settlementDetails) {
			PullSettlementDetail psd = DataConverter.convert(settlementDetail, cache, true, true);
			psds.add(psd);
		}
		return psds;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullPacketDenomination> getCouponDenominations(int pullId)
			throws DataNotFoundException, DatatypeConfigurationException {
		return dao.getCouponDenominations(pullId, cache.getDenominations());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PullPacketDenomination> getCouponDenominations(List<Integer> pullIds, int paymentModeId)
			throws DataNotFoundException, DatatypeConfigurationException {

		List<DenominationDetail> denoms = cache.getPaymentMode(paymentModeId).getDenominations();
		Set<PullPacketDenomination> ppds = new HashSet<>();
		for (DenominationDetail dd : denoms) {
			PullPacketDenomination ppd = new PullPacketDenomination();
			ppd.setDenominationDetail(dd);
			ppds.add(ppd);
		}
		for (int pullId : pullIds) {
			List<PullPacketDenomination> ppdList = dao.getCouponDenominations(pullId, cache.getDenominations());
			if (ppdList.size() > 0) {
				for (PullPacketDenomination ppd : ppdList) {
					for (PullPacketDenomination pull : ppds) {
						if (ppd.getDenominationDetail().getDenominationId() == pull.getDenominationDetail()
								.getDenominationId()) {
							int currencyCount = pull.getLooseCurrencyCount() == null ? 0 : pull.getLooseCurrencyCount();
							pull.setLooseCurrencyCount(currencyCount + ppd.getLooseCurrencyCount());
							BigDecimal total = pull.getTotalAmount() == null ? BigDecimal.ZERO : pull.getTotalAmount();
							pull.setTotalAmount(total.add(ppd.getTotalAmount()));
						}
					}
				}
			}
		}
		List<PullPacketDenomination> ppdSet = new ArrayList<>();
		for (PullPacketDenomination pull : ppds) {
			if (pull.getLooseCurrencyCount() != null && pull.getLooseCurrencyCount() > 0) {
				ppdSet.add(pull);
			}
		}
		return ppdSet;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PullSettlementDetail closePullSettlement(PullSettlementDetail pullSettlement) {
		return dao.closePullSettlement(pullSettlement);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void saveClosurePayment(Date businessDate, Collection<SettlementReport> reports, int unitId) {
		for (SettlementReport report : reports) {

			LOG.info("Saving closure payment details for unit " + unitId);
			ClosurePaymentDetails paymentDetails = new ClosurePaymentDetails();
			paymentDetails.setPaymentModeId(report.getId());
			paymentDetails.setGmv(report.getGrossAmount());
			paymentDetails.setDiscount(report.getDiscountAmount());
			paymentDetails.setNetSalesAmount(report.getGrossAmount().subtract(report.getDiscountAmount()));
			List<ClosurePaymentTaxDetails> taxes = new ArrayList<>();
			BigDecimal totalTax = BigDecimal.ZERO;
			for (Taxation tax : report.getTaxes().keySet()) {
				ClosurePaymentTaxDetails data = new ClosurePaymentTaxDetails();
				data.setTotalTax(report.getTaxes().get(tax));
				data.setTaxCode(tax.getCode());
				data.setTaxPercentage(tax.getPercentage());
				data.setTaxType(tax.getType());
				data.setTotalAmount(report.getGrossAmount());
				data.setTaxableAmount(report.getAmount());
				totalTax = totalTax.add(report.getTaxes().get(tax));
				taxes.add(data);
			}
			paymentDetails.setTotalTax(totalTax);
			paymentDetails.setRoundOff(report.getRoundOff());
			paymentDetails.setTotalAmount(report.getTotal());
			paymentDetails.setBillCount(report.getNoOfBills());
			ClosurePaymentDetails closurePaymentDetail = dao.saveClosurePaymentDetails(paymentDetails, taxes, unitId,
					businessDate);
			PaymentMode mode = cache.getPaymentModesMap().get(paymentDetails.getPaymentModeId());
			if (paymentDetails.getTotalAmount().compareTo(BigDecimal.ZERO) == 1 && mode.isGeneratePull()) {
				LOG.info("Generating pull packet for unit " + paymentDetails.getUnitClosureDetails().getUnitId());
				dao.savePullDetails(cache.getUnitBasicDetail(unitId), mode, closurePaymentDetail);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public String getImagePath(String settlementId) {
		return settlementId != null ? dao.getPullSettlementPath(Integer.parseInt(settlementId)) : null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public PullSettlementDetail getPullSettlement(int settlementId) throws DataNotFoundException, DatatypeConfigurationException {
		SettlementDetail settlement = dao.getSettlement(settlementId);
		return DataConverter.convert(settlement, cache, true, true);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean validatePullTransferDate(Date slipDate, int unitId,int paymentMode) {
		Map<String, Object> validDates = getValidPullTransferDates(unitId, paymentMode);
		Date minDate = (Date) validDates.get("min");
		Date maxDate = (Date) validDates.get("max");
		if (Objects.isNull(slipDate)) {
			return true;
		} else if ((slipDate.after(minDate) || slipDate.equals(minDate)) && (slipDate.before(maxDate) || slipDate.equals(maxDate))) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getValidPullTransferDatesAsString(int unitId,int paymentMode) {
		Map<String, Object> validDates = getValidPullTransferDates(unitId, paymentMode);
		validDates.put("max", DateFormatUtils.format((Date) validDates.get("max"), AppUtils.DATE_FORMAT_STRING));
		validDates.put("min", DateFormatUtils.format((Date) validDates.get("min"), AppUtils.DATE_FORMAT_STRING));
		return validDates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getValidPullTransferDates(int unitId,int paymentMode) {
		Date yesterdayDate = AppUtils.addDays(AppUtils.getCurrentDate(), -1);
		Date currentDate = AppUtils.getEndOfDay(AppUtils.getCurrentDate());
		Date latestPullDate = null;
		Map<String, Object> validDates = new HashMap<>();
		validDates.put("max", currentDate);
		try {
			LOG.info("Getting latest transferred pull date for unit with id {} ", unitId);
			latestPullDate = dao.getLatestSuccessfulPullDate(unitId, paymentMode);
			validDates.put("min", latestPullDate.after(yesterdayDate) ? latestPullDate : yesterdayDate);
		} catch (DataNotFoundException e) {
			LOG.info("No latest transferred pull date for unit with id {} ", unitId);
			validDates.put("min", AppUtils.getDate("1970-01-01", AppUtils.DATE_FORMAT_STRING));
		}
		return validDates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Long getPullSettlementsTotalCount(Date startDate, Date endDate, int unitId, boolean fetchPulls, boolean fetchDenominations)
			throws DataNotFoundException, DatatypeConfigurationException {
		Long settlementsTotalCount = dao.getPullSettlementsTotalCount(startDate, endDate, unitId);
		return settlementsTotalCount;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Long getByTypePullSettlementsTotalCount(Date startDate, Date endDate, int settlementTypeId)
			throws DataNotFoundException, DatatypeConfigurationException {
		Long settlementsGetByTypeTotalCount  = dao.getPullSettlementsByTypeTotalCount(startDate, endDate, settlementTypeId);
		return settlementsGetByTypeTotalCount;

	}

}
