package com.stpl.tech.kettle.customer.dao.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import com.google.ads.googleads.v10.resources.Feed;
import com.stpl.tech.kettle.data.model.OrderFeedbackQuestionResponse;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.FeedbackEventStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackFrequency;
import com.stpl.tech.kettle.core.FeedbackRatingType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.kettle.core.data.vo.AuditTokenInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackOrderMetadata;
import com.stpl.tech.kettle.core.data.vo.FeedbackRatingData;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.dao.FeedbackManagementDao;
import com.stpl.tech.kettle.customer.service.CustomerInfoService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.FeedbackField;
import com.stpl.tech.kettle.data.model.FeedbackForm;
import com.stpl.tech.kettle.data.model.FeedbackInfo;
import com.stpl.tech.kettle.data.model.FeedbackOrderItem;
import com.stpl.tech.kettle.data.model.FeedbackResponse;
import com.stpl.tech.kettle.data.model.FeedbackResponseData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.data.model.OrderFeedbackResponse;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderNPSDetail;
import com.stpl.tech.kettle.data.model.OrderNPSResponseData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.external.EventDetail;
import com.stpl.tech.kettle.domain.model.external.FieldType;
import com.stpl.tech.kettle.domain.model.external.FormField;
import com.stpl.tech.kettle.domain.model.external.FormResponseData;
import com.stpl.tech.kettle.domain.model.external.ResponseType;
import com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.master.core.external.notification.SlackNotification;

@Repository
public class FeedbackManagementDaoImpl extends AbstractDaoImpl implements FeedbackManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(FeedbackManagementDaoImpl.class);

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private CustomerInfoService customerService;

    @Autowired
    private TokenService<FeedbackTokenInfo> tokenService;

    @Autowired
    private NotificationService notificationService;

    private static final Set<Integer> includedCategories = new HashSet<>(Arrays.asList(5, 6, 7, 8, 10));

    @Override
    public List<FeedbackEventInfo> getPendingFeedbackEvents(FeedbackSource source, Date startTime, Date endTime) {
        List<FeedbackEventInfo> result = new ArrayList<>();
        Query query = manager.createQuery(
                "FROM FeedbackEvent E where E.eventSource = :eventSource and E.eventTriggerTime >= :startTime and E.eventTriggerTime < :endTime and E.eventStatus = :eventStatus and E.eventType = :eventType"
                        + " and E.feedbackDetail.feedbackStatus  = :feedbackStatus");
        query.setParameter("feedbackStatus", FeedbackStatus.CREATED.name());
        query.setParameter("eventSource", source.name());
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        query.setParameter("eventType", FeedbackEventType.REGULAR.name());
        query.setParameter("eventStatus", FeedbackEventStatus.CREATED.name());
        List<FeedbackEvent> list = query.getResultList();
        if (list != null && list.size() > 0) {
            for (FeedbackEvent event : list) {
                result.add(DataConverter.convert(masterCache, event));
            }
        }
        return result;
    }

    @Override
    public List<FeedbackEventInfo> getPendingNPSEvents(FeedbackSource source) {
        List<FeedbackEventInfo> result = new ArrayList<>();
        Date startTime = AppUtils.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentBusinessDayStartTime(), -32400);
        Query query = manager.createQuery(
                "FROM FeedbackEvent E where E.eventSource = :eventSource and E.eventStatus = :eventStatus and E.eventType IN (:eventType)"
                        + " and E.feedbackDetail.feedbackStatus  = :feedbackStatus AND E.eventTriggerTime >= :startTime AND E.eventTriggerTime <= :triggerTime");
        query.setParameter("feedbackStatus", FeedbackStatus.CREATED.name());
        query.setParameter("eventSource", source.name());
        query.setParameter("triggerTime", AppUtils.getCurrentTimestamp());
        query.setParameter("startTime", startTime);
        query.setParameter("eventType", Arrays.asList(FeedbackEventType.NPS.name(),FeedbackEventType.NPS_OFFER.name(),FeedbackEventType.ORDER_FEEDBACK.name()));
        query.setParameter("eventStatus", FeedbackEventStatus.CREATED.name());
        List<FeedbackEvent> list = query.getResultList();
        if (list != null && list.size() > 0) {
            for (FeedbackEvent event : list) {
                result.add(DataConverter.convert(masterCache, event));
            }
        }
        return result;
    }

    @Override
    public List<FeedbackEventInfo> getInAppPendingNPSEvents(FeedbackSource source) {
        List<FeedbackEventInfo> result = new ArrayList<>();
        Query query = manager.createQuery(
                "FROM FeedbackEvent E where E.eventSource = :eventSource and E.eventStatus = :eventStatus and E.eventType = :eventType"
                        + " and E.feedbackDetail.feedbackStatus  = :feedbackStatus " +
                        "AND E.eventTriggerTime <= :triggerTime");
        query.setParameter("feedbackStatus", FeedbackStatus.NOTIFIED.name());
        query.setParameter("eventSource", source.name());
        query.setParameter("triggerTime", AppUtils.getCurrentTimestamp());
        query.setParameter("eventType", FeedbackEventType.NPS.name());
        query.setParameter("eventStatus", FeedbackEventStatus.CREATED.name());
        List<FeedbackEvent> list = query.getResultList();
        if (list != null && list.size() > 0) {
            for (FeedbackEvent event : list) {
                result.add(DataConverter.convert(masterCache, event));
            }
        }
        return result;
    }

    @Override
    public List<FeedbackEventInfo> getPendingElaboratedFeedbackEvents(FeedbackSource source, Date startTime,
                                                                      Date endTime) {
        List<FeedbackEventInfo> result = new ArrayList<>();
        Query query = manager.createQuery(
                "FROM FeedbackEvent E where E.eventSource = :eventSource and E.eventTriggerTime >= :startTime and E.eventTriggerTime < :endTime and E.eventStatus = :eventStatus and E.eventType = :eventType");
        query.setParameter("eventSource", source.name());
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        query.setParameter("eventStatus", FeedbackEventStatus.CREATED.name());
        query.setParameter("eventType", FeedbackEventType.ELABORATED.name());
        List<FeedbackEvent> list = query.getResultList();
        if (list != null && list.size() > 0) {
            for (FeedbackEvent event : list) {
                result.add(DataConverter.convert(masterCache, event));
            }
        }
        return result;
    }

    @Override
    public boolean updateFeedbackEventStatus(List<Integer> feedbackEventIds, FeedbackEventStatus status) {
        Query query = manager.createQuery("update FeedbackEvent E "
                + "set E.eventStatus = :eventStatus, E.eventNotificationTime = :eventNotificationTime where E.feedbackEventId in (:feedbackEventIds)");
        query.setParameter("eventStatus", status.name());
        query.setParameter("feedbackEventIds", feedbackEventIds);
        query.setParameter("eventNotificationTime", AppUtils.getCurrentTimestamp());
        query.executeUpdate();
        manager.flush();
        return true;
    }

    @Override
    public FeedbackRatingData addFeedback(EventDetail event, FeedbackTokenInfo tokenInfo) {
        FeedbackRatingData ratingData = new FeedbackRatingData();
        Date currentTime = AppUtils.getCurrentTimestamp();
        Map<String, Integer> fieldMap = new HashMap<>();
        FeedbackForm form = getForm(fieldMap, event);
        Integer rating = null;
        if (event.getFormResponse().getDefinition().getRating() != null) {
            for (FormResponseData data : event.getFormResponse().getAnswers()) {
                if (event.getFormResponse().getDefinition().getRating().getId() == data.getField().getId()) {
                    rating = data.getNumber();
                    break;
                }
            }
        } else {
            rating = event.getFormResponse().getHidden().getRating();
        }

        if (rating == null) {
            for (FormResponseData data : event.getFormResponse().getAnswers()) {
                if (ResponseType.number.name().equals(data.getType()) && data.getField() != null
                        && FieldType.rating.name().equals(data.getField().getType())) {
                    rating = data.getNumber();
                    break;
                }
            }
        }

        if (rating == null) {
            for (FormResponseData data : event.getFormResponse().getAnswers()) {
                if (ResponseType.number.name().equals(data.getType()) && data.getField() != null
                        && FieldType.opinion_scale.name().equals(data.getField().getType())) {
                    rating = data.getNumber();
                    break;
                }
            }
        }
        String contactNumber = null;
        String name = null;
        boolean awardLoyalty = false;

        for (FormResponseData data : event.getFormResponse().getAnswers()) {
            if (ResponseType.phone_number.name().equals(data.getType()) && data.getField() != null
                    && FieldType.phone_number.name().equals(data.getField().getType())) {
                contactNumber = data.getPhoneNumber().substring(data.getPhoneNumber().length() - 10, data.getPhoneNumber().length());
                awardLoyalty = true;
            }
            if (ResponseType.text.name().equals(data.getType()) && data.getField() != null
                    && FieldType.short_text.name().equals(data.getField().getType())) {
                name = data.getText();
            }
        }

        if (awardLoyalty) {
            updateContactDetailsInFeedbackEvent(tokenInfo.getFeedbackId(), contactNumber, name);
        }

        FeedbackInfo info = createFeedback(fieldMap, currentTime, event, form, tokenInfo, rating);
        int orderId = 0;
        try {
            FeedbackDetail feedBackDetail = manager.find(FeedbackDetail.class, info.getFeedbackId());
            if (feedBackDetail != null && !FeedbackStatus.COMPLETED.name().equals(feedBackDetail.getFeedbackStatus())) {
                markFeedbackEventCompletion(info, currentTime, tokenInfo.getFeedbackEventId(), rating);
                markFeedbackCompletion(info, currentTime, tokenInfo.getFeedbackId(), tokenInfo.getFeedbackSource(),
                        event.getFormResponse().getHidden().getFeedbackUnit(), rating);
                orderId = feedBackDetail.getOrderId();
                if (feedBackDetail != null && FeedbackEventType.NPS.name().equals(feedBackDetail.getEventType())) {
                    addOrderNPSDetail(feedBackDetail, rating, event);
                }
            }
        } catch (Exception e) {
            LOG.error("Error NPS", e);
        }
        ratingData.setRating(info.getFeedbackRating());
        ratingData.setContactNumber(contactNumber);
        ratingData.setName(name);
        ratingData.setAwardLoyalty(awardLoyalty);
        ratingData.setOrderId(orderId);
        ratingData.setFeedbackId(tokenInfo.getFeedbackId());
        return ratingData;
    }

    private void updateContactDetailsInFeedbackEvent(Integer feedbackId, String contactNumber, String name) {
        FeedbackDetail feedback = manager.find(FeedbackDetail.class, feedbackId);
        feedback.setContactNumber(contactNumber);
        feedback.setCustomerName(name);
        manager.flush();
    }

    @Override
    public Integer addAudit(EventDetail event, AuditTokenInfo auditInfo) {
        Date currentTime = AppUtils.getCurrentTimestamp();
        Map<String, Integer> fieldMap = new HashMap<>();
        FeedbackForm form = getForm(fieldMap, event);
        Integer score = null;
        if (event.getFormResponse().getDefinition().getRating() != null) {
            for (FormResponseData data : event.getFormResponse().getAnswers()) {
                if (event.getFormResponse().getDefinition().getRating().getId() == data.getField().getId()) {
                    score = data.getNumber();
                    break;
                }
            }
        } else {
            score = event.getFormResponse().getHidden().getRating();
        }
        /*
         * FeedbackInfo info = createFeedback(fieldMap, currentTime, event, form,
         * tokenInfo, rating);
         *
         * markFeedbackEventCompletion(info, currentTime,
         * tokenInfo.getFeedbackEventId(), rating); markFeedbackCompletion(info,
         * currentTime, tokenInfo.getFeedbackId(), tokenInfo.getFeedbackSource(),
         * event.getFormResponse().getHidden().getFeedbackUnit(), rating);
         */
        return score;
    }

    private FeedbackForm getForm(Map<String, Integer> fieldMap, EventDetail event) {
        FeedbackForm form = findBySourceId(event.getFormResponse().getDefinition().getId());
        if (form == null) {
            form = new FeedbackForm();
            form.setFormSourceId(event.getFormResponse().getDefinition().getId());
            form.setFormTitle(event.getFormResponse().getDefinition().getTitle());
            manager.persist(form);
            manager.flush();
        }
        for (FormField field : event.getFormResponse().getDefinition().getFields()) {
            FeedbackField formField = findBySourceId(field.getId(), form.getFormId());
            if (formField == null) {
                formField = createField(form, field);
            } else {
                if (!field.getTitle().equals(formField.getFieldTitle())) {
                    formField.setFieldTitle(field.getTitle());
                    manager.flush();
                }
            }
            if (field.getType().equals(FieldType.rating.value())) {
                event.getFormResponse().getDefinition().setRating(field);
            }
            fieldMap.put(formField.getFeedbackSourceFieldId(), formField.getFeedbackFieldId());
        }
        return form;
    }

    private FeedbackField createField(FeedbackForm form, FormField field) {
        FeedbackField formField = new FeedbackField();
        formField.setFeedbackForm(form);
        formField.setFeedbackSourceFieldId(field.getId());
        formField.setFieldTitle(field.getTitle());
        formField.setFieldType(field.getType());
        manager.persist(formField);
        manager.flush();
        return formField;

    }

    private void createResponse(int feedbackInfoId, Map<String, Integer> fieldMap, FormResponseData response,
                                Integer rating) {
        FeedbackResponse feed = new FeedbackResponse();
        feed.setFeedbackFieldId(fieldMap.get(response.getField().getId()));
        feed.setResponseType(response.getType());
        feed.setFeedbackInfoId(feedbackInfoId);
        feed.setFeedbackRating(rating);
        manager.persist(feed);
        manager.flush();
        if (response.getChoice() != null && response.getChoice().getLabel() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getChoice().getLabel(), rating);
        }
        if (response.getChoices() != null && response.getChoices().getLabels() != null) {
            for (String label : response.getChoices().getLabels()) {
                createResponse(feed.getFeedbackResponseId(), label, rating);
            }
        }
        if (response.getNumber() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getNumber() + "", rating);
        }
        if (response.getText() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getText(), rating);
        }
        if (response.getEmail() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getEmail(), rating);
        }
        if (response.getUrl() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getUrl(), rating);
        }
        if (response.getFileUrl() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getFileUrl(), rating);
        }
        if (response.getBooleanData() != null) {
            createResponse(feed.getFeedbackResponseId(), response.getBooleanData() + "", rating);
        }

    }

    private void createResponse(int feedId, String responseData, Integer rating) {
        FeedbackResponseData data = new FeedbackResponseData();
        data.setFeedbackResponseId(feedId);
        data.setResponseData(responseData);
        data.setFeedbackRating(rating);
        manager.persist(data);
        manager.flush();
    }

    public FeedbackForm findBySourceId(String id) {
        Query query = manager.createQuery("FROM FeedbackForm E where E.formSourceId = :formSourceId");
        query.setParameter("formSourceId", id);
        try {
            Object o = query.getSingleResult();
            return o == null ? null : (FeedbackForm) o;
        } catch (NoResultException e) {
            return null;
        }
    }

    public FeedbackField findBySourceId(String id, int formId) {
        Query query = manager.createQuery(
                "FROM FeedbackField E where E.feedbackForm.formId = :formId and E.feedbackSourceFieldId = :feedbackSourceFieldId");
        query.setParameter("feedbackSourceFieldId", id);
        query.setParameter("formId", formId);
        try {
            Object o = query.getSingleResult();
            return o == null ? null : (FeedbackField) o;
        } catch (NoResultException e) {
            return null;
        }

    }

    private FeedbackInfo createFeedback(Map<String, Integer> fieldMap, Date currentTime, EventDetail event,
                                        FeedbackForm form, FeedbackTokenInfo tokenInfo, Integer rating) {
        FeedbackInfo info = new FeedbackInfo();
        info.setFeedbackId(tokenInfo.getFeedbackId());
        info.setFeedbackEventId(tokenInfo.getFeedbackEventId());
        info.setFeedbackTime(currentTime);
        info.setResponseId(event.getEventId());
        info.setFeedbackForm(form);
        info.setResponseToken(event.getFormResponse().getToken());
        info.setFeedbackRating(rating);
        manager.persist(info);
        manager.flush();
        for (FormResponseData data : event.getFormResponse().getAnswers()) {
            createResponse(info.getFeedbackInfoId(), fieldMap, data, info.getFeedbackRating());
        }
        return info;
    }

    private void markFeedbackEventCompletion(FeedbackInfo info, Date currentTime, int feedbackEventId, Integer rating) {
        FeedbackEvent feedbackEvent = manager.find(FeedbackEvent.class, feedbackEventId);
        feedbackEvent.setEventCompletionTime(currentTime);
        feedbackEvent.setEventStatus(FeedbackEventStatus.SUCCESSFUL.name());
        feedbackEvent.setLatestFeedbackInfoId(info.getFeedbackInfoId());
        if (rating != null) {
            feedbackEvent.setRating(rating);
        }
        Brand brand = masterCache.getBrandMetaData().get(feedbackEvent.getBrandId());
        ShortUrlData shortUrl = new ShortUrlData(feedbackEvent.getEventShortUrlId(), brand.getFeedBackUrl() + brand.getFeedbackEndpointRedirectUrl());
        // feedback.endpoint.redirect.url;
        try {
            SolsInfiniWebServiceClient.getTransactionalClient().updateShortUrl(shortUrl);
        } catch (Exception e) {
            LOG.error("Error while updating the short url : " + shortUrl, e);
        }
        manager.flush();
    }

    private void markOrderFeedbackEventCompletion(FeedbackInfo info, Date currentTime, int feedbackEventId, Integer rating, OrderDetailForFeedback orderDetailForFeedback) {
        FeedbackEvent feedbackEvent = manager.find(FeedbackEvent.class, feedbackEventId);
        feedbackEvent.setEventCompletionTime(currentTime);
        feedbackEvent.setEventStatus(FeedbackEventStatus.SUCCESSFUL.name());
        feedbackEvent.setLatestFeedbackInfoId(info.getFeedbackInfoId());
        if(orderDetailForFeedback.isSonr()){
            feedbackEvent.setRating(orderDetailForFeedback.getOnr());
            feedbackEvent.setRatingType(FeedbackRatingType.NPS_RATING.name());
            feedbackEvent.setMaxRating(10);
        }else if(orderDetailForFeedback.isSor()){
            feedbackEvent.setRating(orderDetailForFeedback.getOr());
            feedbackEvent.setRatingType(FeedbackRatingType.ORDER_RATING.name());
            feedbackEvent.setMaxRating(5);
        }
        Brand brand = masterCache.getBrandMetaData().get(feedbackEvent.getBrandId());
        ShortUrlData shortUrl = new ShortUrlData(feedbackEvent.getEventShortUrlId(), brand.getFeedBackUrl() + brand.getFeedbackEndpointRedirectUrl());
        // feedback.endpoint.redirect.url;
        try {
            SolsInfiniWebServiceClient.getTransactionalClient().updateShortUrl(shortUrl);
        } catch (Exception e) {
            LOG.error("Error while updating the short url : " + shortUrl, e);
        }
        manager.flush();
    }

    private void markFeedbackCompletion(FeedbackInfo info, Date currentTime, int feedbackId, String feedbackSource,
                                        Integer unit, Integer rating) {
        FeedbackDetail feedbackDetail = manager.find(FeedbackDetail.class, feedbackId);
        feedbackDetail.setFeedbackStatus(FeedbackStatus.COMPLETED.name());
        feedbackDetail.setFeedbackTime(currentTime);
        feedbackDetail.setLatestFeedbackInfoId(info.getFeedbackInfoId());
        feedbackDetail.setSource(feedbackSource);
        if (rating != null) {
            feedbackDetail.setRating(rating);
        }
        feedbackDetail.setFeedbackUnitId(unit);
        manager.flush();
    }

    private void markOrderFeedbackCompletion(FeedbackInfo info, Date currentTime, int feedbackId, String feedbackSource,
                                        Integer unit, Integer rating, OrderDetailForFeedback orderDetailForFeedback) {
        FeedbackDetail feedbackDetail = manager.find(FeedbackDetail.class, feedbackId);
        feedbackDetail.setFeedbackStatus(FeedbackStatus.COMPLETED.name());
        feedbackDetail.setFeedbackTime(currentTime);
        feedbackDetail.setLatestFeedbackInfoId(info.getFeedbackInfoId());
        feedbackDetail.setSource(feedbackSource);
        if(orderDetailForFeedback.isSonr()){
            feedbackDetail.setRating(orderDetailForFeedback.getOnr());
            feedbackDetail.setRatingType(FeedbackRatingType.NPS_RATING.name());
            feedbackDetail.setMaxRating(10);
        }else if(orderDetailForFeedback.isSor()){
            feedbackDetail.setRating(orderDetailForFeedback.getOr());
            feedbackDetail.setRatingType(FeedbackRatingType.ORDER_RATING.name());
            feedbackDetail.setMaxRating(5);
        }
        feedbackDetail.setFeedbackUnitId(unit);
        manager.flush();
    }

    @Override
    public boolean updateFeedbackDetail(Integer feedbackId, FeedbackStatus status) {
        FeedbackDetail feedbackDetail = manager.find(FeedbackDetail.class, feedbackId);
        if (feedbackDetail != null) {
            feedbackDetail.setFeedbackStatus(status.name());
            manager.flush();
            return true;
        }
        return false;
    }

    @Override
    public Date updateFeedbackEventStatus(int eventId, ShortUrlData shortUrl, String longUrl,
                                          FeedbackEventStatus status) {
        Date time = AppUtils.getCurrentTimestamp();
//        Query query = manager.createQuery("update FeedbackEvent E "
//                + "set E.eventStatus = :eventStatus,E.eventShortUrl = :eventShortUrl, E.eventShortUrlId = :eventShortUrlId, E.eventLongUrl = :eventLongUrl,  E.eventNotificationTime = :eventNotificationTime where E.feedbackEventId in (:feedbackEventId)");
//        query.setParameter("eventStatus", status.name());
//        query.setParameter("eventShortUrl", shortUrl.getUrl());
//        query.setParameter("eventShortUrlId", shortUrl.getId());
//        query.setParameter("eventLongUrl", longUrl);
//        query.setParameter("feedbackEventId", eventId);
//        query.setParameter("eventNotificationTime", time);
//        query.executeUpdate();
        FeedbackEvent feedbackEvent = manager.find(FeedbackEvent.class,eventId);
        if(feedbackEvent!=null){
            feedbackEvent.setEventStatus(status.name());
            feedbackEvent.setEventShortUrl(null);
            feedbackEvent.setEventShortUrlId(null);
            feedbackEvent.setEventLongUrl(longUrl);
            feedbackEvent.setEventNotificationTime(time);
            manager.flush();
        }
        return time;

    }

    public FeedbackOrderMetadata getOrderFeedbackDetail(int orderId, FeedbackSource source) {
        FeedbackOrderMetadata data = new FeedbackOrderMetadata();
        FeedbackDetail feedbackDetail = getFeedback(orderId, source, FeedbackEventType.REGULAR);

        if (feedbackDetail == null) {
            feedbackDetail = addFeedback(orderId);
        }
        if (feedbackDetail != null && feedbackDetail.getFeedbackStatus().equals(FeedbackStatus.CREATED.name())) {
            FeedbackEvent feedbackEvent = null;
            Query query1 = manager.createQuery(
                    "FROM FeedbackEvent f WHERE f.feedbackDetail.feedbackId = :feedbackId and f.eventSource = :eventSource and f.eventStatus = :eventStatus order by feedbackEventId desc");
            query1.setParameter("feedbackId", feedbackDetail.getFeedbackId());
            query1.setParameter("eventSource", source.name());
            query1.setParameter("eventStatus", FeedbackEventStatus.CREATED.name());
            OrderDetail orderDetail = manager.find(OrderDetail.class, orderId);
            try {
                List<FeedbackEvent> events = query1.getResultList();
                if (events == null || events.size() == 0) {
                    LOG.error("No feedback event found  for orderId {}. Creating one", orderId);
                    feedbackEvent = createFeedbackEvent(source, feedbackDetail.getOrderId(),
                            feedbackDetail.getOrderSource(), feedbackDetail, AppUtils.getCurrentTimestamp(),
                            FeedbackEventType.REGULAR, null, orderDetail.getBrandId());
                } else {
                    feedbackEvent = events.get(0);
                }
            } catch (NoResultException nre) {
                // LOG.error("No feedback event found for orderId {}. Creating
                // one", orderId);
                feedbackEvent = createFeedbackEvent(source, feedbackDetail.getOrderId(),
                        feedbackDetail.getOrderSource(), feedbackDetail, AppUtils.getCurrentTimestamp(),
                        FeedbackEventType.REGULAR, null, orderDetail.getBrandId());
            }
            FeedbackTokenInfo token = new FeedbackTokenInfo(feedbackDetail.getContactNumber(),
                    masterCache.getUnit(feedbackDetail.getUnitId()).getReferenceName(),
                    feedbackDetail.getCustomerName(), feedbackDetail.getOrderId(), feedbackDetail.getOrderSource(),
                    source.name(), feedbackDetail.getFeedbackId(), feedbackEvent.getFeedbackEventId());
            data.setFeedbackEventId(feedbackEvent.getFeedbackEventId());
            data.setFeedbackToken(tokenService.createToken(token, -1L));
            data.setProducts(getProductList(feedbackDetail.getProductIds()));
            data.setUnitId(feedbackDetail.getUnitId());
            data.setUnitName(masterCache.getUnitBasicDetail(feedbackDetail.getUnitId()).getReferenceName());
            data.setOrderDay(new SimpleDateFormat("EEEE").format(feedbackDetail.getFeedbackCreationTime()));
            data.setOrderSource(feedbackDetail.getOrderSource());
            data.setFeedbackStatus(FeedbackStatus.valueOf(feedbackDetail.getFeedbackStatus()));
            data.setOrderTime(
                    AppUtils.getFormattedTime(feedbackDetail.getFeedbackCreationTime(), "dd MMMMM yyyy hh:mm aaa"));
            return data;
        }
        return null;
    }

    @Override
    public FeedbackDetail getFeedback(int orderId, FeedbackSource source, FeedbackEventType eventType) {
        Query query = manager
                .createQuery("FROM FeedbackDetail f WHERE f.orderId = :orderId AND f.eventType = :eventType");
        query.setParameter("orderId", orderId);
        query.setParameter("eventType", eventType.name());
        FeedbackDetail feedbackDetail = null;

        try {
            feedbackDetail = (FeedbackDetail) query.getSingleResult();
        } catch (NoResultException nre) {
            LOG.error("No {} feedback detail for orderId {}", source.name(), orderId);
        }
        return feedbackDetail;
    }


    private FeedbackDetail addFeedback(int orderId) {
        OrderDetail orderDetail = manager.find(OrderDetail.class, orderId);
        if (AppUtils.getDaysDiff(orderDetail.getBillingServerTime(), AppUtils.getCurrentTimestamp()) <= 90) {
            CustomerInfo customerInfo = manager.find(CustomerInfo.class, orderDetail.getCustomerId());
            Set<Integer> productIds = new HashSet<>();
            for (OrderItem item : orderDetail.getOrderItems()) {
                productIds.add(item.getProductId());
            }
            return createFeedbackData(orderId, orderDetail.getUnitId(), orderDetail.getOrderSource(), productIds,
                    customerInfo.getCustomerId(), customerInfo.getEmailId(), customerInfo.getContactNumber(),
                    customerInfo.getFirstName(), AppUtils.getCurrentTimestamp(), FeedbackEventType.REGULAR);
        }
        return null;

    }

    private List<IdCodeName> getProductList(String productIdStr) {
        List<IdCodeName> idCodeNames = new ArrayList<>();
        if (productIdStr == null || productIdStr.trim().equals("")) {
            return idCodeNames;
        }
        String[] productIds = productIdStr.split(",");
        int index = 0;
        for (String productId : productIds) {
            if (index < 3) {
                ProductBasicDetail productDetail = masterCache.getProductBasicDetail(Integer.valueOf(productId));
                if (includedCategories.contains(productDetail.getType())) {
                    index++;
                    IdCodeName detail = productDetail.getDetail();
                    detail.setType(String.valueOf(productDetail.getType()));
                    idCodeNames.add(productDetail.getDetail());
                }
            } else {
                break;
            }

        }
        return idCodeNames;
    }

    @Override
    public FeedbackDetail generateFeedbackData(int orderId, int unitId, String orderSource, Set<Integer> productIds,
                                               Customer customer, Date currentTimestamp, FeedbackEventType eventType, FeedbackSource... eventSource) {
        FeedbackDetail feedback = createFeedbackData(orderId, unitId, orderSource, productIds, customer.getId(),
                customer.getEmailId(), customer.getContactNumber(), customer.getFirstName(), currentTimestamp,
                eventType);
        generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, eventSource);
        return feedback;
    }

    @Override
    public void generateFeedBackEvent(int orderId, String orderSource, Date currentTimestamp,
                                      FeedbackEventType eventType, FeedbackDetail feedback, FeedbackSource[] eventSource) {
        for (FeedbackSource sources : eventSource) {
            OrderDetail orderDetail = manager.find(OrderDetail.class, orderId);
            feedback.getFeedbackEvents().add(
                    createFeedbackEvent(sources, orderId, orderSource, feedback, currentTimestamp, eventType, null, orderDetail.getBrandId()));
        }
    }

    public FeedbackDetail createFeedbackData(int orderId, int unitId, String orderSource, Set<Integer> productIds,
                                             int customerId, String emailId, String contactNumber, String name, Date currentTimestamp,
                                             FeedbackEventType eventType) {
        FeedbackDetail feedback = new FeedbackDetail();
        feedback.setCustomerId(customerId);
        feedback.setFeedbackStatus(FeedbackStatus.CREATED.name());
        feedback.setOrderId(orderId);
        feedback.setUnitId(unitId);
        feedback.setOrderSource(orderSource);
        feedback.setEmailId(emailId);
        feedback.setContactNumber(contactNumber);
        feedback.setCustomerName(name);
        feedback.setFeedbackCreationTime(currentTimestamp);
        feedback.setProductIds(StringUtils.join(productIds, ","));
        feedback.setEventType(eventType.name());
        if(FeedbackEventType.ORDER_FEEDBACK.equals(eventType)){
            if(props.getIsShowNpsRating()){
                feedback.setRatingType(FeedbackRatingType.NPS_RATING.name());
                feedback.setMaxRating(10);
            }else {
                feedback.setRatingType(FeedbackRatingType.ORDER_RATING.name());
                feedback.setMaxRating(5);
            }
        }else{
            feedback.setRatingType(FeedbackRatingType.NPS_RATING.name());
            feedback.setMaxRating(10);
        }
        manager.persist(feedback);
        manager.flush();
        return feedback;
    }

    @Override
    public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource,
                                             FeedbackDetail feedback, Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId) {
        FeedbackEvent event = new FeedbackEvent();
        event.setEventSource(source.name());
        event.setFeedbackDetail(feedback);
        event.setEventGenerationTime(currentTimestamp);
        event.setEventType(eventType.name());
        event.setRating(rating);
        Date triggerTime = getTriggerTime(source, orderSource, currentTimestamp, eventType);
        event.setEventTriggerTime(triggerTime);
        event.setEventStatus(FeedbackEventStatus.CREATED.name());
        event.setBrandId(brandId);
        if(FeedbackEventType.ORDER_FEEDBACK.equals(eventType)){
            if(props.getIsShowNpsRating()){
                event.setRatingType(FeedbackRatingType.NPS_RATING.name());
                event.setMaxRating(10);
            }else {
                event.setRatingType(FeedbackRatingType.ORDER_RATING.name());
                event.setMaxRating(5);
            }
        }else{
            event.setRatingType(FeedbackRatingType.NPS_RATING.name());
            event.setMaxRating(10);
        }
        manager.persist(event);
        manager.flush();
        return event;
    }

    @Override
    public Date getTriggerTime(FeedbackSource source, String orderSource, Date currentTimestamp,
                               FeedbackEventType eventType) {
        Date threshold = AppUtils.getTimeOfDay(AppUtils.getCurrentDate(), props.getThresholdFeedbackMessageDelay());
        Date triggerTime = null;
        if (FeedbackEventType.ELABORATED.equals(eventType)) {
            triggerTime = AppUtils.getTimeOfDay(currentTimestamp, 0);
        } else if (FeedbackEventType.NPS.equals(eventType)|| FeedbackEventType.NPS_OFFER.equals(eventType) || FeedbackEventType.ORDER_FEEDBACK.equals(eventType)) {
            if (orderSource.equals(UnitCategory.COD.name())) {
                triggerTime = AppUtils.getTimeOfDay(currentTimestamp, props.getDeliveryNPSMessageDelay());
            } else {
                triggerTime = AppUtils.getTimeOfDay(currentTimestamp, props.getDineInNPSMessageDelay());
            }
        } else {
            if (source.equals(FeedbackSource.POS)) {
                triggerTime = AppUtils.getTimeOfDay(currentTimestamp, 0);
            } else {
                if (currentTimestamp.before(threshold)) {
                    if (orderSource.equals(UnitCategory.COD.name())) {
                        triggerTime = AppUtils.getTimeOfDay(currentTimestamp, props.getDeliveryFeedbackMessageDelay());
                    } else {
                        triggerTime = AppUtils.getTimeOfDay(currentTimestamp, props.getDineInFeedbackMessageDelay());
                    }
                } else {
                    triggerTime = AppUtils.getTimeOfDay(AppUtils.getNextDate(AppUtils.getCurrentBusinessDate()),
                            props.getNextDayFeedbackMessageDelay());
                }
            }
        }
        return triggerTime;
    }

    @Override
    public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource, int feedbackId,
                                             Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId) {
        FeedbackDetail feedback = manager.find(FeedbackDetail.class, feedbackId);
        return createFeedbackEvent(source, orderId, orderSource, feedback, currentTimestamp, eventType, rating, brandId);
    }

    @Override
    public void updateFeedbackData(FeedbackFrequency frequency) {
        Date currentDate = AppUtils.getCurrentDate();
        if (frequency.equals(FeedbackFrequency.CUMULATIVE)) {
            Query query = manager.createNativeQuery("CALL OVERALL_CONSOLIDATED_CSAT_SCORE(:currentDate)");
            query.setParameter("currentDate", currentDate);
            query.executeUpdate();
        } else {
            Date orderStartTime = AppUtils.getStartOfDayIST(AppUtils.getOldDate(currentDate, frequency.getOrderDays()));
            Date orderEndTime = AppUtils.getEndOfDayIST(AppUtils.getOldDate(currentDate, 1));
            Date feedbackStartTime = AppUtils
                    .getStartOfDayIST(AppUtils.getOldDate(currentDate, frequency.getFeedbackDays()));
            Date feedbackEndTime = AppUtils.getEndOfDayIST(AppUtils.getOldDate(currentDate, 1));
            // LOG.info("Feedback calculated for {} :", frequency.name());
            // LOG.info("Orders Calculated between {} and {} :", orderStartTime,
            // orderEndTime);
            // LOG.info("Feedback Calculated between {} and {} :",
            // feedbackStartTime, feedbackEndTime);
            Query query = manager.createNativeQuery("CALL DAILY_CONSOLIDATED_CSAT_SCORE"
                    + "(:currentDate, :orderStartTime,:orderEndTime,:feedbackStartTime,:feedbackEndTime,"
                    + ":orderDays,:feedbackDays,:frequency)");
            query.setParameter("currentDate", currentDate);
            query.setParameter("orderStartTime", orderStartTime);
            query.setParameter("orderEndTime", orderEndTime);
            query.setParameter("feedbackStartTime", feedbackStartTime);
            query.setParameter("feedbackEndTime", feedbackEndTime);
            query.setParameter("orderDays", frequency.getOrderDays());
            query.setParameter("feedbackDays", frequency.getFeedbackDays());
            query.setParameter("frequency", frequency.name());
            query.executeUpdate();
        }

    }

    public static void main(String[] args) {
        Date currentDate = AppUtils.getCurrentDate();
        Date orderStartTime = AppUtils
                .getStartOfDayIST(AppUtils.getOldDate(currentDate, FeedbackFrequency.WEEKLY.getOrderDays()));
        Date orderEndTime = AppUtils.getEndOfDayIST(AppUtils.getOldDate(currentDate, 1));
        Date feedbackStartTime = AppUtils
                .getStartOfDayIST(AppUtils.getOldDate(currentDate, FeedbackFrequency.WEEKLY.getFeedbackDays()));
        Date feedbackEndTime = AppUtils.getEndOfDayIST(AppUtils.getOldDate(currentDate, 1));
        LOG.info("Feedback calculated for {} :", FeedbackFrequency.WEEKLY.name());
        LOG.info("Orders Calculated between  {}  and {} :", orderStartTime, orderEndTime);
        LOG.info("Feedback Calculated between  {}  and {} :", feedbackStartTime, feedbackEndTime);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.customer.dao.FeedbackManagementDao#addOrderNPSDetail
     * (com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm)
     */
    @Override
    public Integer addOrderNPSDetail(WebEngageSurveyForm form) {
        if (form.getResponse().getData().getCustomData().getOrderId() == null
                || form.getResponse().getData().getCustomData().getOrderId().length == 0) {
            return -1;
        }

        String generatedOrderId = StringUtils.leftPad(form.getResponse().getData().getCustomData().getOrderId()[0], 16,
                "0");
        OrderDetail order = getOrderDetailObject(generatedOrderId);
        OrderNPSDetail nps = new OrderNPSDetail();
        nps.setSurveyCreationTime(AppUtils.getCurrentTimestamp());
        nps.setUnitName(masterCache.getUnitBasicDetail(order.getUnitId()).getName());
        nps.setGeneratedOrderId(generatedOrderId);
        nps.setContactNumber(form.getResponse().getData().getCustomData().getMobileNumber()[0]);
        nps.setRating((Integer) form.getResponse().getData().getQuestionResponses()[0].getValue().getValue());
        if (form.getResponse().getData().getQuestionResponses().length > 1) {
            nps.setQuestion((String) form.getResponse().getData().getQuestionResponses()[1].getQuestionText());
            nps.setResponse(form.getResponse().getData().getQuestionResponses()[1].getValue().getValue() != null
                    ? (String) form.getResponse().getData().getQuestionResponses()[1].getValue().getValue()
                    : null);
        }
        if (order != null) {
            nps.setCustomerId(order.getCustomerId());
            nps.setOrderId(order.getOrderId());
            nps.setUnitId(order.getUnitId());
        }
        manager.persist(nps);
        manager.flush();
        return nps.getSurveryResponseId();
    }

    public Integer addOrderNPSDetail(FeedbackDetail event, Integer rating, EventDetail feedback) {

        OrderDetail order = manager.find(OrderDetail.class, event.getOrderId());
        OrderNPSDetail nps = new OrderNPSDetail();
        nps.setSurveyCreationTime(AppUtils.getCurrentTimestamp());
        nps.setUnitName(masterCache.getUnitBasicDetail(order.getUnitId()).getName());
        nps.setGeneratedOrderId(order.getGeneratedOrderId());
        nps.setContactNumber(event.getContactNumber());
        nps.setRating(rating);
        for (OrderItem item : order.getOrderItems()) {
            customerService.addCustomerProductFeedback(order.getCustomerId(), item.getProductId(), rating,
                    order.getOrderId(), "NPS");
        }
        Map<String, String> questions = new HashMap<>();
        for (FormField feild : feedback.getFormResponse().getDefinition().getFields()) {
            if (FieldType.rating.name().equals(feild.getType())) {
                continue;
            }
            for (FormResponseData answer : feedback.getFormResponse().getAnswers()) {
                if (feild.getId().equals(answer.getField().getId())) {
                    questions.put(feild.getTitle(), getAnswer(answer));
                }
            }
        }
        if (!questions.isEmpty()) {
            nps.setQuestion(questions.keySet().stream().findFirst().get());
            nps.setResponse(questions.get(nps.getQuestion()));
        }
        if (order != null) {
            nps.setCustomerId(order.getCustomerId());
            nps.setOrderId(order.getOrderId());
            nps.setUnitId(order.getUnitId());
        }
        manager.persist(nps);

        if (!questions.isEmpty()) {
            for (String q : questions.keySet()) {
                OrderNPSResponseData e = new OrderNPSResponseData();
                e.setSurveryResponseId(nps.getSurveryResponseId());
                e.setResponse(questions.get(q));
                e.setQuestion(q);
                manager.persist(e);
                nps.getQuestions().add(e);
            }
        }

        manager.flush();
        sendSMSAndSlack(nps, event, masterCache.getUnit(order.getUnitId()).getCafeManager().getName(),
                order);
        return nps.getSurveryResponseId();
    }

    @Override
    public void addNpsDetailForOrderFeedback(OrderDetailForFeedback detail, CustomerInfo info){
        OrderNPSDetail nps = new OrderNPSDetail();
        nps.setOrderId(detail.getOid());
        nps.setUnitId(detail.getUid());
        nps.setCustomerId(detail.getCid());
        nps.setSurveyCreationTime(AppUtils.getCurrentTimestamp());
        nps.setUnitName(masterCache.getUnitBasicDetail(detail.getUid()).getName());
        nps.setGeneratedOrderId(detail.getGoid());
        nps.setContactNumber(info.getContactNumber());
        nps.setQuestion(detail.getNq());
        if(detail.isSonr()) {
            nps.setResponse(detail.getOnr().toString());
            nps.setRating(detail.getOnr());
        }
        if(detail.isSor()){
            nps.setResponse(detail.getOr().toString());
            nps.setRating(detail.getOr());
        }
        manager.persist(nps);
        List<OrderNPSResponseData> orderNPSResponseDataList = new ArrayList<>();
        OrderNPSResponseData e = new OrderNPSResponseData();
        e.setSurveryResponseId(nps.getSurveryResponseId());
        if(detail.isSor()){
            e.setResponse(detail.getOr().toString());
            e.setQuestion(detail.getOfq());
        }
        if(detail.isSor()) {
            e.setResponse(detail.getOnr().toString());
            e.setQuestion(detail.getNq());
        }
        for(OrderFeedbackQuestionResponse res : detail.getYnq()) {
            OrderNPSResponseData npsResponseData = new OrderNPSResponseData();
            npsResponseData.setSurveryResponseId(nps.getSurveryResponseId());
            if(StringUtils.isBlank(res.getR())){
                npsResponseData.setResponse(null);
            }else {
                npsResponseData.setResponse(res.getR());
            }
            npsResponseData.setQuestion(res.getQ());
            orderNPSResponseDataList.add(npsResponseData);
            nps.getQuestions().add(e);
            manager.persist(npsResponseData);
        }
        for(OrderFeedbackQuestionResponse res : detail.getTrq()) {
            OrderNPSResponseData npsResponseData = new OrderNPSResponseData();
            npsResponseData.setSurveryResponseId(nps.getSurveryResponseId());
            if(StringUtils.isBlank(res.getR())){
                npsResponseData.setResponse(null);
            }else {
                npsResponseData.setResponse(res.getR());
            }
            //npsResponseData.setResponse(res.getR());
            npsResponseData.setQuestion(res.getQ());
            orderNPSResponseDataList.add(npsResponseData);
            nps.getQuestions().add(e);
            manager.persist(npsResponseData);
        }
        for(OrderFeedbackQuestionResponse res : detail.getMcq()) {
            OrderNPSResponseData npsResponseData = new OrderNPSResponseData();
            npsResponseData.setSurveryResponseId(nps.getSurveryResponseId());
            if(StringUtils.isBlank(res.getR())){
                npsResponseData.setResponse(null);
            }else {
                npsResponseData.setResponse(res.getR());
            }
            //npsResponseData.setResponse(res.getR());
            npsResponseData.setQuestion(res.getQ());
            orderNPSResponseDataList.add(npsResponseData);
            nps.getQuestions().add(e);
            manager.persist(npsResponseData);
         }

        persistNpsResponseData(orderNPSResponseDataList,nps,e,detail.getErq());
        manager.persist(e);
        nps.getQuestions().add(e);
        manager.flush();
    }

    private void persistNpsResponseData(List<OrderNPSResponseData> orderNPSResponseDataList,OrderNPSDetail nps ,OrderNPSResponseData e , List<OrderFeedbackQuestionResponse> response){
        for(OrderFeedbackQuestionResponse res : response) {
            OrderNPSResponseData npsResponseData = new OrderNPSResponseData();
            npsResponseData.setSurveryResponseId(nps.getSurveryResponseId());
            if(StringUtils.isBlank(res.getR())){
                npsResponseData.setResponse(null);
            }else {
                npsResponseData.setResponse(res.getR());
            }
            //npsResponseData.setResponse(res.getR());
            npsResponseData.setQuestion(res.getQ());
            orderNPSResponseDataList.add(npsResponseData);
            nps.getQuestions().add(e);
            manager.persist(npsResponseData);
        }
    }

    private void sendSMSAndSlack(OrderNPSDetail nps, FeedbackDetail event, String managerName, OrderDetail order) {
        if (nps != null && nps.getQuestions() != null && !nps.getQuestions().isEmpty() && nps.getRating() <= 8) {
            for (OrderNPSResponseData data : nps.getQuestions()) {
                if (data.getQuestion() != null && data.getQuestion().contains(AppConstants.CALL_BACK_QUESTION)
                        && data.getResponse() != null && Boolean.parseBoolean(data.getResponse().trim())) {
                    FeedbackEvent feedbackEvent = getAssociatedFeedBackEvent(event.getFeedbackId(), FeedbackEventType.NPS.name());
                    FeedbackEventInfo feedbackEventInfo = DataConverter.convert(masterCache, feedbackEvent);
                    sendCallBackAssuredSMS(nps, event, feedbackEventInfo);
                    sendCallBackRequiredSlack(nps, event.getCustomerName(), managerName, order, feedbackEventInfo);
                }
            }
        }
    }

    private void sendCallBackRequiredSlack(OrderNPSDetail nps, String customerName, String managerName,
                                           OrderDetail order, FeedbackEventInfo feedbackEventInfo) {
        try {
            StringBuilder text = new StringBuilder("Customer Requested CallBack\n");
            text.append(addNewLineChar("Customer Name", customerName));
            text.append(addNewLineChar("Application Name", masterCache.getChannelPartnerMap().get(order.getChannelPartnerId()) != null ?
                    masterCache.getChannelPartnerMap().get(order.getChannelPartnerId()).getName() : ""));
            text.append(addNewLineChar("Brand Name", feedbackEventInfo.getBrand().getBrandName()));
            text.append(addNewLineChar("Contact Number", nps.getContactNumber()));
            text.append(addNewLineChar("Rating", String.valueOf(nps.getRating())));
            text.append(addNewLineChar("Order Id", nps.getGeneratedOrderId()));
            if (order != null) {
                text.append(addNewLineChar("Source", order.getOrderSource()));
                text.append(addNewLineChar("Amount", String.valueOf(order.getSettledAmount())));
                text.append(addNewLineChar("Products", ""));
                for (OrderItem item : order.getOrderItems()) {
                    text.append(addNewLineChar(item.getProductName() + " - " + item.getDimension(),
                            item.getQuantity() + ""));
                }
            }
            text.append(addNewLineChar("Unit Name", nps.getUnitName()));
            text.append(addNewLineChar("Patch Name", managerName));
            for (OrderNPSResponseData data : nps.getQuestions()) {
                text.append(addNewLineChar("Q", data.getQuestion()));
                text.append(addNewLineChar("A", data.getResponse()));
            }
            text.append("Please call customer within 48 hrs of this msg.");

//            SlackNotificationService.getInstance().send(props.getEnvironmentType(), null, SlackNotification.CUSTOMER_CALL_BACK,
//                    text.toString());
            SlackNotificationService.getInstance().
                    sendNotification(props.getEnvironmentType(), ApplicationName.KETTLE_SERVICE.name(), SlackNotification.CUSTOMER_CALL_BACK,text.toString());
        } catch (Exception e) {
            LOG.error("CALL BACK SLACK FAILED", e);
        }
    }

    @Override
    public void sendCallBackNpsSlack(OrderDetailForFeedback nps, CustomerInfo customerInfo, String managerName,
                                           OrderDetail order) {
        try {
            StringBuilder text = new StringBuilder("Customer Requested CallBack\n");
            text.append("Cafe : "+nps.getUn()+" - "+nps.getUid()+" ("+nps.getBn()+") \n");
            text.append("Customer : "+nps.getCn() +" ("+customerInfo.getContactNumber()+") "+" ("+customerInfo.getEmailId()+") \n");
            text.append("Order Id and Amount : "+order.getGeneratedOrderId()+" - "+order.getTotalAmount().toString()+"\n");
            text.append("Channel Partner and Order Source : "+nps.getCpn()+" - "+nps.getOs()+"\n");
            text.append("Order Rating and Comment : \n");
            if(nps.isSonr()){
                text.append("  -> Recommend Chaayos to his/her friends and family as : "+nps.getOnr()+"/10\n");
            }else{
                text.append("  -> Recommend Chaayos to his/her friends and family as : NA\n");
            }
            if(nps.isSor()){
                text.append("  -> Order Rating : "+nps.getOr()+"/5\n");
            }else{
                text.append("  -> Order Rating : NA\n");
            }
            text.append("  -> Order Comment : "+nps.getOc()+"\n");
            text.append("Product Rating and Comment : \n");
            for(FeedbackOrderItem feedbackOrderItem : nps.getFiol()){
                text.append("  -> "+ feedbackOrderItem.getIn()+"-"+feedbackOrderItem.getD()+"(x"+feedbackOrderItem.getQt()+")");
                text.append("    ("+feedbackOrderItem.getIc()+")  ");
                if(feedbackOrderItem.getIt()!=null){
                    text.append("("+feedbackOrderItem.getIt()+")");
                }
                text.append("   Rating - "+feedbackOrderItem.getIr()+"/5\n");
            }
            text.append("Please call customer within 48 hrs of this msg.");
            LOG.info(text.toString());
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),null,SlackNotification.CUSTOMER_CALL_BACK,
                    text.toString());
        } catch (Exception e) {
            LOG.error("CALL BACK SLACK FAILED FOR NEW ORDER FEEDBACK", e);
        }
    }

    @Override
    public OrderDetail getOrderDetail(Integer orderId) {
        return manager.find(OrderDetail.class,orderId);
    }

    private String addNewLineChar(String key, String value) {
        return key + ": " + value + "\n";
    }

    private FeedbackEvent getAssociatedFeedBackEvent(int feedbackId, String eventType) {
        Query query = manager.createQuery("FROM FeedbackEvent f WHERE f.feedbackDetail.feedbackId = :feedbackId and f.eventType = :eventType");
        query.setParameter("feedbackId", feedbackId);
        query.setParameter("eventType", eventType);
        List<FeedbackEvent> list = query.getResultList();
        if (list == null) {
            return null;
        }
        return list.get(0);
    }

    private void sendCallBackAssuredSMS(OrderNPSDetail nps, FeedbackDetail event, FeedbackEventInfo feedbackEventInfo) {
        try {
            String message = CustomerSMSNotificationType.CALLBACK_ASSURED.getMessage(feedbackEventInfo);
            SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(feedbackEventInfo.getBrand());
            notificationService.sendNotification(CustomerSMSNotificationType.CALLBACK_ASSURED.name(), message,
                    nps.getContactNumber(),
                    smsWebServiceClient,
                    props.getAutomatedNPSSMS(),null);
        } catch (Exception e) {
            LOG.error("Error while sending the callback assured for NPS message to {}", nps.getContactNumber(), e);
        }
    }

    private String getAnswer(FormResponseData answer) {
        ResponseType responseType;
        if (AppConstants.BOOLEAN.equalsIgnoreCase(answer.getType())) {
            responseType = ResponseType.bool;
        } else {
            responseType = ResponseType.valueOf(answer.getType());
        }
        String str = "";
        switch (responseType) {
            case text:
                str = answer.getText();
                break;
            case short_text:
                str = answer.getText();
                break;
            case number:
                str = String.valueOf(answer.getNumber());
                break;
            case bool:
                str = String.valueOf(answer.getBooleanData());
                break;
            case choice:
                str = answer.getChoice().getLabel();
                break;
            case email:
                str = String.valueOf(answer.getEmail());
                break;
            case url:
                str = String.valueOf(answer.getUrl());
                break;
            case phone_number:
                str = String.valueOf(answer.getPhoneNumber());
                break;
            case choices:
                if (answer.getChoices() != null && answer.getChoices().getLabels() != null) {
                    Collections.sort(answer.getChoices().getLabels());
                    str = StringUtils.join(answer.getChoices().getLabels(), "|");
                }
                break;
            default:
                break;
        }
        return str;
    }

    private OrderDetail getOrderDetailObject(String generatedOrderId) {
        Query query = manager.createQuery("FROM OrderDetail E where E.generatedOrderId = :generatedOrderId");
        query.setParameter("generatedOrderId", generatedOrderId);
        OrderDetail order = (OrderDetail) query.getSingleResult();
        return order;
    }

    @Override
    public void runNpsProc() {
        Date currentDate = AppUtils.getCurrentBusinessDate();
        Query query = manager.createNativeQuery("CALL SP_CALCULATE_MONTHLY_NPS_SCORE(:currentDate)");
        query.setParameter("currentDate", currentDate);
        query.executeUpdate();
    }

    @Override
    public boolean cancelFeedBackforOrder(Integer orderId, FeedbackEventType eventType) {
        boolean result = false;
        Query query = manager.createQuery(
                "FROM FeedbackDetail f WHERE f.orderId = :orderId AND f.eventType = :eventType AND f.feedbackStatus = :feedbackStatus");
        query.setParameter("orderId", orderId);
        query.setParameter("eventType", eventType.name());
        query.setParameter("feedbackStatus", FeedbackStatus.CREATED.name());
        FeedbackDetail feedbackDetail = null;
        try {
            feedbackDetail = (FeedbackDetail) query.getSingleResult();
        } catch (NoResultException nre) {
            result = false;
        }
        if (feedbackDetail != null) {
            Query query1 = manager.createQuery(
                    "UPDATE FeedbackEvent f SET eventStatus = :eventStatus WHERE f.feedbackDetail.feedbackId = :feedbackId and f.eventType = :eventType");
            query1.setParameter("eventStatus", FeedbackEventStatus.CANCELLED.name());
            query1.setParameter("feedbackId", feedbackDetail.getFeedbackId());
            query1.setParameter("eventType", eventType.name());
            query1.executeUpdate();
            feedbackDetail.setFeedbackStatus(FeedbackStatus.CANCELLED.name());
            manager.flush();
            result = true;
        }
        return result;
    }

    @Override
    public boolean availableForNPSEvent(int customerId, Date triggerTime) {
        Query query = manager.createQuery(
                "FROM FeedbackEvent f WHERE f.feedbackDetail.customerId = :customerId AND f.eventType = :eventType "
                        + " AND f.eventStatus <> :eventStatus ORDER BY f.eventTriggerTime DESC");
        query.setParameter("customerId", customerId);
        query.setParameter("eventType", FeedbackEventType.NPS.name());
        query.setParameter("eventStatus", FeedbackEventStatus.CANCELLED.name());
        query.setMaxResults(1);
        FeedbackEvent feedbackEvent = null;
        try {
            feedbackEvent = (FeedbackEvent) query.getSingleResult();
            if (feedbackEvent == null) {
                return true;
            }
            return AppUtils.checkNPSApplicable(triggerTime, feedbackEvent.getEventTriggerTime());

        } catch (NoResultException nre) {
            // nothing to do here
        }
        return true;
    }

    @Override
    public List<FeedbackEventInfo> getNotifiedNPSEventsForLastDay(FeedbackSource source) {
        List<FeedbackEventInfo> result = new ArrayList<>();
        Date businessDate = AppUtils.getPreviousDate(AppUtils.getBusinessDate());
        Date startTime = AppUtils.getStartOfBusinessDay(businessDate);
        Date endTime = AppUtils.getEndOfBusinessDay(businessDate);
        Query query = manager
                .createQuery("FROM FeedbackEvent E where E.feedbackDetail.orderSource = :orderSource AND"
                        + " E.eventSource = :eventSource AND E.eventStatus = :eventStatus AND E.eventType = :eventType"
                        + " AND E.feedbackDetail.feedbackStatus  = :feedbackStatus AND E.eventNotificationTime >= :startTriggerTime AND E.eventTriggerTime < :endTriggerTime ");
        query.setParameter("feedbackStatus", FeedbackStatus.NOTIFIED.name());
        query.setParameter("orderSource", AppConstants.COD);
        query.setParameter("eventSource", source.name());
        query.setParameter("startTriggerTime", startTime);
        query.setParameter("endTriggerTime", endTime);
        query.setParameter("eventType", FeedbackEventType.NPS.name());
        query.setParameter("eventStatus", FeedbackEventStatus.NOTIFIED.name());
        List<FeedbackEvent> list = query.getResultList();
        if (list != null && list.size() > 0) {
            for (FeedbackEvent event : list) {
                result.add(DataConverter.convert(masterCache, event));
            }
        }
        return result;
    }

    @Override
    public FeedbackEventInfo getFeedbackEventInfo(int feedbackId, FeedbackSource qr) {
        Query query = manager.createQuery(
                "FROM FeedbackEvent E where E.eventSource = :eventSource and E.feedbackDetail.feedbackId = :feedbackId order by feedbackEventId desc");
        query.setParameter("eventSource", qr.name());
        query.setParameter("feedbackId", feedbackId);
        List<FeedbackEvent> list = query.getResultList();
        if (list != null && list.size() > 0) {
            return DataConverter.convert(masterCache, list.get(0));
        }
        return null;
    }

    @Override
    public FeedbackEvent getFeedbackEvent(int feedbackId) {
        Query query = manager.createQuery(
                "FROM FeedbackEvent E where E.feedbackDetail.feedbackId = :feedbackId");
        query.setParameter("feedbackId", feedbackId);
        List<FeedbackEvent> eventList = query.getResultList();
        FeedbackEvent event = null;
        if(Objects.nonNull(eventList) && !eventList.isEmpty()){
            for(FeedbackEvent e : eventList){
                if(Objects.nonNull(e.getEventLongUrl()) && !e.getEventLongUrl().isEmpty()){
                    event = e;
                    break;
                }
            }
        }
        return event;
    }

    @Override
    public boolean updateCustomerInfoInFeedbackData(int feedbackId, int customerId, String emailId) {
        Query query = manager.createQuery(
                "update FeedbackDetail E set customerId = :customerId, emailId = :emailId where E.feedbackId = :feedbackId");
        query.setParameter("customerId", customerId);
        query.setParameter("emailId", emailId);
        query.setParameter("feedbackId", feedbackId);
        query.executeUpdate();
        manager.flush();
        return true;
    }

	@Override
	public Integer getOrderId(Integer feedbackId) {
		FeedbackDetail feedbackDetail = manager.find(FeedbackDetail.class, feedbackId);
		return feedbackDetail.getOrderId();
	}

    @Override
    public OrderFeedbackResponse getResponseForFeedbackId(Integer feedbackId){
        Query query = manager.createQuery("FROM OrderFeedbackResponse ofr WHERE ofr.feedbackId = :feedbackId");
        query.setParameter("feedbackId",feedbackId);
        try{
            OrderFeedbackResponse orderFeedbackResponse = (OrderFeedbackResponse) query.getSingleResult();
            return orderFeedbackResponse;
        }catch (NoResultException e){
            LOG.info("No OrderFeedbackResponse found for feedbackId : {}",feedbackId);
        }
        return null;
    }

    @Override
    public boolean updateOrderFeedbackEvent(OrderDetailForFeedback feedbackData, Integer rating) {
        try {
            Date currentTime = AppUtils.getCurrentTimestamp();
            FeedbackDetail feedBackDetail = manager.find(FeedbackDetail.class, feedbackData.getFid());
            if (feedBackDetail != null && !FeedbackStatus.COMPLETED.name().equals(feedBackDetail.getFeedbackStatus())) {
                for(FeedbackEvent event : feedBackDetail.getFeedbackEvents()){
                    markOrderFeedbackEventCompletion(new FeedbackInfo(), AppUtils.getCurrentTimestamp(), event.getFeedbackEventId(), rating, feedbackData);
                }
                markOrderFeedbackCompletion(new FeedbackInfo(), currentTime, feedbackData.getFid(), null,
                        null, rating,feedbackData);
            }
        } catch (Exception e) {
            LOG.error("Error updating feedback detail", e);
        }

        return false;
    }

    @Override
    public FeedbackDetail getFeedbackForSource(Integer orderId, FeedbackSource source) {
        Query query = manager
                .createQuery("FROM FeedbackDetail f WHERE f.orderId = :orderId AND f.source = :source");
        query.setParameter("orderId", orderId);
        query.setParameter("source", source.name());
        FeedbackDetail feedbackDetail = null;

        try {
            feedbackDetail = (FeedbackDetail) query.getSingleResult();
        } catch (NoResultException nre) {
            LOG.error("No {} feedback detail for orderId {}", source.name(), orderId);
        }
        return feedbackDetail;
    }


    @Override
    public FeedbackEventInfo getPendingNPSEventsForCustomer(FeedbackSource source, Integer orderId , Integer customerId) {
        try{
            Query query = manager.createQuery(
                    "FROM FeedbackEvent E where E.eventSource = :eventSource and E.eventStatus = :eventStatus and E.eventType IN (:eventType)"
                            + " and E.feedbackDetail.feedbackStatus  = :feedbackStatus and E.feedbackDetail.orderId = :orderId and E.feedbackDetail.customerId = :customerId");
            query.setParameter("feedbackStatus", FeedbackStatus.CREATED.name());
            query.setParameter("eventSource", source.name());
            query.setParameter("orderId", orderId);
            query.setParameter("eventType", Arrays.asList(FeedbackEventType.NPS.name(),FeedbackEventType.NPS_OFFER.name(),FeedbackEventType.ORDER_FEEDBACK.name()));
            query.setParameter("eventStatus", FeedbackEventStatus.CREATED.name());
            query.setParameter("customerId",customerId);
            FeedbackEvent feedbackEvent = (FeedbackEvent) query.getSingleResult();
            return DataConverter.convert(masterCache, feedbackEvent);
        }catch (NoResultException e){
            LOG.error("No feedback event found for customer {} with orderId {}", customerId,orderId,e);
            return  null;
        }
    }
}
