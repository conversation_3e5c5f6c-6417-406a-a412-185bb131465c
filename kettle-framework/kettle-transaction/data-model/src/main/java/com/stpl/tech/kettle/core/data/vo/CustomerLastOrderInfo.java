package com.stpl.tech.kettle.core.data.vo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomerLastOrderInfo {
    Integer productID;
    String dimension;
    Integer quantity;
    String productName;
    //productId, addons List
    Map<Integer,List< List<String> > > productAddons=new HashMap<>();

    public Map<Integer, List<List<String>>> getProductAddons() {
        return productAddons;
    }

    public void setProductAddons(Map<Integer, List<List<String>>> productAddons) {
        this.productAddons = productAddons;
    }

    public Integer getProductID() {
        return productID;
    }

    public void setProductID(Integer productID) {
        this.productID = productID;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }


}
