/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.monk.simulation.model.MonkStatusData;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationResultData;
import com.stpl.tech.kettle.core.monk.simulation.model.SimulationTimeData;

/**
 * <AUTHOR>
 *
 */
public class FifoMonkRouter {

	int noOfMonks;
	List<MonkStatusData> data = new ArrayList<>();
	Date timeOfNextMonkAvailability;
	int nextAvailableMonk;

	public FifoMonkRouter(int noOfMonks) {
		super();
		this.noOfMonks = noOfMonks;
		for (int i = 1; i <= noOfMonks; i++) {
			data.add(new MonkStatusData(i, "FREE"));
		}
	}

	public boolean isThereAMonkAvailable(Date time) {
		return timeOfNextMonkAvailability.before(time);
	}

	public Date whenIsTheNextMonkAvailable() {
		return timeOfNextMonkAvailability;
	};

	public int whichIsTheNextMonk() {
		return nextAvailableMonk;
	}

	public void route(SimulationResultData r, boolean withFluctuation, int transitionTime) {
		Collections.sort(data);
		freeUpMonks(r.orderTime);
		int idealMonks = getFreeMonkCount();
		Date startTime = null;
		MonkStatusData monk = getFreeMonk();
		if (monk == null) {
			monk = data.get(0);
			startTime = monk.freeTime;
		} else {
			startTime = r.orderTime;
		}
		SimulationTimeData t = r.setStartTime(startTime, noOfMonks, monk.monkId, idealMonks, withFluctuation,
				transitionTime);
		monk.freeTime = t.endTime;
		monk.status = "RUNNING";
	}

	/**
	 * @return
	 */
	private MonkStatusData getFreeMonk() {
		for (MonkStatusData d : data) {
			if (d.status == "FREE") {
				return d;
			}
		}
		return null;
	}

	/**
	 * @return
	 */
	private int getFreeMonkCount() {
		int count = 0;
		for (MonkStatusData d : data) {
			if (d.status == "FREE") {
				count = count + 1;
			}
		}
		return count;
	}

	/**
	 * 
	 */
	private void freeUpMonks(Date date) {
		data.forEach(item -> {
			if (item.freeTime.before(date)) {
				item.status = "FREE";
			}
		});
	}

}
