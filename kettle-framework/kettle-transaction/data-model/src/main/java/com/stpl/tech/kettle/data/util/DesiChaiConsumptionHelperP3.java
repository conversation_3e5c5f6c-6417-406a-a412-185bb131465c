/**
 *
 */
package com.stpl.tech.kettle.data.util;

import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ExtendedConsumable;
import com.stpl.tech.master.domain.model.Pair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class DesiChaiConsumptionHelperP3 extends DesiChaiConsumptionHelper {

	public static final String CONSUMABLE_DATA_CREATOR = "CC1_CONSUMABLE_DATA_CREATOR";
	public static final String CONSUMABLE_DATA_CREATOR_DOUBLE_CHECK = "CC1_CONSUMABLE_DATA_CREATOR_DOUBLE_CHECK";
	public static final String SCM_PRODUCT_DATA_CREATOR = "CC1_SCM_PRODUCT_DATA_CREATOR";
	public static final String SCM_PRODUCT_DATA_CREATOR_DOUBLE_CHECK = "CC1_SCM_PRODUCT_DATA_CREATOR_DOUBLE_CHECK";

	private Map<Integer, ExtendedConsumable> scmProducts;
	private Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables;


	public Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables() {
		if (consumables == null) {
			synchronized (CONSUMABLE_DATA_CREATOR) {
				if (consumables == null) {
					synchronized (CONSUMABLE_DATA_CREATOR_DOUBLE_CHECK) {
						consumables = new HashMap<>();
						for (Integer id : products) {
							consumables.put(id, new HashMap<>());
							if (id == DESI_CHAI || id == DESI_CHAI_DASH_MILK || id == DESI_CHAI_DOUBLE_DASH_MILK
									|| id == FULL_DOODH || id == DESI_DOODH_KAM || id == DESI_PAANI_KAM
									|| id == LEMON_GRASS_GINGER || id == LEMON_GRASS_GINGER_DOODH_KUM
									|| id == LEMON_GRASS_GINGER_FULL_DOODH || id == LEMON_GRASS_GINGER_PAANI_KUM) {
								consumables.get(id).put(REGULAR, new HashMap<>());
								consumables.get(id).put(FULL, new HashMap<>());
								consumables.get(id).put(MINI_KETLI, new HashMap<>());
								consumables.get(id).put(TEA_FOR_2, new HashMap<>());
								consumables.get(id).put(TEA_FOR_4, new HashMap<>());
								consumables.get(id).put(CHOTI_KETLI, new HashMap<>());
								consumables.get(id).put(BADI_KETLI, new HashMap<>());
							} else if (id == CUTTING_CHAI) {
								consumables.get(id).put(NONE, new HashMap<>());
							} else if (id == KULHAD_CHAI) {
								consumables.get(id).put(NONE, new HashMap<>());
								consumables.get(id).put(MINI_KETLI, new HashMap<>());
								consumables.get(id).put(CHOTI_KETLI, new HashMap<>());
								consumables.get(id).put(BADI_KETLI, new HashMap<>());
							}
							if (id == CUTTING_CHAI || id == KULHAD_CHAI) {
								for (String key : consumables.get(id).keySet()) {
									consumables.get(id).get(key).put(REGULAR_PATTI_REGULAR_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(REGULAR_PATTI_NO_SUGAR, new ArrayList<>());
								}
							} else if (id == DESI_CHAI || id == DESI_CHAI_DASH_MILK || id == DESI_CHAI_DOUBLE_DASH_MILK
									|| id == FULL_DOODH || id == DESI_DOODH_KAM || id == DESI_PAANI_KAM
									|| id == LEMON_GRASS_GINGER || id == LEMON_GRASS_GINGER_DOODH_KUM
									|| id == LEMON_GRASS_GINGER_FULL_DOODH || id == LEMON_GRASS_GINGER_PAANI_KUM) {
								for (String key : consumables.get(id).keySet()) {
									consumables.get(id).get(key).put(REGULAR_PATTI_REGULAR_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(REGULAR_PATTI_NO_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(KADAK_REGULAR_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(KADAK_NO_SUGAR, new ArrayList<>());
								}
							}

							/*if (id == DESI_CHAI || id == LEMON_GRASS_GINGER || id == DESI_DOODH_KAM
									|| id == LEMON_GRASS_GINGER_DOODH_KUM || id == DESI_CHAI_DASH_MILK
									|| id == DESI_CHAI_DOUBLE_DASH_MILK) {
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101717, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102551, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102550, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102550, new BigDecimal("1.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102597, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102597, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102597, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102597, new BigDecimal("5.00"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));
							}*/
							if (id == DESI_CHAI || id == LEMON_GRASS_GINGER) {
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101717, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101717, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));
							}

							if (id == DESI_PAANI_KAM || id == LEMON_GRASS_GINGER_PAANI_KUM || id == FULL_DOODH
									|| id == LEMON_GRASS_GINGER_FULL_DOODH) {
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101722, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101722, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101724, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101724, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101723, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101723, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101723, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101723, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101723, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101723, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101722, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101722, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101722, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101722, new BigDecimal("5.00"))));
							}

							if (id == DESI_DOODH_KAM || id == LEMON_GRASS_GINGER_DOODH_KUM) {
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101717, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101717, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));
							}

							if (id == DESI_CHAI_DASH_MILK || id == DESI_CHAI_DOUBLE_DASH_MILK) {
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102597, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102597, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102551, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102551, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102550, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102550, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102550, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102550, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102550, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102550, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102597, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102597, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102597, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102597, new BigDecimal("5.00"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101727, new BigDecimal("1.00"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101721, new BigDecimal("1.00"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("1.00"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("2.00"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103468, new BigDecimal("2.00"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));
							}

							if (id == CUTTING_CHAI) {
								consumables.get(id).get(NONE).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102599, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101728, new BigDecimal("1.00"))));
								consumables.get(id).get(NONE).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102599, new BigDecimal("1.00"))));

							}

							if (id == KULHAD_CHAI) {
								consumables.get(id).get(NONE).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101722, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("1.00"))));
								consumables.get(id).get(NONE).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101722, new BigDecimal("1.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101722, new BigDecimal("2.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101722, new BigDecimal("2.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101722, new BigDecimal("5.00")),
												new Pair<Integer, BigDecimal>(101725, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101722, new BigDecimal("5.00"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101723, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101723, new BigDecimal("1.00"))));

							}

						}
					}
				}
			}
		}
		return consumables;
	}

	private Consumable getConsumable(int productId, String name, String uom) {
		Consumable c = new Consumable();
		c.setName(name);
		c.setProductId(productId);
		c.setUom(uom);
		return c;
	}

	public Map<Integer, ExtendedConsumable> scmProducts() {
		if (scmProducts == null) {
			synchronized (SCM_PRODUCT_DATA_CREATOR) {
				if (scmProducts == null) {
					synchronized (SCM_PRODUCT_DATA_CREATOR_DOUBLE_CHECK) {
						scmProducts = new HashMap<>();
						scmProducts.put(101715, getConsumable(101715, "Chai Sachet DRC-White-Regular-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101716, getConsumable(101716, "Chai Sachet DRC-White-MK-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101717, getConsumable(101717, "Chai Sachet DRC-White-Full-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101718, getConsumable(101718, "Chai Sachet MSC-Yellow-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(103467, getConsumable(103467, "Chai Sachet Green Reg - HYD", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(103468, getConsumable(103468, "Chai Sachet DRK-Green-MK-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101721, getConsumable(101721, "Chai Sachet DRK-Green-Full-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101722, getConsumable(101722, "Chai Sachet PKK-Sky Blue-Regular-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101723, getConsumable(101723, "Chai Sachet PKK-Sky Blue-MK-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101724, getConsumable(101724, "Chai Sachet PKK-Sky Blue-Full-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101725, getConsumable(101725, "Sugar Sachet-Regular", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(101726, getConsumable(101726, "Sugar Sachet-MK", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(101727, getConsumable(101727, "Sugar Sachet-Full", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(101728, getConsumable(101728, "Sugar Sachet-None", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(102597, getConsumable(102597, "Chai Sachet-DRC-White-DEL-Only patti", "SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(102599, getConsumable(102599, "Chai Sachet-MSC-Yellow-DEL-Only patti", "SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(102551, getConsumable(102551, "Chai sachet-DRC-White-F-DEL", "SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(102550, getConsumable(102550, "Chai sachet-DRC-White-MK-DEL", "SACHET", DesiChaiConsumptionHelper.PATTI));
					}
				}
			}
		}
		return scmProducts;
	}
}
