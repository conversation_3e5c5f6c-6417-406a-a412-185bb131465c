package com.stpl.tech.kettle.google.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class GoogleCustomerOrderData {

    private String contactNumber;
    private Date transactionTime;
    private BigDecimal orderTaxableAmount;
    private String generatedOrderId;
    private String gclid;

    public GoogleCustomerOrderData() {

    }

    public GoogleCustomerOrderData(String contactNumber, Date transactionTime, BigDecimal orderTaxableAmount, String generatedOrderId, String gclid) {
        super();
        this.contactNumber = contactNumber;
        this.transactionTime = transactionTime;
        this.orderTaxableAmount = orderTaxableAmount;
        this.generatedOrderId = generatedOrderId;
        this.gclid = gclid;
    }

    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    public void setGeneratedOrderId(String generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    public BigDecimal getOrderTaxableAmount() {
        return orderTaxableAmount;
    }

    public void setOrderTaxableAmount(BigDecimal orderTaxableAmount) {
        this.orderTaxableAmount = orderTaxableAmount;
    }

    public Date getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(Date transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getGclid() {
        return gclid;
    }

    public void setGclid(String gclid) {
        this.gclid = gclid;
    }
}