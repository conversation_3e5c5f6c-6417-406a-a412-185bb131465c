/**
 *
 */
package com.stpl.tech.kettle.core.data.vo;

import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class CustomerCashInfo {

	private boolean hasCash;
	private BigDecimal cashAmount;
	private SubscriptionInfoDetail subscriptionInfoDetail;

	public CustomerCashInfo() {
		super();
	}

	public CustomerCashInfo(BigDecimal cashAmount) {
		super();
		this.hasCash = cashAmount != null && BigDecimal.ZERO.compareTo(cashAmount) < 0;
		this.cashAmount = cashAmount == null ? BigDecimal.ZERO : cashAmount;
	}

	public CustomerCashInfo(BigDecimal cashAmount,SubscriptionInfoDetail subscriptionInfoDetail) {
		super();
		this.hasCash = cashAmount != null && BigDecimal.ZERO.compareTo(cashAmount) < 0;
		this.cashAmount = cashAmount == null ? BigDecimal.ZERO : cashAmount;
		this.subscriptionInfoDetail=subscriptionInfoDetail;
	}

	public boolean isHasCash() {
		return hasCash;
	}

	public void setHasCash(boolean hasCash) {
		this.hasCash = hasCash;
	}

	public BigDecimal getCashAmount() {
		return cashAmount;
	}

	public void setCashAmount(BigDecimal cashAmount) {
		this.cashAmount = cashAmount;
	}

	public SubscriptionInfoDetail getSubscriptionInfoDetail() {
		return subscriptionInfoDetail;
	}

	public void setSubscriptionInfoDetail(SubscriptionInfoDetail subscriptionInfoDetail) {
		this.subscriptionInfoDetail = subscriptionInfoDetail;
	}
}
