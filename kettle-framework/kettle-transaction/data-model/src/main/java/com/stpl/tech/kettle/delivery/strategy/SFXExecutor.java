/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.adapter.SFXRequestAdapter;
import com.stpl.tech.kettle.delivery.adapter.SFXResponseAdapter;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.delivery.model.SFXErrorResponse;
import com.stpl.tech.kettle.delivery.model.SFXRequest;
import com.stpl.tech.kettle.delivery.model.SFXResponse;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Unit;
import org.apache.http.HttpResponse;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;

public class SFXExecutor extends AbstractDeliveryStrategy implements DeliveryExecutionStrategy {

	private static Logger LOG = LoggerFactory.getLogger(SFXExecutor.class);

	private SFXRequest request = new SFXRequest();
	private SFXRequestAdapter requestAdapter = new SFXRequestAdapter();
	private SFXResponse response = new SFXResponse();
	private SFXResponseAdapter responseAdapter = new SFXResponseAdapter();
	private SFXErrorResponse error = new SFXErrorResponse();

	private static final String DEV_CALLBACK = "http://uat.kettle.chaayos.com:9191/kettle-service/rest/v1/delivery-management/update/B326yxW0FJXGxa2fE1r1+A==";

	@Override
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props) {

		request = requestAdapter.adaptCreate(order);

		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			LOG.info("environment type is {}", props.getEnvironmentType());
			request.setStore_Code("chaayos001");
			request.setCallbackUrl(DEV_CALLBACK);
		}

		HttpResponse responseFromRequest = createRequest(creationEndpoint, request);
		return readResponse(responseFromRequest, order.getOrder().getGenerateOrderId());
	}

	@Override
	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo,
										   EnvironmentProperties props) {
		JSONObject jsonObject = new JSONObject();
		try {
			jsonObject.put("status", DeliveryStatus.CANCELLED.name());
			cancelationEndpoint = replaceInEndPoint(cancelationEndpoint, "<sfx_order_id>", taskId);
			LOG.info("Cancellation endpoint is :::: {}", cancelationEndpoint);
			return readResponse(putRequest(cancelationEndpoint, jsonObject), orderInfo.getOrder().getGenerateOrderId());
		} catch (JSONException e) {
			LOG.error("JSON exception", e);
		}
		return null;
	}

	@Override
	public void setAuthorizationObject(AuthorizationObject authorization) {
		this.authorization = authorization;
	}

	@Override
	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props) {
		// TODO integrate API level unit on boarding for ShadowFax
		return null;
	}

	@Override
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId) {

		try {
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()) {
				response = WebServiceHelper.convertResponse(responseFromRequest, SFXResponse.class);
				return responseAdapter.adapt(response, orderId);
			} else {
				error = WebServiceHelper.convertResponse(responseFromRequest, SFXErrorResponse.class);
				return responseAdapter.adaptError(error, orderId);
			}
		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}

		return null;
	}

}
