package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.CustomerEventDetailData;
import com.stpl.tech.kettle.customer.dao.CustomerBirthdayDetailDao;
import com.stpl.tech.master.util.CustomerEventType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Slf4j
@Repository
public class CustomerBirthdayDetailDaoImpl extends AbstractMasterDaoImpl implements CustomerBirthdayDetailDao {

    @Override
    public List<CustomerEventDetailData> findByCustomerId(Integer customerId){
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CustomerEventDetailData data where data.customerId = :customerId");
            query.setParameter("customerId",customerId);
            List<CustomerEventDetailData> data = (List<CustomerEventDetailData>) query.getSingleResult();
            log.info("Fetching Customer event details Data took : {}", System.currentTimeMillis() - startTime);
            return data;
        }catch (Exception e){
            log.info("customer not existed with customerId : {}",customerId);
            return null;
        }
    }

    @Override
    public CustomerEventDetailData findByCustomerIdAndEventType(Integer customerId, CustomerEventType eventType) {
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CustomerEventDetailData data where data.customerId = :customerId and data.customerEventType = :eventType");
            query.setParameter("customerId",customerId);
            query.setParameter("eventType", eventType);
            CustomerEventDetailData data = (CustomerEventDetailData) query.getSingleResult();
            log.info("Fetching Customer {} detail Data took : {}",eventType, System.currentTimeMillis() - startTime);
            return data;
        }catch (Exception e){
            log.info("customer not existed with customerId : {}",customerId);
            return null;
        }
    }
    @Override
    public CustomerEventDetailData findByContactNumberAndEventType(String contactNumber, CustomerEventType eventType) {
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CustomerEventDetailData data where data.contactNumber = :contactNumber and data.customerEventType = :eventType");
            query.setParameter("contactNumber",contactNumber);
            query.setParameter("eventType", eventType);
            CustomerEventDetailData data = (CustomerEventDetailData) query.getSingleResult();
            log.info("Fetching Customer {} detail Data took : {}",eventType, System.currentTimeMillis() - startTime);
            return data;
        }catch (Exception e){
            log.info("customer not existed with contact number : {}",contactNumber);
            return null;
        }
    }

    @Override
    public List<CustomerEventDetailData> findByEventMonth(Integer eventMonth){
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CustomerEventDetailData data where data.eventMonth = :eventMonth");
            query.setParameter("eventMonth",eventMonth);
            List<CustomerEventDetailData> data = query.getResultList();
            //CustomerBirthdayDetailData data = (CustomerBirthdayDetailData) query.getSingleResult();
            log.info("Fetching Customer Event detail Data took : {}", System.currentTimeMillis() - startTime);
            return data;
        }catch (Exception e){
            log.info("Error in fetching Event details for event month : {} and getting exception : {}",eventMonth,e);
            return null;
        }
    }

    @Override
    public List<CustomerEventDetailData> findByEventMonthAndEventType(Integer eventMonth, CustomerEventType eventType) {
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CustomerEventDetailData data where data.eventMonth = :eventMonth and data.customerEventType = :eventType ");
            query.setParameter("eventMonth",eventMonth);
            query.setParameter("eventType",eventType);
            List<CustomerEventDetailData> data = query.getResultList();
            //CustomerBirthdayDetailData data = (CustomerBirthdayDetailData) query.getSingleResult();
            log.info("Fetching Customer {} detail Data took : {}",eventType, System.currentTimeMillis() - startTime);
            return data;
        }catch (Exception e){
            log.info("Error in fetching Event detail for {} month : {} and getting exception : {}",eventMonth,eventType,e);
            return null;
        }
    }


}

