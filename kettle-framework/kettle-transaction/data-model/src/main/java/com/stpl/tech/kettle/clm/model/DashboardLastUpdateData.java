package com.stpl.tech.kettle.clm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "DASHBOARD_DATA_LAST_UPDATE")
public class DashboardLastUpdateData {
    private Integer id;
    private Date zfr;
    private Date zgv;
    private Date zors;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "ZOMATO_FOOD_RATING", nullable = true)
    public Date getZfr() {
        return zfr;
    }

    public void setZfr(Date zfr) {
        this.zfr = zfr;
    }

    @Column(name = "GRID_VISIBILITY", nullable = true)
    public Date getZgv() {
        return zgv;
    }

    public void setZgv(Date zgv) {
        this.zgv = zgv;
    }

    @Column(name = "FOOD_RATING_ZOMATO_ORS", nullable = true)
    public Date getZors() {
        return zors;
    }

    public void setZors(Date zors) {
        this.zors = zors;
    }
}

