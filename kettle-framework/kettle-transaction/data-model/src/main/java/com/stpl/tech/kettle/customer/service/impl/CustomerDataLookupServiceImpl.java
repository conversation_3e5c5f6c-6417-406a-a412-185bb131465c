package com.stpl.tech.kettle.customer.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.customer.dao.CustomerDataLookupDao;
import com.stpl.tech.kettle.customer.service.CustomerDataLookupService;
import com.stpl.tech.kettle.data.model.CustomerDataLookup;

@Service("customerDataLookupService")
public class CustomerDataLookupServiceImpl implements CustomerDataLookupService {

	@Autowired
	private CustomerDataLookupDao customerDataLookupDaol;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CustomerDataLookup> updateAll(List<CustomerDataLookup> dataLookups) {
		return customerDataLookupDaol.addAll(dataLookups);
	}

}
