/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.util.AppUtils;

import java.util.HashMap;
import java.util.Map;

public class UnitSessionCache {

	private final Map<UnitTerminalDetail, UnitSessionDetail> unitSessionMapping = new HashMap<UnitTerminalDetail, UnitSessionDetail>();

	private static final UnitSessionCache INSTANCE = new UnitSessionCache();

	private UnitSessionCache() {

	}

	public static UnitSessionCache getInstance() {
		return INSTANCE;
	}

	public UnitSessionDetail generateToken(UnitTerminalDetail unitTerminalDetail) {
		UnitSessionDetail detail = new UnitSessionDetail(unitTerminalDetail.getUnitId(),
				unitTerminalDetail.getTerminalId(), AppUtils.generateRandomOrderId());
		unitSessionMapping.put(unitTerminalDetail, detail);
		return detail;
	}

	public void setCustomer(UnitTerminalDetail unitTerminalDetail, Customer customer, boolean newCustomer) {
		UnitSessionDetail detail = unitSessionMapping.get(unitTerminalDetail);
		if (detail == null) {
			detail = generateToken(unitTerminalDetail);
		}
		detail.setCustomer(customer);
		detail.setNewCustomer(newCustomer);
	}

	public UnitSessionDetail get(UnitTerminalDetail unitTerminalDetail) {
		UnitSessionDetail detail = unitSessionMapping.get(unitTerminalDetail);
		if (detail == null) {
			detail = generateToken(unitTerminalDetail);
		}
		return detail;
	}

	@Override
	public String toString() {
		return "UnitSessionCache{" +
				"unitSessionMapping=" + unitSessionMapping.size() +
				'}';
	}
}
