package com.stpl.tech.kettle.core.data.vo;

import java.util.List;

import com.stpl.tech.kettle.data.model.UnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventAggregate;

public class StockOutReportData {

	List<UnitProductStockEventAggregate> aggregate;

	List<UnitProductStockData> events;

	public StockOutReportData() {

	}

	public StockOutReportData(List<UnitProductStockEventAggregate> aggregate, List<UnitProductStockData> events) {
		super();
		this.aggregate = aggregate;
		this.events = events;
	}

	public List<UnitProductStockEventAggregate> getAggregate() {
		return aggregate;
	}

	public void setAggregate(List<UnitProductStockEventAggregate> aggregate) {
		this.aggregate = aggregate;
	}

	public List<UnitProductStockData> getEvents() {
		return events;
	}

	public void setEvents(List<UnitProductStockData> events) {
		this.events = events;
	}

}
