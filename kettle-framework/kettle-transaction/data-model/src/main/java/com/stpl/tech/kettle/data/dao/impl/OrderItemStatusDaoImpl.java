package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.dao.OrderItemStatusDao;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderItemStatus;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class OrderItemStatusDaoImpl extends AbstractDaoImpl implements OrderItemStatusDao {

    private static final Logger LOG = LoggerFactory.getLogger(OrderItemStatusDaoImpl.class);

    @Autowired
    private MasterDataCache masterCache;

    @Override
    public void initiateOrderItemStatus(OrderDetail orderDetail, Integer tableRequestId) throws DataUpdationException {
        LOG.info("initiate order item status for Table Request id :{} and order id : {} ",tableRequestId,orderDetail.getOrderId());
        try {
            List<OrderItemStatus> orderItemStatusList = new ArrayList<>();
            for(com.stpl.tech.kettle.data.model.OrderItem orderItem :  orderDetail.getOrderItems()) {
                Product product = masterCache.getProduct(orderItem.getProductId());
                if (!AppConstants.SERVICE_CHARGE_SUBTYPE.equals(product.getSubType())
                        && !AppUtils.isGiftCard(orderItem.getTaxCode())
                        && !Objects.nonNull(masterCache.getSubscriptionProductDetail(orderItem.getProductId()))) {
                    String orderItemStatus = OrderStatus.CREATED.value();
                    if (AppConstants.YES.equalsIgnoreCase(orderItem.getIsHoldOn())) {
                        orderItemStatus = OrderStatus.ON_HOLD.value();
                    }
                    orderItemStatusList.add(OrderItemStatus.builder().tableRequestId(tableRequestId).orderId(orderDetail.getOrderId()).orderItemId(orderItem.getOrderItemId())
                            .itemCreationTime(AppUtils.getCurrentTimestamp()).status(orderItemStatus).build());
                }
            }
            addAll(orderItemStatusList);
        }catch (Exception e){
            LOG.error("Error while initiating order Item Statuses : {} ", e);
        }
    }
}
