package com.stpl.tech.kettle.core.data.vo;

import java.util.Date;

import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.master.domain.model.Brand;

public class FeedbackEventInfo {

	private Integer feedbackEventId;
	private String eventStatus;
	private String eventSource;
	private String eventLongUrl;
	private String eventShortUrl;
	private String eventShortUrlId;
	private Date eventGenerationTime;
	private Date eventNotificationTime;
	private Date eventTriggerTime;
	private Date eventCompletionTime;
	private Integer feedbackId;
	private int customerId;
	private String contactNumber;
	private String emailId;
	private String orderSource;
	private Integer unitId;
	private String unitName;
	private Integer orderId;
	private Integer rating;
	private String customerName;
	private FeedbackEventType type;
	private Brand brand;

	public Integer getFeedbackEventId() {
		return feedbackEventId;
	}

	public void setFeedbackEventId(Integer feedbackEventId) {
		this.feedbackEventId = feedbackEventId;
	}

	public String getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

	public String getEventSource() {
		return eventSource;
	}

	public void setEventSource(String eventSource) {
		this.eventSource = eventSource;
	}

	public String getEventLongUrl() {
		return eventLongUrl;
	}

	public void setEventLongUrl(String eventLongUrl) {
		this.eventLongUrl = eventLongUrl;
	}

	public String getEventShortUrl() {
		return eventShortUrl;
	}

	public void setEventShortUrl(String eventShortUrl) {
		this.eventShortUrl = eventShortUrl;
	}

	public Date getEventGenerationTime() {
		return eventGenerationTime;
	}

	public void setEventGenerationTime(Date eventGenerationTime) {
		this.eventGenerationTime = eventGenerationTime;
	}

	public Date getEventNotificationTime() {
		return eventNotificationTime;
	}

	public void setEventNotificationTime(Date eventNotificationTime) {
		this.eventNotificationTime = eventNotificationTime;
	}

	public Date getEventTriggerTime() {
		return eventTriggerTime;
	}

	public void setEventTriggerTime(Date eventTriggerTime) {
		this.eventTriggerTime = eventTriggerTime;
	}

	public Date getEventCompletionTime() {
		return eventCompletionTime;
	}

	public void setEventCompletionTime(Date eventCompletionTime) {
		this.eventCompletionTime = eventCompletionTime;
	}

	public Integer getFeedbackId() {
		return feedbackId;
	}

	public void setFeedbackId(Integer feedbackId) {
		this.feedbackId = feedbackId;
	}

	public int getCustomerId() {
		return customerId;
	}

	public void setCustomerId(int customerId) {
		this.customerId = customerId;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getEventShortUrlId() {
		return eventShortUrlId;
	}

	public void setEventShortUrlId(String eventShortUrlId) {
		this.eventShortUrlId = eventShortUrlId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	public FeedbackEventType getType() {
		return type;
	}

	public void setType(FeedbackEventType type) {
		this.type = type;
	}

	public Brand getBrand() {
		return brand;
	}

	public void setBrand(Brand brand) {
		this.brand = brand;
	}
}
