/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.MetadataManagementDao;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.kettle.data.model.ManualBillBookData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.ManualBillBookStatus;
import com.stpl.tech.master.domain.model.ManualBillBook;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.Unit;

@Repository
public class MetadataManagementDaoImpl extends AbstractDaoImpl implements MetadataManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(MetadataManagementDaoImpl.class);

	@Autowired
	private DeliveryRequestService deliveryService;

	@Override
	public List<PartnerDetail> getAllDeliveyPartnersForUnit(int unitId) {
		List<PartnerDetail> list = new ArrayList<>();
		Query query = manager.createQuery("FROM UnitToDeliveryPartnerMappings E Where E.unitId = :unitId");
		query.setParameter("unitId", unitId);
		@SuppressWarnings("unchecked")
		List<UnitToDeliveryPartnerMappings> resultList = query.getResultList();
		Optional.of(resultList).ifPresent(p -> p.forEach(q -> list.add(DataConverter.convert(q))));
		return list;
	}

	@Override
	public boolean updateDeliveryPartnerForUnit(Unit unit) {

		try {
			List<PartnerDetail> oldList = getAllDeliveyPartnersForUnit(unit.getId());
			List<PartnerDetail> newList = unit.getDeliveryPartners();

			// Update existing partners and add new partners
			newList.forEach(p -> {
				if (oldList.contains(p)) {
					updatePriority(p, p.getPriority());
				} else {
					addMapping(p, unit);
				}
			});

			// deactivate unused partners
			oldList.forEach(p -> {
				if (!newList.contains(p)) {
					updatePriority(p, 0);
				}
			});

			deliveryService.refreshPriorityCache();
			return true;

		} catch (Exception e) {
			LOG.error("Unable to update Delivery partners for unit {}", unit.getId(), e);
		}
		return false;
	}

	private void updatePriority(PartnerDetail detail, int i) {
		UnitToDeliveryPartnerMappings mapping = find(UnitToDeliveryPartnerMappings.class, detail.getMappingId());
		mapping.setPriority(i);
		update(mapping);
	}

	private void addMapping(PartnerDetail detail, Unit unit) {
		UnitToDeliveryPartnerMappings mapping = new UnitToDeliveryPartnerMappings(unit.getId(),
				manager.find(DeliveryPartner.class, detail.getDetail().getId()), detail.getPriority());
		add(mapping);
	}
	
	
	@Override
	public List<ManualBillBookData> getManualBillBookDetail(int unitId, boolean getAll) {
		Query query = null;
		if(getAll){
			query = manager.createQuery(
					"FROM ManualBillBookData g WHERE g.unitId = :unitId order by g.billBookId desc");
			query.setParameter("unitId", unitId);
			
		}else{
			List<String> statusList = new ArrayList<>();
			statusList.add(ManualBillBookStatus.ACTIVATED.value());
			statusList.add(ManualBillBookStatus.CREATED.value());
			query = manager.createQuery(
					"FROM ManualBillBookData g WHERE g.unitId = :unitId AND g.status IN(:statusList) ");
			query.setParameter("unitId", unitId);
			query.setParameter("statusList", statusList);
		}
		return query.getResultList();
	}

	@Override
	public List<ManualBillBookData> validateManualBillBookDetail(ManualBillBook manualBillBook, int stateId) {
		Query query = manager.createQuery("FROM ManualBillBookData g WHERE g.stateId = :stateId and ((:startNo >= g.startNo and :startNo <= g.endNo) or (:endNo >= g.startNo and :endNo <= g.endNo))");
		query.setParameter("startNo", manualBillBook.getStartNo());
		query.setParameter("endNo", manualBillBook.getEndNo());
		query.setParameter("stateId", stateId);
        return query.getResultList();
	}
	
	@Override
	public List<ManualBillBookData> validateManualBillBookNo(int billBookNo,int unitId) {
		String stringQuery="FROM ManualBillBookData g WHERE g.startNo <= :billBookNo and g.endNo >= :billBookNo and g.unitId= :unitId and status= :status";
		Query query = manager.createQuery(stringQuery);
		query.setParameter("billBookNo",billBookNo );
		query.setParameter("unitId",unitId );
		query.setParameter("status", ManualBillBookStatus.ACTIVATED.value());
        return query.getResultList();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.data.dao.MetadataManagementDao#
	 * getAllOrdersForBillBook(int)
	 */
	@Override
	public List<OrderDetail> getAllOrdersForBillBook(int billBookId) {
		ManualBillBookData manualBillBook = manager.find(ManualBillBookData.class, billBookId);
		Query query = manager.createQuery(
				"FROM OrderDetail g WHERE g.unitId = :unitId and g.manualBillBookNo >= :startNo and g.manualBillBookNo <= :endNo order by g.manualBillBookNo");
		query.setParameter("startNo", manualBillBook.getStartNo());
		query.setParameter("endNo", manualBillBook.getEndNo());
		query.setParameter("unitId", manualBillBook.getUnitId());
		return query.getResultList();
	}

	@Override
	public ManualBillBookData getBillBook(int transferOrderId) {
		String stringQuery="FROM ManualBillBookData g WHERE g.transferOrderId = :transferOrderId ";
		Query query = manager.createQuery(stringQuery);
		query.setParameter("transferOrderId",transferOrderId );
        return (ManualBillBookData) query.getSingleResult();
	}
	
	

}