/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.customer.service.impl;

import com.stpl.tech.kettle.data.model.AuthorizationRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.customer.dao.AuthorizationDao;
import com.stpl.tech.kettle.customer.service.AuthorizationService;

/**
 * <AUTHOR>
 *
 */
@Service
public class AuthorizationServiceImpl implements AuthorizationService {

	@Autowired
	private AuthorizationDao dao;

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String createEmailAuthorizationRequest(String emailId, String code, String text) {
		return dao.createEmailAuthorizationRequest(emailId, code, text);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String createSMSAuthorizationRequest(String contactNumber, String code, String text) {
		return dao.createSMSAuthorizationRequest(contactNumber, code, text);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean exists(String email, String token) {
		return dao.exists(email, token);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void expireAuthorizationRequest(String email) {
		dao.expireAuthorizationRequest(email);
	}

    @Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String findAuthorizationToken(String email) {
        AuthorizationRequest request = dao.findAuthorizationToken(email);
        return request!=null ? request.getAuthorizationText() : null;
    }

}
