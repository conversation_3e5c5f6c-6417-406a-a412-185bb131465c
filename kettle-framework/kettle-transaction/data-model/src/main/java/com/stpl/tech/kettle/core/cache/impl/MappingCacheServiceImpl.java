package com.stpl.tech.kettle.core.cache.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import com.stpl.tech.kettle.core.cache.MappingCacheService;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.data.model.OfferDetailData;

@Service
public class MappingCacheServiceImpl implements MappingCacheService {

	@Autowired
	private OfferManagementDao dao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public SubscriptionOfferInfoDetail findSubscriptionDetail(String offerCode){
		OfferDetailData offerDetailData = dao.getOfferDetailDataFromCoupon(offerCode);
		SubscriptionOfferInfoDetail detail = new SubscriptionOfferInfoDetail(offerCode,offerDetailData.getValue());
		detail.setOfferText(offerDetailData.getOfferText());
		return detail;
	}

}
