package com.stpl.tech.kettle.clevertap.domain.model;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClevertapReceivedChaayosCashData implements Serializable {
    private static final long serialVersionUID = -3725556041735278224L;

    @JsonProperty("amount")
    @SerializedName("amount")
    private BigDecimal amount;
    @JsonProperty("cashMetadataType")
    @SerializedName("cashMetadataType")
    private String cashMetadataType;
    @JsonProperty("cashTransactionCode")
    @SerializedName("cashTransactionCode")
    private String cashTransactionCode;
    @JsonProperty("notify")
    @SerializedName("notify")
    private Boolean notify;
    @JsonProperty("creationDate")
    @SerializedName("creationDate")
    private String creationDate;
    @JsonProperty("expirationDate")
    @SerializedName("expirationDate")
    private String expirationDate;
    @JsonProperty("messageType")
    @SerializedName("messageType")
    private String messageType;
//    @JsonProperty("whatsappOptIn")
//    private Boolean whatsappOptIn;
    @JsonProperty("sendNotification")
    @SerializedName("sendNotification")
    private Boolean sendNotification;
    @JsonProperty("walletBalance")
    @SerializedName("walletBalance")
    private BigDecimal walletBalance;
    @JsonProperty("loyaltyBalance")
    @SerializedName("loyaltyBalance")
    private Integer loyaltyBalance;
    @JsonProperty ("chaayosCash")
    @SerializedName("chaayosCash")
    private BigDecimal chaayosCash;
    @JsonProperty("comment")
    @SerializedName("comment")
    private String comment;
//    @JsonProperty("payload")
//    private Map<String, String> payload;
//    @JsonProperty("contactNumber")
//    private String contactNumber;

}
