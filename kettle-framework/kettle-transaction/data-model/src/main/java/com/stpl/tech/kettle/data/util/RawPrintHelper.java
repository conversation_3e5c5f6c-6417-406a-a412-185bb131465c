package com.stpl.tech.kettle.data.util;

import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;

public class RawPrintHelper {

	public static final String FONT_A = "" + (char) 0x1B + (char) 0x4D + (char) 0x30;
	public static final String CENTER = "" + (char) 0x1B + (char) 0x61 + (char) 0x31;
	public static final String LEFT = "" + (char) 0x1B + (char) 0x61 + (char) 0x30;
	public static final String RIGHT = "" + (char) 0x1B + (char) 0x61 + (char) 0x32;
	public static final String LINE_BREAK = "" + (char) 0x0A;
	public static final String BOLD_ON = "" + (char) 0x1B + (char) 0x45 + (char) 0x0D;
	public static final String BOLD_OFF = "" + (char) 0x1B + (char) 0x45 + (char) 0x0A;
	public static final String STANDARD_FONT = "" + (char) 0x1D + (char) 0x21 + (char) 0x00;
	public static final String TALL_FONT = "" + (char) 0x1D + (char) 0x21 + (char) 0x01;
	public static final String WIDE_FONT = "" + (char) 0x1D + (char) 0x21 + (char) 0x10;
	public static final String DOUBLE_FONT = "" + (char) 0x1D + (char) 0x21 + (char) 0x11;
	public static final String CUT_PAPER = "" + (char) 0x1B + (char) 0x69;
	public static final String PARTIAL_CUT_PAPER = "" + (char) 0x1D + (char) 0x56 + (char) 0x01;
	public static final String SEPARATOR = "----------------------------------------------";
	public static final String TAB = "	";
	public static final String COMMA = ", ";
	public static final String SPACE = " ";
	public static final String SMALL_FONT = "" + (char) 0x1B + (char) 0x4D + (char) 0x31;


	public static final String QR_SIZE_BEGIN = "" + (char) 0x1D + (char) 0x28 + (char) 0x6B + (char) 0x04 + (char) 0x00
			+ (char) 0x31 + (char) 0x41 + (char) 0x32 + (char) 0x00 + (char) 0x1D + (char) 0x28 + (char) 0x6B
			+ (char) 0x03 + (char) 0x00 + (char) 0x31 + (char) 0x43 + (char) 0x09 + (char) 0x1D + (char) 0x28
			+ (char) 0x6B + (char) 0x03 + (char) 0x00 + (char) 0x31 + (char) 0x45 + (char) 0x30 + (char) 0x1D
			+ (char) 0x28 + (char) 0x6B;

	public static final String QR_SIZE_END = "" + (char) 0x31 + (char) 0x50 + (char) 0x30;

	public static final String QR_END = "" + (char) 0x1D + (char) 0x28 + (char) 0x6B + (char) 0x03 + (char) 0x00
			+ (char) 0x31 + (char) 0x51 + (char) 0x30 + (char) 0x1D + (char) 0x28 + (char) 0x6B + (char) 0x03
			+ (char) 0x00 + (char) 0x31 + (char) 0x52 + (char) 0x30;

	public static String getOrderSource(String source, Brand brand) {
		String orderSource = null;
		if(brand == null || brand.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID)){
			if (UnitCategory.TAKE_AWAY.name().equals(source)) {
				orderSource = "Chaayos Take Away";
			} else if (UnitCategory.COD.name().equals(source)) {
				orderSource = "Chaayos Chai On Demand";
			} else if (UnitCategory.CAFE.name().equals(source)) {
				orderSource = "Chaayos";
			}
		} else {
			if (UnitCategory.TAKE_AWAY.name().equals(source)) {
				orderSource = brand.getBrandName() +  " Take Away";
			} else if (UnitCategory.COD.name().equals(source)) {
				orderSource = brand.getBrandName() + " Delivery";
			} else if (UnitCategory.CAFE.name().equals(source)) {
				orderSource = brand.getBrandName();
			}
		}

		return orderSource;
	}

	public static String cut() {
		StringBuilder sb = new StringBuilder();
		cut(sb);
		return sb.toString();
	}

	public static String partialCut() {
		StringBuilder sb = new StringBuilder();
		partialCut(sb);
		return sb.toString();
	}


	public static void cut(StringBuilder sb) {
		sb.append(RawPrintHelper.FONT_A);
		sb.append(RawPrintHelper.STANDARD_FONT);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.CUT_PAPER);
	}

	public static void partialCut(StringBuilder sb) {
		sb.append(RawPrintHelper.FONT_A);
		sb.append(RawPrintHelper.STANDARD_FONT);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.LINE_BREAK);
		sb.append(RawPrintHelper.PARTIAL_CUT_PAPER);
	}

	public static String centerAlign(String s) {
		return CENTER + SPACE + s + LINE_BREAK;
	}

	public static String leftAlign(String s) {
		return LEFT + SPACE + s + LINE_BREAK;
	}
	public static String rightAlign(String s){
		return RIGHT + SPACE + s + LINE_BREAK;
	}
	public static String leftAlignWithoutBreak(String s){
		return LEFT + SPACE + s;
	}

	public static String bold(String s) {
		return BOLD_ON + s + BOLD_OFF;
	}

	public static String wideFont(String s) {
		return WIDE_FONT + s + STANDARD_FONT;
	}
	
	public static String tallFont(String s) {
		return TALL_FONT + s + STANDARD_FONT;
	}
	
	public static String doubleFont(String s) {
		return DOUBLE_FONT + s + STANDARD_FONT;
	}
	
	
	public static String qr(String qr) {
		int qrLength = qr.length() + 3;
		String size1 = fromCharCode(qrLength % 256);
		String size0 = fromCharCode((new Double(Math.floor(qrLength / 256))).intValue());
		return QR_SIZE_BEGIN + size1 + size0 + QR_SIZE_END + qr + QR_END;
	}

	private static String fromCharCode(int... codePoints) {
		StringBuilder builder = new StringBuilder(codePoints.length);
		for (int codePoint : codePoints) {
			builder.append(Character.toChars(codePoint));
		}
		return builder.toString();
	}

	public static String smallFont(String s) {
		return SMALL_FONT + s + FONT_A; //FONT_A for Order Raw print
	}
}
