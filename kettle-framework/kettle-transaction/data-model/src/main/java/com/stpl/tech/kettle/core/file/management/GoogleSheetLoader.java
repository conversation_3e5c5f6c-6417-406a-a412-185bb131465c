/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.management;

//import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.google.api.client.http.FileContent;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.FileList;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.stpl.tech.kettle.core.TransactionConstants;

public class GoogleSheetLoader implements FileLoader {

	@Override
	public void downloadFile(String userAccount, String fileId, String mimeType, String destinationFileName)
			throws IOException {
		java.io.File file = new java.io.File(destinationFileName);
		if (!file.getParentFile().exists()) {
			file.getParentFile().mkdirs();
		}
		FileOutputStream fos = new FileOutputStream(file);
		GoogleServiceFactory.getInstance().getDriveService(userAccount).files().export(fileId, mimeType)
				.executeAndDownloadTo(fos);
	}

	public File getFile(String userAccount, String fileId) throws IOException {
		return GoogleServiceFactory.getInstance().getDriveService(userAccount).files().get(fileId).execute();
	}

	@Override
	public List<File> getFiles(String userAccount, String searchText, String mimeType) throws IOException {

		List<File> list = new ArrayList<>();
		String pageToken = null;
		do {
			FileList result = GoogleServiceFactory.getInstance().getDriveService(userAccount).files().list()
					.setQ(String.format("name = '%s' and mimeType = '%s'", searchText, mimeType)).setSpaces("drive")
					.setFields("nextPageToken, files(id, name,parents)").setPageToken(pageToken).execute();
			if (result.getFiles() != null && result.getFiles().size() > 0) {
				list.addAll(result.getFiles());
			}
			pageToken = result.getNextPageToken();
		} while (pageToken != null);
		return list;
	}

	@Override
	public File getFile(String userAccount, String fileName, String mimeType, String parentDirName) throws IOException {
		List<File> files = getFiles(userAccount, fileName, mimeType);
		for (File file : files) {
			List<String> parentFileIds = file.getParents();
			if (file.getParents().size() > 0) {
				for (String parentFileId : parentFileIds) {
					if (getFile(userAccount, parentFileId).getName().endsWith(parentDirName)) {
						return file;
					}
				}
			}
		}
		return null;
	}

	@Override
	public File getFileForDashboardRefresh(String userAccount, String fileName, String mimeType, String parentDirName) throws IOException {
		List<File> files = getFiles(userAccount, fileName, mimeType);
		if(Objects.nonNull(files) && !files.isEmpty()) {
			if(files.size() > 1) {
				return files.get(files.size() - 1);
			}
			return files.get(0);
		}
		return null;
	}

	@Override
	public void uploadExpenseReport(String userAccount, String filePath, String fileName, String mimeType,
			String targetDir, String parentDir) throws IOException {

		File targetFolder = getFile(userAccount, targetDir, TransactionConstants.MIMETYPE_GOOGLE_FOLDER, parentDir);
		File fileMetadata = new File();
		fileMetadata.setName(fileName);
		fileMetadata.setMimeType(TransactionConstants.MIMETYPE_GOOGLE_SHEETS);
		List<String> parentList = new ArrayList<String>();
		parentList.add(targetFolder.getId());
		fileMetadata.setParents(parentList);
		java.io.File fileSource = new java.io.File(filePath);
		FileContent mediaContent = new FileContent(mimeType, fileSource);
		GoogleServiceFactory.getInstance().getDriveService(userAccount).files().create(fileMetadata, mediaContent)
				.setFields("id").execute();
	}

	public static void main(String[] args) throws IOException {
		String pageToken = null;
		do {
			FileList result = GoogleServiceFactory.getInstance().getDriveService("<EMAIL>").files().list()
					.setSpaces("drive").setFields("nextPageToken, files(id, name,mimeType,size)")
					.setPageToken(pageToken).execute();
			List<File> files = result.getFiles();
			for (File file : files) {
				System.out.println(file.getId() + "  " + file.getName() + "  " + file.getSize());
				try{
				if (file.getName().toLowerCase().contains("salary")) {
					java.io.File file1 = new java.io.File("C:/downloads/" + file.getName()+".xlsx");
					if (!file1.getParentFile().exists()) {
						file1.getParentFile().mkdirs();
					}
					FileOutputStream fos = new FileOutputStream(file1);
					GoogleServiceFactory.getInstance().getDriveService("<EMAIL>").files().export(file.getId(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").executeAndDownloadTo(fos);
				}
				}catch(Exception e){
					e.printStackTrace();
				}
			}
			pageToken = result.getNextPageToken();
		} while (pageToken != null);
	}

	@Override
	public void writeRowsSheet(String accountName, String spreadsheetId, String range, List<List<Object>> values)
			throws IOException {
		Sheets service = GoogleServiceFactory.getInstance().getSheetsService(accountName);
		ValueRange requestBody = new ValueRange();
		requestBody.setMajorDimension("ROWS");
		requestBody.setRange(range);
		requestBody.setValues(values);
		Sheets.Spreadsheets.Values.Append request = service.spreadsheets().values().append(spreadsheetId, range,
				requestBody);
		request.setValueInputOption("RAW");
		request.setInsertDataOption("INSERT_ROWS");
		request.execute();

	}

	private static void readFile() throws IOException {

		String pageToken = null;
		do {
			FileList result = GoogleServiceFactory.getInstance().getDriveService("<EMAIL>").files().list()
					.setSpaces("drive").setFields("nextPageToken, files(id, name,mimeType,size)")
					.setPageToken(pageToken).execute();
			List<File> files = result.getFiles();
			for (File file : files) {
				System.out.println(file.getId() + "  " + file.getName() + "  " + file.getSize());
				try {
					if (file.getName().contains("test")) {
						java.io.File file1 = new java.io.File("E:/downloads/" + file.getName());
						if (!file1.getParentFile().exists()) {
							file1.getParentFile().mkdirs();
						}
						FileOutputStream fos = new FileOutputStream(file1);
						GoogleServiceFactory.getInstance().getDriveService("<EMAIL>").files()
								.get(file.getId()).executeMediaAndDownloadTo(fos);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			pageToken = result.getNextPageToken();
		} while (pageToken != null);

	}
}