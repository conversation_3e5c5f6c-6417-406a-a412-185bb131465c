package com.stpl.tech.kettle.facebook.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.clevertap.data.dao.CleverTapDataPushDao;
import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.util.AbstractRestTemplate;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.facebook.converter.FBConverter;
import com.stpl.tech.kettle.facebook.domain.model.FBEventUploadRequest;
import com.stpl.tech.kettle.facebook.domain.model.FBPushResponse;
import com.stpl.tech.kettle.facebook.domain.model.FBRequest;
import com.stpl.tech.kettle.facebook.domain.model.FacebookEndpoints;
import com.stpl.tech.kettle.facebook.service.FacebookDataPushService;
import com.stpl.tech.kettle.facebook.util.FBConstants;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class FacebookDataPushServiceImpl implements FacebookDataPushService {

    private static final Logger LOG = LoggerFactory.getLogger(FacebookDataPushServiceImpl.class);

    @Autowired
    CustomerService customerService;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private CleverTapDataPushDao dao;

    @Autowired
    private EnvironmentProperties props;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void persistFBEvents(List<EventPushTrack> eventList) {
        try {
            LOG.info("#Saving fb events to DB");
            dao.addAll(eventList);
        } catch (Exception e) {
            LOG.error("Error while saving data for facebook events push {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void uploadFBEvent(OrderInfo info, String evtName) {
        try {

            ArrayList<FBEventUploadRequest> fbRequestList = new ArrayList<>();
            String setId;
            if (info.getBrand().getBrandId().equals(AppConstants.GNT_BRAND_ID)) {
                setId = props.getEventSetIdForBrandGNT();
            } else {
                if (info.getOrder().getSource().equals(AppConstants.COD)) {
                    setId = props.getEventSetIdForChaayosDelivery();
                } else {
                    setId = props.getEventSetIdForChaayosDineIn();
                }
            }

            if (Objects.nonNull(info.getCustomer())) {
                FBEventUploadRequest request = FBConverter.convert(info.getOrder(), evtName, info.getCustomer());
                fbRequestList.add(request);
            } else {
                LOG.info("Customer does not exist for customerId: {} tagged with order", info.getCustomer().getId());
                return;
            }

            List<EventPushTrack> eventList = new ArrayList<>();
            if (!fbRequestList.isEmpty()) {
                FBRequest uploadRequest = new FBRequest();
                uploadRequest.setData(fbRequestList);

                Map<String, Object> stringParams = new HashMap<>();
                stringParams.put("access_token", props.getAccessTokenForFacebookPush());
                stringParams.put("upload_tag", FBConstants.BATCH_NAME);

                Map<String, Object> uriVariables = new HashMap<>();
                uriVariables.put("OFFLINE_EVENT_SET_ID", setId);
                LOG.info("Uploading {} order data to facebook", info.getOrder().getOrderId());
                try {
                    String endPoint = AbstractRestTemplate.setUriVariables(AbstractRestTemplate.appendQueryParams(
                            FacebookEndpoints.UPLOAD_EVENT.getUrl(), stringParams), uriVariables);
                    HttpPost httpPost = new HttpPost(endPoint);
                    StringEntity entity = new StringEntity(new Gson().toJson(uploadRequest));
                    httpPost.setEntity(entity);
                    httpPost.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
                    LOG.info("Sending Request to {}, payload {} ", endPoint, new Gson().toJson(uploadRequest));
                    HttpResponse response = WebServiceHelper.postRequest(httpPost);

                    if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                        FBPushResponse fbPushResponse = WebServiceHelper.convertResponse(response, FBPushResponse.class);
                        if (Objects.nonNull(response.getEntity()) && Objects.nonNull(response.getEntity().getContent())) {
                            response.getEntity().getContent().close();
                        }
                        LOG.info("##Response for uploaded FB event {} ", fbPushResponse);
                        EventPushTrack eventPushTrack = new EventPushTrack(evtName, info.getOrder().getOrderId(), "SUCCESS", FBConstants.REGULAR, AppConstants.FACEBOOK);
                        eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                        eventList.add(eventPushTrack);
                    } else {
                        LOG.info("FB request error code {} ", response.getStatusLine().getStatusCode());
                        if (Objects.nonNull(response.getEntity()) && Objects.nonNull(response.getEntity().getContent())) {
                            response.getEntity().getContent().close();
                        }
                        throw new Exception("FB Request Error");
                    }

                } catch (Exception e) {
                    LOG.error("Failed in sending data for order id {} to facebook with order startId :::: ", info.getOrder().getOrderId());
                    LOG.error("Exception while pushing data to facebook :::: ", e);
                    eventList.add(new EventPushTrack(evtName, info.getOrder().getOrderId(), "ERROR", FBConstants.REGULAR, AppConstants.FACEBOOK));
                }
                persistFBEvents(eventList);
            }
        } catch (Exception e) {
            LOG.error("Exception while pushing FB event :::: ", e);
        }
    }

}