/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
public class SimulationResultData implements Cloneable {

	public int orderId;
	public int orderItemId;
	public int taskId = 1;
	public boolean isSplit;
	public boolean hasTransition;
	public boolean toBeSkipped;
	public String orderSource;
	public BigDecimal taxableAmount;
	public String productName;
	public String recipeString;
	public RecipeKey productData;
	public Date orderTime;
	public int actualTimeWithoutVoltageFluctuation;
	public int actualTimeWithVoltageFluctuation;
	public int steepingTime;

	public Map<Pair<Integer, Boolean>, SimulationTimeData> result = new HashMap<>();

	public SimulationTimeData setStartTime(Date startTime, int noOfMonks, int monkNumber, int idealMonks,
			boolean withFluctuation, int transitionTime) {
		Pair<Integer, Boolean> key = new Pair<Integer, Boolean>(noOfMonks, withFluctuation);
		result.put(key, new SimulationTimeData());
		SimulationTimeData t = result.get(key);
		t.idealMonks = idealMonks;
		t.startTime = startTime;
		t.monkNumber = monkNumber;
		t.waitTimeInSecs = (int) ((startTime.getTime() - this.orderTime.getTime()) / 1000);
		if (withFluctuation) {
			t.endTime = AppUtils.getDateBeforeOrAfterInSeconds(startTime,
					this.actualTimeWithVoltageFluctuation + transitionTime);
		} else {
			t.endTime = AppUtils.getDateBeforeOrAfterInSeconds(startTime,
					this.actualTimeWithoutVoltageFluctuation + transitionTime);
		}
		t.timeTaken = (int) ((t.endTime.getTime() - this.orderTime.getTime()) / 1000);
		return t;
	}

	@Override
	public String toString() {
		return "SimulationResultData [orderId=" + orderId + ", orderItemId=" + orderItemId + ", taskId=" + taskId
				+ ", isSplit=" + isSplit + ", hasTransition=" + hasTransition + ", orderSource=" + orderSource
				+ ", taxableAmount=" + taxableAmount + ", productName=" + productName + ", recipeString=" + recipeString
				+ ", productData=" + productData + ", orderTime=" + orderTime + ", actualTimeWithoutVoltageFluctuation="
				+ actualTimeWithoutVoltageFluctuation + ", actualTimeWithVoltageFluctuation="
				+ actualTimeWithVoltageFluctuation + ", steepingTime=" + steepingTime + "]";
	}

	public String toString(int monkCount, boolean withFluctuation) {
		Pair<Integer, Boolean> key = new Pair<Integer, Boolean>(monkCount, withFluctuation);
		return "SimulationResultData [orderId=" + orderId + ", orderItemId=" + orderItemId + ", taskId=" + taskId
				+ ", isSplit=" + isSplit + ", hasTransition=" + hasTransition + ", orderSource=" + orderSource
				+ ", taxableAmount=" + taxableAmount + ", productName=" + productName + ", recipeString=" + recipeString
				+ ", productData=" + productData + ", orderTime=" + orderTime + ", actualTimeWithoutVoltageFluctuation="
				+ actualTimeWithoutVoltageFluctuation + ", actualTimeWithVoltageFluctuation="
				+ actualTimeWithVoltageFluctuation + ", steepingTime=" + steepingTime + ", result=" + result.get(key)
				+ "]";
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		SimulationResultData data = new SimulationResultData();
		data.orderId = this.orderId;
		data.orderItemId = this.orderItemId;
		data.taskId = this.taskId;
		data.isSplit = this.isSplit;
		data.orderSource = this.orderSource;
		data.taxableAmount = this.taxableAmount;
		data.productName = this.productName;
		data.recipeString = this.recipeString;
		data.productData = (RecipeKey) this.productData.clone();
		data.orderTime = (Date) this.orderTime.clone();
		data.actualTimeWithoutVoltageFluctuation = this.actualTimeWithoutVoltageFluctuation;
		data.actualTimeWithVoltageFluctuation = this.actualTimeWithVoltageFluctuation;
		data.steepingTime = this.steepingTime;
		data.hasTransition = this.hasTransition;
		data.result = new HashMap<>();
		return data;
	}

}
