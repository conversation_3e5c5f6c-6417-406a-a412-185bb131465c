package com.stpl.tech.kettle.core.cache;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.core.service.RuleService;
import com.stpl.tech.kettle.data.model.RulesOptionResultData;
import com.stpl.tech.kettle.offer.model.Option;
import com.stpl.tech.kettle.offer.model.OptionData;
import com.stpl.tech.kettle.offer.model.OptionResponseData;
import com.stpl.tech.kettle.offer.model.OptionType;
import com.stpl.tech.kettle.offer.model.RuleData;

@Repository
public class RecommendationCache {

	private static final Logger LOG = LoggerFactory.getLogger(RecommendationCache.class);

	@Autowired
	private RuleService service;

	private Map<Integer, Map<RuleData, Map<OptionType, Map<Integer, OptionData>>>> optionData = new HashMap<>();

	@PostConstruct
	public void reloadCache() {
		LOG.info("POST-CONSTRUCT RecommendationCache - STARTED");
		Stopwatch watch = Stopwatch.createUnstarted();
    	watch.start();
		service.createRules();
		LOG.info("Inside POSTCONSTRUCT - RecommendationCache OVERALL : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
	}
	private void loadMetadata(int unitId) {

		if (!optionData.containsKey(unitId)) {
			optionData.put(unitId, new HashMap<>());
		}
		Map<RuleData, Map<OptionType, Map<Integer, OptionData>>> map = optionData.get(unitId);
		for (RuleData rule : RuleData.values()) {
			if (!map.containsKey(rule)) {
				map.put(rule, new HashMap<>());
				Map<OptionType, Map<Integer, OptionData>> optionMap = map.get(rule);
				for (OptionType type : OptionType.values()) {
					optionMap.put(type, new HashMap<>());
				}
			}
			for (Option option : rule.getOptions()) {
				OptionData data = new OptionData(unitId, rule, option);
				map.get(rule).get(data.getOption().getType()).put(data.getOption().getOptionId(), data);
				RulesOptionResultData result = service.getOrCreateRulesOptionResultData(unitId, rule, option);
				data.setCountWithDiscount(result.getCountWithDiscount());
				data.setCountWithoutDiscount(result.getCountWithoutDiscount());
				data.setOptionResultDataId(result.getOptionResultDataId());
			}
		}
		
	}

	public OptionResponseData getOption(Integer unitId, RuleData rule, boolean hasOffer,
			Set<Integer> skippedProducts) {
		OptionResponseData response = null;
		if(!optionData.containsKey(unitId)){
			LOG.info("Did not find result data cache for unit {}", unitId);
			loadMetadata(unitId);
		}
		if (optionData.containsKey(unitId) && optionData.get(unitId).containsKey(rule)) {
			Map<Integer, OptionData> optionsData = optionData.get(unitId).get(rule).get(OptionType.REGULAR);
			response = getResponse(optionsData, hasOffer, skippedProducts);
			if (response == null) {
				Map<OptionType, Map<Integer, OptionData>> data = optionData.get(unitId).get(OptionType.OTHER);
				response = getResponse(data == null ? null : data.get(rule), hasOffer, skippedProducts);
			}
		}
		return response;
	}

	private OptionResponseData getResponse(Map<Integer, OptionData> optionsData, boolean hasOffer,
			Set<Integer> skippedProducts) {
		OptionResponseData response = null;
		if (optionsData != null) {
			List<OptionData> options = new ArrayList<>(optionsData.values());
			Collections.sort(options);
			boolean giveOffer = false;

			for (OptionData option : options) {
				if (skippedProducts.contains(option.getOption().getOption1())
						|| (option.getOption().getOption2() != null
								&& skippedProducts.contains(option.getOption().getOption2()))) {
					continue;
				}
				if (!hasOffer) {
					if (option.getRule().equals(RuleData.CATCH_ALL)) {
						giveOffer = false;
					} else if (option.getCountWithDiscount() < option.getCountWithoutDiscount()) {
						giveOffer = true;
					}
				}
				if (giveOffer) {
					option.setCountWithDiscount(option.getCountWithDiscount() + 1);
				} else {
					option.setCountWithoutDiscount(option.getCountWithoutDiscount() + 1);
				}
				response = new OptionResponseData(option, giveOffer, option.getOptionResultDataId());
				break;
			}
		}
		return response;
	}

	@Override
	public String toString() {
		return "RecommendationCache{" +
				"optionData=" + optionData.size() +
				'}';
	}
}
