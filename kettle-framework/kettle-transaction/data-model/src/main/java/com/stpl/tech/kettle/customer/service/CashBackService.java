package com.stpl.tech.kettle.customer.service;

import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;

public interface CashBackService {

	boolean allotCashBack(BigDecimal amount, Integer customerId, int orderId, Date creationDate, Date expirationDate, int lagDays);

	CashPacketData allotCashAsFreeGift(BigDecimal amount, String contact, Integer validityInDays, Integer lagDays, Customer customer) throws JMSException, IOException;

	Date getEndDate();

	Date getStartDate();

    void checkCashBack(Order order);

	public void awardCashBackOffer(Order order);

    void invalidateCashBack(Order order);
}
