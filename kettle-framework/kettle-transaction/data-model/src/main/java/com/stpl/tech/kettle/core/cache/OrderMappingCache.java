/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Pair;

public class OrderMappingCache {

	private static final Logger LOG = LoggerFactory.getLogger(OrderMappingCache.class);
	private static final OrderMappingCache INSTANCE = new OrderMappingCache();

	private OrderMappingCache() {

	}

	public static OrderMappingCache getInstance() {
		return INSTANCE;
	}

	private static final Map<OrderUnitMapping, Pair<String, String>> randomNumberMapping = new HashMap<>();

	private synchronized boolean checkExists(OrderUnitMapping mapping, String randomString) {

		return randomNumberMapping.containsKey(mapping) && randomString.equals(randomNumberMapping.get(mapping).getKey());

	}

	public synchronized void add(OrderUnitMapping mapping, String randomString) throws DataUpdationException {
		LOG.info(String.format("Adding order request for %s with randon string %s", mapping, randomString));
		if (checkExists(mapping, randomString)) {
			throw new DataUpdationException(String.format(
					"Tried to create a duplicate order request for %s with randon string %s", mapping, randomString));
		}
		randomNumberMapping.put(mapping, new Pair<String, String>(randomString, null));
	}

	public synchronized String get(OrderUnitMapping mapping, String randomString){
		LOG.info(String.format("Get orderid for %s with randon string %s", mapping, randomString));
		return randomNumberMapping.get(mapping).getValue();
	}

	public synchronized void setGeneratedOrderId(OrderUnitMapping mapping, String randomString, String generatedOrderId){
		LOG.info(String.format("Setting order id %s for %s with randon string %s", generatedOrderId, mapping,
				randomString));
		if (randomNumberMapping.containsKey(mapping)
				&& randomString.equals(randomNumberMapping.get(mapping).getKey())) {
			randomNumberMapping.get(mapping).setValue(generatedOrderId);
		}
	}

}
