package com.stpl.tech.kettle.clm.dao.impl;

import com.stpl.tech.kettle.clm.dao.SelectPotentialSavingDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.EnvType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;

@Repository
@Slf4j
public class SelectPotentialSavingDaoImpl extends CLMDataAbstractDaoImpl implements SelectPotentialSavingDao  {

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Override
    public Object[] getPotentialSavingData(Integer customerId, Integer brandId) {
        try {
            String queryString = "SELECT ORDERS_CNT, ORDERS_CNT_90DAYS, NET_SALES, NET_SALES_90DAYS, TOTAL_SALES, TOTAL_SALES_90DAYS from KETTLE_DATA_LAKE.CLM_CUSTOMER_ONE_VIEW where CUSTOMER_ID = :customerId and BRAND_ID = :brandId";
            if(!environmentProperties.getEnvironmentType().equals(EnvType.SPROD)){
                queryString ="SELECT ORDERS_CNT, ORDERS_CNT_90DAYS, NET_SALES, NET_SALES_90DAYS, TOTAL_SALES, TOTAL_SALES_90DAYS from KETTLE_DATA_LAKE_DEV.CLM_CUSTOMER_ONE_VIEW where CUSTOMER_ID = :customerId and BRAND_ID = :brandId";
            }
            Query query = manager.createNativeQuery(queryString);
            query.setParameter("customerId",customerId);
            query.setParameter("brandId",brandId);
            List<Object[]> data = query.getResultList();
            if(!data.isEmpty()){
                return data.get(0);
            }else{
                log.info("No PotentialSavingData result found for Customer id : {} and brand Id : {}", customerId, brandId);
            }
        }catch (NoResultException e){
            log.error("No PotentialSavingData result found for Customer id : {} and brand Id : {}", customerId, brandId);
        }catch (Exception e){
            log.error("Error while fetching PotentialSavingData for Customer id : {} and brand Id : {}", customerId, brandId, e);
        }
        return null;
    }
}
