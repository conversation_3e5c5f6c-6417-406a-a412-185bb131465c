package com.stpl.tech.kettle.referral.dao.impl;

import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.kettle.data.model.ReferralStatus;
import com.stpl.tech.kettle.referral.dao.CashManagerDao;
import com.stpl.tech.kettle.referral.dao.ReferralDao;
import com.stpl.tech.util.AppUtils;

@Repository
public class ReferralDaoImpl extends AbstractDaoImpl implements ReferralDao {

	private static final Logger LOG = LoggerFactory.getLogger(ReferralDaoImpl.class);

	@Autowired
	private CashManagerDao cashManager;

	@Override
	public ReferralMappingData searchByCode(String refCode, String contactNumber) {
		try {
			Query query = manager.createQuery(
					"FROM ReferralMappingData E where E.referralCode = :refCode AND E.contactNumber= :contactNumber");
			query.setParameter("refCode", refCode);
			query.setParameter("contactNumber", contactNumber);
			return (ReferralMappingData) query.getSingleResult();
		} catch (NoResultException nre) {
			return null;
		} catch (Exception e) {
			LOG.error("Error while searching reference", e);
			return null;
		}
	}

	@Override
	public void updateReferralStatus(CustomerInfo info, String refCode) {
		List<ReferralMappingData> list = searchByContactNumber(info.getContactNumber());
		boolean noRefCode = AppUtils.isBlank(refCode);
		// handle other reference
		if (AppUtils.isNonEmptyList(list)) {
			for (ReferralMappingData d : list) {
				String referentName = info.getFirstName();
				if(!AppUtils.isBlank(info.getLastName())){
					referentName = " " + info.getLastName();
				}
				d.setReferentId(info.getCustomerId());
				if(!AppUtils.isBlank(referentName)){
					d.setReferentName(referentName);
				}
				d.setLastUpdateTime(AppUtils.getCurrentTimestamp());

				// no reference code is used
				if (noRefCode) {
					d.setReferralStatus(ReferralStatus.REF_NOT_USED.name());
				} else if (refCode.equalsIgnoreCase(d.getReferralCode())) {
					// when there is a successful referrer
					d.setReferralStatus(ReferralStatus.SUCCESS.name());
					cashManager.addCashToReferrerOnSuccess(d, refCode,info);
					cashManager.addCashOnReferentOnSuccess(d, info);
				} else {
					d.setReferralStatus(ReferralStatus.SUCCESS_OTHER.name());
				}
			}
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<ReferralMappingData> searchByContactNumber(String contactNumber) {
		Query query = manager.createQuery("FROM ReferralMappingData E where E.contactNumber = :contactNumber");
		query.setParameter("contactNumber", contactNumber);
		return query.getResultList();
	}

	@Override
	public ReferralMappingData getReffralMapping(String contactNumber, int referrerId) {
		try {
			Query query = manager.createQuery(
					"FROM ReferralMappingData E where E.contactNumber = :contactNumber AND E.referrerId = :referrerId");
			query.setParameter("contactNumber", contactNumber);
			query.setParameter("referrerId", referrerId);
			return (ReferralMappingData) query.getSingleResult();
		} catch (NoResultException nre) {
			return null;
		} catch (Exception e) {
			LOG.error("Error while searching ReferralMappingData", e);
			return null;
		}
	}

	@Override
	public List<ReferralMappingData> getReferralMappings(int referrerId) {
		try {
			Query query = manager.createQuery("FROM ReferralMappingData E where E.referrerId = :referrerId");
			query.setParameter("referrerId", referrerId);
			return query.getResultList();
		} catch (NoResultException nre) {
			return null;
		} catch (Exception e) {
			LOG.error("Error while searching ReferralMappingData list", e);
			return null;
		}
	}

}
