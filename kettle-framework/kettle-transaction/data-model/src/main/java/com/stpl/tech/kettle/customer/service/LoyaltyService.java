/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.customer.service;

import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyScore;

import java.util.List;

public interface LoyaltyService {

	public boolean updateScore(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean createEvent(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public LoyaltyScore getScore(int customerId);

	public List<LoyaltyEvents> getLoyaltyEvents(String orderId, String searchType,Integer limit);

}
