package com.stpl.tech.kettle.data.converter;

import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class DroolHelper {

    public static void checkNullAndAdd(DroolsCustomerProperties properties,String dayPart, String value) {
        if(Objects.nonNull(properties) && !StringUtils.isEmpty(value)){
            if(Objects.nonNull(properties.getDayPartOfferString())){
                properties.getDayPartOfferString().put(dayPart,value);
            }else{
                Map<String,String> dayPartString = new HashMap<>();
                dayPartString.put(dayPart,value);
                properties.setDayPartOfferString(dayPartString);
            }
        }
    }
}
