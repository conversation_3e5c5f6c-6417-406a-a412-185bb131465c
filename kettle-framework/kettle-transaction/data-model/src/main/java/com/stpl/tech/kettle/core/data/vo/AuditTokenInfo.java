package com.stpl.tech.kettle.core.data.vo;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.external.acl.service.TokenDao;

import io.jsonwebtoken.Claims;

public class AuditTokenInfo implements TokenDao {

	private Integer auditInfoId;

	public Integer getAuditInfoId() {
		return auditInfoId;
	}

	public void setAuditInfoId(Integer auditInfoId) {
		this.auditInfoId = auditInfoId;
	}

	public Map<String, Object> createClaims() {
		Map<String, Object> claims = new HashMap<String, Object>();
		claims.put("auditInfoId", this.auditInfoId);
		return claims;
	}

	public void parseClaims(Claims claims) {
		this.auditInfoId = claims.get("auditInfoId", Integer.class);
	}

}
