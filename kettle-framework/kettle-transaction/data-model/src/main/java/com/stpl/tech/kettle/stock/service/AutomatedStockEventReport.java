package com.stpl.tech.kettle.stock.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.web.servlet.View;

import com.stpl.tech.kettle.core.data.vo.StockOutReportData;


public interface AutomatedStockEventReport {

	StockOutReportData execute(Date previousDate, Integer unitId,
                               boolean saveResults, List<Date> unitTime, boolean partner,Integer brand);

	View executeForDownload(Date startDate, Date endDate, List<Integer> unitId, Date calculationDate, boolean saveResults);

	public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate);

}
