package com.stpl.tech.kettle.customer.service.impl;


import com.stpl.tech.kettle.core.LoyaltyEventStatus;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.dao.impl.AuthorizationDaoImpl;
import com.stpl.tech.kettle.customer.service.LoyaltyTransferService;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyLogHistory;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.LoyaltyTransfer;
import com.stpl.tech.kettle.data.model.LoyaltyTransferStatusLog;
import com.stpl.tech.kettle.domain.model.webengage.survey.LoyaltyGiftingSMSToken;
import com.stpl.tech.kettle.loyaltyTransfer.model.LoyaltyTransferRequestBody;
import com.stpl.tech.kettle.loyaltyTransfer.model.LoyaltyTransferStatus;
import com.stpl.tech.kettle.referral.dao.LoyaltyTransferDao;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.transaction.TransactionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class LoyaltyTransferServiceImpl implements LoyaltyTransferService {
    @Autowired
    LoyaltyTransferDao loyaltyTransferDao;

    @Autowired
    EnvironmentProperties environmentProperties;

    @Autowired
    private LoyaltyDao loyaltyDao;

    private static final String LOYALTY_EVENT_SUCCESS_STATUS = "SUCCESS";
    private static final String LOYALTY_EVENT_CANCELLED_STATUS = "CANCELLED";
    private static final String LOYALTY_EVENT_EXPIRED_STATUS = "EXPIRED";

    private static final Logger LOG = LoggerFactory.getLogger(AuthorizationDaoImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public LoyaltyTransfer initiateLoyaltyTransfer(LoyaltyTransferRequestBody loyaltyTransferRequestBody, CustomerInfo customerInfo) {
        LoyaltyTransfer loyaltyTransfer = new LoyaltyTransfer();
        CustomerInfo senderInfo = loyaltyTransferDao.getCustomerInfoByCustomerId(loyaltyTransferRequestBody.getSenderId());
        if (customerInfo != null) {
            loyaltyTransfer.setReceiverId(customerInfo.getCustomerId());
            loyaltyTransfer.setReceiverName(this.getCustomerFullName(customerInfo).trim());
        }
        loyaltyTransfer.setSenderName(this.getCustomerFullName(senderInfo).trim());
        loyaltyTransfer.setSenderContactNumber(senderInfo.getContactNumber());
        loyaltyTransfer.setSenderId(loyaltyTransferRequestBody.getSenderId());
        loyaltyTransfer.setReceiverContactNumber(loyaltyTransferRequestBody.getContactNumber());
        loyaltyTransfer.setTransferInitiatedTime(AppUtils.getCurrentTimestamp());
        loyaltyTransfer.setTransferStatus(LoyaltyTransferStatus.INITIATED.name());
        loyaltyTransfer.setTransferType(AppConstants.LOYALTY_GIFTING);
        return loyaltyTransferDao.add(loyaltyTransfer);
    }

    @Override
    public String getCustomerFullName(CustomerInfo customerInfo) {
        String fisrtName = customerInfo.getFirstName() != null ? customerInfo.getFirstName() : "";
        String middleName = customerInfo.getMiddleName() != null ? customerInfo.getMiddleName() : "";
        String lastName = customerInfo.getLastName() != null ? customerInfo.getLastName() : "";
        return fisrtName + " " + middleName + " " + lastName;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public LoyaltyTransfer createLoyaltyTransfer(LoyaltyTransferRequestBody loyaltyTransferRequestBody, LoyaltyScore loyaltyScore, LoyaltyTransfer loyaltyTransfer) {
        if(Objects.isNull(loyaltyScore) || Objects.isNull(loyaltyScore.getOrderCount())
                || loyaltyScore.getOrderCount() < 6){
            return null;
        }
        Date currentDate = AppUtils.getCurrentTimestamp();
        Date expiryDate = AppUtils.addDays(currentDate, 30);
        int bonusPoints = Integer.parseInt(environmentProperties.getBonusPoints());
        int loyaltyPointPerChai = Integer.parseInt(environmentProperties.getLoyaltyPointsPerChai());
        if (loyaltyTransfer.getReceiverId() != null) {
            LoyaltyScore loyaltyScoreOfReceiver = loyaltyTransferDao.getLoyalityScore(loyaltyTransfer.getReceiverId());
            loyaltyTransfer.setReceiverInitialPoints(loyaltyScoreOfReceiver.getAcquiredPoints());
        } else {
            loyaltyTransfer.setReceiverInitialPoints(0);
        }
        loyaltyTransfer.setSenderInitialPoints(loyaltyScore.getAcquiredPoints());
        loyaltyTransfer.setSenderTransferPoints(loyaltyTransferRequestBody.getNoOfChai() * loyaltyPointPerChai);
        loyaltyTransfer.setBonusPoint(bonusPoints);
        loyaltyTransfer.setTotalTransferred((loyaltyTransferRequestBody.getNoOfChai() * loyaltyPointPerChai) + bonusPoints);
        loyaltyTransfer.setReceiverName(loyaltyTransferRequestBody.getReceiverName());
        loyaltyTransfer.setTransferRequestTime(currentDate);
        loyaltyTransfer.setTransferStatus(LoyaltyTransferStatus.CREATED.name());
        loyaltyTransfer.setExpiryTime(expiryDate);
        loyaltyTransfer.setTransferMessage(loyaltyTransferRequestBody.getTransferMessage());
        if (loyaltyScore.getAcquiredPoints() >= loyaltyTransfer.getSenderTransferPoints()
        && isCustomerEligibleToGiftLoyalty(loyaltyTransfer.getSenderId(),loyaltyTransfer.getSenderTransferPoints())) {
            loyaltyTransfer = loyaltyTransferDao.update(loyaltyTransfer);
            LOG.error("update succesfull");
            int acquiredPoints = loyaltyScore.getAcquiredPoints() != null ? loyaltyScore.getAcquiredPoints() : 0;
            int blockedPoints = loyaltyScore.getBlockedPoints() != null ? loyaltyScore.getBlockedPoints() : 0;
            loyaltyScore.setAcquiredPoints(acquiredPoints - loyaltyTransfer.getSenderTransferPoints());
            loyaltyScore.setBlockedPoints(blockedPoints + loyaltyTransfer.getSenderTransferPoints());
            loyaltyTransfer.setSenderBalance(loyaltyScore.getAcquiredPoints());
            loyaltyTransferDao.update(loyaltyScore);
            LoyaltyEvents loyaltyEvents = createLoyaltyTransferEventForSender(loyaltyTransfer);
            loyaltyEvents = loyaltyTransferDao.add(loyaltyEvents);
            Boolean isCustomerAddedIsNew = false;
            try {
                isCustomerAddedIsNew = loyaltyDao.isCustomerAddedIsNew(loyaltyEvents.getCustomerId());
                LOG.info("isCustomerAddedIsNew : {}",isCustomerAddedIsNew);
            }catch (Exception e){
                LOG.info("Error in checking customer add time for customer id : {} and error is : {}",
                        loyaltyEvents.getCustomerId(),e);
            }
            if(isCustomerAddedIsNew) {
                loyaltyDao.updateLoyaltyEvents(loyaltyEvents.getCustomerId(), loyaltyEvents.getTransactionPoints(),
                        -1, loyaltyEvents.getLoyaltyEventsId(), LoyaltyEventStatus.GIFTED.name());
            }
            return loyaltyTransfer;
        }
        return null;
    }

    public LoyaltyEvents createLoyaltyTransferEventForSender(LoyaltyTransfer loyaltyTransfer) {
        LoyaltyEvents loyaltyEvents = new LoyaltyEvents();
        loyaltyEvents.setCustomerId(loyaltyTransfer.getSenderId());
        loyaltyEvents.setTransactionType(TransactionType.CREDIT.name());
        loyaltyEvents.setTransactionPoints(loyaltyTransfer.getSenderTransferPoints() * -1);
        loyaltyEvents.setTransactionCodeType("Subtraction");
        loyaltyEvents.setTransactionCode(LoyaltyEventType.LOYALTY_GIFTING.name());
        loyaltyEvents.setTransactionStatus("SUCCESS");
        loyaltyEvents.setTransactionTime(AppUtils.getCurrentTimestamp());
        loyaltyEvents.setEventId(loyaltyTransfer.getEventId());
        loyaltyEvents.setOpeningBalance(loyaltyTransfer.getSenderInitialPoints());
        loyaltyEvents.setClosingBalance(loyaltyTransfer.getSenderInitialPoints()- loyaltyTransfer.getSenderTransferPoints());
        return loyaltyEvents;
    }


    @Override
    public LoyaltyScore getLoyalityScore(int customerId) {
        return loyaltyTransferDao.getLoyalityScore(customerId);
    }

    @Override
    public CustomerInfo getCustomerInfoByContactNumber(String contactNumber) {
        return loyaltyTransferDao.getCustomerInfoByContactNumber(contactNumber);
    }

    @Override
    public LoyaltyTransfer getLoyalityTransferByEventId(int eventId) {
        return loyaltyTransferDao.find(LoyaltyTransfer.class, eventId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addLoyaltyTransferStatusLog(String fromStatus, String toStatus, int eventId) {
        LoyaltyTransferStatusLog loyaltyTransferStatusLog = new LoyaltyTransferStatusLog();
        loyaltyTransferStatusLog.setFromStatus(fromStatus);
        loyaltyTransferStatusLog.setToStatus(toStatus);
        loyaltyTransferStatusLog.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
        loyaltyTransferStatusLog.setEventId(eventId);
        loyaltyTransferStatusLog.setComment("");
        loyaltyTransferDao.add(loyaltyTransferStatusLog);
    }

    @Override
    public List<LoyaltyTransfer> getAllSentGift(int customerId) {
        return loyaltyTransferDao.getAllSentGift(customerId);
    }

    @Override
    public List<LoyaltyTransfer> getAllReceivedGift(String customerNumber) {
        return loyaltyTransferDao.getAllReceivedGift(customerNumber);
    }

    @Override
    public List<LoyaltyTransfer> getNotClaimedGift(int customerId) {
        return loyaltyTransferDao.getNotClaimedGift(customerId);
    }

    @Override
    public List<LoyaltyTransfer> getNotClaimedReceivedGift(String contactNumber) {
        return loyaltyTransferDao.getNotClaimedReceivedGift(contactNumber);
    }

    @Override
    public List<LoyaltyTransfer> getClaimedGift(int customerId) {
        return loyaltyTransferDao.getClaimedGift(customerId);
    }

    @Override
    public List<LoyaltyTransfer> getClaimedReceivedGift(String contactNumber) {
        return loyaltyTransferDao.getClaimedReceivedGift(contactNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public LoyaltyTransfer cancelGift(int eventId, LoyaltyScore loyaltyScore) {
        try {
            LoyaltyTransfer loyaltyTransfer = loyaltyTransferDao.find(LoyaltyTransfer.class, eventId);
            if (LoyaltyTransferStatus.CREATED.name().equals(loyaltyTransfer.getTransferStatus())) {
                loyaltyTransfer.setTransferStatus(LoyaltyTransferStatus.CANCELLED.name());
                loyaltyScore.setAcquiredPoints(loyaltyScore.getAcquiredPoints() + loyaltyTransfer.getSenderTransferPoints());
                loyaltyScore.setBlockedPoints(loyaltyScore.getBlockedPoints() - loyaltyTransfer.getSenderTransferPoints());
                loyaltyTransfer.setSenderBalance(loyaltyScore.getAcquiredPoints());
                //LoyaltyEvents loyaltyEvents = createLoyaltyTransferEventForSenderOnRevert(loyaltyTransfer);
                LoyaltyEvents loyaltyEvents = createLoyaltyTransferEventForSenderOnRevertNew(loyaltyTransfer,
                        LOYALTY_EVENT_CANCELLED_STATUS,loyaltyScore);
                loyaltyTransferDao.add(loyaltyEvents);
                //loyaltyTransferDao.update(loyaltyScore);
                return loyaltyTransferDao.update(loyaltyTransfer);
            }
            return loyaltyTransfer;
        } catch (NoResultException e) {
            LOG.error("Invalid eventId no record found for event id : " + eventId, e);
        }

        return null;
    }

    public LoyaltyEvents createLoyaltyTransferEventForSenderOnRevert(LoyaltyTransfer loyaltyTransfer) {
        LoyaltyEvents loyaltyEvents = new LoyaltyEvents();
        loyaltyEvents.setCustomerId(loyaltyTransfer.getSenderId());
        loyaltyEvents.setTransactionType(TransactionType.DEBIT.name());
        loyaltyEvents.setTransactionPoints(loyaltyTransfer.getSenderTransferPoints());
        loyaltyEvents.setTransactionCodeType("Addition");
        loyaltyEvents.setTransactionCode(LoyaltyEventType.LOYALTY_GIFTING.name());
        loyaltyEvents.setTransactionStatus("SUCCESS");
        loyaltyEvents.setTransactionTime(AppUtils.getCurrentTimestamp());
        loyaltyEvents.setEventId(loyaltyTransfer.getEventId());
        loyaltyEvents.setClosingBalance(loyaltyTransfer.getSenderInitialPoints());
        loyaltyEvents.setOpeningBalance(loyaltyTransfer.getSenderInitialPoints()- loyaltyTransfer.getSenderTransferPoints());
        loyaltyEvents.setLoyaltyEventStatus(LoyaltyEventStatus.ACTIVE.name());
        loyaltyEvents.setRedeemedPoints(0);
        loyaltyEvents.setExpiredPoints(0);
        Integer daysAfterLoyaltyExpire = environmentProperties.getDaysAfterLoyaltyExpire();
        Date expirationDate = AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(),daysAfterLoyaltyExpire);
        loyaltyEvents.setExpirationTime(AppUtils.getLastDayOfMonth(expirationDate));
        return loyaltyEvents;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public LoyaltyEvents createLoyaltyTransferEventForSenderOnRevertNew(LoyaltyTransfer loyaltyTransfer,String status,
                                                                        LoyaltyScore loyaltyScore) {
        LoyaltyEvents loyaltyEvents = loyaltyTransferDao.getLoyaltyEventByEventId(loyaltyTransfer.getEventId());
        loyaltyEvents.setTransactionStatus(status);
        loyaltyEvents.setLoyaltyEventStatus(LoyaltyEventStatus.GIFTING_CANCELLED_OR_EXPIRED.name());
        loyaltyEvents.setReason(LoyaltyEventStatus.GIFTING_CANCELLED_OR_EXPIRED.name());
        updateLoyaltyEventsOnRevert(loyaltyEvents,loyaltyScore);
        return loyaltyEvents;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateLoyaltyEventsOnRevert(LoyaltyEvents event,LoyaltyScore loyaltyScore){
        List<LoyaltyLogHistory> loyaltyLogHistories =
                loyaltyTransferDao.getLoyaltyLogHistoryByTransactionEventId(event.getLoyaltyEventsId());
        if(!CollectionUtils.isEmpty(loyaltyLogHistories)){
            for(LoyaltyLogHistory history : loyaltyLogHistories){
                LoyaltyEvents loyaltyEvent = loyaltyDao.find(LoyaltyEvents.class,history.getLoyaltyEventId());
                if(Objects.nonNull(loyaltyEvent)){
                    Integer transactionPointInEvent = Math.abs(history.getTransactionPoints());
                    int currentRedeemedPoints = Math.abs(loyaltyEvent.getRedeemedPoints());
                    Integer updatedRedeemedPoints = Math.max(0,Math.abs(currentRedeemedPoints-transactionPointInEvent));
                    loyaltyEvent.setRedeemedPoints(updatedRedeemedPoints);
                    if(AppUtils.isBefore(loyaltyEvent.getExpirationTime(),AppUtils.getCurrentDate())){
                        Integer totalExpiredPoints = Math.abs(loyaltyEvent.getTransactionPoints()-loyaltyEvent.getRedeemedPoints());
                        loyaltyEvent.setExpiredPoints(loyaltyEvent.getExpiredPoints()+totalExpiredPoints);
                        loyaltyEvent.setLoyaltyEventStatus(LoyaltyEventStatus.EXPIRED.name());
                        Integer acquiredPoints = loyaltyScore.getAcquiredPoints();
                        Integer finalAcquiredPoints = Math.max(0,Math.abs(acquiredPoints - totalExpiredPoints));
                        LoyaltyEvents expirationEvent = loyaltyDao.getExpiredLoyaltyEvent(event.getCustomerId(),totalExpiredPoints,acquiredPoints,finalAcquiredPoints);
                        loyaltyScore.setAcquiredPoints(finalAcquiredPoints);
                        loyaltyScore.setTotalExpiredPoints(loyaltyScore.getTotalExpiredPoints()!=null?
                                loyaltyScore.getTotalExpiredPoints() +totalExpiredPoints: 0+totalExpiredPoints);
                        loyaltyDao.add(expirationEvent);
                        loyaltyDao.saveLoyaltyEventLog(loyaltyEvent.getLoyaltyEventsId(),-1*totalExpiredPoints,
                                LoyaltyEventStatus.EXPIRED.name(),TransactionType.CREDIT.name(), loyaltyEvent.getTransactionStatus(),-1,
                                expirationEvent.getLoyaltyEventsId());
                    }else{
                        loyaltyEvent.setLoyaltyEventStatus(LoyaltyEventStatus.ACTIVE.name());
                    }
                    history.setTransactionStatus(event.getTransactionStatus());
                    loyaltyDao.update(history);
                    loyaltyDao.update(loyaltyEvent);
                }
                loyaltyDao.update(loyaltyScore);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public LoyaltyTransfer claimGift(LoyaltyTransfer loyaltyTransfer, LoyaltyScore loyaltyScoreSender) {
        if (LoyaltyTransferStatus.CREATED.name().equals(loyaltyTransfer.getTransferStatus())) {
            CustomerInfo customerInfo = loyaltyTransferDao.getCustomerInfoByContactNumber(loyaltyTransfer.getReceiverContactNumber());
            LoyaltyScore loyaltyScoreOfReceiver = loyaltyTransferDao.getLoyalityScore(customerInfo.getCustomerId());
            loyaltyTransfer.setTransferStatus(LoyaltyTransferStatus.CLAIMED.name());
            loyaltyTransfer.setReceiverId(customerInfo.getCustomerId());
            loyaltyScoreOfReceiver.setAcquiredPoints(loyaltyScoreOfReceiver.getAcquiredPoints() + loyaltyTransfer.getTotalTransferred());
            loyaltyScoreOfReceiver.setCumulativePoints(loyaltyScoreOfReceiver.getCumulativePoints() + loyaltyTransfer.getTotalTransferred());
            loyaltyScoreSender.setBlockedPoints(loyaltyScoreSender.getBlockedPoints() - loyaltyTransfer.getSenderTransferPoints());
            loyaltyTransfer.setReceiverBalance(loyaltyScoreOfReceiver.getAcquiredPoints());
            loyaltyTransferDao.update(loyaltyScoreOfReceiver);
            loyaltyTransferDao.update(loyaltyScoreSender);
            LoyaltyEvents loyaltyEvents = createLoyaltyTransferEventForReceiver(loyaltyTransfer, customerInfo.getCustomerId());
            loyaltyTransferDao.add(loyaltyEvents);
            return loyaltyTransferDao.update(loyaltyTransfer);
        }
        return loyaltyTransfer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markExpired() {
        List<LoyaltyTransfer> loyaltyTransfers=loyaltyTransferDao.allExpiredLoyaltyTransfers();
        for(LoyaltyTransfer loyaltyTransfer : loyaltyTransfers){
            LoyaltyScore loyaltyScore=getLoyalityScore(loyaltyTransfer.getSenderId());
            loyaltyTransfer.setTransferStatus(LoyaltyTransferStatus.EXPIRED.name());
            loyaltyScore.setAcquiredPoints(loyaltyScore.getAcquiredPoints() + loyaltyTransfer.getSenderTransferPoints());
            loyaltyScore.setBlockedPoints(loyaltyScore.getBlockedPoints() - loyaltyTransfer.getSenderTransferPoints());
            loyaltyTransfer.setSenderBalance(loyaltyScore.getAcquiredPoints());
            //LoyaltyEvents loyaltyEvents = createLoyaltyTransferEventForSenderOnRevert(loyaltyTransfer);
            LoyaltyEvents loyaltyEvents = createLoyaltyTransferEventForSenderOnRevertNew(loyaltyTransfer,
                    LOYALTY_EVENT_EXPIRED_STATUS,loyaltyScore);
            loyaltyTransferDao.add(loyaltyEvents);
//            loyaltyTransferDao.update(loyaltyScore);
            loyaltyTransferDao.update(loyaltyTransfer);
        }
    }

    public LoyaltyEvents createLoyaltyTransferEventForReceiver(LoyaltyTransfer loyaltyTransfer, Integer receiverId) {
        LOG.info("in create loyalty transfer event for receiver");
        LoyaltyEvents loyaltyEvents = new LoyaltyEvents();
        loyaltyEvents.setCustomerId(receiverId);
        loyaltyEvents.setTransactionType(TransactionType.DEBIT.name());
        loyaltyEvents.setTransactionPoints(loyaltyTransfer.getTotalTransferred());
        loyaltyEvents.setTransactionCodeType("Addition");
        loyaltyEvents.setTransactionCode(LoyaltyEventType.LOYALTY_GIFTING.name());
        loyaltyEvents.setTransactionStatus("SUCCESS");
        loyaltyEvents.setTransactionTime(AppUtils.getCurrentTimestamp());
        loyaltyEvents.setEventId(loyaltyTransfer.getEventId());
        loyaltyEvents.setOpeningBalance(loyaltyTransfer.getReceiverInitialPoints());
        loyaltyEvents.setClosingBalance(loyaltyTransfer.getReceiverInitialPoints()+loyaltyTransfer.getTotalTransferred());
        loyaltyEvents.setLoyaltyEventStatus(LoyaltyEventStatus.ACTIVE.name());
        loyaltyEvents.setRedeemedPoints(0);
        loyaltyEvents.setExpiredPoints(0);
        Integer daysAfterLoyaltyExpire = environmentProperties.getDaysAfterLoyaltyExpire();
        Date expirationDate = AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(),daysAfterLoyaltyExpire);
        loyaltyEvents.setExpirationTime(AppUtils.getLastDayOfMonth(expirationDate));
        return loyaltyEvents;
    }

    @Override
    public boolean sendLoyaltyGiftingReminderSMS(LoyaltyGiftingSMSToken loyaltyGiftingSMSToken, String contactNumber) {
        return loyaltyTransferDao.sendLoyaltyGiftingReminderSMS(loyaltyGiftingSMSToken, contactNumber);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isCustomerEligibleToGiftLoyalty(Integer customerId,Integer totalTransferPoints){
        try {
            List<LoyaltyEvents> eventsList = loyaltyTransferDao.
                    getLoyaltyEventByCustomerIdExcludeGiftingEvent(customerId);
            if(!CollectionUtils.isEmpty(eventsList)){
                Integer currentPoints = 0;
                for(LoyaltyEvents e : eventsList){
                    currentPoints = currentPoints + Math.abs(
                            Math.abs(Math.abs(e.getTransactionPoints())-Math.abs(e.getRedeemedPoints())));
                }
                if(currentPoints >= totalTransferPoints){
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            LOG.info("Error is checking customer eligibility to gift loyalty for custoemr id : {} and error is : {}",
                    customerId,e);
        }
        return false;

    }





}
