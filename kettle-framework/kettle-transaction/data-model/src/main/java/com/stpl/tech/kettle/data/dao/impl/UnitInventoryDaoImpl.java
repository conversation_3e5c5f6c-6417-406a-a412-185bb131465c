/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.core.InventoryThresholdType;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.UnitInventoryDao;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.kettle.data.model.InventoryLogData;
import com.stpl.tech.kettle.data.model.InventoryThresholdData;
import com.stpl.tech.kettle.data.model.InventoryUpdateData;
import com.stpl.tech.kettle.data.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.data.model.UnitProductInventory;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.InventoryEventData;
import com.stpl.tech.kettle.domain.model.InventoryEventType;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.kettle.domain.model.StockOutEventObject;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.StockEventService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.unit.dao.UnitInventoryManagementDao;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.inventory.model.InventoryData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import com.stpl.tech.util.endpoint.Endpoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;

import javax.jms.JMSException;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Repository
public class UnitInventoryDaoImpl extends AbstractDaoImpl implements UnitInventoryDao {

	private static final Logger LOG = LoggerFactory.getLogger(UnitInventoryDaoImpl.class);
	public static final String SEND_STOCKOUT_NOTIFICATION = "send-stockout-notification";

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private MetadataCache metadataCache;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private DeliveryRequestService deliveryService;

	@Autowired(required = false)
	private UnitInventoryManagementDao masterDao;

	@Autowired
	private StockEventService stockEventService;

	public List<ProductInventory> getUnitInventory(int unitId) throws DataNotFoundException {
		Map<Integer, UnitProductInventory> currentInventory = null;
		List<Integer> units = new ArrayList<>();
		units.add(unitId);
		Map<Integer, Set<Integer>> productsForInventoryTracking = masterDao.getUnitProductMappings(units);
		try {
			// check for live inventory resource
			if (masterCache.getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
				// call service to get inventory data
				currentInventory = getUnitProductInventoryFromInventoryService(unitId);
			} else {
				currentInventory = getUnitProductInventory(unitId);
			}

		} catch (NoResultException e) {
			LOG.info(String.format("Did not find Unit Inventory Details For Unit with ID : %d", unitId), e);
		}
		List<ProductInventory> returnData = DataConverter.getInventoryDetails(masterCache,
				masterCache.getUnitBasicDetail(unitId), currentInventory, productsForInventoryTracking.get(unitId));
		return returnData;
	}

	private Map<Integer, UnitProductInventory> getUnitProductInventoryFromInventoryService(int unitId) {
		Map<Integer, UnitProductInventory> map = new HashMap<>();
		try {
			List<InventoryData> inventory = getInventory(unitId);
			for (InventoryData data : inventory) {
				if (map.containsKey(data.getId())) {
					UnitProductInventory i = map.get(data.getId());
					if (data.getQty() != null && i.getNoOfUnits() > data.getQty().intValue()) {
						i.setNoOfUnits(data.getQty().intValue());
					}
				} else {
					map.put(data.getId(), convertToProductInventory(data, unitId));
				}
			}
		} catch (Exception e) {
			LOG.info(String.format("Did not find Unit Inventory Details For Unit with ID : %d", unitId), e);
		}
		return map;
	}

	private UnitProductInventory convertToProductInventory(InventoryData data, int unitId) {
		UnitProductInventory i = new UnitProductInventory();
		i.setUnitId(unitId);
		i.setProductId(data.getId());
		i.setNoOfUnits(data.getQty() != null ? data.getQty().intValue() : 0);
		return i;
	}

	private List<InventoryData> getInventory(int unitId) {
		long startTime = System.currentTimeMillis();
		String unitZone =masterCache.getUnitBasicDetail(unitId).getUnitZone();
		String endPoint = props.getInventoryServiceBasePath() + Endpoints.INVENTORY_SERVICE_ENTRY_POINT
				+ (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE) +
				Endpoints.INVENTORY_SERVICE_VERSION + Endpoints.GET_CAFE_INVENTORY;
		// String token = props.getInventoryClientToken();
		Map<String, String> params = new HashMap<>();
		params.put("unitId", String.valueOf(unitId));
		List<InventoryData> data = new ArrayList<>();
		try {
			List<?> list = WebServiceHelper.getRequestWithParam(endPoint, null, params, List.class);
			GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
			if (list != null) {
				list.forEach(p -> {
					Gson gson = gSonBuilder.create();
					String str = gson.toJson(p);
					InventoryData cat = gson.fromJson(str, InventoryData.class);
					if (cat != null) {
						data.add(cat);
					}
				});
			} else {
				LOG.info("No Inventory Available for cafe {}", unitId);
			}
		} catch (Exception e) {
			LOG.error("Error while creating request for inventory for unit Id {}", unitId, e);
		}
		LOG.info("Downloaded cafe Inventory Data in {} miliseconds", System.currentTimeMillis() - startTime);
		return data;
	}

	public List<ProductInventory> getUnitInventory(int unitId, List<Integer> productIds) throws DataNotFoundException {
		List<ProductInventory> returnData = new ArrayList<>();
		UnitBasicDetail unit = masterCache.getUnitBasicDetail(unitId);
		List<UnitProductInventory> currentInventory = getInventoryForProducts(unitId, productIds);
		for (UnitProductInventory inventory : currentInventory) {
			ProductBasicDetail product = masterCache.getProductBasicDetail(inventory.getProductId());
			returnData.add(DataConverter.convertToInventory(unit, product, inventory));
		}
		return returnData;
	}

	@Override
	public Map<Integer, Integer> getUnitInventoryForWeb(int unitId) throws DataNotFoundException {
		Map<Integer, Integer> map = new HashMap<>();
		try {
			// check for live inventory resource
			if (masterCache.getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
				List<InventoryData> inventory = getInventory(unitId);
				for (InventoryData data : inventory) {
					if (ProductStatus.ACTIVE.equals(masterCache.getProduct(data.getId()).getStatus())) {
						if (data.getQty() != null) {
							map.put(data.getId(), data.getQty().intValue());
						}
					}
				}
			} else {
				List<UnitProductInventory> list = getInventoryForUnit(unitId);
				if (list != null) {
					list.forEach(p -> {
						if (ProductStatus.ACTIVE.equals(masterCache.getProduct(p.getProductId()).getStatus())) {
							map.put(p.getProductId(), p.getNoOfUnits());
						}
					});
				}
			}
		} catch (NoResultException e) {
			LOG.error("Did not find Unit Inventory Details For Unit with id {}", unitId, e);
		}
		return map;
	}

	public List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
			throws DataNotFoundException {

		Map<Integer, UnitProductInventory> currentInventory = new HashMap<>();
		List<Integer> units = new ArrayList<>();
		units.add(unitId);
		Map<Integer, Set<Integer>> productsForInventoryTracking = masterDao.getUnitProductMappings(units);
		try {
			if (masterCache.getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
				Set<Integer> checkSet = new HashSet<>(productIds);
				List<InventoryData> inventory = getInventory(unitId);
				for (InventoryData i : inventory) {
					if (checkSet.contains(i.getId())) {
						currentInventory.put(i.getId(), convertToProductInventory(i, unitId));
					}
				}
			} else {
				currentInventory = getUnitProductInventoryForProducts(unitId, productIds);
			}

		} catch (NoResultException e) {
			LOG.info(String.format("Did not find Unit Inventory Details For Unit Id : %d", unitId), e);
		}
		List<ProductInventory> returnData = DataConverter.getInventoryDetailsForProducts(masterCache,
				masterCache.getUnitBasicDetail(unitId), currentInventory, productsForInventoryTracking.get(unitId));
		return returnData;
	}

	@Override
	public Map<Integer, Integer> getUnitInventoryForProductsForWeb(int unitId, List<Integer> productIds)
			throws DataNotFoundException {
		Map<Integer, Integer> map = new HashMap<>();
		try {
			if (productIds != null && !productIds.isEmpty()) {
				List<UnitProductInventory> list = new ArrayList<>();
				if (masterCache.getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
					Set<Integer> checkSet = new HashSet<>(productIds);
					List<InventoryData> inventory = getInventory(unitId);
					for (InventoryData i : inventory) {
						if (checkSet.contains(i.getId())) {
							list.add(convertToProductInventory(i, unitId));
						}
					}
				} else {
					list = getInventoryForProducts(unitId, productIds);
				}
				if (list != null) {
					list.forEach(p -> map.put(p.getProductId(), p.getNoOfUnits()));
				}
			}
		} catch (NoResultException e) {
			LOG.error("Did not find Unit Inventory Details For Unit Id {}", unitId, e);
		} catch (Exception e) {
			LOG.error("Error in finding Unit Inventory Details For Unit Id {}", unitId, e);
			throw e;
		}
		return map;
	}

	public Map<String, List<ProductInventory>> getUnitInventoryByCategory(UnitCategory category)
			throws DataNotFoundException {
		List<UnitBasicDetail> unitList = masterCache.getUnits(category);
		List<Integer> unitIds = new ArrayList<>();
		Set<Integer> exclusions = props.getStockoutEmailExclusions();
		for (UnitBasicDetail detail : unitList) {
			if (UnitStatus.ACTIVE.equals(detail.getStatus())) {
				if (!exclusions.contains(detail.getId())) {
					unitIds.add(detail.getId());
				}
			}
		}
		Map<Integer, Map<Integer, UnitProductInventory>> map = getUnitsProductInventory(unitIds);
		Map<String, List<ProductInventory>> finalMap = new TreeMap<>();
		Map<Integer, Set<Integer>> productsForInventoryTracking = masterDao.getUnitProductMappings(unitIds);
		for (Integer unitId : map.keySet()) {
			List<ProductInventory> productList = DataConverter.getInventoryDetails(masterCache,
					masterCache.getUnitBasicDetail(unitId), map.get(unitId), productsForInventoryTracking.get(unitId));
			finalMap.put(unitId.toString(), productList);
		}
		return finalMap;
	}

	public Map<String, Set<ProductInventory>> getStockedOutUnitInventoryByCategory(UnitCategory category,
			InventoryThresholdType thresholdType) throws DataNotFoundException {
		Map<String, Set<ProductInventory>> unitsStockedOutInventory = new TreeMap<String, Set<ProductInventory>>();
		Map<String, List<ProductInventory>> unitsInventory = getUnitInventoryByCategory(category);
		LOG.info(unitsInventory.toString());
		Set<String> unitIds = (Set<String>) unitsInventory.keySet();
		for (String unitId : unitIds) {
			List<ProductInventory> productInventory = unitsInventory.get(unitId);
			Set<ProductInventory> stockedOutProductInventory = new TreeSet<ProductInventory>();
			for (ProductInventory pinventory : productInventory) {
				if (typeQualified(pinventory, thresholdType)) {
					stockedOutProductInventory.add(pinventory);
				}
			}
			if (!stockedOutProductInventory.isEmpty()) {
				unitsStockedOutInventory.put(unitId, stockedOutProductInventory);
			}
		}
		return unitsStockedOutInventory;
	}

	private boolean typeQualified(ProductInventory pinventory, InventoryThresholdType thresholdType) {
		switch (thresholdType) {
		case STOCK_OUT:
			if (pinventory.getQuantity() <= 0) {
				return true;
			}
			break;
		case PROBABLE_STOCK_OUT:
			if (pinventory.getThresholdData() == null
					|| pinventory.getQuantity() <= pinventory.getThresholdData().getAvgQuantity()) {
				return true;
			}
			break;
		default:
			break;
		}
		return false;
	}

	public Map<String, List<ProductInventory>> getLastHourStockedOutUnitInventory(
			Map<String, Set<ProductInventory>> stockedOutInventory, Date lastNotificationTime) {
		Map<String, List<ProductInventory>> lastHourStockedOutInventory = new HashMap<>();
		Set<String> unitIds = (Set<String>) stockedOutInventory.keySet();
		for (String unitId : unitIds) {
			Set<ProductInventory> productInventory = stockedOutInventory.get(unitId);
			List<ProductInventory> stockedOutProductInventory = new ArrayList<ProductInventory>();
			if (productInventory != null) {
				for (ProductInventory pinventory : productInventory) {
					if (lastNotificationTime == null || pinventory.getLastStockOutTime() == null
							|| pinventory.getLastStockOutTime().compareTo(lastNotificationTime) > 0) {
						stockedOutProductInventory.add(pinventory);
					}
				}
			}
			if (!stockedOutProductInventory.isEmpty()) {
				lastHourStockedOutInventory.put(unitId, stockedOutProductInventory);
			}
		}
		return lastHourStockedOutInventory;
	}

	public boolean stockOutInventory(int unitId, int productId) throws DataUpdationException {
		UnitProductInventory inventoryData = lookupUnitProductInventory(unitId, productId);
		setQuantity(inventoryData, 0, 0);
		UnitProductsStockEvent unitProductsStockEvent = new UnitProductsStockEvent();
		unitProductsStockEvent.setUnitId(unitId);
		unitProductsStockEvent.setStatus(StockStatus.STOCK_OUT);
		unitProductsStockEvent.getProductIds().add(Integer.valueOf(productId).toString());
		publishStockEvents(unitProductsStockEvent);
		return true;
	}

	public boolean updateUnitInventory(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData,
			boolean incrementalUpdate, int employeeId, Integer orderId, boolean isCancellation) {

		int unitId = updateData.getUnitId();
		Date businessDate = updateData.getBusinessDate();
		Map<Integer, UnitProductInventory> currentInventory = getUnitProductInventory(unitId);

		if (!incrementalUpdate) {
			logNonIncrementalStockEvent(updateData, employeeId, currentInventory);
		}

		UnitProductsStockEvent unitProductsStockOutEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_OUT);
		UnitProductsStockEvent unitProductsStockInEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_IN);

		for (ProductInventory product : updateData.getCurrentInventory()) {
			UnitProductInventory inventoryData = lookup(unitId, currentInventory,
					product.getProduct().getDetail().getId());
			Integer currentQty = inventoryData.getNoOfUnits();
			Integer finalQty = null;

			if (incrementalUpdate) {
				finalQty = proccessIncrementalInventoryUpdate(product, inventoryData, employeeId, orderId,
						isCancellation, unitId, businessDate);
			} else {
				setQuantity(inventoryData, product.getQuantity(), product.getExpireQuantity());
				finalQty = product.getQuantity();
			}

			if (currentQty > 0 && finalQty <= 0) {
				unitProductsStockOutEvent.getProductIds()
						.add(Integer.valueOf(product.getProduct().getDetail().getId()).toString());
			} else if (currentQty <= 0 && finalQty > 0) {
				unitProductsStockInEvent.getProductIds()
						.add(Integer.valueOf(product.getProduct().getDetail().getId()).toString());
			}
		}
		publishStockEvents(unitProductsStockOutEvent);
		publishStockEvents(unitProductsStockInEvent);
		manager.flush();
		return true;
	}

	private Integer proccessIncrementalInventoryUpdate(ProductInventory product, UnitProductInventory inventoryData,
			int employeeId, Integer orderId, boolean isCancellation, int unitId, Date businessDate) {

		if (!isCancellation && orderId != null && inventoryData.getNoOfUnits() > 0
				&& inventoryData.getNoOfUnits() <= product.getQuantity()) {
			logStockEvent(product.getProduct().getDetail().getId(), unitId, employeeId,
					InventoryEventType.STOCK_OUT.value(), InventoryEventType.ORDER_PUNCH.value(), businessDate,
					orderId);
		} else if (orderId == null && inventoryData.getNoOfUnits() > 0
				&& inventoryData.getNoOfUnits() <= product.getQuantity()) {
			logStockEvent(product.getProduct().getDetail().getId(), unitId, employeeId,
					InventoryEventType.STOCK_OUT.value(), InventoryEventType.CAFE_WASTAGE.value(), businessDate,
					orderId);
		}

		int multiplier = isCancellation ? -1 : 1;
		inventoryData.setNoOfUnits(inventoryData.getNoOfUnits() - (multiplier * product.getQuantity()));

		/**
		 * This is a bit tricky as when there is no expire quantity we cannot increment
		 * on order cancellation as we do not know if the order has expire product
		 * quantity or not, currently we do nothing in such case.
		 *
		 */
		if (inventoryData.getExpireQuantity() > 0 && inventoryData.getExpireQuantity() >= product.getQuantity()) {
			inventoryData
					.setExpireQuantity(inventoryData.getExpireQuantity() - (multiplier * product.getExpireQuantity()));
		} else {
			inventoryData.setExpireQuantity(0);
		}

		Integer finalQty = inventoryData.getNoOfUnits();

		if (inventoryData.getNoOfUnits() <= 1) {
			stockOutSlack(inventoryData, unitId, employeeId, product);
		}

		inventoryData.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
		return finalQty;
	}

	private void stockOutSlack(UnitProductInventory inventoryData, int unitId, int employeeId,
			ProductInventory product) {
		try {
			Unit unit = masterCache.getUnit(unitId);
			String employee = masterCache.getEmployee(employeeId);

			String message = String.format(
					"Unit : *%s*\nProduct : *%s*\nTime : %s\nEmployee : %s\nCurrent Stock : *%d*\nLast Update Time : %s",
					unit.getName(), product.getProduct().getDetail().getName(), AppUtils.getCurrentTimestamp(),
					employee, inventoryData.getNoOfUnits(), inventoryData.getLastUpdateTmstmp());
			Set<String> channels = new HashSet<>();
			if (unit.getManagerChannel() != null) {
				channels.add(unit.getManagerChannel());
			} else {
				LOG.error(" ::: Did not find manager channel while sending stock out notification :::");
			}
			if (unit.getCafeManager() != null && unit.getCafeManager().getId() != 0) {
				channels.add(masterCache.getEmployeeBasicDetail(unit.getCafeManager().getId()).getSlackChannel());
			}

			for (String channel : channels) {
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle",
						AppUtils.isDev(props.getEnvironmentType()) ? null : channel, null, message);
			}

			try {
				StockOutEventObject stockOutEventObject = new StockOutEventObject(unit.getName(), product.getProduct().getDetail().getId(), product.getProduct().getDetail().getName(), inventoryData.getNoOfUnits(), inventoryData.getLastUpdateTmstmp(), unit.getCafeManager().getId(), unit.getManagerId());
				WebServiceHelper.postWithAuth(props.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + SEND_STOCKOUT_NOTIFICATION, props.getKnockMasterToken(), stockOutEventObject, Boolean.class);
			}catch (Exception e){
				LOG.error("Error while sending stock out notification to knock::" , e);
			}

			// for publishing in channel when direct users don't exist
			SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle", null,
					SlackNotification.STOCK_OUT_EVENT.getChannel(props.getEnvironmentType()), message);

		} catch (Exception e) {
			LOG.error("Error while sending slack notification", e);
		}

	}

	private void publishStockEvents(UnitProductsStockEvent unitProductsStockEvent) {
		if (unitProductsStockEvent != null && unitProductsStockEvent.getProductIds().size() > 0) {
			try {
				stockEventService.publishStockEvent(props.getEnvironmentType().name(), unitProductsStockEvent);
			} catch (JMSException e) {
				LOG.error("Error publishing STOCK event:::", e);
			} catch (Exception e) {
				LOG.error("Error publishing STOCK event:::", e);
			}
		}
	}

	private void logNonIncrementalStockEvent(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData,
			int employeeId, Map<Integer, UnitProductInventory> currentInventory) {
		int unitId = updateData.getUnitId();
		for (InventoryEventData productInventory : updateData.getUpdatedInventory()) {
			UnitProductInventory inventoryData = lookup(unitId, currentInventory, productInventory.getProductId());
			if (updateData.getType() == InventoryEventType.STOCK_IN && inventoryData.getNoOfUnits() <= 0
					&& productInventory.getQuantity() > (0 - inventoryData.getNoOfUnits())) {
				logStockEvent(productInventory.getProductId(), unitId, employeeId, InventoryEventType.STOCK_IN.value(),
						InventoryEventType.STOCK_IN.value(), updateData.getBusinessDate(), null);
			}
			if ((updateData.getType() == InventoryEventType.TRANSFER_OUT
					|| updateData.getType() == InventoryEventType.WASTAGE)
					&& (inventoryData.getNoOfUnits() <= productInventory.getQuantity())) {
				logStockEvent(productInventory.getProductId(), unitId, employeeId, InventoryEventType.STOCK_OUT.value(),
						updateData.getType().value(), updateData.getBusinessDate(), null);
			}
			if (updateData.getType() == InventoryEventType.UPDATE) {
				if (productInventory.getQuantity() == 0) {
					logStockEvent(productInventory.getProductId(), unitId, employeeId,
							InventoryEventType.STOCK_OUT.value(), updateData.getType().value(),
							updateData.getBusinessDate(), null);
				} else if (inventoryData.getNoOfUnits() <= 0) {
					logStockEvent(productInventory.getProductId(), unitId, employeeId,
							InventoryEventType.STOCK_IN.value(), updateData.getType().value(),
							updateData.getBusinessDate(), null);
				}
			}
		}
	}

	private void logStockEvent(int productId, int unitId, int employeeId, String eventType, String reasonCode,
			Date businessDate, Integer orderId) {
		InventoryLogData inventoryLogData = new InventoryLogData();
		inventoryLogData.setEmployeeId(employeeId);
		inventoryLogData.setEventType(eventType);
		inventoryLogData.setProductId(productId);
		inventoryLogData.setReasonCode(reasonCode);
		inventoryLogData.setUnitId(unitId);
		inventoryLogData.setUpdateTime(AppUtils.getCurrentTimestamp());
		inventoryLogData.setBusinessDate(businessDate);
		inventoryLogData.setOrderId(orderId);
		manager.persist(inventoryLogData);
	}

	private UnitProductInventory create(int unitId, int productId, int quantity, int expireQuantity) {
		UnitProductInventory data = new UnitProductInventory();
		data.setUnitId(unitId);
		data.setProductId(productId);
		data.setNoOfUnits(quantity);
		data.setProductInventoryStatus(AppConstants.ACTIVE);
		data.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
		data.setExpireQuantity(expireQuantity);
		if (quantity == 0) {
			data.setLastStockOutTime(AppUtils.getCurrentTimestamp());
		}
		manager.persist(data);
		manager.flush();
		return data;
	}

	private void setQuantity(UnitProductInventory inventoryData, int quantity, int expireQuantity) {
		if (inventoryData.getNoOfUnits() != quantity || inventoryData.getExpireQuantity() != expireQuantity) {
			inventoryData.setNoOfUnits(quantity);
			inventoryData.setProductInventoryStatus(AppConstants.ACTIVE);
			inventoryData.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
			inventoryData.setExpireQuantity(expireQuantity);
			if (quantity == 0) {
				inventoryData.setLastStockOutTime(AppUtils.getCurrentTimestamp());
			}
			manager.flush();
		}
	}

	/**
	 * This should be called from within a write transaction
	 *
	 * @param unitId
	 * @param currentInventory
	 * @param productId
	 * @return
	 */
	private UnitProductInventory lookup(int unitId, Map<Integer, UnitProductInventory> currentInventory,
			int productId) {
		return currentInventory == null || currentInventory.get(productId) == null ? create(unitId, productId, 0, 0)
				: currentInventory.get(productId);
	}

	/**
	 * This should be called from within a write transaction
	 *
	 * @param unitId
	 * @param productId
	 * @return
	 * @throws DataUpdationException
	 */
	private UnitProductInventory lookupUnitProductInventory(int unitId, int productId) throws DataUpdationException {
		Object data = null;
		try {
			Query query = manager
					.createQuery("FROM UnitProductInventory where unitId = :unitId and productId = :productId");
			query.setParameter("unitId", unitId);
			query.setParameter("productId", productId);
			data = query.getSingleResult();
		} catch (NoResultException e) {
			data = create(unitId, productId, 0, 0);
		} catch (Exception e) {
			String errorMssge = String.format("Could not create inventory data for unit %d and product %d", unitId,
					productId);
			throw new DataUpdationException(errorMssge, e);
		}

		return data == null ? null : (UnitProductInventory) data;
	}

	public Map<Integer, UnitProductInventory> getUnitProductInventory(int unitId) {
		Map<Integer, UnitProductInventory> map = new HashMap<>();
		try {
			List<UnitProductInventory> list = getInventoryForUnit(unitId);
			for (UnitProductInventory data : list) {
				map.put(data.getProductId(), data);
			}
		} catch (NoResultException e) {
			LOG.info(String.format("Did not find Unit Inventory Details For Unit with ID : %d", unitId), e);
		}
		return map;
	}

	private List<UnitProductInventory> getInventoryForUnit(int unitId) {
		Query query = manager.createQuery(
				"FROM UnitProductInventory where unitId = :unitId and productInventoryStatus = :productInventoryStatus");
		query.setParameter("productInventoryStatus", AppConstants.ACTIVE);
		query.setParameter("unitId", unitId);
		return query.getResultList();
	}

	private List<UnitProductInventory> getInventoryForUnit(int unitId, List<Integer> productIds) {
		Query query = manager.createQuery(
				"FROM UnitProductInventory where unitId = :unitId and productId IN (:productIds) and productInventoryStatus = :productInventoryStatus");
		query.setParameter("productInventoryStatus", AppConstants.ACTIVE);
		query.setParameter("unitId", unitId);
		query.setParameter("productIds", productIds);
		return query.getResultList();
	}

	public Map<Integer, UnitProductInventory> getUnitProductInventoryForProducts(int unitId, List<Integer> productIds) {
		Map<Integer, UnitProductInventory> map = new HashMap<>();
		List<UnitProductInventory> list = getInventoryForProducts(unitId, productIds);
		for (UnitProductInventory data : list) {
			map.put(data.getProductId(), data);
		}
		return map;
	}

	private List<UnitProductInventory> getInventoryForProducts(int unitId, List<Integer> productIds) {
		Query query = manager
				.createQuery("FROM UnitProductInventory where unitId = :unitId and productId IN ( :productIds)");
		query.setParameter("unitId", unitId);
		query.setParameter("productIds", productIds);
		return query.getResultList();
	}

	public Map<Integer, Map<Integer, UnitProductInventory>> getUnitsProductInventory(List<Integer> unitIds) {
		Map<Integer, Map<Integer, UnitProductInventory>> map = new TreeMap<>();
		Query query = manager.createQuery(
				"FROM UnitProductInventory where unitId IN ( :unitIds) and productInventoryStatus =:productInventoryStatus order by unitId, productId");
		query.setParameter("productInventoryStatus", AppConstants.ACTIVE);
		query.setParameter("unitIds", unitIds);
		@SuppressWarnings("unchecked")
		List<UnitProductInventory> list = query.getResultList();
		for (UnitProductInventory data : list) {
			Map<Integer, UnitProductInventory> datas = map.get(data.getUnitId());
			if (datas == null) {
				datas = new TreeMap<>();
				map.put(data.getUnitId(), datas);
			}
			datas.put(data.getProductId(), data);
		}
		return map;
	}

	public Map<Integer, com.stpl.tech.kettle.domain.model.InventoryThresholdData> getInventoryThresholdData(
			int unitId) {
		Map<Integer, com.stpl.tech.kettle.domain.model.InventoryThresholdData> map = new HashMap<>();
		Query query = manager.createQuery(
				"FROM InventoryThresholdData where unitId = :unitId and thresholdDataStatus = :thresholdDataStatus");
		query.setParameter("unitId", unitId);
		query.setParameter("thresholdDataStatus", "ACTIVE");
		@SuppressWarnings("unchecked")
		List<InventoryThresholdData> list = query.getResultList();
		if (list != null) {
			for (InventoryThresholdData data : list) {
				map.put(data.getProductId(), DataConverter.convert(data));
			}
		}
		return map;
	}

	@Override
	public void addUpdateUnitDeliveryMappings(Unit unit) throws DataUpdationException {
		List<UnitToDeliveryPartnerMappings> unitToDeliveryPartnerMappings = new ArrayList<UnitToDeliveryPartnerMappings>();
		for (PartnerDetail profile : unit.getDeliveryPartners()) {
			unitToDeliveryPartnerMappings.add(createUnitToDeliveryPartnerMappings(profile, unit.getId()));
		}

		if (unit.getInventoryCloneUnitId() != null) {
			cloneProductInventoryMappings(unit.getId(), unit.getInventoryCloneUnitId());
		}

	}

	@Override
	public void updateUnitToDeliveryPartnerMappings(int unitId, List<PartnerDetail> profiles) {
		Query query = manager.createQuery("FROM UnitToDeliveryPartnerMappings where unitId = :unitId");
		query.setParameter("unitId", unitId);
		@SuppressWarnings("unchecked")
		List<UnitToDeliveryPartnerMappings> mappings = query.getResultList();
		Map<Integer, UnitToDeliveryPartnerMappings> map = new HashMap<>();
		if (mappings != null) {
			for (UnitToDeliveryPartnerMappings data : mappings) {
				map.put(data.getDeliveryPartner().getPartnerId(), data);
			}
		}
		Set<Integer> updatedPartners = new HashSet<>();
		for (PartnerDetail detail : profiles) {
			UnitToDeliveryPartnerMappings mapping = map.get(detail.getDetail().getId());
			if (mapping == null) {
				mapping = createUnitToDeliveryPartnerMappings(detail, unitId);
			} else {
				mapping.setPriority(detail.getPriority());
			}
			manager.merge(mapping);
			manager.flush();
			updatedPartners.add(detail.getDetail().getId());
		}

		for (Integer partnerId : map.keySet()) {
			if (!updatedPartners.contains(partnerId)) {
				map.get(partnerId).setPriority(0);
			}
		}

		deliveryService.refreshPriorityCache(); // refreshing the delivery
		// partner mappings cache
	}

	private UnitToDeliveryPartnerMappings createUnitToDeliveryPartnerMappings(PartnerDetail profile, int unitId) {
		UnitToDeliveryPartnerMappings mapping = new UnitToDeliveryPartnerMappings(unitId,
				getDeliveryPartner(profile.getDetail().getId()), profile.getPriority());
		return mapping;
	}

	private DeliveryPartner getDeliveryPartner(int partnerId) {
		return manager.find(com.stpl.tech.kettle.data.model.DeliveryPartner.class, partnerId);
	}

	@Override
	public void cloneProductInventoryMappings(int unitId, int cloneUnitId) throws DataUpdationException {

		Query query = manager.createQuery("FROM UnitProductInventory where unitId = :unitId");
		query.setParameter("unitId", cloneUnitId);
		@SuppressWarnings("unchecked")
		List<UnitProductInventory> list = query.getResultList();
		for (UnitProductInventory data : list) {
			create(unitId, data.getProductId(), 0, 0);
		}

	}

	@Override
	public boolean addInventoryEvent(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		// LOG.info(new Gson().toJson(updateData));
		InventoryUpdateEvent data = new InventoryUpdateEvent();
		data.setBusinessDate(updateData.getBusinessDate());
		data.setEventType(updateData.getType().value());
		data.setRecordsCount(updateData.getUpdatedInventory().size());
		data.setUnitId(updateData.getUnitId());
		data.setUpdateComment(updateData.getComment());
		data.setUpdatedBy(updateData.getUpdatedBy());
		data.setUpdateStatus(TransitionStatus.SUCCESS.name());
		data.setUpdateTime(currentTime);

		manager.persist(data);
		manager.flush();
		for (InventoryEventData inventory : updateData.getUpdatedInventory()) {
			InventoryUpdateData update = new InventoryUpdateData();
			update.setAddTime(currentTime);
			update.setBusinessDate(updateData.getBusinessDate());
			update.setInventoryUpdateEventId(data.getInventoryUpdateEventId());
			update.setProductId(inventory.getProductId());
			update.setQuantity(inventory.getQuantity());
			update.setThresholdQuantity(inventory.getThresholdQuantity());
			update.setUnitId(inventory.getUnitId());
			update.setExpireQuantity(inventory.getExpireQuantity());
			manager.persist(update);
		}
		manager.flush();
		return true;
	}

	@Override
	public Collection<IdCodeName> getUnitToDeliveryMappings(int unitId) {
		List<UnitToDeliveryPartnerMappings> deliveryPartners = deliveryService.getUnitPartnerMappings();

		Collection<IdCodeName> returnList = deliveryPartners.stream()
				.filter(new Predicate<UnitToDeliveryPartnerMappings>() {
					@Override
					public boolean test(UnitToDeliveryPartnerMappings t) {
						return t.getUnitId() == unitId;
					}
				}).map(new Function<UnitToDeliveryPartnerMappings, IdCodeName>() {

					@Override
					public IdCodeName apply(UnitToDeliveryPartnerMappings t) {
						IdCodeName obj = metadataCache.getDeliveryPartner(t.getDeliveryPartner().getPartnerId());
						if(Objects.nonNull(obj) && Objects.nonNull(masterCache.getUnitBasicDetail(unitId)) ){
							obj.setZone(masterCache.getUnitBasicDetail(unitId).getUnitZone());
						}
						return obj;
					}
				}).collect(Collectors.toList());

		return returnList;
	}
}
