package com.stpl.tech.kettle.service.notification;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.core.external.notification.FireStoreNotificationType;
import com.stpl.tech.master.core.external.notification.FirebaseNotification;

/**
 * Created by shikhar on 12/6/19.
 */
public class OrderPushNotification implements FirebaseNotification {

	private Map<String, String> message;
	private String topic;
	private String sendToAndroid;

	public OrderPushNotification(Integer orderId, OrderStatus reason, String source, Integer unitId,
								 String assemblyFirestoreUnits, String isAssemblyFirestoreEnabledForAll, FireStoreNotificationType fireStoreNotificationType) {
		this.message = new HashMap<>();
		this.message.put("orderId", orderId.toString());
		this.message.put("unitId", unitId.toString());
		this.message.put("reason", reason.name());
		this.message.put("source", source);
		this.message.put("assemblyFirestoreUnits", assemblyFirestoreUnits);
		this.message.put("isAssemblyFirestoreEnabledForAll", isAssemblyFirestoreEnabledForAll);
		this.message.put("fireStoreNotificationType", fireStoreNotificationType.name());
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}

	@Override
	public String getTopic() {
		return topic;
	}

	@Override
	public Object getData() {
		return this.message;
	}

	@Override
	public String getTitle() {
		return "Order Received";
	}

	@Override
	public String getMessage() {
		return "Order Message Received";
	}

	@Override
	public String sendToAndroid() {
		return this.sendToAndroid;
	}

	public void setSendToAndroid(String sendToAndroid) {
		this.sendToAndroid = sendToAndroid;
	}
}
