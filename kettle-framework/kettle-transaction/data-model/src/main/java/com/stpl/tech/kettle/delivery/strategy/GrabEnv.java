/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

public enum GrabEnv {

    TEST("http://dev.cpanel.grab.in/Api/Pos/"), PRODUCTION("http://api.grab.in/pos/");

    private String value;
    private static GrabEnv[] values;

    GrabEnv(String env) {
        this.value = env;
    }

    public String getValue() {
        return this.value;
    }

    public static GrabEnv[] environments() {
        if (values == null) {
            values = GrabEnv.values();
        }
        return values;
    }
}
