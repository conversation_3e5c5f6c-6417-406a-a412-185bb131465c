/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.util.RequestSigner;
import com.stpl.tech.kettle.delivery.adapter.OPNRequestAdapter;
import com.stpl.tech.kettle.delivery.adapter.OPNResponseAdapter;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.OPNErrorResponse;
import com.stpl.tech.kettle.delivery.model.OPNMerchant;
import com.stpl.tech.kettle.delivery.model.OPNRequest;
import com.stpl.tech.kettle.delivery.model.OPNResponse;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Unit;
import org.apache.http.HttpResponse;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.security.SignatureException;
import java.util.HashMap;
import java.util.Map;

public class OPNExecutor extends AbstractDeliveryStrategy implements DeliveryExecutionStrategy {

	private static Logger LOG = LoggerFactory.getLogger(OPNExecutor.class);

	private OPNRequest requestObject = new OPNRequest();
	private OPNRequestAdapter requestAdapter = new OPNRequestAdapter();

	private OPNResponse responseObject = new OPNResponse();
	private OPNResponseAdapter responseAdapter = new OPNResponseAdapter();
	private OPNErrorResponse error;

	private static final String API_KEY = "G7wiLl8rQXahOgVCcNTOpA==";
	private static final String CALLBACK_URL = "kettle-service/rest/v1/delivery-management/update/"+API_KEY;
	private String host = OPNEnv.TEST.getValue();

	String authorizationHeader = null;

	@Override
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props) {
		requestObject = requestAdapter.adaptCreate(order);

		if (!TransactionUtils.isDev(props.getEnvironmentType())) {
			requestObject.setCallbackUrl(CallBackEnv.DEV_CALLBACK.toString()+ CALLBACK_URL);
		} else {
			requestObject.setCallbackUrl(CallBackEnv.PROD_CALLBACK.toString()+ CALLBACK_URL);
		}

		return readResponse(createRequest(HttpMethod.POST, creationEndpoint, props, requestObject),
				order.getOrder().getGenerateOrderId());
	}

	@Override
	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo,
										   EnvironmentProperties props) {
		Map<String, Integer> request = new HashMap<>();
		request.put("is_cancelled", 1);
		String orderId = orderInfo.getOrder().getGenerateOrderId();
		cancelationEndpoint = replaceInEndPoint(cancelationEndpoint, "<order_code>", orderId);
		LOG.info("Cancellation endpoint is :::: {}", cancelationEndpoint);
		return readResponse(createRequest(HttpMethod.PUT, cancelationEndpoint, props, request), orderId);
	}

	@Override
	public void setAuthorizationObject(AuthorizationObject authorization) {
		this.authorization = authorization;

	}

	@Override
	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props) {
		try {
			OPNMerchant request = new OPNMerchant(unit);
			HttpResponse responseFromRequest = createRequest(HttpMethod.POST, registerEndpoint, props, request);
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()) {

				OPNMerchant merchant = WebServiceHelper.convertResponse(responseFromRequest, OPNMerchant.class);
				LOG.info("received response for merchant request :::: {}", WebServiceHelper.convertToString(merchant));
				return WebServiceHelper.convertToString(merchant);
			} else {
				error = WebServiceHelper.convertResponse(responseFromRequest, OPNErrorResponse.class);
				LOG.info("received response for merchant request :::: {}", WebServiceHelper.convertToString(error));
				return WebServiceHelper.convertToString(error);
			}
		} catch (IllegalStateException | IOException e) {
			LOG.error("JSON Parsing exception", e);
		}
		return null;
	}

	private <T> HttpResponse createRequest(HttpMethod method, String endpoint, EnvironmentProperties props, T request) {
		String canonicalQS = RequestSigner.paramterizeValues(request);
		LOG.info("HttpRequest params before request sent ::::: {}", canonicalQS);

		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			host = OPNEnv.TEST.getValue();
		} else {
			host = OPNEnv.PRODUCTION.getValue();
		}

		try {
			String uri = host + endpoint;
			String authorizationHeader = RequestSigner.getSignedHeader(method, host, endpoint, authorization,
					canonicalQS);
			LOG.info("authorizationHeader :::: {}", authorizationHeader);
			HttpResponse responseFromRequest = null;

			switch (method) {

			case POST: {
				StringEntity entity = new StringEntity(canonicalQS, ContentType.APPLICATION_FORM_URLENCODED);
				responseFromRequest = WebServiceHelper.postRequest(uri, authorizationHeader, entity);
				break;
			}

			case GET: {
				responseFromRequest = WebServiceHelper.getRequest(uri += "?" + canonicalQS, authorizationHeader);
				break;
			}

			case PUT: {
				StringEntity entity = new StringEntity(canonicalQS, ContentType.APPLICATION_FORM_URLENCODED);
				responseFromRequest = WebServiceHelper.putRequest(uri, authorizationHeader, entity);
				break;
			}

			default:
				break;

			}
			if (responseFromRequest != null) {
				LOG.info("HttpResponse after request sent ::::: {} :::: {}",
						responseFromRequest.getStatusLine().getStatusCode(),
						responseFromRequest.getStatusLine().getReasonPhrase());
			}
			return responseFromRequest;
		} catch (SignatureException | IOException e) {
			LOG.error("exception", e);
		}
		return null;
	}

	@Override
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId) {
		try {
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()) {
				responseObject = WebServiceHelper.convertResponse(responseFromRequest, OPNResponse.class);
				return responseAdapter.adapt(responseObject, orderId);
			} else {
				error = WebServiceHelper.convertResponse(responseFromRequest, OPNErrorResponse.class);
				return responseAdapter.adaptError(error, orderId);
			}

		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}
		return null;
	}

}
