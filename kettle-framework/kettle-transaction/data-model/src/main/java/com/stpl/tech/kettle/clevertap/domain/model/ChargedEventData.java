package com.stpl.tech.kettle.clevertap.domain.model;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

public class ChargedEventData {

    Integer Pax;
    List<ChargedEventItemData> Items;
    Integer UnitId;
    Integer BrandId;
    Integer OrderId;//
    String UnitName;
    String OfferCode;
    String OrderType;
    Integer CustomerId;
    BigDecimal FoodAmount;
    String OfferClass;
    String OrderSource;
    String OrderStatus;
    BigDecimal TotalAmount;
    String BusinessDate;//
    BigDecimal DiscountRate;
    Integer FoodQuantity;
    Integer GcPaymentFlag;//
    String IsNewCustomer;
    Integer OfferDetailId;
    BigDecimal TaxableAmount;
    Integer TotalQuantity;
    BigDecimal BeverageAmount;
    BigDecimal DiscountAmount;
    Integer GcPurchaseFlag;
    String OrderFoodClass;
    Integer PointsRedeemed;
    BigInteger PreviousOrderId;//
    Integer BeverageQuantity;
    Integer ChannelPartnerId;
    String BillingServerTime;
    Integer DeliveryPartnerId;
    BigInteger OverallOrderCount;
    BigInteger OrderSourceOrderCount;
    String PreviousOrderTimediff;
    BigInteger PreviousSourceOrderId;
    Integer SubscriptionPurchaseFlag;
    String PreviousBillingServerTime;
    String PreviousSourceOrderTimediff;
    String PreviousSourceBillingServerTime;

    public Integer getPax() {
        return Pax;
    }

    public void setPax(Integer pax) {
        Pax = pax;
    }

    public List<ChargedEventItemData> getItems() {
        return Items;
    }

    public void setItems(List<ChargedEventItemData> items) {
        Items = items;
    }

    public Integer getUnitId() {
        return UnitId;
    }

    public void setUnitId(Integer unitId) {
        UnitId = unitId;
    }

    public Integer getBrandId() {
        return BrandId;
    }

    public void setBrandId(Integer brandId) {
        BrandId = brandId;
    }

    public Integer getOrderId() {
        return OrderId;
    }

    public void setOrderId(Integer orderId) {
        OrderId = orderId;
    }

    public String getUnitName() {
        return UnitName;
    }

    public void setUnitName(String unitName) {
        UnitName = unitName;
    }

    public String getOfferCode() {
        return OfferCode;
    }

    public void setOfferCode(String offerCode) {
        OfferCode = offerCode;
    }

    public String getOrderType() {
        return OrderType;
    }

    public void setOrderType(String orderType) {
        OrderType = orderType;
    }

    public Integer getCustomerId() {
        return CustomerId;
    }

    public void setCustomerId(Integer customerId) {
        CustomerId = customerId;
    }

    public BigDecimal getFoodAmount() {
        return FoodAmount;
    }

    public void setFoodAmount(BigDecimal foodAmount) {
        FoodAmount = foodAmount;
    }

    public String getOfferClass() {
        return OfferClass;
    }

    public void setOfferClass(String offerClass) {
        OfferClass = offerClass;
    }

    public String getOrderSource() {
        return OrderSource;
    }

    public void setOrderSource(String orderSource) {
        OrderSource = orderSource;
    }

    public String getOrderStatus(String orderStatus) {
        return OrderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        OrderStatus = orderStatus;
    }

    public BigDecimal getTotalAmount() {
        return TotalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        TotalAmount = totalAmount;
    }

    public String getBusinessDate() {
        return BusinessDate;
    }

    public void setBusinessDate(String businessDate) {
        BusinessDate = businessDate;
    }

    public BigDecimal getDiscountRate() {
        return DiscountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        DiscountRate = discountRate;
    }

    public Integer getFoodQuantity() {
        return FoodQuantity;
    }

    public void setFoodQuantity(Integer foodQuantity) {
        FoodQuantity = foodQuantity;
    }

    public Integer getGcPaymentFlag() {
        return GcPaymentFlag;
    }

    public void setGcPaymentFlag(Integer gcPaymentFlag) {
        GcPaymentFlag = gcPaymentFlag;
    }

    public String getIsNewCustomer() {
        return IsNewCustomer;
    }

    public void setIsNewCustomer(String isNewCustomer) {
        IsNewCustomer = isNewCustomer;
    }

    public Integer getOfferDetailId() {
        return OfferDetailId;
    }

    public void setOfferDetailId(Integer offerDetailId) {
        OfferDetailId = offerDetailId;
    }

    public BigDecimal getTaxableAmount() {
        return TaxableAmount;
    }

    public void setTaxableAmount(BigDecimal taxableAmount) {
        TaxableAmount = taxableAmount;
    }

    public Integer getTotalQuantity() {
        return TotalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        TotalQuantity = totalQuantity;
    }

    public BigDecimal getBeverageAmount() {
        return BeverageAmount;
    }

    public void setBeverageAmount(BigDecimal beverageAmount) {
        BeverageAmount = beverageAmount;
    }

    public BigDecimal getDiscountAmount() {
        return DiscountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        DiscountAmount = discountAmount;
    }

    public Integer getGcPurchaseFlag() {
        return GcPurchaseFlag;
    }

    public void setGcPurchaseFlag(Integer gcPurchaseFlag) {
        GcPurchaseFlag = gcPurchaseFlag;
    }

    public String getOrderFoodClass() {
        return OrderFoodClass;
    }

    public void setOrderFoodClass(String orderFoodClass) {
        OrderFoodClass = orderFoodClass;
    }

    public Integer getPointsRedeemed() {
        return PointsRedeemed;
    }

    public void setPointsRedeemed(Integer pointsRedeemed) {
        PointsRedeemed = pointsRedeemed;
    }

    public BigInteger getPreviousOrderId() {
        return PreviousOrderId;
    }

    public void setPreviousOrderId(BigInteger previousOrderId) {
        PreviousOrderId = previousOrderId;
    }

    public Integer getBeverageQuantity() {
        return BeverageQuantity;
    }

    public void setBeverageQuantity(Integer beverageQuantity) {
        BeverageQuantity = beverageQuantity;
    }

    public Integer getChannelPartnerId() {
        return ChannelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        ChannelPartnerId = channelPartnerId;
    }

    public String getBillingServerTime() {
        return BillingServerTime;
    }

    public void setBillingServerTime(String billingServerTime) {
        BillingServerTime = billingServerTime;
    }

    public Integer getDeliveryPartnerId() {
        return DeliveryPartnerId;
    }

    public void setDeliveryPartnerId(Integer deliveryPartnerId) {
        DeliveryPartnerId = deliveryPartnerId;
    }

    public BigInteger getOverallOrderCount() {
        return OverallOrderCount;
    }

    public void setOverallOrderCount(BigInteger overallOrderCount) {
        OverallOrderCount = overallOrderCount;
    }

    public BigInteger getOrderSourceOrderCount() {
        return OrderSourceOrderCount;
    }

    public void setOrderSourceOrderCount(BigInteger orderSourceOrderCount) {
        OrderSourceOrderCount = orderSourceOrderCount;
    }

    public String getPreviousOrderTimediff() {
        return PreviousOrderTimediff;
    }

    public void setPreviousOrderTimediff(String previousOrderTimediff) {
        PreviousOrderTimediff = previousOrderTimediff;
    }

    public BigInteger getPreviousSourceOrderId() {
        return PreviousSourceOrderId;
    }

    public void setPreviousSourceOrderId(BigInteger previousSourceOrderId) {
        PreviousSourceOrderId = previousSourceOrderId;
    }

    public Integer getSubscriptionPurchaseFlag() {
        return SubscriptionPurchaseFlag;
    }

    public void setSubscriptionPurchaseFlag(Integer subscriptionPurchaseFlag) {
        SubscriptionPurchaseFlag = subscriptionPurchaseFlag;
    }

    public String getPreviousBillingServerTime() {
        return PreviousBillingServerTime;
    }

    public void setPreviousBillingServerTime(String previousBillingServerTime) {
        PreviousBillingServerTime = previousBillingServerTime;
    }

    public String getPreviousSourceOrderTimediff() {
        return PreviousSourceOrderTimediff;
    }

    public void setPreviousSourceOrderTimediff(String previousSourceOrderTimediff) {
        PreviousSourceOrderTimediff = previousSourceOrderTimediff;
    }

    public String getPreviousSourceBillingServerTime() {
        return PreviousSourceBillingServerTime;
    }

    public void setPreviousSourceBillingServerTime(String previousSourceBillingServerTime) {
        PreviousSourceBillingServerTime = previousSourceBillingServerTime;
    }
}
