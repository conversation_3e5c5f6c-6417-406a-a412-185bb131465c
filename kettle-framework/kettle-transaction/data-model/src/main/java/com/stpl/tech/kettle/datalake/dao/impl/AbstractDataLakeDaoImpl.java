/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.datalake.dao.impl;

import com.stpl.tech.master.data.dao.AbstractDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

public class AbstractDataLakeDaoImpl implements AbstractDao {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractDataLakeDaoImpl.class);

	@PersistenceContext(unitName = "DataLakeDataSourcePUName")
	@Qualifier(value = "DataLakeDataSourceEMFactory")
	protected EntityManager manager;

	@Override
	public <T> T update(T data) {

		try {
			data = manager.merge(data);
			manager.flush();
			return data;
		} catch (Exception e) {
			LOG.error("Error updating " + data.getClass().getName() + " {}", e.getMessage());
		}
		return null;
	}

	@Override
	public <T> T add(T data) {
		try {
			manager.persist(data);
			manager.flush();
			return data;
		} catch (Exception e) {
			LOG.error("Error adding " + data.getClass().getName() + " {}", e.getMessage());
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> List<T> findAll(Class<T> data) {
		Query query = manager.createQuery("FROM " + data.getName() + " T");
		return query.getResultList();
	}

	@Override
	public <T, R> T find(Class<T> data, R key) {
		return manager.find(data, key);
	}

	@Override
	public <T> void delete(T data) {
		try {
			manager.remove(data);
			manager.flush();
		} catch (Exception e) {
			LOG.error("Error deleting " + data.getClass().getName() + " {}", e.getMessage());
		}
	}

	@Override
	public <T> List<T> addAll(List<T> list) {
		if(list == null) {
			return null;
		}
		List<T> l = new ArrayList<T>();
		if(list.isEmpty()) {
			return l;
		}
		try {
			for (T data : list) {
				manager.persist(data);
				l.add(data);
			}
			manager.flush();
			return l;
		} catch (Exception e) {
			LOG.error("Error adding " + list.getClass().getName() + " {}", e.getMessage());
		}
		return null;
	}

}
