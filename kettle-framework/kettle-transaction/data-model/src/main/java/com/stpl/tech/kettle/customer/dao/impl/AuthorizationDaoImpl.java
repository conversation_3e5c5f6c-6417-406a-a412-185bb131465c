

/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.kettle.customer.dao.AuthorizationDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.AuthorizationRequest;
import com.stpl.tech.master.core.AuthorizationType;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;

@Repository
public class AuthorizationDaoImpl extends AbstractDaoImpl implements AuthorizationDao {

    private static final Logger LOG = LoggerFactory.getLogger(AuthorizationDaoImpl.class);

    public String createEmailAuthorizationRequest(String emailId, String code, String text) {
        return createAuthorizationRequest(AuthorizationType.EMAIL, emailId, code, text);
    }

    public String createSMSAuthorizationRequest(String contactNumber, String code, String text) {
        return createAuthorizationRequest(AuthorizationType.SMS, contactNumber, code, text);
    }

    public boolean exists(String email, String token) {
        Query query = manager.createQuery(
                "FROM AuthorizationRequest where authorizationId = :authorizationId and authorizationMode = :authorizationMode and authorizationCode = :authorizationCode and validationTime is null");

        query.setParameter("authorizationId", email);
        query.setParameter("authorizationMode", AuthorizationType.EMAIL.name());
        query.setParameter("authorizationCode", token);
        try {
            Object o = query.getSingleResult();
            if (o != null) {
                AuthorizationRequest request = (AuthorizationRequest) o;
                request.setValidationTime(AppUtils.getCurrentTimestamp());
                manager.flush();
                return true;
            }
        } catch (NoResultException nre) {
            LOG.error("Invalid request for email verification for email productId " + email + " and token " + token, nre);
            return false;
        }
        return false;
    }

    private String createAuthorizationRequest(AuthorizationType type, String id, String code, String text) {
        AuthorizationRequest request = new AuthorizationRequest();
        request.setAddTime(AppUtils.getCurrentTimestamp());
        request.setAuthorizationId(id);
        request.setAuthorizationText(text);
        request.setAuthorizationMode(type.name());
        request.setAuthorizationCode(code);
        manager.persist(request);
        return request.getAuthorizationCode();
    }

    @SuppressWarnings("unchecked")
    public void expireAuthorizationRequest(String email) {
        Query query = manager.createQuery(
                "FROM AuthorizationRequest where authorizationId = :authorizationId and authorizationMode = :authorizationMode and validationTime is null");

        query.setParameter("authorizationId", email);
        query.setParameter("authorizationMode", AuthorizationType.EMAIL.name());
        try {
            List<AuthorizationRequest> list = query.getResultList();
            if (list != null && list.size() > 0) {
                for (AuthorizationRequest request : list) {
                    request.setValidationTime(AppUtils.getCurrentTimestamp());
                    manager.flush();
                }
            }
        } catch (NoResultException nre) {
            LOG.error("Invalid request for email verification for email productId " + email, nre);
        }
    }

    @Override
    public AuthorizationRequest findAuthorizationToken(String email) {
        Query query = manager.createQuery("FROM AuthorizationRequest where authorizationId = :authorizationId " +
                        "and authorizationMode = :authorizationMode and validationTime is null " +
                        "order by authorizationRequestId desc");

        query.setParameter("authorizationId", email);
        query.setParameter("authorizationMode", AuthorizationType.EMAIL.name());
        AuthorizationRequest validRequest = null;
        try {
            List<AuthorizationRequest> list = query.getResultList();
            if(list!=null && !list.isEmpty()){
                validRequest = list.get(0);
                if (list.size() > 1) {
                    for(int i = 1;i<list.size();i++){
                        AuthorizationRequest request = list.get(i);
                        request.setValidationTime(AppUtils.getCurrentTimestamp()); //EXPIRE old requests
                        manager.flush();
                    }
                }
            }
            return validRequest;
        } catch (NoResultException nre) {
            LOG.error("Invalid request for email verification for email productId " + email, nre);
        }
        return validRequest;
    }
}
