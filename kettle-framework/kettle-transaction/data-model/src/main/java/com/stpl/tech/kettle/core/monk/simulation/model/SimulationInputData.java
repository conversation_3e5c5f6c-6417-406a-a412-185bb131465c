/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.model;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class SimulationInputData {

	public int countOfMonks;
	public int noOfMonths;
	public int unitId;
	public String unitName;
	public boolean skipBulkOrder;
	public BigDecimal bulkOrderValue;
	public String strategy;
	public String recipeFilePath;
	public int transitionTime;

	@Override
	public String toString() {
		return "SimulationInputData [countOfMonks=" + countOfMonks + ", noOfMonths=" + noOfMonths + ", unitId=" + unitId
				+ ", unitName=" + unitName + ", skipBulkOrder=" + skipBulkOrder + ", bulkOrderValue=" + bulkOrderValue
				+ ", strategy=" + strategy + ", recipeFilePath=" + recipeFilePath + ", transitionTime=" + transitionTime
				+ "]";
	}

}
