/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.delivery.model.GrabCallback;
import com.stpl.tech.kettle.delivery.model.GrabOrderResponse;
import com.stpl.tech.util.AppUtils;

public class GrabResponseAdapter
		implements ResponseAdapter<GrabOrderResponse, GrabOrderResponse, <PERSON>rab<PERSON>allback, DeliveryResponse> {

	private static final Integer PARTNER_ID = 9;

	@Override
	public DeliveryResponse adapt(GrabOrderResponse data, String orderId) {

		DeliveryResponse response = new DeliveryResponse();

		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryTaskId(data.getOrderId());
		response.setGeneratedOrderId(orderId);
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());

		return response;
	}

	@Override
	public DeliveryResponse adaptError(GrabOrderResponse data, String orderId) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setGeneratedOrderId(orderId);
		response.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		response.setFailureMessage(data != null ? data.getErrorCode() : null);
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		return response;
	}


	@Override
	public DeliveryResponse adaptCallback(GrabCallback data) {
		DeliveryResponse response = new DeliveryResponse();
		response.setDeliveryPartnerId(PARTNER_ID);
		response.setDeliveryTaskId(data.getGrabOrderId());
		response.setGeneratedOrderId(data.getClientOrderId());
		response.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
		response.setDeliveryBoyName(data.getRiderName());
		response.setDeliveryBoyPhoneNum(data.getRiderPhone());
		response.setDeliveryStatus(getGrabStatus(data.getOrderStatus()));
		return response;
	}

	private int getGrabStatus(String orderStatus) {
		int status = Integer.parseInt(orderStatus);
		DeliveryStatus deliveryStatus = null;
		switch (status){
			case 1:
			case 2:
			case 101:
				deliveryStatus = DeliveryStatus.ACCEPTED;
				break;
			case 3: deliveryStatus = DeliveryStatus.ASSIGNED;
				break;
			case 4:
				deliveryStatus = DeliveryStatus.ARRIVED;
				break;
			case 5:
				deliveryStatus = DeliveryStatus.DEPARTED;
				break;
			case 6:
				deliveryStatus = DeliveryStatus.DELIVERED;
				break;
			case 99:
				deliveryStatus = DeliveryStatus.CANCELLED;
				break;
			case 102:
				deliveryStatus = DeliveryStatus.CREATE_DECLINED;
				break;
			default: deliveryStatus = DeliveryStatus.REQUEST_DECLINED;
				break;
		}

		return deliveryStatus.getDeliveryStatus();

	}

	@Override
	public DeliveryResponse adaptCancel(String orderId, String taskId) {
		return null;
	}

}
