package com.stpl.tech.kettle.google.data.dao.impl;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.google.data.dao.GoogleDataPushDao;
import com.stpl.tech.kettle.google.domain.model.GoogleCustomerOrderData;
import com.stpl.tech.kettle.google.util.GoogleConstants;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Repository
public class GoogleDataPushDaoImpl extends AbstractDaoImpl implements GoogleDataPushDao {

    /**
     * For offline conversions for leads - get the lead data for all those leads who converted into offline purchases
     *
     * @param startTime
     * @param endTime
     * @param brandId
     * @return
     */
    @Override
    public List<GoogleCustomerOrderData> getGooglePushDataForLeads(Date startTime, Date endTime, Integer brandId) {

        Query query = manager.createNativeQuery(
                "SELECT t1.CONTACTNUMBER contactNumber, t1.TRANSACTIONTIME transactionTime, t1.ORDERTAXABLEAMOUNT orderTaxableAmount, t1.GENERATEDORDERID generatedOrderId, t2.GCLID gclid FROM " +
                        "(SELECT CI.CONTACT_NUMBER AS CONTACTNUMBER, MIN(OD.BILLING_SERVER_TIME) AS TRANSACTIONTIME, SUM(OD.TAXABLE_AMOUNT) AS ORDERTAXABLEAMOUNT, MIN(OD.GENERATED_ORDER_ID) AS GENERATEDORDERID " +
                        "FROM ORDER_DETAIL OD, CUSTOMER_INFO CI " +
                        "WHERE OD.CUSTOMER_ID = CI.CUSTOMER_ID " +
                        "AND OD.ORDER_STATUS <> 'CANCELLED' " +
                        "AND OD.BILLING_SERVER_TIME >= :startTime " +
                        "AND OD.BILLING_SERVER_TIME <= :endTime " +
                        "AND OD.CUSTOMER_ID NOT IN :excludedCustomerIds " +
                        "AND OD.BRAND_ID = :brandId " +
                        "AND OD.ORDER_TYPE = :orderType " +
                        "AND OD.IS_GIFT_CARD_ORDER = 'N' " +
                        "AND OD.CHANNEL_PARTNER_ID <> :channelPartnerId " +
                        "GROUP BY OD.CUSTOMER_ID) t1 " +
                        "INNER JOIN " +
                        "(SELECT CONTACT_NUMBER, MIN(GCLID) AS GCLID FROM CUSTOMER_CAMPAIGN_JOURNEY WHERE UTM_SOURCE IN :utmSource AND CONTACT_NUMBER IS NOT NULL " +
                        "AND SESSION_START_TIME >= :leadCreationStartTime AND SESSION_START_TIME <= :endTime AND GCLID IS NOT NULL GROUP BY CONTACT_NUMBER) t2 " +
                        "ON " +
                        "t1.CONTACTNUMBER = t2.CONTACT_NUMBER", "GoogleCustomerOrderData");

        Date leadCreationStartTime = AppUtils.getUpdatedTimeInDate(5, 30, 0, AppUtils.getDayBeforeOrAfterDay(endTime,-7));
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        query.setParameter("leadCreationStartTime", leadCreationStartTime);
        query.setParameter("excludedCustomerIds", AppConstants.EXCLUDE_CUSTOMER_IDS);
        query.setParameter("brandId", brandId);
        query.setParameter("orderType", AppConstants.ORDER_TYPE_REGULAR);
        query.setParameter("channelPartnerId", AppConstants.CHANNEL_PARTNER_SWIGGY);
        query.setParameter("utmSource", Arrays.asList(AppConstants.GOOGLE, GoogleConstants.YOUTUBE));

        List<GoogleCustomerOrderData> resultList = query.getResultList();
        return resultList;
    }


    @Override
    public List<GoogleCustomerOrderData> getCustomerOrdersBulk(Date startTime, Date endTime, Integer brandId) {

        Query query = manager.createNativeQuery("SELECT ci.CONTACT_NUMBER as contactNumber, MIN(od.BILLING_SERVER_TIME) as transactionTime, " +
                "SUM(od.TAXABLE_AMOUNT) as orderTaxableAmount, MIN(od.GENERATED_ORDER_ID) as generatedOrderId, " +
                "MIN(ci.FIRST_NAME) as gclid " +
                "FROM ORDER_DETAIL od, CUSTOMER_INFO ci " +
                "WHERE od.CUSTOMER_ID = ci.CUSTOMER_ID " +
                "AND od.ORDER_STATUS <> 'CANCELLED' " +
                "AND od.BILLING_SERVER_TIME >= :startTime " +
                "AND od.BILLING_SERVER_TIME <= :endTime " +
                "AND od.CUSTOMER_ID NOT IN :excludeCustomerIds " +
                "AND od.BRAND_ID = :brandId " +
                "AND od.ORDER_TYPE = :orderType " +
                "AND od.IS_GIFT_CARD_ORDER = 'N' " +
                "AND od.CHANNEL_PARTNER_ID <> :channelPartnerId " +
                "GROUP BY od.CUSTOMER_ID", "GoogleCustomerOrderData");

        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        query.setParameter("excludeCustomerIds", AppConstants.EXCLUDE_CUSTOMER_IDS);
        query.setParameter("brandId", brandId);
        query.setParameter("orderType", AppConstants.ORDER_TYPE_REGULAR);
        query.setParameter("channelPartnerId", AppConstants.CHANNEL_PARTNER_SWIGGY);

        List<GoogleCustomerOrderData> resultList = query.getResultList();
        return resultList;
    }

}
