package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.master.domain.model.ProductBasicDetail;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;

public class PercentageItemMultiXMultiYStrategy extends PercentageItemBogoStrategy {

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems) throws OfferValidationException {
        return applyDiscountStrategy(coupon, order, cache);
    }

    @Override
    public OfferOrder applyDiscountStrategy(CouponDetail coupon, OfferOrder order, MasterDataCache cache) throws OfferValidationException {
        OfferDetail offer = coupon.getOffer();
        Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());
        Map<Integer, OrderItem> productDiscountMap = new HashMap<>();
        Set<ProductBasicDetail> productDetails = new HashSet<>();
        for (OrderItem item : order.getOrder().getOrders()) {
            int productId = item.getProductId();
            productDiscountMap.put(productId, item);
            productDetails.add(cache.getProductBasicDetail(productId));
        }
        for (OfferMetaDataType mappingKey : mappings.keySet()) {
            Optional<List<Integer>> commonListProduct = Optional
                    .ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));
            Set<Integer> commonList = new HashSet<>();
            if (commonListProduct.isPresent()) {
                for (Integer id : commonListProduct.get()) {
                    commonList.add(id);
                }
                int applicableQuantity = 0;
                TreeMap<BigDecimal, List<OrderItem>> productMapCountMap = new TreeMap<>();
                for (OrderItem item : order.getOrder().getOrders()) {
                    if (commonList.contains(item.getProductId())) {
                        applicableQuantity = applicableQuantity + item.getQuantity();
                        if (!productMapCountMap.containsKey(item.getPrice())) {
                            productMapCountMap.put(item.getPrice(), new ArrayList<>(Arrays.asList(item)));
                        } else {
                            List<OrderItem> items = productMapCountMap.get(item.getPrice());
                            items.add(item);
                            productMapCountMap.put(item.getPrice(), items);
                        }
                    }
                }
                int offerCount = offer.getMinQuantity();
                if (applicableQuantity < offer.getMinItemCount()+1) {
                    throw new OfferValidationException("Minimum Count for mapped offer items is " + String.valueOf(offer.getMinItemCount()+offer.getMinQuantity()) + " for this offer",
                            WebErrorCode.MINIMUM_ORDER_VALUE);
                }
                if (commonList.size() > 0) {
                    List<Integer> commonElementList = new ArrayList<>();
                    for (Integer id : commonList) {
                        commonElementList.add(id);
                    }
                    commonElementList = sortCommonList(productDiscountMap, commonElementList);

                    boolean isAnyFree = false;
                    int multipleFactor = offer.getMinItemCount() + offer.getMinQuantity();
                    int maxCounter = offer.getMinQuantity() * 10;

                    int totalProducts = 0;
                    for (int list : commonElementList) {
                        totalProducts = totalProducts + productDiscountMap.get(list).getQuantity();
                    }
                    int counter = offer.getMinQuantity() * (totalProducts/multipleFactor);
                    if(counter > maxCounter){
                        counter = maxCounter;
                    }

                    for (Integer product : commonElementList) {
                        for (Map.Entry<BigDecimal, List<OrderItem>> entry : productMapCountMap.entrySet()) {
                            for (OrderItem item : entry.getValue()) {
                                if (counter == 0) {
                                    break;
                                }
                                if (item.getProductId() == product) {
                                    isAnyFree = true;
                                    OrderItem modifiedItem = new OrderItem();
                                    if(item.getQuantity() < counter) {
                                        modifiedItem = addDiscountDetails(item,
                                                BigDecimal.valueOf(offer.getOfferValue()), offer, item.getQuantity());
                                        offerCount = offerCount - item.getQuantity();
                                        counter = counter - item.getQuantity();
                                    }else{
                                        modifiedItem = addDiscountDetails(item,
                                                BigDecimal.valueOf(offer.getOfferValue()), offer, counter);
                                        offerCount = offerCount - counter;
                                        if (counter <= item.getQuantity()) {
                                            counter = 0;
                                        }
                                    }
                                    addDiscountReason(modifiedItem, coupon);
                                }
                            }
                            if (counter == 0) {
                                break;
                            }
                        }
                        if (counter == 0) {
                            break;
                        }
                    }
                    if (!isAnyFree) {
                        throw new OfferValidationException("Minimum quantity of the particular product required to apply this offer is : " + multipleFactor,
                                WebErrorCode.MINIMUM_ITEM_NOT_AVAILABLE);
                    }
                }
            }
        }
        order.setAppliedOfferMessage(getOfferMessage());
        updateOrderDiscount(order, coupon.getCode(), null);
        return order;
    }
}

