/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.OPNRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.domain.model.Address;

import java.util.List;

public class OPNRequestAdapter implements RequestAdapter<OPNRequest, OrderInfo> {

	@Override
	public OPNRequest adaptCreate(OrderInfo data) {
		OPNRequest opnRequest = new OPNRequest();
		Order order = data.getOrder();
		Customer customer = data.getCustomer();
		Address address = TransactionUtils.getAddressForOrder(order.getDeliveryAddress(), customer.getAddresses());
		Float paidAmount = Float.parseFloat(order.getTransactionDetail().getPaidAmount().toPlainString());

		opnRequest.setAddress(address.toString());
		opnRequest.setAmount(paidAmount);
		opnRequest.setAmount_To_Pay(getAmountToPay(order.getSettlements(), paidAmount));
		opnRequest.setLocality(address.getLocality());
		opnRequest.setPhone(customer.getContactNumber());
		opnRequest.setOrder_Code(order.getGenerateOrderId());
		opnRequest.setMerchant_Id(order.getUnitId());
		// opnRequest.setCallbackUrl("http://localhost:8080");
		return opnRequest;
	}

	private float getAmountToPay(List<Settlement> settlements, float paidAmount) {
		if (settlements.size() == 1 && settlements.get(0).getMode() == 1) {
			return paidAmount;
		}
		return 0f;
	}

	@Override
	public OPNRequest adaptCancel(OrderInfo info, String taskId) {
		return null;
	}
}
