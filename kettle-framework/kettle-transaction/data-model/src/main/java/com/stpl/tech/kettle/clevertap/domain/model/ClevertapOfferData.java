package com.stpl.tech.kettle.clevertap.domain.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClevertapOfferData implements Serializable {

	private static final long serialVersionUID = -3725556041735278224L;

	@JsonProperty("Name")
	@SerializedName("Name")
	private String name;
	@JsonProperty("Phone")
	@SerializedName("Phone")
	private String phone;

	@JsonProperty("NboCouponCode")
	@SerializedName("NboCouponCode")
	private String NBOCode;
	@JsonProperty("NboCouponStartDate")
	@SerializedName("NboCouponStartDate")
	private String NBOValidityFrom;
	@JsonProperty("NboCouponEndDate")
	@SerializedName("NboCouponEndDate")
	private String NBOValidityTill;
	@JsonProperty("NboCreated")
	@SerializedName("NboCreated")
	private String NBOCreatedAt;
	@JsonProperty("NboOfferText")
	@SerializedName("NboOfferText")
	private String NBOOfferText;

	@JsonProperty("GenCouponCode")
	@SerializedName("GenCouponCode")
	private String GenOfferCode;
	@JsonProperty("GenCouponStartDate")
	@SerializedName("GenCouponStartDate")
	private String GenOfferValidityFrom;
	@JsonProperty("GenCouponEndDate")
	@SerializedName("GenCouponEndDate")
	private String GenOfferValidityTill;
	@JsonProperty("GenCreated")
	@SerializedName("GenCreated")
	private String GenCreatedAt;
	@JsonProperty("GenOfferText")
	@SerializedName("GenOfferText")
	private String GenOfferText;

	@JsonProperty("DnboCouponCode")
	@SerializedName("DnboCouponCode")
	private String DNBOCode;
	@JsonProperty("DnboCouponStartDate")
	@SerializedName("DnboCouponStartDate")
	private String DNBOValidityFrom;
	@JsonProperty("DnboCouponEndDate")
	@SerializedName("DnboCouponEndDate")
	private String DNBOValidityTill;
	@JsonProperty("DnboCreated")
	@SerializedName("DnboCreated")
	private String DNBOCreatedAt;
	@JsonProperty("DnboOfferText")
	@SerializedName("DnboOfferText")
	private String DNBOOfferText;

	@JsonProperty("Del_GenCouponCode")
	@SerializedName("Del_GenCouponCode")
	private String DeliveryGenCode;
	@JsonProperty("Del_GenCouponStartDate")
	@SerializedName("Del_GenCouponStartDate")
	private String DeliveryGenValidityFrom;
	@JsonProperty("Del_GenCouponEndDate")
	@SerializedName("Del_GenCouponEndDate")
	private String DeliveryGenValidityTill;
	@JsonProperty("Del_GenCreated")
	@SerializedName("Del_GenCreated")
	private String DeliveryGenCreatedAt;
	@JsonProperty("Del_GenOfferText")
	@SerializedName("Del_GenOfferText")
	private String DeliveryGenOfferText;
}
