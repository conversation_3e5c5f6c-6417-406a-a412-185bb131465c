package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.core.data.vo.SubscriptionRequest;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.data.model.SubscriptionPlanEvent;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;

import java.math.BigDecimal;
import java.util.List;

public interface SubscriptionPlanDao extends AbstractDao {

	public SubscriptionPlan addSubscription(SubscriptionRequest request, String source, Integer campaignId);

	public SubscriptionPlan getActiveSubscription(Integer customerId);

	SubscriptionPlan updateSubscriptionData(SubscriptionRequest request, String source, Integer campaignId);

	void addSubscriptionSaving(int customerId, Order order, Pair<CouponDetail, Product> subscriptionObj);

	SubscriptionPlan getSubscription(Integer customerId);

	SubscriptionPlan getChaayosPrepaidSubscription(Integer customerId);

	SubscriptionPlan getActiveSubscription(Integer customerId, String code);

	SubscriptionPlanEvent getSubscriptionPlanEvent(Integer subscriptionPlanId);

	void addSubscriptionEventSaving(int customerId, BigDecimal subscriptionSavings);

	SubscriptionPlanEvent getActiveSubscriptionEvent(SubscriptionPlan plan);

	SubscriptionPlan getLastSubscriptionPlanForCustomer(Integer customerId, String code);

	List<SubscriptionPlan> getAllSubscriptionPlanForCustomer(int customerId);
}
