package com.stpl.tech.kettle.referral.dao.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.jms.JMSException;
import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CashData;
import com.stpl.tech.kettle.data.model.CashLogData;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.data.model.CashPacketEventStatus;
import com.stpl.tech.kettle.data.model.CashPacketLogData;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.referral.dao.CashManagerDao;
import com.stpl.tech.kettle.referral.model.ReferralLinkData;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.CashMetadata;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.transaction.CashMetadataType;
import com.stpl.tech.util.transaction.CashMetadataWrapper;
import com.stpl.tech.util.transaction.CashTransactionCategory;
import com.stpl.tech.util.transaction.CashTransactionCode;
import com.stpl.tech.util.transaction.CashTransactionMetadata;
import com.stpl.tech.util.transaction.TransactionType;

@Repository
public class CashManagerDaoImpl extends AbstractDaoImpl implements CashManagerDao {

	private static final Logger LOG = LoggerFactory.getLogger(CashManagerDaoImpl.class);

	@Autowired
	private MasterDataCache cache;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private SMSClientProviderService providerService;

	@Autowired
	private EnvironmentProperties props;

	@Override
	public CashData getCustomerCashData(Integer customerId) {
		if (customerId == null) {
			return null;
		}
		try {
			Query query = manager.createQuery("FROM CashData E WHERE E.customerId = :customerId");
			query.setParameter("customerId", customerId);
			return (CashData) query.getSingleResult();
		} catch (NoResultException nre) {
			return null;
		} catch (Exception e) {
			LOG.error("Error while searching cash data", e);
			return null;
		}
	}

	@Override
	public BigDecimal getAvailableCashback(Integer customerId) {
		if (customerId == null) {
			return BigDecimal.ZERO;
		}
		try {
			Query query = manager.createQuery(
					"select sum(currentAmount) FROM CashPacketData E WHERE E.customerId = :customerId and E.eventStatus = :eventStatus and E.transactionCode = :transactionCode");
			query.setParameter("customerId", customerId);
			query.setParameter("eventStatus", CashPacketEventStatus.ACTIVE.name());
			query.setParameter("transactionCode", CashTransactionCode.CASHBACK.name());
			Object result = query.getSingleResult();
			return result == null ? BigDecimal.ZERO : (BigDecimal) result;
		} catch (NoResultException nre) {
			return BigDecimal.ZERO;
		} catch (Exception e) {
			LOG.error("Error while searching cash back packet data", e);
			return BigDecimal.ZERO;
		}
	}

	@Override
	public CashData createCashData(Customer customer) throws DataUpdationException {
		CashData cashData = createCashDataObject(customer.getId(), customer.getRefCode());
		return add(cashData);
	}


	@Override
	public void addCashToReferrerOnSuccess(ReferralMappingData data, String customerRefCode, CustomerInfo info) {
		CashTransactionMetadata metadata = new CashTransactionMetadata(TransactionType.CREDIT,
				CashMetadataType.TOPUP_REFERRER, CashTransactionCategory.REFERRAL, CashTransactionCode.SIGNUP_REFERRER);
		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		wrapper.setMetadata(metadata);
		wrapper.setCustomerId(data.getReferrerId());
		wrapper.setReferentId(data.getReferentId());
		wrapper.setCustomerRefCode(customerRefCode);
		wrapper.setReferralMappingId(data.getReferralDataId());
		addCash(wrapper,info);
	}

	@Override
	public CashPacketData addCashAsFreeGift(int customerId, String customerRefCode, int orderId, BigDecimal cashBack,
											Date activationTime, Date expiration, Integer lagDays) {
		CashTransactionMetadata metadata = new CashTransactionMetadata(TransactionType.CREDIT,
				CashMetadataType.CLM_CASH_BONUS, CashTransactionCategory.CASH_BONUS, CashTransactionCode.CLM_CASH_BONUS);
		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		wrapper.setMetadata(metadata);
		wrapper.setCustomerId(customerId);
		wrapper.setCustomerRefCode(customerRefCode);
		wrapper.setReferralMappingId(null);
		wrapper.setOrderId(orderId);
		CashData cash = getCustomerCashData(wrapper.getCustomerId());
		if (cash == null) {
			cash = createCashDataObject(wrapper.getCustomerId(), wrapper.getCustomerRefCode());
			add(cash);
		}
		wrapper.setTransactionAmount(cashBack);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());
		boolean isActive = isActivePacket(wrapper);

		if (isActive && lagDays == 0) {
			cash.setAccumulatedAmount(AppUtils.add(cash.getAccumulatedAmount(), cashBack));
			cash.setCurrentAmount(AppUtils.add(cash.getCurrentAmount(), cashBack));
			cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			update(cash);
			addCashLog(cash, wrapper);
		}
		activationTime = AppUtils.addDays(AppUtils.getDate(activationTime), lagDays);
		return addCashPacket(cash, wrapper, AppUtils.getCurrentTimestamp(), expiration, activationTime, lagDays);
	}


	@Override
	public CashPacketData uploadChaayosCashForAllotment(int customerId, BigDecimal amount, int lagDays,
			Date activationTime, Date expiration, String cashMetaData, String cashTransactionCode) {
		CashTransactionMetadata metadata = new CashTransactionMetadata(TransactionType.CREDIT,
				CashMetadataType.valueOf(cashMetaData), CashTransactionCategory.CASH_BONUS,
				CashTransactionCode.valueOf(cashTransactionCode));
		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		wrapper.setMetadata(metadata);
		wrapper.setCustomerId(customerId);
		wrapper.setCustomerRefCode(null);
		wrapper.setReferralMappingId(null);
		wrapper.setOrderId(null);
		CashData cash = getCustomerCashData(wrapper.getCustomerId());
		if (cash == null) {
			cash = createCashDataObject(wrapper.getCustomerId(), wrapper.getCustomerRefCode());
			add(cash);
		}
		wrapper.setTransactionAmount(amount);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());
		boolean isActive = isActivePacket(wrapper);

		if (isActive && lagDays == 0) {
			cash.setAccumulatedAmount(AppUtils.add(cash.getAccumulatedAmount(), amount));
			cash.setCurrentAmount(AppUtils.add(cash.getCurrentAmount(), amount));
			cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			update(cash);
			addCashLog(cash, wrapper);
		}
		activationTime = AppUtils.addDays(AppUtils.getDate(activationTime), lagDays);
		return addCashPacket(cash, wrapper, AppUtils.getCurrentTimestamp(), expiration, activationTime, lagDays);
	}



	@Override
	public boolean addCashForCashBack(int customerId, String customerRefCode, int orderId, BigDecimal cashBack,
			Date activationTime, Date expiration, int lagDays) {
		CashTransactionMetadata metadata = new CashTransactionMetadata(TransactionType.CREDIT,
				CashMetadataType.CASHBACK, CashTransactionCategory.CASH_BONUS, CashTransactionCode.CASHBACK);
		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		wrapper.setMetadata(metadata);
		wrapper.setCustomerId(customerId);
		wrapper.setCustomerRefCode(customerRefCode);
		wrapper.setReferralMappingId(null);
		wrapper.setOrderId(orderId);
		// search existing
		CashData cash = getCustomerCashData(wrapper.getCustomerId());
		if (cash == null) {
			// create new in case of new customer
			cash = createCashDataObject(wrapper.getCustomerId(), wrapper.getCustomerRefCode());
			add(cash);
		}

		// top up Amount
		wrapper.setTransactionAmount(cashBack);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

//		boolean isActive = isActivePacket(wrapper);
//
//		if (isActive) {
//			cash.setAccumulatedAmount(AppUtils.add(cash.getAccumulatedAmount(), cashBack));
//			cash.setCurrentAmount(AppUtils.add(cash.getCurrentAmount(), cashBack));
//			cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
//			update(cash);
//			addCashLog(cash, wrapper);
//		}
		addCashPacket(cash, wrapper, AppUtils.getCurrentTimestamp(), expiration, activationTime, lagDays);
		return true;
	}

	@Override
	public void invalidateCashBack(Order order) {
		int orderId = order.getOrderId();
		CashPacketLogData cashPacketLogData = getCashPacketLogDataByOrderId(orderId);
		if(cashPacketLogData != null){
			CashPacketData cashPacketData = getCashPacket(cashPacketLogData.getCashPacketId());
			if(cashPacketData != null){
				expireCashPacket(cashPacketData, CashTransactionCategory.ORDER_CANCELLATION);
			}
		}
	}

	private CashPacketData getCashPacket(Integer cashPacketId) {
		Query query = manager.createQuery(
				"FROM CashPacketData E WHERE E.cashPacketId = :cashPacketId");
		query.setParameter("cashPacketId", cashPacketId);
		return (CashPacketData) query.getSingleResult();
	}

	private CashPacketLogData getCashPacketLogDataByOrderId(int orderId) {
		try {
			Query query = manager.createQuery(
					"FROM CashPacketLogData E WHERE E.orderId = :orderId and E.transactionType = :transactionType " +
							" and E.transactionCode = :transactionCode");
			query.setParameter("orderId", orderId);
			query.setParameter("transactionType", TransactionType.CREDIT.name());
			query.setParameter("transactionCode", CashTransactionCode.CASHBACK.name());
			return (CashPacketLogData) query.getSingleResult();
		} catch(NoResultException e){
			LOG.error("No data found while searching initiated Cash packet");
			return null;
		}
		catch (Exception e) {
			LOG.error("Error while searching Initiated Cash Packet", e);
			return null;
		}
	}

	private CashPacketLogData getCashPacketLogDataByOrderId(int packetId, CashTransactionCode code) {
		try {
			Query query = manager.createQuery(
					"FROM CashPacketLogData E WHERE E.cashPacketId = :packetId and E.transactionCode = :transactionCode");
			query.setParameter("packetId", packetId);
			query.setParameter("transactionCode", code.name());
			return (CashPacketLogData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Error while searching Initiated Cash Packet", e);
			return null;
		}
	}

	@Override
	public void addCashOnReferentOnSuccess(ReferralMappingData data, CustomerInfo customerRefCode) {
		CashTransactionMetadata metadata = new CashTransactionMetadata(TransactionType.CREDIT,
				CashMetadataType.TOPUP_REFERENT, CashTransactionCategory.REFERRAL, CashTransactionCode.SIGNUP_REFERENT);
		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		wrapper.setMetadata(metadata);
		wrapper.setCustomerId(data.getReferentId());
		wrapper.setReferentId(null);
		wrapper.setCustomerRefCode(customerRefCode.getRefCode());
		wrapper.setReferralMappingId(data.getReferralDataId());
		wrapper.setContactnumber(data.getContactNumber());
		addCash(wrapper, customerRefCode);
	}

	@Override
	public void redeemCash(Integer customerId, BigDecimal cashRedeemed, Integer orderId) throws DataUpdationException {

		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		CashTransactionMetadata metadata = new CashTransactionMetadata();
		metadata.setCashMetadataType(CashMetadataType.REDEMPTION);
		metadata.setCashTransactionCategory(CashTransactionCategory.REDEMPTION);
		metadata.setCashTransactionCode(CashTransactionCode.ORDER_DISCOUNT);
		metadata.setTransactionType(TransactionType.DEBIT);
		wrapper.setMetadata(metadata);
		wrapper.setTransactionAmount(cashRedeemed);
		wrapper.setOrderId(orderId);
		wrapper.setCustomerId(customerId);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

		CashData cash = getCustomerCashData(customerId);

		if (BigDecimal.ZERO.equals(cashRedeemed) || cash.getCurrentAmount().compareTo(cashRedeemed) < 0) {
			throw new DataUpdationException(String.format("Insufficent Cash for Customer %d, Required Amount %d",
					cash.getCurrentAmount(), cashRedeemed));
		}

		cash.setCurrentAmount(AppUtils.subtract(cash.getCurrentAmount(), cashRedeemed));
		cash.setRedeemedAmount(AppUtils.add(cash.getRedeemedAmount(), cashRedeemed));
		cash.setLastUpdateTime(wrapper.getTransactionTime());
		addCashLog(cash, wrapper);

		List<CashPacketData> packets = getCashPackets(cash.getCashDataId());
		for (CashPacketData packet : packets) {
			if (BigDecimal.ZERO.compareTo(cashRedeemed) >= 0) {
				// cashRedeemed is ZERO hence exit loop
				break;
			}
			BigDecimal valueRedeemed = null;
			if (cashRedeemed.compareTo(packet.getCurrentAmount()) <= 0) {
				// cash redeemed is less then current amount
				// sequence of these lines is important
				packet.setCurrentAmount(AppUtils.subtract(packet.getCurrentAmount(), cashRedeemed));
				packet.setRedeemedAmount(AppUtils.add(packet.getRedeemedAmount(), cashRedeemed));
				valueRedeemed = cashRedeemed.add(BigDecimal.ZERO);
				cashRedeemed = BigDecimal.ZERO;
			} else {
				// cash redeemed is more then current amount
				// sequence of these lines is important
				valueRedeemed = packet.getCurrentAmount().add(BigDecimal.ZERO);
				cashRedeemed = AppUtils.subtract(cashRedeemed, packet.getCurrentAmount());
				packet.setRedeemedAmount(AppUtils.add(packet.getRedeemedAmount(), packet.getCurrentAmount()));
				packet.setCurrentAmount(BigDecimal.ZERO);

			}
			packet.setLastUpdateTime(wrapper.getTransactionTime());
			update(packet);
			addPacketLog(false, packet, valueRedeemed, wrapper);
		}
	}

	@Override
	public void activateReferralCashPacket(Integer referentId, Integer referrerId, Integer orderId) {
		CashPacketData cashPacket = getInitiatedCashPacket(referentId, referrerId);
		if (cashPacket != null) {
			// only activation time is set here.
			Date activationDate = AppUtils.isDev(props.getEnvironmentType())
					? AppUtils.getDate(AppUtils.getCurrentTimestamp())
					: AppUtils.getNextDate(AppUtils.getBusinessDate(AppUtils.getCurrentTimestamp()));
			cashPacket.setActivationTime(activationDate);
			cashPacket.setEventStatus(CashPacketEventStatus.READY_FOR_ACTIVATION.name());
			update(cashPacket);
			CustomerInfo c = manager.find(CustomerInfo.class, referentId);
			c.setReferrerAwarded(AppUtils.YES);
			try {
				if (!AppUtils.isBlank(c.getRefCode())) {
					sendReferralMessage(c, "CHAAYOS_POS", "SMS");
				}
			} catch (Exception e) {
				LOG.info("Unable to send Referral SMS", e);
			}

			update(c);
		}
	}

	private CashPacketData getInitiatedCashPacket(Integer referentId, Integer referrerId) {
		// referrer - one who has the cash packet
		// referent - person added via referral
		try {
			Query query = manager.createQuery(
					"FROM CashPacketData E WHERE E.customerId = :referrerId AND E.referentId = :referentId  "
							+ " AND E.eventStatus = :eventStatus AND E.currentAmount > 0");
			query.setParameter("referrerId", referrerId);
			query.setParameter("referentId", referentId);
			query.setParameter("eventStatus", CashPacketEventStatus.INITIATED.toString());
			return (CashPacketData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Error while searching Initiated Cash Packet", e);
			return null;
		}
	}



	private void addPacketLog(boolean update, CashPacketData packet, BigDecimal packetTransactionValue,
			CashMetadataWrapper wrapper) {
		if (update && CashTransactionCode.CASHBACK.name().equals(packet.getTransactionCode())) {
			CashPacketLogData log = getCashPacketLogDataByOrderId(packet.getCashPacketId(),
					CashTransactionCode.CASHBACK);
			if (log != null) {
				log.setCashLogDataId(wrapper.getCashLogDataId());
				return;
			}
		}

		CashPacketLogData log = new CashPacketLogData();
		log.setCashPacketId(packet.getCashPacketId());
		log.setTransactionAmount(packetTransactionValue);
		log.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		log.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		log.setTransactionReason("");
		log.setTransactionType(wrapper.getMetadata().getTransactionType().name());
		log.setTransactionTime(wrapper.getTransactionTime());
		log.setOrderId(wrapper.getOrderId());
		log.setCashLogDataId(wrapper.getCashLogDataId());
		add(log);

	}

	@SuppressWarnings("unchecked")
	private List<CashPacketData> getCashPackets(Integer cashDataId) {
		try {
			Query query = manager.createQuery(
					"FROM CashPacketData E WHERE E.cashDataId = :cashDataId AND E.eventStatus = :eventStatus AND E.currentAmount > 0 order by transactionCode, expirationDate");
			query.setParameter("cashDataId", cashDataId);
			query.setParameter("eventStatus", CashPacketEventStatus.ACTIVE.toString());
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Error while searching reference", e);
			return null;
		}
	}

	private void addCash(CashMetadataWrapper wrapper, CustomerInfo info) {

		// search existing
		CashData cash = getCustomerCashData(wrapper.getCustomerId());
		if (cash == null) {
			// create new in case of new customer
			cash = createCashDataObject(wrapper.getCustomerId(), wrapper.getCustomerRefCode());
			add(cash);
		}

		// top up Amount
		CashMetadata topUp = cache.getCashMetadata(wrapper.getMetadata().getCashMetadataType());
		BigDecimal topUpValue = new BigDecimal(topUp.getValue());
		wrapper.setTransactionAmount(topUpValue);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

		boolean isActive = isActivePacket(wrapper);
		if (isActive) {
			cash.setAccumulatedAmount(AppUtils.add(cash.getAccumulatedAmount(), topUpValue));
			cash.setCurrentAmount(AppUtils.add(cash.getCurrentAmount(), topUpValue));
			cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			update(cash);
			addCashLog(cash, wrapper);
		}
		Date creationDate  = AppUtils.getDateWithoutTime(wrapper.getTransactionTime());
		Date expirationDate = getPacketExpirationDate(wrapper.getTransactionTime());
		addCashPacket(cash, wrapper, creationDate, expirationDate, null, 0);

		if (isActive) {
			// send customer notification
			Map<String,String> payload = new HashMap<>();
			payload.put("firstName",info.getFirstName());
			payload.put("refCode",wrapper.getCustomerRefCode());
			payload.put("earnedAmt", getTopUpReferent().toString());
			payload.put("validityDate", AppUtils.getDateInMonth(expirationDate));
			payload.put("orderDayNumber",AppUtils.roundToInteger(AppUtils.divide(cash.getCurrentAmount(),BigDecimal.valueOf(100))).toString());
			sendCustomerReferentTopUpSMS(wrapper.getContactnumber(),
					new ReferralLinkData(null, null, getTopUpReferent(), null),info,payload);
		}
	}

	private void addCashAsFreeGift(CashMetadataWrapper wrapper, CustomerInfo info, Integer topUp) {

		// search existing
		CashData cash = getCustomerCashData(info.getCustomerId());
		if (cash == null) {
			// create new in case of new customer
			cash = createCashDataObject(info.getCustomerId(), info.getRefCode());
			add(cash);
		}

		// top up Amount
		BigDecimal topUpValue = new BigDecimal(topUp);
		wrapper.setTransactionAmount(topUpValue);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

		boolean isActive = isActivePacket(wrapper);
		if (isActive) {
			cash.setAccumulatedAmount(AppUtils.add(cash.getAccumulatedAmount(), topUpValue));
			cash.setCurrentAmount(AppUtils.add(cash.getCurrentAmount(), topUpValue));
			cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			update(cash);
			addCashLog(cash, wrapper);
		}
		Date creationDate  = AppUtils.getDateWithoutTime(wrapper.getTransactionTime());
		Date expirationDate = getPacketExpirationDate(wrapper.getTransactionTime());
		addCashPacket(cash, wrapper, creationDate, expirationDate, null, 0);

		if (isActive) {
			// send customer notification
			Map<String,String> payload = new HashMap<>();
			payload.put("firstName",info.getFirstName());
			payload.put("refCode",wrapper.getCustomerRefCode());
			payload.put("earnedAmt", getTopUpReferent().toString());
			payload.put("validityDate", AppUtils.getDateInMonth(expirationDate));
			payload.put("orderDayNumber",AppUtils.roundToInteger(AppUtils.divide(cash.getCurrentAmount(),BigDecimal.valueOf(100))).toString());
			sendCustomerReferentTopUpSMS(wrapper.getContactnumber(),
					new ReferralLinkData(null, null, getTopUpReferent(), null),info,payload);
		}
	}

	private boolean sendCustomerReferentTopUpSMS(String contact, ReferralLinkData token, CustomerInfo info, Map<String, String> payload) {

		try {
			String message = CustomerSMSNotificationType.REFERENT_SIGNUP_NOTIFY.getMessage(token);
			return notificationService.sendNotification(CustomerSMSNotificationType.REFERENT_SIGNUP_NOTIFY.name(),
					message, contact,
					providerService.getSMSClient(
							CustomerSMSNotificationType.REFERENT_SIGNUP_NOTIFY.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					props.getSendAutomatedOTPSMS(),getNotificationPayload(CustomerSMSNotificationType.REFERENT_SIGNUP_NOTIFY,info,payload));
		} catch (IOException | JMSException e) {
			LOG.error("WHATSAPP_NOTIFICATOIN ::: Error while sending the referral message to " + contact, e);
		}
		return false;
	}

	private void addCashLog(CashData cash, CashMetadataWrapper wrapper) {
		CashLogData log = new CashLogData();
		log.setCashDataId(cash.getCashDataId());
		log.setOrderId(wrapper.getOrderId());
		log.setTransactionAmount(wrapper.getTransactionAmount());
		log.setTransactiontime(wrapper.getTransactionTime());
		log.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		log.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		log.setTransactionType(wrapper.getMetadata().getTransactionType().name());
		add(log);
		wrapper.setCashLogDataId(log.getCashLogDataId());
	}

	private CashPacketData addCashPacket(CashData cashData, CashMetadataWrapper wrapper, Date creationDate, Date expirationDate, Date activationTime, Integer lagDays) {
		CashPacketEventStatus packetStatus = getPacketStatus(wrapper);

		CashPacketData packet = new CashPacketData();

		packet.setCashDataId(cashData.getCashDataId());
		packet.setCreationDate(creationDate);
		packet.setCreationTime(wrapper.getTransactionTime());
		packet.setCustomerId(cashData.getCustomerId());
		packet.setExpirationDate(expirationDate);
		packet.setInitialExpirationDate(expirationDate);
		packet.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		packet.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		packet.setLastUpdateTime(wrapper.getTransactionTime());
		packet.setReferentId(wrapper.getReferentId());
		packet.setReferralDataId(wrapper.getReferralMappingId());
		packet.setCurrentAmount(wrapper.getTransactionAmount());
		packet.setInitialAmount(wrapper.getTransactionAmount());
		packet.setRedeemedAmount(BigDecimal.ZERO);
		packet.setRetainedAmount(BigDecimal.ZERO);
		packet.setExpiredAmount(BigDecimal.ZERO);
		if(activationTime != null){
			packet.setActivationTime(activationTime);
		}
		if(lagDays>0){
			packet.setEventStatus(CashPacketEventStatus.READY_FOR_ACTIVATION.name());
		}else{
			cashData.setAccumulatedAmount(cashData.getAccumulatedAmount().add(wrapper.getTransactionAmount()));
			cashData.setCurrentAmount(cashData.getCurrentAmount().add(wrapper.getTransactionAmount()));
			add(cashData);
			packet.setEventStatus(CashPacketEventStatus.ACTIVE.name());
		}
		add(packet);
		addPacketLog(false, packet, wrapper.getTransactionAmount(), wrapper);

		return packet;
	}

	private Date getPacketExpirationDate(Date transactionTime) {
		return AppUtils.addDays(transactionTime,
				Integer.valueOf(cache.getCashMetadata(CashMetadataType.VALIDITY).getValue()));
	}

	private Integer getTopUpReferent() {
		return Integer.valueOf(cache.getCashMetadata(CashMetadataType.TOPUP_REFERENT).getValue());
	}

	private Integer getTopUpReferrer() {
		return Integer.valueOf(cache.getCashMetadata(CashMetadataType.TOPUP_REFERRER).getValue());
	}

	private CashPacketEventStatus getPacketStatus(CashMetadataWrapper wrapper) {
		/*
		 * Referrer will have this packet as initiated until referent makes 1st
		 * Successful transaction.
		 *
		 */
		if(CashTransactionCode.SIGNUP_REFERRER.equals(wrapper.getMetadata().getCashTransactionCode())){
			return CashPacketEventStatus.INITIATED;
		} else if(CashTransactionCode.CASHBACK.equals(wrapper.getMetadata().getCashTransactionCode())){
			return CashPacketEventStatus.READY_FOR_ACTIVATION;
		}else {
			return CashPacketEventStatus.ACTIVE;
		}
	}

	private boolean isActivePacket(CashMetadataWrapper wrapper) {
		return CashPacketEventStatus.ACTIVE.equals(getPacketStatus(wrapper));
	}

	private CashData createCashDataObject(int customerId, String refCode) {
		CashData cash = new CashData();
		cash.setCustomerId(customerId);
		cash.setAccumulatedAmount(BigDecimal.ZERO);
		cash.setCurrentAmount(BigDecimal.ZERO);
		cash.setExpiredAmount(BigDecimal.ZERO);
		cash.setRedeemedAmount(BigDecimal.ZERO);
		cash.setRetainedAmount(BigDecimal.ZERO);
		cash.setReferralCode(refCode);
		cash.setCreationTime(AppUtils.getCurrentTimestamp());
		cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		return cash;
	}

	@Override
	public synchronized Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> activateCashPackets() {
		Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> customerMap = new HashMap<>();
		List<CashPacketData> cashPackets = getReadyForActivationPackets();
		if (cashPackets == null || cashPackets.isEmpty()) {
			LOG.info("No packets for activation");
			return customerMap;
		}

		CashTransactionMetadata metadata = new CashTransactionMetadata();
		metadata.setCashMetadataType(null);
		metadata.setCashTransactionCategory(CashTransactionCategory.ADDITION);
		metadata.setCashTransactionCode(CashTransactionCode.REFERENT_TRANSACTION);
		metadata.setTransactionType(TransactionType.CREDIT);
		CashMetadataWrapper wrapper = null;

		for (CashPacketData packet : cashPackets) {
			try {

				wrapper = new CashMetadataWrapper();
				wrapper.setMetadata(metadata);
				wrapper.setTransactionAmount(packet.getCurrentAmount());
				wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

				CashData c = manager.find(CashData.class, packet.getCashDataId());
				c.setAccumulatedAmount(AppUtils.add(c.getAccumulatedAmount(), wrapper.getTransactionAmount()));
				c.setCurrentAmount(AppUtils.add(c.getCurrentAmount(), wrapper.getTransactionAmount()));

				wrapper.setCustomerId(c.getCustomerId());

				addCashLog(c, wrapper);

				packet.setEventStatus(CashPacketEventStatus.ACTIVE.name());
				if(!CashMetadataType.CBDAY_CASH_BONUS.name().equals(packet.getTransactionCode())){
					packet.setExpirationDate(getPacketExpirationDate(wrapper.getTransactionTime()));
				}else{
					packet.setExpirationDate(packet.getInitialExpirationDate());
					metadata.setCashMetadataType(null);
					metadata.setCashTransactionCategory(CashTransactionCategory.CASH_BONUS);
					metadata.setCashTransactionCode(CashTransactionCode.CBDAY_CASH_BONUS);
					metadata.setTransactionType(TransactionType.CREDIT);
				}
				packet.setLastUpdateTime(wrapper.getTransactionTime());

				addPacketLog(true, packet, wrapper.getTransactionAmount(), wrapper);
				if (customerMap.containsKey(packet.getCustomerId())) {
					customerMap.put(packet.getCustomerId(),
							org.springframework.data.util.Pair.of(packet.getExpirationDate(),customerMap.get(packet.getCustomerId()).getSecond().add(wrapper.getTransactionAmount())));
				} else {
					customerMap.put(packet.getCustomerId(), org.springframework.data.util.Pair.of(packet.getExpirationDate(),wrapper.getTransactionAmount()));
				}

			} catch (Exception e) {
				LOG.error("Error while cash packet activation", JSONSerializer.toJSON(packet), e);
			}
		}
		return customerMap;
	}

	@SuppressWarnings("unchecked")
	private List<CashPacketData> getReadyForActivationPackets() {
		try {
			Query query = manager.createQuery(
					"FROM CashPacketData E WHERE E.activationTime <= :activationTime AND eventStatus = :eventStatus");
			query.setParameter("activationTime", AppUtils.getBusinessDate());
			query.setParameter("eventStatus", CashPacketEventStatus.READY_FOR_ACTIVATION.name());

			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Error while searching Cash Packet for activation", e);
			return null;
		}
	}

	@Override
	public synchronized void expireCashPackets(int limit) {
		List<CashPacketData> cashPackets = getExpiringPackets(limit);
		if (cashPackets == null || cashPackets.isEmpty()) {
			LOG.info("No packets for Expiration");
			return;
		}


		for (CashPacketData packet : cashPackets) {
			expireCashPacket(packet, CashTransactionCategory.EXPIRATION);
		}
	}

	private void expireCashPacket(CashPacketData packet, CashTransactionCategory cashTransactionCategory) {
		CashTransactionMetadata metadata = new CashTransactionMetadata();
		metadata.setCashMetadataType(null);
		metadata.setCashTransactionCategory(cashTransactionCategory);
		metadata.setCashTransactionCode(CashTransactionCode.VALIDITY_EXPIRED);
		metadata.setTransactionType(TransactionType.DEBIT);
		CashMetadataWrapper wrapper = null;
		try {
			wrapper = new CashMetadataWrapper();
			wrapper.setMetadata(metadata);
			wrapper.setTransactionAmount(packet.getCurrentAmount());
			wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

			CashData c = manager.find(CashData.class, packet.getCashDataId());
			c.setExpiredAmount(AppUtils.add(c.getExpiredAmount(), wrapper.getTransactionAmount()));
			c.setCurrentAmount(AppUtils.subtract(c.getCurrentAmount(), wrapper.getTransactionAmount()));
			if(c.getCurrentAmount().compareTo(BigDecimal.ZERO) < 0) {
				c.setCurrentAmount(BigDecimal.ZERO);
			}

			wrapper.setCustomerId(c.getCustomerId());

			addCashLog(c, wrapper);

			packet.setEventStatus(CashPacketEventStatus.EXPIRED.name());
			packet.setExpiredAmount(AppUtils.add(packet.getExpiredAmount(), wrapper.getTransactionAmount()));
			packet.setCurrentAmount(BigDecimal.ZERO);
			packet.setLastUpdateTime(wrapper.getTransactionTime());

			addPacketLog(false, packet, wrapper.getTransactionAmount(), wrapper);

		} catch (Exception e) {
			LOG.error("Error while expiring cash packet {}", JSONSerializer.toJSON(packet), e);
		}
	}

	@SuppressWarnings("unchecked")
	private List<CashPacketData> getExpiringPackets(int limit) {
		try {
			Query query = manager.createQuery(
					"FROM CashPacketData E WHERE E.expirationDate <= :expirationDate AND E.currentAmount > 0 AND eventStatus = :eventStatus");
			query.setParameter("expirationDate", AppUtils.getBusinessDate());
			query.setParameter("eventStatus", AppConstants.ACTIVE);
			query.setMaxResults(limit);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Error while searching Cash Packet for expiration", e);
			return null;
		}
	}

	public Long countExpiringPackets() {
		try {
			Query query = manager.createQuery(
					"select count(*) FROM CashPacketData E WHERE E.expirationDate <= :expirationDate AND E.currentAmount > 0 AND eventStatus = :eventStatus");
			query.setParameter("expirationDate", AppUtils.getBusinessDate());
			query.setParameter("eventStatus", AppConstants.ACTIVE);
			return (Long)query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Error while searching Cash Packet for expiration", e);
			return 0L;
		}
	}

	@Override
	public void updateRefCodeInCashData(int customerId, String refCode) {
		CashData cash = getCustomerCashData(customerId);
		if (cash != null && cash.getReferralCode() == null) {
			cash.setReferralCode(refCode);
			update(cash);
		}
	}

	@Override
	public void sendRefferalSMS(List<String> contactNumbers, String campaign, String source) {
		if (contactNumbers == null || contactNumbers.isEmpty()) {
			return;
		}
		for (String contact : contactNumbers) {
			try {
				CustomerInfo c = getCustomerInfoObject(contact);
				if (c != null) {
					sendReferralMessage(c, campaign, source);
				} else {
					LOG.info("Customer does not exist {}", contact);
				}
			} catch (IOException | URISyntaxException e) {
				LOG.error("Error while sending referral SMS", e);
			}
		}

	}

	public CustomerInfo getCustomerInfoObject(String contact) {
		try {
			Query query = manager.createQuery("FROM CustomerInfo E where E.contactNumber = :contactNumber");
			query.setParameter("contactNumber", contact);
			return (CustomerInfo) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.info("No Customer with contact number {}", contact);
		}
		return null;
	}

	private void sendReferralMessage(CustomerInfo c, String campaign, String source) throws IOException, URISyntaxException {
		c.setIsRefSubscriber(AppUtils.YES);
		String url = createReferralUrl(c.getRefCode(), c.getFirstName(), campaign, source);
		ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(url);
		ReferralLinkData data = new ReferralLinkData(c.getFirstName(), shortUrl.getUrl(), getTopUpReferent(),
				c.getRefCode());
		// sendReferralSMS(c.getContactNumber(), data);
	}

	private String createReferralUrl(String refCode, String name, String campaign, String source)
			throws URISyntaxException, MalformedURLException {

		URIBuilder builder = new URIBuilder("https://cafes.chaayos.com/refer");

		if (refCode != null && refCode.trim().length() > 0) {
			builder.addParameter("r", refCode);
		}
		if (name != null && name.trim().length() > 0) {
			builder.addParameter("n", name);
		}
		if (campaign != null && campaign.length() > 0) {
			builder.addParameter("c", campaign);
		}
		if (source != null && source.length() > 0) {
			builder.addParameter("s", source);
		}

		return builder.build().toURL().toString();
	}

	protected boolean sendReferralSMS(String contact, ReferralLinkData token) {
		try {
			String message = CustomerSMSNotificationType.REFERRAL_SUBSCRIPTION_NOTIFY.getMessage(token);
			return notificationService.sendNotification(CustomerSMSNotificationType.REFERRAL_SUBSCRIPTION_NOTIFY.name(),
					message, contact,
					providerService.getSMSClient(
							CustomerSMSNotificationType.REFERRAL_SUBSCRIPTION_NOTIFY.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					props.getSendAutomatedOTPSMS(),null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the referral message to " + contact, e);
		}
		return false;
	}

	@Override
	public void notifyRefSuccess(Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> customerMap) {
		for (Integer customerId : customerMap.keySet()) {
			CustomerInfo c = manager.find(CustomerInfo.class, customerId);
			CashData cash = getCustomerCashData(customerId);
			ReferralLinkData referralLinkData = new ReferralLinkData(c.getFirstName(), null,
					customerMap.get(customerId).getSecond().intValue(), null, cash.getCurrentAmount().intValue());
			Map<String,String> payload = new HashMap<>();
			payload.put("firstName",c.getFirstName());
			payload.put("refCode",c.getRefCode());
			payload.put("earnedAmt",String.valueOf(referralLinkData.getAmount()));
			payload.put("validDate",AppUtils.getDateInMonth(customerMap.get(customerId).getFirst()));
			payload.put("currentAmount",cash.getCurrentAmount().toString());
			payload.put("orderDayNumber",AppUtils.roundToInteger(AppUtils.divide(cash.getCurrentAmount(),BigDecimal.valueOf(100))).toString());
			sendReferralPacketActivationSMS(c, referralLinkData,payload);
		}
	}

	protected boolean sendReferralPacketActivationSMS(CustomerInfo customerInfo, ReferralLinkData token, Map<String, String> payload) {
		try {
			String message = CustomerSMSNotificationType.REFERRAL_SUCCESS_NOTIFY.getMessage(token);
			return notificationService.sendNotification(CustomerSMSNotificationType.REFERRAL_SUCCESS_NOTIFY.name(),
					message, customerInfo.getContactNumber(),
					providerService.getSMSClient(
							CustomerSMSNotificationType.REFERRAL_SUCCESS_NOTIFY.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					props.getSendAutomatedOTPSMS(),getNotificationPayload(CustomerSMSNotificationType.REFERRAL_SUCCESS_NOTIFY,customerInfo,payload));
		} catch (IOException | JMSException e) {
			LOG.error("WHATSAPP_NOTIFICATOIN :::Error while sending the referral message to " + customerInfo.getContactNumber(), e);
		}
		return false;
	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, CustomerInfo customerInfo, Map<String, String> payload) {
		try {
			NotificationPayload load = new NotificationPayload();
			load.setCustomerId(customerInfo.getCustomerId());
			load.setContactNumber(customerInfo.getContactNumber());
			load.setOrderId(null);
			load.setMessageType(type.name());
			load.setSendWhatsapp(type.isWhatsapp());
			if (Objects.nonNull(customerInfo.getOptWhatsapp())) {
				load.setWhatsappOptIn(customerInfo.getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}
			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(payload);
			return load;
		} catch (Exception e){
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",customerInfo.getCustomerId());
			return null;
		}
	}

}
