package com.stpl.tech.kettle.clevertap.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.clevertap.data.model.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.clevertap.domain.model.GetProfileResponse;
import com.stpl.tech.kettle.clevertap.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;

/**
 * Created by Chaayos on 02-05-2017.
 */
public interface CleverTapDataPushService {

    CleverTapPushResponse pushUsersToCleverTap(List<Integer> customerIds, String updateType,Boolean ...lead) throws DataNotFoundException;

    List<Integer> getCustomerIdsBatch(int batchSize, Integer lastCustomerId);

    GetProfileResponse getUserProfile(Integer customerIds);

    List<Integer> getOrdersBatch(Integer startOrderId, Integer batchSize);

    CleverTapPushResponse uploadEvent(List<Integer> orderList, String evtName, String updateType, Map<Integer, OrderNotification> orderNotificationMap);

    CleverTapPushResponse uploadNextBestOfferEvents(List<Integer> orderList, String evtName, String updateType, String nextOfferEventName);

    CleverTapPushResponse pushUserToCleverTap(Integer customerId,Boolean ...lead);

    void persistProfileTrack(CleverTapProfilePushTrack entity);

    void persistEventTrack(EventPushTrack entity);

    CleverTapPushResponse pushUsersToCleverTapWithCheckCustomer(List<Integer> customerIds, String updateType)
        throws DataNotFoundException;

    void persistProfileTrack(List<CleverTapProfilePushTrack> profilePushTracks);

    void persistEventTracks(List<EventPushTrack> eventPushTrack);


	CleverTapPushResponse publishGenericEvent(Integer customerId, String evtName, String cashMetadataType,
			String cashTransactionCode, Date creationDate, Date expirationDate, BigDecimal amount,
			Boolean sendNotification, CustomerSMSNotificationType customerSMSNotificationType, BigDecimal walletBalance,
			Integer loyaltyBalance, BigDecimal chaayosCash, String comment);
	
	 CleverTapPushResponse uploadProfileAttributes(Integer customerId, long epochSeconds, String updateType, Object data);
	 
		CleverTapPushResponse publishCustomEvent(Integer customerId, String evtName, long epochSeconds,
				String updateType, Object data);
		
		String pushUsersToCleverTapAdhoc(List<String> contactNumber, String updateType, String offerCode,
				String offerText, int minValue) throws DataNotFoundException;

        void updateCustomerAppAction(int customerId , String appAction ) throws DataNotFoundException, DataUpdationException;

    public CleverTapPushResponse uploadEventForPartner(Map<Integer,Integer> orderList,String evtName,String updateType,Map<Integer, OrderNotification> orderNotificationMap);

    public List<ProfileUploadRequest> getUserProfileForCleverTap(List<Integer> customerIds, String updateType)
            throws DataNotFoundException;

    public List<EventUploadRequest> getLeadEventRequestForCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
            throws DataNotFoundException;
    public List<EventUploadRequest> getEventDataList(List<Integer> orderList, String updateType,
                                                     Map<Integer, OrderNotification> orderNotificationMap);
    public CleverTapPushResponse publishToCleverTapQueueNew(Object payload,Integer id,String updateType);

    public void generateOtpEventPayloadForCleverTap(CustomerResponse customer, String otp);

	}
