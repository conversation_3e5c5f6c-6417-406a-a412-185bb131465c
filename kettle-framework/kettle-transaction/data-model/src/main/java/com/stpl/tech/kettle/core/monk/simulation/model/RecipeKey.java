/**
 * 
 */
package com.stpl.tech.kettle.core.monk.simulation.model;

/**
 * <AUTHOR>
 *
 */
public class <PERSON>cipe<PERSON>ey implements Cloneable {

	public int productId;
	public String dimension;
	public int quantity;

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dimension == null) ? 0 : dimension.hashCode());
		result = prime * result + productId;
		result = prime * result + quantity;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RecipeKey other = (RecipeKey) obj;
		if (dimension == null) {
			if (other.dimension != null)
				return false;
		} else if (!dimension.equals(other.dimension))
			return false;
		if (productId != other.productId)
			return false;
		if (quantity != other.quantity)
			return false;
		return true;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#clone()
	 */
	@Override
	protected Object clone() throws CloneNotSupportedException {
		RecipeKey recipe = new RecipeKey();
		recipe.dimension = this.dimension;
		recipe.quantity = this.quantity;
		recipe.productId = this.productId;
		return recipe;
	}

	@Override
	public String toString() {
		return "RecipeKey [productId=" + productId + ", dimension=" + dimension + ", quantity=" + quantity + "]";
	}

}
