/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.SFXCustomer;
import com.stpl.tech.kettle.delivery.model.SFXOrder;
import com.stpl.tech.kettle.delivery.model.SFXRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.AppUtils;

import java.util.List;

public class SFXRequestAdapter implements RequestAdapter<SFXRequest, OrderInfo> {

	private static final Integer PREP_TIME = 5;

	@Override
	public SFXRequest adaptCreate(OrderInfo orderInfo) {
		SFXRequest sfxRequest = new SFXRequest();

		Order order = orderInfo.getOrder();
		Customer customer = orderInfo.getCustomer();
		Address customerAddress = TransactionUtils.getAddressForOrder(order.getDeliveryAddress(),
				customer.getAddresses());
		if (customerAddress != null) {
			String address = customerAddress.getLine1() + " " + customerAddress.getLine2();
			sfxRequest.setCustomer_Details(
					new SFXCustomer(customer.getFirstName(), AppUtils.removeCountryCode(customer.getContactNumber()),
							address, customerAddress.getLocality(), customerAddress.getCity()));
		}
		sfxRequest.setStore_Code(String.valueOf(order.getUnitId()));

		sfxRequest.setOrder_Details(new SFXOrder(order.getGenerateOrderId(),
				order.getTransactionDetail().getPaidAmount().floatValue(), getPaid(order.getSettlements()), PREP_TIME,
				null, AppUtils.getTimeWithoutMillisISTString(order.getBillingServerTime())));

		String unitContact = orderInfo.getUnit().getAddress().getContact1();
		if (unitContact != null) {
			unitContact = AppUtils.removeCountryCode(unitContact);
			sfxRequest.setPickup_Contact_Number(unitContact);
		} else {
			sfxRequest.setPickup_Contact_Number("");
		}
		return sfxRequest;
	}

	@Override
	public SFXRequest adaptCancel(OrderInfo order, String taskId) {
		return null;
	}

	private String getPaid(List<Settlement> settlements) {
		String settlementType = "true";
		if (settlements.size() == 1) {
			if (settlements.get(0).getMode() == 1) {
				settlementType = "false";
			}
		}
		return settlementType;
	}
}
