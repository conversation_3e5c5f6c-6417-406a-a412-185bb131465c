package com.stpl.tech.kettle.facebook.converter;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.facebook.domain.model.FBEventUploadRequest;
import com.stpl.tech.kettle.facebook.domain.model.FBMatchKeys;
import com.stpl.tech.kettle.facebook.util.FBConstants;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.HashGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.NoSuchAlgorithmException;
import java.util.Objects;

public class FBConverter {

    private static final Logger LOG = LoggerFactory.getLogger(FBConverter.class);

    private FBConverter() {
        throw new IllegalStateException("Converter class");
    }

    public static FBEventUploadRequest convert(Order order, String evtName, Customer customerData) {
        FBEventUploadRequest eventRequestItem = new FBEventUploadRequest();
        FBMatchKeys matchingObj = new FBMatchKeys();
        try {
            if(Objects.nonNull(customerData.getContactNumber())) {
                matchingObj.setPhone(HashGenerator.createHash(AppConstants.SHA256_HASH, "91" + customerData.getContactNumber()));
            }
            if(Objects.nonNull(customerData.getEmailId())) {
                matchingObj.setEmail(HashGenerator.createHash(AppConstants.SHA256_HASH, customerData.getEmailId()));
            }
            if(Objects.nonNull(customerData.getFirstName())) {
                matchingObj.setFn(HashGenerator.createHash(AppConstants.SHA256_HASH, customerData.getFirstName()));
            }
        } catch (NoSuchAlgorithmException e) {
            LOG.info("Error while converting to payload data {} for order ", order);
            LOG.info(e.toString());
        }

        eventRequestItem.setMatch_keys(matchingObj);
        eventRequestItem.setCurrency(FBConstants.CURRENCY);
        eventRequestItem.setValue(order.getTransactionDetail().getPaidAmount());
        eventRequestItem.setEvent_name(evtName);
        eventRequestItem.setEvent_time(order.getBillingServerTime().getTime() / 1000);
        eventRequestItem.setOrder_id(order.getGenerateOrderId());
        return eventRequestItem;
    }
}
