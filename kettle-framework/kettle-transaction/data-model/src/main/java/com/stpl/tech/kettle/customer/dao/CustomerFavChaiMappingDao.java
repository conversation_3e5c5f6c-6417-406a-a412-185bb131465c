package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.util.AppConstants;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerFavChaiMappingDao extends JpaRepository<CustomerFavChaiMapping,Integer> {
    Optional<CustomerFavChaiMapping> findByCreatedAt(Date createdAt);

    List<CustomerFavChaiMapping> findByCustomerIdAndStatus(Integer customerId, String status);

}
