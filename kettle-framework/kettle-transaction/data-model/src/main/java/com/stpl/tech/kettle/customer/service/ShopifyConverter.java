package com.stpl.tech.kettle.customer.service;


import com.stpl.tech.kettle.core.data.vo.ShopifyCustomerResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ShopifyAddress;
import com.stpl.tech.util.AppConstants;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class ShopifyConverter {

    private static final ObjectFactory objectFactory = new ObjectFactory();

    public final Customer getCustomerFromShopify(ShopifyCustomerResponse shopifyCustomerResponse) {
        Customer customer = objectFactory.createCustomer();
        customer.setFirstName(shopifyCustomerResponse.getFirstName());
        customer.setLastName(shopifyCustomerResponse.getLastName());
        if (shopifyCustomerResponse.getAddresses() != null) {
            customer.setAddresses(getAddressFromShopifyAddress(shopifyCustomerResponse.getAddresses()));
        }
        customer.setContactNumber(shopifyCustomerResponse.getPhone());
        customer.setEmailId(shopifyCustomerResponse.getEmail());
        customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customer.setRegistrationUnitId(shopifyCustomerResponse.getUnitId());
        customer.setAcquisitionSource("SHOPIFY");
        customer.setAcquisitionToken("SHOPIFY_API");
        customer.setOrderCount(0);
        customer.setAvailedSignupOffer(false);
        if (shopifyCustomerResponse.getBrandId() == null) {
            shopifyCustomerResponse.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
            customer.setAcquisitionBrandId(shopifyCustomerResponse.getBrandId());
        }
        customer.setChaayosCustomer(shopifyCustomerResponse.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID));
        customer.setChaayosCustomer(true);
        return customer;
    }

    public void updateShopifyCustomerResponse(Customer customer, ShopifyCustomerResponse shopifyCustomerResponse) {
        shopifyCustomerResponse.setIid(customer.getId());
        shopifyCustomerResponse.setFirstName(customer.getFirstName());
        shopifyCustomerResponse.setLastName(customer.getLastName());
        shopifyCustomerResponse.setPhone(customer.getContactNumber());
        shopifyCustomerResponse.setEmail(customer.getEmailId());
        shopifyCustomerResponse.setVerifiedEmail(customer.isEmailVerified());
    }

    public List<Address> getAddressFromShopifyAddress(List<ShopifyAddress> shopifyAddresses) {
        List<Address> addresses = new ArrayList<>();

        for (ShopifyAddress shopifyAddress : shopifyAddresses) {
            if (shopifyAddress.getLine1() == null) continue;
            Address address = new Address();
            address.setSource(shopifyAddress.getSource());
            address.setSourceId(shopifyAddress.getSourceId());
            address.setName(shopifyAddress.getName());
            address.setLine1(shopifyAddress.getLine1());
            address.setLine2(shopifyAddress.getLine2());
            address.setLocality(shopifyAddress.getLocality());
            address.setCity(shopifyAddress.getCity());
            address.setState(shopifyAddress.getState());
            address.setCountry(shopifyAddress.getCountry());
            address.setZipCode(shopifyAddress.getZipCode());
            address.setCompany(shopifyAddress.getCompany());
            address.setContact1(shopifyAddress.getContact1());
            address.setPreferredAddress(shopifyAddress.getPreferredAddress());
            address.setAddressType(shopifyAddress.getAddressType());
            addresses.add(address);
        }
        return addresses;
    }


    public List<Address> updateAddressFromShopifyAddress(List<Address> existingCustomerAddresses, List<ShopifyAddress> shopifyAddresses) {

        for (ShopifyAddress shopifyAddress : shopifyAddresses) {
            boolean newAddress = true;
            for (Address existingCustomerAddress : existingCustomerAddresses) {
                existingCustomerAddress.setStatus(AppConstants.IN_ACTIVE);
                if (Objects.equals(shopifyAddress.getSourceId(), existingCustomerAddress.getSourceId())) {
                    existingCustomerAddress.setSource(shopifyAddress.getSource());
                    existingCustomerAddress.setSourceId(shopifyAddress.getSourceId());
                    existingCustomerAddress.setName(shopifyAddress.getName());
                    existingCustomerAddress.setLine1(shopifyAddress.getLine1());
                    existingCustomerAddress.setLine2(shopifyAddress.getLine2());
                    existingCustomerAddress.setLocality(shopifyAddress.getLocality());
                    existingCustomerAddress.setCity(shopifyAddress.getCity());
                    existingCustomerAddress.setState(shopifyAddress.getState());
                    existingCustomerAddress.setCountry(shopifyAddress.getCountry());
                    existingCustomerAddress.setZipCode(shopifyAddress.getZipCode());
                    existingCustomerAddress.setCompany(shopifyAddress.getCompany());
                    existingCustomerAddress.setContact1(shopifyAddress.getContact1());
                    existingCustomerAddress.setPreferredAddress(shopifyAddress.getPreferredAddress());
                    existingCustomerAddress.setAddressType(shopifyAddress.getAddressType());
                    existingCustomerAddress.setStatus(AppConstants.ACTIVE);
                    newAddress = false;
                    break;
                }
            }

            if (newAddress) {
                Address address = new Address();
                address.setSource(shopifyAddress.getSource());
                address.setSourceId(shopifyAddress.getSourceId());
                address.setName(shopifyAddress.getName());
                address.setLine1(shopifyAddress.getLine1());
                address.setLine2(shopifyAddress.getLine2());
                address.setLocality(shopifyAddress.getLocality());
                address.setCity(shopifyAddress.getCity());
                address.setState(shopifyAddress.getState());
                address.setCountry(shopifyAddress.getCountry());
                address.setZipCode(shopifyAddress.getZipCode());
                address.setCompany(shopifyAddress.getCompany());
                address.setContact1(shopifyAddress.getContact1());
                address.setPreferredAddress(shopifyAddress.getPreferredAddress());
                address.setAddressType(shopifyAddress.getAddressType());
                address.setStatus(AppConstants.ACTIVE);
                existingCustomerAddresses.add(address);
            }
        }
        return existingCustomerAddresses;
    }


}
