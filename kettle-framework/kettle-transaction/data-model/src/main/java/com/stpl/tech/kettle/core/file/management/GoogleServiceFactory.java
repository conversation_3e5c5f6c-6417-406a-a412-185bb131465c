/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.management;

import java.io.IOException;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.DriveScopes;
import com.google.api.services.sheets.v4.Sheets;

public class GoogleServiceFactory {

	private static final Logger LOG = LoggerFactory.getLogger(GoogleServiceFactory.class);

	private static final GoogleServiceFactory INSTANCE = new GoogleServiceFactory();

	/** Email of the Service Account */
	private static final String SERVICE_ACCOUNT_EMAIL = "<EMAIL>";

	/**
	 * Build and returns a Drive service object authorized with the service
	 * accounts that act on behalf of the given user.
	 *
	 * @param userEmail
	 *            The email of the user.
	 * @return Drive service object that is ready to make requests.
	 */

	private static final String SERVICE_ACCOUNT_PKCS12_FILE_PATH = "Credentials.p12";

	private static final JacksonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();

	private static final HttpTransport HTTP_TRANSPORT = new NetHttpTransport();

	private Drive driveService;

	private Sheets sheetService;

	private GoogleCredential credentials;

	public static GoogleServiceFactory getInstance() {
		return INSTANCE;
	}

	public GoogleCredential getCredentials(String userAccount) {
		if (credentials == null) {
			try {
				KeyStore keystore = KeyStore.getInstance("PKCS12");
				keystore.load(
						GoogleSheetLoader.class.getClassLoader().getResourceAsStream(SERVICE_ACCOUNT_PKCS12_FILE_PATH),
						"notasecret".toCharArray());
				PrivateKey pk = (PrivateKey) keystore.getKey("privatekey", "notasecret".toCharArray());
				credentials = new GoogleCredential.Builder().setTransport(HTTP_TRANSPORT).setJsonFactory(JSON_FACTORY)
						.setServiceAccountId(SERVICE_ACCOUNT_EMAIL)
						.setServiceAccountScopes(Arrays.asList(DriveScopes.DRIVE)).setServiceAccountUser(userAccount)
						.setServiceAccountPrivateKey(pk).build();
			} catch (Throwable e) {
				LOG.error("Error in creating credentials ", e);
			}

		}
		return credentials;
	}

	public Drive getDriveService(String userAccount) {
		if (driveService == null) {
			driveService = new Drive.Builder(HTTP_TRANSPORT, JSON_FACTORY, null)
					.setHttpRequestInitializer(getCredentials(userAccount)).build();
		}
		return driveService;
	}

	/**
	 * Build and return an authorized Sheets API client service.
	 * 
	 * @return an authorized Sheets API client service
	 * @throws IOException
	 */
	public Sheets getSheetsService(String userAccount) throws IOException {
		if (sheetService == null) {
			sheetService = new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(userAccount)).build();
		}
		return sheetService;
	}
}
