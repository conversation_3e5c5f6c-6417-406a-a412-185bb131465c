package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.clm.dao.impl.CLMDataAbstractDaoImpl;
import com.stpl.tech.kettle.data.dao.WalletRecommendationDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.List;

@Slf4j
@Repository
public class WalletRecommendationDaoImpl extends CLMDataAbstractDaoImpl implements WalletRecommendationDao {

    @Override
    @Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Object[]> getCustomerWalletInfo(Integer customerId, Integer brandId){

        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_WALLET_SUGGESTION_DECISION(:customerId,:brandId)");
            query.setParameter("customerId",customerId);
            query.setParameter("brandId",brandId);
            return query.getResultList();
        }
        catch (Exception e){
            log.error("Error fetching customer Wallet Info from One View for customer:"+customerId,e);
        }
        return null;
    }
}
