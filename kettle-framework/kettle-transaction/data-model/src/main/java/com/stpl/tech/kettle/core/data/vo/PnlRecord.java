package com.stpl.tech.kettle.core.data.vo;

import java.util.ArrayList;
import java.util.List;

public class PnlRecord {

	private String key;
	private String currentValue;
	private List<String> mtdValue;
	private String budget;
	private String budgetMtd;
	private List<PnlRecord> drilldowns;

	public PnlRecord(String key, String value, List<String> mtdValue, String budget,String budgetMtd) {
		super();
		this.key = key;
		this.currentValue = value;
		this.mtdValue = mtdValue;
		this.budget = budget;
		this.budgetMtd=budgetMtd;
	}

	public PnlRecord(String key) {
		super();
		this.key = key;
	}

	public PnlRecord() {
		super();
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getCurrentValue() {
		return currentValue;
	}

	public void setCurrentValue(String currentValue) {
		this.currentValue = currentValue;
	}

	public List<String> getMtdValue() {
		if (mtdValue == null) {
			mtdValue = new ArrayList<>();
		}
		return mtdValue;
	}

	public void setMtdValue(List<String> mtdValue) {
		this.mtdValue = mtdValue;
	}

	public String getBudget() {
		return budget;
	}

	public void setBudget(String budget) {
		this.budget = budget;
	}

	public List<PnlRecord> getDrilldowns() {
		if (this.drilldowns == null) {
			this.drilldowns = new ArrayList<PnlRecord>();
		}
		return this.drilldowns;
	}

	public void setDrilldowns(List<PnlRecord> drilldowns) {
		this.drilldowns = drilldowns;
	}

	public String getBudgetMtd() {
		return budgetMtd;
	}

	public void setBudgetMtd(String budgetMtd) {
		this.budgetMtd = budgetMtd;
	}

	
}
