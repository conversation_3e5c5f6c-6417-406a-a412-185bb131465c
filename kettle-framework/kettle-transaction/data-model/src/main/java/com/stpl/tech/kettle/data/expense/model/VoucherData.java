package com.stpl.tech.kettle.data.expense.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "VOUCHER_DATA")
public class VoucherData {

    private Integer id;
    private String generatedVoucherId;
    private String accountType;
    private String accountNo;
    private Integer walletId;
    private Date businessDate;
    private String expenseType;
    private String expenseDetail;
    private String currentStatus;
    private BigDecimal issuedAmount;
    private BigDecimal expenseAmount;
    private Integer issuedTo;
    private Date issuedTime;
    private Integer issuedBy;
    private Date lastUpdatedTime;
    private String isReimbursed;
    private List<VoucherFileData> fileDatas = new ArrayList<VoucherFileData>();
    private List<VoucherStatusData> statusDatas = new ArrayList<>();
    private Integer claimId = null;
    private Integer expenseMetadataId;
    private String expenseCategory;
    private String accountableInPNL;
    private String budgetCategory;
    private Integer pnlDetailId;
    private Date pnlInclusionDate;
    private Integer grNumber;
    private Date voucherDate;



    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VOUCHER_ID", nullable = false)

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "GENERATED_VOUCHER_ID", nullable = false, unique = true)
    public String getGeneratedVoucherId() {
        return generatedVoucherId;
    }

    public void setGeneratedVoucherId(String generatedVoucherId) {
        this.generatedVoucherId = generatedVoucherId;
    }

    @Column(name = "ACCOUNT_TYPE", nullable = false)
    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    @Column(name = "ACCOUNT_NO", nullable = false)
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    @Column(name = "WALLET_ID", nullable = false)
    public Integer getWalletId() {
        return walletId;
    }

    public void setWalletId(Integer walletId) {
        this.walletId = walletId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = false)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "EXPENSE_TYPE", nullable = true)
    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    @Column(name = "EXPENSE_DETAIL", nullable = true)
    public String getExpenseDetail() {
        return expenseDetail;
    }

    public void setExpenseDetail(String expenseDetail) {
        this.expenseDetail = expenseDetail;
    }

    @Column(name = "CURRENT_STATUS", nullable = false)
    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    @Column(name = "ISSUED_AMOUNT", nullable = false)
    public BigDecimal getIssuedAmount() {
        return issuedAmount;
    }

    public void setIssuedAmount(BigDecimal issuedAmount) {
        this.issuedAmount = issuedAmount;
    }

    @Column(name = "EXPENSE_AMOUNT", nullable = true)
    public BigDecimal getExpenseAmount() {
        return expenseAmount;
    }

    public void setExpenseAmount(BigDecimal expenseAmount) {
        this.expenseAmount = expenseAmount;
    }

    @Column(name = "ISSUED_TO", nullable = false)
    public Integer getIssuedTo() {
        return issuedTo;
    }

    public void setIssuedTo(Integer issuedTo) {
        this.issuedTo = issuedTo;
    }

    @Column(name = "ISSUED_TIME", nullable = false)
    public Date getIssuedTime() {
        return issuedTime;
    }

    public void setIssuedTime(Date issuedTime) {
        this.issuedTime = issuedTime;
    }

    @Column(name = "ISSUED_BY", nullable = false)
    public Integer getIssuedBy() {
        return issuedBy;
    }

    public void setIssuedBy(Integer issuedBy) {
        this.issuedBy = issuedBy;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = true)
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "voucherData")
    public List<VoucherFileData> getFileDatas() {
        return fileDatas;
    }

    public void setFileDatas(List<VoucherFileData> fileDatas) {
        this.fileDatas = fileDatas;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "voucherData")
    public List<VoucherStatusData> getStatusDatas() {
        return statusDatas;
    }

    public void setStatusDatas(List<VoucherStatusData> statusDatas) {
        this.statusDatas = statusDatas;
    }

    @Column(name = "IS_REIMBURSED", nullable = true)
    public String getIsReimbursed() {
        return isReimbursed;
    }

    public void setIsReimbursed(String isReimbursed) {
        this.isReimbursed = isReimbursed;
    }

    @Column(name = "CLAIM_ID", nullable = true)
    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    @Column(name = "EXPENSE_METADATA_ID" , nullable = false)
    public Integer getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(Integer expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    @Column(name = "EXPENSE_CATEGORY", nullable = false, length = 50)
    public String getExpenseCategory() {
        return expenseCategory;
    }

    public void setExpenseCategory(String expenseCategory) {
        this.expenseCategory = expenseCategory;
    }

    @Column(name = "ACCOUNTABLE_IN_PNL", nullable = false, length = 1)
    public String getAccountableInPNL() {
        return accountableInPNL;
    }

    public void setAccountableInPNL(String accountableInPNL) {
        this.accountableInPNL = accountableInPNL;
    }

    @Column(name = "BUDGET_CATEGORY", length = 200)
    public String getBudgetCategory() {
        return budgetCategory;
    }

    public void setBudgetCategory(String budgetCategory) {
        this.budgetCategory = budgetCategory;
    }

    @Column(name = "PNL_DETAIL_ID")
    public Integer getPnlDetailId() {
        return pnlDetailId;
    }

    public void setPnlDetailId(Integer pnlDetailId) {
        this.pnlDetailId = pnlDetailId;
    }

    @Column(name = "PNL_INCLUSION_DATE")
    public Date getPnlInclusionDate() {
        return pnlInclusionDate;
    }

    public void setPnlInclusionDate(Date pnlInclusionDate) {
        this.pnlInclusionDate = pnlInclusionDate;
    }
    @Column(name = "GR_NUMBER",unique = true)
    public Integer getGrNumber() {
        return grNumber;
    }

    public void setGrNumber(Integer grNumber) {
        this.grNumber = grNumber;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "VOUCHER_DATE")
    public Date getVoucherDate(){ return voucherDate; }

    public void setVoucherDate(Date voucherDate){
        this.voucherDate = voucherDate;
    }
}
