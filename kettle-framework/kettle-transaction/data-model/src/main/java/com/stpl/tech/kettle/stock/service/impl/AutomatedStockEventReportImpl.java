package com.stpl.tech.kettle.stock.service.impl;

import com.stpl.tech.kettle.core.data.vo.StockOutReportData;
import com.stpl.tech.kettle.data.dao.PosMetadataDao;
import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventAggregate;
import com.stpl.tech.kettle.data.mongo.stock.dao.UnitProductStockEventDataDao;
import com.stpl.tech.kettle.domain.model.UnitProductsStockEventData;
import com.stpl.tech.kettle.stock.dao.UnitProductStockDataDao;
import com.stpl.tech.kettle.stock.dao.UnitProductStockEventAggregateDao;
import com.stpl.tech.kettle.stock.service.AutomatedStockEventReport;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class AutomatedStockEventReportImpl implements AutomatedStockEventReport {

    private static final Logger LOG = LoggerFactory.getLogger(AutomatedStockEventReportImpl.class);

    @Autowired
    UnitProductStockDataDao dao;

    @Autowired
    UnitProductStockDataDao partnerStockDao;

    @Autowired
    UnitProductStockEventDataDao unitProductStockDataDao;

    @Autowired
    UnitProductStockEventAggregateDao unitProductStockEventAggregateDao;

    @Autowired
    MasterDataCache cache;

    UnitProductStockData addUnitProductStockData(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new UnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(),
                stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                stockData.getEventTimeStamp(), AppUtils.getHour(fromStamp), ((AppUtils.getHour(stockData.getEventTimeStamp()) + 1) % 24),
                hoursDifference(AppUtils.getHour(fromStamp), ((AppUtils.getHour(stockData.getEventTimeStamp()) + 1) % 24)), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    UnitProductStockData addUnitProductStockDataAfterDayClose(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new UnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(), stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                endTime, AppUtils.getHour(fromStamp), ((AppUtils.getHour(endTime) + 1) % 24),
                hoursDifference(AppUtils.getHour(fromStamp), ((AppUtils.getHour(endTime) + 1) % 24)), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    PartnerUnitProductStockData addPartnerUnitProductStockData(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new PartnerUnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(),
                stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                stockData.getEventTimeStamp(), AppUtils.getHour(fromStamp), ((AppUtils.getHour(stockData.getEventTimeStamp()) + 1) % 24),
                hoursDifference(AppUtils.getHour(fromStamp), ((AppUtils.getHour(stockData.getEventTimeStamp()) + 1) % 24)), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    PartnerUnitProductStockData addPartnerUnitProductStockDataAfterDayClose(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new PartnerUnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(), stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                endTime, AppUtils.getHour(fromStamp), ((AppUtils.getHour(endTime) + 1) % 24),
                hoursDifference(AppUtils.getHour(fromStamp), ((AppUtils.getHour(endTime) + 1) % 24)), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    private Integer hoursDifference(int startHour, int endHour) {
        if (endHour < startHour) {
            return ((24 - Math.abs(endHour - startHour)) * 60);
        } else {
            return (Math.abs(endHour - startHour) * 60);
        }
    }

    //    boolean checkDayCloseTime(UnitProductStockEventDataDetail unitProductsStockEventData, List<Date> unitTime) {
    boolean checkDayCloseTime(UnitProductsStockEventData unitProductsStockEventData, List<Date> unitTime) {
        return unitProductsStockEventData.getEventTimeStamp().after(unitTime.get(0))
                && unitProductsStockEventData.getEventTimeStamp().before(unitTime.get(1));
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StockOutReportData execute(Date previousDate, Integer unitId,
                                      boolean saveResults, List<Date> unitTime, boolean partner,Integer brand) {
        try {
            if (previousDate != null && unitId != null && unitTime != null) {
                Unit unitData = cache.getUnit(unitId);
//                List<UnitProductStockEventDataDetail> stockData = unitProductStockDataDao
//                        .findAllByUnitIdAndEventTimeStampOrderByProductIdAsc(unitId, previousDate);
                List<UnitProductsStockEventData> stockData = unitProductStockDataDao
                        .findAllByUnitIdAndBrandIdAndEventTimeStampBetweenOrderByProductIdAscDimensionAscEventTimeStampAsc(unitId, brand,
                                AppUtils.getStartOfBusinessDay(previousDate), AppUtils.getStartOfBusinessDay(AppUtils.getDayBeforeOrAfterDay(previousDate, 1)));
                Map<String, PartnerUnitProductStockData> previousDayStockData =
                        partnerStockDao.getPreviousDayStockData(unitId, AppUtils.getDayBeforeOrAfterDay(AppUtils.getCurrentDate(), -2));
                Map<String, List<UnitProductsStockEventData>> productIdsInStockData = new HashMap<>();
                for(UnitProductsStockEventData sd : stockData){
                    String key = sd.getProductId()+sd.getDimension();
                    if(!productIdsInStockData.containsKey(key)){
                        productIdsInStockData.put(key, new ArrayList<>());
                    }
                    productIdsInStockData.get(key).add(sd);
                }
                for(Product product : cache.getUnitProductDetails(unitId)){
                    if(!brand.equals(product.getBrandId()) || !product.isInventoryTracked() || !ProductStatus.ACTIVE.equals(product.getStatus())){
                        continue;
                    }
                    for(ProductPrice price : product.getPrices()){
                        String key = product.getId()+price.getDimension();
                        if(!productIdsInStockData.containsKey(key)){
                            productIdsInStockData.put(key, new ArrayList<>());
                            PartnerUnitProductStockData prevData = previousDayStockData.get(key);
                            if(Objects.isNull(prevData) || prevData.getCloseTime().equals(prevData.getCafeClosing())){
                                productIdsInStockData.get(key).add(new UnitProductsStockEventData(unitId,unitData.getName(), product.getId(),
                                        product.getName(), AppConstants.STOCK_OUT_CONSTANT,unitTime.get(0),price.getDimension(),brand, InventorySource.CAFE_ORDER.name()));
                            }
                        }
                    }

                }
                stockData = new ArrayList<>();
                for(Map.Entry<String, List<UnitProductsStockEventData>> map : productIdsInStockData.entrySet()){
                    stockData.addAll(map.getValue());
                }
                List<UnitProductStockData> stockOutEventData = new ArrayList<>();
                List<PartnerUnitProductStockData> partnerStockOutEventData = new ArrayList<>();
                if (stockData != null) {
                    LOG.info("Success for GET request for Unit {}", unitId);
                } else {
                    return null;
                }
                int downtime;
                Date fromStamp = new Date();
                Date fromStampCheck = new Date();
                for (int i = 0; i < stockData.size(); i++) {
                    LOG.info("opening : {} and closing is :{}",unitTime.get(0),unitTime.get(1));
                    LOG.info("status is : {} and unit :{} product id :{} dimesion :{} and eventTime is : {}",stockData.get(i).getStatus(),stockData.get(i).getUnitId(),stockData.get(i).getProductId(),stockData.get(i).getDimension(),stockData.get(i).getEventTimeStamp());
                    try {
                        if (stockData.get(i).getBrandId() == null) {
                            int brandId = getBrandId(stockData, i);
                            stockData.get(i).setBrandId(brandId);
                        }
                        if (stockData.get(i).getDimension() == null) {
                            String dimensionName = getDimension(stockData, i);
                            stockData.get(i).setDimension(dimensionName);
                        }
                        if(stockData.size() > 1 && i!=0){
                            if(!stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    || !stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    || !stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())){
                                fromStamp = unitTime.get(0);
                                LOG.info("Changed from stamp for new product or dimension or brand");
                            }
                        }
                        if (stockData.get(i).getStatus().equals(AppConstants.STOCK_IN_CONSTANT)
                                && checkDayCloseTime(stockData.get(i), unitTime)) {
                            if (i == 0) {
                                continue;
                            }
                            if (stockData.get(i - 1).getStatus().equals(AppConstants.STOCK_IN_CONSTANT)
                                    && stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                continue;
                            }
                            if (stockData.get(i - 1).getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)
                                    && stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                if (fromStampCheck.equals(fromStamp)) {
                                    fromStamp = unitTime.get(0);
                                }
                                downtime = (AppUtils.getMinDiffernce(fromStamp, stockData.get(i).getEventTimeStamp()));
                                if(!partner){
                                    stockOutEventData.add(addUnitProductStockData(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                                            unitTime.get(1), downtime, fromStamp));
                                }else {
                                    partnerStockOutEventData.add(addPartnerUnitProductStockData(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                                            unitTime.get(1), downtime, fromStamp));
                                }
                                fromStamp = unitTime.get(0);
                            }
                        }
                        if (stockData.get(i).getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)
                                && checkDayCloseTime(stockData.get(i), unitTime)) {
                            if (i == 0) {
                                fromStamp = stockData.get(i).getEventTimeStamp();
                            }
                            if (i > 0 && stockData.get(i - 1).getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)
                                    && (stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension()))
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                continue;
                            }
                            if (i > 0 && (!stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    || !stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    || !stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId()))) {
                                fromStamp = stockData.get(i).getEventTimeStamp();
                            }
                            if (i > 0 && stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                fromStamp = stockData.get(i).getEventTimeStamp();
                            }

//                            if ((i == stockData.size()) || (i < stockData.size() - 1 && (!stockData.get(i).getProductId().equals(stockData.get(i + 1).getProductId())
//                                    || !stockData.get(i).getDimension().equals(getDimension(stockData, i + 1))
//                                    || !stockData.get(i).getBrandId().equals(getBrandId(stockData, i + 1))))) {
//
//
//                                fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData, i);
//                            }

                            if((i == stockData.size())){
                                fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i,partner);
                            }
                            else if((i < stockData.size() - 1)){
                                if(!stockData.get(i).getProductId().equals(stockData.get(i + 1).getProductId()) || !checkDayCloseTime(stockData.get(i+1), unitTime)){
                                    fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i, partner);
                                }
                                else{
                                    if(!stockData.get(i).getDimension().equals(getDimension(stockData, i + 1)) || !checkDayCloseTime(stockData.get(i+1), unitTime)){
                                        fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i, partner);
                                    }
                                    else{
                                        if(!stockData.get(i).getBrandId().equals(getBrandId(stockData, i + 1)) || !checkDayCloseTime(stockData.get(i+1), unitTime)){
                                            fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i, partner);
                                        }
                                    }
                                }

                            }
                        }
                    } catch (Exception e) {
                        LOG.error("Exception Caught While Generating Data for product Id ::{}", stockData.get(i).getProductId(), e);
                    }
                }

                StockOutReportData data = new StockOutReportData();
                if (saveResults) {
                    LOG.info("Loading Data into Database");
                    List<UnitProductStockData> stockReport = dao.addAll(stockOutEventData);
                    partnerStockDao.addAll(partnerStockOutEventData);
                    data.setEvents(stockReport);
                    return data;
                } else {
                    LOG.info("Requesting Data for Excel Sheet Creation");
                    data.setEvents(stockOutEventData);
                    return data;
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Faced : ",e);
        }
        return null;
    }

    private Date createStockDataAfterDayClose(List<Date> unitTime, List<UnitProductsStockEventData> stockData, List<UnitProductStockData> stockOutEventData, List<PartnerUnitProductStockData> partnerStockOutEventData, int i, boolean partner) {
        int downtime;
        Date fromStamp;
        downtime = (AppUtils.getMinDiffernce(stockData.get(i).getEventTimeStamp(), unitTime.get(1)));
        if(!partner) {
            stockOutEventData.add(addUnitProductStockDataAfterDayClose(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                    unitTime.get(1), downtime, stockData.get(i).getEventTimeStamp()));
        }
        else {
            partnerStockOutEventData.add(addPartnerUnitProductStockDataAfterDayClose(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                    unitTime.get(1), downtime, stockData.get(i).getEventTimeStamp()));
        }
        fromStamp = unitTime.get(0);
        return fromStamp;
    }

    private int getBrandId(List<UnitProductsStockEventData> stockData, int i) {
        if(stockData.get(i).getBrandId() !=null){
            return stockData.get(i).getBrandId();
        }
        int brandId = AppConstants.CHAAYOS_BRAND_ID;
        try {
            brandId = cache.getProduct(stockData.get(i).getProductId()).getBrandId();
        } catch (Exception e) {
            LOG.error("Brand Not Found :: {}", stockData.get(i).getProductId());
        }
        return brandId;
    }

    private String getDimension(List<UnitProductsStockEventData> stockData, int i) {
        if(stockData.get(i).getDimension() !=null){
            return stockData.get(i).getDimension();
        }
        String dimensionName = "None";
        try {
            dimensionName = (cache.getDimensionProfile(cache.getProduct(stockData.get(i).getProductId()).getDimensionProfileId()).getContent() != null &&
                    !cache.getDimensionProfile(cache.getProduct(stockData.get(i).getProductId()).getDimensionProfileId()).getContent().isEmpty()) ?
                    cache.getDimensionProfile(cache.getProduct(stockData.get(i).getProductId()).getDimensionProfileId()).getContent().get(0).getCode() : "None";
        } catch (Exception e) {
            LOG.error("Dimension Not Found {}", stockData.get(i).getProductId());
        }
        stockData.get(i).setDimension(dimensionName);
        return dimensionName;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public View executeForDownload(Date startDate, Date endDate, List<Integer> unitId, Date calculationDate,
                                   boolean saveResults) {
        try {
            List<UnitProductStockData> unitProductsStockData = new ArrayList<>();

            List<UnitProductStockEventAggregate> unitProductStockEventAggregates = new ArrayList<>();
            Map<Integer, List<Date>> unitTime = partnerStockDao.getUnitClosingTimeMap(startDate);
            for (Integer unitID : unitId) {
                LOG.info("Code Execution for " + unitID);

                StockOutReportData stockOutReportData = execute(startDate, unitID, saveResults, unitTime.get(unitID), false,0);
                if (stockOutReportData.getEvents() != null) {
                    unitProductsStockData.addAll(stockOutReportData.getEvents());
                }
                if (stockOutReportData.getAggregate() != null) {
                    unitProductStockEventAggregates.addAll(stockOutReportData.getAggregate());
                }
            }
            StockOutReportData data = new StockOutReportData();
            data.setEvents(unitProductsStockData);
            data.setAggregate(unitProductStockEventAggregates);

            LOG.info("Creating Excel for downloading");
            LOG.info("unitProductsStockData List Size - " + unitProductsStockData.size());
            LOG.info("unitProductStockEventAggregates List Size - " + unitProductStockEventAggregates.size());
            return new RenderExcelView(unitProductsStockData, unitProductStockEventAggregates);
        } catch (Exception e) {
            LOG.error("Exception Faced :", e);
        }
        return null;
    }

    @Override
    public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate) {
        return partnerStockDao.getUnitClosingTimeMap(previousDate);
    }


    private static class RenderExcelView extends AbstractXlsxView {

        List<UnitProductStockData> unitProductsStockData;
        List<UnitProductStockEventAggregate> unitProductStockEventAggregates;

        public RenderExcelView(List<UnitProductStockData> unitProductsStockData,
                               List<UnitProductStockEventAggregate> unitProductStockEventAggregates) {
            super();
            this.unitProductsStockData = unitProductsStockData;
            this.unitProductStockEventAggregates = unitProductStockEventAggregates;
        }

        @Override
        protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                          HttpServletResponse response) throws Exception {
            try {
                LOG.info("Inside Building The excel");
                response.addHeader("Content-Disposition", "attachment; filename=\"StockReportSheet.xlsx\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitProductStockEventAggregate> l = new ArrayList<>();
                Integer downTimeAggr = 0;
                Integer oprationTimeAggr = 0;
                List<UnitProductStockData> s = new ArrayList<>();
                for (UnitProductStockData stock : unitProductsStockData) {
                    s.add(stock);
                }
                writer.writeSheet(s, UnitProductStockData.class);
                for (UnitProductStockEventAggregate aggregate : unitProductStockEventAggregates) {
                    l.add(aggregate);
                    downTimeAggr = downTimeAggr + aggregate.getDownTime();
                    oprationTimeAggr = oprationTimeAggr + aggregate.getOperationTime();
                }
                l.add(new UnitProductStockEventAggregate(new Date(), downTimeAggr, oprationTimeAggr));
                writer.writeSheet(l, UnitProductStockEventAggregate.class);
                LOG.info("Completed Building The excel");
            } catch (Exception e) {
                LOG.error("Error in building excel", e);
            }
        }
    }
}
