/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.DeliveryConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.adapter.HLDRequestAdapter;
import com.stpl.tech.kettle.delivery.adapter.HLDResponseAdapter;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.HLDErrorResponse;
import com.stpl.tech.kettle.delivery.model.HLDGenericRequest;
import com.stpl.tech.kettle.delivery.model.HLDResponse;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Unit;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;

public class HLDExecutor extends AbstractDeliveryStrategy implements DeliveryExecutionStrategy {
	private static final Logger LOG = LoggerFactory.getLogger(HLDExecutor.class);

	private HLDGenericRequest request = null;
	private HLDRequestAdapter requestAdapter = new HLDRequestAdapter();
	private HLDResponse response = new HLDResponse();
	private HLDErrorResponse error = new HLDErrorResponse();
	private HLDResponseAdapter responseAdapter = new HLDResponseAdapter();

	@Override
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props) {
		request = requestAdapter.adaptCreate(order);

		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			LOG.info("environment type is {}", props.getEnvironmentType());
			creationEndpoint = HLDEnv.TEST.getValue() + creationEndpoint;
		} else {
			creationEndpoint = HLDEnv.PRODUCTION.getValue() + creationEndpoint;
		}

		return readResponse(createRequest(creationEndpoint, request), order.getOrder().getGenerateOrderId());
	}

	@Override
	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo,
										   EnvironmentProperties props) {
		request = requestAdapter.adaptCancel(orderInfo,taskId);

		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			LOG.info("environment type is {}", props.getEnvironmentType());
			cancelationEndpoint = HLDEnv.TEST.getValue() + cancelationEndpoint;
		} else {
			cancelationEndpoint = HLDEnv.PRODUCTION.getValue() + cancelationEndpoint;
		}

		cancelationEndpoint = replaceInEndPoint(cancelationEndpoint, "<order_id>", orderInfo.toString());
		LOG.info("cancelltaion endpoint is :::: {}", cancelationEndpoint);
		return readResponse(createRequest(cancelationEndpoint, request), orderInfo.getOrder().getGenerateOrderId());
	}

	@Override
	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public HttpUriRequest addHeaders(HttpUriRequest requestObject, AuthorizationObject token) {
		requestObject = super.addHeaders(requestObject, token);
		requestObject.addHeader(WebServiceHelper.ACCEPTS, DeliveryConstants.ACCEPTS);
		return requestObject;
	}

	@Override
	public void setAuthorizationObject(AuthorizationObject authorization) {
		this.authorization = authorization;
	}

	@Override
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId) {
		try {
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()) {
				response = WebServiceHelper.convertResponse(responseFromRequest, HLDResponse.class);
				return responseAdapter.adapt(response, orderId);
			} else {
				error = WebServiceHelper.convertResponse(responseFromRequest, HLDErrorResponse.class);
				return responseAdapter.adaptError(error, orderId);
			}
		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}
		return null;
	}

}
