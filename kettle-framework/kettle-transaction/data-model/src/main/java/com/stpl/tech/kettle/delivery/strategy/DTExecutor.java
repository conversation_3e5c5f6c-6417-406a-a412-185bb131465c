/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.util.RequestSigner;
import com.stpl.tech.kettle.delivery.adapter.DTRequestAdapter;
import com.stpl.tech.kettle.delivery.adapter.DTResponseAdapter;
import com.stpl.tech.kettle.delivery.model.*;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;

public class DTExecutor extends AbstractDeliveryStrategy implements DeliveryExecutionStrategy{

	private static Logger LOG = LoggerFactory.getLogger(DTExecutor.class);
	private static final String SUCCESS = "success";

	private DTRequest requestObject;
	private DTRequestAdapter requestAdapter = new DTRequestAdapter();

	private DTOrderResponse responseObject;
	private DTResponseAdapter responseAdapter = new DTResponseAdapter();

	private String getHost(EnvironmentProperties props) {
		return TransactionUtils.isDev(props.getEnvironmentType())
				? DTEnv.TEST.getValue()
				: DTEnv.PRODUCTION.getValue();
	}

	@Override
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props) {
		requestObject = requestAdapter.adaptCreate(order);
		String restaurantId = "57ce389ae2243"; // test restaurantId
		if(!props.getEnvironmentType().equals(EnvType.DEV)){
			restaurantId = getAuthorization().getToken();
		}
		requestObject.assignRestaurantId(restaurantId);
		return readResponse(createRequest(getHost(props)+creationEndpoint, requestObject), order.getOrder().getGenerateOrderId());
	}

	@Override
	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo,
										   EnvironmentProperties props) {
		requestObject = requestAdapter.adaptCancel(orderInfo,taskId);
		requestObject.assignRestaurantId(authorization.getToken()); // need restaurantid for authentication
		LOG.info("Cancellation endpoint is :::: {}", cancelationEndpoint);
		return readResponse(createRequest(getHost(props)+cancelationEndpoint, requestObject), orderInfo.getOrder().getGenerateOrderId());
	}

	@Override
	public void setAuthorizationObject(AuthorizationObject authorization) {
		this.authorization = authorization;
	}

	@Override
	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props) {
		return null;
	}


	@Override
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId) {
		try {
			responseObject = WebServiceHelper.convertResponse(responseFromRequest, DTOrderResponse.class);
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.MULTIPLE_CHOICES.value()
					&& responseObject.getResult().equalsIgnoreCase(SUCCESS)){

					return responseAdapter.adapt(responseObject, orderId);
			}else{
				return responseAdapter.adaptError(responseObject, orderId);
			}

		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}
		return null;
	}

	public HttpResponse createRequest(String creationEndpoint, DeliveryRequest request) {
		HttpPost requestObject = new HttpPost(creationEndpoint);
		requestObject.addHeader(WebServiceHelper.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType());
		String canonicalQS = null;
		try {
			canonicalQS = RequestSigner.paramterizeValues(request);
			StringEntity orderJson = new StringEntity(canonicalQS, ContentType.APPLICATION_FORM_URLENCODED);
			LOG.info("canonicalQS is ::::::::::::::::::::::::::: {}", canonicalQS.toString());
			requestObject.setEntity(orderJson);
			LOG.info("inside createRequest of Abstract Strategy before sending request");
			HttpResponse responseFromRequest = WebServiceHelper.postRequest(requestObject);
			LOG.info("HttpResponse after request sent ::::: {} :::: {}",
					responseFromRequest.getStatusLine().getStatusCode(),
					responseFromRequest.getStatusLine().getReasonPhrase());

			return responseFromRequest;
		} catch (JsonProcessingException e) {
			LOG.error("Unable to parse json :::", e);
		} catch (ClientProtocolException e) {
			LOG.error("Invalid client protocol :::", e);
		} catch (IOException e) {
			LOG.error("IO Exception encountered :::", e);
		}
		return null;
	}

}
