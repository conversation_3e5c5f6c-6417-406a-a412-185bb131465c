package com.stpl.tech.kettle.google.service;

import com.stpl.tech.kettle.google.domain.model.GoogleCustomerOrderData;

import java.util.Date;
import java.util.List;

public interface GoogleDataPushService {

    void uploadGoogleOfflineConversionDataBulk(Date businessDate, Integer brandId, String brandCustomerId);

    void uploadDataForConversionsForLeads(Date businessDate, Integer brandId, String brandCustomerId);
}