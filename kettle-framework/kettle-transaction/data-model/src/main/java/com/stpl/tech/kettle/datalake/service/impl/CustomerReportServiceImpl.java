package com.stpl.tech.kettle.datalake.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.data.vo.CustomerFeedbackData;
import com.stpl.tech.kettle.core.data.vo.CustomerNpsData;
import com.stpl.tech.kettle.core.data.vo.FeedbackCategory;
import com.stpl.tech.kettle.core.data.vo.NPSCategory;
import com.stpl.tech.kettle.datalake.dao.CustomerReportDao;
import com.stpl.tech.kettle.datalake.model.CustomerNPSFeedbackStats;
import com.stpl.tech.kettle.datalake.model.CustomerOrderFeedbackStats;
import com.stpl.tech.kettle.datalake.service.CustomerReportService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.AppConstants;

@Service
public class CustomerReportServiceImpl implements CustomerReportService {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerReportServiceImpl.class);
	@Autowired
	private CustomerReportDao dao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "DataLakeDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void calculateNpsForBusinessDate(Date previousBusinessDate) {
		dao.calculateNpsForBusinessDate(previousBusinessDate, previousBusinessDate);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "DataLakeDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void calculateCustomerOneViewFeedbackStats(Date previousBusinessDate) {
        dao.calculateCustomerOneViewFeedbackStats(previousBusinessDate, previousBusinessDate);
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "DataLakeDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void calculateFeedbackForBusinessDate(Date previousBusinessDate) {
		dao.calculateFeedbackForBusinessDate(previousBusinessDate, previousBusinessDate);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "DataLakeDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void calculateCustomerOneViewOrderFeedbackStats(Date previousBusinessDate) {
        dao.calculateCustomerOneViewOrderFeedbackStats(previousBusinessDate, previousBusinessDate);
    }

    
    @Override
    @Transactional(rollbackFor = Exception.class, value = "DataLakeDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerNpsData getNpsForCustomerDetail(Integer customerId) throws DataNotFoundException {
        CustomerNpsData customerNpsData = new CustomerNpsData();
        CustomerNPSFeedbackStats customerNPSFeedbackStats = dao.getNpsForCustomerDetail(customerId);
        if(customerNPSFeedbackStats == null){
            return customerNpsData;
        }
        customerNpsData.setTotalNps(customerNPSFeedbackStats.getTotalNps());
        customerNpsData.setLastNps(customerNPSFeedbackStats.getLastNpsScore());
        customerNpsData.setSecondNps(customerNPSFeedbackStats.getSecondLastNpsScore());
        customerNpsData.setThirdNps(customerNPSFeedbackStats.getThirdLastNpsScore());
        customerNpsData.setCustomerId(customerNPSFeedbackStats.getCustomerId());
        customerNpsData.setLastFeedbackOfLastOrder(customerNPSFeedbackStats.getIsLastFeedbackOfLastOrder());
        if (customerNpsData.getLastNps().compareTo(AppConstants.PROMOTER) >= 0) {
            customerNpsData.setNpsCategory(NPSCategory.PROMOTER.getKey());
            customerNpsData.setResult(NPSCategory.PROMOTER.getValue());
        } else if (customerNpsData.getLastNps().compareTo(AppConstants.PASSIVE) > 0 &&
            customerNpsData.getLastNps().compareTo(AppConstants.PROMOTER) < 0) {
            customerNpsData.setNpsCategory(NPSCategory.PASSIVE.getKey());
            customerNpsData.setResult(NPSCategory.PASSIVE.getValue());
        } else {
            customerNpsData.setNpsCategory(NPSCategory.DETRACTOR.getKey());
            customerNpsData.setResult(NPSCategory.DETRACTOR.getValue());
        }
        LOG.info("returning NPS feedback data ");
        return customerNpsData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "DataLakeDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerFeedbackData getFeedbackForCustomerDetail(Integer customerId) throws DataNotFoundException {
    	CustomerFeedbackData customerNpsData = new CustomerFeedbackData();
        CustomerOrderFeedbackStats customerNPSFeedbackStats = dao.getFeedbackForCustomerDetail(customerId);
        if(customerNPSFeedbackStats == null){
            return customerNpsData;
        }
        customerNpsData.setTotalFeedback(customerNPSFeedbackStats.getTotalOrderFeedback());
        customerNpsData.setLastFeedback(customerNPSFeedbackStats.getLastOrderScore());
        customerNpsData.setSecondFeedback(customerNPSFeedbackStats.getSecondLastOrderScore());
        customerNpsData.setThirdFeedback(customerNPSFeedbackStats.getThirdLastOrderScore());
        customerNpsData.setCustomerId(customerNPSFeedbackStats.getCustomerId());
        customerNpsData.setLastFeedbackOfLastOrder(customerNPSFeedbackStats.getIsLastFeedbackOfLastOrder());
        if (customerNpsData.getLastFeedback().compareTo(AppConstants.FEEDBACK_PROMOTER) >= 0) {
            customerNpsData.setFeedbackCategory(FeedbackCategory.PROMOTER.getKey());
            customerNpsData.setResult(FeedbackCategory.PROMOTER.getValue());
        } else if (customerNpsData.getLastFeedback().compareTo(AppConstants.FEEDBACK_PASSIVE) > 0 &&
            customerNpsData.getLastFeedback().compareTo(AppConstants.FEEDBACK_PROMOTER) < 0) {
            customerNpsData.setFeedbackCategory(FeedbackCategory.PASSIVE.getKey());
            customerNpsData.setResult(FeedbackCategory.PASSIVE.getValue());
        } else {
            customerNpsData.setFeedbackCategory(FeedbackCategory.DETRACTOR.getKey());
            customerNpsData.setResult(FeedbackCategory.DETRACTOR.getValue());
        }
        LOG.info("returning Order feedback data ");
        return customerNpsData;
    }

    
    @Override
    @Transactional(rollbackFor =  Exception.class,value = "DataLakeDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> customerRecommendedProduct(Integer customerId) {
        List<Integer> recommendedList=dao.customerRecommendedProduct(customerId);
        List<Integer> finalList=new ArrayList<>();
        for(Integer data :recommendedList){
            if(AppConstants.BAARISH_CHAI_ID.contains(data)){
                finalList.add(AppConstants.BAARISH_CHAI_ID.get(0));
            }else
            if(AppConstants.DESI_CHAI_ID.contains(data)){
                finalList.add(AppConstants.DESI_CHAI_ID.get(0));
            }else {
                finalList.add(data);
            }
        }
        LOG.info("data after {}",finalList);
        return finalList;
    }



}
