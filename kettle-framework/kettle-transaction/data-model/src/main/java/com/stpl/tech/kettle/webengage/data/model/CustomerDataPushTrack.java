package com.stpl.tech.kettle.webengage.data.model;

import javax.persistence.*;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Entity
@Table(name = "CUSTOMER_DATA_PUSH_TRACK")
public class CustomerDataPushTrack {

    private Integer id;
    private Integer customerId;
    private String status;

    public CustomerDataPushTrack(){};

    public CustomerDataPushTrack(Integer id, Integer customerId, String status) {
        this.id = id;
        this.customerId = customerId;
        this.status = status;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CUSTOMER_ID", nullable = false)
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
