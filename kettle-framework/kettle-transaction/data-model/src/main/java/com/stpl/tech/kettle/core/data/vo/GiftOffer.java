/**
 *
 */
package com.stpl.tech.kettle.core.data.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class GiftOffer implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 8801526727443326762L;
	private BigDecimal denomination;
	private BigDecimal offer;
	private BigDecimal suggestWalletOffer;
	private Date endDate;

	public BigDecimal getDenomination() {
		return denomination;
	}

	public void setDenomination(BigDecimal denomination) {
		this.denomination = denomination;
	}

	public BigDecimal getOffer() {
		return offer;
	}

	public void setOffer(BigDecimal offer) {
		this.offer = offer;
	}

	public BigDecimal getSuggestWalletOffer() {
		return suggestWalletOffer;
	}

	public void setSuggestWalletOffer(BigDecimal suggestWalletOffer) {
		this.suggestWalletOffer = suggestWalletOffer;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

}
