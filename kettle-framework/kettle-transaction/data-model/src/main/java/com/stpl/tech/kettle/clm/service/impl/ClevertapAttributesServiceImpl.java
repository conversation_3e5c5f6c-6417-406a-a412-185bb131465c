package com.stpl.tech.kettle.clm.service.impl;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.clm.dao.CLMDao;
import com.stpl.tech.kettle.clm.dao.ClevertapAttributesDao;
import com.stpl.tech.kettle.clm.service.ClevertapAttributesService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;

@Service("clevertapAttributesService")
public class ClevertapAttributesServiceImpl implements ClevertapAttributesService {

	@Autowired
	private ClevertapAttributesDao clevertapAttributesDao;
	
	@Autowired
	private CLMDao clmDao;
	
	@Autowired
	private EnvironmentProperties environmentProperties;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getProfileAttributes(Customer customer) {
		return clevertapAttributesDao.calculateUserAttributes(customer);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getEventAttributes(OrderDetail order, CustomerInfo customer) {
		return clevertapAttributesDao.calculateEventAttributes(order, customer);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getSubscriptionAttributes(OrderDetail order, CustomerInfo customer) {
		return clevertapAttributesDao.calculateSubscriptionAttributes(order, customer);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public int updateCommunicationAttributes(String optInWhatsapp, String optInSms, int customerId) {
		EnvType type = environmentProperties.getEnvironmentType();
		if (type.equals(EnvType.PROD) || type.equals(EnvType.SPROD)) {
			if (StringUtils.isBlank(optInSms)) {
				optInSms = AppConstants.YES;
			}
			if (StringUtils.isBlank(optInWhatsapp)) {
				optInWhatsapp = AppConstants.YES;
			}
			//return clmDao.updateCommunicationAttributes(optInWhatsapp, optInSms, customerId); REMOVED DUMP DEPENDENCY
			return 0;
		}
		return 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getProfileAttributes(Integer customerId){
		return clevertapAttributesDao.calculateUserAttributes(customerId);
	}

}
