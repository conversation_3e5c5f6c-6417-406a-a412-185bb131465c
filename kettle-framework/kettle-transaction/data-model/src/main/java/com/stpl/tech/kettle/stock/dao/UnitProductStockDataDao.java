package com.stpl.tech.kettle.stock.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventDataDetail;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface UnitProductStockDataDao extends AbstractDao {

	public void deleteData(Date calculationDate, Integer unitId);

	List<UnitProductStockEventDataDetail> findAllByUnitIdAndEventTimeStampOrderByProductIdAsc(Integer unitId, Date previousDate);

	Map<String, PartnerUnitProductStockData> getPreviousDayStockData(int unit, Date businessDate);

	public Map<Integer, List<Date>> getUnitClosingTimeMap(Date previousDate);


}

