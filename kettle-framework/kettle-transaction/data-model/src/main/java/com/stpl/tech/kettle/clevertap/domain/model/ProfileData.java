package com.stpl.tech.kettle.clevertap.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.TimeZone;

public class ProfileData {


    @JsonProperty("Name")
    private String name;
    @JsonProperty("Email")
    private String email;
    @JsonProperty("Phone")
    private String phone;
    @JsonProperty("Gender")
    private String gender;
    private TimeZone tz;
    private String customerId;
    private String dob;
    private String referral;
    private boolean availedSignupOffer;
    private String cardAmount;
    private String chaayosCash;
    private String hasCard;
    private String isVeg;
    private String itp;
    private String lastOrderId;
    private String lastOrderItem;
    private String loyaltyPoints;
    private String offerAvailed;
    private String orderCount;
    private String refCode;
    private boolean signupOfferAvailed;
    private boolean signupOfferExpired;
    private String signupOfferExpiryTime;
    private String tags;
    private String city;
    private String acceptsMarketing;
    private String hasAccount;
    private String ordersCount;
    private String taxExempt;
    private String totalSpent;
    private String shopifyId;
    private String firstName;
    private String lastName;
    private String customerAppId;
    private String lastUpdateTime;


    @JsonProperty("Name")
    public String getName() {
        return name;
    }

    @JsonProperty("Name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("Email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("Email")
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("Phone")
    public String getPhone() {
        return phone;
    }

    @JsonProperty("Phone")
    public void setPhone(String phone) {
        this.phone = phone;
    }

    @JsonProperty("Gender")
    public String getGender() {
        return gender;
    }

    @JsonProperty("Gender")
    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getDob() {
        return dob;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public String getReferral() {
        return referral;
    }

    public void setReferral(String referral) {
        this.referral = referral;
    }

    public boolean getAvailedSignupOffer() {
        return availedSignupOffer;
    }

    public void setAvailedSignupOffer(boolean availedSignupOffer) {
        this.availedSignupOffer = availedSignupOffer;
    }

    public String getCardAmount() {
        return cardAmount;
    }

    public void setCardAmount(String cardAmount) {
        this.cardAmount = cardAmount;
    }

    public String getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(String chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public String getHasCard() {
        return hasCard;
    }

    public void setHasCard(String hasCard) {
        this.hasCard = hasCard;
    }

    public String getIsVeg() {
        return isVeg;
    }

    public void setIsVeg(String isVeg) {
        this.isVeg = isVeg;
    }

    public String getItp() {
        return itp;
    }

    public void setItp(String itp) {
        this.itp = itp;
    }

    public String getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(String lastOrderId) {
        this.lastOrderId = lastOrderId;
    }

    public String getLastOrderItem() {
        return lastOrderItem;
    }

    public void setLastOrderItem(String lastOrderItem) {
        this.lastOrderItem = lastOrderItem;
    }

    public String getLoyaltyPoints() {
        return loyaltyPoints;
    }

    public void setLoyaltyPoints(String loyaltyPoints) {
        this.loyaltyPoints = loyaltyPoints;
    }

    public String getOfferAvailed() {
        return offerAvailed;
    }

    public void setOfferAvailed(String offerAvailed) {
        this.offerAvailed = offerAvailed;
    }

    public String getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(String orderCount) {
        this.orderCount = orderCount;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public boolean getSignupOfferAvailed() {
        return signupOfferAvailed;
    }

    public void setSignupOfferAvailed(boolean signupOfferAvailed) {
        this.signupOfferAvailed = signupOfferAvailed;
    }

    public boolean getSignupOfferExpired() {
        return signupOfferExpired;
    }

    public void setSignupOfferExpired(boolean signupOfferExpired) {
        this.signupOfferExpired = signupOfferExpired;
    }

    public String getSignupOfferExpiryTime() {
        return signupOfferExpiryTime;
    }

    public void setSignupOfferExpiryTime(String signupOfferExpiryTime) {
        this.signupOfferExpiryTime = signupOfferExpiryTime;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAcceptsMarketing() {
        return acceptsMarketing;
    }

    public void setAcceptsMarketing(String acceptsMarketing) {
        this.acceptsMarketing = acceptsMarketing;
    }

    public String getHasAccount() {
        return hasAccount;
    }

    public void setHasAccount(String hasAccount) {
        this.hasAccount = hasAccount;
    }

    public String getOrdersCount() {
        return ordersCount;
    }

    public void setOrdersCount(String ordersCount) {
        this.ordersCount = ordersCount;
    }

    public String getTaxExempt() {
        return taxExempt;
    }

    public void setTaxExempt(String taxExempt) {
        this.taxExempt = taxExempt;
    }

    public String getTotalSpent() {
        return totalSpent;
    }

    public void setTotalSpent(String totalSpent) {
        this.totalSpent = totalSpent;
    }

    public String getShopifyId() {
        return shopifyId;
    }

    public void setShopifyId(String shopifyId) {
        this.shopifyId = shopifyId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCustomerAppId() {
        return customerAppId;
    }

    public void setCustomerAppId(String customerAppId) {
        this.customerAppId = customerAppId;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public TimeZone getTz() {
        return tz;
    }

    public void setTz(TimeZone tz) {
        this.tz = tz;
    }

}
