package com.stpl.tech.kettle.customer.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.jms.JMSException;

import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.referral.model.ReferentInfo;
import com.stpl.tech.kettle.referral.model.ReferralRequest;
import com.stpl.tech.kettle.referral.model.ReferralResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;

public interface ReferralService {

	public ReferralResponse submitReferralByCode(ReferralRequest request, boolean isValidation);

	public Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> activateCashPackets();

	public void expireCashPackets(int limit);

	public void generateRefCodes();

	public void sendReferralSMS(List<String> contactNumbers, String campaign, String source);

	public void notifyRefSuccess(Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> customerMap);

	Long countExpiringPackets();

	List<ReferentInfo> getCustomerReferents(int customerId);

	boolean allotChaayosCash(int customerId, int validityInDays, int lagDays, BigDecimal amount, Date creationDate,
			Date expirationDate, String cashMetadataType, String cashTransactionCode, boolean sendNotification,
			boolean publishEvent, String comment)
			throws DataNotFoundException, IOException, JMSException, CardValidationException;

	}
