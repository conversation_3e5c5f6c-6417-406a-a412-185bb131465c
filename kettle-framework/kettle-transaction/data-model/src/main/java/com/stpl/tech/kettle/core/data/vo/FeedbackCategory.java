package com.stpl.tech.kettle.core.data.vo;

public enum FeedbackCategory {
    PROMOTER("Promoter","Personalise conversation with customer, let’s get 5/5 from this visit"),
    PASSIVE("Passive","Personalise conversation with customer, identify reason for Neutral experience"),
    DETRACTOR("Detractor","Handle with care, identify last order to understand reason for unpleasant experience");

    private final String key;
    private final String value;

    FeedbackCategory(String key, String value) {
        this.key = key;
        this.value = value;
    }
    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }


}
