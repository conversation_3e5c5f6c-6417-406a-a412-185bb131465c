/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.dao.CashManagementDao;
import com.stpl.tech.kettle.data.model.ClosurePaymentDetails;
import com.stpl.tech.kettle.data.model.ClosurePaymentTaxDetails;
import com.stpl.tech.kettle.data.model.PullDenomination;
import com.stpl.tech.kettle.data.model.PullDetail;
import com.stpl.tech.kettle.data.model.SettlementDenomination;
import com.stpl.tech.kettle.data.model.SettlementDetail;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PullPacket;
import com.stpl.tech.kettle.domain.model.PullPacketDenomination;
import com.stpl.tech.kettle.domain.model.PullPacketStatus;
import com.stpl.tech.kettle.domain.model.PullSettlementDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDetail;
import com.stpl.tech.kettle.domain.model.SettlementStatus;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Repository
public class CashManagementDaoImpl extends AbstractDaoImpl implements CashManagementDao {

	@Override
	public List<PullDetail> getOpenPullsForUnit(Integer unitId, Integer paymentModeId, Integer resultCount,
			List<String> statusList) {
		Query query;
		String queryString = "FROM PullDetail E where E.unitId = :unitId ";
		if (paymentModeId != null && paymentModeId != 0) {
			queryString += "AND E.paymentModeId = :paymentModeId ";
		}
		if (statusList != null && statusList.size() > 0) {
			queryString += "AND E.status IN :statusList ";
		}
		query = manager.createQuery(queryString);
		query.setParameter("unitId", unitId);
		if (paymentModeId != null && paymentModeId != 0) {
			query.setParameter("paymentModeId", paymentModeId);
		}
		if (statusList != null && statusList.size() > 0) {
			query.setParameter("statusList", statusList);
		}
		if (resultCount != null && resultCount != 0) {
			query.setMaxResults(resultCount);
		}
		@SuppressWarnings("unchecked")
		List<PullDetail> pullDetails = query.getResultList();
		return pullDetails;
	}

	@Override
	public List<PullDetail> getUnitPullsForTransfer(Integer unitId) {
		String[] statuses = { "INITIATED", "CREATED" };
		Query query = manager.createQuery("FROM PullDetail E where E.unitId = :unitId AND E.status IN :pullStatus");
		query.setParameter("unitId", unitId);
		query.setParameter("pullStatus", statuses);
		@SuppressWarnings("unchecked")
		List<PullDetail> pullDetails = query.getResultList();
		return pullDetails;
	}

	@Override
	public PullDetail submitPull(PaymentMode mode, PullPacket pullPacket) {
		PullDetail pullDetail = manager.find(PullDetail.class, pullPacket.getPullPacketId());
		return submitPull(pullPacket.getPullPacketUnit(), mode, pullDetail, pullPacket.getComment(),
				pullPacket.getCreatedBy(), pullPacket.getWitnessedBy(), pullPacket.getPullDenominations());
	}

	private PullDetail submitPull(UnitBasicDetail unit, PaymentMode mode, PullDetail pullDetail, String comment,
			int createdBy, String withnessedBy, List<PullPacketDenomination> pullDenominations) {
		if (pullDetail != null) {
			pullDetail.setComment(comment);
			pullDetail.setCreatedBy(createdBy);
			pullDetail.setStatus(PullPacketStatus.CREATED.value());
			pullDetail.setWitnessedBy(withnessedBy);
			if (pullDenominations != null) {
				pullDetail.setPullDenominations(convertDenominations(pullDetail, pullDenominations));
			}
			pullDetail = manager.merge(pullDetail);
			manager.flush();
			if (mode.isAutoTransfer()) {
				PullSettlementDetail pullSettlementDetail = createPullSettlementDetail(unit, mode, pullDetail);
				transferPull(pullSettlementDetail);
			}
		}
		return pullDetail;
	}

	private PullSettlementDetail createPullSettlementDetail(UnitBasicDetail unit, PaymentMode mode,
			PullDetail pullDetail) {
		PullSettlementDetail settlementDetail = new PullSettlementDetail();
		settlementDetail.setClosingAmount(pullDetail.getPullAmount());
		settlementDetail.setExtraAmount(BigDecimal.ZERO);
		settlementDetail.setOriginalAmount(pullDetail.getPullAmount());
		settlementDetail.setSettlementAmount(pullDetail.getPullAmount());
		settlementDetail.setSettlementType(mode);
		settlementDetail.setTotalAmount(pullDetail.getPullAmount());
		settlementDetail.setSettlementUnit(unit);
		settlementDetail.setUnsettledAmount(BigDecimal.ZERO);
		settlementDetail.setPaymentMode(mode);
		PullPacket packet = new PullPacket();
		packet.setPullPacketId(pullDetail.getId());
		settlementDetail.getPullDetails().add(packet);
		return settlementDetail;
	}

	@Override
	public PullSettlementDetail transferPull(PullSettlementDetail pullSettlementDetail) {
		SettlementDetail sdetail = new SettlementDetail();
		sdetail.setClosingAmount(pullSettlementDetail.getClosingAmount());
		sdetail.setSettlementAmount(pullSettlementDetail.getSettlementAmount());
		sdetail.setExtraAmount(pullSettlementDetail.getExtraAmount());
		sdetail.setOriginalAmount(pullSettlementDetail.getOriginalAmount());
		sdetail.setSettlementClosingReceipt(pullSettlementDetail.getSettlementClosingReceipt());
		sdetail.setSettlementReceiptPath(pullSettlementDetail.getSettlementReceiptPath());
		sdetail.setSettlementServiceProvider(pullSettlementDetail.getSettlementServiceProvider());
		sdetail.setSettlementServiceProviderReceipt(pullSettlementDetail.getSettlementServiceProviderReceipt());
		sdetail.setSettlementTypeId(pullSettlementDetail.getSettlementType().getId());
		sdetail.setTime(AppUtils.getCurrentTimestamp());
		sdetail.setSettlementStatus(SettlementStatus.UNSETTLED.name());
		sdetail.setTotalAmount(pullSettlementDetail.getTotalAmount());
		sdetail.setUnitId(pullSettlementDetail.getSettlementUnit().getId());
		sdetail.setUnsettledAmount(pullSettlementDetail.getUnsettledAmount());
		sdetail.setSettlementDate(pullSettlementDetail.getSettlementDate());
		sdetail.setSerialNumber(pullSettlementDetail.getSerialNumber());
		sdetail.setTicketNumber(pullSettlementDetail.getTicketNumber());
		sdetail.setSlipNumber(pullSettlementDetail.getSlipNumber());
		manager.persist(sdetail);
		pullSettlementDetail.setId(sdetail.getId());
		saveSettlementDenominations(pullSettlementDetail);
		updatePullPackets(pullSettlementDetail);
		if (pullSettlementDetail.getSettlementType().isAutoCloseTransfer()) {
			closePullSettlement(pullSettlementDetail);
		}
		return pullSettlementDetail;
	}

	@Override
	public List<SettlementDetail> getOpenPullSettlements(Integer unitId, Integer settlementId) {
		StringBuffer queryStr = new StringBuffer(
		        "FROM SettlementDetail E where E.unitId = :unitId AND E.settlementStatus = :settlementStatus");
		if(settlementId != null && settlementId > 0){
		    queryStr.append(" AND E.id = :id");
        }
	    Query query = manager.createQuery(queryStr.toString());
		query.setParameter("unitId", unitId);
        if(settlementId != null && settlementId > 0){
            query.setParameter("id", settlementId);
        }
		query.setParameter("settlementStatus", SettlementStatus.UNSETTLED.value());
		@SuppressWarnings("unchecked")
		List<SettlementDetail> settlementDetails = query.getResultList();
		return settlementDetails;
	}

	@Override
	public List<SettlementDetail> getOpenPullSettlementsByType(Integer settlementTypeId) {
		Query query = manager.createQuery(
				"FROM SettlementDetail E where E.settlementTypeId = :settlementTypeId AND E.settlementStatus = :settlementStatus");
		query.setParameter("settlementTypeId", settlementTypeId);
		query.setParameter("settlementStatus", SettlementStatus.UNSETTLED.value());
		@SuppressWarnings("unchecked")
		List<SettlementDetail> settlementDetails = query.getResultList();
		return settlementDetails;
	}

	@Override
	public List<SettlementDetail> getPullSettlements(Date startDate, Date endDate, int unitId,int start,int batchSize) {
		Query query = manager.createQuery(
				"FROM SettlementDetail E where E.unitId = :unitId AND E.time >= :startDate AND E.time <= :endDate ORDER BY 1 DESC");
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		query.setParameter("unitId", unitId);
		query.setFirstResult((start-1) * batchSize);
		query.setMaxResults(batchSize);
		@SuppressWarnings("unchecked")
		List<SettlementDetail> settlementDetails = query.getResultList();
		return settlementDetails;
	}

	@Override
	public List<SettlementDetail> getPullSettlementsByType(Date startDate, Date endDate, int settlementTypeId,int start,int batchSize) {
		Query query = manager.createQuery(
				"FROM SettlementDetail E where E.settlementTypeId = :settlementTypeId AND E.time >= :startDate AND E.time <= :endDate order by id desc");
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		query.setParameter("settlementTypeId", settlementTypeId);
		query.setFirstResult((start-1) * batchSize);
		query.setMaxResults(batchSize);
		@SuppressWarnings("unchecked")
		List<SettlementDetail> settlementDetails = query.getResultList();
		return settlementDetails;
	}

	private List<PullDenomination> convertDenominations(PullDetail pullDetail,
			List<PullPacketDenomination> pullPacketDenominations) {
		List<PullDenomination> denominationList = new ArrayList<>();
		for (PullPacketDenomination denom : pullPacketDenominations) {
			PullDenomination pullDenomination = new PullDenomination();
			pullDenomination.setDenominationId(denom.getDenominationDetail().getDenominationId());
			pullDenomination.setLooseCurrencyCount(denom.getLooseCurrencyCount());
			pullDenomination.setPacketCount(denom.getPacketCount());
			pullDenomination.setPullDetail(pullDetail);
			pullDenomination.setTotalAmount(denom.getTotalAmount());
			manager.persist(pullDenomination);
			denominationList.add(pullDenomination);
		}
		manager.flush();
		return denominationList;
	}

	private void saveSettlementDenominations(PullSettlementDetail settlementDetail) {
		if (settlementDetail.getSettlementDenominations() == null) {
			return;
		}

		for (PullSettlementDenomination denom : settlementDetail.getSettlementDenominations()) {
			SettlementDenomination sdenom = new SettlementDenomination();
			sdenom.setDenominationId(denom.getDenominationDetail().getDenominationId());
			sdenom.setLooseCurrencyCount(denom.getLooseCurrencyCount());
			sdenom.setPacketCount(denom.getPacketCount());
			sdenom.setTotalAmount(denom.getTotalAmount());
			sdenom.setSettlementDetail(manager.find(SettlementDetail.class, settlementDetail.getId()));
			manager.persist(sdenom);
		}
		manager.flush();
	}

	private void updatePullPackets(PullSettlementDetail settlementDetail) {
		for (PullPacket packet : settlementDetail.getPullDetails()) {
			PullDetail pull = manager.find(PullDetail.class, packet.getPullPacketId());
			pull.setStatus(PullPacketStatus.TRANSFERRED.value());
			pull.setSettlementDetail(manager.find(SettlementDetail.class, settlementDetail.getId()));
			manager.merge(pull);
		}
		manager.flush();
	}

	@Override
	public List<PullPacketDenomination> getCouponDenominations(int pullId,
			Map<Integer, DenominationDetail> denominations) {
		PullDetail pull = manager.find(PullDetail.class, pullId);
		int startOrderId = pull.getClosurePaymentDetails().getUnitClosureDetails().getStartOrderId();
		int endOrderId = pull.getClosurePaymentDetails().getUnitClosureDetails().getLastOrderId();
		int paymentModeId = pull.getPaymentModeId();
		System.out.println(startOrderId + ":" + endOrderId + ":" + paymentModeId);
		Query query = manager.createQuery(
				"SELECT O.id, O.orderId, O.orderSettlement, O.denominationId, SUM(O.count), SUM(O.totalAmount) FROM OrderPaymentDenominationDetail O"
						+ " where O.orderId > :startOrderId AND O.orderId <= :endOrderId AND O.orderSettlement.orderDetail.unitId = :unitId"
						+ " AND O.orderSettlement.orderDetail.orderStatus <> :orderStatus AND O.orderSettlement.paymentModeId = :paymentModeId"
						+ " GROUP BY O.denominationId ");
		query.setParameter("startOrderId", startOrderId);
		query.setParameter("endOrderId", endOrderId);
		query.setParameter("paymentModeId", paymentModeId);
		query.setParameter("unitId", pull.getUnitId());
		query.setParameter("orderStatus", OrderStatus.CANCELLED.value());
		@SuppressWarnings("unchecked")
		List<Object[]> opdds = query.getResultList();
		List<PullPacketDenomination> ppds = new ArrayList<>();
		for (Object[] row : opdds) {
			PullPacketDenomination ppd = new PullPacketDenomination();
			ppd.setDenominationDetail(denominations.get((Integer) row[3]));
			ppd.setLooseCurrencyCount(Integer.valueOf(row[4].toString()));
			ppd.setPullDenominationId(Integer.valueOf(row[0].toString()));
			ppd.setTotalAmount(new BigDecimal(row[5].toString()));
			ppds.add(ppd);
		}
		return ppds;
	}

	@Override
	public PullSettlementDetail closePullSettlement(PullSettlementDetail pullSettlement) {
		SettlementDetail settlementDetail = manager.find(SettlementDetail.class, pullSettlement.getId());
		settlementDetail.setSettlementAmount(pullSettlement.getSettlementAmount());
		settlementDetail.setExtraAmount(pullSettlement.getExtraAmount());
		settlementDetail.setOriginalAmount(pullSettlement.getOriginalAmount());
		settlementDetail.setClosingAmount(pullSettlement.getClosingAmount());
		settlementDetail.setUnsettledAmount(pullSettlement.getUnsettledAmount());
		settlementDetail.setSettlementStatus(SettlementStatus.SETTLED.value());
		manager.merge(settlementDetail);
		manager.flush();
		pullSettlement.setSettlementStatus(SettlementStatus.SETTLED);
		return pullSettlement;
	}

	@Override
	public PullDetail savePullDetails(UnitBasicDetail unit, PaymentMode mode, ClosurePaymentDetails paymentDetails) {
		PullDetail pullDetail = new PullDetail();
		pullDetail.setClosurePaymentDetails(paymentDetails);
		pullDetail.setCreationTime(AppUtils.getCurrentTimestamp());
		pullDetail.setComment("Component Automatically Created By System");
		pullDetail.setCreatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
		pullDetail.setWitnessedBy(AppConstants.SYSTEM_EMPLOYEE_NAME);
		pullDetail.setPaymentModeId(paymentDetails.getPaymentModeId());
		pullDetail.setPullAmount(paymentDetails.getTotalAmount());
		pullDetail.setPullDate(paymentDetails.getUnitClosureDetails().getBusinessDate());
		pullDetail.setSource("AUTOMATED");
		pullDetail.setUnitId(paymentDetails.getUnitClosureDetails().getUnitId());
		pullDetail.setStatus(PullPacketStatus.INITIATED.value());
		manager.persist(pullDetail);
		manager.flush();
		if (mode.isAutoPullValidate()) {
			submitPull(unit, mode, pullDetail, "Auto Validated By System", AppConstants.SYSTEM_EMPLOYEE_ID,
					AppConstants.SYSTEM_EMPLOYEE_NAME, null);
		}
		return pullDetail;
	}

	public UnitClosureDetails getLastClosureByUnit(int unitId, Date businessDate) {
		Query query = manager
				.createQuery("FROM UnitClosureDetails E where E.unitId = :unitId and E.businessDate = :businessDate and"
						+ " E.currentStatus <> :currentStatus order by E.closureId desc");
		query.setParameter("unitId", unitId);
		query.setParameter("businessDate", businessDate);
		query.setParameter("currentStatus", ClosureState.CANCELLED.name());
		query.setMaxResults(1);
		@SuppressWarnings("unchecked")
		List<UnitClosureDetails> unitClosureDetailsList = query.getResultList();
		UnitClosureDetails unitClosureDetails = unitClosureDetailsList.get(0);
		return unitClosureDetails;
	}

	@Override
	public String getPullSettlementPath(Integer settlementId) {
		SettlementDetail detail = manager.find(SettlementDetail.class, settlementId);
		return detail != null ? detail.getSettlementReceiptPath() : null;
	}

	@Override
	public SettlementDetail getSettlement(int settlementId) {
		return manager.find(SettlementDetail.class, settlementId);
	}

	@Override
	public ClosurePaymentDetails saveClosurePaymentDetails(ClosurePaymentDetails paymentDetails,
			List<ClosurePaymentTaxDetails> taxes, int unitId, Date businessDate) {
		UnitClosureDetails unitClosureDetails = getLastClosureByUnit(unitId, businessDate);
		paymentDetails.setUnitClosureDetails(unitClosureDetails);
		manager.persist(paymentDetails);
		manager.flush();
		for (ClosurePaymentTaxDetails tax : taxes) {
			tax.setClosurePaymentDetail(paymentDetails);
			manager.persist(tax);
		}
		return paymentDetails;
	}

	@Override
	public Date getLatestSuccessfulPullDate(int unitId, int paymentMode) throws DataNotFoundException {
		Query query = manager.createQuery("SELECT pd.pullDate FROM PullDetail pd where pd.paymentModeId = :paymentMode and pd.unitId = :unitId and  pd.status in (\'TRANSFERRED\',\'CANCELLED\') order by pd.pullDate  desc");
		query.setParameter("unitId", unitId);
		query.setParameter("paymentMode", paymentMode);
		List<Date> latestSuccessfulPullDate = query.getResultList();
		if (CollectionUtils.isEmpty(latestSuccessfulPullDate))
			throw new DataNotFoundException();
		return latestSuccessfulPullDate.get(0);
	}

	@Override
	public Long getPullSettlementsTotalCount(Date startDate, Date endDate, int unitId) {
		Query Countquery = manager.createQuery(
				"select count(*) FROM SettlementDetail E where E.unitId = :unitId AND E.time >= :startDate AND E.time <= :endDate");
		Countquery.setParameter("startDate", startDate);
		Countquery.setParameter("endDate", endDate);
		Countquery.setParameter("unitId", unitId);
		Long totalCount = (Long)Countquery.getSingleResult();
		return  totalCount;
	}

	@Override
	public Long getPullSettlementsByTypeTotalCount(Date startDate, Date endDate, int settlementTypeId) {
		Query query = manager.createQuery(
				"select count(*) FROM SettlementDetail E where E.settlementTypeId = :settlementTypeId AND E.time >= :startDate AND E.time <= :endDate order by id");
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		query.setParameter("settlementTypeId", settlementTypeId);
		@SuppressWarnings("unchecked")
		Long totalCount = (Long)query.getSingleResult();
		return totalCount;
	}
}
