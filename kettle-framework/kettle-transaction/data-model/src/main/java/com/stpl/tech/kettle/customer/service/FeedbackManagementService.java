package com.stpl.tech.kettle.customer.service;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.FeedbackEventStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackFrequency;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.kettle.core.data.vo.AuditTokenInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackOrderMetadata;
import com.stpl.tech.kettle.core.data.vo.FeedbackRatingData;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.domain.model.external.EventDetail;
import com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.domain.model.Pair;

public interface FeedbackManagementService {

    public List<FeedbackEventInfo> getPendingFeedbackEvents(FeedbackSource source, Date startTime, Date endTime);

    public List<FeedbackEventInfo> getPendingElaboratedFeedbackEvents(FeedbackSource source, Date startTime, Date endTime);

    public boolean updateFeedbackEventStatus(List<Integer> eventIds, FeedbackEventStatus status);

	public Date updateFeedbackEventStatus(int feedbackId, int eventId, ShortUrlData shortUrl, String longUrl,
			FeedbackEventStatus status, FeedbackStatus feedbackStatus);

    public FeedbackRatingData addFeedback(EventDetail event, FeedbackTokenInfo tokenIfo);

    public Integer addAudit(EventDetail event, AuditTokenInfo auditInfo);

    public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource, int feedbackId,
                                             Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId);

    public boolean cancelFeedback(String feedbackToken);

    public void updateFeedbackData(FeedbackFrequency frequency);

    public boolean getFeedbackStatus(int customerId);

    public FeedbackOrderMetadata getFeedbackData(int customerId);

    public Integer addOrderNPSDetail(WebEngageSurveyForm form);

	public void runNpsProc();

	public List<FeedbackEventInfo> getPendingNPSEvents(FeedbackSource sms);

	public List<FeedbackEventInfo> getInAppPendingNPSEvents(FeedbackSource sms);

	public void updateLastNPSTime(Date updateTime, int customerId);

	public boolean updateFeedbackDetail(Integer feedbackId, FeedbackStatus cancelled);

	public List<FeedbackEventInfo> getNotifiedNPSEventsForLastDay(FeedbackSource sms);

	public FeedbackEventInfo getFeedbackEventInfo(int feedbackId, FeedbackSource qr);

	boolean updateCustomerInfoInFeedbackData(int feedbackId, int customerId, String emailId);

    Pair<String, String> getFeedbackLinkForSource(int feedbackId, FeedbackSource feedbackSource);

    Integer getOrderID(Integer feedbackId);

    String getOrderDetailForFeedback(FeedbackTokenInfo tokenInfo) throws IOException;


    boolean saveFeedback(OrderDetailForFeedback feedbackData);

    FeedbackDetail getFeedbackDetailForSource(Integer orderId, FeedbackSource whatsApp);

    public  FeedbackEventInfo getPendingNPSForCustomer(FeedbackSource source, Integer orderId,Integer customerId);
}
