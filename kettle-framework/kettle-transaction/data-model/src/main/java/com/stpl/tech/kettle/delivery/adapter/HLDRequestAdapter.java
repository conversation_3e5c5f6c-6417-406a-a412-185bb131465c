/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.*;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class HLDRequestAdapter implements RequestAdapter<HLDGenericRequest, OrderInfo> {

	private static enum HLDDeliveryType {
		PREPAID, COD;
	}

	private static final Logger LOG = LoggerFactory.getLogger(HLDRequestAdapter.class);

	@Override
	public HLDGenericRequest adaptCreate(OrderInfo data) {
		HLDRequest hldRequest = new HLDRequest();

		hldRequest.setOrder_Id(data.getOrder().getGenerateOrderId());

		Customer customer = data.getCustomer();
		HLDAddrMetadata customerAddressComplete = new HLDAddrMetadata();
		HLDAddress customerAdd = new HLDAddress();
		HLDAddrMetadata restaurantAddress = new HLDAddrMetadata();
		HLDAddress restaurant = new HLDAddress();

		Address deliveryAdd = TransactionUtils.getAddressForOrder(data.getOrder().getDeliveryAddress(),
				customer.getAddresses());
		if (deliveryAdd != null) {
			customerAddressComplete.setLat(null);
			customerAddressComplete.setLng(null);
			customerAddressComplete.setDelivery_Subzone(null);
			customerAddressComplete.setName(customer.getFirstName());
			customerAddressComplete.setPhone(AppUtils.removeCountryCode(customer.getContactNumber()));
			customerAdd.setCity(deliveryAdd.getCity());
			customerAdd.setLocality(deliveryAdd.getLocality());
			customerAdd.setPin_Code(deliveryAdd.getZipCode());
			customerAdd.setState(deliveryAdd.getState());
		}

		Address unitAddress = data.getUnit().getAddress();
		if (unitAddress != null) {
			restaurantAddress.setLat(unitAddress.getLatitude());
			restaurantAddress.setLng(unitAddress.getLongitude());
			restaurantAddress.setName(data.getUnit().getName());
			restaurantAddress.setDelivery_Subzone(null);

			String unitContact = unitAddress.getContact1();

			restaurantAddress.setPhone(unitContact != null ? AppUtils.removeCountryCode(unitContact) : "");
			restaurant.setCity(unitAddress.getCity());
			restaurant.setLocality(unitAddress.getLine1());
			restaurant.setSub_Locality(unitAddress.getLocality());
		}

		customerAddressComplete.setAddress(customerAdd);
		restaurantAddress.setAddress(restaurant);

		TransactionDetail transactionDetails = data.getOrder().getTransactionDetail();
		if (transactionDetails != null) {
			hldRequest.setCollectable_Amount(transactionDetails.getPaidAmount());
			hldRequest.setTotalAmount(transactionDetails.getPaidAmount());
		}
		hldRequest.setCustomer(customerAddressComplete);
		hldRequest.setSlot_Order(new HLDSlotOrder(null, null));
		hldRequest.setDelivery_Type(getDeliveryType(data.getOrder().getSettlements()));
		hldRequest.setMerchant_Id(data.getOrder().getUnitId());
		// hldRequest.setMerchant_Id(312302); //localhost specific merchant productId
		hldRequest.setRestaurant(restaurantAddress);
		String isoDate = AppUtils.getCurrentISOTime().toString();
		LOG.info("Current time stamp in ISO format :::: {}", isoDate);
		hldRequest.setPickup_Time(isoDate);

		return hldRequest;
	}

	private String getDeliveryType(List<Settlement> settlements) {
		HLDDeliveryType deliveryType = HLDDeliveryType.PREPAID;
		if (settlements.size() == 1) {
			deliveryType = settlements.get(0).getMode() == 1 ? HLDDeliveryType.COD : HLDDeliveryType.PREPAID;
		}
		return deliveryType.toString();
	}

	@Override
	public HLDGenericRequest adaptCancel(OrderInfo orderInfo, String taskId) {
		HLDCancelRequest cancelRequest = new HLDCancelRequest();
		cancelRequest.setStatus(DeliveryStatus.CANCELLED.name());
		cancelRequest.setReason("testing");
		return cancelRequest;
	}
}
