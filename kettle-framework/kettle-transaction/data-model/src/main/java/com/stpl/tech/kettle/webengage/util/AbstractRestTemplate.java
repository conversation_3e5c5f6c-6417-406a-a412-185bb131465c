package com.stpl.tech.kettle.webengage.util;

import com.stpl.tech.master.core.WebServiceHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;

/**
 * Created by Chaayos on 09-05-2017.
 */
public class AbstractRestTemplate {
    private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);

    public static <T> T postWithAuth(String endPoint, String token, Object body, Class<T> clazz) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set("Authorization", token);
            requestHeaders.set("content-type",MediaType.APPLICATION_JSON_VALUE);
            HttpEntity<?> requestEntity;
            if (body != null) {
                requestEntity = new HttpEntity(body, requestHeaders);
            } else {
                requestEntity = new HttpEntity(requestHeaders);
            }
            MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
            jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON));
            restTemplate.getMessageConverters().add(jackson);
            return restTemplate.postForObject(endPoint, requestEntity, clazz);
        } catch (Exception e) {
            LOG.error("ERROR", e);
            throw e;
        }
    }



    public static <E> Collection<E> makeCollection(Iterable<E> iterable) {
        Collection<E> list = new ArrayList<E>();
        for (E item : iterable) {
            list.add(item);
        }
        return list;
    }
}
