package com.stpl.tech.kettle.datalake.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "RFM_SCORE_FOR_ACTIVE_CUSTOMERS")
public class RFMForActiveCustomers {

    private Integer customerId;
    private Integer productID;
    private Integer recency;
    private Integer frequency;
    private Integer RQuantile;
    private Integer FQuantile;
    private Integer RFMScore;
    private Date lastUpdateDate;
    private String recommendationSegment;

    @Id
    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductID() {
        return productID;
    }

    public void setProductID(Integer productID) {
        this.productID = productID;
    }

    @Column(name = "RECENCY")
    public Integer getRecency() {
        return recency;
    }

    public void setRecency(Integer recency) {
        this.recency = recency;
    }

    @Column(name = "FREQUENCY")
    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    @Column(name = "R_QUARTILE")
    public Integer getRQuantile() {
        return RQuantile;
    }

    public void setRQuantile(Integer RQuantile) {
        this.RQuantile = RQuantile;
    }

    @Column(name = "F_QUARTILE")
    public Integer getFQuantile() {
        return FQuantile;
    }

    public void setFQuantile(Integer FQuantile) {
        this.FQuantile = FQuantile;
    }

    @Column(name = "RFM_SCORE")
    public Integer getRFMScore() {
        return RFMScore;
    }

    public void setRFMScore(Integer RFMScore) {
        this.RFMScore = RFMScore;
    }

    @Column(name = "LAST_UPDATE_DATE")
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    @Column(name = "RECOMMENDATION_SEGMENT")
    public String getRecommendationSegment() {
        return recommendationSegment;
    }

    public void setRecommendationSegment(String recommendationSegment) {
        this.recommendationSegment = recommendationSegment;
    }
}
