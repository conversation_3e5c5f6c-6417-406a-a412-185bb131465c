/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.file.load;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.NotImplementedException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

public abstract class AbstractRowMapper<T, R> implements RowMapper<T, R> {

	private List<String> errors = new ArrayList<>();

	private static final String ERROR_MESSAGE_TEMPLATE = "Incorrect value in row [%d] and cell [%d]. Expected type %s and error message %s";
	private static final String ERROR_MESSAGE_TEMPLATE_CSV = "Incorrect value in cell [%d]. Expected type %s and error message %s";

	protected Object getCellValue(Cell cell) {
		switch (cell.getCellType()) {
			case STRING:
				return cell.getStringCellValue() == null ? "" : cell.getStringCellValue();

			case BOOLEAN:
				return cell.getBooleanCellValue();

			case NUMERIC:
				return cell.getNumericCellValue();
			default:
				break;
		}
		return null;
	}

	public void addError(Cell cell, String error) {
		errors.add(String.format(ERROR_MESSAGE_TEMPLATE, cell.getRowIndex(), cell.getColumnIndex(), cell.getCellType(),
				error));
	}

	public void addError(int i, String error) {
		errors.add(String.format(ERROR_MESSAGE_TEMPLATE_CSV, i, error, error));
	}

	public List<String> getErrors() {
		return errors;
	}

	public void setErrors(List<String> errorMessages) {
		this.errors = errorMessages;
	}

	@Override
	public void setData(T object, R nextCell, int index) {
		throw new NotImplementedException("set data for ExpenseRowMapper is not implemented");
	}
}
