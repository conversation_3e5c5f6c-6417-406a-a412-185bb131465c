package com.stpl.tech.kettle.datalake.service;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.data.vo.CustomerFeedbackData;
import com.stpl.tech.kettle.core.data.vo.CustomerNpsData;
import com.stpl.tech.master.core.exception.DataNotFoundException;

public interface CustomerReportService {

	void calculateNpsForBusinessDate(Date previousBusinessDate);

	void calculateCustomerOneViewFeedbackStats(Date previousBusinessDate);

	void calculateFeedbackForBusinessDate(Date previousBusinessDate);

	void calculateCustomerOneViewOrderFeedbackStats(Date previousBusinessDate);

	CustomerNpsData getNpsForCustomerDetail(Integer customerId) throws DataNotFoundException;

	List<Integer> customerRecommendedProduct(Integer customerId);

	CustomerFeedbackData getFeedbackForCustomerDetail(Integer customerId) throws DataNotFoundException;

}
