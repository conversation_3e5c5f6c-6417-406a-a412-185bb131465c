package com.stpl.tech.kettle.google.converter;


import com.google.ads.googleads.v12.common.TransactionAttribute;
import com.google.ads.googleads.v12.common.UserData;
import com.google.ads.googleads.v12.common.UserIdentifier;
import com.google.ads.googleads.v12.enums.UserIdentifierSourceEnum;
import com.google.ads.googleads.v12.services.ClickConversion;
import com.google.ads.googleads.v12.services.OfflineUserDataJobOperation;
import com.google.ads.googleads.v12.utils.ResourceNames;
import com.google.common.collect.ImmutableList;
import com.stpl.tech.kettle.facebook.converter.FBConverter;
import com.stpl.tech.kettle.google.util.GoogleConstants;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class GoogleDataConverter {

    private static final Logger LOG = LoggerFactory.getLogger(FBConverter.class);

    private GoogleDataConverter() {
        throw new IllegalStateException("Converter class");
    }

    /**
     * Create the transaction for upload based on an email address and phone number (E164 format)
     * @param orderTaxableAmount
     * @param transactionTime
     * @param contactNumber
     * @param customerId
     * @param conversionActionId
     * @param customKey
     * @param customValue
     * @param sha256Digest hash object
     * @return required object format
     */
    public static OfflineUserDataJobOperation convertOrderDataToOperation(BigDecimal orderTaxableAmount,
                                                                          Date transactionTime, String contactNumber,
                                                                          Long customerId, Long conversionActionId,
                                                                          String customKey, String customValue,
                                                                          MessageDigest sha256Digest, String generatedOrderId) {

        try {
            List<UserIdentifier> userIdentifiers = new ArrayList<>();
            if (Objects.nonNull(contactNumber)) {
                userIdentifiers.add(UserIdentifier.newBuilder()
                        .setHashedPhoneNumber(
                                normalizeAndHash(sha256Digest, AppConstants.DEFAULT_COUNTRY_CODE + contactNumber))
                        .build());
            }
//            if (Objects.nonNull(customerData.getEmailId())) {
//                userIdentifiers.add(UserIdentifier.newBuilder()
//                        .setHashedEmail(
//                                normalizeAndHash(sha256Digest, customerData.getEmailId()))
//                        .build());
//            }
            if (userIdentifiers.isEmpty()) {
                throw new Exception("No user identifiers found");
            }

            UserData.Builder userTransactionData = UserData.newBuilder()
                    .addAllUserIdentifiers(ImmutableList.<UserIdentifier>builder().addAll(userIdentifiers).build())
                    .setTransactionAttribute(TransactionAttribute.newBuilder()
                            .setConversionAction(ResourceNames.conversionAction(customerId, conversionActionId))
                            .setCurrencyCode(GoogleConstants.INR)
                            .setTransactionAmountMicros(orderTaxableAmount.doubleValue() * 1_000_000L)
                            .setTransactionDateTime(
                                    AppUtils.getDateString(transactionTime, "yyyy-MM-dd HH:mm:ss") + "+05:30")
                            .setOrderId(generatedOrderId));

            //Optional: If uploading data with custom key and values, also assign the custom value.
            if (customKey != null) {
                userTransactionData.getTransactionAttributeBuilder().setCustomValue(customValue);
            }

            return OfflineUserDataJobOperation.newBuilder().setCreate(userTransactionData.build()).build();
        } catch (Exception e) {
            LOG.info("Error while converting to Google spec data {} for contact number ", contactNumber);
            LOG.info(e.toString());
            return null;
        }
    }

    public static ClickConversion convertOrderDataToClickConversionUploadSpec(String conversionActionResourceName,
                                                                              String emailAddress,
                                                                              Date transactionTime,
                                                                              Double conversionValue,
                                                                              String orderId,
                                                                              String contactNumber,
                                                                              String gclid,
                                                                              MessageDigest sha256Digest) throws UnsupportedEncodingException {

        // Creates a builder for constructing the click conversion.
        ClickConversion.Builder clickConversionBuilder = ClickConversion.newBuilder()
                .setConversionAction(conversionActionResourceName)
                .setConversionDateTime(AppUtils.getDateString(transactionTime, "yyyy-MM-dd HH:mm:ss") + "+05:30")
                .setConversionValue(conversionValue)
                .setCurrencyCode(GoogleConstants.INR);

        if (Objects.nonNull(orderId)) {
            clickConversionBuilder.setOrderId(orderId);
        }

        if (Objects.nonNull(gclid)) {
            clickConversionBuilder.setGclid(gclid);
        }

        if(Objects.nonNull(emailAddress)) {
            UserIdentifier userIdentifier = UserIdentifier.newBuilder()
                    .setHashedEmail(normalizeAndHashEmailAddress(sha256Digest, emailAddress))
                    .setUserIdentifierSource(UserIdentifierSourceEnum.UserIdentifierSource.FIRST_PARTY)
                    .build();
            // Adds the user identifier to the conversion.
            clickConversionBuilder.addUserIdentifiers(userIdentifier);
        }

        if(Objects.nonNull(contactNumber)) {
            UserIdentifier userIdentifier = UserIdentifier.newBuilder()
                    .setHashedPhoneNumber(normalizeAndHash(sha256Digest, AppConstants.DEFAULT_COUNTRY_CODE + contactNumber))
                    .setUserIdentifierSource(UserIdentifierSourceEnum.UserIdentifierSource.FIRST_PARTY)
                    .build();
            clickConversionBuilder.addUserIdentifiers(userIdentifier);
        }

        return clickConversionBuilder.build();
    }

    /**
     * Private customer data must be hashed during upload
     * @param digest the digest to use to hash the normalized string.
     * @param s      the string to normalize and hash.
     */
    private static String normalizeAndHash(MessageDigest digest, String s)
            throws UnsupportedEncodingException {
        // Normalizes by removing leading and trailing whitespace and converting all characters to lower case.
        String normalized = s.trim().toLowerCase();
        // Hashes the normalized string using the hashing algorithm.
        byte[] hash = digest.digest(normalized.getBytes("UTF-8"));
        StringBuilder result = new StringBuilder();
        for (byte b : hash) {
            result.append(String.format("%02x", b));
        }

        return result.toString();
    }

    /**
     * Returns the result of normalizing and hashing an email address. For this use case, Google Ads
     * requires removal of any '.' characters preceding {@code gmail.com} or {@code googlemail.com}.
     *
     * @param digest the digest to use to hash the normalized string.
     * @param emailAddress the email address to normalize and hash.
     */
    private static String normalizeAndHashEmailAddress(MessageDigest digest, String emailAddress)
            throws UnsupportedEncodingException {
        String normalizedEmail = emailAddress.toLowerCase();
        String[] emailParts = normalizedEmail.split("@");
        if (emailParts.length > 1 && emailParts[1].matches("^(gmail|googlemail)\\.com\\s*")) {
            emailParts[0] = emailParts[0].replaceAll("\\.", "");
            normalizedEmail = String.format("%s@%s", emailParts[0], emailParts[1]);
        }
        return normalizeAndHash(digest, normalizedEmail);
    }
}
