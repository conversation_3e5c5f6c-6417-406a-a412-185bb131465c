package com.stpl.tech.kettle.clm.service;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;

import java.util.Map;

public interface ClevertapAttributesService {

    public Map<String, Object> getProfileAttributes(Customer customer) ;

    public Map<String, Object> getEventAttributes(OrderDetail order, CustomerInfo customer) ;

    public Map<String, Object> getSubscriptionAttributes(OrderDetail order, CustomerInfo customer) ;
    
    public int updateCommunicationAttributes(String optInWhatsapp , String optInSms,int customerId);

    public Map<String, Object> getProfileAttributes(Integer customerId) ;

}
