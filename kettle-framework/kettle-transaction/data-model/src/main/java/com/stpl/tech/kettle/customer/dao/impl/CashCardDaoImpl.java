/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.kettle.core.CashCardStatus;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.GiftOffer;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.customer.dao.CashCardDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardEvent;
import com.stpl.tech.kettle.data.model.CashCardEventsLogData;
import com.stpl.tech.kettle.data.model.CashCardOffer;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.CashCardEventStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.GiftCardActivationRequest;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Repository
public class CashCardDaoImpl extends AbstractDaoImpl implements CashCardDao {

    private static final Logger LOG = LoggerFactory.getLogger(CashCardDaoImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public CashCardDetail getCardDetail(int customerId, String cardNumber, boolean runValidations)
            throws CardValidationException {
        Query query = manager.createQuery("FROM CashCardDetail E where E.cardNumber = :cardNumber");
        query.setParameter("cardNumber", cardNumber);
        CashCardDetail cashCardDetail = null;
        try {
            cashCardDetail = (CashCardDetail) query.getSingleResult();
        } catch (Exception e) {
            String message = "Did not find any card with card number : " + cardNumber;
            LOG.info(message);
            throw new CardValidationException(message);
        }
        if (cashCardDetail == null) {
            throw new CardValidationException("Did not find any card with card number : " + cardNumber);
        }
        if (runValidations) {
            validateCard(cashCardDetail, customerId, BigDecimal.ZERO);
        }
        return cashCardDetail;

    }

    @Override
    public CashCardDetail getCardDetail(String serial) throws CardValidationException {
        Query query = manager.createQuery("FROM CashCardDetail E where E.serialNumber = :serial");
        query.setParameter("serial", serial);
        CashCardDetail cashCardDetail = null;
        try {
            cashCardDetail = (CashCardDetail) query.getSingleResult();
        } catch (Exception e) {
            String message = "Did not find any card with card serial : " + serial;
            LOG.info(message);
            throw new CardValidationException(message);
        }
        if (cashCardDetail == null) {
            throw new CardValidationException("Did not find any card with card number : " + serial);
        }
        return cashCardDetail;

    }

    @Override
    public CashCardDetail getCardDetailForActivation(GiftCardActivationRequest giftCardActivationRequest)
            throws CardValidationException, DataUpdationException {
        Query query = null;
        if (giftCardActivationRequest.getCardSerial() != null) {
            query = manager.createQuery("FROM CashCardDetail E where E.cardSerial = :cardSerial");
            query.setParameter("cardSerial", giftCardActivationRequest.getCardSerial());
        } else if (giftCardActivationRequest.getCardNumber() != null) {
            query = manager.createQuery("FROM CashCardDetail E where E.cardNumber = :cardNumber");
            query.setParameter("cardNumber", giftCardActivationRequest.getCardNumber());
        }
        CashCardDetail cashCardDetail = null;
        if (query != null) {
            try {
                cashCardDetail = (CashCardDetail) query.getSingleResult();
            } catch (Exception e) {
                String message = "Did not find any card with card serial : " + giftCardActivationRequest.getCardSerial()
                        + " and number: " + giftCardActivationRequest.getCardNumber();
                LOG.info(message);
                cashCardDetail = new CashCardDetail();
                cashCardDetail.setCardNumber(giftCardActivationRequest.getCardNumber());
                cashCardDetail.setCardSerial(giftCardActivationRequest.getCardSerial());
                logCashCardEvent(cashCardDetail, CashCardEventStatus.CARD___ACTIVATION___FAILED,
                        giftCardActivationRequest);
                throw new CardValidationException(message);
            }
        }
        if (cashCardDetail == null) {
            cashCardDetail = new CashCardDetail();
            cashCardDetail.setCardNumber(giftCardActivationRequest.getCardNumber());
            cashCardDetail.setCardSerial(giftCardActivationRequest.getCardSerial());
            logCashCardEvent(cashCardDetail, CashCardEventStatus.CARD___ACTIVATION___FAILED, giftCardActivationRequest);
        }
        return cashCardDetail;

    }

    @Override
    public void logCashCardEvent(CashCardDetail cashCardDetail, CashCardEventStatus cashCardEventStatus,
                                 GiftCardActivationRequest giftCardActivationRequest) throws DataUpdationException {
        CashCardEventsLogData cashCardEventsLogData = new CashCardEventsLogData();
        cashCardEventsLogData.setEventTime(AppUtils.getCurrentTimestamp());
        cashCardEventsLogData.setCardNumber(cashCardDetail.getCardNumber());
        cashCardEventsLogData.setCardSerial(cashCardDetail.getCardSerial());
        cashCardEventsLogData.setEvent(cashCardEventStatus.value());
        cashCardEventsLogData.setEventDetail(giftCardActivationRequest.getReason());
        cashCardEventsLogData.setEmpId(giftCardActivationRequest.getRequestingEmployee());
        cashCardEventsLogData.setUnitId(giftCardActivationRequest.getUnitId());
        add(cashCardEventsLogData);
    }

    @Override
    public List<CashCardDetail> getCardDetails(int customerId) throws CardValidationException {
        Query query = manager.createQuery("FROM CashCardDetail C WHERE (C.customerId = :customerId OR C.buyerId = :customerId) " +
                "AND C.cashPendingAmount > :pendingAmount AND C.cardStatus IN (:cardStatus)");
/*        Query query = manager.createQuery(
                "FROM CashCardDetail E where ( E.customerId = :customerId AND E.cashPendingAmount > :pendingAmount AND E.cardStatus = :cardStatus ) "
                        + " OR ( E.buyerId = :buyerId AND E.cashPendingAmount > :pendingAmount AND E.cardStatus = :readyCardStatus) "
                        + " ORDER BY E.cashPendingAmount");*/
        query.setParameter("customerId", customerId);
        query.setParameter("pendingAmount", BigDecimal.ZERO);
        query.setParameter("cardStatus", Arrays.asList(CashCardStatus.ACTIVE.name(),CashCardStatus.READY_FOR_ACTIVATION.name()));
        return query.getResultList();
    }

    @Override
    public CustomerCardInfo getCashCardAmount(int customerId) {
        Query query = manager.createQuery(
                "select sum(cashPendingAmount) FROM CashCardDetail E where E.customerId = :customerId AND E.cashPendingAmount > :pendingAmount AND E.cardStatus = :cardStatus");
        query.setParameter("customerId", customerId);
        query.setParameter("pendingAmount", BigDecimal.ZERO);
        query.setParameter("cardStatus", CashCardStatus.ACTIVE.name());
        Object data = query.getSingleResult();
        return new CustomerCardInfo(data);
    }

    private void validateCard(CashCardDetail cashCardDetail, int customerId, BigDecimal amount)
            throws CardValidationException {
        // pending (100) - amount (100) = 0
        if (cashCardDetail.getCashPendingAmount().subtract(amount).compareTo(BigDecimal.ZERO) < 0) {
            throw new CardValidationException("Card does not have sufficient value : Card Value "
                    + cashCardDetail.getCashPendingAmount() + " : Amount Charged :" + amount);
        }
        if (cashCardDetail.getCustomerId() != null && cashCardDetail.getCustomerId() != customerId) {
            throw new CardValidationException("Card is not linked with the customer");
        }
        /*
         * if (cashCardDetail.getEndDate().before(AppUtils.getCurrentBusinessDate() )) {
         * throw new CardValidationException("Card has expired on: " +
         * cashCardDetail.getEndDate()); }
         */
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.INITIATED.name())) {
            throw new CardValidationException("Card has not been purchased.");
        }
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.EXPIRED.name())) {
            throw new CardValidationException("Card has expired");
        }
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.BLOCKED.name())) {
            throw new CardValidationException("Card is invalid");
        }

    }

    @Override
    public void createCardEvent(int cashCardId, int customerId, int orderId, int settlementId, BigDecimal amount)
            throws CardValidationException {
        CashCardDetail detail = getCardDetail(cashCardId);

        validateCard(detail, customerId, amount);
        if (detail.getCustomerId() == null) {
            detail.setCustomerId(customerId);
            detail.setActivationTime(AppUtils.getCurrentTimestamp());
        }
        if (detail.getCardStatus().equals(CashCardStatus.READY_FOR_ACTIVATION.name())) {
            detail.setCardStatus(CashCardStatus.ACTIVE.name());
        }
        detail.setCashPendingAmount(detail.getCashPendingAmount().subtract(amount));
        manager.flush();
        CashCardEvent event = new CashCardEvent();
        event.setCashCardId(cashCardId);
        event.setOrderId(orderId);
        event.setOrderSettlementId(settlementId);
        event.setSettlementAmount(amount);
        event.setSettlementStatus(AppConstants.ACTIVE);
        event.setSettlementTime(AppUtils.getCurrentTimestamp());
//        event.setOpeningBalance(AppUtils.add(amount,detail.getCashPendingAmount()));
//        event.setClosingBalance(detail.getCashPendingAmount());
        manager.persist(event);
        manager.flush();
    }

    @Override
    public void activateCashCard(CashCardDetail cardDetail, OrderDetail orderDetail) throws DataUpdationException {
        if (cardDetail.getCustomerId() == null) {
            cardDetail.setCustomerId(cardDetail.getBuyerId());
            cardDetail.setActivationTime(AppUtils.getCurrentTimestamp());
        }
        cardDetail.setCardStatus(CashCardStatus.ACTIVE.name());
        manager.flush();
        manager.merge(cardDetail);
        GiftCardActivationRequest giftCardActivationRequest = new GiftCardActivationRequest();
        giftCardActivationRequest.setReason("Activation at the time of purchasing");
        giftCardActivationRequest.setRequestingEmployee(orderDetail.getEmpId());
        giftCardActivationRequest.setUnitId(orderDetail.getUnitId());
        logCashCardEvent(cardDetail, CashCardEventStatus.CARD___ACTIVATION___SUCCESS, giftCardActivationRequest);
    }

    @Override
    public void cancelCardEvent(int orderId, int settlementId, int cashCardId) {
        String sqlQuery = "FROM CashCardEvent C where C.orderId = :orderId "
                + "and C.orderSettlementId = :orderSettlementId and C.cashCardId = :cashCardId";
        Query query = manager.createQuery(sqlQuery);
        query.setParameter("orderId", orderId);
        query.setParameter("orderSettlementId", settlementId);
        query.setParameter("cashCardId", cashCardId);
        CashCardEvent cardEvent = (CashCardEvent) query.getSingleResult();
        cardEvent.setSettlementStatus(SwitchStatus.IN_ACTIVE.toString());
        manager.merge(cardEvent);

        CashCardDetail cashCardDetail = manager.find(CashCardDetail.class, cashCardId);
        BigDecimal modifiedAmount = cashCardDetail.getCashPendingAmount().add(cardEvent.getSettlementAmount());
        cashCardDetail.setCashPendingAmount(modifiedAmount);
        manager.merge(cashCardDetail);

        manager.flush();

    }

    @Override
    public void deactivateCashCards() {
        Query query = manager.createNativeQuery("CALL EXPIRE_CASH_CARD()");
        query.executeUpdate();
    }

    @Override
    public CashCardDetail getCardDetail(int cardId) {
        return manager.find(CashCardDetail.class, cardId);
    }

    public List<GiftOffer> getCurrentCardOffer(int unitId, Integer partnerId) {
        List<GiftOffer> offers = new ArrayList<>();
        StringBuilder qlString = new StringBuilder("FROM CashCardOffer E where E.unitId = :unitId and "
                + ":currentDate >= E.startDate and " + ":currentDate <= E.endDate and "
                + "E.offerStatus = :offerStatus AND E.partnerId = :partnerId order by E.cashCardOfferId desc, E.denomination desc");
        Query query = manager.createQuery(qlString.toString());
        query.setParameter("unitId", unitId);
        query.setParameter("currentDate", AppUtils.getCurrentBusinessDate());
        query.setParameter("offerStatus", AppConstants.ACTIVE);
        query.setParameter("partnerId", partnerId);
        if(partnerId != null) {
            query.setParameter("partnerId", partnerId);
        }
        try {
            List<?> result = query.getResultList();
            if (result != null && result.size() > 0) {
                Set<Integer> denominations = new HashSet<>();
                for (Object r : result) {
                    CashCardOffer offer = (CashCardOffer) r;
                    GiftOffer o = new GiftOffer();
                    if (!denominations.contains(offer.getDenomination().intValue())) {
                        o.setDenomination(offer.getDenomination());
                        o.setOffer(Objects.nonNull(offer.getPercentage()) ? offer.getPercentage() : BigDecimal.ZERO);
                        o.setSuggestWalletOffer(Objects.nonNull(offer.getSuggestWalletPercentage()) ? offer.getSuggestWalletPercentage() : BigDecimal.ZERO);
                        o.setEndDate(offer.getEndDate());
                        offers.add(o);
                        denominations.add(offer.getDenomination().intValue());
                    }
                }
            }
        } catch (NoResultException e) {

        }
        return offers;
    }

    @Override
    public CashCardOffer getCashCardOffer(BigDecimal cardAmount, int unitId,int partnerId) {
        Query query = manager.createQuery("FROM CashCardOffer E where E.unitId = :unitId and "
                + ":currentDate >= E.startDate and " + ":currentDate <= E.endDate and "
                + "E.offerStatus = :offerStatus and " + "E.denomination = :cardAmount and E.partnerId = :partnerId  order by E.cashCardOfferId desc");
        query.setParameter("unitId", unitId);
        query.setParameter("currentDate", AppUtils.getCurrentBusinessDate());
        query.setParameter("offerStatus", AppConstants.ACTIVE);
        query.setParameter("cardAmount", cardAmount);
        query.setParameter("partnerId", partnerId);
        try {
            List<?> result = query.getResultList();
            if (result == null || result.size() == 0) {
                return null;
            } else {
                return (CashCardOffer) result.get(0);
            }
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public List<CashCardDetail> getCardsByPurchaseOrderId(Integer orderId) {
        Query query = manager.createQuery("FROM CashCardDetail E where E.purchaseOrderId = :purchaseOrderId");
        query.setParameter("purchaseOrderId", orderId);
        return query.getResultList();
    }

    @Override
    public void blockCards(Integer orderId) {
        Query query = manager.createQuery("FROM CashCardDetail E where E.purchaseOrderId = :purchaseOrderId");
        query.setParameter("purchaseOrderId", orderId);
        List<CashCardDetail> cards = query.getResultList();
        if (cards != null) {
            for (CashCardDetail card : cards) {
                card.setCardStatus(CashCardStatus.BLOCKED.name());
            }
        }
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.data.dao.CashCardDao#getUnitsWithCardOffersForToday( )
     */
    @Override
    public List<Integer> getUnitsWithCardOffersForToday() {
        Query query = manager
                .createQuery("select distinct unitId FROM CashCardOffer E where " + ":currentDate >= E.startDate and "
                        + ":currentDate <= E.endDate and " + "E.offerStatus = :offerStatus");
        query.setParameter("currentDate", AppUtils.getCurrentBusinessDate());
        query.setParameter("offerStatus", AppConstants.ACTIVE);
        try {
            return query.getResultList();
        } catch (NoResultException e) {

        }
        return new ArrayList<>();
    }

    @Override
    public List<CashCardDetail> getActiveCashCards(int customerId) {
        Query query = manager.createQuery(
                "FROM CashCardDetail E WHERE E.customerId = :customerId AND E.cashPendingAmount > :pendingAmount AND E.cardStatus = :cardStatus ");
        query.setParameter("customerId", customerId);
        query.setParameter("pendingAmount", BigDecimal.ZERO);
        query.setParameter("cardStatus", CashCardStatus.ACTIVE.name());
        return query.getResultList();
    }

    @Override
    public List<CashCardOffer> getAllCashCardOffers(Date businessDate) {
        Query query = manager.createQuery("FROM CashCardOffer E WHERE E.endDate >= :currentDate");
        query.setParameter("currentDate", businessDate);
        return query.getResultList();
    }

    @Override
    public List<CashCardOffer> getCashCardOffersForDate(Date startDate, Date endDate, Integer partnerId) {
        Query query = manager.createQuery("FROM CashCardOffer E WHERE E.startDate <= :startDate and E.endDate >= :endDate AND E.partnerId = :partnerId");
        query.setParameter("endDate", endDate);
        query.setParameter("startDate", startDate);
        query.setParameter("partnerId", partnerId);
        return query.getResultList();
    }

    @Override
    public List<CashCardOffer> getCashCardOffersForDateWithState(Date startDate, Date endDate, Integer partnerId, String status) {
        Query query = manager.createQuery("FROM CashCardOffer E WHERE E.startDate <= :startDate and E.endDate >= :endDate AND E.partnerId = :partnerId AND E.offerStatus = :status");
        query.setParameter("endDate", endDate);
        query.setParameter("startDate", startDate);
        query.setParameter("status", status);
        query.setParameter("partnerId", partnerId);
        return query.getResultList();
    }



    @Override
    public List<CashCardOffer> addCashCardOffers(List<CashCardOffer> list) throws CardValidationException {
        List<CashCardOffer> l = new ArrayList<CashCardOffer>();
        List<CashCardOffer> existingOffers = getCashCardOffersForDateWithState(list.get(0).getStartDate(), list.get(0).getEndDate(), list.get(0).getPartnerId(), AppConstants.ACTIVE);
        Map<Integer, List<CashCardOffer>> existingOffersMap = new HashMap<>();
        existingOffers.forEach(cashCardOffer -> {
            if(!existingOffersMap.containsKey(cashCardOffer.getUnitId()) || Objects.isNull(existingOffersMap.get(cashCardOffer.getUnitId()))) {
                existingOffersMap.put(cashCardOffer.getUnitId(), new ArrayList<>());
            }
            existingOffersMap.get(cashCardOffer.getUnitId()).add(cashCardOffer);
        });
        for (CashCardOffer c : list) {
            boolean offerExist = false;
            if(existingOffersMap.containsKey(c.getUnitId())) {
                List<CashCardOffer> existingOfferForUnit  =new ArrayList<>();
                offerExist = existingOffersMap.get(c.getUnitId()).stream().anyMatch(cashCardOffer -> AppUtils.isEqual(cashCardOffer.getDenomination(), c.getDenomination()));
            }
            if(!offerExist) {
                l.add(add(c));
            } else {
                try{
                    updateCashCardDetails(c);
                }catch (Exception e) {
                    LOG.error("Error Updating Cash Card Percentage for Unit" + c.getUnitId(), e);
                    throw new CardValidationException("Error in updating Cash Cards for these dates for unit: " + masterDataCache.getUnit(c.getUnitId()).getName());
                }

            }
        }
        manager.flush();
        return l;
    }

    public void updateCashCardDetails(CashCardOffer offer) throws CardValidationException {
            try {
                if (offer.getWalletType().equals(AppConstants.GIFT_CARD_WALLET)) {
                    Query query = manager.createQuery("update CashCardOffer set percentage = :percentage,description = : description"
                            + " where  startDate <= :startDate and endDate >= :endDate AND partnerId = :partnerId AND offerStatus = :status AND unitId =:unitId AND denomination =:denomination");
                    query.setParameter("unitId", offer.getUnitId());
                    query.setParameter("percentage", offer.getPercentage());
                    query.setParameter("description", offer.getDescription());
                    query.setParameter("endDate", offer.getEndDate());
                    query.setParameter("startDate", offer.getStartDate());
                    query.setParameter("status", offer.getOfferStatus());
                    query.setParameter("partnerId", offer.getPartnerId());
                    query.setParameter("status", AppConstants.ACTIVE);
                    query.setParameter("denomination", offer.getDenomination());
                    query.executeUpdate();
                } else {
                    Query query = manager.createQuery("update CashCardOffer set suggestWalletPercentage = :suggestWalletPercentage, suggestWalletDescription = :suggestWalletDescription "
                            + " where  startDate <= :startDate and endDate >= :endDate AND partnerId = :partnerId AND offerStatus = :status AND unitId =:unitId AND denomination =:denomination");
                    query.setParameter("unitId", offer.getUnitId());
                    query.setParameter("suggestWalletPercentage", offer.getSuggestWalletPercentage());
                    query.setParameter("suggestWalletDescription", offer.getSuggestWalletDescription());
                    query.setParameter("endDate", offer.getEndDate());
                    query.setParameter("startDate", offer.getStartDate());
                    query.setParameter("status", offer.getOfferStatus());
                    query.setParameter("partnerId", offer.getPartnerId());
                    query.setParameter("status", AppConstants.ACTIVE);
                    query.setParameter("denomination", offer.getDenomination());
                    query.executeUpdate();
                }
            }catch (Exception e){
                LOG.error("Error in Updating Card Details for Unit "+ offer.getUnitId(),e);
            }
    }

    @Override
    public List<CashCardOffer> changeStatusAllCashCardOffers(List<CashCardOffer> list) {
        List<CashCardOffer> l = new ArrayList<CashCardOffer>();
        for (CashCardOffer c : list) {
            CashCardOffer e = manager.find(CashCardOffer.class, c.getCashCardOfferId());
            if (e != null) {
                e.setOfferStatus(AppConstants.ACTIVE.equals(e.getOfferStatus()) ? AppConstants.IN_ACTIVE
                        : AppConstants.ACTIVE);
                l.add(e);
            }
        }
        manager.flush();
        return l;
    }

    @Override
    public String getUniqueCashCardNumber() {

        String cardNumber = AppUtils.generateRandomAlphaNumericCode(8);
        Query query = manager.createQuery("FROM CashCardDetail c WHERE c.cardNumber = :cardNumber");
        query.setParameter("cardNumber", cardNumber);
        List<CashCardDetail> list = query.getResultList();

        if (list.size() > 0) {
            getUniqueCashCardNumber();
        }
        return cardNumber;

    }

	@Override
	public BigDecimal getGiftCardOffer(Integer purchaseOrderId) {
		Query query = manager
				.createQuery("SELECT SUM(initialOffer) FROM CashCardDetail WHERE purchaseOrderId = :purchaseOrderId");
		query.setParameter("purchaseOrderId", purchaseOrderId);
		Object o = query.getSingleResult();
		return o == null ? BigDecimal.ZERO : (BigDecimal) o;
	}

}
