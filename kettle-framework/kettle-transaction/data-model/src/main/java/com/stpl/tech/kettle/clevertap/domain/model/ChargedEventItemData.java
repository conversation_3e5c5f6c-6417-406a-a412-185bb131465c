package com.stpl.tech.kettle.clevertap.domain.model;

import java.math.BigDecimal;

public class ChargedEventItemData {
    Integer ProductId;
    String ProductName;
    String ProductType;
    String ProductSubType;
    String FoodClass;
    Integer PaxCount;
    BigDecimal ItemTotalAmount;
    BigDecimal ItemAmountPaid;
    Integer Quantity;

    public Integer getProductId() {
        return ProductId;
    }

    public void setProductId(Integer productId) {
        ProductId = productId;
    }

    public String getProductName() {
        return ProductName;
    }

    public void setProductName(String productName) {
        ProductName = productName;
    }

    public String getProductType() {
        return ProductType;
    }

    public void setProductType(String productType) {
        ProductType = productType;
    }

    public String getProductSubType() {
        return ProductSubType;
    }

    public void setProductSubType(String productSubType) {
        ProductSubType = productSubType;
    }

    public String getFoodClass() {
        return FoodClass;
    }

    public void setFoodClass(String foodClass) {
        FoodClass = foodClass;
    }

    public Integer getPaxCount() {
        return PaxCount;
    }

    public void setPaxCount(Integer paxCount) {
        PaxCount = paxCount;
    }

    public BigDecimal getItemTotalAmount() {
        return ItemTotalAmount;
    }

    public void setItemTotalAmount(BigDecimal itemTotalAmount) {
        ItemTotalAmount = itemTotalAmount;
    }

    public BigDecimal getItemAmountPaid() {
        return ItemAmountPaid;
    }

    public void setItemAmountPaid(BigDecimal itemAmountPaid) {
        ItemAmountPaid = itemAmountPaid;
    }

    public Integer getQuantity() {
        return Quantity;
    }

    public void setQuantity(Integer quantity) {
        Quantity = quantity;
    }
}
