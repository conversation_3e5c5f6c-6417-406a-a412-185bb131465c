package com.stpl.tech.kettle.core.data.vo;

import com.stpl.tech.kettle.domain.model.PotentialSavingData;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class CustomerVisitInfo {

    Integer customerId;
    Boolean firstTimeCustomer;
    Boolean regularCustomer;
    Boolean outStationCustomer;
    Integer unitId;
    String unitName;
    String currentRegion;
    String previousRegion;
    Set<Integer> productId;
    List<CustomerLastOrderInfo> infoList = new ArrayList<>();
    String orderSource;
    Integer channelPartner;
    Integer orderId;
    Boolean lastFeedback;
    Boolean lastFeedbackEvent;
    String  lastFeedbackDetail;
    Boolean firstZomatoOrder;
    PotentialSavingData potentialSavingData;

    public Boolean getLastFeedbackEvent() {
        return lastFeedbackEvent;
    }

    public void setLastFeedbackEvent(Boolean lastFeedbackEvent) {
        this.lastFeedbackEvent = lastFeedbackEvent;
    }

    public Boolean getLastFeedback() {
        return lastFeedback;
    }

    public void setLastFeedback(Boolean lastFeedback) {
        this.lastFeedback = lastFeedback;
    }

    public String getLastFeedbackDetail() {
        return lastFeedbackDetail;
    }

    public void setLastFeedbackDetail(String lastFeedbackDetail) {
        this.lastFeedbackDetail = lastFeedbackDetail;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<CustomerLastOrderInfo> getInfoList() {
        return infoList;
    }

    public void setInfoList(List<CustomerLastOrderInfo> infoList) {
        this.infoList = infoList;
    }

    public Set<Integer> getProductId() {
        return productId;
    }

    public void setProductId(Set<Integer> productId) {
        this.productId = productId;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getCurrentRegion() {
        return currentRegion;
    }

    public void setCurrentRegion(String currentRegion) {
        this.currentRegion = currentRegion;
    }

    public String getPreviousRegion() {
        return previousRegion;
    }

    public void setPreviousRegion(String previousRegion) {
        this.previousRegion = previousRegion;
    }

    public Boolean getFirstTimeCustomer() {
        return firstTimeCustomer;
    }

    public void setFirstTimeCustomer(Boolean firstTimeCustomer) {
        this.firstTimeCustomer = firstTimeCustomer;
    }

    public Boolean getRegularCustomer() {
        return regularCustomer;
    }

    public void setRegularCustomer(Boolean regularCustomer) {
        this.regularCustomer = regularCustomer;
    }

    public Boolean getOutStationCustomer() {
        return outStationCustomer;
    }

    public void setOutStationCustomer(Boolean outStationCustomer) {
        this.outStationCustomer = outStationCustomer;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public Integer getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(Integer channelPartner) {
        this.channelPartner = channelPartner;
    }

    public Boolean getFirstZomatoOrder() {
        return firstZomatoOrder;
    }

    public void setFirstZomatoOrder(Boolean firstZomatoOrder) {
        this.firstZomatoOrder = firstZomatoOrder;
    }

    public PotentialSavingData getPotentialSavingData() {
        return potentialSavingData;
    }

    public void setPotentialSavingData(PotentialSavingData potentialSavingData) {
        this.potentialSavingData = potentialSavingData;
    }
}
