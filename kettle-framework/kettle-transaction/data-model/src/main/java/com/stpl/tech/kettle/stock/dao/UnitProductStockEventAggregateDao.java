package com.stpl.tech.kettle.stock.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.data.model.UnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventAggregate;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface UnitProductStockEventAggregateDao extends AbstractDao {
	
	public List<UnitProductStockEventAggregate> getStockEventArg(Integer unitId, Date calculationDate,Integer dayDiff);

	public void deleteData(Date calculationDate, Integer unitId);

	List<UnitProductStockEventAggregate> getStockEventArg(List<UnitProductStockData> results, Integer dayDiff);
}
