package com.stpl.tech.kettle.data.expense.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "VOUCHER_COST_CENTER_ALLOCATION")
public class VoucherCostCenterAllocationEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VOUCHER_ALLOCATION_ID", nullable = false)
    private Integer voucherAllocationId;
    @Column(name = "VOUCHER_ID", nullable = false)
    private Integer voucherId;
    @Column(name = "BUSINESS_COST_CENTER_ID", nullable = false)
    private Integer businessCostCenterId;
    @Column(name = "BUSINESS_COST_CENTER", nullable = false)
    private String businessCostCenter;
    @Column(name = "ALLOCATED_ISSUED_AMOUNT", nullable = false)
    private BigDecimal allocatedIssuedAmount;
    @Column(name = "FORCE_ALLOCATION", nullable = false)
    private String forceAllocation;
    @Column(name = "ISSUED_TIME", nullable = false)
    private Date issuedTime;
}
