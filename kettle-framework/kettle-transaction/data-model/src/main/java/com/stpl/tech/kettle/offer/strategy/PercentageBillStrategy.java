/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;

public class PercentageBillStrategy extends AbstractBillStrategy implements OfferActionStrategy {

	StringBuffer offerMessage = new StringBuffer();
	String messageTemplate = "Applied discount of %.2f on order <br/>";

	/**
	 * PERCENTAGE BILL = Apply % value discount to this order.
	 * <p>
	 * Here we will update the discount details of the order. Rest of the
	 * calculations will be done by the UI before punching of the order.
	 * 
	 */
	@Override
	public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems) {

		BigDecimal discountPercentage = new BigDecimal(coupon.getOffer().getOfferValue());
		return apply(offerOrder, coupon, discountPercentage);
	}

	@Override
	public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache,
			Map<String, OrderItem> foundItems, BigDecimal offerValue) {
		if (offerValue == null) {
			return applyStrategy(order, coupon, cache, foundItems);
		} else {
			return apply(order,coupon,offerValue);
		}
	}

	@Override
	public String getOfferMessage() {
		return offerMessage.toString();
	}

	private OfferOrder apply(OfferOrder offerOrder, CouponDetail coupon, BigDecimal offerValue){
		BigDecimal discountPercentage = offerValue;
		updateOrderDiscount(offerOrder, coupon.getCode(), discountPercentage);
		offerMessage.append(String.format(messageTemplate,
				offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount().getValue()));
		updateOrderItemDiscount(offerOrder, coupon.getCode(), discountPercentage);
		offerOrder.setAppliedOfferMessage(getOfferMessage());
		return offerOrder;
	}

	@Override
	public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
			BigDecimal paidAmount, BigDecimal maxDiscountValue) {
		return getOrderDiscount(couponCode, discount, totalAmount);
	}

}
