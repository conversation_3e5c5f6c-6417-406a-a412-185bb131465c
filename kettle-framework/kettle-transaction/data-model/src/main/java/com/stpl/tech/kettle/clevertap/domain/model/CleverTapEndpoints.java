package com.stpl.tech.kettle.clevertap.domain.model;

public enum CleverTapEndpoints {
    UPLOAD_PROFILE_OR_EVENT("https://api.clevertap.com/1/upload", "https://api.clevertap.com/1/upload"),
    GET_USER_PROFILE("https://api.clevertap.com/1/profile.json?identity={identity}", "https://api.clevertap.com/1/profile.json?identity={identity}");

    private String dev;
    private String prod;

    CleverTapEndpoints(String dev, String prod) {
        this.dev = dev;
        this.prod = prod;
    }

    public String getUrl(boolean isDev) {
        if (isDev) {
            return this.dev;
        } else {
            return this.prod;
        }

    }
}
