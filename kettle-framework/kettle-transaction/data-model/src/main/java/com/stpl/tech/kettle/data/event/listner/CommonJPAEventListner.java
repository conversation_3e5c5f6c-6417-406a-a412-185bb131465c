package com.stpl.tech.kettle.data.event.listner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.persistence.PostPersist;

import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.service.impl.CleverTapDataPushServiceImpl;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.customer.service.CustomerDataLookupService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerDataLookup;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.spring.crypto.DataEncrypter;
import com.stpl.tech.spring.service.util.SpringBeanProvider;
import com.stpl.tech.util.AppUtils;

public class CommonJPAEventListner {

	private void pushLeadData(CustomerInfo customerInfo) {
		CleverTapDataPushService cleverTapDataPushService = SpringBeanProvider
				.getBean(CleverTapDataPushServiceImpl.class);
		EnvironmentProperties properties = SpringBeanProvider.getBean(EnvironmentProperties.class);

		if (properties.getClevertapLeadSources().contains(customerInfo.getAcquisitionSource())
				&& properties.getCleverTapEnabled()) {
			Map<String, Object> data = new HashMap<>();
			if (Objects.nonNull(customerInfo.getFirstName())) {
				data.put("Name", customerInfo.getFirstName());
			}
			if (Objects.nonNull(customerInfo.getCustomerId())) {
				data.put("CustomerId", customerInfo.getCustomerId());
			}
			if (StringUtils.isNotBlank(customerInfo.getAcquisitionSource())) {
				data.put("AcquisitionSource", customerInfo.getAcquisitionSource());
			}
			if (StringUtils.isNotBlank(customerInfo.getSmsSubscriber())) {
				data.put("MSG-sms", AppUtils.getStatus(customerInfo.getSmsSubscriber()));
			}
			if (StringUtils.isNotBlank(customerInfo.getOptWhatsapp())) {
				data.put("MSG-whatsapp", AppUtils.getStatus(customerInfo.getOptWhatsapp()));
			}
			if (StringUtils.isNotBlank(customerInfo.getContactNumber())) {
				data.put("Phone", customerInfo.getCountryCode() + customerInfo.getContactNumber());
			}
			cleverTapDataPushService.uploadProfileAttributes(customerInfo.getCustomerId(),
					customerInfo.getAddTime().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
			cleverTapDataPushService.publishCustomEvent(customerInfo.getCustomerId(), CleverTapEvents.LEAD_GENERATED,
					customerInfo.getAddTime().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
		}

	}

	@PostPersist
	private void AfterOperation(Object object) {
		ThreadPoolTaskExecutor executor = SpringBeanProvider.getBean("taskExecutor", ThreadPoolTaskExecutor.class);
		DataEncrypter dataEncrypter = SpringBeanProvider.getBean("dataEncrypter", DataEncrypter.class);
		CustomerDataLookupService dataLookupService = SpringBeanProvider.getBean("customerDataLookupService",
				CustomerDataLookupService.class);
		executor.execute(() -> {
			List<CustomerDataLookup> customerDataLookups = new ArrayList<>();
			if (object instanceof CustomerInfo) {
				CustomerInfo customerInfo = (CustomerInfo) object;
//				customerDataLookups
//						.add(new CustomerDataLookup(customerInfo.getCustomerId(), customerInfo.getContactNumber(),
//								customerInfo.getEmailId(), dataEncrypter.encryptData(customerInfo.getContactNumber()),
//								dataEncrypter.encryptData(customerInfo.getEmailId()), AppConstants.CUSTOMER_INFO));
				pushLeadData(customerInfo);
			}
//			else if (object instanceof CustomerContactInfoMapping) {
//				CustomerContactInfoMapping customerContactInfoMapping = (CustomerContactInfoMapping) object;
//				customerDataLookups.add(new CustomerDataLookup(customerContactInfoMapping.getCustomerId(),
//						customerContactInfoMapping.getOldContactNumber(), null,
//						dataEncrypter.encryptData(customerContactInfoMapping.getOldContactNumber()), null,
//						AppConstants.CUSTOMER_CONTACT_INFO_MAPPING_OLD));
//				customerDataLookups.add(new CustomerDataLookup(customerContactInfoMapping.getCustomerId(),
//						customerContactInfoMapping.getNewContactNumber(), null,
//						dataEncrypter.encryptData(customerContactInfoMapping.getNewContactNumber()), null,
//						AppConstants.CUSTOMER_CONTACT_INFO_MAPPING));
//			} else if (object instanceof WebOfferCouponRedemptionDetail) {
//				WebOfferCouponRedemptionDetail webOfferCouponRedemptionDetail = (WebOfferCouponRedemptionDetail) object;
//				customerDataLookups.add(new CustomerDataLookup(webOfferCouponRedemptionDetail.getKeyId(),
//						webOfferCouponRedemptionDetail.getPhoneNumber(), null,
//						dataEncrypter.encryptData(webOfferCouponRedemptionDetail.getPhoneNumber()), null,
//						AppConstants.WEB_OFFER_COUPON_REDEMPTION_DETAIL));
//			}
//
//			dataLookupService.updateAll(customerDataLookups);
		});
	}
}
