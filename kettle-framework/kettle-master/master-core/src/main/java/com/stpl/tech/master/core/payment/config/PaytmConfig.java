/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.payment.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;

import com.stpl.tech.util.EnvType;

@Configuration
@PropertySource({ "classpath:props/paytm-${env.type}.properties" })
public class PaytmConfig {

	@Autowired
	private Environment env;

	public PaytmConfig() {
		super();
	}

	public EnvType getEnvironmentType() {
		return EnvType.valueOf(env.getProperty("paytm.environment.type"));
	}
	
	public String getWebsite() {
        return env.getProperty("paytm.website.url");
    }

    public String getIndustryTypeId() {
        return env.getProperty("paytm.industry.type.id");
    }

    public String getChannelId() {
        return env.getProperty("paytm.channel.id");
    }

    public String getMid() {
        return env.getProperty("paytm.mid");
    }

    public String getMerchantKey() {
        return env.getProperty("paytm.merchant.key");
    }

    public String getBaseUrl() {
        return env.getProperty("paytm.base.url");
    }

    public String getAuthKey() {
        return env.getProperty("paytm.auth.key");
    }

    public String getCallBackUrl() {
        return env.getProperty("paytm.callback.url");
    }

    //////////////////////////// KIOSK PAYTM METHODS ////////////////////////////

    public String getKIOSKBaseUrl() {
        return env.getProperty("kiosk.paytm.base.url");
    }

    public String getKIOSKMid() {
        return env.getProperty("kiosk.paytm.mid");
    }

    public String getKIOSKBusinessType() {
        return env.getProperty("kiosk.paytm.business.type");
    }

    public String getPaytmUpiBusinessType() {
        return env.getProperty("kiosk.paytm.upi.business.type");
    }

    public String getKIOSKMerchantKey() { return env.getProperty("kiosk.paytm.merchant.key");}

    public String getKIOSKChannelId() {
        return env.getProperty("kiosk.paytm.channel.id");
    }

    public String getKIOSKClientId() {
        return env.getProperty("kiosk.paytm.client.id");
    }

    public String getKIOSKVersion() {
        return env.getProperty("kiosk.paytm.version");
    }

    //////////////////////////// KIOSK PAYTM METHODS ////////////////////////////

    public String getDineInPaytmBaseUrl() {
        return env.getProperty("dinein.paytm.base.url");
    }

    public String getDineInPaytmMid() {
        return env.getProperty("dinein.paytm.mid");
    }

    public String getDineInPaytmInductryTypeId() {
        return env.getProperty("dinein.paytm.industry.type.id");
    }

    public String getDineInPaytmMerchantKey() { return env.getProperty("dinein.paytm.merchant.key");}

    public String getDineInPaytmChannelIdWeb() {
        return env.getProperty("dinein.paytm.channel.id.web");
    }

    public String getDineInPaytmChannelIdApp() {
        return env.getProperty("dinein.paytm.channel.id.app");
    }

    public String getDineInPaytmWebsiteWeb() {
        return env.getProperty("dinein.paytm.website.web");
    }

    public String getDineInPaytmWebsiteApp() {
        return env.getProperty("dinein.paytm.website.app");
    }

    public String getDineInPaytmClientId() {
        return env.getProperty("dinein.paytm.client.id");
    }

    public String getDineInPaytmClientSecret() {
        return env.getProperty("dinein.paytm.client.secret");
    }

    public String getPaytmOauthBaseUrl(){
	    return env.getProperty("dinein.paytm.oauth.base.url");
    }

    public String getMaxPaytmStatusCheck() {return  env.getProperty("dinein.paytm.max.status.check"); }

    public String getDineInCallBackUrl() {
        return  env.getProperty("dinein.paytm.callback.url");
    }

    public String getKettlePaytmRefundUrl() {
        return env.getProperty("kettle.paytm.refund.url");
    }
}