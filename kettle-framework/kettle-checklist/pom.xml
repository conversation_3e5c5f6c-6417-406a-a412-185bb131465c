<?xml version="1.0"?>
<!-- ~ Copyright (C) 2016, Sunshine Teahouse Private Limited - All Rights
	Reserved ~ Unauthorized copying of this file, via any medium is strictly
	prohibited ~ Proprietary and confidential ~ Written by Chaayos Technology
	Team, 2015 -->

<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.stpl.tech.kettle</groupId>
        <artifactId>kettle-framework</artifactId>
        <version>4.1.0-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.stpl.tech.kettle.checklist</groupId>
    <artifactId>kettle-checklist</artifactId>
    <name>kettle-checklist</name>
    <packaging>jar</packaging>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <url>http://maven.apache.org</url>
<!--    <build>-->
<!--        <plugins>-->
<!--            &lt;!&ndash; <plugin>-->
<!--                    <groupId>org.jvnet.jaxb2.maven2</groupId>-->
<!--                    <artifactId>maven-jaxb2-plugin</artifactId>-->
<!--                    <version>0.11.0</version>-->
<!--                    <executions>-->
<!--                        <execution>-->
<!--                            <id>domain-schema</id>-->
<!--                            <phase>generate-sources</phase>-->
<!--                            <goals>-->
<!--                                <goal>generate</goal>-->
<!--                            </goals>-->
<!--                            <configuration>-->
<!--                                <schemaDirectory>src/main/xsds</schemaDirectory>-->
<!--                                <generatePackage>com.stpl.tech.checklist.model</generatePackage>-->
<!--                                <generateDirectory>${project.build.directory}/generated-sources/xjc-domain</generateDirectory>-->
<!--                            </configuration>-->
<!--                        </execution>-->
<!--                    </executions>-->
<!--                </plugin> &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-war-plugin</artifactId>-->
<!--                <version>2.6</version>-->
<!--                <configuration>-->
<!--                    <webXml>WebContent/WEB-INF/web.xml</webXml>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--        <finalName>kettle-checklist</finalName>-->
<!--    </build>-->
    <build>
        <finalName>${project.artifactId}</finalName>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>data-model</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
