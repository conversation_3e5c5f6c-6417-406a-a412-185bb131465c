<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration  -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listinfo/logback-user             -->
<!--                                                                -->
<!-- For professional support please see                            -->
<!--    http://www.qos.ch/shop/products/professionalSupport         -->
<!--                                                                -->
<configuration>
    <!-- Errors were reported during translation. -->
    <!-- No class found for appender CHECKLIST -->
    <!-- Could not find transformer for null -->
    <appender name="CHECKLIST">
        <!--No layout specified for appender named [CHECKLIST] of class [null]-->
    </appender>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <logger name="o.s.w.c.s" additivity="false" level="INFO">
        <appender-ref ref="CHECKLIST"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="CHECKLIST"/>
    </root>
</configuration>