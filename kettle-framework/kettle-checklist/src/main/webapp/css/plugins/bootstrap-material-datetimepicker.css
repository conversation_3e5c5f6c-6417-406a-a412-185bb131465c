.dtp { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.4); z-index: 2000; }
.dtp > .dtp-content { background: #fff; max-width: 300px; box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); max-height: 500px; position: relative; left: 50%; }
.dtp > .dtp-content > .dtp-date-view > header.dtp-header { background: #2C6AF9; color: #fff; text-align: center; padding: 0.3rem; }

.dtp div.dtp-date, .dtp div.dtp-time { background: #2196F3; text-align: center; color: #fff; padding: 10px; }
.dtp div.dtp-date > div { padding: 0; margin: 0; }
.dtp div.dtp-actual-month { font-size: 1.5em; }
.dtp div.dtp-actual-num { font-size: 3em; line-height: 0.9; }
.dtp div.dtp-actual-maxtime { font-size: 3em; line-height: 0.9; }
.dtp div.dtp-actual-year { font-size: 1.5em; color: #DCEDC8; }
.dtp div.dtp-picker { padding: 1rem; text-align: center; }

.dtp div.dtp-picker-month, .dtp div.dtp-actual-time { font-weight: 500; text-align: center; }

.dtp .dtp-close { position: absolute; top: 0.5em; right: 1rem; }
.dtp .dtp-close > a { color: #fff; }
.dtp .dtp-close > a > i { font-size: 1em; }

.dtp table.dtp-picker-days { margin: 0; min-height: 251px;}
.dtp table.dtp-picker-days, .dtp table.dtp-picker-days tr, .dtp table.dtp-picker-days tr > td { border: none; }
.dtp table.dtp-picker-days tr > td {  font-weight: 700; font-size: 1.2rem; text-align: center; padding: 1rem 0.3rem; }
.dtp table.dtp-picker-days tr > td > span.dtp-select-day { color: #BBB1B1 !important; }
.dtp table.dtp-picker-days tr > td > a, .dtp .dtp-picker-time > a { color: #BBB1B1; text-decoration: none; padding: 0.4rem 0.5rem 0.5rem 0.6rem; border-radius: 50%!important; }
.dtp table.dtp-picker-days tr > td > a.selected{ background: #2196F3; color: #fff; }
.dtp table.dtp-picker-days tr > th { color: #BBB1B1; text-align: center; font-weight: 700; padding: 0.4rem 0.3rem; }

.dtp .p10 > a { color: #2C6AF9; text-decoration: none; }
.dtp .p10 { width: 10%; display: inline-block; }
.dtp .p20 { width: 20%; display: inline-block; }
.dtp .p60 { width: 60%; display: inline-block; }
.dtp .p80 { width: 80%; display: inline-block; }

.dtp a.dtp-meridien-am, .dtp a.dtp-meridien-pm { position: relative; top: 10px; color: #BBB1B1; font-weight: 500; padding: 0.7rem 0.5rem; border-radius: 50%!important;text-decoration: none; background: #eee; font-size:1rem; }
.dtp .dtp-actual-meridien a.selected { background: #2C6AF9; color: #fff; }

.dtp .dtp-picker-time > a { display: block; line-height: 23px; padding: 0.3rem 0.3rem 0.3rem 0.3rem; }
.dtp .dtp-picker-time { position: absolute;width: 30px;height: 30px;font-size: 1em;border-radius: 50%;cursor: pointer;font-weight: 500;text-align: center!important; }

.dtp .dtp-picker-time > a.dtp-select-hour.selected { background: #2C6AF9; color: #fff; }
.dtp .dtp-picker-time > a.dtp-select-hour.disabled, .dtp .dtp-picker-time > a.dtp-select-minute.disabled { color: #757575; }
.dtp .dtp-picker-time > a.dtp-select-minute.selected { background: #2196F3; color: #fff; }

.dtp div.dtp-picker-clock { margin: 1rem 2rem 0 2rem; padding: 1rem; border-radius: 50%!important; background: #eee; }
.dtp-clock-center { width: 15px; height: 15px; background: #757575; border-radius: 50%; position: absolute; z-index: 50; }

.dtp .dtp-hand, .dtp .dtp-hour-hand { position: absolute; width: 4px; margin-left: -2px; background: #BDBDBD; -moz-transform: rotate(0deg); -ms-transform: rotate(0deg); -webkit-transform: rotate(0deg); transform: rotate(0deg); -moz-transform-origin: bottom; -ms-transform-origin: bottom; -webkit-transform-origin: bottom; transform-origin: bottom; z-index: 1; }
.dtp .dtp-minute-hand { width: 2px; margin-left: -1px; }
.dtp .dtp-hand.on { background: #2196F3; }

.dtp .dtp-buttons { padding: 0 1rem 1rem 1rem; text-align: right; }

.dtp.hidden, .dtp .hidden { display: none; }
.dtp .invisible { visibility: hidden; }

.dtp .left { float: left; }
.dtp .right { float: right; }
.dtp .clearfix { clear: both; }

.dtp .center { text-align: center; }