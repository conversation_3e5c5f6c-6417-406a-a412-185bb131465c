

body {
  font-family: "Source Sans Pro","Helvetica Neue",Helvetica,Arial,sans-serif;
  font-size: 10px;
  background-color:#f0f3f4;
  line-height: 1.42857143 !important;
  color: #080808 !important;
}

a,
a:hover {
  -webkit-text-decoration: none;
  -moz-text-decoration: none;
  -ms-text-decoration: none;
  -o-text-decoration: none;
  text-decoration: none;
  color: #2196F3;
  font-weight: bold;
}

input:focus,
textarea:focus {
  outline: none !important;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075) !important;
  -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075) !important;
  -ms-box-shadow: inset 0 1px 1px rgba(0,0,0,.075) !important;
  -o-box-shadow: inset 0 1px 1px rgba(0,0,0,.075) !important;
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075) !important;
  border:1px solid #ddd !important;
}

video,
audio {
  width: 100% !important;
  height: auto !important;
}

code {text-align: left !important;}

input[type='text'] {
  border: 1px solid #ddd;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
  padding: 10px;
  border-radius: 0px !important;
}

select{
  border-radius: 0px !important;
  /* border:1px solid #ddd; */
 /*  border:1px solid #ddd; */
  padding: 7px;
  padding-left: 20px;
  padding-right: 20px;
}

label{ font-weight:400; }


th { border-bottom: none !important; }

td {
  padding: 10px !important;
  /* border:1px solid #f4f4f4 !important; */
}

ul li{
  list-style: none;
}

p.quote{
  font-size: 18px;
  padding: 10px;
}
  p.quote:before {
    color: #fff;
    font-family: "times new roman";
    content: open-quote;
    font-size: 4em;
    line-height: .1em;
    margin-right: .25em;
    vertical-align: -.4em;
  }


.content-header .content-header-tab .active a{
  background: none !important;
}

.right-option-v1{
  position: absolute;
  right: 0;
}
.right-option-v1 .right-option-v1-icon{
  font-size: 1.4em !important;
  margin-top: 20px;
  cursor: pointer;
}

.content-header{
  background: #f6f8f8;
  border-bottom: 1px solid #ddd !important;
 }

.thumbnail{ border:none !important;}


/*start: font color*/
 .text-white{color: #fff !important;}
/*end: font color*/

/*background*/
 .bg-none    {background: none !important;}
 .bg-primary {background-color: #1c84c6 !important;}
 .bg-success {background-color: #27C24C !important;}
 .bg-info    {background-color: #73CAEF !important;}
 .bg-danger  {background-color: #FEC8C3 !important;}
 .bg-warning {background-color: #F0AD4E !important;}
 .bg-default {background-color: #857E7E !important;}

 .bg-white       {background-color: #FFF !important;}
 .bg-red         {background-color: #F44336 !important;}
 .bg-pink        {background-color: #E91E63 !important;}
 .bg-purple      {background-color: #9C27B0 !important;}
 .bg-deep-purple {background-color: #673AB7 !important;}
 .bg-indigo      {background-color: #3F51B5 !important;}
 .bg-blue        {background-color: #2196F3 !important;}
 .bg-light-blue  {background-color: #03A9F4 !important;}
 .bg-cyan        {background-color: #00BCD4 !important;}
 .bg-teal        {background-color: #009688 !important;}
 .bg-green       {background-color: #4CAF50 !important;}
 .bg-light-green {background-color: #8BC34A !important;}
 .bg-lime        {background-color: #CDDC39 !important;}
 .bg-yellow      {background-color: #FFEB3B !important;}
 .bg-amber       {background-color: #FFC107 !important;}
 .bg-orange      {background-color: #FF9800 !important;}
 .bg-deep-orange {background-color: #FF5722 !important;}
 .bg-brown       {background-color: #795548 !important;}
 .bg-grey        {background-color: #9E9E9E !important;}
 .bg-light-grey  {background-color: #f0f3f4 !important;}
 .bg-blue-grey   {background-color: #607D8B !important;}

 .bg-dark-red         {background-color: #d32f2f !important;}
 .bg-dark-pink        {background-color: #c2185b !important;}
 .bg-dark-purple      {background-color: #7b1fa2 !important;}
 .bg-dark-deep-purple {background-color: #512da8 !important;}
 .bg-dark-indigo      {background-color: #303f9f !important;}
 .bg-dark-blue        {background-color: #1976d2 !important;}
 .bg-dark-light-blue  {background-color: #0288d1 !important;}
 .bg-dark-cyan        {background-color: #0097a7 !important;}
 .bg-dark-teal        {background-color: #00796b !important;}
 .bg-dark-green       {background-color: #388e3c !important;}
 .bg-dark-light-green {background-color: #689f38 !important;}
 .bg-dark-lime        {background-color: #afb42b !important;}
 .bg-dark-yellow      {background-color: #fbc02d !important;}
 .bg-dark-amber       {background-color: #ffa000 !important;}
 .bg-dark-orange      {background-color: #f57c00 !important;}
 .bg-dark-deep-orange {background-color: #e64a19 !important;}
 .bg-dark-brown       {background-color: #5d4037 !important;}
 .bg-dark-grey        {background-color: #616161 !important;}
 .bg-dark-blue-grey   {background-color: #455a64 !important;}
/*end background*/

/*start: Mega Menu*/

.menu-large {
  position: static !important;
   color: #918C8C !important;
}

.menu-large .sub-megamenu{
  border-right: 1px solid #ddd;
}

.megamenu{
  padding: 20px 0px;
  width:100%;
  border:none;
  margin-top: -5px !important;
}

.megamenu> li > ul {
  padding: 0;
  margin: 0;
}
.megamenu> li > ul > li {
  list-style: none;
}
.megamenu> li > ul > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 500;
  line-height: 1.428571429;
  color: #333333;
  white-space: normal;
}
.megamenu> li ul > li > a:hover,
.megamenu> li ul > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.megamenu.disabled > a,
.megamenu.disabled > a:hover,
.megamenu.disabled > a:focus {
  color: #999999;
}
.megamenu.disabled > a:hover,
.megamenu.disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.megamenu.dropdown-header {
  color: #428bca;
  font-size: 18px;
}

/*end: Mega Menu*/


/*start: Lock Screen*/
 .lock-screen-v1 img{
   margin: 0 auto;
   margin-bottom: 20px;
   width: 200px;
   border:4px solid #fff;
 }
/*end: Lock Screen*/

/*start: page 404*/
 .page-404{padding-top: 10%;font-size: 20px;}
 .page-404 img{margin-bottom: 2px;width: 350px;}
 .page-404 a{margin-left: 30px;}
 .page-404 a span{margin-left: 20px;}
/*end: page 404*/

/*start: price*/
 .price .panel-header{
   padding: 10px;
 }
 .price sub{
    font-size: 17px;
    font-weight:bold;
  }
  .price .price-money{padding: 30px;}
 .price .list-group-item{
    border-color: #eee !important;
    font-size: 13px;
    padding: 10px;
  }
 .price sup{top: -20px;font-size: 20px;}
 .price .panel-header span{font-size: 5em;font-weight: 600;}
/*end: price*/

/*start:product grid*/
 .product-location{
   position: absolute;
   z-index: 9;
   padding: 10px;
   font-size: 17px;
 }

 .product-grid .rate{
   color: #F0AD4E !important;
   font-size: 15px;
 }

 .product-grid img{
   width: 100% !important;
 }

 .product-location .pull-right{
   margin-right: 40px;
 }

 .product-price{
   background-color: #fff;
   height: 40px;
   width: 80px;
   z-index: 9;
   padding-left: 10px;
   position: absolute;
 }
 
 .product-price-bottom{
   top: 49%;
 }
/*end: product grid*/

/*start: Login*/
 .form-signin-wrapper{background: #fff !important;}
 .form-signin{
         max-width: 330px;
         padding: 15px;
         margin: 0 auto;
    }
    .form-signin a{color: #fff !important;font-weight: bold;}
    .form-signin .btn{margin-top: 15px;}
    .form-signin .form-group{margin-bottom: 20px !important;}
    .form-signin .panel{background: #50773e;color: #fff;padding: 10px;border:none !important;box-shadow: 0 7px 16px #50773e, 0 4px 5px #50773e;}
    .form-signin .panel-body{padding-bottom: 50px;padding-top: 30px;}
    .form-signin .atomic-number{font-size: 20px;}
    .form-signin .atomic-symbol{font-size: 10em !important;font-weight: 500;padding-bottom: 20spx;}
    .form-signin .element-name,.form-signin .atomic-mass{font-size: 18px;margin-top: -10px;font-weight: 600;line-height: 1.2;}
    .form-signin label{color:#fff !important;}
    .form-signin .bar:before,.form-signin .bar:after{background-color:#fff !important;}
/*end: login*/

/*start:profile v1*/
      .profile-v1{}
        .profile-v1 .profile-v1-cover{
          top: 0;
          max-height: 300px;
          min-height: 300px;
          padding: 0px;
          margin-bottom: 20px;
          overflow: hidden;
        }
        .profile-v1 .profile-v1-body{padding-right: 0px;}
        .profile-v1 .profile-v1-name h2{font-weight: bold;color: #fff;text-shadow: -1px -1px 1px #111111, 2px 2px 1px #363636;}
        .profile-v1 .profile-v1-cover img{min-width: 100%;}
        .profile-v1 .profile-v1-pp{z-index:9;position: absolute;margin: 20px;}
        .profile-v1 .profile-v1-pp h2{padding-left: 20px;color:#fff;}
        .profile-v1 .profile-v1-pp .btn{margin-left: 20px;}
        .profile-v1 .profile-v1-pp img{border:2px solid #fff;margin-left: 20px;width: 100px;height: 100px;}
        .profile-v1 .profile-v1-right{padding-left: 0px;}
        .profile-v1 .profile-v1-right .sub-profile-v1-right{min-height: 150px;background: #fff;}
        .profile-v1 .profile-v1-right .sub-profile-v1-right h1{font-size: 4em;}
        .profile-v1 .profile-v1-right .sub-profile-v1-right p{margin-top: -10px;}
        .profile-v1 .profile-v1-right  .sub-profile-v1-right1{padding: 20px;}
        .profile-v1 .profile-v1-right  .sub-profile-v1-right2{color:#fff;background: #6254b2; box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);padding:20px;}
        .profile-v1 .profile-v1-right  .sub-profile-v1-right3{color:#fff;background: #2196f3;padding: 20px;}
        .profile-v1 .profile-v1-right  .sub-profile-v1-right3:after{
            position: absolute;
            top: -10px;
            left: 132px;
            display: inline-block;
            border-right: 10px solid transparent;
            border-bottom: 10px solid  #2196f3;
            border-left: 10px solid transparent;
            content: '';
        }
/*end: profile*/

/*start: search v1*/
  .search-v1{}
    .search-v1 .panel,.search-v1 .panel-body{background: #fbfbfb;}
    .search-v1 .tabs-area{border:none !important;margin-top: -18px;background: #fff;}
    .search-v1 #tabs-demo6 li{padding: 5px !important;}
    .search-v1 #tabs-demo6 li.active:after{
      border-color: rgba(185, 9, 8, 0);
      border-top-color: #fbfbfb;
      margin-top: 18px;
      z-index: 9
    }
    .search-v1 .search-v1-avatar{width: 80px;height: 80px;border-radius: 100%;margin-right: 10px;}
    .search-v1 #tabs-demo6,.search-v1 #tabs-demo6 a{background-color: #fbfbfb !important;}
    .search-v1 .tab-pane{padding: 20px;}
    .search-v1 .search-v1-menu1 .media-left{font-size: 2em;line-height: 0.7;font-weight: bold;padding-top: 10px;}
    .search-v1 .search-v1-menu1 .media-heading{font-size:17px;color: #2196F3 !important;font-weight: 600 !important;}
    .search-v1 .media{padding: 20px;}
    .search-v1 .media:nth-child(2n+2){
      background-color: rgba(0,0,0,0.02) !important;
    }
    .search-v1 .panel{
      border:none !important;
      -webkit-box-shadow: none !important;
      -moz-box-shadow: none !important;
      -ms-box-shadow: none !important;
      -o-box-shadow: none !important;
      box-shadow: none !important;
    }

    .search-v1 #search-v1-images{position: absolute;}
   
    .search-v1 #search-v1-images .post { height: 100px; background: cyan; margin-bottom: 20px; }
    .search-v1 #search-v1-images .tall { height: 200px; }
}
/*end: search v1*/


/*start: article v1*/
  .article-v1{}
    .article-v1 .article-v1-body 
    {
      padding: 100px;
      padding-top: 0px;
    }
    .article-v1 .article-v1-body p{
      margin: auto auto 1.5em;
      font-weight: 400;
      font-family: serif;
      font-size: 18px;
      text-indent: 2em;
    }
    .article-v1 .article-v1-title h2{color:#777272;padding: 40px;font-weight: bold !important;}
    .article-v1 .article-v1-footer{padding: 100px;padding-top: 0px;}
    .article-v1 .article-v1-footer img{height: 100px;height: 100px;}
    .article-v1 .article-v1-footer .article-v1-comment{
      padding: 20px;
      margin-top: 50px;
      background-color: rgba(0,0,0,0.02) !important;
    }
    .article-v1 .article-v1-footer .article-v1-comment:after{
         position: absolute;
        top: -10px;
        left: 4px;
        display: inline-block;
        border-right: 10px solid transparent;
        border-bottom: 10px solid  rgba(0,0,0,0.02);
        border-left: 10px solid transparent;
        content: '';
    }
    .article-v1 .article-v1-time{padding: 20px;font-size: 18px;}
/*end: article v1*/


/*start: invoice*/
.invoice .invoice-logo{
  width: 70px;
  float: left;
  margin-top: -20px;
  margin-right: 10px;
}
.invoice .invoice-v1-content{
  padding: 20px;
  padding-top: 50px;
  margin-top: 80px;
  color: #5C5757;
} 

.invoice .invoice-v1-content table td,.invoice .invoice-v1-content table th{
  padding: 20px !important;
}
/*end: invoice*/


.box-shadow-none,.box-shadow-none:focus{
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  -o-box-shadow: none !important;
  box-shadow: none !important;
}
.border-none,.border-none:focus{
  border:none !important;
}

.chaayos-wrapper{
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.search-nav .form-animate-text{
  margin-bottom: 0px !important;
}

.panel{
  border-radius:0px;
  padding: 0px;
  border: none;
}

.panel-heading{
  line-height: 1.42857143 !important;
  color: #58666e !important;
}

.panel-heading,.fc-head{
  padding: 0px;
  background: #F9F9F9;
  padding: 5px;
  padding-left: 20px;
  border-bottom: 1px solid #ddd;
}

/*dashboard boxed*/
.boxed{background:#ECECEC;}
  .boxed #left-menu .sub-left-menu{
    position: relative !important;
    float: left;
    padding-bottom: 100%;
  }
  .boxed .chaayos-wrapper{
    -webkit-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
    -moz-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
    -ms-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
    -o-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
    box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
  }
  .boxed #content .col-md-12{padding-right: 0px !important;}
  .boxed #content .panel{display: table;width: 100%;} 
  .boxed .panel{box-shadow: none !important;}  

/*dashboard top navigation*/

.topnav #content{
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.topnav .navbar-header .navbar-right{
  margin-right: 5px;
}

.topnav .navbar-header li.active{
  border-bottom: 3px solid #fff;
  background:none !important;
  padding-bottom: 3px;
}

.topnav .navbar-header li.active a{
  background:none !important;
}

.topnav .dropdown-menu{
  margin-top:5px;
}

.topnav .navbar-header li.active:after,.user-name:after,.avatar-dropdown:after,.navbar-header .dropdown-menu li:after{
  background:none !important;
}

.topnav .navbar-header li:after{
  position:absolute;
  bottom:-6px;
  left:50%;
  height:3px;
  width:0%;
  background-color:#fff;
  display:block;
  content:'';
  transition:0.3s;
  top: 52px !important;
}

.topnav .navbar-header li:hover:after{
  left:0;
  width:100%;
}



.navbar-header a{
  color: #fff !important;
  font-size: 16px;
}

.dropdown a{
  font-size: 16px !important;
}
.dropdown.open a{
  background:none !important;
}
.navbar-header .dropdown-menu a{
  color: #716767 !important;
  font-size: 14px !important;
}

.input-group-addon{background-color: #fff;font-weight: 100;}
.form-control[type='text']:focus,
.form-control[type='password']:focus {
  border: 1px solid #BBB1B1;
}
.top-20{margin-top: 20px;}
.form-control.border-bottom[type='text'],
.form-control.border-bottom[type='password'] {
  border: none !important;
  border-bottom: 1px solid #ddd !important;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
}

.form-control.border-left[type='text'],
.form-control.border-left[type='password'] {
  border: none !important;
  border-left: 3px solid #ddd !important;
  background-color: #f4f4f4;
  color: #BBB1B1;
}

.form-control.android[type='text'],
.form-control.android[type='password'] {
  width: 100%%;
  font-family: "Roboto", "Droid Sans", sans-serif;
  font-size: 16px;
  margin: 0;
  padding: 8px 8px 6px 8px;
  position: relative;
  display: block;
  outline: none;
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
  background: bottom left linear-gradient(#a9a9a9, #a9a9a9) no-repeat, bottom center linear-gradient(#a9a9a9, #a9a9a9) repeat-x, bottom right linear-gradient(#a9a9a9, #a9a9a9) no-repeat;
  -webkit-background-size: 1px 6px, 1px 1px, 1px 6px;
  -moz-background-size: 1px 6px, 1px 1px, 1px 6px;
  -ms-background-size: 1px 6px, 1px 1px, 1px 6px;
  -o-background-size: 1px 6px, 1px 1px, 1px 6px;
  background-size: 1px 6px, 1px 1px, 1px 6px;
}

.form-control.android[type='text']:hover,
.form-control.android[type='password']:hover,
.form-control.android[type='text']:focus,
.form-control.android[type='password']:focus {
  background: bottom left linear-gradient(#0099cc, #0099cc) no-repeat, bottom center linear-gradient(#0099cc, #0099cc) repeat-x, bottom right linear-gradient(#0099cc, #0099cc) no-repeat;
  -webkit-background-size: 1px 6px, 1px 1px, 1px 6px;
  -moz-background-size: 1px 6px, 1px 1px, 1px 6px;
  -ms-background-size: 1px 6px, 1px 1px, 1px 6px;
  -o-background-size: 1px 6px, 1px 1px, 1px 6px;
  background-size: 1px 6px, 1px 1px, 1px 6px;
}

.form-control.primary {
  border-color: #1c84c6;
  border-left: 4px solid #1c84c6 !important;
}

.form-control.success {
  border-color: #27C24C;
  border-left:4px solid #27C24C !important;
}

.form-control.info {
  border-color: #73CAEF;
  border-left:4px solid #73CAEF !important;
}

.form-control.danger {
 border-color: #FEC8C3;
 border-left: 4px solid #FF968B !important;
}

.form-control.warning {
  border-color: #F0AD4E;
  border-left: 4px solid #F0AD4E !important;
}

.form-control.default {
  border-color: #857E7E;
  border-left:4px solid #857E7E !important;
}

.modal-example .modal {
  margin-top: 100px;
  position: relative !important;
  top: auto !important;
  overflow: inherit;
  right: auto !important;
  bottom: auto !important;
  left: auto !important;
  z-index: 1 !important;
  display: block !important;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
}

.ripple {
  width: 100%;
  height: 100%;
}

.ripple {
  position: relative;
  overflow: hidden;
}

.ripple a {
  cursor: pointer;
  user-select: none;
  -webkit-text-decoration: none;
  -moz-text-decoration: none;
  -ms-text-decoration: none;
  -o-text-decoration: none;
  text-decoration: none;
  position: relative;
}

ul li a:focus {
  -webkit-text-decoration: none;
  -moz-text-decoration: none;
  -ms-text-decoration: none;
  -o-text-decoration: none;
  text-decoration: none;
}

.ink {
  display: block;
  position: absolute;
  background: #ddd;
  z-index: 228;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.dtp-date-view-ripple .ink.animate {
  -webkit-animation: rippleDate 0.75s linear;
  -moz-animation: rippleDate 0.75s linear;
  -ms-animation: rippleDate 0.75s linear;
  -o-animation: rippleDate 0.75s linear;
  animation: rippleDate 0.75s linear;
  background: #2196F3;
}

/*animation effect*/

.ink.animate {
  -webkit-animation: ripple 0.75s linear;
  -moz-animation: ripple 0.75s linear;
  -ms-animation: ripple 0.75s linear;
  -o-animation: ripple 0.75s linear;
  animation: ripple 0.75s linear;
  cursor: pointer;
}

@keyframes ripple {
  from {
    -webkit-opacity: 0.8;
    -moz-opacity: 0.8;
    -ms-opacity: 0.8;
    -o-opacity: 0.8;
    opacity: 0.8;
    filter: alpha(opacity=80);
  }
  to {
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes rippleDate {
  from {
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
.left {
  float: left;
}

.right {
  float: right;
}

.padding-0 {
  padding: 0px;
}

.fc {
  color: #444;
}

.fc h2 {
  font-weight: 200;
  color: #918C8C;
}



.fc .fc-event {
  padding: 10px;
  background-color: #02A8F3;
  border: none;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
}

.fc td {
  border: 1px solid #F9F9F9;
  font-weight: 200;
}

.fc .fc-bgevent {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
}

.fc .fc-nonbusiness {
  background-color: #F4F4F4 !important;
}

.fc-state-default {
  background-color: #ddd !important;
  background-image: none !important;
  border: none !important;
  -webkit-border-radius: 0px !important;
  -moz-border-radius: 0px !important;
  -ms-border-radius: 0px !important;
  -o-border-radius: 0px !important;
  border-radius: 0px !important;
  color: #868181 !important;
  text-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  -o-box-shadow: none !important;
  box-shadow: none !important;
  height: 40px !important;
  width: 60px !important;
}

.fc-state-default:focus {
  outline: none;
  background-color: #B9B0ED;
}

.progress {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  -o-box-shadow: none !important;
  box-shadow: none !important;
}

.progress.progress-mini {
  height: 5px !important;
}

.progress.progress-small {
  height: 13px !important;
}

.text-primary {
  color: #1c84c6;
}

.text-success {
  color: #27C24C;
}

.text-info {
  color: #73CAEF;
}

.text-danger {
  color: #FF6656;
}

.text-warning {
  color: #F0AD4E;
}

.text-default {
  color: #857E7E;
}

.bg-primary {
  background-color: #1c84c6 !important;
}

.bg-success {
  background-color: #27C24C !important;
}

.bg-info {
  background-color: #73CAEF !important;
}

.bg-danger {
  background-color: #FF6656 !important;
}

.bg-warning {
  background-color: #F0AD4E !important;
}

.bg-default {
  background-color: #857E7E !important;
}

.label {
  padding: 5px;
}

.btn {
  -webkit-border-radius: 16px !important;
  -moz-border-radius: 16px !important;
  -ms-border-radius: 16px !important;
  -o-border-radius: 16px !important;
  border-radius: 16px !important;
  padding-left: 20px;
  padding-right: 20px;
  background-color: #fff;
  color: #857E7E;
  border: 3px solid #ddd;
}

.btn:focus {
  outline: none;
}

.btn-round {
  -webkit-border-radius: 24px !important;
  -moz-border-radius: 24px !important;
  -ms-border-radius: 24px !important;
  -o-border-radius: 24px !important;
  border-radius: 24px !important;
}

.btn.ripple-infinite {
  position: relative;
  overflow: hidden;
}

.btn.btn-circle {
  -webkit-border-radius: 200% !important;
  -moz-border-radius: 200% !important;
  -ms-border-radius: 200% !important;
  -o-border-radius: 200% !important;
  border-radius: 200% !important;
  width: 70px;
  height: 70px;
  padding: 10px;
  font-size: 2em;
}

.btn.btn-circle.btn-lg {
  width: 100px;
  height: 100px;
  font-size: 3em;
}

.btn.btn-circle.btn-sm {
  width: 50px;
  height: 50px;
  font-size: 1em;
}

.btn.btn-circle.btn-mn {
  width: 30px;
  height: 30px;
  padding: 0px;
  font-size: 1em;
}

.btn.ripple-infinite div:hover:before,
.btn.ripple-infinite div:hover:after {
  background: none !important;
}

.btn.ripple-infinite div:before,
.btn.ripple-infinite div:after {
  -webkit-transition: all 0.7s ease !important;
  -moz-transition: all 0.7s ease !important;
  -ms-transition: all 0.7s ease !important;
  -o-transition: all 0.7s ease !important;
  transition: all 0.7s ease !important;
  content: "" !important;
  position: absolute !important;
  background: rgba(255, 255, 255, 0.2) !important;
  z-index: 99 !important;
  left: 15% !important;
  right: 15% !important;
  top: -50% !important;
  bottom: -50% !important;
  -webkit-border-radius: 200% !important;
  -moz-border-radius: 200% !important;
  -ms-border-radius: 200% !important;
  -o-border-radius: 200% !important;
  border-radius: 200% !important;
  -webkit-animation: pulseA ease 1.4s infinite !important;
  -moz-animation: pulseA ease 1.4s infinite !important;
  -ms-animation: pulseA ease 1.4s infinite !important;
  -o-animation: pulseA ease 1.4s infinite !important;
  animation: pulseA ease 1.4s infinite !important;
}

@-webkit-keyframes pulseA {
    from {
      -webkit-transform: scale(1);
      -moz-transform: scale(1);
      -ms-transform: scale(1);
      -o-transform: scale(1);
      transform: scale(1);
    }
    50% {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
    to {
      -webkit-transform: scale(1);
      -moz-transform: scale(1);
      -ms-transform: scale(1);
      -o-transform: scale(1);
      transform: scale(1);
    }
  }

  .btn-primary,
  .label-primary,
  .alert-primary,
  .badge-primary {
    color: #fff !important;
    border: none !important;
    background-color: #1c84c6 !important;
  }

  .btn-success,
  .label-success,
  .alert-success,
  .badge-success {
    color: #fff !important;
    border: none !important;
    background-color: #50773e !important;
  }

  .btn-info,
  .label-info,
  .alert-info,
  .badge-info {
    color: #fff !important;
    border: none !important;
    background-color: #73CAEF !important;
  }

  .btn-danger,
  .label-danger,
  .alert-danger,
  .badge-danger {
    color: #fff !important;
    border: none !important;
    background-color: #FF6656 !important;
  }

  .btn-warning,
  .label-warning,
  .alert-warning,
  .badge-warning {
    color: #fff !important;
    border: none !important;
    background-color: #F0AD4E !important;
  }

  .btn-default,
  .label-default,
  .alert-default,
  .badge-default {
    color: #857E7E !important;
    border: none !important;
    background-color: #fff !important;
  }

  .badges-v1 {
    margin: 10px auto;
    width: 280px;
    height: 370px;
    background: white;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border-radius: 10px;
    -webkit-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    -ms-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    -o-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 90;
  }

  .badges-ribbon {
    width: 85px;
    height: 88px;
    overflow: hidden;
    position: absolute;
    top: -3px;
    right: -3px;
  }

  .badges-ribbon-content {
    font: bold 15px Sans-Serif;
    color: #333;
    text-align: center;
    padding: 7px 0;
    left: -5px;
    top: 15px;
    width: 120px;
    -webkit-box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
    -ms-box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
    -o-box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
    text-shadow: rgba(255, 255, 255, 0.5) 0px 1px 0px;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    position: relative;
  }

  .badges-ribbon-content:before,
  .badges-ribbon-contentribbon-green:after {
    content: "";
    border-top: 3px solid #6e8900;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    position: absolute;
    bottom: -3px;
  }

  .badges-ribbon-content:before {
    left: 0;
  }

  .badges-ribbon-content:after {
    right: 0;
  }

  .btn-outline {
    padding: 10px;
    -webkit-border-radius: 2px !important;
    -moz-border-radius: 2px !important;
    -ms-border-radius: 2px !important;
    -o-border-radius: 2px !important;
    border-radius: 2px !important;
    background: transparent !important;
    -webkit-transition-property: background;
    -moz-transition-property: background;
    -ms-transition-property: background;
    -o-transition-property: background;
    transition-property: background;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -ms-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: linear;
    -moz-transition-timing-function: linear;
    -ms-transition-timing-function: linear;
    -o-transition-timing-function: linear;
    transition-timing-function: linear;
  }

  .btn-primary.btn-outline,
  .label-outline.label-primary,
  .alert-outline.alert-primary {
    border: 1px solid #1c84c6 !important;
    background: none !important;
    color: #1c84c6 !important;
  }

  .btn-outline.btn-success,
  .label-outline.label-success,
  .alert-outline.alert-success {
    border: 1px solid #27C24C !important;
    background: none !important;
    color: #27C24C !important;
  }

  .btn-outline.btn-info,
  .label-outline.label-info,
  .alert-outline.alert-info {
    border: 1px solid #73CAEF !important;
    background: none !important;
    color: #73CAEF !important;
  }

  .btn-outline.btn-danger,
  .label-outline.label-danger,
  .alert-outline.alert-danger {
    border: 1px solid #FF6656 !important;
    background: none !important;
    color: #FF6656 !important;
  }

  .btn-outline.btn-warning,
  .label-outline.label-warning,
  .alert-outline.alert-warning {
    border: 1px solid #F0AD4E !important;
    background: none !important;
    color: #F0AD4E !important;
  }

  .btn-outline.btn-default,
  .label-outline.label-default,
  .alert-outline.alert-default {
    border: 1px solid #ddd !important;
    background: none !important;
    color: #857E7E !important;
  }

  .btn-primary.btn-outline:hover,
  .btn-primary.btn-outline:active,
  .btn-primary.btn-outline:focus,
  .label-outline.label-primary:hover,
  .alert-outline.alert-primary:hover {
    background: none !important;
    color: #918C8C !important;
    border: 1px solid #918C8C !important;
  }

  .btn-outline.btn-success:hover,
  .btn-outline.btn-success:active,
  .btn-outline.btn-success:focus,
  .label-outline.label-success:hover,
  .alert-outline.alert-success:hover {
    background: none !important;
    color: #918C8C !important;
    border: 1px solid #918C8C !important;
  }

  .btn-outline.btn-info:hover,
  .btn-outline.btn-info:active,
  .btn-outline.btn-info:focus,
  .label-outline.label-info:hover,
  .alert-outline.alert-info:hover {
    background: none !important;
    color: #918C8C !important;
    border: 1px solid #918C8C !important;
  }

  .btn-outline.btn-danger:hover,
  .btn-outline.btn-danger:active,
  .btn-outline.btn-danger:focus,
  .label-outline.label-danger:hover,
  .alert-outline.alert-danger:hover {
    background: none !important;
    color: #918C8C !important;
    border: 1px solid #918C8C !important;
  }

  .btn-outline.btn-warning:hover,
  .btn-outline.btn-warning:active,
  .btn-outline.btn-warning:focus,
  .label-outline.label-warning:hover,
  .alert-outline.alert-warning:hover {
    background: none !important;
    color: #918C8C !important;
    border: 1px solid #918C8C !important;
  }

  .btn-outline.btn-default:hover,
  .btn-outline.btn-default:active,
  .btn-outline.btn-default:focus,
  .label-outline.label-default:hover,
  .alert-outline.alert-default:hover {
    background: none !important;
    color: #918C8C !important;
    border: 1px solid #918C8C !important;
  }

  .btn-gradient {
    color: #fff !important;
    font-weight: bold;
  }

  .btn-gradient.btn-primary,
  .label-gradient.label-primary,
  .alert-gradient.alert-primary {
    background: -moz-linear-gradient(top, #33a6cc 50%, #1c84c6 50%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #33a6cc), color-stop(50%, #1c84c6));
    background: -webkit-linear-gradient(top, #33a6cc 50%, #1c84c6 50%);
    background: -o-linear-gradient(top, #33a6cc 50%, #1c84c6 50%);
    background: -ms-linear-gradient(top, #33a6cc 50%, #1c84c6 50%);
    background: linear-gradient(to bottom, #33a6cc 50%, #1c84c6 50%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#33a6cc', endColorstr='#1c84c6', GradientType=0);
  }
  .btn-gradient.btn-success,
  .label-gradient.label-success,
  .alert-gradient.alert-success {
    background: -moz-linear-gradient(top, #27C24C 50%, #1DAA3F 50%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #27C24C), color-stop(50%, #1DAA3F));
    background: -webkit-linear-gradient(top, #27C24C 50%, #1DAA3F 50%);
    background: -o-linear-gradient(top, #27C24C 50%, #1DAA3F 50%);
    background: -ms-linear-gradient(top, #27C24C 50%, #1DAA3F 50%);
    background: linear-gradient(to bottom, #27C24C 50%, #1DAA3F 50%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#27C24C', endColorstr='#1DAA3F', GradientType=0);
  }
  .btn-gradient.btn-info,
  .label-gradient.label-info,
  .alert-gradient.alert-info {
    background: -moz-linear-gradient(top, #73CAEF 50%, #47B4E2 50%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #73CAEF), color-stop(50%, #47B4E2));
    background: -webkit-linear-gradient(top, #73CAEF 50%, #47B4E2 50%);
    background: -o-linear-gradient(top, #73CAEF 50%, #47B4E2 50%);
    background: -ms-linear-gradient(top, #73CAEF 50%, #47B4E2 50%);
    background: linear-gradient(to bottom, #73CAEF 50%, #47B4E2 50%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#73CAEF', endColorstr='#47B4E2', GradientType=0);
  }

  .btn-gradient.btn-danger,
  .label-gradient.label-danger,
  .alert-gradient.alert-danger {
    background: -moz-linear-gradient(top, #FF6656 50%, #E43927 50%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #FF6656), color-stop(50%, #E43927));
    background: -webkit-linear-gradient(top, #FF6656 50%, #E43927 50%);
    background: -o-linear-gradient(top, #FF6656 50%, #E43927 50%);
    background: -ms-linear-gradient(top, #FF6656 50%, #E43927 50%);
    background: linear-gradient(to bottom, #FF6656 50%, #E43927 50%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#FF6656', endColorstr='#E43927', GradientType=0);
  }

  .btn-gradient.btn-warning,
  .label-gradient.label-warning,
  .alert-gradient.alert-warning {
    background: -moz-linear-gradient(top, #F0AD4E 50%, #D49236 50%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #F0AD4E), color-stop(50%, #D49236));
    background: -webkit-linear-gradient(top, #F0AD4E 50%, #D49236 50%);
    background: -o-linear-gradient(top, #F0AD4E 50%, #D49236 50%);
    background: -ms-linear-gradient(top, #F0AD4E 50%, #D49236 50%);
    background: linear-gradient(to bottom, #F0AD4E 50%, #D49236 50%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#F0AD4E', endColorstr='#D49236', GradientType=0);
  }

  .btn-gradient.btn-default,
  .label-gradien.label-default,
  .alert-gradient.alert-default {
    background: -moz-linear-gradient(top, #857E7E 50%, #7B7474 50%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #857E7E), color-stop(50%, #7B7474));
    background: -webkit-linear-gradient(top, #857E7E 50%, #7B7474 50%);
    background: -o-linear-gradient(top, #857E7E 50%, #7B7474 50%);
    background: -ms-linear-gradient(top, #857E7E 50%, #7B7474 50%);
    background: linear-gradient(to bottom, #857E7E 50%, #7B7474 50%);
    color: #fff !important;
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#857E7E', endColorstr='#7B7474', GradientType=0);
  }

  .btn-raised.btn-primary,
  .label-raised.label-primary,
  .alert-raised.alert-primary {
    -webkit-box-shadow: 0 3px 0 0 #007299 !important;
    -moz-box-shadow: 0 3px 0 0 #007299 !important;
    -ms-box-shadow: 0 3px 0 0 #007299 !important;
    -o-box-shadow: 0 3px 0 0 #007299 !important;
    box-shadow: 0 3px 0 0 #007299 !important;
  }

  .btn-raised.btn-success,
  .label-raised.label-success,
  .alert-raised.alert-success {
    -webkit-box-shadow: 0 3px 0 0 #50773e !important;
    -moz-box-shadow: 0 3px 0 0 #50773e !important;
    -ms-box-shadow: 0 3px 0 0 #50773e !important;
    -o-box-shadow: 0 3px 0 0 #50773e !important;
    box-shadow: 0 3px 0 0 #50773e !important;
  }

  .btn-raised.btn-info,
  .label-raised.label-info,
  .alert-raised.alert-info {
    -webkit-box-shadow: 0 3px 0 0 #47B4E2 !important;
    -moz-box-shadow: 0 3px 0 0 #47B4E2 !important;
    -ms-box-shadow: 0 3px 0 0 #47B4E2 !important;
    -o-box-shadow: 0 3px 0 0 #47B4E2 !important;
    box-shadow: 0 3px 0 0 #47B4E2 !important;
  }

  .btn-raised.btn-danger,
  .label-raised.label-danger,
  .alert-raised.alert-danger {
    -webkit-box-shadow: 0 3px 0 0 #E43927 !important;
    -moz-box-shadow: 0 3px 0 0 #E43927 !important;
    -ms-box-shadow: 0 3px 0 0 #E43927 !important;
    -o-box-shadow: 0 3px 0 0 #E43927 !important;
    box-shadow: 0 3px 0 0 #E43927 !important;
  }

  .btn-raised.btn-warning,
  .label-raised.label-warning,
  .alert-raised.alert-warning {
    -webkit-box-shadow: 0 3px 0 0 #D49236 !important;
    -moz-box-shadow: 0 3px 0 0 #D49236 !important;
    -ms-box-shadow: 0 3px 0 0 #D49236 !important;
    -o-box-shadow: 0 3px 0 0 #D49236 !important;
    box-shadow: 0 3px 0 0 #D49236 !important;
  }

  .btn-raised.btn-default,
  .label-raised.label-default,
  .alert-raised.alert-default {
    -webkit-box-shadow: 0 3px 0 0 #7B7474 !important;
    -moz-box-shadow: 0 3px 0 0 #7B7474 !important;
    -ms-box-shadow: 0 3px 0 0 #7B7474 !important;
    -o-box-shadow: 0 3px 0 0 #7B7474 !important;
    box-shadow: 0 3px 0 0 #7B7474 !important;
  }

  .btn-3d {
    font-weight: bold !important;
    -webkit-border-radius: 10px !important;
    -moz-border-radius: 10px !important;
    -ms-border-radius: 10px !important;
    -o-border-radius: 10px !important;
    border-radius: 10px !important;
  }

  .btn-3d:active {
    outline: none !important;
    position: relative;
    top: 2px;
  }

  .btn-3d.btn-primary,
  .label-3d.label-primary,
  .alert-3d.alert-primary {
    -webkit-box-shadow: inset 0px 0px 0px #007299, 0px 5px 0px 0px #007299, 0px 10px 5px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #007299, 0px 5px 0px 0px #007299, 0px 10px 5px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #007299, 0px 5px 0px 0px #007299, 0px 10px 5px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #007299, 0px 5px 0px 0px #007299, 0px 10px 5px #999 !important;
    box-shadow: inset 0px 0px 0px #007299, 0px 5px 0px 0px #007299, 0px 10px 5px #999 !important;
  }

  .btn-3d.btn-primary:active,
  .label-3d.label-primary:active,
  .alert-3d.alert-primary:active {
    -webkit-box-shadow: inset 0px 0px 0px #007299, 0px 2px 0px 0px #007299, 0px 4px 2px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #007299, 0px 2px 0px 0px #007299, 0px 4px 2px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #007299, 0px 2px 0px 0px #007299, 0px 4px 2px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #007299, 0px 2px 0px 0px #007299, 0px 4px 2px #999 !important;
    box-shadow: inset 0px 0px 0px #007299, 0px 2px 0px 0px #007299, 0px 4px 2px #999 !important;
  }

  .btn-3d.btn-success,
  .label-3d.label-success,
  .alert-3d.alert-success {
    -webkit-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 5px 0px 0px #1DAA3F, 0px 10px 5px #999;
    -moz-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 5px 0px 0px #1DAA3F, 0px 10px 5px #999;
    -ms-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 5px 0px 0px #1DAA3F, 0px 10px 5px #999;
    -o-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 5px 0px 0px #1DAA3F, 0px 10px 5px #999;
    box-shadow: inset 0px 0px 0px #1DAA3F, 0px 5px 0px 0px #1DAA3F, 0px 10px 5px #999;
  }

  .btn-3d.btn-success:active,
  .label-3d.label-success:active,
  .alert-3d.alert-success:active {
    -webkit-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 2px 0px 0px #1DAA3F, 0px 4px 2px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 2px 0px 0px #1DAA3F, 0px 4px 2px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 2px 0px 0px #1DAA3F, 0px 4px 2px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #1DAA3F, 0px 2px 0px 0px #1DAA3F, 0px 4px 2px #999 !important;
    box-shadow: inset 0px 0px 0px #1DAA3F, 0px 2px 0px 0px #1DAA3F, 0px 4px 2px #999 !important;
  }

  .btn-3d.btn-info,
  .label-3d.label-info,
  .alert-3d.alert-info {
    -webkit-box-shadow: inset 0px 0px 0px #47B4E2, 0px 5px 0px 0px #47B4E2, 0px 10px 5px #999;
    -moz-box-shadow: inset 0px 0px 0px #47B4E2, 0px 5px 0px 0px #47B4E2, 0px 10px 5px #999;
    -ms-box-shadow: inset 0px 0px 0px #47B4E2, 0px 5px 0px 0px #47B4E2, 0px 10px 5px #999;
    -o-box-shadow: inset 0px 0px 0px #47B4E2, 0px 5px 0px 0px #47B4E2, 0px 10px 5px #999;
    box-shadow: inset 0px 0px 0px #47B4E2, 0px 5px 0px 0px #47B4E2, 0px 10px 5px #999;
  }

  .btn-3d.btn-info:active,
  .label-3d.label-info:active,
  .alert-3d.alert-info:active {
    -webkit-box-shadow: inset 0px 0px 0px #47B4E2, 0px 2px 0px 0px #47B4E2, 0px 4px 2px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #47B4E2, 0px 2px 0px 0px #47B4E2, 0px 4px 2px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #47B4E2, 0px 2px 0px 0px #47B4E2, 0px 4px 2px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #47B4E2, 0px 2px 0px 0px #47B4E2, 0px 4px 2px #999 !important;
    box-shadow: inset 0px 0px 0px #47B4E2, 0px 2px 0px 0px #47B4E2, 0px 4px 2px #999 !important;
  }

  .btn-3d.btn-danger,
  .label-3d.label-danger,
  .alert-3d.alert-danger {
    -webkit-box-shadow: inset 0px 0px 0px #E43927, 0px 5px 0px 0px #E43927, 0px 10px 5px #999;
    -moz-box-shadow: inset 0px 0px 0px #E43927, 0px 5px 0px 0px #E43927, 0px 10px 5px #999;
    -ms-box-shadow: inset 0px 0px 0px #E43927, 0px 5px 0px 0px #E43927, 0px 10px 5px #999;
    -o-box-shadow: inset 0px 0px 0px #E43927, 0px 5px 0px 0px #E43927, 0px 10px 5px #999;
    box-shadow: inset 0px 0px 0px #E43927, 0px 5px 0px 0px #E43927, 0px 10px 5px #999;
  }

  .btn-3d.btn-danger:active,
  .label-3d.label-danger:active,
  .alert-3d.alert-danger:active {
    -webkit-box-shadow: inset 0px 0px 0px #E43927, 0px 2px 0px 0px #E43927, 0px 4px 2px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #E43927, 0px 2px 0px 0px #E43927, 0px 4px 2px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #E43927, 0px 2px 0px 0px #E43927, 0px 4px 2px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #E43927, 0px 2px 0px 0px #E43927, 0px 4px 2px #999 !important;
    box-shadow: inset 0px 0px 0px #E43927, 0px 2px 0px 0px #E43927, 0px 4px 2px #999 !important;
  }

  .btn-3d.btn-warning,
  .label-3d.label-warning,
  .alert-3d.alert-warning {
    -webkit-box-shadow: inset 0px 0px 0px #D49236, 0px 5px 0px 0px #D49236, 0px 10px 5px #999;
    -moz-box-shadow: inset 0px 0px 0px #D49236, 0px 5px 0px 0px #D49236, 0px 10px 5px #999;
    -ms-box-shadow: inset 0px 0px 0px #D49236, 0px 5px 0px 0px #D49236, 0px 10px 5px #999;
    -o-box-shadow: inset 0px 0px 0px #D49236, 0px 5px 0px 0px #D49236, 0px 10px 5px #999;
    box-shadow: inset 0px 0px 0px #D49236, 0px 5px 0px 0px #D49236, 0px 10px 5px #999;
  }

  .btn-3d.btn-warning:active,
  .label-3d.label-warning:active,
  .alert-3d.alert-warning:active {
    -webkit-box-shadow: inset 0px 0px 0px #D49236, 0px 2px 0px 0px #D49236, 0px 4px 2px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #D49236, 0px 2px 0px 0px #D49236, 0px 4px 2px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #D49236, 0px 2px 0px 0px #D49236, 0px 4px 2px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #D49236, 0px 2px 0px 0px #D49236, 0px 4px 2px #999 !important;
    box-shadow: inset 0px 0px 0px #D49236, 0px 2px 0px 0px #D49236, 0px 4px 2px #999 !important;
  }

  .btn-3d.btn-default,
  .label-3d.label-default,
  .alert-3d.alert-default {
    -webkit-box-shadow: inset 0px 0px 0px #7B7474, 0px 5px 0px 0px #7B7474, 0px 10px 5px #999;
    -moz-box-shadow: inset 0px 0px 0px #7B7474, 0px 5px 0px 0px #7B7474, 0px 10px 5px #999;
    -ms-box-shadow: inset 0px 0px 0px #7B7474, 0px 5px 0px 0px #7B7474, 0px 10px 5px #999;
    -o-box-shadow: inset 0px 0px 0px #7B7474, 0px 5px 0px 0px #7B7474, 0px 10px 5px #999;
    box-shadow: inset 0px 0px 0px #7B7474, 0px 5px 0px 0px #7B7474, 0px 10px 5px #999;
  }

  .btn-3d.btn-default:active,
  .label-3d.label-default:active,
  .alert-3d.alert-default:active {
    -webkit-box-shadow: inset 0px 0px 0px #7B7474, 0px 2px 0px 0px #7B7474, 0px 4px 2px #999 !important;
    -moz-box-shadow: inset 0px 0px 0px #7B7474, 0px 2px 0px 0px #7B7474, 0px 4px 2px #999 !important;
    -ms-box-shadow: inset 0px 0px 0px #7B7474, 0px 2px 0px 0px #7B7474, 0px 4px 2px #999 !important;
    -o-box-shadow: inset 0px 0px 0px #7B7474, 0px 2px 0px 0px #7B7474, 0px 4px 2px #999 !important;
    box-shadow: inset 0px 0px 0px #7B7474, 0px 2px 0px 0px #7B7474, 0px 4px 2px #999 !important;
  }

  .btn-flip {
    height: 50px !important;
    padding: 0px !important;
    -webkit-perspective: 600;
    -moz-perspective: 600;
    -ms-perspective: 600;
    -o-perspective: 600;
    perspective: 600;
    position: relative;
    width: 150px !important;
  }

  .btn-flip .flip {
    height: 100%;
    position: absolute;
    margin-top: -10px;
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transition: all 1s ease-in-out;
    -moz-transition: all 1s ease-in-out;
    -ms-transition: all 1s ease-in-out;
    -o-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    width: 100%;
  }

  .btn-flip .flip:hover {
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    -ms-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
    transform: rotateY(180deg);
  }

  .btn-flip .flip .side {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -o-backface-visibility: hidden;
    backface-visibility: hidden;
    height: 100%;
    position: absolute;
    width: 100%;
  }

  .btn-flip .flip .back {
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    -ms-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
    transform: rotateY(180deg);
  }


  .avatar {
    height: 40px;
    width: 40px;
    border: 3px solid #ffffff !important;
    margin-top: 5px;
  }

  .panel-primary .panel-heading,
  .progress-bar.progress-bar-primary{
    background-color: #1c84c6 !important;
  }

  .panel-success .panel-heading,
  .progress-bar.progress-bar-success {
    background-color: #27C24C !important;
  }

  .panel-info .panel-heading,
  .progress-bar.progress-bar-info {
    background-color: #73CAEF !important;
  }

  .panel-danger .panel-heading,
  .progress-bar.progress-bar-danger {
    background-color: #FF6656 !important;
  }

  .panel-warning .panel-heading,
  .progress-bar.progress-bar-warning {
    background-color: #F0AD4E !important;
  }

  .panel-default.panel-heading,
  .progress-bar.progress-bar-default {
    background-color: #857E7E !important;
  }

  .progress.progress-rounded,
  .progress.progress-rounded .progress-bar {
    -webkit-border-radius: 20px !important;
    -moz-border-radius: 20px !important;
    -ms-border-radius: 20px !important;
    -o-border-radius: 20px !important;
    border-radius: 20px !important;
  }

  .alert.alert-icon {
    padding: 10px;
    padding-top: 20px;
    padding-right: 20px;
    text-align: left;
  }

  .alert.alert-icon p {
    font-size: 14px;
  }

  .alert-default.alert-icon .icon-wrapper {
    color: #918C8C !important;
    border-right: 1px solid #918C8C !important;
  }

  .alert.alert-icon .icon-wrapper {
    font-size: 1.4em !important;
    padding: 0px;
    color: #fff;
    border-right: 1px solid #fff;
  }

  .alert-border {
    text-align: left !important;
    padding: 20px;
    padding-top: 10px;
    color: #655E5E !important;
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    -ms-border-radius: 0px !important;
    -o-border-radius: 0px !important;
    border-radius: 0px !important;
  }

  .alert-border .close {
    position: absolute;
    margin-right: 40px;
    margin-top: 10px;
    color: #090808 !important;
  }

  .alert.alert-primary.alert-border {
    border-left: 8px solid #1c84c6 !important;
    background-color: #84B6D6 !important;
  }

  .alert.alert-success.alert-border {
    border-left: 8px solid #27C24C !important;
    background-color: #9CCFA8 !important;
  }

  .alert.alert-info.alert-border {
    border-left: 8px solid #73CAEF !important;
    background-color: #A6E2FB !important;
  }

  .alert.alert-danger.alert-border {
    border-left: 8px solid #FF6656 !important;
    background-color: #FFC1C1 !important;
  }

  .alert.alert-warning.alert-border {
    border-left: 8px solid #F0AD4E !important;
    background-color: #FFD08D !important;
  }

  .alert.alert-default.alert-border {
    border-left: 8px solid #857E7E !important;
    background-color: #BBBBBB !important;
  }

  #spinners li {
    float: left;
    padding: 20px;
    list-style: none;
  }

  .navbar {
    height: 55px;
    background-color: #50773e;
    color: #fff;
    border: none;
  }

  .navbar .navbar-brand {
    text-shadow: 0px 1px 5px #918C8C;
    padding: 15px;
    margin-left: 20px;
    color: #FFFFFF !important;
  }

  .navbar .active a {
    background: none;
  }

  .opener-right-menu span{
    font-size: 25px;
    color: #0c66ae;
  }

  .opener-right-menu {
    color: #fff !important;
  }

  .navbar-nav {
    padding-left: 10px;
  }

  .navbar li {
    background: none;
  }

  .dropdown a {
    font-size: 1em;
  }

  .navbar .search {
    padding-top: 15px;
    padding-left: 10px;
    color: #ffffff;
  }

  .navbar .search .form-animate-text{
    float: right;
    margin-top: -10px;
    margin-left: 5px;
    width: 200px;
  }


  .navbar-header .search .form-animate-text .form-text:focus ~ .label-search,.form-text:valid ~ .label-search {
    top: -50px !important;
    font-size: 14px;
    color: #fffff !important;
  }

  .navbar .search .form-animate-text .form-text{
   border:none !important;
 }

 .navbar .search .form-animate-text .bar:before,.navbar .search .form-animate-text
 .bar:after {
  height: 1px;
  background: #0c66ae !important;
}


.navbar .search .form-animate-text label{
  color:#0c66ae;
  font-size: 15px !important;
}



.user-dropdown {
  margin-top: 20px;
  border: 1px solid #EAE8E8;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1) !important;
  -moz-box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1) !important;
  -ms-box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1) !important;
  -o-box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1) !important;
}

.user-dropdown li {
  overflow: inherit;
}

.user-dropdown li a {
  color: #9E9E9E;
  font-weight: 10;
}

.user-dropdown:before {
  position: absolute;
  top: -7px;
  left: 134px;
  display: inline-block;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-left: 7px solid transparent;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  content: '';
}

.user-dropdown:after {
  position: absolute;
  top: -6px;
  left: 135px;
  display: inline-block;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  border-left: 6px solid transparent;
  content: '';
}

.user-dropdown .more {
  background-color: #F9F9F9;
  padding: 0px;
  width: 100%;
  height: 40px;
  border-top: 1px solid #ddd;
}

.user-dropdown .more ul {
  padding: 0px;
  padding-left: 10px;
}

.user-dropdown .more li {
  float: left;
  list-style: none;
  padding: 15px;
  padding-top: 5px;
}

.user-dropdown .more a {
  font-size: 18px;
  color: #ddd;
}

.user-name {
  padding-top: 15px;
  padding-right: 10px;
}

.user-name span {
  font-size: 16px;
  font-weight: bold;
  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/*-----Left menu-------*/
#left-menu{}
#left-menu .left-bg {
  background: url('img/left-bg.png');
  background-repeat: repeat-x;
  height: 100px;
  left: 0;
  position: relative;
  margin-left: -2px;
}


#left-menu .sub-left-menu {
  background-color: #fff;
  left: 0;
  padding-top: 50px;
  z-index: 222;
  width: 230px;
  height: 100%;
  position: fixed;
  overflow-y: hidden;
  -webkit-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
  -moz-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
  -ms-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
  -o-box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
  box-shadow: 0 2px 5px 0 rgba(239, 235, 235, 0.16), 0 2px 10px 0 rgba(72, 70, 70, 0.12);
}

#left-menu .sub-left-menu a {
  color: #918C8C;
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
}

#left-menu .sub-left-menu .right-arrow {
  float: right;
  padding-top: 13px;
}

#left-menu .sub-left-menu a:hover {
  background: none;
  -webkit-text-decoration: none;
  -moz-text-decoration: none;
  -ms-text-decoration: none;
  -o-text-decoration: none;
  text-decoration: none;
}

#left-menu .sub-left-menu span {
  padding-right: 10px;
}

#left-menu .sub-left-menu li {
  line-height: 44px;
}

#left-menu .sub-left-menu .active {
  border-left: 4px solid #C1B8B8;
}

#left-menu .sub-left-menu .active {
  width: 100%;
}



#left-menu .sub-left-menu .time h1 {
  font-weight: 500;
  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 30px;
  text-align: center;
  color: #918C8C;
}

#left-menu .sub-left-menu .time p {
  margin-top: -20px;
  text-align: center;
  font-size: 11px;
  color: #918C8C;
}

#left-menu .sub-left-menu .tree {
  background-color: #F6F8F8;
  padding: 0px;
  margin: 0px;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}

#left-menu .sub-left-menu .tree li {
  padding: 0px;
  padding-left: 10%;
}

.opener-left-menu {
  float: left;
  padding: 20px;
  padding-top: 35px;
  background-color: #50773e;
  padding-bottom: 11px;
  color: #C62828;
  cursor: pointer;
  -webkit-box-shadow: 0px 9px 0px 0px #50773e, 0 -9px 0px 0px #50773e, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 9px 0px 0px #50773e, 0 -9px 0px 0px #50773e, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  -ms-box-shadow: 0px 9px 0px 0px #50773e, 0 -9px 0px 0px #50773e, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  -o-box-shadow: 0px 9px 0px 0px #50773e, 0 -9px 0px 0px #50773e, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 9px 0px 0px #50773e, 0 -9px 0px 0px #50773e, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
}


.opener-left-menu.is-open {
  padding-top: 29px;
  padding-bottom: 17px;
}

.opener-left-menu:hover,
.opener-left-menu:focus,
.opener-left-menu:active {
  outline: none;
}

.opener-left-menu.is-closed:before {
  content: '';
  display: block;
  width: 30px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -ms-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-closed:hover:before {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
  display: block;
  -webkit-transform: translate3d(-100px, 0, 0);
  -moz-transform: translate3d(-100px, 0, 0);
  -ms-transform: translate3d(-100px, 0, 0);
  -o-transform: translate3d(-100px, 0, 0);
  transform: translate3d(-100px, 0, 0);
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-closed .top,
.opener-left-menu.is-closed .middle,
.opener-left-menu.is-closed .bottom,
.opener-left-menu.is-open .top,
.opener-left-menu.is-open .middle,
.opener-left-menu.is-open .bottom {
  position: absolute;
  height: 4px;
  width: 30px;
}

.opener-left-menu.is-closed .top,
.opener-left-menu.is-closed .middle,
.opener-left-menu.is-closed .bottom {
  background-color: #fff;
}

.opener-left-menu.is-closed .top {
  top: 15px;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-closed .middle {
  top: 24px;
}

.opener-left-menu.is-closed .bottom {
  top: 32px;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-closed:hover .top {
  top: 10px;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-closed:hover .bottom {
  bottom: 10px;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-open .top,
.opener-left-menu.is-open .middle,
.opener-left-menu.is-open .bottom {
  background-color: #fff;
}

.opener-left-menu.is-open .top,
.opener-left-menu.is-open .bottom {
  margin-top: -2px;
}

.opener-left-menu.is-open .top {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  -moz-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  -ms-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  -o-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
}

.opener-left-menu.is-open .middle {
  display: none;
}

.opener-left-menu.is-open .bottom {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  -moz-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  -ms-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  -o-transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
  transition: -webkit-transform .2s cubic-bezier(.73, 1, .28, .08);
}

.opener-left-menu.is-open:before {
  content: '';
  display: block;
  width: 30px;
  font-size: 14px;
  color: #fff;
  line-height: 32px;
  text-align: center;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -ms-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

.opener-left-menu.is-open:hover:before {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
  display: block;
  -webkit-transform: translate3d(-100px, 0, 0);
  -moz-transform: translate3d(-100px, 0, 0);
  -ms-transform: translate3d(-100px, 0, 0);
  -o-transform: translate3d(-100px, 0, 0);
  transform: translate3d(-100px, 0, 0);
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out;
}

/*----right menu-------*/

#right-menu {
  position: fixed;
  right: 0;
  top: 50px;
  display: none;
  z-index: 222;
  width: 230px;
  background-color: #fff;
  height: 100%;
  border-left: 1px solid #dee5e7;
  -webkit-box-shadow: 0px 9px 0px 0px white, 0 -9px 0px 0px white, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0px 9px 0px 0px white, 0 -9px 0px 0px white, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  -ms-box-shadow: 0px 9px 0px 0px white, 0 -9px 0px 0px white, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  -o-box-shadow: 0px 9px 0px 0px white, 0 -9px 0px 0px white, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 9px 0px 0px white, 0 -9px 0px 0px white, 4px 0 15px -4px rgba(0, 0, 0, 0.16), -4px 0 15px -4px rgba(0, 0, 0, 0.12);
}

#right-menu .tab-content {
  background-color: #fff;
  height: 100%;
  width: 100%;
}

#right-menu .nav-tabs {
  width: 100%;
  border-bottom: 1px solid #dee5e7;
}

#right-menu .nav-tabs span {
  color: #98a6ad;
}

#right-menu .nav-tabs li {
  padding: 0px;
  padding-left: 4px;
  width: 32.666666667%;
}

#right-menu .nav-tabs li a {
  padding: 15px;
}

#right-menu .nav-tabs .active a {
  background-color: #fff;
  border: none;
  border-bottom: 1px solid #23b7e5;
}

#right-menu .nav-tabs .active span {
  color: #98a6ad;
}

#right-menu .nav-tabs li:hover {
  background: #fff;
  border: none;
  border-bottom: 1px solid #23b7e5;
}

#right-menu .nav-tabs li a:hover {
  background-color: #fff;
  border: none;
  border-bottom: 1px solid #23b7e5;
}

#right-menu .search {
  padding: 10px;
  background-color: #fff;
}

#right-menu .search input {
  width: 100%;
  padding-left: 10px;
  border: none;
  height: 40px;
  background: #fff;
}

#right-menu .search input:focus {
  outline: none;
}

#right-menu .user {
  padding-top: 10px;
  padding-bottom: 5px;
  height: 600px;
  width: 100%;
}

#right-menu .gadget {
  position: absolute;
  margin-top: -36px;
  margin-left: -7px;
}

#right-menu .online .gadget {
  color: #98a6ad;
}

#right-menu .away .gadget {
  color: #98a6ad;
}

#right-menu .online h5 {
  color: #98a6ad !important;
}

#right-menu .away h5 {
  color: #98a6ad !important;
}

#right-menu .user .online {
  background-color: #fff;
}

#right-menu .user ul {
  padding: 0px;
  max-height: 100%;
}

#right-menu .user li {
  list-style: none;
  padding: 10px;
  float: left;
  padding-left: 15px;
  cursor: pointer;
  width: 100%;
}

#right-menu .user .dot {
  width: 15px;
  height: 15px;
  position: absolute;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  margin-left: 150px;
  margin-top: -10px;
}

#right-menu .user .online .dot {
  background-color: #27C24C !important;
}

#right-menu .user .away .dot {
  background-color: #FB5A3D !important;
}

#right-menu .user .offline .dot {
  background-color: #6F767A !important;
}

#right-menu .user img {
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  width: 50px;
  height: 50px;
  float: right;
  margin-right: 10px;
}

#right-menu .name {
  font-size: 1em;
  padding-left: 10px;
  color: #ddd !important;
}

#right-menu .name h1 {
  margin-top: -10px;
}

#right-menu .name p {
  margin-top: -14px;
}

#right-menu .chatbox {
  width: 100%;
  padding: 0px;
  color: #98A6AD;
  display: none;
}

#right-menu .chatbox .close-chat {
  font-size: 20px;
  font-weight: 300;
  position: absolute;
  right: 10px;
  -webkit-text-decoration: none;
  -moz-text-decoration: none;
  -ms-text-decoration: none;
  -o-text-decoration: none;
  text-decoration: none;
  color: #474747;
}

#right-menu .chatbox .chat-input {
  width: 100%;
  height: 60px;
  background-color: #fff;
  bottom: 80px;
  position: fixed;
  z-index: 230;
}

#right-menu .chatbox .chat-input textarea {
  height: 50px;
  width: 220px;
  margin: 0 auto;
  border: none;
  margin-top: 10px;
  resize: none;
  padding: 10px;
}

#right-menu .chatbox .chat-input textarea:focus {
  outline: none;
}

#right-menu .chatbox .chat-area {
  width: 100%;
  height: 700px;
}

#right-menu .chatbox .user-list .dot {
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  margin-top: -15px;
  width: 15px;
  height: 15px;
  border: 2px solid #fff;
}

#right-menu .chatbox .user-list .online .dot {
  background-color: #27C24C !important;
}

#right-menu .chatbox .user-list .offline .dot {
  background-color: #6F767A !important;
}

#right-menu .chatbox .user-list .away .dot {
  background-color: #FB5A3D !important;
}

.msg_container_base {
  background: #FFF;
  margin: 0;
  padding: 0 10px 10px;
  overflow-x: hidden;
  max-height: 400px;
}

.top-bar {
  background: #666;
  color: white;
  padding: 10px;
  position: relative;
  overflow: hidden;
}

.msg_receive {
  padding-left: 0;
  margin-left: 0;
}

.msg_sent {
  padding-bottom: 20px !important;
  margin-right: 0;
}

.messages {
  background: white;
  padding: 10px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  -ms-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  -o-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  max-width: 100%;
}

.messages > p {
  font-size: 13px;
  margin: 0 0 0.2rem 0;
}

.messages > time {
  font-size: 11px;
  color: #ccc;
}

.msg_container {
  padding: 10px;
  overflow: hidden;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
}

.avatar {
  position: relative;
  padding: 0px;
}



.receive > .avatar:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border: 5px solid #FFF;
  border-left-color: rgba(0, 0, 0, 0);
  border-bottom-color: rgba(0, 0, 0, 0);
}

.bubble {
  padding: 0px;
}

.send {
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  -ms-justify-content: flex-end;
  -o-justify-content: flex-end;
  justify-content: flex-end;
  -webkit-align-items: flex-end;
  -moz-align-items: flex-end;
  -ms-align-items: flex-end;
  -o-align-items: flex-end;
  align-items: flex-end;
}

.send > .avatar:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 0;
  border: 5px solid white;
  border-right-color: transparent;
  border-top-color: transparent;
  -webkit-box-shadow: 1px 1px 2px rgba(black, 0.2);
  -moz-box-shadow: 1px 1px 2px rgba(black, 0.2);
  -ms-box-shadow: 1px 1px 2px rgba(black, 0.2);
  -o-box-shadow: 1px 1px 2px rgba(black, 0.2);
  box-shadow: 1px 1px 2px rgba(black, 0.2);
}

.msg_sent > time {
  float: right;
}

.msg_container_base::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -ms-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -o-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #F5F5F5;
}

.msg_container_base::-webkit-scrollbar {
  width: 12px;
  background-color: #F5F5F5;
}

.msg_container_base::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  -moz-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  -ms-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  -o-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: #555;
}

#right-menu .chatbox .user-list {
  height: 80px;
  bottom: 0;
  position: fixed;
  padding-left: 0px;
  border-top: 1px solid #ddd;
  float: left;
  width: 100%;
  z-index: 230;
  background-color: #F4F4F4;
  overflow: hidden;
  -webkit-box-shadow: inset 0 8px 8px -8px #BFBFBF, inset 0 -8px 8px -8px #BFBFBF;
  -moz-box-shadow: inset 0 8px 8px -8px #BFBFBF, inset 0 -8px 8px -8px #BFBFBF;
  -ms-box-shadow: inset 0 8px 8px -8px #BFBFBF, inset 0 -8px 8px -8px #BFBFBF;
  -o-box-shadow: inset 0 8px 8px -8px #BFBFBF, inset 0 -8px 8px -8px #BFBFBF;
  box-shadow: inset 0 8px 8px -8px #BFBFBF, inset 0 -8px 8px -8px #BFBFBF;
}

#right-menu .chatbox .user-list img {
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  width: 50px;
  height: 50px;
  border: 2px solid #98a6ad;
}

#right-menu .user-list > ul {
  padding: 0px;
  padding-left: 5px;
  width: 228px;
  height: 80px;
  display: block;
  white-space: nowrap;
  overflow: auto;
}

#right-menu .user-list li {
  width: 55px;
  padding-top: 14px;
  display: inline-block;
}

#content {
  margin-top: 51px;
  padding: 0px;
  padding-bottom: 10px;
  padding-left: 230px;
  width: 100%;
  color: #080808;
}


.input-group-addon,
.user-photo,
.btn,
.cover-v1,
.nav-tabs.nav-tabs-v2,
.tabs-area,
.mail-wrapper,
.mail-reply,
.panel,
.thumbnail,
.navbar{
  -webkit-box-shadow: 0 1px 0px rgba(0,0,0,0.12), 0 1px 1px rgba(0,0,0,0.24);
  -moz-box-shadow: 0 1px 0px rgba(0,0,0,0.12), 0 1px 1px rgba(0,0,0,0.24);
  -ms-box-shadow: 0 1px 0px rgba(0,0,0,0.12), 0 1px 1px rgba(0,0,0,0.24);
  -o-box-shadow: 0 1px 0px rgba(0,0,0,0.12), 0 1px 1px rgba(0,0,0,0.24);
  box-shadow: 0 1px 0px rgba(0,0,0,0.12), 0 1px 1px rgba(0,0,0,0.24);
}

/*start: box v-1*/
.box-v1{}
  .box-v1 .panel-body{padding-top: 40px;}
    .box-v1 .panel-body h1{line-height: .5;}
    .box-v1 .panel-body hr{width: 60%;}

/*start: box v-2*/
.box-v2{}
  .box-v2 .panel-heading{}
    .box-v2 .panel-heading .box-v2-cover{width: 100%;}
    .box-v2 .panel-heading .box-v2-detail{
      position: absolute;
      z-index: 9;
      margin-top: -100px;
      margin-left: 10px;
      color: #fff;
      font-weight: 400;
    }
      .box-v2 .panel-heading .box-v2-detail img{
        width: 60px;
        height: 60px;
        border: 3px solid #fff;
      }
    .box-v2 .panel-body{
      background: #E30049;
      color: #fff;
      line-height: 0;
      padding-bottom: 30px;
    }

/*start: box v-3*/
.box-v3{padding: 30px;}

/*start: box v-4*/
.box-v4{}
  .box-v4 .panel-heading{padding: 20px;}
  .box-v4 .panel-body{}
    .box-v4 .panel-body .box-v4-alert{
      width: 100%;
      padding: 30px;
      background-color: #F9F9F9;
      border-top: 1px solid #e7eaec;
      border-bottom: 1px solid #e7eaec;
      margin-bottom: 10px;
    }

/*start: box v-5*/
.box-v5{}
  .box-v5 .panel-heading textarea{
    height: 150px;
    width: 100%;
    border:1px solid #ddd;
    padding: 10px;
  }
  .box-v5 .tool a{
    padding: 10px;
  }


/*start: box v-6*/
.box-v6 img {
  width: 30px;
  height: 30px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
}

.box-v6 p {
  margin-top: -10px;
}

.box-v6 .box-v6-content-bg {
  position: absolute;
  background-color: #FBF9F9;
  height: 127px;
}

.box-v6 .box-v6-content{
  padding-top: 20px;
}

.box-v6 .box-v6-progress{
  padding: 20px;
}

/*start box v7*/

.box-v7{color:#747373;}
.box-v7 .box-v7-avatar{
  width: 50px;
  height: 50px;
  margin-right: 10px;
  border-radius: 100%;
}
.box-v7 .box-v7-header{padding: 10px}
.box-v7 .box-v7-header p{line-height: 1px;}
.box-v7 .box-v7-text{padding: 10px;}
.box-v7 .box-v7-header .box-v7-menu{font-size: 20px;cursor: pointer;color:#747373 !important;}
.box-v7 .box-v7-comment{background: #f4f4f4;margin-top: 20px;padding: 20px;}
.box-v7 .box-v7-comment:after{
  position: absolute;
  top: -10px;
  left: 4px;
  display: inline-block;
  border-right: 10px solid transparent;
  border-bottom: 10px solid  #f4f4f4;
  border-left: 10px solid transparent;
  content: '';
}
.box-v7 .box-v7-comment .media-left{padding-right:0px;}
.box-v7 .box-v7-comment .box-v7-avatar{width: 40px !important;height: 40px !important;}
.box-v7 .box-v7-commenttextbox{height: 60px;width:100% !important;padding:10px;border-radius: 0px !important;border:1px solid #ddd;}

/*======================== Wheather ===========================*/

.wheather {
  padding-right: 10%;
}

.sub-wheather {
  width: 100px;
  height: 100px;
}

/*---cloudy----*/

.cloudy {
  height: 40px;
  width: 100px;
  background: #fff;
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -ms-border-radius: 40px;
  -o-border-radius: 40px;
  border-radius: 40px;
  border: 2px solid #ddd;
  position: relative;
  margin-top: 20px;
}

.cloudy:before {
  content: "";
  position: absolute;
  top: -10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  height: 30px;
  width: 30px;
  left: 15px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  border: 2px solid #ddd;
  border-bottom-color: transparent;
  border-right-color: transparent;
  background: #fff;
  -webkit-transform: rotate(40deg);
  -moz-transform: rotate(40deg);
  -ms-transform: rotate(40deg);
  -o-transform: rotate(40deg);
  transform: rotate(40deg);
}

.cloudy:after {
  content: "";
  position: absolute;
  height: 50px;
  width: 50px;
  top: -20px;
  left: 32px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  border: 2px solid #ddd;
  border-bottom-color: transparent;
  background: #fff;
  border-right-color: transparent;
  -webkit-transform: rotate(55deg);
  -moz-transform: rotate(55deg);
  -ms-transform: rotate(55deg);
  -o-transform: rotate(55deg);
  transform: rotate(55deg);
}

.cloudy .shadow {
  background-color: #F7F7F7;
  height: 20px;
  width: 100px;
  position: absolute;
  margin-top: 80px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
}

/*---rainy----*/

.rainy {
  height: 40px;
  width: 100px;
  background: #F9F9F9;
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -ms-border-radius: 40px;
  -o-border-radius: 40px;
  border-radius: 40px;
  border: 2px solid #F7F5F5;
  position: relative;
  margin-top: 20px;
}

.rainy:before {
  content: "";
  position: absolute;
  top: -10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  height: 30px;
  width: 30px;
  left: 15px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid #F3ECEC;
  border-bottom-color: transparent;
  border-right-color: transparent;
  background: #F9F9F9;
  -webkit-transform: rotate(40deg);
  -moz-transform: rotate(40deg);
  -ms-transform: rotate(40deg);
  -o-transform: rotate(40deg);
  transform: rotate(40deg);
}

.rainy:after {
  content: "";
  position: absolute;
  height: 50px;
  width: 50px;
  top: -20px;
  left: 32px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #F5F3F3;
  border-bottom-color: transparent;
  background: #F9F9F9;
  border-right-color: transparent;
  -webkit-transform: rotate(55deg);
  -moz-transform: rotate(55deg);
  -ms-transform: rotate(55deg);
  -o-transform: rotate(55deg);
  transform: rotate(55deg);
}

.rainy .shadow {
  height: 10px;
  width: 60px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  background-color: #C4EDFF;
  -webkit-transition: height .25s ease, width .25s ease;
  -moz-transition: height .25s ease, width .25s ease;
  -ms-transition: height .25s ease, width .25s ease;
  -o-transition: height .25s ease, width .25s ease;
  transition: height .25s ease, width .25s ease;
  -webkit-transition: height .25s ease, width .25s ease;
  -moz-transition: height .25s ease, width .25s ease;
  -ms-transition: height .25s ease, width .25s ease;
  -o-transition: height .25s ease, width .25s ease;
  transition: height .25s ease, width .25s ease;
  -webkit-transform: transform: translate(-50%, -50%);
  -moz-transform: transform: translate(-50%, -50%);
  -ms-transform: transform: translate(-50%, -50%);
  -o-transform: transform: translate(-50%, -50%);
  transform: transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-top: 90px;
  margin-left: 50px;
}

.rainy .shadow:before,
.rainy .shadow:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid #F1F9FD;
}

.rainy .shadow:before {
  -webkit-animation: rippleX 2s linear infinite;
  -moz-animation: rippleX 2s linear infinite;
  -ms-animation: rippleX 2s linear infinite;
  -o-animation: rippleX 2s linear infinite;
  animation: rippleX 2s linear infinite;
  -webkit-animation: rippleX 2s linear infinite;
  -moz-animation: rippleX 2s linear infinite;
  -ms-animation: rippleX 2s linear infinite;
  -o-animation: rippleX 2s linear infinite;
  animation: rippleX 2s linear infinite;
}

.rainy .shadow:after {
  -webkit-animation: ripple 2s linear 1s infinite;
  -moz-animation: ripple 2s linear 1s infinite;
  -ms-animation: ripple 2s linear 1s infinite;
  -o-animation: ripple 2s linear 1s infinite;
  animation: ripple 2s linear 1s infinite;
  -webkit-animation: rippleX 2s linear 1s infinite;
  -moz-animation: rippleX 2s linear 1s infinite;
  -ms-animation: rippleX 2s linear 1s infinite;
  -o-animation: rippleX 2s linear 1s infinite;
  animation: rippleX 2s linear 1s infinite;
}

@-webkit-keyframes rippleX {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  75% {
    -webkit-transform: scale(1.75);
    -moz-transform: scale(1.75);
    -ms-transform: scale(1.75);
    -o-transform: scale(1.75);
    transform: scale(1.75);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    -o-transform: scale(2);
    transform: scale(2);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes rippleX {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  75% {
    -webkit-transform: scale(1.75);
    -moz-transform: scale(1.75);
    -ms-transform: scale(1.75);
    -o-transform: scale(1.75);
    transform: scale(1.75);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    -o-transform: scale(2);
    transform: scale(2);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
.rain {
  padding-left: 30px;
  margin-top: -10px;
}

.droplet {
  width: 2px;
  height: 10px;
  margin-top: 0px;
  background: -webkit-radial-gradient(center, farthest-corner, #C4EDFF, #C4EDFF);
  background: -moz-radial-gradient(center, farthest-corner, #C4EDFF, #C4EDFF);
  background: -ms-radial-gradient(center, farthest-corner, #C4EDFF, #C4EDFF);
  background: -o-radial-gradient(center, farthest-corner, #C4EDFF, #C4EDFF);
  background: radial-gradient(center, farthest-corner, #C4EDFF, #C4EDFF);
  background: -webkit-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -moz-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -ms-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -o-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -webkit-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -moz-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -ms-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: -o-radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  background: radial-gradient(center, farthest-corner, #A4DDF5, #76CBF1);
  position: absolute;
  -webkit-border-radius: 10px 10px 10px 10px / 30px 30px 10px 10px;
  -moz-border-radius: 10px 10px 10px 10px / 30px 30px 10px 10px;
  -ms-border-radius: 10px 10px 10px 10px / 30px 30px 10px 10px;
  -o-border-radius: 10px 10px 10px 10px / 30px 30px 10px 10px;
  border-radius: 10px 10px 10px 10px / 30px 30px 10px 10px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.droplet2,
.droplet5 {
  margin-left: 20px;
}

.droplet3,
.droplet6 {
  margin-left: 40px;
}

.droplet1,
.droplet2,
.droplet3 {
  -webkit-animation: rain2 linear 1.5s infinite .75s;
  -moz-animation: rain2 linear 1.5s infinite .75s;
  -ms-animation: rain2 linear 1.5s infinite .75s;
  -o-animation: rain2 linear 1.5s infinite .75s;
  animation: rain2 linear 1.5s infinite .75s;
  -webkit-animation: rain2 linear 1.5s infinite .75s;
  -moz-animation: rain2 linear 1.5s infinite .75s;
  -ms-animation: rain2 linear 1.5s infinite .75s;
  -o-animation: rain2 linear 1.5s infinite .75s;
  animation: rain2 linear 1.5s infinite .75s;
  -webkit-animation: rain2 linear 1.5s infinite .75s;
  -moz-animation: rain2 linear 1.5s infinite .75s;
  -ms-animation: rain2 linear 1.5s infinite .75s;
  -o-animation: rain2 linear 1.5s infinite .75s;
  animation: rain2 linear 1.5s infinite .75s;
}

.droplet4,
.droplet5,
.droplet6 {
  -webkit-animation: rain linear 1.5s infinite;
  -moz-animation: rain linear 1.5s infinite;
  -ms-animation: rain linear 1.5s infinite;
  -o-animation: rain linear 1.5s infinite;
  animation: rain linear 1.5s infinite;
  -webkit-animation: rain linear 1.5s infinite;
  -moz-animation: rain linear 1.5s infinite;
  -ms-animation: rain linear 1.5s infinite;
  -o-animation: rain linear 1.5s infinite;
  animation: rain linear 1.5s infinite;
  -webkit-animation: rain linear 1.5s infinite;
  -moz-animation: rain linear 1.5s infinite;
  -ms-animation: rain linear 1.5s infinite;
  -o-animation: rain linear 1.5s infinite;
  animation: rain linear 1.5s infinite;
}

@-webkit-keyframes rain {
  0% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  90% {
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);

  }
  100% {
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);

  }
}
@-moz-keyframes rain {
  0% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  90% {
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@-webkit-keyframes rain2 {
  0% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);

  }
  90% {
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);

  }
}
@-moz-keyframes rain2 {
  0% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  90% {
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-transform: translate(0, 40px);
    -moz-transform: translate(0, 40px);
    -ms-transform: translate(0, 40px);
    -o-transform: translate(0, 40px);
    transform: translate(0, 40px);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-transform: translate(0, 60px);
    -moz-transform: translate(0, 60px);
    -ms-transform: translate(0, 60px);
    -o-transform: translate(0, 60px);
    transform: translate(0, 60px);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
/*================== Stormy ========================*/
.stormy {
  background-color: #989898;
  z-index: 10;
}

.stormy:after {
  background-color: #989898;
}

.stormy:before {
  background-color: #989898;
}

.thunder {
  position: absolute;
  position: absolute;
  height: 50px;
  width: 50px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  margin-left: 30px;
  margin-top: 0px;
  -webkit-animation: stormy_thunder 2s steps(1, end) infinite;
  -moz-animation: stormy_thunder 2s steps(1, end) infinite;
  -ms-animation: stormy_thunder 2s steps(1, end) infinite;
  -o-animation: stormy_thunder 2s steps(1, end) infinite;
  animation: stormy_thunder 2s steps(1, end) infinite;
}

.thunder:after,
.thunder:before {
  position: absolute;
  content: '';
  height: 100%;
  width: 100%;
  top: 0%;
  left: 0%;
}

.thunder:before {
  background: -webkit-linear-gradient(transparent 0%, yellow 0%), linear-gradient(to top left, #ffff00 43%, #ffff00 43%, #ffff00 44%, transparent 44%), linear-gradient(to top left, transparent 56%, #ffff00 56%, #ffff00 57%, #ffff00 57%), linear-gradient(transparent 0%, #ffff00 0%), linear-gradient(to top left, #ffff00 51%, transparent 51%), linear-gradient(to top left, transparent 49%, #ffff00 49%);
  background: -moz-linear-gradient(transparent 0%, yellow 0%), linear-gradient(to top left, #ffff00 43%, #ffff00 43%, #ffff00 44%, transparent 44%), linear-gradient(to top left, transparent 56%, #ffff00 56%, #ffff00 57%, #ffff00 57%), linear-gradient(transparent 0%, #ffff00 0%), linear-gradient(to top left, #ffff00 51%, transparent 51%), linear-gradient(to top left, transparent 49%, #ffff00 49%);
  background: -ms-linear-gradient(transparent 0%, yellow 0%), linear-gradient(to top left, #ffff00 43%, #ffff00 43%, #ffff00 44%, transparent 44%), linear-gradient(to top left, transparent 56%, #ffff00 56%, #ffff00 57%, #ffff00 57%), linear-gradient(transparent 0%, #ffff00 0%), linear-gradient(to top left, #ffff00 51%, transparent 51%), linear-gradient(to top left, transparent 49%, #ffff00 49%);
  background: -o-linear-gradient(transparent 0%, yellow 0%), linear-gradient(to top left, #ffff00 43%, #ffff00 43%, #ffff00 44%, transparent 44%), linear-gradient(to top left, transparent 56%, #ffff00 56%, #ffff00 57%, #ffff00 57%), linear-gradient(transparent 0%, #ffff00 0%), linear-gradient(to top left, #ffff00 51%, transparent 51%), linear-gradient(to top left, transparent 49%, #ffff00 49%);
  background: linear-gradient(transparent 0%, yellow 0%), linear-gradient(to top left, #ffff00 43%, #ffff00 43%, #ffff00 44%, transparent 44%), linear-gradient(to top left, transparent 56%, #ffff00 56%, #ffff00 57%, #ffff00 57%), linear-gradient(transparent 0%, #ffff00 0%), linear-gradient(to top left, #ffff00 51%, transparent 51%), linear-gradient(to top left, transparent 49%, #ffff00 49%);
  -webkit-background-size: 20% 40%, 22% 42%, 22% 42%, 23% 42%, 23% 42%, 23% 42%;
  -moz-background-size: 20% 40%, 22% 42%, 22% 42%, 23% 42%, 23% 42%, 23% 42%;
  -ms-background-size: 20% 40%, 22% 42%, 22% 42%, 23% 42%, 23% 42%, 23% 42%;
  -o-background-size: 20% 40%, 22% 42%, 22% 42%, 23% 42%, 23% 42%, 23% 42%;
  background-size: 20% 40%, 22% 42%, 22% 42%, 23% 42%, 23% 42%, 23% 42%;
  background-position: 50% 50%, 32% 5%, 70% 100%, 50% 50%, 33% 7%, 69% 98%;
  background-repeat: no-repeat;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -o-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: skewY(-30deg) skewX(-30deg);
  -moz-transform: skewY(-30deg) skewX(-30deg);
  -ms-transform: skewY(-30deg) skewX(-30deg);
  -o-transform: skewY(-30deg) skewX(-30deg);
  transform: skewY(-30deg) skewX(-30deg);
  z-index: 2;
}

@keyframes stormy_thunder {
  0% {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);

  }
  5% {
    -webkit-transform: rotate(-3deg);
    -moz-transform: rotate(-3deg);
    -ms-transform: rotate(-3deg);
    -o-transform: rotate(-3deg);
    transform: rotate(-3deg);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  10% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-opacity: 0.5;
    -moz-opacity: 0.5;
    -ms-opacity: 0.5;
    -o-opacity: 0.5;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  15% {
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
/*============== Snowy ===================*/
.snowy {
  z-index: 10;
  background-color: #fff;
}

.snowy:after {
  background-color: #fff;
}

.snowy:before {
  background-color: #fff;
}

.snowy .shadow {
  background-color: #F7F7F7;
  height: 20px;
  width: 100px;
  position: absolute;
  margin-top: 80px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  z-index: -10;
}

.snowy .shadow:after {
  display: none;
}

.snowy .shadow:before {
  display: none;
}

.snowy-sub-wheather .droplet {
  width: 8px;
  height: 8px;
  margin-top: 0px;
  background: #fff !important;
  border: 1px solid #F4F4F4;
}

/*=========== Suny ================*/

.suny {
  margin-top: 20px;
  margin-left: 30px;
}

.sun {
  width: 60px;
  height: 60px;
  -webkit-box-shadow: 0px 0px 113px 23px #fcfc14;
  -moz-box-shadow: 0px 0px 113px 23px #fcfc14;
  -ms-box-shadow: 0px 0px 113px 23px #fcfc14;
  -o-box-shadow: 0px 0px 113px 23px #fcfc14;
  box-shadow: 0px 0px 113px 23px #fcfc14;
  background: #f9db62;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  margin-left: 20px;
}

.mount {
  width: 0;
  height: 0;
  height: 0;
  position: absolute;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 60px solid #12CC5C;
}

.mount1 {
  margin-left: 30px;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 40px solid #08BF51;
  margin-top: 20px;
}

.mount2 {
  margin-left: 60px;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 40px solid #2BA01C;
  margin-top: 20px;
}

/*======== Mostly Suny ============*/

.mostly-suny .cloudy {
  position: absolute;
  margin-top: -20px;
  -webkit-animation: moveclouds 10s linear infinite;
  -moz-animation: moveclouds 10s linear infinite;
  -ms-animation: moveclouds 10s linear infinite;
  -o-animation: moveclouds 10s linear infinite;
  animation: moveclouds 10s linear infinite;
  -webkit-animation: moveclouds 10s linear infinite;
  -moz-animation: moveclouds 10s linear infinite;
  -ms-animation: moveclouds 10s linear infinite;
  -o-animation: moveclouds 10s linear infinite;
  animation: moveclouds 10s linear infinite;
  -webkit-animation: moveclouds 10s linear infinite;
  -moz-animation: moveclouds 10s linear infinite;
  -ms-animation: moveclouds 10s linear infinite;
  -o-animation: moveclouds 10s linear infinite;
  animation: moveclouds 10s linear infinite;
}

@-webkit-keyframes moveclouds {
  0% {
    margin-left: 50px;
  }
  100% {
    margin-left: -50px;
  }
}
@-moz-keyframes moveclouds {
  0% {
    margin-left: 50px;
  }
  100% {
    margin-left: -50px;
  }
}
@-o-keyframes moveclouds {
  0% {
    margin-left: 50px;
  }
  100% {
    margin-left: -50px;
  }
}
/*============ Hazy ================*/

.hazy {
  -webkit-animation: moveclouds 10s linear infinite;
  -moz-animation: moveclouds 10s linear infinite;
  -ms-animation: moveclouds 10s linear infinite;
  -o-animation: moveclouds 10s linear infinite;
  animation: moveclouds 10s linear infinite;
  -webkit-animation: moveclouds 10s linear infinite;
  -moz-animation: moveclouds 10s linear infinite;
  -ms-animation: moveclouds 10s linear infinite;
  -o-animation: moveclouds 10s linear infinite;
  animation: moveclouds 10s linear infinite;
  -webkit-animation: moveclouds 10s linear infinite;
  -moz-animation: moveclouds 10s linear infinite;
  -ms-animation: moveclouds 10s linear infinite;
  -o-animation: moveclouds 10s linear infinite;
  animation: moveclouds 10s linear infinite;
}

.hazy,
.hazy:after,
.hazy:before {
  background-color: #ddd;
  -webkit-box-shadow: 0px 0px 80px 23px #ddd;
  -moz-box-shadow: 0px 0px 80px 23px #ddd;
  -ms-box-shadow: 0px 0px 80px 23px #ddd;
  -o-box-shadow: 0px 0px 80px 23px #ddd;
  box-shadow: 0px 0px 80px 23px #ddd;
}

/*============= Tornado ================*/



.tornado .cloudy {
  background-color: #ddd;
}

.tornado .cloudy:after {
  background-color: #ddd;
}

.tornado .cloudy:before {
  background-color: #ddd;
}

.tornado .wind {
  margin-top: 5px;
  margin-left: 10px;
  background-color: #ddd;
  width: 80px;
  height: 2px;
  -webkit-border-radius: 20%;
  -moz-border-radius: 20%;
  -ms-border-radius: 20%;
  -o-border-radius: 20%;
  border-radius: 20%;
  -webkit-animation-name: spinner;
  -moz-animation-name: spinner;
  -ms-animation-name: spinner;
  -o-animation-name: spinner;
  animation-name: spinner;
  -webkit-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -ms-animation-timing-function: linear;
  -o-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  -ms-animation-iteration-count: infinite;
  -o-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-duration: 2s;
  -moz-animation-duration: 2s;
  -ms-animation-duration: 2s;
  -o-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-name: spinner;
  -moz-animation-name: spinner;
  -ms-animation-name: spinner;
  -o-animation-name: spinner;
  animation-name: spinner;
  -webkit-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -ms-animation-timing-function: linear;
  -o-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  -ms-animation-iteration-count: infinite;
  -o-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-duration: 2s;
  -moz-animation-duration: 2s;
  -ms-animation-duration: 2s;
  -o-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.tornado .wind2 {
  width: 60px;
  margin-left: 10px;
}

.tornado .wind3 {
  width: 40px;
  margin-left: 30px;
}

.tornado .wind4 {
  width: 20px;
  margin-left: 30px;
}

@-webkit-keyframes spinner {
  from {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
  }
  to {
    -webkit-transform: rotateY(-360deg);
    -moz-transform: rotateY(-360deg);
    -ms-transform: rotateY(-360deg);
    -o-transform: rotateY(-360deg);
    transform: rotateY(-360deg);
  }
}
@keyframes spinner {
  from {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
  }
  to {
    -webkit-transform: rotateY(-360deg);
    -moz-transform: rotateY(-360deg);
    -ms-transform: rotateY(-360deg);
    -o-transform: rotateY(-360deg);
    transform: rotateY(-360deg);
    -webkit-transform: rotateY(-360deg);
    -moz-transform: rotateY(-360deg);
    -ms-transform: rotateY(-360deg);
    -o-transform: rotateY(-360deg);
    transform: rotateY(-360deg);
    -webkit-transform: rotateY(-360deg);
    -moz-transform: rotateY(-360deg);
    -ms-transform: rotateY(-360deg);
    -o-transform: rotateY(-360deg);
    transform: rotateY(-360deg);
  }
}
/*==================== Toggle On Off ======================*/

.onoffswitch {
  position: relative;
  width: 99px;
  user-select: none;
  user-select: none;
  user-select: none;
}

.onoffswitch-checkbox {
  display: none;
}

.onoffswitch-label {
  display: block;
  overflow: hidden;
  cursor: pointer;
  height: 36px;
  padding: 0;
  line-height: 36px;
  border: 2px solid #CCCCCC;
  -webkit-border-radius: 36px;
  -moz-border-radius: 36px;
  -ms-border-radius: 36px;
  -o-border-radius: 36px;
  border-radius: 36px;
  background-color: #FFFFFF;
  -webkit-transition: background-color 0.3s ease-in;
  -moz-transition: background-color 0.3s ease-in;
  -ms-transition: background-color 0.3s ease-in;
  -o-transition: background-color 0.3s ease-in;
  transition: background-color 0.3s ease-in;
}

.onoffswitch-label:before {
  content: "";
  display: block;
  width: 36px;
  margin: 0px;
  background: #FFFFFF;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 61px;
  border: 2px solid #CCCCCC;
  -webkit-border-radius: 36px;
  -moz-border-radius: 36px;
  -ms-border-radius: 36px;
  -o-border-radius: 36px;
  border-radius: 36px;
  -webkit-transition: all 0.3s ease-in 0s;
  -moz-transition: all 0.3s ease-in 0s;
  -ms-transition: all 0.3s ease-in 0s;
  -o-transition: all 0.3s ease-in 0s;
  transition: all 0.3s ease-in 0s;
}

.onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #FBFBFB;
}

.onoffswitch-checkbox:checked + .onoffswitch-label,
.onoffswitch-checkbox:checked + .onoffswitch-label:before {
  border-color: #F4F4F4;
}

.onoffswitch-checkbox:checked + .onoffswitch-label:before {
  right: 0px;
}

/*=========== on off switch default ==============*/

.mini-onoffswitch {
  position: relative;
  width: 60px;
  user-select: none;
  user-select: none;
  user-select: none;
}

.mini-onoffswitch .onoffswitch-checkbox {
  display: none;
}

.mini-onoffswitch .onoffswitch-label {
  display: block;
  overflow: hidden;
  cursor: pointer;
  height: 20px;
  padding: 0;
  line-height: 36px;
  border: 1px solid #CCCCCC;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
  background-color: #FFFFFF;
  -webkit-transition: background-color 0.3s ease-in;
  -moz-transition: background-color 0.3s ease-in;
  -ms-transition: background-color 0.3s ease-in;
  -o-transition: background-color 0.3s ease-in;
  transition: background-color 0.3s ease-in;
}

.mini-onoffswitch .onoffswitch-label:before {
  content: "";
  display: block;
  width: 30px;
  margin: 0px;
  background: #FFFFFF;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 31px;
  border: 1px solid #CCCCCC;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
  -webkit-transition: all 0.3s ease-in 0s;
  -moz-transition: all 0.3s ease-in 0s;
  -ms-transition: all 0.3s ease-in 0s;
  -o-transition: all 0.3s ease-in 0s;
  transition: all 0.3s ease-in 0s;
}

.mini-onoffswitch .onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #FBFBFB;
}

.mini-onoffswitch .onoffswitch-checkbox:checked + .onoffswitch-label,
.onoffswitch-checkbox:checked + .onoffswitch-label:before {
  border-color: #F4F4F4;
}

.mini-onoffswitch .onoffswitch-checkbox:checked + .onoffswitch-label:before {
  right: 0px;
}

.onoffswitch-primary .onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #1c84c6;
}

.onoffswitch-info .onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #73CAEF;
}

.onoffswitch-warning .onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #f0ad4e;
}

.onoffswitch-success .onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #20C572;
}

.onoffswitch-danger .onoffswitch-checkbox:checked + .onoffswitch-label {
  background-color: #FF6656;
}

/*=============== mini-TimeLine ==================*/

.mini-timeline {
  list-style: none;
  position: relative;
  float: left;
  width: 100%;
  padding-bottom: 20px;
  margin-top: 10px;
}

.mini-timeline:before {
  top: 0;
  bottom: 0;
  position: absolute;
  content: " ";
  width: 3px;
  background-color: #eeeeee;
  margin-left: -21px;
}

.mini-timeline > li {
  margin-bottom: 20px;
  position: relative;
}

.mini-timeline > li:before,
.mini-timeline > li:after {
  content: " ";
  display: table;
}

.mini-timeline > li:after {
  clear: both;
}

.mini-timeline > li:before,
.mini-timeline > li:after {
  content: " ";
  display: table;
}

.mini-timeline > li:after {
  clear: both;
}

.mini-timeline .mini-timeline-highlight {
  height: 50px;
  margin-left: -21px;
}

.mini-timeline .mini-timeline-info {
  border-left: 3px solid #73CAEF;
}

.mini-timeline .mini-timeline-info:before {
  margin-left: -10px !important;
  border: 2px solid #73CAEF !important;
}

.mini-timeline .mini-timeline-success {
  border-left: 3px solid #20C572;
}

.mini-timeline .mini-timeline-success:before {
  margin-left: -10px !important;
  border: 2px solid #20C572 !important;
}

.mini-timeline .mini-timeline-warning {
  border-left: 3px solid #f0ad4e;
}

.mini-timeline .mini-timeline-warning:before {
  margin-left: -10px !important;
  border: 2px solid #f0ad4e !important;
}

.mini-timeline .mini-timeline-danger {
  border-left: 3px solid #FF6656;
}

.mini-timeline .mini-timeline-danger:before {
  margin-left: -10px !important;
  border: 2px solid #FF6656 !important;
}

.mini-timeline .mini-timeline-panel {
  position: absolute;
  padding-left: 10px;
  width: 200px;
}

.mini-timeline .mini-timeline-panel .time {
  color: #98a6ad;
}

.mini-timeline .mini-timeline-panel p {
  margin-top: -10px;
  color: #58666e;
}

.mini-timeline .mini-timeline-highlight:before {
  width: 15px;
  height: 15px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  content: " ";
  margin-left: -6px;
  border: 2px solid #ddd;
  background-color: #F4F4F4;
}

/*========== list Timeline ===========*/



.list-timeline .list-timeline-section {
  background-color: #FFF;
  color: #BBB1B1;
  padding: 10px;
}

.list-timeline-icon{
  font-size: 2em;
}
.list-timeline-detail{}
.list-timeline-detail p{
  border-left: 2px solid #ddd;
  padding-left: 10px;
}
/*=========Iconic Timeline===========*/



.iconic-timeline .iconic-timeline-icon i {
  background-color: #F4F4F4;
  border: 1px solid #ddd;
  padding: 10px;
  margin-right: -2px;
  color: #676A6C;
}

.iconic-timeline .iconic-timeline-icon {
  padding: 0px;
  padding-top: 9px;
  text-align: right;
  float: left;
  z-index: 9;
}

.iconic-timeline .iconic-timeline-detail {
  border-left: 1px solid #ddd;
  background-color: #fff;
}

.iconic-timeline .iconic-timeline-detail:hover {
  background-color: #F4F4F4;
}

.iconic-timeline .iconic-timeline-detail p {
  margin-top: -10px;
}

.iconic-timeline .icon-primary {
  background-color: #1c84c6 !important;
  color: #fff !important;
}

.iconic-timeline .icon-success {
  background-color: #27C24C !important;
  color: #fff !important;
}

.iconic-timeline .icon-info {
  background-color: #73CAEF !important;
  color: #fff !important;
}

.iconic-timeline .icon-danger {
  background-color: #FF6656 !important;
  color: #fff !important;
}

.iconic-timeline .icon-warning {
  background-color: #F0AD4E !important;
  color: #fff !important;
}

.iconic-timeline .icon-default {
  background-color: #857E7E !important;
  color: #fff !important;
}

/*=============== Default TimeLine =================*/

.timeline {
  list-style: none;
  padding: 20px 0 20px;
  position: relative;
}

.timeline:before {
  top: 0;
  bottom: 0;
  position: absolute;
  content: " ";
  width: 3px;
  background-color: #eeeeee;
  left: 50%;
  margin-left: -1.5px;
}

.timeline > li {
  margin-bottom: 20px;
  position: relative;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li > .timeline-panel {
  width: 46%;
  float: left;
  border: 1px solid #d4d4d4;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  padding: 20px;
  position: relative;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
  -moz-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
  -ms-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
  -o-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
}

.timeline > li > .timeline-panel:before {
  position: absolute;
  top: 26px;
  right: -15px;
  display: inline-block;
  border-top: 15px solid transparent;
  border-left: 15px solid #ccc;
  border-right: 0 solid #ccc;
  border-bottom: 15px solid transparent;
  content: " ";
}

.timeline > li > .timeline-panel:after {
  position: absolute;
  top: 27px;
  right: -14px;
  display: inline-block;
  border-top: 14px solid transparent;
  border-left: 14px solid #fff;
  border-right: 0 solid #fff;
  border-bottom: 14px solid transparent;
  content: " ";
}

.timeline > li > .timeline-badge {
  color: #fff;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 1.4em;
  text-align: center;
  position: absolute;
  top: 16px;
  left: 50%;
  margin-left: -25px;
  background-color: #999999;
  z-index: 100;
  -webkit-border-top-right-radius: 50%;
  -moz-border-top-right-radius: 50%;
  -ms-border-top-right-radius: 50%;
  -o-border-top-right-radius: 50%;
  border-top-right-radius: 50%;
  -webkit-border-top-left-radius: 50%;
  -moz-border-top-left-radius: 50%;
  -ms-border-top-left-radius: 50%;
  -o-border-top-left-radius: 50%;
  border-top-left-radius: 50%;
  -webkit-border-bottom-right-radius: 50%;
  -moz-border-bottom-right-radius: 50%;
  -ms-border-bottom-right-radius: 50%;
  -o-border-bottom-right-radius: 50%;
  border-bottom-right-radius: 50%;
  -webkit-border-bottom-left-radius: 50%;
  -moz-border-bottom-left-radius: 50%;
  -ms-border-bottom-left-radius: 50%;
  -o-border-bottom-left-radius: 50%;
  border-bottom-left-radius: 50%;
}

.timeline > li.timeline-inverted > .timeline-panel {
  float: right;
}

.timeline > li.timeline-inverted > .timeline-panel:before {
  border-left-width: 0;
  border-right-width: 15px;
  left: -15px;
  right: auto;
}

.timeline > li.timeline-inverted > .timeline-panel:after {
  border-left-width: 0;
  border-right-width: 14px;
  left: -14px;
  right: auto;
}

.timeline-badge.primary {
  background-color: #2e6da4 !important;
}

.timeline-badge.success {
  background-color: #3f903f !important;
}

.timeline-badge.warning {
  background-color: #f0ad4e !important;
}

.timeline-badge.danger {
  background-color: #d9534f !important;
}

.timeline-badge.info {
  background-color: #5bc0de !important;
}

.timeline-title {
  margin-top: 0;
  color: inherit;
}

.timeline-body > p,
.timeline-body > ul {
  margin-bottom: 0;
}

.timeline-body > p + p {
  margin-top: 5px;
}

@media (max-width: 767px) {
  ul.timeline:before {
    left: 40px;
  }
  ul.timeline > li > .timeline-panel {
    width: calc(100% - 90px);
    width: -moz-calc(100% - 90px);
    width: -webkit-calc(100% - 90px);
  }
  ul.timeline > li > .timeline-badge {
    left: 15px;
    margin-left: 0;
    top: 16px;
  }
  ul.timeline > li > .timeline-panel {
    float: right;
  }
  ul.timeline > li > .timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
  }
  ul.timeline > li > .timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
  }
}
/*carousel*/

.img-responsive,
.thumbnail > img,
.thumbnail a > img,
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  display: block;
  width: 100%;
  height: auto;
}

.carousel-thumb-img li {
  float: left;
  list-style: none;
  margin: 10px;
  cursor: pointer;
}

.carousel-thumb-img-li.active img {
  border: 5px solid #ddd;
}

.carousel-thumb-img-li img {
  width: 80px;
  height: 80px;
}

/* carousel */

#quote-carousel {
  padding: 0 10px 30px 10px;
  margin-top: 30px;
}

/* Control buttons  */

#quote-carousel .carousel-control {
  background: none;
  color: #222;
  font-size: 2.3em;
  text-shadow: none;
  margin-top: 30px;
}

/* Previous button  */

#quote-carousel .carousel-control.left {
  left: -12px;
}

/* Next button  */

#quote-carousel .carousel-control.right {
  right: -12px !important;
}

/* Changes the position of the indicators */

#quote-carousel .carousel-indicators {
  right: 50%;
  top: auto;
  bottom: 0px;
  margin-right: -19px;
}

/* Changes the color of the indicators */

#quote-carousel .carousel-indicators li {
  background: #c0c0c0;
}

#quote-carousel .carousel-indicators .active {
  background: #333333;
}

#quote-carousel img {
  width: 250px;
  height: 100px;
}

/* End carousel */

.item blockquote {
  border-left: none;
  margin: 0;
}

.item blockquote img {
  margin-bottom: 10px;
}

.item blockquote p:before {
  content: "\f10d";
  font-family: 'Fontawesome';
  float: left;
  margin-right: 10px;
}

/*============ Menu 2 (Right Menu) ==========*/

#right-menu-config {
  color: #918C8C;
  padding: 20px;
}

#right-menu-config .btnmore {
  background-color: #ddd;
  border: none;
  width: 100%;
  height: 40px;
  margin-top: 20px;
  color: #444;
}

#right-menu-config .mini-onoffswitch {
  margin-top: 10px;
}


/*====*/

.chart-title {
  height: 90px;
  padding: 10px;
  padding-left: 20px;
}

.chart-title h3 {
  font-weight: 100;
  color: #918C8C;
}

/*=========== Email ===========*/

.mail-wrapper {
  background-color: #fff;
  padding: 0px;
}

.mail-wrapper .mail-left {
  padding-bottom: 50px;
}

.mail-wrapper .mail-left .mail-left-header {
  height: 50px;
  padding-top: 50px;
  padding-bottom: 50px;
  font-size: 2em;
}

.mail-wrapper .mail-left .mail-left-header .btncompose-mail {
  width: 100% !important;
}

.mail-wrapper .mail-left .mail-left-content {
  padding-top: 20px;
}

.mail-wrapper .mail-left .mail-left-content h5 {
  color: #928A8A;
}

.mail-wrapper .mail-left .mail-left-content span {
  padding-right: 5px;
}

.mail-wrapper .mail-left .mail-left-content hr {
  color: #918C8C;
  width: 80%;
}

.mail-wrapper .mail-left .mail-left-content li {
  padding: 2px;
}

.mail-wrapper .mail-left .mail-left-content li a {
  color: #918C8C;
  font-weight: 100;
  font-size: 18px;
}

.mail-wrapper .mail-left .mail-left-content li .active {
  font-weight: bold;
}

.mail-wrapper .mail-left .mail-left-content li a:hover {
  background: #fff;
  border-left: 2px solid #918C8C;
}

.mail-wrapper .mail-right {
  padding: 25px;
}

.mail-wrapper .mail-right-header{
  margin-bottom: 30px;
}

.mail-wrapper .mail-right .mail-right-options {
  padding: 20px;
  font-size: 20px;
  color: #918C8C;
  padding-right: 0px;
}

.mail-wrapper .mail-right .mail-right-tool {
  color: #918C8C;
  font-weight: 300;
  padding: 20px;
}

.mail-wrapper .mail-right .mail-right-tool .txtinput {
  width: 100%;
  height: 40px;
  border: none;
  font-size: 15px;
  color: #918C8C;
  font-weight: 200;
  padding: 10px;
}

.mail-wrapper .mail-right .mail-right-tool li {
  float: left;
}

.mail-wrapper .mail-right .mail-right-tool .icheckbox_flat-red {
  margin-top: 15px;
  margin-right: 10px;
}

.mail-wrapper .mail-right .mail-right-tool li a {
  font-size: 18px;
  color: #918C8C;
  font-weight: 500;
  border: 1px solid #E8E8E8;
  margin-right: 5px;
}

.mail-wrapper .mail-right .mail-right-tool li a:hover {
  background: none;
}

.mail-wrapper .mail-right .mail-right-content {
  padding: 10px;
  line-height: 2;
  color: #918C8C;
}

.mail-wrapper .mail-right .mail-right-content .mail-reply {
  padding: 4px;
  border: 1px solid #F4F4F4;
  padding-bottom: 20px;
  margin-top: 200px;
}

.mail-wrapper .mail-right .mail-right-content .mail-attachment {
  padding: 10px;
}

.mail-wrapper .mail-right .mail-right-content .mail-attachment-icon .type-icon {
  font-size: 7em;
}

.mail-wrapper .mail-right .mail-right-content hr {
  color: #918C8C;
  width: 90%;
  margin-top: 20px;
}

.mail-wrapper .mail-right .mail-right-text {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.mail-wrapper .mail-right .mail-right-content .note-toolbar {
  background: #fff !important;
  border: none;
}

.mail-wrapper .mail-right .mail-right-content .btn {
  background: #F4F4F4;
}

.mail-wrapper .mail-right .mail-right-content .note-editable {
  color: #918C8C;
  font-size: 18px;
  font-weight: 400;
}

.mail-wrapper .mail-right .mail-right-content .note-editor {
  border: none;
  box-shadow: none !important;
}

.mail-wrapper .mail-right .mail-right-content a {
  color: #918C8C;
}

.mail-wrapper .mail-right .mail-right-content tr td {
  padding: 10px;
  padding-top: 20px;
  padding-left: 20px;
  border-top: 1px solid 1px solid #E7E7E7;
}

.mail-wrapper .mail-right .searchbox-v1 {
  padding-left: 0px;
}

.mail-wrapper .mail-right .mail-right-content .unread {
  font-weight: bold;
  background-color: #F4F4F4;
}

/*===================== Searchbox V1 ===================*/

.searchbox-v1 {
  padding: 20px;
}

.searchbox-v1 span {
  color: #918C8C;
  font-size: 17px;
}

.searchbox-v1 .txtsearch {
  width: 100%;
  border: none;
  height: 40px;
  padding: 5px;
  padding-left: 10px;
  color: #918C8C;
  font-size: 18px;
  font-weight: 100;
}

.searchbox-v1 .txtsearch:focus {
  outline: none;

}

.searchbox-v1 .input-group-addon {
  background: none;
  border: none;
  padding-right: 5px !important;
}

/*================ TAGS =====================*/

.tags {
  width: 100%;
  padding: 0px;
  padding-top: 20px;
}

.tags li {
  float: left;
  list-style: none;
}

.tags li a {
  background-color: #fff;
  padding: 5px;
  border: 1px solid #ddd;
  font-size: 15px !important;
  display: block;
}

.tags li a:hover {
  -webkit-text-decoration: none;
  -moz-text-decoration: none;
  -ms-text-decoration: none;
  -o-text-decoration: none;
  text-decoration: none;
}

/*========= TYpography =============*/



.typography .windows {
  padding: 20px !important;
  color: #958E8E;
  padding-bottom: 5px !important;
}

/*======== iCOns ========*/

.icon-wrapper,
.icon-wrapper h3,
.icon-wrapper a {
  color: #918C8C;
  font-weight: 300;
}

.icon-wrapper .windows {
  padding: 50px;
}

.icon-wrapper .windows-header {
  padding-left: 5px !important;
}

.icon-wrapper .icon-box,
.icon-wrapper a {
  font-size: 15px;
  padding: 10px;
}

.icon-wrapper .icon-box:hover {
  font-weight: bold;
}

.icon-wrapper .icons,
.icon-wrapper .fa {
  padding: 10px;
  font-size: 2em;
}

/*======= Button Page========*/



.btn-wrapper .btn-3d {
  margin-top: 10px !important;
}

.btn-wrapper .btn{margin-bottom: 10px;}

/*========= Video Page ========*/



.video-v1 .windows {
  text-align: left;
  border-bottom: 4px solid #ddd;
}

.video-v1-time {
  margin-top: -6px;
}

.video-v1 .video-v1-rate,
.video-v2 .video-v2-rate {
  padding-top: 10px;
  float: right;
  color: #FF6656;
  text-align: right;
}

.video-v1 .video-v1-content {
  padding: 20px;
  padding-bottom: 15px;
}

.video-v1 .video-v1-footer {
  padding: 0px;
}

.video-v1 .video-v1-footer span {
  padding: 10px;
  font-size: 15px;
}

.video-v2 .windows {
  text-align: left;
}

.video-v1 .windows .windows-content .video-v2 .video-v2-rate {
  color: #918C8C;
  font-weight: bold;
  font-size: 18px;
}

.video-v2 .video-v2-time {
  padding-bottom: 10px;
}

.video-v2 .video-v2-time:before {
  content: ' ';
  width: 20px;
  height: 20px;
  background-color: #F0F3F4;
  position: absolute;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  left: -8px;
}

.video-v2 .video-v2-footer {
  font-size: 13px;
}

/*=============== Tabs Page ==================*/

.tabs-wrapper .windows {
  padding: 0px !important;
}

.tabs-wrapper .windows-content {
  padding: 0px !important;
  height: 235px;
}

.tabs-wrapper #tabs-demo {
  border: none !important;
}

.tabs-wrapper #tabs-demo li,
.tabs-wrapper #tabs-demo li a {
  border: none !important;
}

.tabs-wrapper #tabs-demo .active a {
  border-bottom: 2px solid #918C8C !important;
}

.tabs-wrapper #tabs-content-demo {
  padding: 20px;
}

.tabs-wrapper .tab-content {
  padding: 20px !important;
  text-align: left;
  color: #8F8686 !important;
}

.tabs-area {
  background-color: #fff;
  padding: 0px;
  margin-top: 20px;
}

.tab-content-v1 {
  border-left: 2px solid #918C8C;
}

.nav-tabs.nav-tabs-v1 {
  border: none !important;
  padding: 10px !important;
}

.nav-tabs.nav-tabs-v1 a {
  color: #918C8C;
  font-weight: bold;
}

.nav-tabs.nav-tabs-v1 .active a {
  border: none !important;
  color: #ddd !important;
}

.nav-tabs.nav-tabs-v1 li a:before {
  content: '';
  position: absolute;
  width: 80%;
  border-bottom: 2px solid #918C8C;
  bottom: 1px;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: -webkit-transform 0.2s ease-in;
  -moz-transition: -webkit-transform 0.2s ease-in;
  -ms-transition: -webkit-transform 0.2s ease-in;
  -o-transition: -webkit-transform 0.2s ease-in;
  transition: -webkit-transform 0.2s ease-in;
  -webkit-transition: transform: 0.2s ease-in;
  -moz-transition: transform: 0.2s ease-in;
  -ms-transition: transform: 0.2s ease-in;
  -o-transition: transform: 0.2s ease-in;
  transition: transform: 0.2s ease-in;
}

.nav-tabs.nav-tabs-v1 li a:focus {
  color: #ddd;
}

.nav-tabs.nav-tabs-v1 li a:hover:before {
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
}

.nav-tabs.nav-tabs-v1 li a:hover {
  background: none !important;
  color: #A19C9C !important;
  border: none;
}

/*tabs v2*/

.nav-tabs.nav-tabs-v2 {
  border-bottom: 1px solid #ddd !important;
  padding-top: 20px;
}





.nav-tabs.nav-tabs-v2 .active a {
  background-color: #f4f4f4 !important;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
}

.tabs-content-v2 {
  background-color: #F4F4F4;
}

/*tabs v3*/

.nav-tabs.nav-tabs-v3 {
  border-bottom: 2px solid #DDD;
  padding-top: 20px;
}

.nav-tabs.nav-tabs-v3 > li.active > a,
.nav-tabs.nav-tabs-v3 > li.active > a:focus,
.nav-tabs.nav-tabs-v3 > li.active > a:hover {
  border-width: 0;
}

.nav-tabs.nav-tabs-v3 > li > a {
  border: none;
  color: #666;
}

.nav-tabs.nav-tabs-v3 > li.active > a,
.nav-tabs.nav-tabs-v3 > li > a:hover {
  border: none;
  color: #4285F4 !important;
  background: transparent;
}

.nav-tabs.nav-tabs-v3 > li > a::after {
  content: "";
  background: #4285F4;
  height: 2px;
  position: absolute;
  width: 100%;
  left: 0px;
  bottom: -1px;
  -webkit-transition: all 250ms ease 0s;
  -moz-transition: all 250ms ease 0s;
  -ms-transition: all 250ms ease 0s;
  -o-transition: all 250ms ease 0s;
  transition: all 250ms ease 0s;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.nav-tabs.nav-tabs-v3 > li.active > a::after,
.nav-tabs.nav-tabs-v3 > li:hover > a::after {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.tab-nav.nav-tabs-v3 > li > a::after {
  background: #21527d none repeat scroll 0% 0%;
  color: #fff;
}

/*tabs4*/

.nav-tabs.nav-tabs-v4 {
  width: 70%;
  margin: auto;
  border: none !important;
   padding-top: 0px !important;
}

.nav-tabs.nav-tabs-v4 li.active a{
  background: #2196f3;
  color: #fff;
  border:none !important;
}

.nav-tabs.nav-tabs-v4 li {
  font-size: 2em;
  width: 25%;
  padding-top: 20px;
}

.nav-tabs.nav-tabs-v4 li a {
  border: 1px solid #1c84c6 !important;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  margin: 10px;
  width: 60px;
  height: 60px;
  text-align: center;
}



/*tabs5*/

.tab-content-v5 p.narrow {
  width: 60%;
  margin: 0px auto;
}

.nav-tabs.nav-tabs-v5 {
  background-color: #f4f4f4;
}

.nav-tabs.nav-tabs-v5 .liner {
  height: 2px;
  background: #ddd;
  position: absolute;
  width: 80%;
  margin: 0 auto;
  left: 0;
  right: 0;
  margin-top: 55px;
  z-index: 1;
}

.nav-tabs.nav-tabs-v5 > li.active > a,
.nav-tabs.nav-tabs-v5 > li.active > a:hover,
.nav-tabs.nav-tabs-v5 > li.active > a:focus {
  color: #555555;
  cursor: default;
  /* background-color: #ffffff; */
  border: 0;
  border-bottom-color: transparent;
}

.nav-tabs.nav-tabs-v5 span.round-tabs {
  width: 70px;
  height: 70px;
  line-height: 70px;
  display: inline-block;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -ms-border-radius: 100px;
  -o-border-radius: 100px;
  border-radius: 100px;
  background: white;
  z-index: 2;
  position: absolute;
  left: 0;
  text-align: center;
  font-size: 25px;
}

.nav-tabs.nav-tabs-v5 span.round-tabs.one {
  color: rgb(34, 194, 34);
  border: 2px solid rgb(34, 194, 34);
}

.nav-tabs.nav-tabs-v5 li.active span.round-tabs.one {
  background: #fff !important;
  border: 2px solid #ddd;
  color: rgb(34, 194, 34);
}

.nav-tabs.nav-tabs-v5 span.round-tabs.two {
  color: #febe29;
  border: 2px solid #febe29;
}

.nav-tabs.nav-tabs-v5 li.active span.round-tabs.two {
  background: #fff !important;
  border: 2px solid #ddd;
  color: #febe29;
}

.nav-tabs.nav-tabs-v5 span.round-tabs.three {
  color: #3e5e9a;
  border: 2px solid #3e5e9a;
}

.nav-tabs.nav-tabs-v5 li.active span.round-tabs.three {
  background: #fff !important;
  border: 2px solid #ddd;
  color: #3e5e9a;
}

.nav-tabs.nav-tabs-v5 span.round-tabs.four {
  color: #f1685e;
  border: 2px solid #f1685e;
}

.nav-tabs.nav-tabs-v5 li.active span.round-tabs.four {
  background: #fff !important;
  border: 2px solid #ddd;
  color: #f1685e;
}

.nav-tabs.nav-tabs-v5 span.round-tabs.five {
  color: #999;
  border: 2px solid #999;
}

.nav-tabs.nav-tabs-v5 li.active span.round-tabs.five {
  background: #fff !important;
  border: 2px solid #ddd;
  color: #999;
}

.nav-tabs.nav-tabs-v5 > li.active > a span.round-tabs {
  background: #fafafa;
}

.nav-tabs.nav-tabs-v5 > li {
  width: 20%;
}

.nav-tabs.nav-tabs-v5 li.active:before {
  content: " ";
  position: absolute;
  left: 45%;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -ms-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
  margin: 0 auto;
  bottom: -2px;
  border: 10px solid transparent;
  border-bottom-color: #fff;
  z-index: 1;
  -webkit-transition: 0.2s ease-in-out;
  -moz-transition: 0.2s ease-in-out;
  -ms-transition: 0.2s ease-in-out;
  -o-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
}

.nav-tabs.nav-tabs-v5 li:after {
  content: " ";
  position: absolute;
  left: 45%;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -ms-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
  margin: 0 auto;
  bottom: 0px;
  border: 5px solid transparent;
  border-bottom-color: #ddd;
  -webkit-transition: 0.1s ease-in-out;
  -moz-transition: 0.1s ease-in-out;
  -ms-transition: 0.1s ease-in-out;
  -o-transition: 0.1s ease-in-out;
  transition: 0.1s ease-in-out;
}

.nav-tabs.nav-tabs-v5 li.active:after {
  content: " ";
  position: absolute;
  left: 45%;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
  margin: 0 auto;
  bottom: 0px;
  border: 10px solid transparent;
  border-bottom-color: #ddd;
}

.nav-tabs.nav-tabs-v5 > li a {
  width: 70px;
  height: 70px;
  margin: 20px auto;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  padding: 0;
}

.tab-content-v5 .btn {
  padding: 12px 20px;
  margin: 20px;
}

/*tabs 6*/

.nav-tabs.nav-tabs-v6 > li.active:after {
  top: 90%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(185, 9, 8, 0);
  border-top-color: #F4F4F4;
  border-width: 30px;
  margin-left: -30px;
}

.nav-tabs.nav-tabs-v6 {
  background-color: #F4F4F4 !important;
  border-bottom: none !important;
}

.nav-tabs.nav-tabs-v6 li {
  padding: 40px;
  padding-bottom: 10px;
}

.nav-tabs.nav-tabs-v6 li a {
  background-color: #F4F4F4 !important;
  border: none !important;
}

.tab-content.tab-content-v6 {
  padding: 30px !important;
}

/*tabs 6*/



.tab-pane {
  position: relative;
}

.tab-content .head {
  font-family: 'Roboto Condensed', sans-serif;
  font-size: 25px;
  text-transform: uppercase;
  padding-bottom: 10px;
}


/*Money exchanger*/

.money-ex {
  background-color: #F9F9F9;
}

.money-ex .money-ex-left {
  background-color: #02A8F3;
  padding: 0px;
  padding-bottom: 10px;
  color: #fff;
}

.money-ex .money-ex-right {
  background-color: #B5B5B5;
  padding: 0px;
  color: #fff;
  padding-bottom: 10px;
}

/*===========Modal============*/

.modal-v1 .modal-header,
.modal-v1 .modal-footer,
.modal-v2 .modal-header,
.modal-v2 .modal-footer {
  border: none !important;
}

.modal-v1 .modal-body {
  text-align: center;
  padding: 50px;
  padding-top: 20px;
}

.modal-v1 .modal-body .btn {
  width: 100%;
  height: 50px;
  margin-top: 10px;
  -webkit-border-radius: 0px !important;
  -moz-border-radius: 0px !important;
  -ms-border-radius: 0px !important;
  -o-border-radius: 0px !important;
  border-radius: 0px !important;
}

.modal-v1 .modal-body .btn-default {
  background-color: #ddd !important;
  font-weight: bold;
}

.modal-v2 {
  border: none;
}

.modal-v2 .modal-content {
  height: 180px;
  background-color: #F4F4F4;
  border: none;
}

.modal-v2 .modal-content-left {
  background-color: #252424;
  height: 100%;
  padding: 20px;
}

.modal-v2 .modal-content-left .modal-content-left-text-1 {
  position: absolute;
  color: #BCC635;
  font-weight: bold;
}

.modal-v2 .modal-content-left .modal-content-left-percent {
  font-size: 5em;
  color: #fff;
}

.modal-v2 .modal-content-left .modal-content-left-icon {
  font-size: 4em;
}

.modal-v2 .modal-content-right {
  font-size: 18px;
  padding: 20px;
  font-weight: 100;
}

.modal-v2 .modal-content-right .btn {
  width: 50%;
  height: 40px;
  margin-top: 5px;
}

.modal-v2 .modal-content-right .modal-content-right-text-mail {
  width: 100%;
  height: 40px;
  border: none;
  background-color: #EBE8E8;
  padding: 10px;
}

/*mobile================================*/

#chaayos-mobile {
  position: fixed;
  bottom: 0px;
  right: 0px;
  z-index: -1;
  width: 100%;
  height: 100%;
  display: none
}

#chaayos-mobile-menu-opener{
  right: 20px;
  position: fixed;
  bottom: 20px;
  outline: none !important;
  z-index: 9999999;
  display: none
}

#chaayos-mobile a{
  color: #fff;
  padding-right: 0px;
}
#chaayos-mobile a:hover{
  background:none !important;
}
#chaayos-mobile .tree{
  padding-left: 10px;
  background: #ef5949;
}
.sub-chaayos-mobile-menu-list {
  display: none;
  padding: 0;
  height: 300px;
  font-weight: 100;
  overflow: auto;
  font-size: 2em;
}

.sub-chaayos-mobile-menu-list .fa{margin-right: 10px;}

#chaayos-mobile.animate {
  -webkit-animation: mobile-menubg 0.75s linear;
  -moz-animation: mobile-menubg 0.75s linear;
  -ms-animation: mobile-menubg 0.75s linear;
  -o-animation: mobile-menubg 0.75s linear;
  animation: mobile-menubg 0.75s linear;
}

#chaayos-mobile .ink {
  background: #FF6656;
}

#chaayos-mobile .ink.animate {
  -webkit-animation: ripple-mobile 0.75s linear;
  -moz-animation: ripple-mobile 0.75s linear;
  -ms-animation: ripple-mobile 0.75s linear;
  -o-animation: ripple-mobile 0.75s linear;
  animation: ripple-mobile 0.75s linear;
}

#chaayos-mobile.reverse.animate {
  -webkit-animation: mobile-menubg-reverse 0.75s linear !important;
  -moz-animation: mobile-menubg-reverse 0.75s linear !important;
  -ms-animation: mobile-menubg-reverse 0.75s linear !important;
  -o-animation: mobile-menubg-reverse 0.75s linear !important;
  animation: mobile-menubg-reverse 0.75s linear !important;
}

#chaayos-mobile.reverse .ink.animate {
  -webkit-animation: ripple-mobile-reverse 0.75s linear !important;
  -moz-animation: ripple-mobile-reverse 0.75s linear !important;
  -ms-animation: ripple-mobile-reverse 0.75s linear !important;
  -o-animation: ripple-mobile-reverse 0.75s linear !important;
  animation: ripple-mobile-reverse 0.75s linear !important;
}

@keyframes ripple-mobile-reverse {
  from {
   -webkit-transform: scale(2.5);
   -moz-transform: scale(2.5);
   -ms-transform: scale(2.5);
   -o-transform: scale(2.5);
   transform: scale(2.5);
   -webkit-transform: scale(2.5);
   -moz-transform: scale(2.5);
   -ms-transform: scale(2.5);
   -o-transform: scale(2.5);
   transform: scale(2.5);
   -webkit-opacity: 1;
   -moz-opacity: 1;
   -ms-opacity: 1;
   -o-opacity: 1;
   opacity: 1;
   filter: alpha(opacity=100);
 }
 to {      
   -webkit-opacity: 1;
   -moz-opacity: 1;
   -ms-opacity: 1;
   -o-opacity: 1;
   opacity: 1;
   filter: alpha(opacity=100);
 }
}

@keyframes mobile-menubg-reverse {
  from {
   -webkit-opacity: 1;
   -moz-opacity: 1;
   -ms-opacity: 1;
   -o-opacity: 1;
   opacity: 1;
   filter: alpha(opacity=100);
   background: #FF6656;
 }
 to {
   -webkit-opacity: 0;
   -moz-opacity: 0;
   -ms-opacity: 0;
   -o-opacity: 0;
   opacity: 0;
   filter: alpha(opacity=0);
 }
}



@keyframes ripple-mobile {
  from {
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes mobile-menubg {
  from {
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);

  }
  to {
    -webkit-opacity: 1;
    -moz-opacity: 1;
    -ms-opacity: 1;
    -o-opacity: 1;
    opacity: 1;
    filter: alpha(opacity=100);
    background: #FF6656;
  }
}


/*=================Form===============*/

.form-element .form-element-padding .form-group,
.form-element .form-element-padding .input-group {
  padding: 15px;
}

.form-element .form-element-padding select,.form-element .form-element-padding .select2{
  margin-top: 30px;
}


.form-animate-radio .radio {
  display: inline-block;
  padding-right: 20px;
  font-size: 18px;
  font-weight: 100;
  padding-bottom: 10px;
  cursor: pointer;
  margin-top: 0px;
}

.form-animate-radio .radio:hover .inner {
  -webkit-transform: scale(0.5);
  -moz-transform: scale(0.5);
  -ms-transform: scale(0.5);
  -o-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform: scale(0.5);
  -moz-transform: scale(0.5);
  -ms-transform: scale(0.5);
  -o-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform: scale(0.5);
  -moz-transform: scale(0.5);
  -ms-transform: scale(0.5);
  -o-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-opacity: .5;
  -moz-opacity: .5;
  -ms-opacity: .5;
  -o-opacity: .5;
  opacity: .5;
}

.form-animate-radio .radio input {
  width: 1px;
  height: 1px;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -ms-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
}

.form-animate-radio .radio input:checked + .outer .inner {
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
  -webkit-animation: ripple 0.2s;
  -moz-animation: ripple 0.2s;
  -ms-animation: ripple 0.2s;
  -o-animation: ripple 0.2s;
  animation: ripple 0.2s;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
}

.form-animate-radio .radio input:checked + .outer {
  border: 3px solid #FF6656;
}

.form-animate-radio .radio input:focus + .outer .inner {
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
  -webkit-animation: ripple 0.2s;
  -moz-animation: ripple 0.2s;
  -ms-animation: ripple 0.2s;
  -o-animation: ripple 0.2s;
  animation: ripple 0.2s;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  -ms-opacity: 1;
  -o-opacity: 1;
  opacity: 1;
  background-color: #FF6656;
}

.form-animate-radio .radio .outer {
  width: 20px;
  height: 20px;
  display: block;
  float: left;
  border: 2px solid #ddd;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #fff;
}

.form-animate-radio .radio .inner {
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  -ms-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  -ms-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  width: 10px;
  height: 10px;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  display: block;
  margin: 2px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  background-color: #FF6656;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -ms-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
}

.form-animate-checkbox label {
  font-weight: 100;
  font-size: 18px;
}

.form-animate-checkbox .checkbox {
  position: relative;
  top: -0.375rem;
  float: left;
  margin: 0 1rem 0 0;
  margin-top: 5px;
  cursor: pointer;
}

.form-animate-checkbox .checkbox:before {
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  content: "";
  position: absolute;
  left: 0;
  z-index: 1;
  width: 2rem;
  height: 2rem;
  border: 2px solid #ddd;
}

.form-animate-checkbox .checkbox:checked:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  height: 1rem;
  border-color: #009688;
  border-top-style: none;
  border-right-style: none;
}

.form-animate-checkbox .checkbox:after {
  content: "";
  position: absolute;
  top: -0.125rem;
  left: 0;
  width: 2rem;
  height: 2rem;
  background: #fff;
  cursor: pointer;
}

.form-animate-text .form-text,.form-animate-text .form-text:focus {
  width: 100%;
  border: none !important;
  border-bottom: 1px solid #ddd !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  -o-box-shadow: none !important;
  box-shadow: none !important;
  -webkit-border-radius: 0px !important;
  -moz-border-radius: 0px !important;
  -ms-border-radius: 0px !important;
  -o-border-radius: 0px !important;
  border-radius: 0px !important;
  padding: 10px;
}

.ripple-form {
  position: absolute;
  width: 50px;
  height: 50px;
  left: 0px;
  z-index: 99;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  -webkit-animation: rippleForm 0.5s linear;
  -moz-animation: rippleForm 0.5s linear;
  -ms-animation: rippleForm 0.5s linear;
  -o-animation: rippleForm 0.5s linear;
  animation: rippleForm 0.5s linear;
}

.form-group.form-animate-text {
  position: relative;
  margin-bottom: 45px;
}

.form-group.form-animate-text .form-text {
  font-size: 18px;
  padding: 10px 10px 10px 5px;
  display: block;
  width: 100%;
  border: none;
  background:none;
  border-bottom: 1px solid #757575;
}

.form-group.form-animate-text .form-text:focus {
  outline: none;
}

.form-group.form-animate-text label {
  color: #999;
  font-size: 18px;
  font-weight: normal;
  position: absolute;
  pointer-events: none;
  left: 5px;
  top: 10px;
  -webkit-transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -ms-transition: 0.2s ease all;
  -o-transition: 0.2s ease all;
  transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -ms-transition: 0.2s ease all;
  -o-transition: 0.2s ease all;
  transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -ms-transition: 0.2s ease all;
  -o-transition: 0.2s ease all;
  transition: 0.2s ease all;
}

.form-group.form-animate-text.form-animate-error label{
  color:#FF968B !important;
}


.form-group.form-animate-text.form-animate-error .bar:before,.form-group.form-animate-text.form-animate-error .bar:after{
  background-color: #FF968B !important;
  width: 50%;
}

.form-group.form-animate-text.form-animate-error .form-animate-error-msg{
  color:#FF968B !important;
  font-size: 16px;
  display: none;
}

.form-group.form-animate-text .form-text:focus ~ label,
.form-text:valid ~ label {
  top: -10px;
  font-size: 14px;
  color: #999C9E;
}

.form-group.form-animate-text .bar {
  position: relative;
  display: block;
  width: 100%;
}

.form-group.form-animate-text .bar:before,
.bar:after {
  content: '';
  height: 2px;
  width: 0;
  bottom: 1px;
  position: absolute;
  background: #5264AE;
  -webkit-transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -ms-transition: 0.2s ease all;
  -o-transition: 0.2s ease all;
  transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -ms-transition: 0.2s ease all;
  -o-transition: 0.2s ease all;
  transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -ms-transition: 0.2s ease all;
  -o-transition: 0.2s ease all;
  transition: 0.2s ease all;
}

.form-group.form-animate-text .bar:before {
  left: 50%;
}

.form-group.form-animate-text .bar:after {
  right: 50%;
}

.form-group.form-animate-text .form-text:focus ~ .bar:before,
.form-text:focus ~ .bar:after {
  width: 50%;
}

.form-group.form-animate-text .form-text:focus ~ .highlight {
  -webkit-animation: inputHighlighter 0.3s ease;
  -moz-animation: inputHighlighter 0.3s ease;
  -ms-animation: inputHighlighter 0.3s ease;
  -o-animation: inputHighlighter 0.3s ease;
  animation: inputHighlighter 0.3s ease;
  -webkit-animation: inputHighlighter 0.3s ease;
  -moz-animation: inputHighlighter 0.3s ease;
  -ms-animation: inputHighlighter 0.3s ease;
  -o-animation: inputHighlighter 0.3s ease;
  animation: inputHighlighter 0.3s ease;
  -webkit-animation: inputHighlighter 0.3s ease;
  -moz-animation: inputHighlighter 0.3s ease;
  -ms-animation: inputHighlighter 0.3s ease;
  -o-animation: inputHighlighter 0.3s ease;
  animation: inputHighlighter 0.3s ease;
}

@keyframes rippleForm {
  from {
    -webkit-opacity: 0.8;
    -moz-opacity: 0.8;
    -ms-opacity: 0.8;
    -o-opacity: 0.8;
    opacity: 0.8;
    filter: alpha(opacity=80);
    background-color: #ddd;

  }
  to {
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
    transform: scale(2.5);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    -ms-opacity: 0;
    -o-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-webkit-keyframes inputHighlighter {
  from {
    background: #5264AE;
  }
  to {
    width: 0;
    background: transparent;
  }
}
@-moz-keyframes inputHighlighter {
  from {
    background: #5264AE;
  }
  to {
    width: 0;
    background: transparent;
  }
}
@keyframes inputHighlighter {
  from {
    background: #5264AE;
  }
  to {
    width: 0;
    background: transparent;
  }
}


/*========Responsive========*/
@media (max-width: 1200px) {
  #content{
    padding-left: 230px;
    padding-right: 0px
  }
  .profile-v1 .profile-v1-cover{margin-bottom: 0px !important;min-height: inherit !important;max-height: inherit !important;}
  .profile-v1 .profile-v1-cover-wrap{padding: 0px !important;}
  .profile-v1 .profile-v1-right-wrap,.profile-v1 .profile-v1-body{padding-left:inherit !important;padding: 0px !important;}
  .sub-left-menu{
    padding-top: 0px !important;
    top: 50px;
  }

  /*profile-v1*/
  .profile-v1 .profile-v1-right  .sub-profile-v1-right3:after{display: none !important;}

  .menu-large .dropdown-menu{
    margin-top: 52px !important;
  }
  .megamenu{
    margin-left: 0 ;
    margin-right: 0 ;
  }
  .megamenu> li {
    margin-bottom: 30px;
  }
  .megamenu> li:last-child {
    margin-bottom: 0;
  }
  .megamenu.dropdown-header {
    padding: 3px 15px !important;

  }
  .navbar-nav .open .dropdown-menu .dropdown-header{
  color:#fff;
  }
}



@media (max-width: 991px) {
  .iconic-timeline .iconic-timeline-detail {
    padding-left: 50px !important;
  }
  .article-v1-body{
    padding: 20px !important;
  }
  .article-v1-footer{
    padding: 0px !important;
  }
}


@media (min-width: 768px) {
  #quote-carousel {
    margin-bottom: 0;
    padding: 0 40px 30px 40px;
  }
}



@media (min-width : 992px) {

}



@media (min-width : 768px) and (max-width: 991px) {


}

@media only screen and (max-width: 800px) {
  .profile-v1 .profile-v1-wrapper{padding: 0px !important;}
  .responsive-table table, 
  .responsive-table thead, 
  .responsive-table tbody, 
  .responsive-table th, 
  .responsive-table td, 
  .responsive-table tr { 
    display: block !important; 
  }

  .responsive-table thead tr { 
    position: absolute !important;
    top: -9999px !important;
    left: -9999px !important;
  }

  .responsive-table tr { border: 1px solid #ccc !important; }

  .responsive-table td { 
    border: none !important;
    border-bottom: 1px solid #eee !important; 
    position: relative !important;
    padding-left: 10% !important; 
    white-space: normal !important;
    text-align:left !important;
  }

  .responsive-table td:before { 
    position: absolute !important;
    top: 6px !important;
    left: 6px !important;
    width: 45% !important; 
    padding-right: 10px; 
    white-space: nowrap;
    text-align:left;
    font-weight: bold;
  }

  .responsive-table td:before { content: attr(data-title); }

  .search-nav{
    display: none !Im;
  }

  #quote-carousel .carousel-indicators {
    bottom: -20px !important;
  }


  #quote-carousel .carousel-indicators li {
    display: inline-block;
    margin: 0px 5px;
    width: 15px;
    height: 15px;
  }
  
  #quote-carousel .carousel-indicators li.active {
    margin: 0px 5px;
    width: 20px;
    height: 20px;
  }

  .topnav #chaayos-mobile,.topnav #chaayos-mobile-menu-opener{display: block !important;}

  .user-nav{float: right;}
  .user-nav li{
    float: left;
  }
  .user-nav .user-name{
    padding-top: 10px !important;
  }
  .user-nav .dropdown{
    margin-top: -4px !important; 
  }
  .user-nav .user-dropdown{
    position: absolute !important;
    left: -120px !important;
  }
}



@media( max-width: 585px) {
  .nav-tabs.nav-tabs-v5 span.round-tabs {
    font-size: 16px;
    width: 50px;
    height: 50px;
    line-height: 50px;
  }

  .tab-content .head {
    font-size: 20px;
  }

  .nav-tabs.nav-tabs-v5 > li a {
    width: 50px;
    height: 50px;
    line-height: 50px;
  }

  .nav-tabs.nav-tabs-v5 li.active:after {
    content: " ";
    position: absolute;
    left: 35%;
  }
}


@media only screen and (max-width: 479px) { 
  .mail-right-tool li{padding: 5px !important;}
  .fc-right{display: none;}
  .fc-left{margin-bottom: 20px;float: right !important;}
  .search-v1 #tabs-demo6 li.active:after{
    display: none !important;
  }
  .search-v1 .search-v1-result-wrapper{padding: 0px !important;}
  #content{
    padding-left: 0px;
    padding-right: 0px;
  }
  .user-nav{margin-right: 10px;}
  .user-dropdown{background-color: #fff !important;}
  .nav-wrapper .navbar-header,.nav-wrapper,.navbar-brand{
    padding-left: 0px !important;
  }
  .user-nav .user-name,
  .user-nav .opener-right-menu,
  #left-menu,
  .opener-left-menu,
  #right-menu{
  }

  #chaayos-mobile,#chaayos-mobile-menu-opener{display: block !important;}

  .search-nav{
    display: none;
  }

}

.fullScreenLoader{
    position: fixed;
    top:0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background: rgba(255,255,255,.8);
    z-index: 99999999999999;
}

.fullScreenLoader img{
    margin-top: 10%;
}