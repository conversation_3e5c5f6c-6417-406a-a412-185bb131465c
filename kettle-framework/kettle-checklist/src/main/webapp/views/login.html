<div class="container" data-ng-init="init()">
	<form class="form-signin">
		<div class="panel periodic-login">
			<div class="panel-body text-center">
				<img src="img/chaayos.png">
				<div class="alert"
					data-ng-class="{'alert-warning':!authSuccess, 'alert-success':authSuccess}"
					data-ng-show="showMessage">
					<button type="button" class="close">
						<span aria-hidden="true" data-ng-click="removeAlert()">&times;</span>
					</button>
					{{authMessage}}
				</div>
				<div class="form-group form-animate-text">
					<input type="number" class="form-text" data-ng-model="userId"
						required> <span class="bar"></span> <label>User name</label>
				</div>
				<div class="form-group form-animate-text">
					<input type="password" class="form-text" data-ng-model="passcode"
						required> <span class="bar"></span> <label>Password</label>
				</div>
				<div class="form-group ">
					<select class="form-control"
						data-ng-init="unitCategory = unitCategories[0]"
						data-ng-options="category as category.name for category in unitCategories track by category.name"
						data-ng-model="unitCategory"
						data-ng-change="updateUnitBasicDetail(unitCategory)">
					</select>
				</div>
				<div class="form-group">
					<select class="form-control"
						data-ng-init="unitDetail = unitBasicDetails[0]"
						data-ng-options="unit as unit.name for unit in unitBasicDetails track by unit.id "
						data-ng-model="unitDetail">
					</select>
				</div>
				<input type="button" data-ng-click="login()" class="btn col-md-12 btn col-xs-12"
					style="background: #0f4e11; color: #FFF; font-weight: bold"
					value="Login" />
			</div>
		</div>
	</form>
</div>