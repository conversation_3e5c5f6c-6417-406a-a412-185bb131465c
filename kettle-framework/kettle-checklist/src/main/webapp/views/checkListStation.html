<!-- start: Content -->
<div id="content" data-ng-init="init()">
	<div class="col-xs-12 col-sm-12 col-md-12">
			<h5 align="center"><b>{{currentStation}} Station</b></h5>
	</div>
					<loading style align="center"></loading>
	</div>
	<!-- <div class="col-md-12 mail-right-content"> -->
	<div class="col-xs-12 col-sm-12 col-md-12">
		<div class="col-xs-12" data-ng-if="allCheckList.length > 0">
		<table class="table table-hover table-striped table-condensed table-bordered">
			<tr style="background-color:#50773e; color:#ffffff">
				<th class="col-xs-1">Action</th>
				<th class="col-xs-1">Name</th>
				<th class="col-xs-2">Description</th>
				<th class="col-xs-2">Start Time</th>
				<th class="col-xs-2">End Time</th>
				<th class="col-xs-2">Submission Time</th>
				<th class="col-xs-2">Last update Time</th>
				
				
			</tr>
			<tr data-ng-repeat="instance in allCheckList | filter:currentStation:true">
				<td class="col-xs-1" ng-show="instance.submissionTime==null">
				<input class="submit btn btn-success"  type="button" value="Fill"  data-ng-click="showCheckList(instance)" />
				</td>
				<td class="col-xs-1">{{instance.checkList.name}}</td>
				<td class="col-xs-2">{{instance.checkList.description}}</td>
				<td class="col-xs-2">{{instance.startTime | date:'medium' }}</td>
				<td class="col-xs-2">{{instance.endTime | date:'medium' }}</td>
				<td class="col-xs-2">{{instance.submissionTime | date:'medium' }}</td>
				<td class="col-xs-2" >{{instance.lastUpdateTime | date:'medium' }}</td>
				<td class="col-xs-2" ng-show="instance.submissionTime!=null" style="background-color: #339966"><input type="button" value="Re-Submit"
					data-ng-click="showCheckList(instance)" /></td>
				
			</tr>
		</table>
	</div>
	</div>
	
</div>