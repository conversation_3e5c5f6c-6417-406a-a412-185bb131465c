<!-- start: Content -->
<div id="content" data-ng-init="init()">
	<div class="col-md-12">
		<div class="row">
			<div class="col-xs-12 col-sm-12 col-md-12">
				
					<div  align="center">
						<h5><b>{{currentInstance.checkList.name}}</b></h5>
					</div>
					 <div class="col-sm-12 col-md-12" style="margin-top:5px;margin-bottom:5px">
                                   <button class="btn ripple btn-raised btn-success">
                                    <div data-ng-click="showStationList(currentInstance.checkList.station)">Back  </div>
                                  </button>
                     </div>
                              
					
				
			</div>
		</div>
	</div>



<div class="col-md-12 padding-0">
<table class="table table-hover" border="2">
	<tr data-ng-repeat="item in currentInstance.checkList.items">
	<td width="20%">{{item.action}}</td>
	 <td width="20%">{{item.description}}</td>
		<td width="40%" data-ng-show="item.responseType!='text'"><input type="text" class="form-control" placeholder="text" data-ng-model="item.comment"></td>
		  <td width="20%">
			<span data-ng-show="item.responseType!=='text'">
			  <div class="form-group form-animate-checkbox">
				<input type="checkbox" class="checkbox"	data-ng-model="item.response" data-ng-value="item.response" data-ng-true-value="'true'" data-ng-false-value="'false'"
					data-ng-checked="item.response !== undefined && item.response !== null && item.response !== 'false' " data-ng-click="checkStatus()">					</div>
			 </span>
			<span data-ng-show="item.responseType=='text'">	<input type="text" class="form-control"  placeholder="input" data-ng-model="item.response">	</span>
		  </td>
	</tr>
</table>
</div>
<div align="center" id="btnSubmit" class="col-md-12" style="margin-bottom:40px">
	<input type="button" data-ng-click="submitChecklist()" class="btn ripple btn-raised btn-success col-xs-12 col-sm-12 col-md-12" value="Submit" />
</div>
</div>