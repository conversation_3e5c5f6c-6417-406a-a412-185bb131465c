angular.module('listapp').controller("hotListOneDataController", function($scope,$location,$http,AppUtil){
	$scope.stationApps = 
	[{
	    _id: null,
	    detachAll: null,
	    event: null,
	    checkList: {
	        _id: null,
	        detachAll: null,
	        name: "Dummy Checklist One",
	        description: "Dummy Checklist description",
	        type: null,
	        category: null,
	        schedule: null,
	        items: [{
	            _id: null,
	            detachAll: null,
	            ordering: 1,
	            step: "Item Step One",
	            action: "Some Item Action to do something",
	            description: "Item Description",
	            response: null,
	            mandatory: false,
	            multiValued: false,
	            responseType: null,
	            probableValues: [],
	            status: null
	        }],
	        template: false,
	        status: null
	    },
	    userName: "DummyUser",
	    userRole: null,
	    userId: 123456,
	    businessDate: 1463596200000,
	    startTime: null,
	    endTime: null,
	    unitName: "DummyUnit",
	    unitId: 10000,
	    submissionTime: null,
	    lastUpdateTime: null,
	    status: null
	},
	
	{
	    _id: null,
	    detachAll: null,
	    event: null,
	    checkList: {
	        _id: null,
	        detachAll: null,
	        name: "Dummy Checklist Two",
	        description: "Dummy Checklist description",
	        type: null,
	        category: null,
	        schedule: null,
	        items: [{
	            _id: null,
	            detachAll: null,
	            ordering: 1,
	            step: "Item Step",
	            action: "Some Item Action to do something",
	            description: "Item Description",
	            response: null,
	            mandatory: false,
	            multiValued: false,
	            responseType: null,
	            probableValues: [],
	            status: null
	        }],
	        template: false,
	        status: null
	    },
	    userName: "DummyUser",
	    userRole: null,
	    userId: 123456,
	    businessDate: 1463596200000,
	    startTime: null,
	    endTime: null,
	    unitName: "DummyUnit",
	    unitId: 10000,
	    submissionTime: null,
	    lastUpdateTime: null,
	    status: null
	},
	
	{
	    _id: null,
	    detachAll: null,
	    event: null,
	    checkList: {
	        _id: null,
	        detachAll: null,
	        name: "Dummy Checklist Three",
	        description: "Dummy Checklist description",
	        type: null,
	        category: null,
	        schedule: null,
	        items: [{
	            _id: null,
	            detachAll: null,
	            ordering: 1,
	            step: "Item Step",
	            action: "Some Item Action to do something",
	            description: "Item Description",
	            response: null,
	            mandatory: false,
	            multiValued: false,
	            responseType: null,
	            probableValues: [],
	            status: null
	        }],
	        template: false,
	        status: null
	    },
	    userName: "DummyUser",
	    userRole: null,
	    userId: 123456,
	    businessDate: 1463596200000,
	    startTime: null,
	    endTime: null,
	    unitName: "DummyUnit",
	    unitId: 10000,
	    submissionTime: null,
	    lastUpdateTime: null,
	    status: null
	}]
	
});