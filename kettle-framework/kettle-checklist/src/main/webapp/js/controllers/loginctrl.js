/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('listapp')
    .controller(
        "LoginController",
        function ($scope, $location, $http, AuthService, $cookieStore,
                  AppUtil) {

            $scope.allUnits = [];
            $scope.unitBasicDetails = [];
            $scope.unitCategories = [{
                "name": "Cafe",
                "value": "CAFE"
            }, {
                "name": "COD",
                "value": "DELIVERY"
            }];

            $scope.init = function () {
                $scope.userId = null;
                $scope.passcode = null;
                $scope.showMessage = false;
                $scope.loginText = "Login";
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.allUnits,
                    params: {
                        "category": "ALL"
                    }
                })
                    .then(
                        function success(response) {
                            $scope.allUnits = response.data;
                            $scope.allUnits
                                .sort(function (a, b) {
                                    var nameA = a.name
                                        .toLowerCase(), nameB = b.name
                                        .toLowerCase()
                                    if (nameA < nameB)
                                        return -1
                                    if (nameA > nameB)
                                        return 1
                                    return 0
                                });
                            $scope.updateUnitBasicDetail($scope.unitCategories[0]);
                        }, function error(response) {
                            console.log("error:" + response);
                        });
            }

            $scope.login = function () {
                var userObj = {
                    unitId: $scope.unitDetail.id,
                    userId: $scope.userId,
                    password: $scope.passcode,
                    terminalId: 10,
                    screenType: "CHECKLIST",
                    macAddress: $location.search().mac,
                    application: "KETTLE_CHECKLIST"
                };

                if (userObj.userId != null && userObj.password != null) {
                    $scope.loginText = "Logging in...";
                    $scope.loginDisabled = true;
                } else {
                    alert("Please fill user name and password");
                    return;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.users.login,
                    data: userObj
                })
                    .then(
                        function success(response) {
                            if (response.data.sessionKeyId == null) {
                                $scope.authSuccess = false;
                                $scope.authMessage = 'Credentials are not correct!';
                                $scope.showMessage = true;
                                $scope.loginText = "Login";
                                $scope.loginDisabled = false;
                            } else {
                                $scope.authSuccess = true;
                                $scope.authMessage = 'Authentication successful. Redirecting to dashboard!';
                                $scope.showMessage = true;
                                response.data.userType = "checklist-user";
                                response.data.user.currentAddress = null;
                                response.data.user.department = null;
                                response.data.user.permanentAddress = null;
                                response.data.unitDetail = $scope.unitDetail;
                                $cookieStore.put('checklistglobals',response.data);
                                AuthService
                                    .setAuthorization(response.data.jwtToken);
                                $location.path("/dashboard");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
            }
            $scope.updateUnitBasicDetail = function (unitCategory) {
                $scope.unitBasicDetails = [];
                angular.forEach($scope.allUnits, function (unit) {
                    if (unit.category === unitCategory.value) {
                        $scope.unitBasicDetails.push(unit);
                    }
                });
                $scope.unitDetail = $scope.unitBasicDetails[0];
            };
            $scope.removeAlert = function () {
                $scope.showMessage = false;
            };
        });