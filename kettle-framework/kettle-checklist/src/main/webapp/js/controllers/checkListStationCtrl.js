angular.module('listapp').controller("checkListStationCtrl",
function($rootScope,$scope, $location, $http, AppUtil) {
	$scope.init = function() {
			if($scope.currentStation == null){
				$location.path("dashboard")
			}
		}
	$scope.showCheckList = function(instance) {
			//console.log(instance);
			for(var index in instance.checkList.items)
			{
				instance.checkList.items[index].response=null;
				instance.checkList.items[index].comment=null;
			}
			$rootScope.currentInstance = instance;
			$location.path("dashboard/checkListView");
		}
});