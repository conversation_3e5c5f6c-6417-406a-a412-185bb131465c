/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('listapp').controller("MainController",
    function ($rootScope, $scope, $interval, $location, $http, $cookieStore, AuthService, AppUtil) {


        	$scope.init = function () {
        	//document.body.style.zoom = screen.logicalXDPI / screen.deviceXDPI;

        	$rootScope.showFullScreenLoader = true;
            $scope.user = $cookieStore.get('checklistglobals').user;
            $scope.unitDetail = $cookieStore.get('checklistglobals').unitDetail;
            console.log($cookieStore.get('checklistglobals'));
            $http({
                method: 'GET',
                url: AppUtil.restUrls.checkList.checklists,
                params: {
                    "unitId": $cookieStore.get('checklistglobals').unitId
                }
            })
                .then(
                    function success(response) {
                    	$rootScope.showFullScreenLoader = false;
                        $scope.allCheckList = response.data;
                        console.log($scope.allCheckList);
//                        leftMenu();
//                        rightMenu();
//                        treeMenu();
//                        hide();
                        $scope.loading = false;
                    }, function error(response) {
                    	$rootScope.showFullScreenLoader = false;
                        console.log("error:" + response);
                    });

            $http({
            	
                method: 'GET',
                url: AppUtil.restUrls.checkList.station,
            })
                .then(
                    function success(response) {
                        $scope.allStations = response.data;
                        console.log($scope.allStations);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
            
            //$scope.$watch('allCheckList',function(newVal,oldVal){
        		//$scope.allCheckList = newVal;
            //});
        }

        if ($location.path() == "/dashboard") {
            $location.path("dashboard");
        }

        $scope.showCheckList = function (instance) {
            $rootScope.currentInstance = instance;
            $location.path("dashboard/checkListView");
        }

        $scope.$on("showCheckList", function (event, args) {
        	//console.log("called for update!!!",JSON.stringify(args));
        	$scope.allCheckList = args.checkListView;     
        	console.log($scope.allCheckList);
        });
        
        $scope.showStationList = function (station) {
        	//console.log("Station here=",station);
            $scope.currentStation = station;
            $location.path("dashboard/checkListStation");
            //toggleClass("opener-left-menu is-closed");
            $(".opener-left-menu is-closed")
        }

        $scope.logout = function () {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.users.logout,
                data: $cookieStore.get('checklistglobals')
            }).then(function success(response) {
                $cookieStore.remove('checklistglobals');
                AuthService.setAuthorization(null);
                $location.path("/login");
            });
        }
        var tick = function () {
            $scope.clock = Date.now();
            $scope.dateTimeValue = Date.now();
        }
        tick();
        $interval(tick, 1000);
    });