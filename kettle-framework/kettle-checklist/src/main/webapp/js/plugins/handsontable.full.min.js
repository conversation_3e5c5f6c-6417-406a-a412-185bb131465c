/*!
(The MIT License)

Copyright (c) 2012-2014 <PERSON><PERSON>
Copyright (c) 2015 Handsoncode sp. z o.o. <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
'Software'), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

*/
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.Handsontable=e()}}(function(){var e;return function(e,t,n){return function o(e,t,n){function r(a,l){if(!t[a]){if(!e[a]){var u="function"==typeof require&&require;if(!l&&u)return u(a,!0);if(i)return i(a,!0);if(s[a]&&"undefined"!=typeof window[s[a]])return window[s[a]];var c=new Error("Cannot find module '"+a+"'");throw c.code="MODULE_NOT_FOUND",c}var d=t[a]={exports:{}};e[a][0].call(d.exports,function(t){var n=e[a][1][t];return r(n?n:t)},d,d.exports,o,e,t,n)}return t[a].exports}for(var i="function"==typeof require&&require,s=JSON.parse('{"zeroclipboard":"ZeroClipboard","moment":"moment","pikaday":"Pikaday"}')||{},a=0;a<n.length;a++)r(n[a]);return r}(e,t,n),function(){return Handsontable}}({1:[function(e,t,n){"use strict";window.jQuery&&!function(e,t,n){t.fn.handsontable=function(e){var t,o,r,i,s,a=this.first(),l=a.data("handsontable");if("string"!=typeof e)return s=e||{},l?l.updateSettings(s):(l=new n.Core(a[0],s),a.data("handsontable",l),l.init()),a;if(r=[],arguments.length>1)for(t=1,o=arguments.length;o>t;t++)r.push(arguments[t]);if(l){if("undefined"==typeof l[e])throw new Error("Handsontable do not provide action: "+e);i=l[e].apply(l,r),"destroy"===e&&a.removeData()}return i}}(window,jQuery,Handsontable)},{}],2:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableBorder:{get:function(){return b}},__esModule:{value:!0}});var o,r,i,s,a,l=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),u=l.getComputedStyle,c=l.getTrimmingContainer,d=l.innerWidth,h=l.innerHeight,f=l.offset,p=l.outerHeight,g=l.outerWidth,m=(r=e("helpers/dom/event"),r&&r.__esModule&&r||{"default":r}).stopImmediatePropagation,v=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).EventManager,w=(s=e("cell/coords"),s&&s.__esModule&&s||{"default":s}).WalkontableCellCoords,y=(a=e("overlay/_base.js"),a&&a.__esModule&&a||{"default":a}).WalkontableOverlay,b=function(e,t){t&&(this.eventManager=new v(e),this.instance=e,this.wot=e,this.settings=t,this.mouseDown=!1,this.main=null,this.top=null,this.left=null,this.bottom=null,this.right=null,this.topStyle=null,this.leftStyle=null,this.bottomStyle=null,this.rightStyle=null,this.cornerDefaultStyle={width:"5px",height:"5px",borderWidth:"2px",borderStyle:"solid",borderColor:"#FFF"},this.corner=null,this.cornerStyle=null,this.createBorders(t),this.registerListeners())};$traceurRuntime.createClass(b,{registerListeners:function(){var e=this;this.eventManager.addEventListener(document.body,"mousedown",function(){return e.onMouseDown()}),this.eventManager.addEventListener(document.body,"mouseup",function(){return e.onMouseUp()});for(var t=this,n=function(n,o){t.eventManager.addEventListener(t.main.childNodes[n],"mouseenter",function(t){return e.onMouseEnter(t,e.main.childNodes[n])})},o=0,r=this.main.childNodes.length;r>o;o++)n(o,r)},onMouseDown:function(){this.mouseDown=!0},onMouseUp:function(){this.mouseDown=!1},onMouseEnter:function(e,t){function n(e){return e.clientY<Math.floor(i.top)?!0:e.clientY>Math.ceil(i.top+i.height)?!0:e.clientX<Math.floor(i.left)?!0:e.clientX>Math.ceil(i.left+i.width)?!0:void 0}function o(e){n(e)&&(r.eventManager.removeEventListener(document.body,"mousemove",o),t.style.display="block")}if(this.mouseDown&&this.wot.getSetting("hideBorderOnMouseDownOver")){e.preventDefault(),m(e);var r=this,i=t.getBoundingClientRect();t.style.display="none",this.eventManager.addEventListener(document.body,"mousemove",o)}},createBorders:function(e){this.main=document.createElement("div");var t=["top","left","bottom","right","corner"],n=this.main.style;n.position="absolute",n.top=0,n.left=0;for(var o=0;5>o;o++){var r=t[o],i=document.createElement("div");i.className="wtBorder "+(this.settings.className||""),this.settings[r]&&this.settings[r].hide&&(i.className+=" hidden"),n=i.style,n.backgroundColor=this.settings[r]&&this.settings[r].color?this.settings[r].color:e.border.color,n.height=this.settings[r]&&this.settings[r].width?this.settings[r].width+"px":e.border.width+"px",n.width=this.settings[r]&&this.settings[r].width?this.settings[r].width+"px":e.border.width+"px",this.main.appendChild(i)}this.top=this.main.childNodes[0],this.left=this.main.childNodes[1],this.bottom=this.main.childNodes[2],this.right=this.main.childNodes[3],this.topStyle=this.top.style,this.leftStyle=this.left.style,this.bottomStyle=this.bottom.style,this.rightStyle=this.right.style,this.corner=this.main.childNodes[4],this.corner.className+=" corner",this.cornerStyle=this.corner.style,this.cornerStyle.width=this.cornerDefaultStyle.width,this.cornerStyle.height=this.cornerDefaultStyle.height,this.cornerStyle.border=[this.cornerDefaultStyle.borderWidth,this.cornerDefaultStyle.borderStyle,this.cornerDefaultStyle.borderColor].join(" "),Handsontable.mobileBrowser&&this.createMultipleSelectorHandles(),this.disappear(),this.wot.wtTable.bordersHolder||(this.wot.wtTable.bordersHolder=document.createElement("div"),this.wot.wtTable.bordersHolder.className="htBorders",this.wot.wtTable.spreader.appendChild(this.wot.wtTable.bordersHolder)),this.wot.wtTable.bordersHolder.insertBefore(this.main,this.wot.wtTable.bordersHolder.firstChild)},createMultipleSelectorHandles:function(){this.selectionHandles={topLeft:document.createElement("DIV"),topLeftHitArea:document.createElement("DIV"),bottomRight:document.createElement("DIV"),bottomRightHitArea:document.createElement("DIV")};var e=10,t=40;this.selectionHandles.topLeft.className="topLeftSelectionHandle",this.selectionHandles.topLeftHitArea.className="topLeftSelectionHandle-HitArea",this.selectionHandles.bottomRight.className="bottomRightSelectionHandle",this.selectionHandles.bottomRightHitArea.className="bottomRightSelectionHandle-HitArea",this.selectionHandles.styles={topLeft:this.selectionHandles.topLeft.style,topLeftHitArea:this.selectionHandles.topLeftHitArea.style,bottomRight:this.selectionHandles.bottomRight.style,bottomRightHitArea:this.selectionHandles.bottomRightHitArea.style};var n={position:"absolute",height:t+"px",width:t+"px","border-radius":parseInt(t/1.5,10)+"px"};for(var o in n)n.hasOwnProperty(o)&&(this.selectionHandles.styles.bottomRightHitArea[o]=n[o],this.selectionHandles.styles.topLeftHitArea[o]=n[o]);var r={position:"absolute",height:e+"px",width:e+"px","border-radius":parseInt(e/1.5,10)+"px",background:"#F5F5FF",border:"1px solid #4285c8"};for(var i in r)r.hasOwnProperty(i)&&(this.selectionHandles.styles.bottomRight[i]=r[i],this.selectionHandles.styles.topLeft[i]=r[i]);this.main.appendChild(this.selectionHandles.topLeft),this.main.appendChild(this.selectionHandles.bottomRight),this.main.appendChild(this.selectionHandles.topLeftHitArea),this.main.appendChild(this.selectionHandles.bottomRightHitArea)},isPartRange:function(e,t){return!this.wot.selections.area.cellRange||e==this.wot.selections.area.cellRange.to.row&&t==this.wot.selections.area.cellRange.to.col?!1:!0},updateMultipleSelectionHandlesPosition:function(e,t,n,o,r,i){var s=parseInt(this.selectionHandles.styles.topLeft.width,10),a=parseInt(this.selectionHandles.styles.topLeftHitArea.width,10);this.selectionHandles.styles.topLeft.top=parseInt(n-s,10)+"px",this.selectionHandles.styles.topLeft.left=parseInt(o-s,10)+"px",this.selectionHandles.styles.topLeftHitArea.top=parseInt(n-a/4*3,10)+"px",this.selectionHandles.styles.topLeftHitArea.left=parseInt(o-a/4*3,10)+"px",this.selectionHandles.styles.bottomRight.top=parseInt(n+i,10)+"px",this.selectionHandles.styles.bottomRight.left=parseInt(o+r,10)+"px",this.selectionHandles.styles.bottomRightHitArea.top=parseInt(n+i-a/4,10)+"px",this.selectionHandles.styles.bottomRightHitArea.left=parseInt(o+r-a/4,10)+"px",this.settings.border.multipleSelectionHandlesVisible&&this.settings.border.multipleSelectionHandlesVisible()?(this.selectionHandles.styles.topLeft.display="block",this.selectionHandles.styles.topLeftHitArea.display="block",this.isPartRange(e,t)?(this.selectionHandles.styles.bottomRight.display="none",this.selectionHandles.styles.bottomRightHitArea.display="none"):(this.selectionHandles.styles.bottomRight.display="block",this.selectionHandles.styles.bottomRightHitArea.display="block")):(this.selectionHandles.styles.topLeft.display="none",this.selectionHandles.styles.bottomRight.display="none",this.selectionHandles.styles.topLeftHitArea.display="none",this.selectionHandles.styles.bottomRightHitArea.display="none"),e==this.wot.wtSettings.getSetting("fixedRowsTop")||t==this.wot.wtSettings.getSetting("fixedColumnsLeft")?(this.selectionHandles.styles.topLeft.zIndex="9999",this.selectionHandles.styles.topLeftHitArea.zIndex="9999"):(this.selectionHandles.styles.topLeft.zIndex="",this.selectionHandles.styles.topLeftHitArea.zIndex="")},appear:function(e){if(!this.disabled){var t,n,o,r,i,s,a,l,m,v,b,C,R,_,S,E,T,O,M;M=y.isOverlayTypeOf(this.wot.cloneOverlay,y.CLONE_TOP)||y.isOverlayTypeOf(this.wot.cloneOverlay,y.CLONE_TOP_LEFT_CORNER)?this.wot.getSetting("fixedRowsTop"):y.isOverlayTypeOf(this.wot.cloneOverlay,y.CLONE_BOTTOM)||y.isOverlayTypeOf(this.wot.cloneOverlay,y.CLONE_BOTTOM_LEFT_CORNER)?this.wot.getSetting("fixedRowsBottom"):this.wot.wtTable.getRenderedRowsCount();for(var k=0;M>k;k++){var H=this.wot.wtTable.rowFilter.renderedToSource(k);if(H>=e[0]&&H<=e[2]){R=H;break}}for(var D=M-1;D>=0;D--){var x=this.wot.wtTable.rowFilter.renderedToSource(D);if(x>=e[0]&&x<=e[2]){S=x;break}}M=this.wot.wtTable.getRenderedColumnsCount();for(var A=0;M>A;A++){var P=this.wot.wtTable.columnFilter.renderedToSource(A);if(P>=e[1]&&P<=e[3]){_=P;break}}for(var N=M-1;N>=0;N--){var L=this.wot.wtTable.columnFilter.renderedToSource(N);if(L>=e[1]&&L<=e[3]){E=L;break}}if(void 0===R||void 0===_)return void this.disappear();t=R!==S||_!==E,n=this.wot.wtTable.getCell(new w(R,_)),o=t?this.wot.wtTable.getCell(new w(S,E)):n,r=f(n),i=t?f(o):r,s=f(this.wot.wtTable.TABLE),l=r.top,b=i.top+p(o)-l,v=r.left,C=i.left+g(o)-v,a=l-s.top-1,m=v-s.left-1;var W=u(n);parseInt(W.borderTopWidth,10)>0&&(a+=1,b=b>0?b-1:0),parseInt(W.borderLeftWidth,10)>0&&(m+=1,C=C>0?C-1:0),this.topStyle.top=a+"px",this.topStyle.left=m+"px",this.topStyle.width=C+"px",this.topStyle.display="block",this.leftStyle.top=a+"px",this.leftStyle.left=m+"px",this.leftStyle.height=b+"px",this.leftStyle.display="block";var I=Math.floor(this.settings.border.width/2);this.bottomStyle.top=a+b-I+"px",this.bottomStyle.left=m+"px",this.bottomStyle.width=C+"px",this.bottomStyle.display="block",this.rightStyle.top=a+"px",this.rightStyle.left=m+C-I+"px",this.rightStyle.height=b+1+"px",this.rightStyle.display="block",Handsontable.mobileBrowser||!this.hasSetting(this.settings.border.cornerVisible)||this.isPartRange(S,E)?this.cornerStyle.display="none":(this.cornerStyle.top=a+b-4+"px",this.cornerStyle.left=m+C-4+"px",this.cornerStyle.borderRightWidth=this.cornerDefaultStyle.borderWidth,this.cornerStyle.width=this.cornerDefaultStyle.width,this.cornerStyle.display="block",T=c(this.wot.wtTable.TABLE),E===this.wot.getSetting("totalColumns")-1&&(O=o.offsetLeft+g(o)>=d(T),O&&(this.cornerStyle.left=Math.floor(m+C-3-parseInt(this.cornerDefaultStyle.width)/2)+"px",this.cornerStyle.borderRightWidth=0)),S===this.wot.getSetting("totalRows")-1&&(O=o.offsetTop+p(o)>=h(T),O&&(this.cornerStyle.top=Math.floor(a+b-3-parseInt(this.cornerDefaultStyle.height)/2)+"px",this.cornerStyle.borderBottomWidth=0))),Handsontable.mobileBrowser&&this.updateMultipleSelectionHandlesPosition(R,_,a,m,C,b)}},disappear:function(){this.topStyle.display="none",this.leftStyle.display="none",this.bottomStyle.display="none",this.rightStyle.display="none",this.cornerStyle.display="none",Handsontable.mobileBrowser&&(this.selectionHandles.styles.topLeft.display="none",this.selectionHandles.styles.bottomRight.display="none")},hasSetting:function(e){return"function"==typeof e?e():!!e}},{}),window.WalkontableBorder=b},{"cell/coords":5,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,"overlay/_base.js":11}],3:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableViewportColumnsCalculator:{get:function(){return r}},__esModule:{value:!0}});var o=new WeakMap,r=function(e,t,n,r,i,s,a){o.set(this,{viewportWidth:e,scrollOffset:t,totalColumns:n,columnWidthFn:r,overrideFn:i,onlyFullyVisible:s}),this.count=0,this.startColumn=null,this.endColumn=null,this.startPosition=null,this.stretchAllRatio=0,this.stretchLastWidth=0,this.stretch=a,this.totalTargetWidth=0,this.needVerifyLastColumnWidth=!0,this.stretchAllColumnsWidth=[],this.calculate()},i=r;$traceurRuntime.createClass(r,{calculate:function(){for(var e,t=0,n=!0,r=[],i=o.get(this),s=i.onlyFullyVisible,a=i.overrideFn,l=i.scrollOffset,u=i.totalColumns,c=i.viewportWidth,d=0;u>d;d++)if(e=this._getColumnWidth(d),l>=t&&!s&&(this.startColumn=d),t>=l&&l+c>=t+e&&(null==this.startColumn&&(this.startColumn=d),this.endColumn=d),r.push(t),t+=e,s||(this.endColumn=d),t>=l+c){n=!1;break}if(this.endColumn===u-1&&n)for(this.startColumn=this.endColumn;this.startColumn>0;){var h=r[this.endColumn]+e-r[this.startColumn-1];if((c>=h||!s)&&this.startColumn--,h>c)break}null!==this.startColumn&&a&&a(this),this.startPosition=r[this.startColumn],void 0==this.startPosition&&(this.startPosition=null),null!==this.startColumn&&(this.count=this.endColumn-this.startColumn+1)},refreshStretching:function(e){if("none"!==this.stretch){for(var t,n,r=0,i=o.get(this),s=i.totalColumns,a=0;s>a;a++)t=this._getColumnWidth(a),r+=t;this.totalTargetWidth=e,n=r-e,"all"===this.stretch&&0>n?(this.stretchAllRatio=e/r,this.stretchAllColumnsWidth=[],this.needVerifyLastColumnWidth=!0):"last"===this.stretch&&e!==1/0&&(this.stretchLastWidth=-n+this._getColumnWidth(s-1))}},getStretchedColumnWidth:function(e,t){var n=null;return"all"===this.stretch&&0!==this.stretchAllRatio?n=this._getStretchedAllColumnWidth(e,t):"last"===this.stretch&&0!==this.stretchLastWidth&&(n=this._getStretchedLastColumnWidth(e)),n},_getStretchedAllColumnWidth:function(e,t){var n=0,r=o.get(this),i=r.totalColumns;if(this.stretchAllColumnsWidth[e]||(this.stretchAllColumnsWidth[e]=Math.round(t*this.stretchAllRatio)),this.stretchAllColumnsWidth.length===i&&this.needVerifyLastColumnWidth){this.needVerifyLastColumnWidth=!1;for(var s=0;s<this.stretchAllColumnsWidth.length;s++)n+=this.stretchAllColumnsWidth[s];n!==this.totalTargetWidth&&(this.stretchAllColumnsWidth[this.stretchAllColumnsWidth.length-1]+=this.totalTargetWidth-n)}return this.stretchAllColumnsWidth[e]},_getStretchedLastColumnWidth:function(e){var t=o.get(this),n=t.totalColumns;return e===n-1?this.stretchLastWidth:null},_getColumnWidth:function(e){var t=o.get(this).columnWidthFn(e);return void 0===t&&(t=i.DEFAULT_WIDTH),t}},{get DEFAULT_WIDTH(){return 50}}),window.WalkontableViewportColumnsCalculator=r},{}],4:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableViewportRowsCalculator:{get:function(){return r}},__esModule:{value:!0}});var o=new WeakMap,r=function(e,t,n,r,i,s,a){o.set(this,{viewportHeight:e,scrollOffset:t,totalRows:n,rowHeightFn:r,overrideFn:i,onlyFullyVisible:s,horizontalScrollbarHeight:a}),this.count=0,this.startRow=null,this.endRow=null,this.startPosition=null,this.calculate()},i=r;$traceurRuntime.createClass(r,{calculate:function(){for(var e=0,t=!0,n=[],r=o.get(this),s=r.onlyFullyVisible,a=r.overrideFn,l=r.rowHeightFn,u=r.scrollOffset,c=r.totalRows,d=r.viewportHeight,h=r.horizontalScrollbarHeight||0,f=0;c>f;f++){var p=l(f);if(void 0===p&&(p=i.DEFAULT_HEIGHT),u>=e&&!s&&(this.startRow=f),e>=u&&u+d-h>=e+p&&(null===this.startRow&&(this.startRow=f),this.endRow=f),n.push(e),e+=p,s||(this.endRow=f),e>=u+d-h){t=!1;break}}if(this.endRow===c-1&&t)for(this.startRow=this.endRow;this.startRow>0;){var g=n[this.endRow]+p-n[this.startRow-1];if((d-h>=g||!s)&&this.startRow--,g>=d-h)break}null!==this.startRow&&a&&a(this),this.startPosition=n[this.startRow],void 0==this.startPosition&&(this.startPosition=null),null!==this.startRow&&(this.count=this.endRow-this.startRow+1)}},{get DEFAULT_HEIGHT(){return 23}}),window.WalkontableViewportRowsCalculator=r},{}],5:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableCellCoords:{get:function(){return o}},__esModule:{value:!0}});var o=function(e,t){"undefined"!=typeof e&&"undefined"!=typeof t?(this.row=e,this.col=t):(this.row=null,this.col=null)};$traceurRuntime.createClass(o,{isValid:function(e){return this.row<0||this.col<0?!1:this.row>=e.getSetting("totalRows")||this.col>=e.getSetting("totalColumns")?!1:!0},isEqual:function(e){return e===this?!0:this.row===e.row&&this.col===e.col},isSouthEastOf:function(e){return this.row>=e.row&&this.col>=e.col},isNorthWestOf:function(e){return this.row<=e.row&&this.col<=e.col},isSouthWestOf:function(e){return this.row>=e.row&&this.col<=e.col},isNorthEastOf:function(e){return this.row<=e.row&&this.col>=e.col}},{}),window.WalkontableCellCoords=o},{}],6:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableCellRange:{get:function(){return i}},__esModule:{value:!0}});var o,r=(o=e("cell/coords"),o&&o.__esModule&&o||{"default":o}).WalkontableCellCoords,i=function(e,t,n){this.highlight=e,this.from=t,this.to=n},s=i;$traceurRuntime.createClass(i,{isValid:function(e){return this.from.isValid(e)&&this.to.isValid(e)},isSingle:function(){return this.from.row===this.to.row&&this.from.col===this.to.col},getHeight:function(){return Math.max(this.from.row,this.to.row)-Math.min(this.from.row,this.to.row)+1},getWidth:function(){return Math.max(this.from.col,this.to.col)-Math.min(this.from.col,this.to.col)+1},includes:function(e){var t=this.getTopLeftCorner(),n=this.getBottomRightCorner();return e.row<0&&(e.row=0),e.col<0&&(e.col=0),t.row<=e.row&&n.row>=e.row&&t.col<=e.col&&n.col>=e.col},includesRange:function(e){return this.includes(e.getTopLeftCorner())&&this.includes(e.getBottomRightCorner())},isEqual:function(e){return Math.min(this.from.row,this.to.row)==Math.min(e.from.row,e.to.row)&&Math.max(this.from.row,this.to.row)==Math.max(e.from.row,e.to.row)&&Math.min(this.from.col,this.to.col)==Math.min(e.from.col,e.to.col)&&Math.max(this.from.col,this.to.col)==Math.max(e.from.col,e.to.col)},overlaps:function(e){return e.isSouthEastOf(this.getTopLeftCorner())&&e.isNorthWestOf(this.getBottomRightCorner())},isSouthEastOf:function(e){return this.getTopLeftCorner().isSouthEastOf(e)||this.getBottomRightCorner().isSouthEastOf(e)},isNorthWestOf:function(e){return this.getTopLeftCorner().isNorthWestOf(e)||this.getBottomRightCorner().isNorthWestOf(e)},expand:function(e){var t=this.getTopLeftCorner(),n=this.getBottomRightCorner();return e.row<t.row||e.col<t.col||e.row>n.row||e.col>n.col?(this.from=new r(Math.min(t.row,e.row),Math.min(t.col,e.col)),this.to=new r(Math.max(n.row,e.row),Math.max(n.col,e.col)),!0):!1},expandByRange:function(e){if(this.includesRange(e)||!this.overlaps(e))return!1;var t=this.getTopLeftCorner(),n=this.getBottomRightCorner(),o=(this.getTopRightCorner(),this.getBottomLeftCorner(),e.getTopLeftCorner()),i=e.getBottomRightCorner(),a=Math.min(t.row,o.row),l=Math.min(t.col,o.col),u=Math.max(n.row,i.row),c=Math.max(n.col,i.col),d=new r(a,l),h=new r(u,c),f=new s(d,d,h).isCorner(this.from,e),p=e.isEqual(new s(d,d,h));return f&&!p&&(this.from.col>d.col&&(d.col=c,h.col=l),this.from.row>d.row&&(d.row=u,h.row=a)),this.from=d,this.to=h,!0},getDirection:function(){return this.from.isNorthWestOf(this.to)?"NW-SE":this.from.isNorthEastOf(this.to)?"NE-SW":this.from.isSouthEastOf(this.to)?"SE-NW":this.from.isSouthWestOf(this.to)?"SW-NE":void 0},setDirection:function(e){switch(e){case"NW-SE":this.from=this.getTopLeftCorner(),this.to=this.getBottomRightCorner();break;case"NE-SW":this.from=this.getTopRightCorner(),this.to=this.getBottomLeftCorner();break;case"SE-NW":this.from=this.getBottomRightCorner(),this.to=this.getTopLeftCorner();break;case"SW-NE":this.from=this.getBottomLeftCorner(),this.to=this.getTopRightCorner()}},getTopLeftCorner:function(){return new r(Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))},getBottomRightCorner:function(){return new r(Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))},getTopRightCorner:function(){return new r(Math.min(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))},getBottomLeftCorner:function(){return new r(Math.max(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))},isCorner:function(e,t){return t&&t.includes(e)&&(this.getTopLeftCorner().isEqual(new r(t.from.row,t.from.col))||this.getTopRightCorner().isEqual(new r(t.from.row,t.to.col))||this.getBottomLeftCorner().isEqual(new r(t.to.row,t.from.col))||this.getBottomRightCorner().isEqual(new r(t.to.row,t.to.col)))?!0:e.isEqual(this.getTopLeftCorner())||e.isEqual(this.getTopRightCorner())||e.isEqual(this.getBottomLeftCorner())||e.isEqual(this.getBottomRightCorner())},getOppositeCorner:function(e,t){if(!(e instanceof r))return!1;if(t&&t.includes(e)){if(this.getTopLeftCorner().isEqual(new r(t.from.row,t.from.col)))return this.getBottomRightCorner();if(this.getTopRightCorner().isEqual(new r(t.from.row,t.to.col)))return this.getBottomLeftCorner();if(this.getBottomLeftCorner().isEqual(new r(t.to.row,t.from.col)))return this.getTopRightCorner();if(this.getBottomRightCorner().isEqual(new r(t.to.row,t.to.col)))return this.getTopLeftCorner()}return e.isEqual(this.getBottomRightCorner())?this.getTopLeftCorner():e.isEqual(this.getTopLeftCorner())?this.getBottomRightCorner():e.isEqual(this.getTopRightCorner())?this.getBottomLeftCorner():e.isEqual(this.getBottomLeftCorner())?this.getTopRightCorner():void 0},getBordersSharedWith:function(e){if(!this.includesRange(e))return[];var t={top:Math.min(this.from.row,this.to.row),bottom:Math.max(this.from.row,this.to.row),left:Math.min(this.from.col,this.to.col),right:Math.max(this.from.col,this.to.col)},n={top:Math.min(e.from.row,e.to.row),bottom:Math.max(e.from.row,e.to.row),left:Math.min(e.from.col,e.to.col),right:Math.max(e.from.col,e.to.col)},o=[];return t.top==n.top&&o.push("top"),t.right==n.right&&o.push("right"),t.bottom==n.bottom&&o.push("bottom"),t.left==n.left&&o.push("left"),o},getInner:function(){for(var e=this.getTopLeftCorner(),t=this.getBottomRightCorner(),n=[],o=e.row;o<=t.row;o++)for(var i=e.col;i<=t.col;i++)this.from.row===o&&this.from.col===i||this.to.row===o&&this.to.col===i||n.push(new r(o,i));return n},getAll:function(){for(var e=this.getTopLeftCorner(),t=this.getBottomRightCorner(),n=[],o=e.row;o<=t.row;o++)for(var i=e.col;i<=t.col;i++)e.row===o&&e.col===i?n.push(e):t.row===o&&t.col===i?n.push(t):n.push(new r(o,i));return n},forAll:function(e){for(var t=this.getTopLeftCorner(),n=this.getBottomRightCorner(),o=t.row;o<=n.row;o++)for(var r=t.col;r<=n.col;r++){var i=e(o,r);if(i===!1)return}}},{}),window.WalkontableCellRange=i},{"cell/coords":5}],7:[function(e,t,n){"use strict";Object.defineProperties(n,{Walkontable:{get:function(){return x}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c,d,h,f,p,g,m,v=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),w=v.addClass,y=v.fastInnerText,b=v.isVisible,C=v.removeClass,R=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}).objectEach,_=(i=e("helpers/string"),i&&i.__esModule&&i||{"default":i}),S=_.toUpperCaseFirst,E=_.randomString,T=(s=e("event"),s&&s.__esModule&&s||{"default":s}).WalkontableEvent,O=(a=e("overlays"),a&&a.__esModule&&a||{"default":a}).WalkontableOverlays,M=(l=e("scroll"),l&&l.__esModule&&l||{"default":l}).WalkontableScroll,k=(u=e("settings"),u&&u.__esModule&&u||{"default":u}).WalkontableSettings,H=(c=e("table"),c&&c.__esModule&&c||{"default":c}).WalkontableTable,D=(d=e("viewport"),d&&d.__esModule&&d||{"default":d}).WalkontableViewport,x=((h=e("overlay/_base.js"),h&&h.__esModule&&h||{"default":h}).WalkontableOverlay,(f=e("overlay/top.js"),f&&f.__esModule&&f||{"default":f}).WalkontableTopOverlay,(p=e("overlay/left.js"),p&&p.__esModule&&p||{"default":p}).WalkontableLeftOverlay,(g=e("overlay/debug.js"),g&&g.__esModule&&g||{"default":g}).WalkontableDebugOverlay,(m=e("overlay/topLeftCorner.js"),m&&m.__esModule&&m||{"default":m}).WalkontableTopLeftCornerOverlay,function(e){var t=[];if(this.guid="wt_"+E(),e.cloneSource?(this.cloneSource=e.cloneSource,this.cloneOverlay=e.cloneOverlay,this.wtSettings=e.cloneSource.wtSettings,this.wtTable=new H(this,e.table,e.wtRootElement),this.wtScroll=new M(this),this.wtViewport=e.cloneSource.wtViewport,this.wtEvent=new T(this),this.selections=this.cloneSource.selections):(this.wtSettings=new k(this,e),this.wtTable=new H(this,e.table),this.wtScroll=new M(this),this.wtViewport=new D(this),this.wtEvent=new T(this),this.selections=this.getSetting("selections"),this.wtOverlays=new O(this),this.exportSettingsAsClassNames()),this.wtTable.THEAD.childNodes.length&&this.wtTable.THEAD.childNodes[0].childNodes.length){for(var n=0,o=this.wtTable.THEAD.childNodes[0].childNodes.length;o>n;n++)t.push(this.wtTable.THEAD.childNodes[0].childNodes[n].innerHTML);this.getSetting("columnHeaders").length||this.update("columnHeaders",[function(e,n){y(n,t[e])}])}this.drawn=!1,this.drawInterrupted=!1});$traceurRuntime.createClass(x,{draw:function(){var e=void 0!==arguments[0]?arguments[0]:!1;return this.drawInterrupted=!1,e||b(this.wtTable.TABLE)?this.wtTable.draw(e):this.drawInterrupted=!0,this},getCell:function(e){var t=void 0!==arguments[1]?arguments[1]:!1;if(!t)return this.wtTable.getCell(e);var n=this.wtSettings.getSetting("totalRows"),o=this.wtSettings.getSetting("fixedRowsTop"),r=this.wtSettings.getSetting("fixedRowsBottom"),i=this.wtSettings.getSetting("fixedColumnsLeft");if(e.row<o&&e.col<i)return this.wtOverlays.topLeftCornerOverlay.clone.wtTable.getCell(e);if(e.row<o)return this.wtOverlays.topOverlay.clone.wtTable.getCell(e);if(e.col<i&&e.row>=n-r){if(this.wtOverlays.bottomLeftCornerOverlay.clone)return this.wtOverlays.bottomLeftCornerOverlay.clone.wtTable.getCell(e)}else{if(e.col<i)return this.wtOverlays.leftOverlay.clone.wtTable.getCell(e);if(e.row>=n-r&&this.wtOverlays.bottomOverlay.clone)return this.wtOverlays.bottomOverlay.clone.wtTable.getCell(e)}return this.wtTable.getCell(e)},update:function(e,t){return this.wtSettings.update(e,t)},scrollVertical:function(e){return this.wtOverlays.topOverlay.scrollTo(e),this.getSetting("onScrollVertically"),this},scrollHorizontal:function(e){return this.wtOverlays.leftOverlay.scrollTo(e),this.getSetting("onScrollHorizontally"),this},scrollViewport:function(e){return this.wtScroll.scrollViewport(e),this},getViewport:function(){return[this.wtTable.getFirstVisibleRow(),this.wtTable.getFirstVisibleColumn(),this.wtTable.getLastVisibleRow(),this.wtTable.getLastVisibleColumn()]},getOverlayName:function(){return this.cloneOverlay?this.cloneOverlay.type:"master"},exportSettingsAsClassNames:function(){var e=this,t={rowHeaders:["array"],columnHeaders:["array"]},n=[],o=[];R(t,function(t,r){t.indexOf("array")>-1&&e.getSetting(r).length&&o.push("ht"+S(r)),n.push("ht"+S(r))}),C(this.wtTable.wtRootElement.parentNode,n),w(this.wtTable.wtRootElement.parentNode,o)},getSetting:function(e,t,n,o,r){return this.wtSettings.getSetting(e,t,n,o,r)},hasSetting:function(e){return this.wtSettings.has(e)},destroy:function(){this.wtOverlays.destroy(),this.wtEvent.destroy()}},{}),window.Walkontable=x},{event:8,"helpers/dom/element":45,"helpers/object":50,"helpers/string":52,"overlay/_base.js":11,"overlay/debug.js":12,"overlay/left.js":13,"overlay/top.js":14,"overlay/topLeftCorner.js":15,overlays:16,scroll:17,settings:19,table:20,viewport:22}],8:[function(e,t,n){"use strict";function o(e){var t=this,n=c(e);this.instance=e;var o=[null,null];this.dblClickTimeout=[null,null];var r,i,s=function(e){var n=t.parentCell(e.realTarget);l(e.realTarget,"corner")?t.instance.getSetting("onCellCornerMouseDown",e,e.realTarget):n.TD&&t.instance.hasSetting("onCellMouseDown")&&t.instance.getSetting("onCellMouseDown",e,n.coords,n.TD,t.instance),2!==e.button&&n.TD&&(o[0]=n.TD,clearTimeout(t.dblClickTimeout[0]),t.dblClickTimeout[0]=setTimeout(function(){o[0]=null},1e3))},d=function(e){t.instance.touchMoving=!0},h=function(e){n.addEventListener(this,"touchmove",d),t.checkIfTouchMove=setTimeout(function(){return t.instance.touchMoving===!0?(t.instance.touchMoving=void 0,void n.removeEventListener("touchmove",d,!1)):void s(e)},30)},f=function(e){var n,o;t.instance.hasSetting("onCellMouseOver")&&(n=t.instance.wtTable.TABLE,o=a(e.realTarget,["TD","TH"],n),o&&o!==i&&u(o,n)&&(i=o,t.instance.getSetting("onCellMouseOver",e,t.instance.wtTable.getCoords(o),o,t.instance)))},p=function(e){if(2!==e.button){var n=t.parentCell(e.realTarget);n.TD===o[0]&&n.TD===o[1]?(l(e.realTarget,"corner")?t.instance.getSetting("onCellCornerDblClick",e,n.coords,n.TD,t.instance):t.instance.getSetting("onCellDblClick",e,n.coords,n.TD,t.instance),o[0]=null,o[1]=null):n.TD===o[0]&&(o[1]=n.TD,clearTimeout(t.dblClickTimeout[1]),t.dblClickTimeout[1]=setTimeout(function(){o[1]=null},500))}},g=function(e){clearTimeout(r),e.preventDefault(),p(e)};if(n.addEventListener(this.instance.wtTable.holder,"mousedown",s),n.addEventListener(this.instance.wtTable.TABLE,"mouseover",f),n.addEventListener(this.instance.wtTable.holder,"mouseup",p),this.instance.wtTable.holder.parentNode.parentNode&&Handsontable.mobileBrowser&&!t.instance.wtTable.isWorkingOnClone()){var m="."+this.instance.wtTable.holder.parentNode.className.split(" ").join(".");n.addEventListener(this.instance.wtTable.holder,"touchstart",function(e){t.instance.touchApplied=!0,u(e.target,m)&&h.call(e.target,e)}),n.addEventListener(this.instance.wtTable.holder,"touchend",function(e){t.instance.touchApplied=!1,u(e.target,m)&&g.call(e.target,e)}),t.instance.momentumScrolling||(t.instance.momentumScrolling={}),n.addEventListener(this.instance.wtTable.holder,"scroll",function(e){clearTimeout(t.instance.momentumScrolling._timeout),t.instance.momentumScrolling.ongoing||t.instance.getSetting("onBeforeTouchScroll"),t.instance.momentumScrolling.ongoing=!0,t.instance.momentumScrolling._timeout=setTimeout(function(){t.instance.touchApplied||(t.instance.momentumScrolling.ongoing=!1,t.instance.getSetting("onAfterMomentumScroll"))},200)})}n.addEventListener(window,"resize",function(){"none"!==t.instance.getSetting("stretchH")&&t.instance.draw()}),this.destroy=function(){clearTimeout(this.dblClickTimeout[0]),clearTimeout(this.dblClickTimeout[1]),n.destroy()}}Object.defineProperties(n,{WalkontableEvent:{get:function(){return o}},__esModule:{value:!0}});var r,i,s=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),a=s.closest,l=s.hasClass,u=s.isChildOf,c=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager;o.prototype.parentCell=function(e){var t={},n=this.instance.wtTable.TABLE,o=a(e,["TD","TH"],n);return o&&u(o,n)?(t.coords=this.instance.wtTable.getCoords(o),t.TD=o):l(e,"wtBorder")&&l(e,"current")?(t.coords=this.instance.selections.current.cellRange.highlight,t.TD=this.instance.wtTable.getCell(t.coords)):l(e,"wtBorder")&&l(e,"area")&&this.instance.selections.area.cellRange&&(t.coords=this.instance.selections.area.cellRange.to,t.TD=this.instance.wtTable.getCell(t.coords)),t},window.WalkontableEvent=o},{eventManager:41,"helpers/dom/element":45}],9:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableColumnFilter:{get:function(){return o}},__esModule:{value:!0}});var o=function(e,t,n){this.offset=e,this.total=t,this.countTH=n};$traceurRuntime.createClass(o,{offsetted:function(e){return e+this.offset},unOffsetted:function(e){return e-this.offset},renderedToSource:function(e){return this.offsetted(e)},sourceToRendered:function(e){return this.unOffsetted(e)},offsettedTH:function(e){return e-this.countTH},unOffsettedTH:function(e){return e+this.countTH},visibleRowHeadedColumnToSourceColumn:function(e){return this.renderedToSource(this.offsettedTH(e))},sourceColumnToVisibleRowHeadedColumn:function(e){return this.unOffsettedTH(this.sourceToRendered(e))}},{}),window.WalkontableColumnFilter=o;
},{}],10:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableRowFilter:{get:function(){return o}},__esModule:{value:!0}});var o=function(e,t,n){this.offset=e,this.total=t,this.countTH=n};$traceurRuntime.createClass(o,{offsetted:function(e){return e+this.offset},unOffsetted:function(e){return e-this.offset},renderedToSource:function(e){return this.offsetted(e)},sourceToRendered:function(e){return this.unOffsetted(e)},offsettedTH:function(e){return e-this.countTH},unOffsettedTH:function(e){return e+this.countTH},visibleColHeadedRowToSourceRow:function(e){return this.renderedToSource(this.offsettedTH(e))},sourceRowToVisibleColHeadedRow:function(e){return this.unOffsettedTH(this.sourceToRendered(e))}},{}),window.WalkontableRowFilter=o},{}],11:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableOverlay:{get:function(){return h}},__esModule:{value:!0}});var o,r,i,s=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),a=s.getScrollableElement,l=s.getTrimmingContainer,u=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}).defineGetter,c=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,d={},h=function(e){u(this,"wot",e,{writable:!1}),this.instance=this.wot,this.type="",this.TABLE=this.wot.wtTable.TABLE,this.hider=this.wot.wtTable.hider,this.spreader=this.wot.wtTable.spreader,this.holder=this.wot.wtTable.holder,this.wtRootElement=this.wot.wtTable.wtRootElement,this.trimmingContainer=l(this.hider.parentNode.parentNode),this.mainTableScrollableElement=a(this.wot.wtTable.TABLE),this.needFullRender=this.shouldBeRendered(),this.areElementSizesAdjusted=!1},f=h;$traceurRuntime.createClass(h,{shouldBeRendered:function(){return!0},makeClone:function(e){if(-1===f.CLONE_TYPES.indexOf(e))throw new Error('Clone type "'+e+'" is not supported.');var t=document.createElement("DIV"),n=document.createElement("TABLE");return t.className="ht_clone_"+e+" handsontable",t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.overflow="hidden",n.className=this.wot.wtTable.TABLE.className,t.appendChild(n),this.type=e,this.wot.wtTable.wtRootElement.parentNode.appendChild(t),new Walkontable({cloneSource:this.wot,cloneOverlay:this,table:n})},refresh:function(){var e=void 0!==arguments[0]?arguments[0]:!1,t=this.shouldBeRendered();this.clone&&(this.needFullRender||t)&&this.clone.draw(e),this.needFullRender=t},destroy:function(){c(this.clone).destroy()}},{get CLONE_TOP(){return"top"},get CLONE_BOTTOM(){return"bottom"},get CLONE_LEFT(){return"left"},get CLONE_TOP_LEFT_CORNER(){return"top_left_corner"},get CLONE_BOTTOM_LEFT_CORNER(){return"bottom_left_corner"},get CLONE_DEBUG(){return"debug"},get CLONE_TYPES(){return[f.CLONE_TOP,f.CLONE_BOTTOM,f.CLONE_LEFT,f.CLONE_TOP_LEFT_CORNER,f.CLONE_BOTTOM_LEFT_CORNER,f.CLONE_DEBUG]},registerOverlay:function(e,t){if(-1===f.CLONE_TYPES.indexOf(e))throw new Error("Unsupported overlay ("+e+").");d[e]=t},createOverlay:function(e,t){return new d[e](t)},isOverlayTypeOf:function(e,t){return e&&d[t]?e instanceof d[t]:!1}}),window.WalkontableOverlay=h},{eventManager:41,"helpers/dom/element":45,"helpers/object":50}],12:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableDebugOverlay:{get:function(){return a}},__esModule:{value:!0}});var o,r,i=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}).addClass,s=(r=e("_base"),r&&r.__esModule&&r||{"default":r}).WalkontableOverlay,a=function(e){$traceurRuntime.superConstructor(l).call(this,e),this.clone=this.makeClone(s.CLONE_DEBUG),this.clone.wtTable.holder.style.opacity=.4,this.clone.wtTable.holder.style.textShadow="0 0 2px #ff0000",i(this.clone.wtTable.holder.parentNode,"wtDebugVisible")},l=a;$traceurRuntime.createClass(a,{},{},s),window.WalkontableDebugOverlay=a,s.registerOverlay(s.CLONE_DEBUG,a)},{_base:11,"helpers/dom/element":45}],13:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableLeftOverlay:{get:function(){return g}},__esModule:{value:!0}});var o,r,i=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),s=i.addClass,a=i.getScrollbarWidth,l=i.getScrollLeft,u=i.getWindowScrollTop,c=i.hasClass,d=i.outerWidth,h=i.removeClass,f=i.setOverlayPosition,p=(r=e("_base"),r&&r.__esModule&&r||{"default":r}).WalkontableOverlay,g=function(e){$traceurRuntime.superConstructor(m).call(this,e),this.clone=this.makeClone(p.CLONE_LEFT)},m=g;$traceurRuntime.createClass(g,{shouldBeRendered:function(){return this.wot.getSetting("fixedColumnsLeft")||this.wot.getSetting("rowHeaders").length?!0:!1},resetFixedPosition:function(){if(this.needFullRender&&this.wot.wtTable.holder.parentNode){var e=this.clone.wtTable.holder.parentNode,t=0;if(this.trimmingContainer===window){var n,o,r=this.wot.wtTable.hider.getBoundingClientRect(),i=Math.ceil(r.left),s=Math.ceil(r.right);o=this.wot.wtTable.hider.style.top,o=""===o?0:o,n=0>i&&s-e.offsetWidth>0?-i:0,t=n,n+="px",f(e,n,o)}else t=this.getScrollPosition();this.adjustHeaderBordersPosition(t),this.adjustElementsSize()}},setScrollPosition:function(e){this.mainTableScrollableElement===window?window.scrollTo(e,u()):this.mainTableScrollableElement.scrollLeft=e},onScroll:function(){this.wot.getSetting("onScrollHorizontally")},sumCellSizes:function(e,t){for(var n=0,o=this.wot.wtSettings.defaultColumnWidth;t>e;)n+=this.wot.wtTable.getStretchedColumnWidth(e)||o,e++;return n},adjustElementsSize:function(){var e=void 0!==arguments[0]?arguments[0]:!1;(this.needFullRender||e)&&(this.adjustRootElementSize(),this.adjustRootChildrenSize(),e||(this.areElementSizesAdjusted=!0))},adjustRootElementSize:function(){var e,t=this.wot.wtTable.holder,n=t.clientHeight===t.offsetHeight?0:a(),o=this.clone.wtTable.holder.parentNode,r=o.style;this.trimmingContainer!==window&&(r.height=this.wot.wtViewport.getWorkspaceHeight()-n+"px"),this.clone.wtTable.holder.style.height=r.height,e=d(this.clone.wtTable.TABLE),r.width=(0===e?e:e+4)+"px"},adjustRootChildrenSize:function(){var e=a();this.clone.wtTable.hider.style.height=this.hider.style.height,this.clone.wtTable.holder.style.height=this.clone.wtTable.holder.parentNode.style.height,0===e&&(e=30),this.clone.wtTable.holder.style.width=parseInt(this.clone.wtTable.holder.parentNode.style.width,10)+e+"px"},applyToDOM:function(){var e=this.wot.getSetting("totalColumns");if(this.areElementSizesAdjusted||this.adjustElementsSize(),"number"==typeof this.wot.wtViewport.columnsRenderCalculator.startPosition)this.spreader.style.left=this.wot.wtViewport.columnsRenderCalculator.startPosition+"px";else{if(0!==e)throw new Error("Incorrect value of the columnsRenderCalculator");this.spreader.style.left="0"}this.spreader.style.right="",this.needFullRender&&this.syncOverlayOffset()},syncOverlayOffset:function(){"number"==typeof this.wot.wtViewport.rowsRenderCalculator.startPosition?this.clone.wtTable.spreader.style.top=this.wot.wtViewport.rowsRenderCalculator.startPosition+"px":this.clone.wtTable.spreader.style.top=""},scrollTo:function(e,t){var n=this.getTableParentOffset(),o=this.wot.cloneSource?this.wot.cloneSource:this.wot,r=o.wtTable.holder,i=0;t&&r.offsetWidth!==r.clientWidth&&(i=a()),t?(n+=this.sumCellSizes(0,e+1),n-=this.wot.wtViewport.getViewportWidth()):n+=this.sumCellSizes(this.wot.getSetting("fixedColumnsLeft"),e),n+=i,this.setScrollPosition(n)},getTableParentOffset:function(){return this.trimmingContainer===window?this.wot.wtTable.holderOffset.left:0},getScrollPosition:function(){return l(this.mainTableScrollableElement)},adjustHeaderBordersPosition:function(e){var t=this.wot.wtTable.holder.parentNode,n=this.wot.getSetting("rowHeaders"),o=this.wot.getSetting("fixedColumnsLeft");if(o&&!n.length)s(t,"innerBorderLeft");else if(!o&&n.length){var r=c(t,"innerBorderLeft");e?s(t,"innerBorderLeft"):h(t,"innerBorderLeft"),(!r&&e||r&&!e)&&this.wot.wtOverlays.adjustElementsSize()}}},{},p),window.WalkontableLeftOverlay=g,p.registerOverlay(p.CLONE_LEFT,g)},{_base:11,"helpers/dom/element":45}],14:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableTopOverlay:{get:function(){return g}},__esModule:{value:!0}});var o,r,i=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),s=i.addClass,a=i.getScrollbarWidth,l=i.getScrollTop,u=i.getWindowScrollLeft,c=i.hasClass,d=i.outerHeight,h=i.removeClass,f=i.setOverlayPosition,p=(r=e("_base"),r&&r.__esModule&&r||{"default":r}).WalkontableOverlay,g=function(e){$traceurRuntime.superConstructor(m).call(this,e),this.clone=this.makeClone(p.CLONE_TOP)},m=g;$traceurRuntime.createClass(g,{shouldBeRendered:function(){return this.wot.getSetting("fixedRowsTop")||this.wot.getSetting("columnHeaders").length?!0:!1},resetFixedPosition:function(){if(this.needFullRender&&this.wot.wtTable.holder.parentNode){var e=this.clone.wtTable.holder.parentNode,t=0;if(this.wot.wtOverlays.leftOverlay.trimmingContainer===window){var n,o,r=this.wot.wtTable.hider.getBoundingClientRect(),i=Math.ceil(r.top),s=Math.ceil(r.bottom);n=this.wot.wtTable.hider.style.left,n=""===n?0:n,o=0>i&&s-e.offsetHeight>0?-i:0,t=o,o+="px",f(e,n,o)}else t=this.getScrollPosition();this.adjustHeaderBordersPosition(t),this.adjustElementsSize()}},setScrollPosition:function(e){this.mainTableScrollableElement===window?window.scrollTo(u(),e):this.mainTableScrollableElement.scrollTop=e},onScroll:function(){this.wot.getSetting("onScrollVertically")},sumCellSizes:function(e,t){for(var n=0,o=this.wot.wtSettings.settings.defaultRowHeight;t>e;){var r=this.wot.wtTable.getRowHeight(e);n+=void 0===r?o:r,e++}return n},adjustElementsSize:function(){var e=void 0!==arguments[0]?arguments[0]:!1;(this.needFullRender||e)&&(this.adjustRootElementSize(),this.adjustRootChildrenSize(),e||(this.areElementSizesAdjusted=!0))},adjustRootElementSize:function(){var e,t=this.wot.wtTable.holder,n=t.clientWidth===t.offsetWidth?0:a(),o=this.clone.wtTable.holder.parentNode,r=o.style;this.trimmingContainer!==window&&(r.width=this.wot.wtViewport.getWorkspaceWidth()-n+"px"),this.clone.wtTable.holder.style.width=r.width,e=d(this.clone.wtTable.TABLE),r.height=(0===e?e:e+4)+"px"},adjustRootChildrenSize:function(){var e=a();this.clone.wtTable.hider.style.width=this.hider.style.width,this.clone.wtTable.holder.style.width=this.clone.wtTable.holder.parentNode.style.width,0===e&&(e=30),this.clone.wtTable.holder.style.height=parseInt(this.clone.wtTable.holder.parentNode.style.height,10)+e+"px"},applyToDOM:function(){var e=this.wot.getSetting("totalRows");if(this.areElementSizesAdjusted||this.adjustElementsSize(),"number"==typeof this.wot.wtViewport.rowsRenderCalculator.startPosition)this.spreader.style.top=this.wot.wtViewport.rowsRenderCalculator.startPosition+"px";else{if(0!==e)throw new Error("Incorrect value of the rowsRenderCalculator");this.spreader.style.top="0"}this.spreader.style.bottom="",this.needFullRender&&this.syncOverlayOffset()},syncOverlayOffset:function(){"number"==typeof this.wot.wtViewport.columnsRenderCalculator.startPosition?this.clone.wtTable.spreader.style.left=this.wot.wtViewport.columnsRenderCalculator.startPosition+"px":this.clone.wtTable.spreader.style.left=""},scrollTo:function(e,t){var n=this.getTableParentOffset(),o=this.wot.cloneSource?this.wot.cloneSource:this.wot,r=o.wtTable.holder,i=0;if(t&&r.offsetHeight!==r.clientHeight&&(i=a()),t){var s=this.wot.getSetting("fixedRowsBottom"),l=(this.wot.getSetting("fixedRowsTop"),this.wot.getSetting("totalRows"));n+=this.sumCellSizes(0,e+1),n-=this.wot.wtViewport.getViewportHeight()-this.sumCellSizes(l-s,l),n+=1}else n+=this.sumCellSizes(this.wot.getSetting("fixedRowsTop"),e);n+=i,this.setScrollPosition(n)},getTableParentOffset:function(){return this.mainTableScrollableElement===window?this.wot.wtTable.holderOffset.top:0},getScrollPosition:function(){return l(this.mainTableScrollableElement)},adjustHeaderBordersPosition:function(e){if(0===this.wot.getSetting("fixedRowsTop")&&this.wot.getSetting("columnHeaders").length>0){var t=this.wot.wtTable.holder.parentNode,n=c(t,"innerBorderTop");e?s(t,"innerBorderTop"):h(t,"innerBorderTop"),(!n&&e||n&&!e)&&this.wot.wtOverlays.adjustElementsSize()}if(0===this.wot.getSetting("rowHeaders").length){var o=this.clone.wtTable.THEAD.querySelectorAll("th:nth-of-type(2)");if(o)for(var r=0;r<o.length;r++)o[r].style["border-left-width"]=0}}},{},p),window.WalkontableTopOverlay=g,p.registerOverlay(p.CLONE_TOP,g)},{_base:11,"helpers/dom/element":45}],15:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableTopLeftCornerOverlay:{get:function(){return c}},__esModule:{value:!0}});var o,r,i=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),s=i.outerHeight,a=i.outerWidth,l=i.setOverlayPosition,u=(r=e("_base"),r&&r.__esModule&&r||{"default":r}).WalkontableOverlay,c=function(e){$traceurRuntime.superConstructor(d).call(this,e),this.clone=this.makeClone(u.CLONE_TOP_LEFT_CORNER)},d=c;$traceurRuntime.createClass(c,{shouldBeRendered:function(){return(this.wot.getSetting("fixedRowsTop")||this.wot.getSetting("columnHeaders").length)&&(this.wot.getSetting("fixedColumnsLeft")||this.wot.getSetting("rowHeaders").length)?!0:!1},resetFixedPosition:function(){if(this.wot.wtTable.holder.parentNode){var e=this.clone.wtTable.holder.parentNode,t=s(this.clone.wtTable.TABLE),n=a(this.clone.wtTable.TABLE);if(this.trimmingContainer===window){var o,r,i=this.wot.wtTable.hider.getBoundingClientRect(),u=Math.ceil(i.top),c=Math.ceil(i.left),d=Math.ceil(i.bottom),h=Math.ceil(i.right);o=0>c&&h-e.offsetWidth>0?-c+"px":"0",r=0>u&&d-e.offsetHeight>0?-u+"px":"0",l(e,o,r)}e.style.height=(0===t?t:t+4)+"px",e.style.width=(0===n?n:n+4)+"px"}}},{},u),window.WalkontableTopLeftCornerOverlay=c,u.registerOverlay(u.CLONE_TOP_LEFT_CORNER,c)},{_base:11,"helpers/dom/element":45}],16:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableOverlays:{get:function(){return f}},__esModule:{value:!0}});var o,r,i,s=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),a=s.getScrollableElement,l=s.getScrollbarWidth,u=s.getScrollLeft,c=s.getScrollTop,d=(r=e("helpers/unicode"),r&&r.__esModule&&r||{"default":r}).isKey,h=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).EventManager,f=function(e){this.wot=e,this.instance=this.wot,this.eventManager=new h(this.wot),this.wot.update("scrollbarWidth",l()),this.wot.update("scrollbarHeight",l()),this.mainTableScrollableElement=a(this.wot.wtTable.TABLE),this.topOverlay=WalkontableOverlay.createOverlay(WalkontableOverlay.CLONE_TOP,this.wot),"undefined"==typeof WalkontableBottomOverlay?this.bottomOverlay={needFullRender:!1}:this.bottomOverlay=WalkontableOverlay.createOverlay(WalkontableOverlay.CLONE_BOTTOM,this.wot),this.leftOverlay=WalkontableOverlay.createOverlay(WalkontableOverlay.CLONE_LEFT,this.wot),this.topOverlay.needFullRender&&this.leftOverlay.needFullRender&&(this.topLeftCornerOverlay=WalkontableOverlay.createOverlay(WalkontableOverlay.CLONE_TOP_LEFT_CORNER,this.wot)),this.bottomOverlay.needFullRender&&this.leftOverlay.needFullRender&&"undefined"!=typeof WalkontableBottomLeftCornerOverlay?this.bottomLeftCornerOverlay=WalkontableOverlay.createOverlay(WalkontableOverlay.CLONE_BOTTOM_LEFT_CORNER,this.wot):this.bottomLeftCornerOverlay={needFullRender:!1},this.wot.getSetting("debug")&&(this.debug=WalkontableOverlay.createOverlay(WalkontableOverlay.CLONE_DEBUG,this.wot)),this.destroyed=!1,this.keyPressed=!1,this.spreaderLastSize={width:null,height:null},this.overlayScrollPositions={master:{top:0,left:0},top:{top:null,left:0},bottom:{top:null,left:0},left:{top:0,left:null}},this.registerListeners()};$traceurRuntime.createClass(f,{refreshAll:function(){if(this.wot.drawn){if(!this.wot.wtTable.holder.parentNode)return void this.destroy();this.wot.draw(!0),this.topOverlay.onScroll(),this.leftOverlay.onScroll()}},registerListeners:function(){var e=this;this.eventManager.addEventListener(document.documentElement,"keydown",function(t){return e.onKeyDown(t)}),this.eventManager.addEventListener(document.documentElement,"keyup",function(){return e.onKeyUp()}),this.eventManager.addEventListener(document,"visibilitychange",function(){return e.onKeyUp()}),this.eventManager.addEventListener(this.mainTableScrollableElement,"scroll",function(t){return e.onTableScroll(t)}),this.topOverlay.needFullRender&&(this.eventManager.addEventListener(this.topOverlay.clone.wtTable.holder,"scroll",function(t){return e.onTableScroll(t)}),this.eventManager.addEventListener(this.topOverlay.clone.wtTable.holder,"wheel",function(t){return e.onTableScroll(t)})),this.bottomOverlay.needFullRender&&(this.eventManager.addEventListener(this.bottomOverlay.clone.wtTable.holder,"scroll",function(t){return e.onTableScroll(t)}),this.eventManager.addEventListener(this.bottomOverlay.clone.wtTable.holder,"wheel",function(t){return e.onTableScroll(t)})),this.leftOverlay.needFullRender&&(this.eventManager.addEventListener(this.leftOverlay.clone.wtTable.holder,"scroll",function(t){return e.onTableScroll(t)}),this.eventManager.addEventListener(this.leftOverlay.clone.wtTable.holder,"wheel",function(t){return e.onTableScroll(t)})),this.topOverlay.trimmingContainer!==window&&this.leftOverlay.trimmingContainer!==window&&this.eventManager.addEventListener(window,"wheel",function(t){var n,o=t.wheelDeltaY||t.deltaY,r=t.wheelDeltaX||t.deltaX;e.topOverlay.clone.wtTable.holder.contains(t.realTarget)?n="top":e.bottomOverlay.clone&&e.bottomOverlay.clone.wtTable.holder.contains(t.realTarget)?n="bottom":e.leftOverlay.clone.wtTable.holder.contains(t.realTarget)&&(n="left"),"top"==n&&0!==o?t.preventDefault():"left"==n&&0!==r?t.preventDefault():"bottom"==n&&0!==o&&t.preventDefault()})},onTableScroll:function(e){Handsontable.mobileBrowser||(!this.keyPressed||this.mainTableScrollableElement===window||e.target.contains(this.mainTableScrollableElement))&&("scroll"===e.type?this.syncScrollPositions(e):this.translateMouseWheelToScroll(e))},onKeyDown:function(e){this.keyPressed=d(e.keyCode,"ARROW_UP|ARROW_RIGHT|ARROW_DOWN|ARROW_LEFT")},onKeyUp:function(){this.keyPressed=!1},translateMouseWheelToScroll:function(e){for(var t,n=this.topOverlay.clone.wtTable.holder,o=this.bottomOverlay.clone?this.bottomOverlay.clone.wtTable.holder:null,r=this.leftOverlay.clone.wtTable.holder,i={type:"wheel"},s=e.target,a=e.wheelDeltaY||-1*e.deltaY,l=e.wheelDeltaX||-1*e.deltaX;s!=document&&null!=s;){if(s.className.indexOf("wtHolder")>-1){t=s;break}s=s.parentNode}return i.target=t,t==n?this.syncScrollPositions(i,-.2*a):t==o?this.syncScrollPositions(i,-.2*a):t==r&&this.syncScrollPositions(i,-.2*l),!1},syncScrollPositions:function(e){var t=void 0!==arguments[1]?arguments[1]:null;if(!this.destroyed){if(0===arguments.length)return void this.syncScrollWithMaster();var n,o,r,i=this.mainTableScrollableElement,s=e.target,a=0,l=!1;this.topOverlay.needFullRender&&(n=this.topOverlay.clone.wtTable.holder),this.bottomOverlay.needFullRender&&(r=this.bottomOverlay.clone.wtTable.holder),this.leftOverlay.needFullRender&&(o=this.leftOverlay.clone.wtTable.holder),s===document&&(s=window),s===i?(a=u(s),this.overlayScrollPositions.master.left!==a&&(this.overlayScrollPositions.master.left=a,l=!0,n&&(n.scrollLeft=a),r&&(r.scrollLeft=a)),a=c(s),this.overlayScrollPositions.master.top!==a&&(this.overlayScrollPositions.master.top=a,l=!0,o&&(o.scrollTop=a))):s===r?(a=u(s),this.overlayScrollPositions.bottom.left!==a&&(this.overlayScrollPositions.bottom.left=a,l=!0,i.scrollLeft=a),null!==t&&(l=!0,i.scrollTop+=t)):s===n?(a=u(s),this.overlayScrollPositions.top.left!==a&&(this.overlayScrollPositions.top.left=a,l=!0,i.scrollLeft=a),null!==t&&(l=!0,i.scrollTop+=t)):s===o&&(a=c(s),this.overlayScrollPositions.left.top!==a&&(this.overlayScrollPositions.left.top=a,l=!0,i.scrollTop=a),null!==t&&(l=!0,i.scrollLeft+=t)),!this.keyPressed&&l&&"scroll"===e.type&&this.refreshAll()}},syncScrollWithMaster:function(){var e=this.topOverlay.mainTableScrollableElement;this.topOverlay.needFullRender&&(this.topOverlay.clone.wtTable.holder.scrollLeft=e.scrollLeft),this.leftOverlay.needFullRender&&(this.leftOverlay.clone.wtTable.holder.scrollTop=e.scrollTop)},destroy:function(){this.eventManager.destroy(),this.topOverlay.destroy(),this.bottomOverlay.clone&&this.bottomOverlay.destroy(),this.leftOverlay.destroy(),this.topLeftCornerOverlay&&this.topLeftCornerOverlay.destroy(),this.bottomLeftCornerOverlay&&this.bottomLeftCornerOverlay.clone&&this.bottomLeftCornerOverlay.destroy(),this.debug&&this.debug.destroy(),this.destroyed=!0},refresh:function(){var e=void 0!==arguments[0]?arguments[0]:!1;if(this.topOverlay.areElementSizesAdjusted&&this.leftOverlay.areElementSizesAdjusted){var t=this.wot.wtTable.wtRootElement.parentNode||this.wot.wtTable.wtRootElement,n=t.clientWidth,o=t.clientHeight;(n!==this.spreaderLastSize.width||o!==this.spreaderLastSize.height)&&(this.spreaderLastSize.width=n,this.spreaderLastSize.height=o,this.adjustElementsSize())}this.bottomOverlay.clone&&this.bottomOverlay.refresh(e),this.leftOverlay.refresh(e),this.topOverlay.refresh(e),this.topLeftCornerOverlay&&this.topLeftCornerOverlay.refresh(e),this.bottomLeftCornerOverlay&&this.bottomLeftCornerOverlay.clone&&this.bottomLeftCornerOverlay.refresh(e),this.debug&&this.debug.refresh(e)},adjustElementsSize:function(){var e=void 0!==arguments[0]?arguments[0]:!1,t=this.wot.getSetting("totalColumns"),n=this.wot.getSetting("totalRows"),o=this.wot.wtViewport.getRowHeaderWidth(),r=this.wot.wtViewport.getColumnHeaderHeight(),i=this.wot.wtTable.hider.style;i.width=o+this.leftOverlay.sumCellSizes(0,t)+"px",i.height=r+this.topOverlay.sumCellSizes(0,n)+1+"px",this.topOverlay.adjustElementsSize(e),this.leftOverlay.adjustElementsSize(e),this.bottomOverlay.clone&&this.bottomOverlay.adjustElementsSize(e)},applyToDOM:function(){this.topOverlay.areElementSizesAdjusted&&this.leftOverlay.areElementSizesAdjusted||this.adjustElementsSize(),this.topOverlay.applyToDOM(),this.bottomOverlay.clone&&this.bottomOverlay.applyToDOM(),this.leftOverlay.applyToDOM()}},{}),window.WalkontableOverlays=f},{eventManager:41,"helpers/dom/element":45,"helpers/unicode":53}],17:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableScroll:{get:function(){return o}},__esModule:{value:!0}});var o=function(e){this.wot=e,this.instance=e};$traceurRuntime.createClass(o,{scrollViewport:function(e){if(this.wot.drawn){var t=this.wot.getSetting("totalRows"),n=this.wot.getSetting("totalColumns"),o=this.instance.getSetting("fixedRowsTop"),r=this.instance.getSetting("fixedRowsBottom"),i=this.instance.getSetting("fixedColumnsLeft");if(e.row<0||e.row>t-1)throw new Error("row "+e.row+" does not exist");if(e.col<0||e.col>n-1)throw new Error("column "+e.col+" does not exist");e.row>this.instance.wtTable.getLastVisibleRow()&&e.row<t-r?this.wot.wtOverlays.topOverlay.scrollTo(e.row,!0):e.row>=o&&e.row<this.instance.wtTable.getFirstVisibleRow()&&this.wot.wtOverlays.topOverlay.scrollTo(e.row),e.col>this.instance.wtTable.getLastVisibleColumn()?this.wot.wtOverlays.leftOverlay.scrollTo(e.col,!0):e.col>=i&&e.col<this.instance.wtTable.getFirstVisibleColumn()&&this.wot.wtOverlays.leftOverlay.scrollTo(e.col)}}},{}),window.WalkontableScroll=o},{}],18:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableSelection:{get:function(){return d}},__esModule:{value:!0}});var o,r,i,s,a=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}).addClass,l=(r=e("border"),r&&r.__esModule&&r||{"default":r}).WalkontableBorder,u=(i=e("cell/coords"),i&&i.__esModule&&i||{"default":i}).WalkontableCellCoords,c=(s=e("cell/range"),s&&s.__esModule&&s||{"default":s}).WalkontableCellRange,d=function(e,t){this.settings=e,this.cellRange=t||null,this.instanceBorders={}};$traceurRuntime.createClass(d,{getBorder:function(e){return this.instanceBorders[e.guid]?this.instanceBorders[e.guid]:void(this.instanceBorders[e.guid]=new l(e,this.settings))},isEmpty:function(){return null===this.cellRange},add:function(e){this.isEmpty()?this.cellRange=new c(e,e,e):this.cellRange.expand(e)},replace:function(e,t){if(!this.isEmpty()){if(this.cellRange.from.isEqual(e))return this.cellRange.from=t,!0;if(this.cellRange.to.isEqual(e))return this.cellRange.to=t,!0}return!1},clear:function(){this.cellRange=null},getCorners:function(){var e=this.cellRange.getTopLeftCorner(),t=this.cellRange.getBottomRightCorner();return[e.row,e.col,t.row,t.col]},addClassAtCoords:function(e,t,n,o){var r=e.wtTable.getCell(new u(t,n));"object"==typeof r&&a(r,o)},draw:function(e){if(this.isEmpty()){if(this.settings.border){var t=this.getBorder(e);t&&t.disappear()}}else{for(var n,o,r,i=e.wtTable.getRenderedRowsCount(),s=e.wtTable.getRenderedColumnsCount(),l=this.getCorners(),u=0;s>u;u++)o=e.wtTable.columnFilter.renderedToSource(u),o>=l[1]&&o<=l[3]&&(r=e.wtTable.getColumnHeader(o),r&&this.settings.highlightColumnClassName&&a(r,this.settings.highlightColumnClassName));for(var c=0;i>c;c++){n=e.wtTable.rowFilter.renderedToSource(c),n>=l[0]&&n<=l[2]&&(r=e.wtTable.getRowHeader(n),r&&this.settings.highlightRowClassName&&a(r,this.settings.highlightRowClassName));for(var d=0;s>d;d++)o=e.wtTable.columnFilter.renderedToSource(d),n>=l[0]&&n<=l[2]&&o>=l[1]&&o<=l[3]?this.settings.className&&this.addClassAtCoords(e,n,o,this.settings.className):n>=l[0]&&n<=l[2]?this.settings.highlightRowClassName&&this.addClassAtCoords(e,n,o,this.settings.highlightRowClassName):o>=l[1]&&o<=l[3]&&this.settings.highlightColumnClassName&&this.addClassAtCoords(e,n,o,this.settings.highlightColumnClassName)}if(e.getSetting("onBeforeDrawBorders",l,this.settings.className),this.settings.border){var h=this.getBorder(e);h&&h.appear(l)}}}},{}),window.WalkontableSelection=d},{border:2,"cell/coords":5,"cell/range":6,"helpers/dom/element":45}],19:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableSettings:{get:function(){return i}},__esModule:{value:!0}});var o,r=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}).fastInnerText,i=function(e,t){var n=this;this.wot=e,this.instance=e,this.defaults={table:void 0,debug:!1,externalRowCalculator:!1,stretchH:"none",currentRowClassName:null,currentColumnClassName:null,data:void 0,fixedColumnsLeft:0,fixedRowsTop:0,fixedRowsBottom:0,minSpareRows:0,rowHeaders:function(){return[]},columnHeaders:function(){return[]},totalRows:void 0,totalColumns:void 0,cellRenderer:function(e,t,o){var i=n.getSetting("data",e,t);r(o,void 0===i||null===i?"":i)},columnWidth:function(e){},rowHeight:function(e){},defaultRowHeight:23,defaultColumnWidth:50,selections:null,hideBorderOnMouseDownOver:!1,viewportRowCalculatorOverride:null,viewportColumnCalculatorOverride:null,onCellMouseDown:null,onCellMouseOver:null,onCellDblClick:null,onCellCornerMouseDown:null,onCellCornerDblClick:null,beforeDraw:null,onDraw:null,onBeforeDrawBorders:null,onScrollVertically:null,onScrollHorizontally:null,onBeforeTouchScroll:null,onAfterMomentumScroll:null,scrollbarWidth:10,scrollbarHeight:10,renderAllRows:!1,groups:!1},this.settings={};for(var o in this.defaults)if(this.defaults.hasOwnProperty(o))if(void 0!==t[o])this.settings[o]=t[o];else{if(void 0===this.defaults[o])throw new Error('A required setting "'+o+'" was not provided');this.settings[o]=this.defaults[o]}};$traceurRuntime.createClass(i,{update:function(e,t){if(void 0===t)for(var n in e)e.hasOwnProperty(n)&&(this.settings[n]=e[n]);else this.settings[e]=t;return this.wot},getSetting:function(e,t,n,o,r){return"function"==typeof this.settings[e]?this.settings[e](t,n,o,r):void 0!==t&&Array.isArray(this.settings[e])?this.settings[e][t]:this.settings[e]},has:function(e){return!!this.settings[e]}},{}),window.WalkontableSettings=i},{"helpers/dom/element":45}],20:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableTable:{get:function(){return C}},__esModule:{value:!0}});var o,r,i,s,a,l,u=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),c=u.getStyle,d=u.getTrimmingContainer,h=u.hasClass,f=u.index,p=u.offset,g=u.removeClass,m=u.removeTextNodes,v=(u.overlayContainsElement,(r=e("cell/coords"),r&&r.__esModule&&r||{"default":r}).WalkontableCellCoords),w=((i=e("cell/range"),i&&i.__esModule&&i||{"default":i}).WalkontableCellRange,(s=e("filter/column"),s&&s.__esModule&&s||{"default":s}).WalkontableColumnFilter),y=(a=e("filter/row"),a&&a.__esModule&&a||{"default":a}).WalkontableRowFilter,b=(l=e("tableRenderer"),l&&l.__esModule&&l||{"default":l}).WalkontableTableRenderer,C=function(e,t){this.wot=e,this.instance=this.wot,this.TABLE=t,this.TBODY=null,this.THEAD=null,this.COLGROUP=null,this.tableOffset=0,this.holderOffset=0,m(this.TABLE),this.spreader=this.createSpreader(this.TABLE),this.hider=this.createHider(this.spreader),this.holder=this.createHolder(this.hider),this.wtRootElement=this.holder.parentNode,this.alignOverlaysWithTrimmingContainer(),this.fixTableDomTree(),this.colgroupChildrenLength=this.COLGROUP.childNodes.length,this.theadChildrenLength=this.THEAD.firstChild?this.THEAD.firstChild.childNodes.length:0,this.tbodyChildrenLength=this.TBODY.childNodes.length,this.rowFilter=null,this.columnFilter=null};$traceurRuntime.createClass(C,{fixTableDomTree:function(){this.TBODY=this.TABLE.querySelector("tbody"),this.TBODY||(this.TBODY=document.createElement("tbody"),this.TABLE.appendChild(this.TBODY)),this.THEAD=this.TABLE.querySelector("thead"),this.THEAD||(this.THEAD=document.createElement("thead"),this.TABLE.insertBefore(this.THEAD,this.TBODY)),this.COLGROUP=this.TABLE.querySelector("colgroup"),this.COLGROUP||(this.COLGROUP=document.createElement("colgroup"),this.TABLE.insertBefore(this.COLGROUP,this.THEAD)),this.wot.getSetting("columnHeaders").length&&!this.THEAD.childNodes.length&&this.THEAD.appendChild(document.createElement("TR"))},createSpreader:function(e){var t,n=e.parentNode;return n&&1===n.nodeType&&h(n,"wtHolder")||(t=document.createElement("div"),t.className="wtSpreader",n&&n.insertBefore(t,e),t.appendChild(e)),t.style.position="relative",t},createHider:function(e){var t,n=e.parentNode;return n&&1===n.nodeType&&h(n,"wtHolder")||(t=document.createElement("div"),t.className="wtHider",n&&n.insertBefore(t,e),t.appendChild(e)),t},createHolder:function(e){var t,n=e.parentNode;return n&&1===n.nodeType&&h(n,"wtHolder")||(t=document.createElement("div"),t.style.position="relative",t.className="wtHolder",n&&n.insertBefore(t,e),this.isWorkingOnClone()||(t.parentNode.className+="ht_master handsontable"),t.appendChild(e)),t},alignOverlaysWithTrimmingContainer:function(){var e=d(this.wtRootElement);this.isWorkingOnClone()||(this.holder.parentNode.style.position="relative",e===window?(this.holder.style.overflow="visible",this.wtRootElement.style.overflow="visible"):(this.holder.style.width=c(e,"width"),this.holder.style.height=c(e,"height"),this.holder.style.overflow=""))},isWorkingOnClone:function(){return!!this.wot.cloneSource},draw:function(e){var t=this.instance.getSetting("totalRows");if(this.isWorkingOnClone()||(this.holderOffset=p(this.holder),e=this.wot.wtViewport.createRenderCalculators(e)),e)this.isWorkingOnClone()||this.wot.wtViewport.createVisibleCalculators(),this.wot.wtOverlays&&this.wot.wtOverlays.refresh(!0);else{this.isWorkingOnClone()?this.tableOffset=this.wot.cloneSource.wtTable.tableOffset:this.tableOffset=p(this.TABLE);var n;n=WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_DEBUG)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_TOP)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_TOP_LEFT_CORNER)?0:WalkontableOverlay.isOverlayTypeOf(this.instance.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM)||WalkontableOverlay.isOverlayTypeOf(this.instance.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM_LEFT_CORNER)?t-this.wot.getSetting("fixedRowsBottom"):this.wot.wtViewport.rowsRenderCalculator.startRow;var o;o=WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_DEBUG)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_LEFT)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_TOP_LEFT_CORNER)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM_LEFT_CORNER)?0:this.wot.wtViewport.columnsRenderCalculator.startColumn,
this.rowFilter=new y(n,t,this.wot.getSetting("columnHeaders").length),this.columnFilter=new w(o,this.wot.getSetting("totalColumns"),this.wot.getSetting("rowHeaders").length),this._doDraw(),this.alignOverlaysWithTrimmingContainer()}return this.refreshSelections(e),this.isWorkingOnClone()||(this.wot.wtOverlays.topOverlay.resetFixedPosition(),this.wot.wtOverlays.bottomOverlay.clone&&this.wot.wtOverlays.bottomOverlay.resetFixedPosition(),this.wot.wtOverlays.leftOverlay.resetFixedPosition(),this.wot.wtOverlays.topLeftCornerOverlay&&this.wot.wtOverlays.topLeftCornerOverlay.resetFixedPosition(),this.instance.wtOverlays.bottomLeftCornerOverlay&&this.instance.wtOverlays.bottomLeftCornerOverlay.clone&&this.wot.wtOverlays.bottomLeftCornerOverlay.resetFixedPosition()),this.wot.drawn=!0,this},_doDraw:function(){var e=new b(this);e.render()},removeClassFromCells:function(e){for(var t=this.TABLE.querySelectorAll("."+e),n=0,o=t.length;o>n;n++)g(t[n],e)},refreshSelections:function(e){if(this.wot.selections){var t=this.wot.selections.length;if(e)for(var n=0;t>n;n++)this.wot.selections[n].settings.className&&this.removeClassFromCells(this.wot.selections[n].settings.className),this.wot.selections[n].settings.highlightRowClassName&&this.removeClassFromCells(this.wot.selections[n].settings.highlightRowClassName),this.wot.selections[n].settings.highlightColumnClassName&&this.removeClassFromCells(this.wot.selections[n].settings.highlightColumnClassName);for(var o=0;t>o;o++)this.wot.selections[o].draw(this.wot,e)}},getCell:function(e){if(this.isRowBeforeRenderedRows(e.row))return-1;if(this.isRowAfterRenderedRows(e.row))return-2;var t=this.TBODY.childNodes[this.rowFilter.sourceToRendered(e.row)];return t?t.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e.col)]:void 0},getColumnHeader:function(e){var t=void 0!==arguments[1]?arguments[1]:0,n=this.THEAD.childNodes[t];return n?n.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e)]:void 0},getRowHeader:function(e){if(0===this.columnFilter.sourceColumnToVisibleRowHeadedColumn(0))return null;var t=this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)];return t?t.childNodes[0]:void 0},getCoords:function(e){var t=e.parentNode,n=f(t);n=t.parentNode===this.THEAD?this.rowFilter.visibleColHeadedRowToSourceRow(n):this.rowFilter.renderedToSource(n);var o=this.columnFilter.visibleRowHeadedColumnToSourceColumn(e.cellIndex);return new v(n,o)},getTrForRow:function(e){return this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)]},getFirstRenderedRow:function(){return this.wot.wtViewport.rowsRenderCalculator.startRow},getFirstVisibleRow:function(){return this.wot.wtViewport.rowsVisibleCalculator.startRow},getFirstRenderedColumn:function(){return this.wot.wtViewport.columnsRenderCalculator.startColumn},getFirstVisibleColumn:function(){return this.wot.wtViewport.columnsVisibleCalculator.startColumn},getLastRenderedRow:function(){return this.wot.wtViewport.rowsRenderCalculator.endRow},getLastVisibleRow:function(){return this.wot.wtViewport.rowsVisibleCalculator.endRow},getLastRenderedColumn:function(){return this.wot.wtViewport.columnsRenderCalculator.endColumn},getLastVisibleColumn:function(){return this.wot.wtViewport.columnsVisibleCalculator.endColumn},isRowBeforeRenderedRows:function(e){return this.rowFilter.sourceToRendered(e)<0&&e>=0},isRowAfterViewport:function(e){return this.rowFilter.sourceToRendered(e)>this.getLastVisibleRow()},isRowAfterRenderedRows:function(e){return this.rowFilter.sourceToRendered(e)>this.getLastRenderedRow()},isColumnBeforeViewport:function(e){return this.columnFilter.sourceToRendered(e)<0&&e>=0},isColumnAfterViewport:function(e){return this.columnFilter.sourceToRendered(e)>this.getLastVisibleColumn()},isLastRowFullyVisible:function(){return this.getLastVisibleRow()===this.getLastRenderedRow()},isLastColumnFullyVisible:function(){return this.getLastVisibleColumn()===this.getLastRenderedColumn()},getRenderedColumnsCount:function(){return WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_DEBUG)?this.wot.getSetting("totalColumns"):WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_LEFT)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_TOP_LEFT_CORNER)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM_LEFT_CORNER)?this.wot.getSetting("fixedColumnsLeft"):this.wot.wtViewport.columnsRenderCalculator.count},getRenderedRowsCount:function(){return WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_DEBUG)?this.wot.getSetting("totalRows"):WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_TOP)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_TOP_LEFT_CORNER)?this.wot.getSetting("fixedRowsTop"):WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM_LEFT_CORNER)?this.instance.getSetting("fixedRowsBottom"):this.wot.wtViewport.rowsRenderCalculator.count},getVisibleRowsCount:function(){return this.wot.wtViewport.rowsVisibleCalculator.count},allRowsInViewport:function(){return this.wot.getSetting("totalRows")==this.getVisibleRowsCount()},getRowHeight:function(e){var t=this.wot.wtSettings.settings.rowHeight(e),n=this.wot.wtViewport.oversizedRows[e];return void 0!==n&&(t=void 0===t?n:Math.max(t,n)),t},getColumnHeaderHeight:function(e){var t=this.wot.wtSettings.settings.defaultRowHeight,n=this.wot.wtViewport.oversizedColumnHeaders[e];return void 0!==n&&(t=t?Math.max(t,n):n),t},getVisibleColumnsCount:function(){return this.wot.wtViewport.columnsVisibleCalculator.count},allColumnsInViewport:function(){return this.wot.getSetting("totalColumns")==this.getVisibleColumnsCount()},getColumnWidth:function(e){var t=this.wot.wtSettings.settings.columnWidth;return"function"==typeof t?t=t(e):"object"==typeof t&&(t=t[e]),t||this.wot.wtSettings.settings.defaultColumnWidth},getStretchedColumnWidth:function(e){var t=this.getColumnWidth(e),n=-1===[void 0,null].indexOf(t)?t:this.instance.wtSettings.settings.defaultColumnWidth,o=this.wot.wtViewport.columnsRenderCalculator;if(o){var r=o.getStretchedColumnWidth(e,n);r&&(n=r)}return n}},{}),window.WalkontableTable=C},{"cell/coords":5,"cell/range":6,"filter/column":9,"filter/row":10,"helpers/dom/element":45,tableRenderer:21}],21:[function(e,t,n){"use strict";function o(e,t){var n=document.createElement("TH");return t.insertBefore(n,e),t.removeChild(e),n}function r(e,t){var n=document.createElement("TD");return t.insertBefore(n,e),t.removeChild(e),n}Object.defineProperties(n,{WalkontableTableRenderer:{get:function(){return h}},__esModule:{value:!0}});var i,s=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}),a=s.addClass,l=s.empty,u=s.getScrollbarWidth,c=s.hasClass,d=s.innerHeight,h=function(e){this.wtTable=e,this.wot=e.instance,this.instance=e.instance,this.rowFilter=e.rowFilter,this.columnFilter=e.columnFilter,this.TABLE=e.TABLE,this.THEAD=e.THEAD,this.TBODY=e.TBODY,this.COLGROUP=e.COLGROUP,this.rowHeaders=[],this.rowHeaderCount=0,this.columnHeaders=[],this.columnHeaderCount=0,this.fixedRowsTop=0,this.fixedRowsBottom=0};$traceurRuntime.createClass(h,{render:function(){this.wtTable.isWorkingOnClone()||this.wot.getSetting("beforeDraw",!0),this.rowHeaders=this.wot.getSetting("rowHeaders"),this.rowHeaderCount=this.rowHeaders.length,this.fixedRowsTop=this.wot.getSetting("fixedRowsTop"),this.fixedRowsBottom=this.wot.getSetting("fixedRowsBottom"),this.columnHeaders=this.wot.getSetting("columnHeaders"),this.columnHeaderCount=this.columnHeaders.length;var e,t=this.wtTable.getRenderedColumnsCount(),n=this.wtTable.getRenderedRowsCount(),o=this.wot.getSetting("totalColumns"),r=this.wot.getSetting("totalRows"),i=!1;if((WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM)||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM_LEFT_CORNER))&&(this.columnHeaders=[],this.columnHeaderCount=0),o>0&&(this.adjustAvailableNodes(),i=!0,this.renderColumnHeaders(),this.renderRows(r,n,t),this.wtTable.isWorkingOnClone()||(e=this.wot.wtViewport.getWorkspaceWidth(),this.wot.wtViewport.containerWidth=null),this.adjustColumnHeaderHeights(),this.adjustColumnWidths(t),this.markOversizedColumns()),i||this.adjustAvailableNodes(),this.removeRedundantRows(n),this.wtTable.isWorkingOnClone()){if(WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM)){var s=this.wot.cloneOverlay.instance;this.wot.cloneOverlay.markOversizedFixedBottomRows(),s.wtOverlays.adjustElementsSize()}}else{if(this.markOversizedRows(),this.wot.wtViewport.createVisibleCalculators(),this.wot.wtOverlays.refresh(!1),this.wot.wtOverlays.applyToDOM(),e!==this.wot.wtViewport.getWorkspaceWidth()){this.wot.wtViewport.containerWidth=null;for(var a=this.wtTable.getFirstRenderedColumn(),l=this.wtTable.getLastRenderedColumn(),u=a;l>u;u++){var c=this.wtTable.getStretchedColumnWidth(u),d=this.columnFilter.sourceToRendered(u);this.COLGROUP.childNodes[d+this.rowHeaderCount].style.width=c+"px"}}this.wot.getSetting("onDraw",!0)}},removeRedundantRows:function(e){for(;this.wtTable.tbodyChildrenLength>e;)this.TBODY.removeChild(this.TBODY.lastChild),this.wtTable.tbodyChildrenLength--},renderRows:function(e,t,n){for(var o,r,i=0,s=this.rowFilter.renderedToSource(i),a=this.wtTable.isWorkingOnClone();e>s&&s>=0&&(i>1e3&&console.error("Security brake: Too much TRs. Please define height for your table, which will enforce scrollbars."),void 0===t||i!==t);){if(r=this.getOrCreateTrForRow(i,r),this.renderRowHeaders(s,r),this.adjustColumns(r,n+this.rowHeaderCount),o=this.renderCells(s,r,n),(!a||WalkontableOverlay.isOverlayTypeOf(this.wot.cloneOverlay,WalkontableOverlay.CLONE_BOTTOM))&&this.resetOversizedRow(s),r.firstChild){var l=this.wot.wtTable.getRowHeight(s);l?(l--,r.firstChild.style.height=l+"px"):r.firstChild.style.height=""}i++,s=this.rowFilter.renderedToSource(i)}},resetOversizedRow:function(e){this.wot.getSetting("externalRowCalculator")||this.wot.wtViewport.oversizedRows&&this.wot.wtViewport.oversizedRows[e]&&(this.wot.wtViewport.oversizedRows[e]=void 0)},markOversizedRows:function(){if(!this.wot.getSetting("externalRowCalculator")){var e,t,n,o,r,i=this.instance.wtTable.TBODY.childNodes.length,s=i*this.instance.wtSettings.settings.defaultRowHeight,a=d(this.instance.wtTable.TBODY)-1;this.instance.getSetting("totalRows");if(s!==a||this.instance.getSetting("fixedRowsBottom"))for(;i;)i--,n=this.instance.wtTable.rowFilter.renderedToSource(i),e=this.instance.wtTable.getRowHeight(n),o=this.instance.wtTable.getTrForRow(n),r=o.querySelector("th"),t=r?d(r):d(o)-1,(!e&&this.instance.wtSettings.settings.defaultRowHeight<t||t>e)&&(this.instance.wtViewport.oversizedRows[n]=++t)}},markOversizedColumns:function(){var e=this.wot.getOverlayName();if(this.columnHeaderCount&&!this.wot.wtViewport.isMarkedOversizedColumn[e]&&!this.wtTable.isWorkingOnClone()){for(var t=this.wtTable.getRenderedColumnsCount(),n=0;n<this.columnHeaderCount;n++)for(var o=-1*this.rowHeaderCount;t>o;o++)this.markIfOversizedColumnHeader(o);this.wot.wtViewport.isMarkedOversizedColumn[e]=!0}},adjustColumnHeaderHeights:function(){for(var e=this.wot.getSetting("columnHeaders"),t=this.wot.wtTable.THEAD.childNodes,n=this.wot.wtViewport.oversizedColumnHeaders,o=0,r=e.length;r>o;o++)if(n[o]){if(0===t[o].childNodes.length)return;t[o].childNodes[0].style.height=n[o]+"px"}},markIfOversizedColumnHeader:function(e){for(var t,n,o,r=this.wot.wtTable.columnFilter.renderedToSource(e),i=this.columnHeaderCount,s=this.wot.wtSettings.settings.defaultRowHeight;i;)i--,t=this.wot.wtTable.getColumnHeaderHeight(i),n=this.wot.wtTable.getColumnHeader(r,i),n&&(o=d(n),(!t&&o>s||o>t)&&(this.wot.wtViewport.oversizedColumnHeaders[i]=o))},renderCells:function(e,t,n){for(var o,i,s=0;n>s;s++)i=this.columnFilter.renderedToSource(s),o=0===s?t.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(i)]:o.nextSibling,"TH"==o.nodeName&&(o=r(o,t)),c(o,"hide")||(o.className=""),o.removeAttribute("style"),this.wot.wtSettings.settings.cellRenderer(e,i,o);return o},adjustColumnWidths:function(e){var t=0,n=this.wot.cloneSource?this.wot.cloneSource:this.wot,o=n.wtTable.holder;o.offsetHeight<o.scrollHeight&&(t=u()),this.wot.wtViewport.columnsRenderCalculator.refreshStretching(this.wot.wtViewport.getViewportWidth()-t);for(var r=0;e>r;r++){var i=this.wtTable.getStretchedColumnWidth(this.columnFilter.renderedToSource(r));this.COLGROUP.childNodes[r+this.rowHeaderCount].style.width=i+"px"}},appendToTbody:function(e){this.TBODY.appendChild(e),this.wtTable.tbodyChildrenLength++},getOrCreateTrForRow:function(e,t){var n;return e>=this.wtTable.tbodyChildrenLength?(n=this.createRow(),this.appendToTbody(n)):n=0===e?this.TBODY.firstChild:t.nextSibling,n.className&&n.removeAttribute("class"),n},createRow:function(){for(var e=document.createElement("TR"),t=0;t<this.rowHeaderCount;t++)e.appendChild(document.createElement("TH"));return e},renderRowHeader:function(e,t,n){n.className="",n.removeAttribute("style"),this.rowHeaders[t](e,n,t)},renderRowHeaders:function(e,t){for(var n=t.firstChild,r=0;r<this.rowHeaderCount;r++)n?"TD"==n.nodeName&&(n=o(n,t)):(n=document.createElement("TH"),t.appendChild(n)),this.renderRowHeader(e,r,n),n=n.nextSibling},adjustAvailableNodes:function(){this.adjustColGroups(),this.adjustThead()},renderColumnHeaders:function(){this.wot.getOverlayName();if(this.columnHeaderCount)for(var e=this.wtTable.getRenderedColumnsCount(),t=0;t<this.columnHeaderCount;t++)for(var n=this.getTrForColumnHeaders(t),o=-1*this.rowHeaderCount;e>o;o++){var r=this.columnFilter.renderedToSource(o);this.renderColumnHeader(t,r,n.childNodes[o+this.rowHeaderCount])}},adjustColGroups:function(){for(var e=this.wtTable.getRenderedColumnsCount();this.wtTable.colgroupChildrenLength<e+this.rowHeaderCount;)this.COLGROUP.appendChild(document.createElement("COL")),this.wtTable.colgroupChildrenLength++;for(;this.wtTable.colgroupChildrenLength>e+this.rowHeaderCount;)this.COLGROUP.removeChild(this.COLGROUP.lastChild),this.wtTable.colgroupChildrenLength--;this.rowHeaderCount&&a(this.COLGROUP.childNodes[0],"rowHeader")},adjustThead:function(){var e=this.wtTable.getRenderedColumnsCount(),t=this.THEAD.firstChild;if(this.columnHeaders.length){for(var n=0,o=this.columnHeaders.length;o>n;n++){for(t=this.THEAD.childNodes[n],t||(t=document.createElement("TR"),this.THEAD.appendChild(t)),this.theadChildrenLength=t.childNodes.length;this.theadChildrenLength<e+this.rowHeaderCount;)t.appendChild(document.createElement("TH")),this.theadChildrenLength++;for(;this.theadChildrenLength>e+this.rowHeaderCount;)t.removeChild(t.lastChild),this.theadChildrenLength--}var r=this.THEAD.childNodes.length;if(r>this.columnHeaders.length)for(var i=this.columnHeaders.length;r>i;i++)this.THEAD.removeChild(this.THEAD.lastChild)}else t&&l(t)},getTrForColumnHeaders:function(e){return this.THEAD.childNodes[e]},renderColumnHeader:function(e,t,n){return n.className="",n.removeAttribute("style"),this.columnHeaders[e](t,n,e)},adjustColumns:function(e,t){for(var n=e.childNodes.length;t>n;){var o=document.createElement("TD");e.appendChild(o),n++}for(;n>t;)e.removeChild(e.lastChild),n--},removeRedundantColumns:function(e){for(;this.wtTable.tbodyChildrenLength>e;)this.TBODY.removeChild(this.TBODY.lastChild),this.wtTable.tbodyChildrenLength--}},{}),window.WalkontableTableRenderer=h},{"helpers/dom/element":45}],22:[function(e,t,n){"use strict";Object.defineProperties(n,{WalkontableViewport:{get:function(){return v}},__esModule:{value:!0}});var o,r,i,s,a=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),l=a.getScrollbarWidth,u=a.getScrollTop,c=a.getStyle,d=a.offset,h=a.outerHeight,f=a.outerWidth,p=(r=e("eventManager"),r&&r.__esModule&&r||{"default":r}).EventManager,g=(i=e("calculator/viewportColumns"),i&&i.__esModule&&i||{"default":i}).WalkontableViewportColumnsCalculator,m=(s=e("calculator/viewportRows"),s&&s.__esModule&&s||{"default":s}).WalkontableViewportRowsCalculator,v=function(e){var t=this;this.wot=e,this.instance=this.wot,this.oversizedRows=[],this.oversizedColumnHeaders=[],this.isMarkedOversizedColumn={},this.clientHeight=0,this.containerWidth=NaN,this.rowHeaderWidth=NaN,this.rowsVisibleCalculator=null,this.columnsVisibleCalculator=null,this.eventManager=new p(this.wot),this.eventManager.addEventListener(window,"resize",function(){t.clientHeight=t.getWorkspaceHeight()})};$traceurRuntime.createClass(v,{getWorkspaceHeight:function(){var e,t=this.instance.wtOverlays.topOverlay.trimmingContainer,n=0;return t===window?n=document.documentElement.clientHeight:(e=h(t),n=e>0&&t.clientHeight>0?t.clientHeight:1/0),n},getWorkspaceWidth:function(){var e,t,n=this.instance.getSetting("totalColumns"),o=this.instance.wtOverlays.leftOverlay.trimmingContainer,r=this.instance.getSetting("stretchH"),i=document.documentElement.offsetWidth;return e=Handsontable.freezeOverlays?Math.min(i-this.getWorkspaceOffset().left,i):Math.min(this.getContainerFillWidth(),i-this.getWorkspaceOffset().left,i),o===window&&n>0&&this.sumColumnWidths(0,n-1)>e?document.documentElement.clientWidth:o!==window&&(t=c(this.instance.wtOverlays.leftOverlay.trimmingContainer,"overflow"),"scroll"==t||"hidden"==t||"auto"==t)?Math.max(e,o.clientWidth):"none"!==r&&r?e:Math.max(e,f(this.instance.wtTable.TABLE))},hasVerticalScroll:function(){return this.getWorkspaceActualHeight()>this.getWorkspaceHeight()},hasHorizontalScroll:function(){return this.getWorkspaceActualWidth()>this.getWorkspaceWidth()},sumColumnWidths:function(e,t){for(var n=0;t>e;)n+=this.wot.wtTable.getColumnWidth(e),e++;return n},getContainerFillWidth:function(){if(this.containerWidth)return this.containerWidth;var e,t,n=this.instance.wtTable.holder;return t=document.createElement("div"),t.style.width="100%",t.style.height="1px",n.appendChild(t),e=t.offsetWidth,this.containerWidth=e,n.removeChild(t),e},getWorkspaceOffset:function(){return d(this.wot.wtTable.TABLE)},getWorkspaceActualHeight:function(){return h(this.wot.wtTable.TABLE)},getWorkspaceActualWidth:function(){return f(this.wot.wtTable.TABLE)||f(this.wot.wtTable.TBODY)||f(this.wot.wtTable.THEAD)},getColumnHeaderHeight:function(){return isNaN(this.columnHeaderHeight)&&(this.columnHeaderHeight=h(this.wot.wtTable.THEAD)),this.columnHeaderHeight},getViewportHeight:function(){var e,t=this.getWorkspaceHeight();return t===1/0?t:(e=this.getColumnHeaderHeight(),e>0&&(t-=e),t)},getRowHeaderWidth:function(){if(this.wot.cloneSource)return this.wot.cloneSource.wtViewport.getRowHeaderWidth();if(isNaN(this.rowHeaderWidth)){var e=this.instance.getSetting("rowHeaders");if(e.length){var t=this.instance.wtTable.TABLE.querySelector("TH");this.rowHeaderWidth=0;for(var n=0,o=e.length;o>n;n++)t?(this.rowHeaderWidth+=f(t),t=t.nextSibling):this.rowHeaderWidth+=50}else this.rowHeaderWidth=0}return this.rowHeaderWidth},getViewportWidth:function(){var e,t=this.getWorkspaceWidth();return t===1/0?t:(e=this.getRowHeaderWidth(),e>0?t-e:t)},createRowsCalculator:function(){var e,t,n,o,r,i,s,a=void 0!==arguments[0]?arguments[0]:!1,c=this;return this.rowHeaderWidth=NaN,e=this.wot.wtSettings.settings.renderAllRows?1/0:this.getViewportHeight(),t=u(this.wot.wtOverlays.mainTableScrollableElement)-this.wot.wtOverlays.topOverlay.getTableParentOffset(),0>t&&(t=0),n=this.wot.getSetting("fixedRowsTop"),r=this.wot.getSetting("fixedRowsBottom"),s=this.wot.getSetting("totalRows"),n&&(i=this.wot.wtOverlays.topOverlay.sumCellSizes(0,n),t+=i,e-=i),r&&this.wot.wtOverlays.bottomOverlay.clone&&(i=this.wot.wtOverlays.bottomOverlay.sumCellSizes(s-r,s),e-=i),o=this.wot.wtTable.holder.clientHeight===this.wot.wtTable.holder.offsetHeight?0:l(),new m(e,t,this.wot.getSetting("totalRows"),function(e){return c.wot.wtTable.getRowHeight(e)},a?null:this.wot.wtSettings.settings.viewportRowCalculatorOverride,a,o)},createColumnsCalculator:function(){var e,t,n=void 0!==arguments[0]?arguments[0]:!1,o=this,r=this.getViewportWidth();if(this.columnHeaderHeight=NaN,e=this.wot.wtOverlays.leftOverlay.getScrollPosition()-this.wot.wtOverlays.leftOverlay.getTableParentOffset(),0>e&&(e=0),t=this.wot.getSetting("fixedColumnsLeft")){var i=this.wot.wtOverlays.leftOverlay.sumCellSizes(0,t);e+=i,r-=i}return this.wot.wtTable.holder.clientWidth!==this.wot.wtTable.holder.offsetWidth&&(r-=l()),new g(r,e,this.wot.getSetting("totalColumns"),function(e){return o.wot.wtTable.getColumnWidth(e)},n?null:this.wot.wtSettings.settings.viewportColumnCalculatorOverride,n,this.wot.getSetting("stretchH"))},createRenderCalculators:function(){var e=void 0!==arguments[0]?arguments[0]:!1;if(e){var t=this.createRowsCalculator(!0),n=this.createColumnsCalculator(!0);this.areAllProposedVisibleRowsAlreadyRendered(t)&&this.areAllProposedVisibleColumnsAlreadyRendered(n)||(e=!1)}return e||(this.rowsRenderCalculator=this.createRowsCalculator(),this.columnsRenderCalculator=this.createColumnsCalculator()),this.rowsVisibleCalculator=null,this.columnsVisibleCalculator=null,e},createVisibleCalculators:function(){this.rowsVisibleCalculator=this.createRowsCalculator(!0),this.columnsVisibleCalculator=this.createColumnsCalculator(!0)},areAllProposedVisibleRowsAlreadyRendered:function(e){return this.rowsVisibleCalculator?e.startRow<this.rowsRenderCalculator.startRow||e.startRow===this.rowsRenderCalculator.startRow&&e.startRow>0?!1:e.endRow>this.rowsRenderCalculator.endRow||e.endRow===this.rowsRenderCalculator.endRow&&e.endRow<this.wot.getSetting("totalRows")-1?!1:!0:!1},areAllProposedVisibleColumnsAlreadyRendered:function(e){return this.columnsVisibleCalculator?e.startColumn<this.columnsRenderCalculator.startColumn||e.startColumn===this.columnsRenderCalculator.startColumn&&e.startColumn>0?!1:e.endColumn>this.columnsRenderCalculator.endColumn||e.endColumn===this.columnsRenderCalculator.endColumn&&e.endColumn<this.wot.getSetting("totalColumns")-1?!1:!0:!1}},{}),window.WalkontableViewport=v},{"calculator/viewportColumns":3,"calculator/viewportRows":4,eventManager:41,"helpers/dom/element":45}],23:[function(e,t,n){"use strict";var o,r,i,s,a,l,u,c,d,h,f,p,g,m,v,w,y,b,C;window.Handsontable=function I(e,t){var n=new I.Core(e,t||{});return n.init(),n},o=e("shims/classes"),o&&o.__esModule&&o||{"default":o},r=e("es6collections"),r&&r.__esModule&&r||{"default":r};var R=(i=e("pluginHooks"),i&&i.__esModule&&i||{"default":i}).Hooks;Handsontable.hooks||(Handsontable.hooks=new R),s=e("core"),s&&s.__esModule&&s||{"default":s},a=e("renderers/_cellDecorator"),a&&a.__esModule&&a||{"default":a},l=e("cellTypes"),l&&l.__esModule&&l||{"default":l},u=e("plugins/jqueryHandsontable"),u&&u.__esModule&&u||{"default":u};var _=(c=e("helpers/array"),c&&c.__esModule&&c||{"default":c}),S=(d=e("helpers/browser"),d&&d.__esModule&&d||{"default":d}),E=(h=e("helpers/data"),h&&h.__esModule&&h||{"default":h}),T=(f=e("helpers/function"),f&&f.__esModule&&f||{"default":f}),O=(p=e("helpers/mixed"),p&&p.__esModule&&p||{"default":p}),M=(g=e("helpers/number"),g&&g.__esModule&&g||{"default":g}),k=(m=e("helpers/object"),m&&m.__esModule&&m||{"default":m}),H=(v=e("helpers/setting"),v&&v.__esModule&&v||{"default":v}),D=(w=e("helpers/string"),w&&w.__esModule&&w||{"default":w}),x=(y=e("helpers/unicode"),y&&y.__esModule&&y||{"default":y}),A=(b=e("helpers/dom/element"),b&&b.__esModule&&b||{"default":b}),P=(C=e("helpers/dom/event"),C&&C.__esModule&&C||{"default":C}),N=[_,S,E,T,O,M,k,H,D,x],L=[A,P];Handsontable.buildDate="Thu Nov 19 2015 10:51:56 GMT+0100 (CET)",Handsontable.packageName="handsontable",Handsontable.version="0.20.1";var W="@@baseVersion";/^@@/.test(W)||(Handsontable.baseVersion=W),Handsontable.plugins={},Handsontable.helper={},Handsontable.dom={},Handsontable.Dom=Handsontable.dom,_.arrayEach(N,function(e){_.arrayEach(Object.getOwnPropertyNames(e),function(t){"_"!==t.charAt(0)&&(Handsontable.helper[t]=e[t])})}),_.arrayEach(L,function(e){_.arrayEach(Object.getOwnPropertyNames(e),function(t){"_"!==t.charAt(0)&&(Handsontable.dom[t]=e[t])})})},{cellTypes:24,core:25,es6collections:"es6collections","helpers/array":42,"helpers/browser":43,"helpers/data":44,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/function":47,"helpers/mixed":48,"helpers/number":49,"helpers/object":50,"helpers/setting":51,"helpers/string":52,"helpers/unicode":53,pluginHooks:56,"plugins/jqueryHandsontable":1,"renderers/_cellDecorator":89,"shims/classes":96}],24:[function(e,t,n){"use strict";var o,r,i,s,a,l,u,c,d,h,f,p,g,m,v,w,y,b,C,R,_,S,E=(o=e("helpers/browser"),o&&o.__esModule&&o||{"default":o}).isMobileBrowser,T=(r=e("editors"),r&&r.__esModule&&r||{"default":r}).getEditorConstructor,O=(i=e("renderers"),i&&i.__esModule&&i||{"default":i}).getRenderer;(s=e("editors/autocompleteEditor"),s&&s.__esModule&&s||{"default":s}).AutocompleteEditor,(a=e("editors/checkboxEditor"),a&&a.__esModule&&a||{"default":a}).CheckboxEditor,(l=e("editors/dateEditor"),l&&l.__esModule&&l||{"default":l}).DateEditor,(u=e("editors/dropdownEditor"),u&&u.__esModule&&u||{"default":u}).DropdownEditor,(c=e("editors/handsontableEditor"),c&&c.__esModule&&c||{"default":c}).HandsontableEditor,(d=e("editors/mobileTextEditor"),d&&d.__esModule&&d||{"default":d}).MobileTextEditor,(h=e("editors/numericEditor"),h&&h.__esModule&&h||{"default":h}).NumericEditor,(f=e("editors/passwordEditor"),f&&f.__esModule&&f||{"default":f}).PasswordEditor,(p=e("editors/selectEditor"),p&&p.__esModule&&p||{"default":p}).SelectEditor,(g=e("editors/textEditor"),g&&g.__esModule&&g||{"default":g}).TextEditor,(m=e("renderers/autocompleteRenderer"),m&&m.__esModule&&m||{"default":m}).AutocompleteRenderer,(v=e("renderers/checkboxRenderer"),v&&v.__esModule&&v||{"default":v}).CheckboxRenderer,(w=e("renderers/htmlRenderer"),w&&w.__esModule&&w||{"default":w}).HtmlRenderer,(y=e("renderers/numericRenderer"),y&&y.__esModule&&y||{"default":y}).NumericRenderer,(b=e("renderers/passwordRenderer"),b&&b.__esModule&&b||{"default":b}).PasswordRenderer,(C=e("renderers/textRenderer"),C&&C.__esModule&&C||{"default":C}).TextRenderer,(R=e("validators/autocompleteValidator"),R&&R.__esModule&&R||{"default":R}).AutocompleteValidator,(_=e("validators/dateValidator"),_&&_.__esModule&&_||{"default":_}).DateValidator,(S=e("validators/numericValidator"),S&&S.__esModule&&S||{"default":S}).NumericValidator;Handsontable.mobileBrowser=E(),Handsontable.AutocompleteCell={editor:T("autocomplete"),renderer:O("autocomplete"),validator:Handsontable.AutocompleteValidator},Handsontable.CheckboxCell={editor:T("checkbox"),renderer:O("checkbox")},Handsontable.TextCell={editor:T(Handsontable.mobileBrowser?"mobile":"text"),renderer:O("text")},Handsontable.NumericCell={editor:T("numeric"),renderer:O("numeric"),validator:Handsontable.NumericValidator,dataType:"number"},Handsontable.DateCell={editor:T("date"),validator:Handsontable.DateValidator,renderer:O("autocomplete")},Handsontable.HandsontableCell={editor:T("handsontable"),renderer:O("autocomplete")},Handsontable.PasswordCell={editor:T("password"),renderer:O("password"),copyable:!1},Handsontable.DropdownCell={editor:T("dropdown"),renderer:O("autocomplete"),validator:Handsontable.AutocompleteValidator},Handsontable.cellTypes={text:Handsontable.TextCell,date:Handsontable.DateCell,numeric:Handsontable.NumericCell,checkbox:Handsontable.CheckboxCell,autocomplete:Handsontable.AutocompleteCell,handsontable:Handsontable.HandsontableCell,password:Handsontable.PasswordCell,dropdown:Handsontable.DropdownCell},Handsontable.cellLookup={validator:{numeric:Handsontable.NumericValidator,autocomplete:Handsontable.AutocompleteValidator}}},{editors:29,"editors/autocompleteEditor":31,"editors/checkboxEditor":32,"editors/dateEditor":33,"editors/dropdownEditor":34,"editors/handsontableEditor":35,"editors/mobileTextEditor":36,"editors/numericEditor":37,"editors/passwordEditor":38,"editors/selectEditor":39,"editors/textEditor":40,"helpers/browser":43,renderers:88,"renderers/autocompleteRenderer":90,"renderers/checkboxRenderer":91,"renderers/htmlRenderer":92,"renderers/numericRenderer":93,"renderers/passwordRenderer":94,"renderers/textRenderer":95,"validators/autocompleteValidator":100,"validators/dateValidator":101,"validators/numericValidator":102}],25:[function(e,t,n){"use strict";var o,r,i,s,a,l,u,c,d,h,f,p,g,m,v,w,y,b,C,R=(o=e("numeral"),o&&o.__esModule&&o||{"default":o})["default"],_=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),S=_.addClass,E=_.empty,T=_.isChildOfWebComponentTable,O=_.removeClass,M=(i=e("helpers/setting"),i&&i.__esModule&&i||{"default":i}).columnFactory,k=(s=e("dataMap"),s&&s.__esModule&&s||{"default":s}).DataMap,H=(a=e("editorManager"),a&&a.__esModule&&a||{"default":a}).EditorManager,D=(l=e("eventManager"),l&&l.__esModule&&l||{"default":l}).eventManager,x=(u=e("helpers/object"),u&&u.__esModule&&u||{"default":u}),A=x.extend,P=x.duckSchema,N=x.isObjectEquals,L=x.deepClone,W=(c=e("helpers/array"),c&&c.__esModule&&c||{"default":c}).arrayFlatten,I=(d=e("plugins"),d&&d.__esModule&&d||{"default":d}).getPlugin,j=(h=e("renderers"),h&&h.__esModule&&h||{"default":h}).getRenderer,B=(f=e("helpers/string"),f&&f.__esModule&&f||{"default":f}).randomString,F=(p=e("helpers/number"),p&&p.__esModule&&p||{"default":p}).rangeEach,V=(g=e("tableView"),g&&g.__esModule&&g||{"default":g}).TableView,z=(m=e("dataSource"),m&&m.__esModule&&m||{"default":m}).DataSource,Y=(v=e("helpers/data"),v&&v.__esModule&&v||{"default":v}),U=Y.translateRowsToColumns,G=Y.cellMethodLookupFactory,$=Y.spreadsheetColumnLabel,K=(w=e("3rdparty/walkontable/src/cell/coords"),w&&w.__esModule&&w||{"default":w}).WalkontableCellCoords,X=(y=e("3rdparty/walkontable/src/cell/range"),y&&y.__esModule&&y||{"default":y}).WalkontableCellRange,q=((b=e("3rdparty/walkontable/src/selection"),b&&b.__esModule&&b||{"default":b}).WalkontableSelection,(C=e("3rdparty/walkontable/src/calculator/viewportColumns"),C&&C.__esModule&&C||{"default":C}).WalkontableViewportColumnsCalculator);Handsontable.activeGuid=null,Handsontable.Core=function(e,t){function n(){var e=!1;return{validatorsInQueue:0,valid:!0,addValidatorToQueue:function(){this.validatorsInQueue++,e=!1},removeValidatorFormQueue:function(){this.validatorsInQueue=this.validatorsInQueue-1<0?0:this.validatorsInQueue-1,this.checkIfQueueIsEmpty()},onQueueEmpty:function(e){},checkIfQueueIsEmpty:function(){0==this.validatorsInQueue&&0==e&&(e=!0,this.onQueueEmpty(this.valid))}}}function o(e,t,o){function r(){var n;e.length&&(n=Handsontable.hooks.run(m,"beforeChange",e,t),"function"==typeof n?console.warn("Your beforeChange callback returns a function. It's not supported since Handsontable 0.12.1 (and the returned function will not be executed)."):n===!1&&e.splice(0,e.length)),o()}var i=new n;i.onQueueEmpty=r;for(var s=e.length-1;s>=0;s--)if(null===e[s])e.splice(s,1);else{var a=e[s][0],l=d.propToCol(e[s][1]),u=m.runHooks("modifyCol",l),c=m.getCellMeta(a,u);if("numeric"===c.type&&"string"==typeof e[s][3]&&e[s][3].length>0&&(/^-?[\d\s]*(\.|\,)?\d*$/.test(e[s][3])||c.format)){var h=e[s][3].length;"undefined"==typeof c.language?R.language("en"):e[s][3].indexOf(".")===h-3&&-1===e[s][3].indexOf(",")?R.language("en"):R.language(c.language),R.validate(e[s][3])&&(e[s][3]=R().unformat(e[s][3]))}m.getCellValidator(c)&&(i.addValidatorToQueue(),m.validateCell(e[s][3],c,function(t,n){return function(o){if("boolean"!=typeof o)throw new Error("Validation error: result is not boolean");o===!1&&n.allowInvalid===!1&&(e.splice(t,1),n.valid=!0,--t),i.removeValidatorFormQueue()}}(s,c),t))}i.checkIfQueueIsEmpty()}function r(e,t){var n=e.length-1;if(!(0>n)){for(;n>=0;n--)if(null!==e[n]){if(null!=e[n][2]||null!=e[n][3]){if(c.settings.allowInsertRow)for(;e[n][0]>m.countRows()-1;)d.createRow();if("array"===m.dataType&&c.settings.allowInsertColumn)for(;d.propToCol(e[n][1])>m.countCols()-1;)d.createCol();d.set(e[n][0],e[n][1],e[n][3])}}else e.splice(n,1);m.forceFullRender=!0,f.adjustRowsAndCols(),Handsontable.hooks.run(m,"beforeChangeRender",e,t),
p.refreshBorders(null,!0),m.view.wt.wtOverlays.adjustElementsSize(),Handsontable.hooks.run(m,"afterChange",e,t||"edit")}}function i(e,t,n){return"object"==typeof e?e:[[e,t,n]]}function s(e){if(e.hasOwnProperty("type")){var t,n={};if("object"==typeof e.type)t=e.type;else if("string"==typeof e.type&&(t=Handsontable.cellTypes[e.type],void 0===t))throw new Error('You declared cell type "'+e.type+'" as a string that is not mapped to a known object. Cell type must be an object or a string mapped to an object in Handsontable.cellTypes');for(var o in t)t.hasOwnProperty(o)&&!e.hasOwnProperty(o)&&(n[o]=t[o]);return n}}function a(e){return Handsontable.hooks.run(m,"modifyRow",e)}function l(e){return Handsontable.hooks.run(m,"modifyCol",e)}function u(){throw new Error("This method cannot be called because this Handsontable instance has been destroyed")}var c,d,h,f,p,g,m=this,v=function(){},w=D(m);A(v.prototype,Z.prototype),A(v.prototype,t),A(v.prototype,s(t)),this.rootElement=e,this.isHotTableEnv=T(this.rootElement),Handsontable.eventManager.isHotTableEnv=this.isHotTableEnv,this.container=document.createElement("DIV"),this.renderCall=!1,e.insertBefore(this.container,e.firstChild),this.guid="ht_"+B(),this.rootElement.id&&"ht_"!==this.rootElement.id.substring(0,3)||(this.rootElement.id=this.guid),c={cellSettings:[],columnSettings:[],columnsSettingConflicts:["data","width"],settings:new v,selRange:null,isPopulated:null,scrollable:null,firstRun:!0},f={alter:function(e,t,n,o,r){var i;switch(n=n||1,e){case"insert_row":if(m.getSettings().maxRows===m.countSourceRows())return;i=d.createRow(t,n),i&&(p.isSelected()&&c.selRange.from.row>=t?(c.selRange.from.row=c.selRange.from.row+i,p.transformEnd(i,0)):p.refreshBorders());break;case"insert_col":if(i=d.createCol(t,n)){if(Array.isArray(m.getSettings().colHeaders)){var s=[t,0];s.length+=i,Array.prototype.splice.apply(m.getSettings().colHeaders,s)}p.isSelected()&&c.selRange.from.col>=t?(c.selRange.from.col=c.selRange.from.col+i,p.transformEnd(0,i)):p.refreshBorders()}break;case"remove_row":d.removeRow(t,n),c.cellSettings.splice(t,n);var a=m.countRows(),l=m.getSettings().fixedRowsTop;l>=t+1&&(m.getSettings().fixedRowsTop-=Math.min(n,l-t));var u=m.getSettings().fixedRowsBottom;u&&t+1>=a-u&&(m.getSettings().fixedRowsBottom-=Math.min(n,u-t)),f.adjustRowsAndCols(),p.refreshBorders();break;case"remove_col":d.removeCol(t,n);for(var h=0,g=d.getAll().length;g>h;h++)h in c.cellSettings&&c.cellSettings[h].splice(t,n);var v=m.getSettings().fixedColumnsLeft;v>=t+1&&(m.getSettings().fixedColumnsLeft-=Math.min(n,v-t)),Array.isArray(m.getSettings().colHeaders)&&("undefined"==typeof t&&(t=-1),m.getSettings().colHeaders.splice(t,n)),f.adjustRowsAndCols(),p.refreshBorders();break;default:throw new Error('There is no such action "'+e+'"')}r||f.adjustRowsAndCols()},adjustRowsAndCols:function(){if(c.settings.minRows){var e=m.countRows();if(e<c.settings.minRows)for(var t=0,n=c.settings.minRows;n-e>t;t++)d.createRow(m.countRows(),1,!0)}if(c.settings.minSpareRows){var o=m.countEmptyRows(!0);if(o<c.settings.minSpareRows)for(;o<c.settings.minSpareRows&&m.countRows()<c.settings.maxRows;o++)d.createRow(m.countRows(),1,!0)}var r;if((c.settings.minCols||c.settings.minSpareCols)&&(r=m.countEmptyCols(!0)),c.settings.minCols&&!c.settings.columns&&m.countCols()<c.settings.minCols)for(;m.countCols()<c.settings.minCols;r++)d.createCol(m.countCols(),1,!0);if(c.settings.minSpareCols&&!c.settings.columns&&"array"===m.dataType&&r<c.settings.minSpareCols)for(;r<c.settings.minSpareCols&&m.countCols()<c.settings.maxCols;r++)d.createCol(m.countCols(),1,!0);var i=m.countRows(),s=m.countCols();if((0===i||0===s)&&p.deselect(),p.isSelected()){var a=!1,l=c.selRange.from.row,u=c.selRange.from.col,h=c.selRange.to.row,f=c.selRange.to.col;l>i-1?(l=i-1,a=!0,h>l&&(h=l)):h>i-1&&(h=i-1,a=!0,l>h&&(l=h)),u>s-1?(u=s-1,a=!0,f>u&&(f=u)):f>s-1&&(f=s-1,a=!0,u>f&&(u=f)),a&&m.selectCell(l,u,h,f)}m.view&&m.view.wt.wtOverlays.adjustElementsSize()},populateFromArray:function(e,t,n,o,r,i,s){var a,l,u,d,h=[],f={};if(l=t.length,0===l)return!1;var p,g,v,w;({row:null===n?null:n.row,col:null===n?null:n.col});switch(r){case"shift_down":for(p=n?n.col-e.col+1:0,g=n?n.row-e.row+1:0,t=U(t),u=0,d=t.length,v=Math.max(d,p);v>u;u++)if(d>u){for(a=0,l=t[u].length;g-l>a;a++)t[u].push(t[u][a%l]);t[u].unshift(e.col+u,e.row,0),m.spliceCol.apply(m,t[u])}else t[u%d][0]=e.col+u,m.spliceCol.apply(m,t[u%d]);break;case"shift_right":for(p=n?n.col-e.col+1:0,g=n?n.row-e.row+1:0,a=0,l=t.length,w=Math.max(l,g);w>a;a++)if(l>a){for(u=0,d=t[a].length;p-d>u;u++)t[a].push(t[a][u%d]);t[a].unshift(e.row+a,e.col,0),m.spliceRow.apply(m,t[a])}else t[a%l][0]=e.row+a,m.spliceRow.apply(m,t[a%l]);break;case"overwrite":default:f.row=e.row,f.col=e.col;var y,b={row:n&&e?n.row-e.row+1:1,col:n&&e?n.col-e.col+1:1},C=0,R=0,_=!0,S=function(e){var n=void 0!==arguments[1]?arguments[1]:null,o=t[e%t.length];return null!==n?o[n%o.length]:o},E=t.length,T=n?n.row-e.row+1:0;for(l=n?T:Math.max(E,T),a=0;l>a&&!(n&&f.row>n.row&&T>E||!c.settings.allowInsertRow&&f.row>m.countRows()-1||f.row>=c.settings.maxRows);a++){var O=a-C,M=S(O).length,k=n?n.col-e.col+1:0;if(d=n?k:Math.max(M,k),f.col=e.col,y=m.getCellMeta(f.row,f.col),"paste"!==o&&"autofill"!==o||!y.skipRowOnPaste){for(R=0,u=0;d>u&&!(n&&f.col>n.col&&k>M||!c.settings.allowInsertColumn&&f.col>m.countCols()-1||f.col>=c.settings.maxCols);u++)if(y=m.getCellMeta(f.row,f.col),"paste"!==o&&"autofill"!==o||!y.skipColumnOnPaste)if(y.readOnly)f.col++;else{var H=u-R,D=S(O,H),x=m.getDataAtCell(f.row,f.col),A={row:O,col:H};if("autofill"===o){var W=m.runHooks("beforeAutofillInsidePopulate",A,i,t,s,{},b);W&&(D="undefined"==typeof W.value?D:W.value)}if(null!==D&&"object"==typeof D)if(null===x||"object"!=typeof x)_=!1;else{var I=P(x[0]||x),j=P(D[0]||D);N(I,j)?D=L(D):_=!1}else null!==x&&"object"==typeof x&&(_=!1);_&&h.push([f.row,f.col,D]),_=!0,f.col++}else R++,f.col++,d++;f.row++}else C++,f.row++,l++}m.setDataAtCell(h,null,null,o||"populateFromArray")}}},this.selection=p={inProgress:!1,selectedHeader:{cols:!1,rows:!1},setSelectedHeaders:function(e,t){m.selection.selectedHeader.rows=e,m.selection.selectedHeader.cols=t},begin:function(){m.selection.inProgress=!0},finish:function(){var e=m.getSelected();Handsontable.hooks.run(m,"afterSelectionEnd",e[0],e[1],e[2],e[3]),Handsontable.hooks.run(m,"afterSelectionEndByProp",e[0],m.colToProp(e[1]),e[2],m.colToProp(e[3])),m.selection.inProgress=!1},isInProgress:function(){return m.selection.inProgress},setRangeStart:function(e,t){Handsontable.hooks.run(m,"beforeSetRangeStart",e),c.selRange=new X(e,e,e),p.setRangeEnd(e,null,t)},setRangeEnd:function(e,t,n){if(null!==c.selRange){var o,r=!1,i=!0,s=m.view.wt.wtTable.getFirstVisibleRow(),a=m.view.wt.wtTable.getFirstVisibleColumn(),l={row:null,col:null};Handsontable.hooks.run(m,"beforeSetRangeEnd",e),m.selection.begin(),l.row=e.row<0?s:e.row,l.col=e.col<0?a:e.col,c.selRange.to=new K(l.row,l.col),c.settings.multiSelect||(c.selRange.from=e),m.view.wt.selections.current.clear(),o=m.getCellMeta(c.selRange.highlight.row,c.selRange.highlight.col).disableVisualSelection,"string"==typeof o&&(o=[o]),(o===!1||Array.isArray(o)&&-1===o.indexOf("current"))&&m.view.wt.selections.current.add(c.selRange.highlight),m.view.wt.selections.area.clear(),(o===!1||Array.isArray(o)&&-1===o.indexOf("area"))&&p.isMultiple()&&(m.view.wt.selections.area.add(c.selRange.from),m.view.wt.selections.area.add(c.selRange.to)),(c.settings.currentRowClassName||c.settings.currentColClassName)&&(m.view.wt.selections.highlight.clear(),m.view.wt.selections.highlight.add(c.selRange.from),m.view.wt.selections.highlight.add(c.selRange.to)),Handsontable.hooks.run(m,"afterSelection",c.selRange.from.row,c.selRange.from.col,c.selRange.to.row,c.selRange.to.col),Handsontable.hooks.run(m,"afterSelectionByProp",c.selRange.from.row,d.colToProp(c.selRange.from.col),c.selRange.to.row,d.colToProp(c.selRange.to.col)),(0===c.selRange.from.row&&c.selRange.to.row===m.countRows()-1&&m.countRows()>1||0===c.selRange.from.col&&c.selRange.to.col===m.countCols()-1&&m.countCols()>1)&&(r=!0),(e.row<0||e.col<0)&&(i=!1),t!==!1&&!r&&i&&(c.selRange.from&&!p.isMultiple()?m.view.scrollViewport(c.selRange.from):m.view.scrollViewport(e)),p.refreshBorders(null,n)}},refreshBorders:function(e,t){t||g.destroyEditor(e),m.view.render(),p.isSelected()&&!t&&g.prepareEditor()},isMultiple:function(){var e=!(c.selRange.to.col===c.selRange.from.col&&c.selRange.to.row===c.selRange.from.row),t=Handsontable.hooks.run(m,"afterIsMultipleSelection",e);return e?t:void 0},transformStart:function(e,t,n,o){var r,i,s,a,l=new K(e,t),u=0,d=0;m.runHooks("modifyTransformStart",l),r=m.countRows(),i=m.countCols(),a=m.getSettings().fixedRowsBottom,c.selRange.highlight.row+e>r-1?n&&c.settings.minSpareRows>0&&!(a&&c.selRange.highlight.row>=r-a-1)?(m.alter("insert_row",r),r=m.countRows()):c.settings.autoWrapCol&&(l.row=1-r,l.col=c.selRange.highlight.col+l.col==i-1?1-i:1):c.settings.autoWrapCol&&c.selRange.highlight.row+l.row<0&&c.selRange.highlight.col+l.col>=0&&(l.row=r-1,l.col=c.selRange.highlight.col+l.col==0?i-1:-1),c.selRange.highlight.col+l.col>i-1?n&&c.settings.minSpareCols>0?(m.alter("insert_col",i),i=m.countCols()):c.settings.autoWrapRow&&(l.row=c.selRange.highlight.row+l.row==r-1?1-r:1,l.col=1-i):c.settings.autoWrapRow&&c.selRange.highlight.col+l.col<0&&c.selRange.highlight.row+l.row>=0&&(l.row=c.selRange.highlight.row+l.row==0?r-1:-1,l.col=i-1),s=new K(c.selRange.highlight.row+l.row,c.selRange.highlight.col+l.col),s.row<0?(u=-1,s.row=0):s.row>0&&s.row>=r&&(u=1,s.row=r-1),s.col<0?(d=-1,s.col=0):s.col>0&&s.col>=i&&(d=1,s.col=i-1),m.runHooks("afterModifyTransformStart",s,u,d),p.setRangeStart(s,o)},transformEnd:function(e,t){var n,o,r,i=new K(e,t),s=0,a=0;m.runHooks("modifyTransformEnd",i),n=m.countRows(),o=m.countCols(),r=new K(c.selRange.to.row+i.row,c.selRange.to.col+i.col),r.row<0?(s=-1,r.row=0):r.row>0&&r.row>=n&&(s=1,r.row=n-1),r.col<0?(a=-1,r.col=0):r.col>0&&r.col>=o&&(a=1,r.col=o-1),m.runHooks("afterModifyTransformEnd",r,s,a),p.setRangeEnd(r,!0)},isSelected:function(){return null!==c.selRange},inInSelection:function(e){return p.isSelected()?c.selRange.includes(e):!1},deselect:function(){p.isSelected()&&(m.selection.inProgress=!1,c.selRange=null,m.view.wt.selections.current.clear(),m.view.wt.selections.area.clear(),(c.settings.currentRowClassName||c.settings.currentColClassName)&&m.view.wt.selections.highlight.clear(),g.destroyEditor(),p.refreshBorders(),Handsontable.hooks.run(m,"afterDeselect"))},selectAll:function(){c.settings.multiSelect&&(p.setRangeStart(new K(0,0)),p.setRangeEnd(new K(m.countRows()-1,m.countCols()-1),!1))},empty:function(){if(p.isSelected()){var e,t,n=c.selRange.getTopLeftCorner(),o=c.selRange.getBottomRightCorner(),r=[];for(e=n.row;e<=o.row;e++)for(t=n.col;t<=o.col;t++)m.getCellMeta(e,t).readOnly||r.push([e,t,""]);m.setDataAtCell(r)}}},this.init=function(){h=new z(m,c.settings.data),Handsontable.hooks.run(m,"beforeInit"),Handsontable.mobileBrowser&&S(m.rootElement,"mobile"),this.updateSettings(c.settings,!0),this.view=new V(this),g=new H(m,c,p,d),this.forceFullRender=!0,Handsontable.hooks.run(m,"init"),this.view.render(),"object"==typeof c.firstRun&&(Handsontable.hooks.run(m,"afterChange",c.firstRun[0],c.firstRun[1]),c.firstRun=!1),Handsontable.hooks.run(m,"afterInit")},this.validateCell=function(e,t,n,o){function r(e){var o=t.physicalCol,r=t.physicalRow,i=m.getCell(r,o,!0);i&&m.view.wt.wtSettings.settings.cellRenderer(r,o,i),n(e)}var i=m.getCellValidator(t);"[object RegExp]"===Object.prototype.toString.call(i)&&(i=function(e){return function(t,n){n(e.test(t))}}(i)),"function"==typeof i?(e=Handsontable.hooks.run(m,"beforeValidate",e,t.row,t.prop,o),m._registerTimeout(setTimeout(function(){i.call(t,e,function(n){n=Handsontable.hooks.run(m,"afterValidate",n,e,t.row,t.prop,o),t.valid=n,r(n),Handsontable.hooks.run(m,"postAfterValidate",n,e,t.row,t.prop,o)})},0))):(t.valid=!0,r(t.valid))},this.setDataAtCell=function(e,t,n,s){var a,l,u,c=i(e,t,n),h=[];for(a=0,l=c.length;l>a;a++){if("object"!=typeof c[a])throw new Error("Method `setDataAtCell` accepts row number or changes array of arrays as its first parameter");if("number"!=typeof c[a][1])throw new Error("Method `setDataAtCell` accepts row and column number as its parameters. If you want to use object property name, use method `setDataAtRowProp`");u=d.colToProp(c[a][1]),h.push([c[a][0],u,d.get(c[a][0],u),c[a][2]])}s||"object"!=typeof e||(s=t),o(h,s,function(){r(h,s)})},this.setDataAtRowProp=function(e,t,n,s){var a,l,u=i(e,t,n),c=[];for(a=0,l=u.length;l>a;a++)c.push([u[a][0],u[a][1],d.get(u[a][0],u[a][1]),u[a][2]]);s||"object"!=typeof e||(s=t),o(c,s,function(){r(c,s)})},this.listen=function(){Handsontable.activeGuid=m.guid},this.unlisten=function(){Handsontable.activeGuid=null},this.isListening=function(){return Handsontable.activeGuid===m.guid},this.destroyEditor=function(e){p.refreshBorders(e)},this.populateFromArray=function(e,t,n,o,r,i,s,a,l){var u;if("object"!=typeof n||"object"!=typeof n[0])throw new Error("populateFromArray parameter `input` must be an array of arrays");return u="number"==typeof o?new K(o,r):null,f.populateFromArray(new K(e,t),n,u,i,s,a,l)},this.spliceCol=function(e,t,n){return d.spliceCol.apply(d,arguments)},this.spliceRow=function(e,t,n){return d.spliceRow.apply(d,arguments)},this.getSelected=function(){return p.isSelected()?[c.selRange.from.row,c.selRange.from.col,c.selRange.to.row,c.selRange.to.col]:void 0},this.getSelectedRange=function(){return p.isSelected()?c.selRange:void 0},this.render=function(){m.view&&(m.renderCall=!0,m.forceFullRender=!0,p.refreshBorders(null,!0))},this.loadData=function(e){function t(){c.cellSettings.length=0}if("object"==typeof e&&null!==e)e.push&&e.splice||(e=[e]);else{if(null!==e)throw new Error("loadData only accepts array of objects or array of arrays ("+typeof e+" given)");e=[];for(var n,o=0,r=c.settings.startRows;r>o;o++){n=[];for(var i=0,s=c.settings.startCols;s>i;i++)n.push(null);e.push(n)}}c.isPopulated=!1,v.prototype.data=e,Array.isArray(c.settings.dataSchema)||Array.isArray(e[0])?m.dataType="array":"function"==typeof c.settings.dataSchema?m.dataType="function":m.dataType="object",d=new k(m,c,v),h.data=e,h.dataType=m.dataType,h.colToProp=d.colToProp.bind(d),h.propToCol=d.propToCol.bind(d),t(),f.adjustRowsAndCols(),Handsontable.hooks.run(m,"afterLoadData",c.firstRun),c.firstRun?c.firstRun=[null,"loadData"]:(Handsontable.hooks.run(m,"afterChange",null,"loadData"),m.render()),c.isPopulated=!0},this.getData=function(e,t,n,o){return"undefined"==typeof e?d.getAll():d.getRange(new K(e,t),new K(n,o),d.DESTINATION_RENDERER)},this.getCopyableText=function(e,t,n,o){return d.getCopyableText(new K(e,t),new K(n,o))},this.getCopyableData=function(e,t){return d.getCopyable(e,d.colToProp(t))},this.getSchema=function(){return d.getSchema()},this.updateSettings=function(e,t){var n,o;if("undefined"!=typeof e.rows)throw new Error('"rows" setting is no longer supported. do you mean startRows, minRows or maxRows?');if("undefined"!=typeof e.cols)throw new Error('"cols" setting is no longer supported. do you mean startCols, minCols or maxCols?');for(n in e)"data"!==n&&(Handsontable.hooks.getRegistered().indexOf(n)>-1?("function"==typeof e[n]||Array.isArray(e[n]))&&m.addHook(n,e[n]):!t&&e.hasOwnProperty(n)&&(v.prototype[n]=e[n]));if(void 0===e.data&&void 0===c.settings.data?m.loadData(null):void 0!==e.data?m.loadData(e.data):void 0!==e.columns&&d.createMap(),o=m.countCols(),c.cellSettings.length=0,o>0){var r,i;for(n=0;o>n;n++)c.columnSettings[n]=M(v,c.columnsSettingConflicts),r=c.columnSettings[n].prototype,v.prototype.columns&&(i=v.prototype.columns[n],A(r,i),A(r,s(i)))}if("undefined"!=typeof e.cell)for(n in e.cell)if(e.cell.hasOwnProperty(n)){var a=e.cell[n];m.setCellMetaObject(a.row,a.col,a)}if(Handsontable.hooks.run(m,"afterCellMetaReset"),"undefined"!=typeof e.className&&(v.prototype.className&&O(m.rootElement,v.prototype.className),e.className&&S(m.rootElement,e.className)),"undefined"!=typeof e.height){var l=e.height;"function"==typeof l&&(l=l()),m.rootElement.style.height=l+"px"}if("undefined"!=typeof e.width){var u=e.width;"function"==typeof u&&(u=u()),m.rootElement.style.width=u+"px"}l&&(m.rootElement.style.overflow="hidden"),t||Handsontable.hooks.run(m,"afterUpdateSettings"),f.adjustRowsAndCols(),m.view&&!c.firstRun&&(m.forceFullRender=!0,p.refreshBorders(null,!0))},this.getValue=function(){var e=m.getSelected();if(v.prototype.getValue){if("function"==typeof v.prototype.getValue)return v.prototype.getValue.call(m);if(e)return m.getData()[e[0]][v.prototype.getValue]}else if(e)return m.getDataAtCell(e[0],e[1])},this.getSettings=function(){return c.settings},this.clear=function(){p.selectAll(),p.empty()},this.alter=function(e,t,n,o,r){f.alter(e,t,n,o,r)},this.getCell=function(e,t,n){return m.view.getCellAtCoords(new K(e,t),n)},this.getCoords=function(e){return this.view.wt.wtTable.getCoords.call(this.view.wt.wtTable,e)},this.colToProp=function(e){return d.colToProp(e)},this.propToCol=function(e){return d.propToCol(e)},this.getDataAtCell=function(e,t){return d.get(e,d.colToProp(t))},this.getDataAtRowProp=function(e,t){return d.get(e,t)},this.getDataAtCol=function(e){var t=[];return t.concat.apply(t,d.getRange(new K(0,e),new K(c.settings.data.length-1,e),d.DESTINATION_RENDERER))},this.getDataAtProp=function(e){var t,n=[];return t=d.getRange(new K(0,d.propToCol(e)),new K(c.settings.data.length-1,d.propToCol(e)),d.DESTINATION_RENDERER),n.concat.apply(n,t)},this.getSourceData=function(e,t,n,o){var r;return r=void 0===e?h.getData():h.getByRange(new K(e,t),new K(n,o))},this.getSourceDataAtCol=function(e){return h.getAtColumn(e)},this.getSourceDataAtRow=function(e){return h.getAtRow(e)},this.getSourceDataAtCell=function(e,t){return h.getAtCell(e,t)},this.getDataAtRow=function(e){var t=d.getRange(new K(e,0),new K(e,this.countCols()-1),d.DESTINATION_RENDERER);return t[0]},this.getDataType=function(e,t,n,o){var r=this,i=null,s=null;void 0===e&&(e=0,n=this.countRows(),t=0,o=this.countCols()),void 0===n&&(n=e),void 0===o&&(o=t);var a="mixed";return F(Math.min(e,n),Math.max(e,n),function(e){var n=!0;return F(Math.min(t,o),Math.max(t,o),function(t){var o=r.getCellMeta(e,t);return s=o.type,i?n=i===s:i=s,n}),a=n?s:"mixed",n}),a},this.removeCellMeta=function(e,t,n){var o=m.getCellMeta(e,t);void 0!=o[n]&&delete c.cellSettings[e][t][n]},this.setCellMetaObject=function(e,t,n){if("object"==typeof n)for(var o in n)if(n.hasOwnProperty(o)){var r=n[o];this.setCellMeta(e,t,o,r)}},this.setCellMeta=function(e,t,n,o){c.cellSettings[e]||(c.cellSettings[e]=[]),c.cellSettings[e][t]||(c.cellSettings[e][t]=new c.columnSettings[t]),c.cellSettings[e][t][n]=o,Handsontable.hooks.run(m,"afterSetCellMeta",e,t,n,o)},this.getCellsMeta=function(){return W(c.cellSettings)},this.getCellMeta=function(e,t){var n,o=d.colToProp(t),r=e,i=t;if(e=a(e),t=l(t),c.columnSettings[t]||(c.columnSettings[t]=M(v,c.columnsSettingConflicts)),c.cellSettings[e]||(c.cellSettings[e]=[]),c.cellSettings[e][t]||(c.cellSettings[e][t]=new c.columnSettings[t]),n=c.cellSettings[e][t],n.row=e,n.col=t,n.physicalRow=r,n.physicalCol=i,n.prop=o,n.instance=m,Handsontable.hooks.run(m,"beforeGetCellMeta",e,t,n),A(n,s(n)),n.cells){var u=n.cells.call(n,e,t,o);u&&(A(n,u),A(n,s(u)))}return Handsontable.hooks.run(m,"afterGetCellMeta",e,t,n),n},this.isColumnModificationAllowed=function(){return!("object"===m.dataType||m.getSettings().columns)};var y=G("renderer");this.getCellRenderer=function(e,t){var n=y.call(this,e,t);return j(n)},this.getCellEditor=G("editor"),this.getCellValidator=G("validator"),this.validateCells=function(e){var t=new n;t.onQueueEmpty=e;for(var o=m.countRows()-1;o>=0;){for(var r=m.countCols()-1;r>=0;)t.addValidatorToQueue(),m.validateCell(m.getDataAtCell(o,r),m.getCellMeta(o,r),function(e){if("boolean"!=typeof e)throw new Error("Validation error: result is not boolean");e===!1&&(t.valid=!1),t.removeValidatorFormQueue()},"validateCells"),r--;o--}t.checkIfQueueIsEmpty()},this.getRowHeader=function(e){var t=c.settings.rowHeaders;return void 0!==e&&(e=Handsontable.hooks.run(m,"modifyRowHeader",e)),void 0===e?(t=[],F(m.countRows()-1,function(e){t.push(m.getRowHeader(e))})):Array.isArray(t)&&void 0!==t[e]?t=t[e]:"function"==typeof t?t=t(e):t&&"string"!=typeof t&&"number"!=typeof t&&(t=e+1),t},this.hasRowHeaders=function(){return!!c.settings.rowHeaders},this.hasColHeaders=function(){if(void 0!==c.settings.colHeaders&&null!==c.settings.colHeaders)return!!c.settings.colHeaders;for(var e=0,t=m.countCols();t>e;e++)if(m.getColHeader(e))return!0;return!1},this.getColHeader=function(e){if(e=Handsontable.hooks.run(m,"modifyColHeader",e),void 0===e){for(var t=[],n=0,o=m.countCols();o>n;n++)t.push(m.getColHeader(n));return t}var r=e;return e=Handsontable.hooks.run(m,"modifyCol",e),c.settings.columns&&c.settings.columns[e]&&c.settings.columns[e].title?c.settings.columns[e].title:Array.isArray(c.settings.colHeaders)&&void 0!==c.settings.colHeaders[e]?c.settings.colHeaders[e]:"function"==typeof c.settings.colHeaders?c.settings.colHeaders(e):c.settings.colHeaders&&"string"!=typeof c.settings.colHeaders&&"number"!=typeof c.settings.colHeaders?$(r):c.settings.colHeaders},this._getColWidthFromSettings=function(e){var t=m.getCellMeta(0,e),n=t.width;if((void 0===n||n===c.settings.width)&&(n=t.colWidths),void 0!==n&&null!==n){switch(typeof n){case"object":n=n[e];break;case"function":n=n(e)}"string"==typeof n&&(n=parseInt(n,10))}return n},this.getColWidth=function(e){var t=m._getColWidthFromSettings(e);return t=Handsontable.hooks.run(m,"modifyColWidth",t,e),void 0===t&&(t=q.DEFAULT_WIDTH),t},this._getRowHeightFromSettings=function(e){var t=c.settings.rowHeights;if(void 0!==t&&null!==t){switch(typeof t){case"object":t=t[e];break;case"function":t=t(e)}"string"==typeof t&&(t=parseInt(t,10))}return t},this.getRowHeight=function(e){var t=m._getRowHeightFromSettings(e);return t=Handsontable.hooks.run(m,"modifyRowHeight",t,e)},this.countSourceRows=function(){return m.getSourceData()?m.getSourceData().length:0},this.countRows=function(){return d.getLength()},this.countCols=function(){return"object"===m.dataType||"function"===m.dataType?c.settings.columns&&c.settings.columns.length?c.settings.columns.length:d.colToPropCache.length:"array"===m.dataType?c.settings.columns&&c.settings.columns.length?c.settings.columns.length:c.settings.data&&c.settings.data[0]&&c.settings.data[0].length?c.settings.data[0].length:0:void 0},this.getColspanOffset=function(e,t){var n=0;if(m.colspanArray){for(var o=0;e>o;o++)n+=m.colspanArray[t][o]-1||0;return n}for(var n=0,r=m.view.wt.wtTable.THEAD.childNodes.length-t-1,i=m.view.wt.wtTable.THEAD.querySelector("tr:nth-child("+parseInt(r+1,10)+")"),s=m.view.wt.wtSettings.settings.rowHeaders().length,o=s;s+e>o;o++)i.childNodes[o].hasAttribute("colspan")&&(n+=parseInt(i.childNodes[o].getAttribute("colspan"),10)-1);return n},this.getHeaderColspan=function(e,t){var n=m.view.wt.wtTable.THEAD.childNodes.length-t-1,o=m.view.wt.wtSettings.settings.rowHeaders().length,r=m.view.wt.wtTable.THEAD.querySelector("tr:nth-child("+parseInt(n+1,10)+")"),i=o+e-m.view.wt.wtViewport.columnsRenderCalculator.startColumn;return r.childNodes[i].hasAttribute("colspan")?parseInt(r.childNodes[i].getAttribute("colspan"),10):0},this.rowOffset=function(){return m.view.wt.wtTable.getFirstRenderedRow()},this.colOffset=function(){return m.view.wt.wtTable.getFirstRenderedColumn()},this.countRenderedRows=function(){return m.view.wt.drawn?m.view.wt.wtTable.getRenderedRowsCount():-1},this.countVisibleRows=function(){return m.view.wt.drawn?m.view.wt.wtTable.getVisibleRowsCount():-1},this.countRenderedCols=function(){return m.view.wt.drawn?m.view.wt.wtTable.getRenderedColumnsCount():-1},this.countVisibleCols=function(){return m.view.wt.drawn?m.view.wt.wtTable.getVisibleColumnsCount():-1},this.countEmptyRows=function(e){for(var t,n=m.countRows()-1,o=0;n>=0;){if(t=Handsontable.hooks.run(this,"modifyRow",n),m.isEmptyRow(t))o++;else if(e)break;n--}return o},this.countEmptyCols=function(e){if(m.countRows()<1)return 0;for(var t=m.countCols()-1,n=0;t>=0;){if(m.isEmptyCol(t))n++;else if(e)break;t--}return n},this.isEmptyRow=function(e){return c.settings.isEmptyRow.call(m,e)},this.isEmptyCol=function(e){return c.settings.isEmptyCol.call(m,e)},this.selectCell=function(e,t,n,o,r,i){var s;if(i="undefined"==typeof i||i===!0,"number"!=typeof e||0>e||e>=m.countRows())return!1;if("number"!=typeof t||0>t||t>=m.countCols())return!1;if("undefined"!=typeof n){if("number"!=typeof n||0>n||n>=m.countRows())return!1;if("number"!=typeof o||0>o||o>=m.countCols())return!1}return s=new K(e,t),c.selRange=new X(s,s,s),i&&m.listen(),"undefined"==typeof n?p.setRangeEnd(c.selRange.from,r):p.setRangeEnd(new K(n,o),r),m.selection.finish(),!0},this.selectCellByProp=function(e,t,n,o,r){return arguments[1]=d.propToCol(arguments[1]),"undefined"!=typeof arguments[3]&&(arguments[3]=d.propToCol(arguments[3])),m.selectCell.apply(m,arguments)},this.deselectCell=function(){p.deselect()},this.destroy=function(){m._clearTimeouts(),m.view&&m.view.destroy(),h&&h.destroy(),h=null,E(m.rootElement),w.destroy(),Handsontable.hooks.run(m,"afterDestroy"),Handsontable.hooks.destroy(m);for(var e in m)m.hasOwnProperty(e)&&("function"==typeof m[e]?m[e]=u:"guid"!==e&&(m[e]=null));c=null,d=null,f=null,p=null,g=null,m=null,v=null},this.getActiveEditor=function(){return g.getActiveEditor()},this.getPlugin=function(e){return I(this,e)},this.getInstance=function(){return m},this.addHook=function(e,t){Handsontable.hooks.add(e,t,m)},this.addHookOnce=function(e,t){Handsontable.hooks.once(e,t,m)},this.removeHook=function(e,t){Handsontable.hooks.remove(e,t,m)},this.runHooks=function(e,t,n,o,r,i,s){return Handsontable.hooks.run(m,e,t,n,o,r,i,s)},this.timeouts=[],this._registerTimeout=function(e){this.timeouts.push(e)},this._clearTimeouts=function(){for(var e=0,t=this.timeouts.length;t>e;e++)clearTimeout(this.timeouts[e])},this.version=Handsontable.version,Handsontable.hooks.run(m,"construct")};var Z=function(){};Z.prototype={data:void 0,dataSchema:void 0,width:void 0,height:void 0,startRows:5,startCols:5,rowHeaders:null,colHeaders:null,colWidths:void 0,rowHeights:void 0,columns:void 0,cells:void 0,cell:[],comments:!1,customBorders:!1,minRows:0,minCols:0,maxRows:1/0,maxCols:1/0,minSpareRows:0,minSpareCols:0,allowInsertRow:!0,allowInsertColumn:!0,allowRemoveRow:!0,allowRemoveColumn:!0,multiSelect:!0,fillHandle:!0,fixedRowsTop:0,fixedRowsBottom:0,fixedColumnsLeft:0,outsideClickDeselects:!0,enterBeginsEditing:!0,enterMoves:{row:1,col:0},tabMoves:{row:0,col:1},autoWrapRow:!1,autoWrapCol:!1,copyRowsLimit:1e3,copyColsLimit:1e3,pasteMode:"overwrite",persistentState:!1,currentRowClassName:void 0,currentColClassName:void 0,className:void 0,tableClassName:void 0,stretchH:"none",isEmptyRow:function(e){var t,n,o,r;for(t=0,n=this.countCols();n>t;t++)if(o=this.getDataAtCell(e,t),""!==o&&null!==o&&"undefined"!=typeof o)return"object"==typeof o?(r=this.getCellMeta(e,t),N(this.getSchema()[r.prop],o)):!1;return!0},isEmptyCol:function(e){var t,n,o;for(t=0,n=this.countRows();n>t;t++)if(o=this.getDataAtCell(t,e),""!==o&&null!==o&&"undefined"!=typeof o)return!1;return!0},observeDOMVisibility:!0,allowInvalid:!0,invalidCellClassName:"htInvalid",placeholder:!1,placeholderCellClassName:"htPlaceholder",readOnlyCellClassName:"htDimmed",renderer:void 0,commentedCellClassName:"htCommentCell",fragmentSelection:!1,readOnly:!1,skipColumnOnPaste:!1,search:!1,type:"text",copyable:!0,editor:void 0,autoComplete:void 0,visibleRows:10,trimDropdown:!0,debug:!1,wordWrap:!0,noWordWrapClassName:"htNoWrap",contextMenu:void 0,contextMenuCopyPaste:void 0,copyPaste:void 0,undo:void 0,columnSorting:void 0,manualColumnMove:void 0,manualColumnResize:void 0,manualRowMove:void 0,manualRowResize:void 0,mergeCells:!1,viewportRowRenderingOffset:"auto",viewportColumnRenderingOffset:"auto",validator:void 0,disableVisualSelection:!1,sortIndicator:!1,manualColumnFreeze:void 0,trimWhitespace:!0,settings:void 0,source:void 0,title:void 0,checkedTemplate:void 0,uncheckedTemplate:void 0,label:void 0,format:void 0,language:void 0,selectOptions:void 0,autoColumnSize:void 0,autoRowSize:void 0,dateFormat:void 0,correctFormat:!1,defaultDate:void 0,strict:void 0,renderAllRows:void 0},Handsontable.DefaultSettings=Z},{"3rdparty/walkontable/src/calculator/viewportColumns":3,"3rdparty/walkontable/src/cell/coords":5,"3rdparty/walkontable/src/cell/range":6,"3rdparty/walkontable/src/selection":18,dataMap:26,dataSource:27,editorManager:28,eventManager:41,"helpers/array":42,"helpers/data":44,"helpers/dom/element":45,"helpers/number":49,"helpers/object":50,"helpers/setting":51,"helpers/string":52,numeral:"numeral",plugins:57,renderers:88,tableView:97}],26:[function(e,t,n){"use strict";function o(e,t,n){this.instance=e,this.priv=t,this.GridSettings=n,this.dataSource=this.instance.getSettings().data,this.dataSource[0]?this.duckSchema=this.recursiveDuckSchema(this.dataSource[0]):this.duckSchema={},this.createMap()}Object.defineProperties(n,{DataMap:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l,u,c,d=(r=e("SheetClip"),r&&r.__esModule&&r||{"default":r})["default"],h=(i=e("helpers/data"),i&&i.__esModule&&i||{"default":i}).cellMethodLookupFactory,f=(s=e("helpers/setting"),s&&s.__esModule&&s||{"default":s}).columnFactory,p=(a=e("helpers/object"),a&&a.__esModule&&a||{"default":a}),g=p.duckSchema,m=p.deepExtend,v=(l=e("helpers/array"),l&&l.__esModule&&l||{"default":l}),w=v.extendArray,y=v.to2dArray,b=(u=e("helpers/number"),u&&u.__esModule&&u||{"default":u}).rangeEach,C=(c=e("multiMap"),c&&c.__esModule&&c||{"default":c}).MultiMap;o.prototype.DESTINATION_RENDERER=1,o.prototype.DESTINATION_CLIPBOARD_GENERATOR=2,o.prototype.recursiveDuckSchema=function(e){return g(e)},o.prototype.recursiveDuckColumns=function(e,t,n){var o,r;if("undefined"==typeof t&&(t=0,n=""),"object"==typeof e&&!Array.isArray(e))for(r in e)e.hasOwnProperty(r)&&(null===e[r]?(o=n+r,this.colToPropCache.push(o),this.propToColCache.set(o,t),t++):t=this.recursiveDuckColumns(e[r],t,r+"."));return t},o.prototype.createMap=function(){var e,t,n=this.getSchema();if("undefined"==typeof n)throw new Error("trying to create `columns` definition but you didnt' provide `schema` nor `data`");this.colToPropCache=[],this.propToColCache=new C;var o=this.instance.getSettings().columns;if(o)for(e=0,t=o.length;t>e;e++)"undefined"!=typeof o[e].data&&(this.colToPropCache[e]=o[e].data,this.propToColCache.set(o[e].data,e));else this.recursiveDuckColumns(n)},o.prototype.colToProp=function(e){return e=Handsontable.hooks.run(this.instance,"modifyCol",e),this.colToPropCache&&"undefined"!=typeof this.colToPropCache[e]?this.colToPropCache[e]:e},o.prototype.propToCol=function(e){var t;return t="undefined"==typeof this.propToColCache.get(e)?e:this.propToColCache.get(e),t=Handsontable.hooks.run(this.instance,"modifyCol",t)},o.prototype.getSchema=function(){var e=this.instance.getSettings().dataSchema;return e?"function"==typeof e?e():e:this.duckSchema},o.prototype.createRow=function(e,t,n){var o,r,i=this.instance.countCols(),s=0;t||(t=1),("number"!=typeof e||e>=this.instance.countSourceRows())&&(e=this.instance.countSourceRows()),r=e;for(var a=this.instance.getSettings().maxRows;t>s&&this.instance.countSourceRows()<a;){if("array"===this.instance.dataType){o=[];for(var l=0;i>l;l++)o.push(null)}else"function"===this.instance.dataType?o=this.instance.getSettings().dataSchema(e):(o={},m(o,this.getSchema()));e===this.instance.countSourceRows()?this.dataSource.push(o):this.dataSource.splice(e,0,o),s++,r++}return Handsontable.hooks.run(this.instance,"afterCreateRow",e,s,n),this.instance.forceFullRender=!0,s},o.prototype.createCol=function(e,t,n){if(!this.instance.isColumnModificationAllowed())throw new Error("Cannot create new column. When data source in an object, you can only have as much columns as defined in first data row, data schema or in the 'columns' setting.If you want to be able to add new columns, you have to use array datasource.");
var o,r,i=this.instance.countSourceRows(),s=this.dataSource,a=0;t||(t=1),r=e;for(var l=this.instance.getSettings().maxCols;t>a&&this.instance.countCols()<l;){if(o=f(this.GridSettings,this.priv.columnsSettingConflicts),"number"!=typeof e||e>=this.instance.countCols()){for(var u=0;i>u;u++)"undefined"==typeof s[u]&&(s[u]=[]),s[u].push(null);this.priv.columnSettings.push(o)}else{for(var u=0;i>u;u++)s[u].splice(r,0,null);this.priv.columnSettings.splice(r,0,o)}a++,r++}return Handsontable.hooks.run(this.instance,"afterCreateCol",e,a,n),this.instance.forceFullRender=!0,a},o.prototype.removeRow=function(e,t){t||(t=1),"number"!=typeof e&&(e=-t),e=(this.instance.countSourceRows()+e)%this.instance.countSourceRows();var n=this.physicalRowsToLogical(e,t),o=Handsontable.hooks.run(this.instance,"beforeRemoveRow",e,t,n);if(o!==!1){var r=this.dataSource,i=r.filter(function(e,t){return-1==n.indexOf(t)});r.length=0,Array.prototype.push.apply(r,i),Handsontable.hooks.run(this.instance,"afterRemoveRow",e,t,n),this.instance.forceFullRender=!0}},o.prototype.removeCol=function(e,t){if("object"===this.instance.dataType||this.instance.getSettings().columns)throw new Error("cannot remove column with object data source or columns option specified");t||(t=1),"number"!=typeof e&&(e=-t),e=(this.instance.countCols()+e)%this.instance.countCols();var n=Handsontable.hooks.run(this.instance,"beforeRemoveCol",e,t);if(n!==!1){for(var o=this.dataSource,r=0,i=this.instance.countSourceRows();i>r;r++)o[r].splice(e,t);this.priv.columnSettings.splice(e,t),Handsontable.hooks.run(this.instance,"afterRemoveCol",e,t),this.instance.forceFullRender=!0}},o.prototype.spliceCol=function(e,t,n){var o=4<=arguments.length?[].slice.call(arguments,3):[],r=this.instance.getDataAtCol(e),i=r.slice(t,t+n),s=r.slice(t+n);w(o,s);for(var a=0;n>a;)o.push(null),a++;return y(o),this.instance.populateFromArray(t,e,o,null,null,"spliceCol"),i},o.prototype.spliceRow=function(e,t,n){var o=4<=arguments.length?[].slice.call(arguments,3):[],r=this.instance.getSourceDataAtRow(e),i=r.slice(t,t+n),s=r.slice(t+n);w(o,s);for(var a=0;n>a;)o.push(null),a++;return this.instance.populateFromArray(e,t,[o],null,null,"spliceRow"),i},o.prototype.get=function(e,t){if(e=Handsontable.hooks.run(this.instance,"modifyRow",e),"string"==typeof t&&t.indexOf(".")>-1){var n=t.split("."),o=this.dataSource[e];if(!o)return null;for(var r=0,i=n.length;i>r;r++)if(o=o[n[r]],"undefined"==typeof o)return null;return o}return"function"==typeof t?t(this.dataSource.slice(e,e+1)[0]):this.dataSource[e]&&this.dataSource[e].hasOwnProperty&&this.dataSource[e].hasOwnProperty(t)?this.dataSource[e][t]:null};var R=h("copyable",!1);o.prototype.getCopyable=function(e,t){return R.call(this.instance,e,this.propToCol(t))?this.get(e,t):""},o.prototype.set=function(e,t,n,o){if(e=Handsontable.hooks.run(this.instance,"modifyRow",e,o||"datamapGet"),"string"==typeof t&&t.indexOf(".")>-1){for(var r=t.split("."),i=this.dataSource[e],s=0,a=r.length-1;a>s;s++)"undefined"==typeof i[r[s]]&&(i[r[s]]={}),i=i[r[s]];i[r[s]]=n}else"function"==typeof t?t(this.dataSource.slice(e,e+1)[0],n):this.dataSource[e][t]=n},o.prototype.physicalRowsToLogical=function(e,t){for(var n,o=this.instance.countSourceRows(),r=(o+e)%o,i=[],s=t;o>r&&s;)n=Handsontable.hooks.run(this.instance,"modifyRow",r),i.push(n),s--,r++;return i},o.prototype.clear=function(){for(var e=0;e<this.instance.countSourceRows();e++)for(var t=0;t<this.instance.countCols();t++)this.set(e,this.colToProp(t),"")},o.prototype.getLength=function(){var e=this,t=this.instance.countSourceRows();return Handsontable.hooks.has("modifyRow",this.instance)&&b(this.instance.countSourceRows()-1,function(n){n=Handsontable.hooks.run(e.instance,"modifyRow",n),null===n&&t--}),t},o.prototype.getAll=function(){var e={row:0,col:0},t={row:Math.max(this.instance.countSourceRows()-1,0),col:Math.max(this.instance.countCols()-1,0)};return e.row-t.row!==0||this.instance.countSourceRows()?this.getRange(e,t,o.prototype.DESTINATION_RENDERER):[]},o.prototype.getRange=function(e,t,n){var o,r,i,s,a,l=[],u=n===this.DESTINATION_CLIPBOARD_GENERATOR?this.getCopyable:this.get;for(r=Math.max(e.row,t.row),s=Math.max(e.col,t.col),o=Math.min(e.row,t.row);r>=o;o++){a=[];var c=Handsontable.hooks.run(this.instance,"modifyRow",o);for(i=Math.min(e.col,t.col);s>=i;i++){if(null===c)break;a.push(u.call(this,o,this.colToProp(i)))}null!==c&&l.push(a)}return l},o.prototype.getText=function(e,t){return d.stringify(this.getRange(e,t,this.DESTINATION_RENDERER))},o.prototype.getCopyableText=function(e,t){return d.stringify(this.getRange(e,t,this.DESTINATION_CLIPBOARD_GENERATOR))},Handsontable.DataMap=o},{SheetClip:"SheetClip","helpers/array":42,"helpers/data":44,"helpers/number":49,"helpers/object":50,"helpers/setting":51,multiMap:55}],27:[function(e,t,n){"use strict";Object.defineProperties(n,{DataSource:{get:function(){return f}},__esModule:{value:!0}});var o,r,i,s,a,l=((o=e("helpers/data"),o&&o.__esModule&&o||{"default":o}).cellMethodLookupFactory,(r=e("helpers/setting"),r&&r.__esModule&&r||{"default":r}).columnFactory,i=e("helpers/object"),i&&i.__esModule&&i||{"default":i}),u=(l.duckSchema,l.deepExtend,l.getProperty),c=(s=e("helpers/array"),s&&s.__esModule&&s||{"default":s}),d=(c.extendArray,c.arrayEach),h=(a=e("helpers/number"),a&&a.__esModule&&a||{"default":a}).rangeEach,f=function(e,t){this.hot=e,this.data=t,this.dataType="array",this.colToProp=function(){},this.propToCol=function(){}};$traceurRuntime.createClass(f,{getData:function(){return this.data},getAtColumn:function(e){var t=this,n=[];return d(this.data,function(o){var r=t.colToProp(e);o="string"==typeof r?u(o,r):o[r],n.push(o)}),n},getAtRow:function(e){return this.data[e]},getAtCell:function(e,t){return this.data[e][this.colToProp(t)]},getByRange:function(e,t){var n=this,o=Math.min(e.row,t.row),r=Math.min(e.col,t.col),i=Math.max(e.row,t.row),s=Math.max(e.col,t.col),a=[];return h(o,i,function(e){var t,o=n.getAtRow(e);"array"===n.dataType?t=o.slice(r,s):"object"===n.dataType&&(t={},h(r,s,function(e){var r=n.colToProp(e);t[r]=o[r]})),a.push(t)}),a},destroy:function(){this.data=null,this.hot=null}},{})},{"helpers/array":42,"helpers/data":44,"helpers/number":49,"helpers/object":50,"helpers/setting":51}],28:[function(e,t,n){"use strict";function o(e,t,n){function o(e){var o="function"==typeof t.settings.enterMoves?t.settings.enterMoves(event):t.settings.enterMoves;e?n.transformStart(-o.row,-o.col):n.transformStart(o.row,o.col,!0)}function r(e){e?n.transformEnd(-1,0):n.transformStart(-1,0)}function i(e){e?n.transformEnd(1,0):n.transformStart(1,0)}function s(e){e?n.transformEnd(0,1):n.transformStart(0,1)}function a(e){e?n.transformEnd(0,-1):n.transformStart(0,-1)}function l(l){var c,p;if(e.isListening()&&(Handsontable.hooks.run(e,"beforeKeyDown",l),!C&&!v(l)&&(t.lastKeyCode=l.keyCode,n.isSelected()))){if(c=(l.ctrlKey||l.metaKey)&&!l.altKey,y&&!y.isWaiting()&&!(h(l.keyCode)||f(l.keyCode)||c||b.isEditorOpened()))return void b.openEditor("",l);switch(p=l.shiftKey?n.setRangeEnd:n.setRangeStart,l.keyCode){case d.A:!b.isEditorOpened()&&c&&(n.selectAll(),l.preventDefault(),g(l));break;case d.ARROW_UP:b.isEditorOpened()&&!y.isWaiting()&&b.closeEditorAndSaveChanges(c),r(l.shiftKey),l.preventDefault(),g(l);break;case d.ARROW_DOWN:b.isEditorOpened()&&!y.isWaiting()&&b.closeEditorAndSaveChanges(c),i(l.shiftKey),l.preventDefault(),g(l);break;case d.ARROW_RIGHT:b.isEditorOpened()&&!y.isWaiting()&&b.closeEditorAndSaveChanges(c),s(l.shiftKey),l.preventDefault(),g(l);break;case d.ARROW_LEFT:b.isEditorOpened()&&!y.isWaiting()&&b.closeEditorAndSaveChanges(c),a(l.shiftKey),l.preventDefault(),g(l);break;case d.TAB:var w="function"==typeof t.settings.tabMoves?t.settings.tabMoves(l):t.settings.tabMoves;l.shiftKey?n.transformStart(-w.row,-w.col):n.transformStart(w.row,w.col,!0),l.preventDefault(),g(l);break;case d.BACKSPACE:case d.DELETE:n.empty(l),b.prepareEditor(),l.preventDefault();break;case d.F2:b.openEditor(null,l),y&&y.enableFullEditMode(),l.preventDefault();break;case d.ENTER:b.isEditorOpened()?(y&&y.state!==Handsontable.EditorState.WAITING&&b.closeEditorAndSaveChanges(c),o(l.shiftKey)):e.getSettings().enterBeginsEditing?(b.openEditor(null,l),y&&y.enableFullEditMode()):o(l.shiftKey),l.preventDefault(),m(l);break;case d.ESCAPE:b.isEditorOpened()&&b.closeEditorAndRestoreOriginalValue(c),l.preventDefault();break;case d.HOME:p(l.ctrlKey||l.metaKey?new u(0,t.selRange.from.col):new u(t.selRange.from.row,0)),l.preventDefault(),g(l);break;case d.END:p(l.ctrlKey||l.metaKey?new u(e.countRows()-1,t.selRange.from.col):new u(t.selRange.from.row,e.countCols()-1)),l.preventDefault(),g(l);break;case d.PAGE_UP:n.transformStart(-e.countVisibleRows(),0),l.preventDefault(),g(l);break;case d.PAGE_DOWN:n.transformStart(e.countVisibleRows(),0),l.preventDefault(),g(l)}}}function c(){function t(e,t,n){"TD"==n.nodeName&&(b.openEditor(),y&&y.enableFullEditMode())}e.addHook("afterDocumentKeyDown",l),p.addEventListener(document.documentElement,"keydown",function(t){e.runHooks("afterDocumentKeyDown",t)}),e.view.wt.update("onCellDblClick",t),e.addHook("afterDestroy",function(){C=!0})}var p,y,b=this,C=!1;p=w(e),this.destroyEditor=function(e){this.closeEditor(e)},this.getActiveEditor=function(){return y},this.prepareEditor=function(){var n,o,r,i,s,a,l;return y&&y.isWaiting()?void this.closeEditor(!1,!1,function(e){e&&b.prepareEditor()}):(n=t.selRange.highlight.row,o=t.selRange.highlight.col,r=e.colToProp(o),i=e.getCell(n,o),s=e.getDataAtCell(n,o),a=e.getCellMeta(n,o),l=e.getCellEditor(a),void(l?(y=Handsontable.editors.getEditor(l,e),y.prepare(n,o,r,i,s,a)):y=void 0))},this.isEditorOpened=function(){return y&&y.isOpened()},this.openEditor=function(e,t){y&&!y.cellProperties.readOnly?y.beginEditing(e,t):y&&y.cellProperties.readOnly&&t&&t.keyCode===d.ENTER&&o()},this.closeEditor=function(e,t,n){y?y.finishEditing(e,t,n):n&&n(!1)},this.closeEditorAndSaveChanges=function(e){return this.closeEditor(!1,e)},this.closeEditorAndRestoreOriginalValue=function(e){return this.closeEditor(!0,e)},c()}Object.defineProperties(n,{EditorManager:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l,u=(r=e("3rdparty/walkontable/src/cell/coords"),r&&r.__esModule&&r||{"default":r}).WalkontableCellCoords,c=(i=e("helpers/unicode"),i&&i.__esModule&&i||{"default":i}),d=c.KEY_CODES,h=c.isMetaKey,f=c.isCtrlKey,p=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),g=p.stopPropagation,m=p.stopImmediatePropagation,v=p.isImmediatePropagationStopped,w=((a=e("editors"),a&&a.__esModule&&a||{"default":a}).getEditor,(l=e("eventManager"),l&&l.__esModule&&l||{"default":l}).eventManager);Handsontable.EditorManager=o},{"3rdparty/walkontable/src/cell/coords":5,editors:29,eventManager:41,"helpers/dom/event":46,"helpers/unicode":53}],29:[function(e,t,n){"use strict";function o(e){var t,n;n={},t=e,this.getConstructor=function(){return e},this.getInstance=function(e){return e.guid in n||(n[e.guid]=new t(e)),n[e.guid]}}function r(e,t){var n=new o(t);"string"==typeof e&&(c[e]=n,Handsontable.editors[u(e)+"Editor"]=t),d.set(t,n)}function i(e,t){var n;if("function"==typeof e)d.get(e)||r(null,e),n=d.get(e);else{if("string"!=typeof e)throw Error('Only strings and functions can be passed as "editor" parameter ');n=c[e]}if(!n)throw Error('No editor registered under name "'+e+'"');return n.getInstance(t)}function s(e){var t;if("string"!=typeof e)throw Error('Only strings and functions can be passed as "editor" parameter ');if(t=c[e],!t)throw Error('No editor registered under name "'+e+'"');return t.getConstructor()}function a(e){return c[e]?!0:!1}Object.defineProperties(n,{registerEditor:{get:function(){return r}},getEditor:{get:function(){return i}},hasEditor:{get:function(){return a}},getEditorConstructor:{get:function(){return s}},__esModule:{value:!0}});var l,u=(l=e("helpers/string"),l&&l.__esModule&&l||{"default":l}).toUpperCaseFirst,c={},d=new WeakMap;Handsontable.editors=Handsontable.editors||{},Handsontable.editors.registerEditor=r,Handsontable.editors.getEditor=i},{"helpers/string":52}],30:[function(e,t,n){"use strict";function o(e){this.instance=e,this.state=Handsontable.EditorState.VIRGIN,this._opened=!1,this._fullEditMode=!1,this._closeCallback=null,this.init()}Object.defineProperties(n,{BaseEditor:{get:function(){return o}},__esModule:{value:!0}});var r,i,s=(r=e("helpers/mixed"),r&&r.__esModule&&r||{"default":r}).stringify,a=(i=e("3rdparty/walkontable/src/cell/coords"),i&&i.__esModule&&i||{"default":i}).WalkontableCellCoords;Handsontable.editors=Handsontable.editors||{},Handsontable.editors.BaseEditor=o,Handsontable.EditorState={VIRGIN:"STATE_VIRGIN",EDITING:"STATE_EDITING",WAITING:"STATE_WAITING",FINISHED:"STATE_FINISHED"},o.prototype._fireCallbacks=function(e){this._closeCallback&&(this._closeCallback(e),this._closeCallback=null)},o.prototype.init=function(){},o.prototype.getValue=function(){throw Error("Editor getValue() method unimplemented")},o.prototype.setValue=function(e){throw Error("Editor setValue() method unimplemented")},o.prototype.open=function(){throw Error("Editor open() method unimplemented")},o.prototype.close=function(){throw Error("Editor close() method unimplemented")},o.prototype.prepare=function(e,t,n,o,r,i){this.TD=o,this.row=e,this.col=t,this.prop=n,this.originalValue=r,this.cellProperties=i,this.instance.view.isMouseDown()&&document.activeElement&&document.activeElement!==document.body?document.activeElement.blur():document.activeElement||document.body.focus(),this.state=Handsontable.EditorState.VIRGIN},o.prototype.extend=function(){function e(){n.apply(this,arguments)}function t(e,t){function n(){}return n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e,e}var n=this.constructor;return t(e,n)},o.prototype.saveValue=function(e,t){var n,o;t?(n=this.instance.getSelected(),n[0]>n[2]&&(o=n[0],n[0]=n[2],n[2]=o),n[1]>n[3]&&(o=n[1],n[1]=n[3],n[3]=o),this.instance.populateFromArray(n[0],n[1],e,n[2],n[3],"edit")):this.instance.populateFromArray(this.row,this.col,e,null,null,"edit")},o.prototype.beginEditing=function(e,t){this.state==Handsontable.EditorState.VIRGIN&&(this.instance.view.scrollViewport(new a(this.row,this.col)),this.instance.view.render(),this.state=Handsontable.EditorState.EDITING,e="string"==typeof e?e:this.originalValue,this.setValue(s(e)),this.open(t),this._opened=!0,this.focus(),this.instance.view.render())},o.prototype.finishEditing=function(e,t,n){var o,r=this;if(n){var i=this._closeCallback;this._closeCallback=function(e){i&&i(e),n(e)}}if(!this.isWaiting()){if(this.state==Handsontable.EditorState.VIRGIN)return void this.instance._registerTimeout(setTimeout(function(){r._fireCallbacks(!0)},0));if(this.state==Handsontable.EditorState.EDITING){if(e)return this.cancelChanges(),void this.instance.view.render();o=this.instance.getSettings().trimWhitespace?[["string"==typeof this.getValue()?String.prototype.trim.call(this.getValue()||""):this.getValue()]]:[[this.getValue()]],this.state=Handsontable.EditorState.WAITING,this.saveValue(o,t),this.instance.getCellValidator(this.cellProperties)?this.instance.addHookOnce("postAfterValidate",function(e){r.state=Handsontable.EditorState.FINISHED,r.discardEditor(e)}):(this.state=Handsontable.EditorState.FINISHED,this.discardEditor(!0))}}},o.prototype.cancelChanges=function(){this.state=Handsontable.EditorState.FINISHED,this.discardEditor()},o.prototype.discardEditor=function(e){this.state===Handsontable.EditorState.FINISHED&&(e===!1&&this.cellProperties.allowInvalid!==!0?(this.instance.selectCell(this.row,this.col),this.focus(),this.state=Handsontable.EditorState.EDITING,this._fireCallbacks(!1)):(this.close(),this._opened=!1,this._fullEditMode=!1,this.state=Handsontable.EditorState.VIRGIN,this._fireCallbacks(!0)))},o.prototype.enableFullEditMode=function(){this._fullEditMode=!0},o.prototype.isInFullEditMode=function(){return this._fullEditMode},o.prototype.isOpened=function(){return this._opened},o.prototype.isWaiting=function(){return this.state===Handsontable.EditorState.WAITING},o.prototype.checkEditorSection=function(){var e=this.instance.countRows(),t="";return this.row<this.instance.getSettings().fixedRowsTop?t=this.col<this.instance.getSettings().fixedColumnsLeft?"top-left-corner":"top":this.instance.getSettings().fixedRowsBottom&&this.row>=e-this.instance.getSettings().fixedRowsBottom?t=this.col<this.instance.getSettings().fixedColumnsLeft?"bottom-left-corner":"bottom":this.col<this.instance.getSettings().fixedColumnsLeft&&(t="left"),t}},{"3rdparty/walkontable/src/cell/coords":5,"helpers/mixed":48}],31:[function(e,t,n){"use strict";function o(e){T=!1;var t=this.getActiveEditor();if(h(e.keyCode)||e.keyCode===d.BACKSPACE||e.keyCode===d.DELETE||e.keyCode===d.INSERT){var n=0;if(e.keyCode===d.C&&(e.ctrlKey||e.metaKey))return;t.isOpened()||(n+=10),t.htEditor&&t.instance._registerTimeout(setTimeout(function(){t.queryChoices(t.TEXTAREA.value),T=!0},n))}}Object.defineProperties(n,{AutocompleteEditor:{get:function(){return E}},__esModule:{value:!0}});var r,i,s,a,l,u,c=(r=e("helpers/unicode"),r&&r.__esModule&&r||{"default":r}),d=c.KEY_CODES,h=c.isPrintableChar,f=(i=e("helpers/mixed"),i&&i.__esModule&&i||{"default":i}).stringify,p=(s=e("helpers/array"),s&&s.__esModule&&s||{"default":s}).pivot,g=(a=e("helpers/dom/element"),a&&a.__esModule&&a||{"default":a}),m=g.addClass,v=g.getCaretPosition,w=g.getScrollbarWidth,y=g.getSelectionEndPosition,b=g.outerWidth,C=g.setCaretPosition,R=(l=e("editors"),l&&l.__esModule&&l||{"default":l}),_=(R.getEditorConstructor,R.registerEditor),S=(u=e("handsontableEditor"),u&&u.__esModule&&u||{"default":u}).HandsontableEditor,E=S.prototype.extend();E.prototype.init=function(){S.prototype.init.apply(this,arguments),this.query=null,this.choices=[]},E.prototype.createElements=function(){S.prototype.createElements.apply(this,arguments),m(this.htContainer,"autocompleteEditor"),m(this.htContainer,-1===window.navigator.platform.indexOf("Mac")?"":"htMacScroll")};var T=!1;E.prototype.prepare=function(){this.instance.addHook("beforeKeyDown",o),S.prototype.prepare.apply(this,arguments)},E.prototype.open=function(){this.TEXTAREA_PARENT.style.overflow="auto",S.prototype.open.apply(this,arguments),this.TEXTAREA_PARENT.style.overflow="";var e=this.htEditor.getInstance(),t=this,n=void 0===this.cellProperties.trimDropdown?!0:this.cellProperties.trimDropdown;this.TEXTAREA.style.visibility="visible",this.focus(),e.updateSettings({colWidths:n?[b(this.TEXTAREA)-2]:void 0,width:n?b(this.TEXTAREA)+w()+2:void 0,afterRenderer:function(e,n,o,r,i){var s,a,l=this.getCellMeta(n,o).filteringCaseSensitive===!0,i=f(i);i&&(s=l?i.indexOf(this.query):i.toLowerCase().indexOf(t.query.toLowerCase()),-1!=s&&(a=i.substr(s,t.query.length),e.innerHTML=i.replace(a,"<strong>"+a+"</strong>")))},autoColumnSize:!0,modifyColWidth:function(e,t){var o=this.getPlugin("autoColumnSize").widths;return o[t]&&(e=o[t]),n?e:e+15}}),this.htEditor.view.wt.wtTable.holder.parentNode.style["padding-right"]=w()+2+"px",T&&(T=!1),t.instance._registerTimeout(setTimeout(function(){t.queryChoices(t.TEXTAREA.value)},0))},E.prototype.close=function(){S.prototype.close.apply(this,arguments)},E.prototype.queryChoices=function(e){if(this.query=e,"function"==typeof this.cellProperties.source){var t=this;this.cellProperties.source(e,function(e){t.updateChoicesList(e)})}else if(Array.isArray(this.cellProperties.source)){var n;if(e&&this.cellProperties.filter!==!1){var o=this.cellProperties.filteringCaseSensitive===!0,r=e.toLowerCase();n=this.cellProperties.source.filter(function(t){return o?-1!=t.indexOf(e):-1!=t.toLowerCase().indexOf(r)})}else n=this.cellProperties.source;this.updateChoicesList(n)}else this.updateChoicesList([])},E.prototype.updateChoicesList=function(e){var t,n=v(this.TEXTAREA),o=y(this.TEXTAREA),r=E.sortByRelevance(this.getValue(),e,this.cellProperties.filteringCaseSensitive);if(0==this.cellProperties.filter)t=r[0];else{for(var i=[],s=0,a=r.length;a>s;s++)i.push(e[r[s]]);t=0,e=i}this.choices=e,this.htEditor.loadData(p([e])),this.updateDropdownHeight(),this.cellProperties.strict===!0&&this.highlightBestMatchingChoice(t),this.instance.listen(),this.TEXTAREA.focus(),C(this.TEXTAREA,n,n==o?void 0:o)},E.prototype.updateDropdownHeight=function(){var e=this.htEditor.getColWidth(0)+w()+2,t=void 0===this.cellProperties.trimDropdown?!0:this.cellProperties.trimDropdown;this.htEditor.updateSettings({height:this.getDropdownHeight(),width:t?void 0:e}),this.htEditor.view.wt.wtTable.alignOverlaysWithTrimmingContainer()},E.prototype.finishEditing=function(e){e||this.instance.removeHook("beforeKeyDown",o),S.prototype.finishEditing.apply(this,arguments)},E.prototype.highlightBestMatchingChoice=function(e){"number"==typeof e?this.htEditor.selectCell(e,0):this.htEditor.deselectCell()},E.sortByRelevance=function(e,t,n){var o,r,i,s,a,l=[],u=e.length,c=[];if(0===u){for(s=0,a=t.length;a>s;s++)c.push(s);return c}for(s=0,a=t.length;a>s;s++)o=f(t[s]),r=n?o.indexOf(e):o.toLowerCase().indexOf(e.toLowerCase()),-1!=r&&(i=o.length-r-u,l.push({baseIndex:s,index:r,charsLeft:i,value:o}));for(l.sort(function(e,t){return-1===t.index?-1:-1===e.index?1:e.index<t.index?-1:t.index<e.index?1:e.index===t.index?e.charsLeft<t.charsLeft?-1:e.charsLeft>t.charsLeft?1:0:void 0}),s=0,a=l.length;a>s;s++)c.push(l[s].baseIndex);return c},E.prototype.getDropdownHeight=function(){var e=this.htEditor.getInstance().getRowHeight(0)||23,t=this.cellProperties.visibleRows;return this.choices.length>=t?t*e:this.choices.length*e+8},E.prototype.allowKeyEventPropagation=function(e){var t={row:this.htEditor.getSelectedRange()?this.htEditor.getSelectedRange().from.row:-1},n=!1;return e===d.ARROW_DOWN&&t.row<this.htEditor.countRows()-1&&(n=!0),e===d.ARROW_UP&&t.row>-1&&(n=!0),n},_("autocomplete",E)},{editors:29,handsontableEditor:35,"helpers/array":42,"helpers/dom/element":45,"helpers/mixed":48,"helpers/unicode":53}],32:[function(e,t,n){"use strict";Object.defineProperties(n,{CheckboxEditor:{get:function(){return u}},__esModule:{value:!0}});var o,r,i,s=(o=e("editors"),o&&o.__esModule&&o||{"default":o}).registerEditor,a=(r=e("_baseEditor"),r&&r.__esModule&&r||{"default":r}).BaseEditor,l=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}).hasClass,u=function(){$traceurRuntime.superConstructor(c).apply(this,arguments)},c=u;$traceurRuntime.createClass(u,{beginEditing:function(){var e=this.TD.querySelector('input[type="checkbox"]');l(e,"htBadValue")||e.click()},finishEditing:function(){},init:function(){},open:function(){},close:function(){},getValue:function(){},setValue:function(){},focus:function(){}},{},a),s("checkbox",u)},{_baseEditor:30,editors:29,"helpers/dom/element":45}],33:[function(e,t,n){"use strict";Object.defineProperties(n,{DateEditor:{get:function(){return S}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c,d,h=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),f=h.addClass,p=h.outerHeight,g=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}).deepExtend,m=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).EventManager,v=(s=e("editors"),s&&s.__esModule&&s||{"default":s}),w=(v.getEditor,v.registerEditor),y=(a=e("helpers/unicode"),a&&a.__esModule&&a||{"default":a}).isMetaKey,b=(l=e("helpers/dom/event"),l&&l.__esModule&&l||{"default":l}).stopPropagation,C=(u=e("textEditor"),u&&u.__esModule&&u||{"default":u}).TextEditor,R=(c=e("moment"),c&&c.__esModule&&c||{"default":c})["default"],_=(d=e("pikaday"),d&&d.__esModule&&d||{"default":d})["default"];Handsontable.editors=Handsontable.editors||{},Handsontable.editors.DateEditor=S;var S=function(e){this.$datePicker=null,this.datePicker=null,this.datePickerStyle=null,this.defaultDateFormat="DD/MM/YYYY",this.isCellEdited=!1,this.parentDestroyed=!1,$traceurRuntime.superConstructor(E).call(this,e)},E=S;$traceurRuntime.createClass(S,{init:function(){var e=this;if("function"!=typeof R)throw new Error("You need to include moment.js to your project.");if("function"!=typeof _)throw new Error("You need to include Pikaday to your project.");$traceurRuntime.superGet(this,E.prototype,"init").call(this),this.instance.addHook("afterDestroy",function(){e.parentDestroyed=!0,e.destroyElements()})},createElements:function(){$traceurRuntime.superGet(this,E.prototype,"createElements").call(this),this.datePicker=document.createElement("DIV"),this.datePickerStyle=this.datePicker.style,this.datePickerStyle.position="absolute",this.datePickerStyle.top=0,this.datePickerStyle.left=0,this.datePickerStyle.zIndex=9999,f(this.datePicker,"htDatepickerHolder"),document.body.appendChild(this.datePicker),this.$datePicker=new _(this.getDatePickerConfig());var e=new m(this);e.addEventListener(this.datePicker,"mousedown",function(e){return b(e)}),this.hideDatepicker()},destroyElements:function(){this.$datePicker.destroy()},prepare:function(e,t,n,o,r,i){this._opened=!1,$traceurRuntime.superGet(this,E.prototype,"prepare").call(this,e,t,n,o,r,i)},open:function(){var e=void 0!==arguments[0]?arguments[0]:null;$traceurRuntime.superGet(this,E.prototype,"open").call(this),this.showDatepicker(e)},close:function(){var e=this;this._opened=!1,this.instance._registerTimeout(setTimeout(function(){e.instance.selection.refreshBorders()},0)),$traceurRuntime.superGet(this,E.prototype,"close").call(this)},finishEditing:function(){var e=void 0!==arguments[0]?arguments[0]:!1,t=void 0!==arguments[1]?arguments[1]:!1;if(e){var n=this.originalValue;void 0!==n&&this.setValue(n)}this.hideDatepicker(),$traceurRuntime.superGet(this,E.prototype,"finishEditing").call(this,e,t)},showDatepicker:function(e){this.$datePicker.config(this.getDatePickerConfig());var t,n=this.TD.getBoundingClientRect(),o=this.cellProperties.dateFormat||this.defaultDateFormat,r=this.$datePicker.config(),i=this.instance.view.isMouseDown(),s=e?y(e.keyCode):!1;this.datePickerStyle.top=window.pageYOffset+n.top+p(this.TD)+"px",this.datePickerStyle.left=window.pageXOffset+n.left+"px",this.$datePicker._onInputFocus=function(){},r.format=o,this.originalValue?(t=this.originalValue,R(t,o,!0).isValid()&&this.$datePicker.setMoment(R(t,o),!0),s||i||this.setValue("")):this.cellProperties.defaultDate?(t=this.cellProperties.defaultDate,r.defaultDate=t,R(t,o,!0).isValid()&&this.$datePicker.setMoment(R(t,o),!0),s||i||this.setValue("")):this.$datePicker.gotoToday(),this.datePickerStyle.display="block",this.$datePicker.show()},hideDatepicker:function(){this.datePickerStyle.display="none",this.$datePicker.hide()},getDatePickerConfig:function(){var e=this,t=this.TEXTAREA,n={};this.cellProperties&&this.cellProperties.datePickerConfig&&g(n,this.cellProperties.datePickerConfig);var o=n.onSelect,r=n.onClose;return n.field=t,n.trigger=t,n.container=this.datePicker,n.bound=!1,n.format=n.format||this.defaultDateFormat,n.reposition=n.reposition||!1,n.onSelect=function(t){isNaN(t.getTime())||(t=R(t).format(e.cellProperties.dateFormat||e.defaultDateFormat)),e.setValue(t),e.hideDatepicker(),o&&o()},n.onClose=function(){e.parentDestroyed||e.finishEditing(!1),r&&r()},n}},{},C),w("date",S)},{editors:29,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/object":50,"helpers/unicode":53,moment:"moment",pikaday:"pikaday",textEditor:40}],34:[function(e,t,n){"use strict";Object.defineProperties(n,{DropdownEditor:{get:function(){return l}},__esModule:{value:!0}});var o,r,i=(o=e("editors"),o&&o.__esModule&&o||{"default":o}),s=(i.getEditor,i.registerEditor),a=(r=e("autocompleteEditor"),r&&r.__esModule&&r||{"default":r}).AutocompleteEditor,l=function(){$traceurRuntime.superConstructor(u).apply(this,arguments)},u=l;$traceurRuntime.createClass(l,{prepare:function(e,t,n,o,r,i){$traceurRuntime.superGet(this,u.prototype,"prepare").call(this,e,t,n,o,r,i),this.cellProperties.filter=!1,this.cellProperties.strict=!0}},{},a),Handsontable.hooks.add("beforeValidate",function(e,t,n,o){var r=this.getCellMeta(t,n);r.editor===Handsontable.editors.DropdownEditor&&void 0===r.strict&&(r.filter=!1,r.strict=!0)}),s("dropdown",l)},{autocompleteEditor:31,editors:29}],35:[function(e,t,n){"use strict";Object.defineProperties(n,{HandsontableEditor:{get:function(){return w}},__esModule:{value:!0}});var o,r,i,s,a,l,u=(o=e("helpers/unicode"),o&&o.__esModule&&o||{"default":o}).KEY_CODES,c=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}).extend,d=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}).setCaretPosition,h=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),f=h.stopImmediatePropagation,p=h.isImmediatePropagationStopped,g=(a=e("editors"),a&&a.__esModule&&a||{"default":a}),m=(g.getEditor,g.registerEditor),v=(l=e("textEditor"),l&&l.__esModule&&l||{"default":l}).TextEditor,w=v.prototype.extend();w.prototype.createElements=function(){v.prototype.createElements.apply(this,arguments);var e=document.createElement("DIV");e.className="handsontableEditor",this.TEXTAREA_PARENT.appendChild(e),this.htContainer=e,this.assignHooks()},w.prototype.prepare=function(e,t,n,o,r,i){v.prototype.prepare.apply(this,arguments);var s=this,a={startRows:0,startCols:0,minRows:0,minCols:0,className:"listbox",copyPaste:!1,autoColumnSize:!1,autoRowSize:!1,readOnly:!0,fillHandle:!1,afterOnCellMouseDown:function(){var e=this.getValue();void 0!==e&&s.setValue(e),s.instance.destroyEditor()}};this.cellProperties.handsontable&&c(a,i.handsontable),this.htOptions=a};var y=function(e){if(!p(e)){var t,n=this.getActiveEditor(),o=n.htEditor.getInstance();if(e.keyCode==u.ARROW_DOWN)if(o.getSelected()){var r=o.getSelected()[0],i=o.countRows()-1;t=Math.min(i,r+1)}else t=0;else if(e.keyCode==u.ARROW_UP&&o.getSelected()){var r=o.getSelected()[0];t=r-1}void 0!==t&&(0>t?o.deselectCell():o.selectCell(t,0),o.getData().length&&(e.preventDefault(),f(e),n.instance.listen(),n.TEXTAREA.focus()))}};w.prototype.open=function(){this.instance.addHook("beforeKeyDown",y),v.prototype.open.apply(this,arguments),this.htEditor&&this.htEditor.destroy(),this.htEditor=new Handsontable(this.htContainer,this.htOptions),this.cellProperties.strict?(this.htEditor.selectCell(0,0),this.TEXTAREA.style.visibility="hidden"):(this.htEditor.deselectCell(),this.TEXTAREA.style.visibility="visible"),d(this.TEXTAREA,0,this.TEXTAREA.value.length)},w.prototype.close=function(){this.instance.removeHook("beforeKeyDown",y),this.instance.listen(),v.prototype.close.apply(this,arguments)},w.prototype.focus=function(){this.instance.listen(),v.prototype.focus.apply(this,arguments)},w.prototype.beginEditing=function(e){var t=this.instance.getSettings().onBeginEditing;t&&t()===!1||v.prototype.beginEditing.apply(this,arguments)},w.prototype.finishEditing=function(e,t){if(this.htEditor&&this.htEditor.isListening()&&this.instance.listen(),this.htEditor&&this.htEditor.getSelected()){var n=this.htEditor.getInstance().getValue();void 0!==n&&this.setValue(n)}return v.prototype.finishEditing.apply(this,arguments)},w.prototype.assignHooks=function(){var e=this;this.instance.addHook("afterDestroy",function(){e.htEditor&&e.htEditor.destroy()})},m("handsontable",w)},{editors:29,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/object":50,"helpers/unicode":53,textEditor:40}],36:[function(e,t,n){"use strict";Object.defineProperties(n,{MobileTextEditor:{get:function(){return M}},__esModule:{value:!0}});var o,r,i,s,a,l,u=(o=e("helpers/unicode"),o&&o.__esModule&&o||{"default":o}).KEY_CODES,c=(r=e("helpers/dom/event"),r&&r.__esModule&&r||{"default":r}),d=c.stopImmediatePropagation,h=c.isImmediatePropagationStopped,f=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}),p=f.addClass,g=f.getScrollLeft,m=f.getScrollTop,v=f.hasClass,w=f.isChildOf,y=f.offset,b=f.outerHeight,C=f.outerWidth,R=f.removeClass,_=f.setCaretPosition,S=(s=e("editors"),s&&s.__esModule&&s||{"default":s}),E=(S.getEditor,S.registerEditor),T=(a=e("_baseEditor"),a&&a.__esModule&&a||{"default":a}).BaseEditor,O=(l=e("eventManager"),l&&l.__esModule&&l||{"default":l}).eventManager,M=T.prototype.extend(),k={},H=function(){this.controls={},this.controls.leftButton=document.createElement("DIV"),this.controls.leftButton.className="leftButton",
this.controls.rightButton=document.createElement("DIV"),this.controls.rightButton.className="rightButton",this.controls.upButton=document.createElement("DIV"),this.controls.upButton.className="upButton",this.controls.downButton=document.createElement("DIV"),this.controls.downButton.className="downButton";for(var e in this.controls)this.controls.hasOwnProperty(e)&&this.positionControls.appendChild(this.controls[e])};M.prototype.valueChanged=function(){return this.initValue!=this.getValue()},M.prototype.init=function(){var e=this;this.eventManager=O(this.instance),this.createElements(),this.bindEvents(),this.instance.addHook("afterDestroy",function(){e.destroy()})},M.prototype.getValue=function(){return this.TEXTAREA.value},M.prototype.setValue=function(e){this.initValue=e,this.TEXTAREA.value=e},M.prototype.createElements=function(){this.editorContainer=document.createElement("DIV"),this.editorContainer.className="htMobileEditorContainer",this.cellPointer=document.createElement("DIV"),this.cellPointer.className="cellPointer",this.moveHandle=document.createElement("DIV"),this.moveHandle.className="moveHandle",this.inputPane=document.createElement("DIV"),this.inputPane.className="inputs",this.positionControls=document.createElement("DIV"),this.positionControls.className="positionControls",this.TEXTAREA=document.createElement("TEXTAREA"),p(this.TEXTAREA,"handsontableInput"),this.inputPane.appendChild(this.TEXTAREA),this.editorContainer.appendChild(this.cellPointer),this.editorContainer.appendChild(this.moveHandle),this.editorContainer.appendChild(this.inputPane),this.editorContainer.appendChild(this.positionControls),H.call(this),document.body.appendChild(this.editorContainer)},M.prototype.onBeforeKeyDown=function(e){var t=this,n=t.getActiveEditor();if(e.target===n.TEXTAREA&&!h(e))switch(e.keyCode){case u.ENTER:n.close(),e.preventDefault();break;case u.BACKSPACE:d(e)}},M.prototype.open=function(){this.instance.addHook("beforeKeyDown",this.onBeforeKeyDown),p(this.editorContainer,"active"),R(this.cellPointer,"hidden"),this.updateEditorPosition()},M.prototype.focus=function(){this.TEXTAREA.focus(),_(this.TEXTAREA,this.TEXTAREA.value.length)},M.prototype.close=function(){this.TEXTAREA.blur(),this.instance.removeHook("beforeKeyDown",this.onBeforeKeyDown),R(this.editorContainer,"active")},M.prototype.scrollToView=function(){var e=this.instance.getSelectedRange().highlight;this.instance.view.scrollViewport(e)},M.prototype.hideCellPointer=function(){v(this.cellPointer,"hidden")||p(this.cellPointer,"hidden")},M.prototype.updateEditorPosition=function(e,t){if(e&&t)e=parseInt(e,10),t=parseInt(t,10),this.editorContainer.style.top=t+"px",this.editorContainer.style.left=e+"px";else{var n=this.instance.getSelected(),o=this.instance.getCell(n[0],n[1]);if(k.cellPointer||(k.cellPointer={height:b(this.cellPointer),width:C(this.cellPointer)}),k.editorContainer||(k.editorContainer={width:C(this.editorContainer)}),void 0!==o){var r=this.instance.view.wt.wtOverlays.leftOverlay.trimmingContainer==window?0:g(this.instance.view.wt.wtOverlays.leftOverlay.holder),i=this.instance.view.wt.wtOverlays.topOverlay.trimmingContainer==window?0:m(this.instance.view.wt.wtOverlays.topOverlay.holder),s=y(o),a=C(o),l={x:r,y:i};this.editorContainer.style.top=parseInt(s.top+b(o)-l.y+k.cellPointer.height,10)+"px",this.editorContainer.style.left=parseInt(window.innerWidth/2-k.editorContainer.width/2,10)+"px",s.left+a/2>parseInt(this.editorContainer.style.left,10)+k.editorContainer.width?this.editorContainer.style.left=window.innerWidth-k.editorContainer.width+"px":s.left+a/2<parseInt(this.editorContainer.style.left,10)+20&&(this.editorContainer.style.left="0px"),this.cellPointer.style.left=parseInt(s.left-k.cellPointer.width/2-y(this.editorContainer).left+a/2-l.x,10)+"px"}}},M.prototype.updateEditorData=function(){var e=this.instance.getSelected(),t=this.instance.getDataAtCell(e[0],e[1]);this.row=e[0],this.col=e[1],this.setValue(t),this.updateEditorPosition()},M.prototype.prepareAndSave=function(){var e;return this.valueChanged()?(e=this.instance.getSettings().trimWhitespace?[[String.prototype.trim.call(this.getValue())]]:[[this.getValue()]],void this.saveValue(e)):!0},M.prototype.bindEvents=function(){var e=this;this.eventManager.addEventListener(this.controls.leftButton,"touchend",function(t){e.prepareAndSave(),e.instance.selection.transformStart(0,-1,null,!0),e.updateEditorData(),t.preventDefault()}),this.eventManager.addEventListener(this.controls.rightButton,"touchend",function(t){e.prepareAndSave(),e.instance.selection.transformStart(0,1,null,!0),e.updateEditorData(),t.preventDefault()}),this.eventManager.addEventListener(this.controls.upButton,"touchend",function(t){e.prepareAndSave(),e.instance.selection.transformStart(-1,0,null,!0),e.updateEditorData(),t.preventDefault()}),this.eventManager.addEventListener(this.controls.downButton,"touchend",function(t){e.prepareAndSave(),e.instance.selection.transformStart(1,0,null,!0),e.updateEditorData(),t.preventDefault()}),this.eventManager.addEventListener(this.moveHandle,"touchstart",function(t){if(1==t.touches.length){var n=t.touches[0],o={x:e.editorContainer.offsetLeft,y:e.editorContainer.offsetTop},r={x:n.pageX-o.x,y:n.pageY-o.y};e.eventManager.addEventListener(this,"touchmove",function(t){var n=t.touches[0];e.updateEditorPosition(n.pageX-r.x,n.pageY-r.y),e.hideCellPointer(),t.preventDefault()})}}),this.eventManager.addEventListener(document.body,"touchend",function(t){w(t.target,e.editorContainer)||w(t.target,e.instance.rootElement)||e.close()}),this.eventManager.addEventListener(this.instance.view.wt.wtOverlays.leftOverlay.holder,"scroll",function(t){e.instance.view.wt.wtOverlays.leftOverlay.trimmingContainer!=window&&e.hideCellPointer()}),this.eventManager.addEventListener(this.instance.view.wt.wtOverlays.topOverlay.holder,"scroll",function(t){e.instance.view.wt.wtOverlays.topOverlay.trimmingContainer!=window&&e.hideCellPointer()})},M.prototype.destroy=function(){this.eventManager.clear(),this.editorContainer.parentNode.removeChild(this.editorContainer)},E("mobile",M)},{_baseEditor:30,editors:29,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/unicode":53}],37:[function(e,t,n){"use strict";Object.defineProperties(n,{NumericEditor:{get:function(){return c}},__esModule:{value:!0}});var o,r,i,s=(o=e("numeral"),o&&o.__esModule&&o||{"default":o})["default"],a=(r=e("editors"),r&&r.__esModule&&r||{"default":r}),l=(a.getEditor,a.registerEditor),u=(i=e("textEditor"),i&&i.__esModule&&i||{"default":i}).TextEditor,c=function(){$traceurRuntime.superConstructor(d).apply(this,arguments)},d=c;$traceurRuntime.createClass(c,{beginEditing:function(e){if("undefined"==typeof e&&this.originalValue){"undefined"!=typeof this.cellProperties.language&&s.language(this.cellProperties.language);var t=s.languageData().delimiters.decimal;e=(""+this.originalValue).replace(".",t)}$traceurRuntime.superGet(this,d.prototype,"beginEditing").call(this,e)}},{},u),l("numeric",c)},{editors:29,numeral:"numeral",textEditor:40}],38:[function(e,t,n){"use strict";Object.defineProperties(n,{PasswordEditor:{get:function(){return c}},__esModule:{value:!0}});var o,r,i,s=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}).empty,a=(r=e("editors"),r&&r.__esModule&&r||{"default":r}),l=(a.getEditor,a.registerEditor),u=(i=e("textEditor"),i&&i.__esModule&&i||{"default":i}).TextEditor,c=function(){$traceurRuntime.superConstructor(d).apply(this,arguments)},d=c;$traceurRuntime.createClass(c,{createElements:function(){$traceurRuntime.superGet(this,d.prototype,"createElements").call(this),this.TEXTAREA=document.createElement("input"),this.TEXTAREA.setAttribute("type","password"),this.TEXTAREA.className="handsontableInput",this.textareaStyle=this.TEXTAREA.style,this.textareaStyle.width=0,this.textareaStyle.height=0,s(this.TEXTAREA_PARENT),this.TEXTAREA_PARENT.appendChild(this.TEXTAREA)}},{},u),l("password",c)},{editors:29,"helpers/dom/element":45,textEditor:40}],39:[function(e,t,n){"use strict";Object.defineProperties(n,{SelectEditor:{get:function(){return S}},__esModule:{value:!0}});var o,r,i,s,a,l=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),u=l.addClass,c=l.empty,d=l.fastInnerHTML,h=l.getComputedStyle,f=l.getCssTransform,p=l.getScrollableElement,g=l.offset,m=l.outerHeight,v=l.outerWidth,w=l.resetCssTransform,y=(r=e("helpers/dom/event"),r&&r.__esModule&&r||{"default":r}).stopImmediatePropagation,b=(i=e("helpers/unicode"),i&&i.__esModule&&i||{"default":i}).KEY_CODES,C=(s=e("editors"),s&&s.__esModule&&s||{"default":s}),R=(C.getEditor,C.registerEditor),_=(a=e("_baseEditor"),a&&a.__esModule&&a||{"default":a}).BaseEditor,S=_.prototype.extend();S.prototype.init=function(){this.select=document.createElement("SELECT"),u(this.select,"htSelectEditor"),this.select.style.display="none",this.instance.rootElement.appendChild(this.select),this.registerHooks()},S.prototype.registerHooks=function(){var e=this;this.instance.addHook("afterScrollVertically",function(){return e.refreshDimensions()}),this.instance.addHook("afterColumnResize",function(){return e.refreshDimensions()}),this.instance.addHook("afterRowResize",function(){return e.refreshDimensions()})},S.prototype.prepare=function(){_.prototype.prepare.apply(this,arguments);var e,t=this.cellProperties.selectOptions;e="function"==typeof t?this.prepareOptions(t(this.row,this.col,this.prop)):this.prepareOptions(t),c(this.select);for(var n in e)if(e.hasOwnProperty(n)){var o=document.createElement("OPTION");o.value=n,d(o,e[n]),this.select.appendChild(o)}},S.prototype.prepareOptions=function(e){var t={};if(Array.isArray(e))for(var n=0,o=e.length;o>n;n++)t[e[n]]=e[n];else"object"==typeof e&&(t=e);return t},S.prototype.getValue=function(){return this.select.value},S.prototype.setValue=function(e){this.select.value=e};var E=function(e){var t=this,n=t.getActiveEditor();switch(e.keyCode){case b.ARROW_UP:var o=n.select.selectedIndex-1;o>=0&&(n.select[o].selected=!0),y(e),e.preventDefault();break;case b.ARROW_DOWN:var r=n.select.selectedIndex+1;r<=n.select.length-1&&(n.select[r].selected=!0),y(e),e.preventDefault()}};S.prototype.open=function(){this._opened=!0,this.refreshDimensions(),this.select.style.display="",this.instance.addHook("beforeKeyDown",E)},S.prototype.close=function(){this._opened=!1,this.select.style.display="none",this.instance.removeHook("beforeKeyDown",E)},S.prototype.focus=function(){this.select.focus()},S.prototype.refreshDimensions=function(){if(this.state===Handsontable.EditorState.EDITING){if(this.TD=this.getEditedCell(),!this.TD)return void this.close();var e,t=v(this.TD)+1,n=m(this.TD)+1,o=g(this.TD),r=g(this.instance.rootElement),i=p(this.TD),s=o.top-r.top-1-(i.scrollTop||0),a=o.left-r.left-1-(i.scrollLeft||0),l=this.checkEditorSection(),u=this.instance.getSettings();u.rowHeaders?1:0,u.colHeaders?1:0;switch(l){case"top":e=f(this.instance.view.wt.wtOverlays.topOverlay.clone.wtTable.holder.parentNode);break;case"left":e=f(this.instance.view.wt.wtOverlays.leftOverlay.clone.wtTable.holder.parentNode);break;case"top-left-corner":e=f(this.instance.view.wt.wtOverlays.topLeftCornerOverlay.clone.wtTable.holder.parentNode);break;case"bottom-left-corner":e=f(this.instance.view.wt.wtOverlays.bottomLeftCornerOverlay.clone.wtTable.holder.parentNode);break;case"bottom":e=f(this.instance.view.wt.wtOverlays.bottomOverlay.clone.wtTable.holder.parentNode)}0===this.instance.getSelected()[0]&&(s+=1),0===this.instance.getSelected()[1]&&(a+=1);var c=this.select.style;e&&-1!=e?c[e[0]]=e[1]:w(this.select);var d=h(this.TD);parseInt(d.borderTopWidth,10)>0&&(n-=1),parseInt(d.borderLeftWidth,10)>0&&(t-=1),c.height=n+"px",c.minWidth=t+"px",c.top=s+"px",c.left=a+"px",c.margin="0px"}},S.prototype.getEditedCell=function(){var e,t=this.checkEditorSection();switch(t){case"top":e=this.instance.view.wt.wtOverlays.topOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.select.style.zIndex=101;break;case"corner":e=this.instance.view.wt.wtOverlays.topLeftCornerOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.select.style.zIndex=103;break;case"left":e=this.instance.view.wt.wtOverlays.leftOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.select.style.zIndex=102;break;default:e=this.instance.getCell(this.row,this.col),this.select.style.zIndex=""}return-1!=e&&-2!=e?e:void 0},R("select",S)},{_baseEditor:30,editors:29,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/unicode":53}],40:[function(e,t,n){"use strict";Object.defineProperties(n,{TextEditor:{get:function(){return H}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),d=c.addClass,h=c.getCaretPosition,f=c.getComputedStyle,p=c.getCssTransform,g=c.getScrollableElement,m=c.innerWidth,v=c.offset,w=c.resetCssTransform,y=c.setCaretPosition,b=(r=e("autoResize"),r&&r.__esModule&&r||{"default":r})["default"],C=(i=e("_baseEditor"),i&&i.__esModule&&i||{"default":i}).BaseEditor,R=(s=e("eventManager"),s&&s.__esModule&&s||{"default":s}).eventManager,_=(a=e("editors"),a&&a.__esModule&&a||{"default":a}),S=(_.getEditor,_.registerEditor),E=(l=e("helpers/unicode"),l&&l.__esModule&&l||{"default":l}).KEY_CODES,T=(u=e("helpers/dom/event"),u&&u.__esModule&&u||{"default":u}),O=T.stopPropagation,M=T.stopImmediatePropagation,k=T.isImmediatePropagationStopped,H=C.prototype.extend();H.prototype.init=function(){var e=this;this.createElements(),this.eventManager=R(this),this.bindEvents(),this.autoResize=b(),this.instance.addHook("afterDestroy",function(){e.destroy()})},H.prototype.getValue=function(){return this.TEXTAREA.value},H.prototype.setValue=function(e){this.TEXTAREA.value=e};var D=function(e){var t,n=this,o=n.getActiveEditor();if(t=(e.ctrlKey||e.metaKey)&&!e.altKey,e.target===o.TEXTAREA&&!k(e)){if(17===e.keyCode||224===e.keyCode||91===e.keyCode||93===e.keyCode)return void M(e);switch(e.keyCode){case E.ARROW_RIGHT:o.isInFullEditMode()&&(!o.isWaiting()&&!o.allowKeyEventPropagation||!o.isWaiting()&&o.allowKeyEventPropagation&&!o.allowKeyEventPropagation(e.keyCode))&&M(e);break;case E.ARROW_LEFT:o.isInFullEditMode()&&(!o.isWaiting()&&!o.allowKeyEventPropagation||!o.isWaiting()&&o.allowKeyEventPropagation&&!o.allowKeyEventPropagation(e.keyCode))&&M(e);break;case E.ARROW_UP:case E.ARROW_DOWN:o.isInFullEditMode()&&(!o.isWaiting()&&!o.allowKeyEventPropagation||!o.isWaiting()&&o.allowKeyEventPropagation&&!o.allowKeyEventPropagation(e.keyCode))&&M(e);break;case E.ENTER:var r=o.instance.getSelected(),i=!(r[0]===r[2]&&r[1]===r[3]);if(t&&!i||e.altKey){if(o.isOpened()){var s=h(o.TEXTAREA),a=o.getValue(),l=a.slice(0,s)+"\n"+a.slice(s);o.setValue(l),y(o.TEXTAREA,s+1)}else o.beginEditing(o.originalValue+"\n");M(e)}e.preventDefault();break;case E.A:case E.X:case E.C:case E.V:t&&M(e);break;case E.BACKSPACE:case E.DELETE:case E.HOME:case E.END:M(e)}-1===[E.ARROW_UP,E.ARROW_RIGHT,E.ARROW_DOWN,E.ARROW_LEFT].indexOf(e.keyCode)&&o.autoResize.resize(String.fromCharCode(e.keyCode))}};H.prototype.open=function(){this.refreshDimensions(),this.instance.addHook("beforeKeyDown",D)},H.prototype.close=function(e){this.textareaParentStyle.display="none",this.autoResize.unObserve(),document.activeElement===this.TEXTAREA&&this.instance.listen(),this.instance.removeHook("beforeKeyDown",D)},H.prototype.focus=function(){this.TEXTAREA.focus(),y(this.TEXTAREA,this.TEXTAREA.value.length)},H.prototype.createElements=function(){this.TEXTAREA=document.createElement("TEXTAREA"),d(this.TEXTAREA,"handsontableInput"),this.textareaStyle=this.TEXTAREA.style,this.textareaStyle.width=0,this.textareaStyle.height=0,this.TEXTAREA_PARENT=document.createElement("DIV"),d(this.TEXTAREA_PARENT,"handsontableInputHolder"),this.textareaParentStyle=this.TEXTAREA_PARENT.style,this.textareaParentStyle.top=0,this.textareaParentStyle.left=0,this.textareaParentStyle.display="none",this.TEXTAREA_PARENT.appendChild(this.TEXTAREA),this.instance.rootElement.appendChild(this.TEXTAREA_PARENT);var e=this;this.instance._registerTimeout(setTimeout(function(){e.refreshDimensions()},0))},H.prototype.getEditedCell=function(){var e,t=this.checkEditorSection();switch(t){case"top":e=this.instance.view.wt.wtOverlays.topOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.textareaParentStyle.zIndex=101;break;case"top-left-corner":e=this.instance.view.wt.wtOverlays.topLeftCornerOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.textareaParentStyle.zIndex=103;break;case"bottom-left-corner":e=this.instance.view.wt.wtOverlays.bottomLeftCornerOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.textareaParentStyle.zIndex=103;break;case"left":e=this.instance.view.wt.wtOverlays.leftOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.textareaParentStyle.zIndex=102;break;case"bottom":e=this.instance.view.wt.wtOverlays.bottomOverlay.clone.wtTable.getCell({row:this.row,col:this.col}),this.textareaParentStyle.zIndex=102;break;default:e=this.instance.getCell(this.row,this.col),this.textareaParentStyle.zIndex=""}return-1!=e&&-2!=e?e:void 0},H.prototype.refreshDimensions=function(){if(this.state===Handsontable.EditorState.EDITING){if(this.TD=this.getEditedCell(),!this.TD)return void this.close(!0);var e,t=v(this.TD),n=v(this.instance.rootElement),o=g(this.TD),r=this.instance.countRows(),i=t.top-n.top-1-(o.scrollTop||0),s=t.left-n.left-1-(o.scrollLeft||0),a=this.instance.getSettings(),l=(a.rowHeaders?1:0,a.colHeaders?1:0),u=this.checkEditorSection(),c=this.TD.style.backgroundColor;switch(u){case"top":e=p(this.instance.view.wt.wtOverlays.topOverlay.clone.wtTable.holder.parentNode);break;case"left":e=p(this.instance.view.wt.wtOverlays.leftOverlay.clone.wtTable.holder.parentNode);break;case"top-left-corner":e=p(this.instance.view.wt.wtOverlays.topLeftCornerOverlay.clone.wtTable.holder.parentNode);break;case"bottom-left-corner":e=p(this.instance.view.wt.wtOverlays.bottomLeftCornerOverlay.clone.wtTable.holder.parentNode);break;case"bottom":e=p(this.instance.view.wt.wtOverlays.bottomOverlay.clone.wtTable.holder.parentNode)}(l&&0===this.instance.getSelected()[0]||a.fixedRowsBottom&&this.instance.getSelected()[0]===r-a.fixedRowsBottom)&&(i+=1),0===this.instance.getSelected()[1]&&(s+=1),e&&-1!=e?this.textareaParentStyle[e[0]]=e[1]:w(this.textareaParentStyle),this.textareaParentStyle.top=i+"px",this.textareaParentStyle.left=s+"px";var d=this.TD.offsetTop-this.instance.view.wt.wtOverlays.topOverlay.getScrollPosition(),h=this.TD.offsetLeft-this.instance.view.wt.wtOverlays.leftOverlay.getScrollPosition(),y=m(this.TD)-8,b=this.instance.view.maximumVisibleElementWidth(h)-9,C=this.TD.scrollHeight+1,R=Math.max(this.instance.view.maximumVisibleElementHeight(d)-2,23),_=f(this.TD);this.TEXTAREA.style.fontSize=_.fontSize,this.TEXTAREA.style.fontFamily=_.fontFamily,this.TEXTAREA.style.backgroundColor="",this.TEXTAREA.style.backgroundColor=c?c:f(this.TEXTAREA).backgroundColor,this.autoResize.init(this.TEXTAREA,{minHeight:Math.min(C,R),maxHeight:R,minWidth:Math.min(y,b),maxWidth:b},!0),this.textareaParentStyle.display="block"}},H.prototype.bindEvents=function(){var e=this;this.eventManager.addEventListener(this.TEXTAREA,"cut",function(e){O(e)}),this.eventManager.addEventListener(this.TEXTAREA,"paste",function(e){O(e)}),this.instance.addHook("afterScrollVertically",function(){e.refreshDimensions()}),this.instance.addHook("afterColumnResize",function(){e.refreshDimensions(),e.focus()}),this.instance.addHook("afterRowResize",function(){e.refreshDimensions(),e.focus()}),this.instance.addHook("afterDestroy",function(){e.eventManager.destroy()})},H.prototype.destroy=function(){this.eventManager.destroy()},S("text",H)},{_baseEditor:30,autoResize:"autoResize",editors:29,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/unicode":53}],41:[function(e,t,n){"use strict";function o(e,t){var n,o,r,i,s,a="HOT-TABLE";if(t.isTargetWebComponent=!1,t.realTarget=t.target,!Handsontable.eventManager.isHotTableEnv)return t;for(t=l(t),s=t.path?t.path.length:0;s--;){if(t.path[s].nodeName===a)n=!0;else if(n&&t.path[s].shadowRoot){i=t.path[s];break}0!==s||i||(i=t.path[s])}return i||(i=t.target),t.isTargetWebComponent=!0,c()?t.realTarget=t.srcElement||t.toElement:(e instanceof Handsontable.Core||e instanceof Walkontable)&&(e instanceof Handsontable.Core?o=e.view?e.view.wt.wtTable.TABLE:null:e instanceof Walkontable&&(o=e.wtTable.TABLE.parentNode.parentNode),r=u(t.target,[a],o),r?t.realTarget=o.querySelector(a)||t.target:t.realTarget=t.target),Object.defineProperty(t,"target",{get:function(){return l(i)},enumerable:!0,configurable:!0}),t}function r(e){return new d(e)}Object.defineProperties(n,{EventManager:{get:function(){return d}},eventManager:{get:function(){return r}},__esModule:{value:!0}});var i,s,a=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}),l=a.polymerWrap,u=a.closest,c=(s=e("helpers/browser"),s&&s.__esModule&&s||{"default":s}).isWebComponentSupportedNatively,d=function(){var e=void 0!==arguments[0]?arguments[0]:null;this.context=e||this,this.context.eventListeners||(this.context.eventListeners=[])};$traceurRuntime.createClass(d,{addEventListener:function(e,t,n){function r(e){void 0==e.target&&void 0!=e.srcElement&&(e.definePoperty?e.definePoperty("target",{value:e.srcElement}):e.target=e.srcElement),void 0==e.preventDefault&&(e.definePoperty?e.definePoperty("preventDefault",{value:function(){this.returnValue=!1}}):e.preventDefault=function(){this.returnValue=!1}),e=o(s,e),n.call(this,e)}var i=this,s=this.context;return this.context.eventListeners.push({element:e,event:t,callback:n,callbackProxy:r}),window.addEventListener?e.addEventListener(t,r,!1):e.attachEvent("on"+t,r),Handsontable.countEventManagerListeners++,function(){i.removeEventListener(e,t,n)}},removeEventListener:function(e,t,n){for(var o,r=this.context.eventListeners.length;r--;)if(o=this.context.eventListeners[r],o.event==t&&o.element==e){if(n&&n!=o.callback)continue;this.context.eventListeners.splice(r,1),o.element.removeEventListener?o.element.removeEventListener(o.event,o.callbackProxy,!1):o.element.detachEvent("on"+o.event,o.callbackProxy),Handsontable.countEventManagerListeners--}},clearEvents:function(){if(this.context)for(var e=this.context.eventListeners.length;e--;){var t=this.context.eventListeners[e];t&&this.removeEventListener(t.element,t.event,t.callback)}},clear:function(){this.clearEvents()},destroy:function(){this.clearEvents(),this.context=null},fireEvent:function(e,t){var n,o={bubbles:!0,cancelable:"mousemove"!==t,view:window,detail:0,screenX:0,screenY:0,clientX:1,clientY:1,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:void 0};document.createEvent?(n=document.createEvent("MouseEvents"),n.initMouseEvent(t,o.bubbles,o.cancelable,o.view,o.detail,o.screenX,o.screenY,o.clientX,o.clientY,o.ctrlKey,o.altKey,o.shiftKey,o.metaKey,o.button,o.relatedTarget||document.body.parentNode)):n=document.createEventObject(),e.dispatchEvent?e.dispatchEvent(n):e.fireEvent("on"+t,n)}},{}),window.Handsontable=window.Handsontable||{},Handsontable.countEventManagerListeners=0,Handsontable.eventManager=r},{"helpers/browser":43,"helpers/dom/element":45}],42:[function(e,t,n){"use strict";function o(e){for(var t=0,n=e.length;n>t;)e[t]=[e[t]],t++}function r(e,t){for(var n=0,o=t.length;o>n;)e.push(t[n]),n++}function i(e){var t=[];if(!e||0===e.length||!e[0]||0===e[0].length)return t;for(var n=e.length,o=e[0].length,r=0;n>r;r++)for(var i=0;o>i;i++)t[i]||(t[i]=[]),t[i][r]=e[r][i];return t}function s(e,t,n,o){var r=-1,i=e.length;for(o&&i&&(n=e[++r]);++r<i;)n=t(n,e[r],r,e);return n}function a(e,t){for(var n=-1,o=e.length,r=-1,i=[];++n<o;){var s=e[n];t(s,n,e)&&(i[++r]=s)}return i}function l(e,t){for(var n=-1,o=e.length,r=-1,i=[];++n<o;){var s=e[n];i[++r]=t(s,n,e)}return i}function u(e,t){for(var n=-1,o=e.length;++n<o&&t(e[n],n,e)!==!1;);return e}function c(e){return s(e,function(e,t){return e+t},0)}function d(e){return s(e,function(e,t){return e>t?e:t},Array.isArray(e)?e[0]:void 0)}function h(e){return s(e,function(e,t){return t>e?e:t},Array.isArray(e)?e[0]:void 0)}function f(e){return e.length?c(e)/e.length:0}function p(e){return s(e,function(e,t){return e.concat(Array.isArray(t)?p(t):t)},[])}function g(e){var t=[];return u(e,function(e){-1===t.indexOf(e)&&t.push(e)}),t}Object.defineProperties(n,{to2dArray:{get:function(){return o}},extendArray:{get:function(){return r}},pivot:{get:function(){return i}},arrayReduce:{get:function(){return s}},arrayFilter:{get:function(){return a}},arrayMap:{get:function(){return l}},arrayEach:{get:function(){return u}},arraySum:{get:function(){return c}},arrayMax:{get:function(){return d}},arrayMin:{get:function(){return h}},arrayAvg:{get:function(){return f}},arrayFlatten:{get:function(){return p}},arrayUnique:{get:function(){return g}},__esModule:{value:!0}})},{}],43:[function(e,t,n){"use strict";function o(){return f}function r(){return p}function i(){return g}function s(){return m}function a(e){return e||(e=navigator.userAgent),/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)}function l(){return"ontouchstart"in window}function u(){var e=document.createElement("div");return e.createShadowRoot&&e.createShadowRoot.toString().match(/\[native code\]/)?!0:!1}function c(){var e=document.createElement("TABLE");e.style.borderSpacing=0,e.style.borderWidth=0,e.style.padding=0;var t=document.createElement("TBODY");e.appendChild(t),t.appendChild(document.createElement("TR")),t.firstChild.appendChild(document.createElement("TD")),t.firstChild.firstChild.innerHTML="<tr><td>t<br>t</td></tr>";var n=document.createElement("CAPTION");n.innerHTML="c<br>c<br>c<br>c",n.style.padding=0,n.style.margin=0,e.insertBefore(n,t),document.body.appendChild(e),h=e.offsetHeight<2*e.lastChild.offsetHeight,document.body.removeChild(e)}function d(){return void 0===h&&c(),h}Object.defineProperties(n,{isIE8:{get:function(){return o}},isIE9:{get:function(){return r}},isSafari:{get:function(){return i}},isChrome:{get:function(){return s}},isMobileBrowser:{get:function(){return a}},isTouchSupported:{get:function(){return l}},isWebComponentSupportedNatively:{get:function(){return u}},hasCaptionProblem:{get:function(){return d}},__esModule:{value:!0}});var h,f=!document.createTextNode("test").textContent,p=!!document.documentMode,g=/Safari/.test(navigator.userAgent)&&/Apple Computer/.test(navigator.vendor),m=/Chrome/.test(navigator.userAgent)&&/Google/.test(navigator.vendor)},{}],44:[function(e,t,n){"use strict";function o(e){for(var t,n=e+1,o="";n>0;)t=(n-1)%26,o=String.fromCharCode(65+t)+o,n=parseInt((n-t)/26,10);return o}function r(e,t){e="number"==typeof e?e:100,t="number"==typeof t?t:4;var n,r,i=[];for(n=0;e>n;n++){var s=[];for(r=0;t>r;r++)s.push(o(r)+(n+1));i.push(s)}return i}function i(e,t){e="number"==typeof e?e:100,t="number"==typeof t?t:4;var n,r,i=[];for(n=0;e>n;n++){var s={};for(r=0;t>r;r++)s["prop"+r]=o(r)+(n+1);i.push(s)}return i}function s(e,t){for(var n,o=[],r=0;e>r;r++){n=[];for(var i=0;t>i;i++)n.push("");o.push(n)}return o}function a(e){var t,n,o,r,i=[],s=0;for(t=0,n=e.length;n>t;t++)for(o=0,r=e[t].length;r>o;o++)o==s&&(i.push([]),s++),i[o].push(e[t][o]);return i}function l(e,t){function n(e){var t=Handsontable.cellTypes[e];if("undefined"==typeof t)throw new Error('You declared cell type "'+e+'" as a string that is not mapped to a known object. Cell type must be an object or a string mapped to an object in Handsontable.cellTypes');return t}return t="undefined"==typeof t?!0:t,function(o,r){return function i(o){if(o){if(o.hasOwnProperty(e)&&void 0!==o[e])return o[e];if(o.hasOwnProperty("type")&&o.type){var r;if("string"!=typeof o.type)throw new Error("Cell type must be a string ");if(r=n(o.type),r.hasOwnProperty(e))return r[e];if(t)return}return i(c(o))}}("number"==typeof o?this.getCellMeta(o,r):o)}}Object.defineProperties(n,{spreadsheetColumnLabel:{get:function(){return o}},createSpreadsheetData:{get:function(){return r}},createSpreadsheetObjectData:{get:function(){return i}},createEmptySpreadsheetData:{get:function(){return s}},translateRowsToColumns:{get:function(){return a}},cellMethodLookupFactory:{get:function(){return l}},__esModule:{value:!0}});var u,c=(u=e("object"),u&&u.__esModule&&u||{"default":u}).getPrototypeOf},{object:50}],45:[function(e,t,n){"use strict";function o(e,t,n){for(;null!=e&&e!==n;){if(e.nodeType===Node.ELEMENT_NODE&&(t.indexOf(e.nodeName)>-1||t.indexOf(e)>-1))return e;e=e.host&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.host:e.parentNode}return null}function r(e,t){var n=e.parentNode,o=[];for("string"==typeof t?o=Array.prototype.slice.call(document.querySelectorAll(t),0):o.push(t);null!=n;){if(o.indexOf(n)>-1)return!0;n=n.parentNode}return!1}function i(e){function t(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName===o.toUpperCase()}var n,o="hot-table",r=!1;for(n=s(e);null!=n;){if(t(n)){r=!0;break}if(n.host&&n.nodeType===Node.DOCUMENT_FRAGMENT_NODE){if(r=t(n.host))break;n=n.host}n=n.parentNode}return r}function s(e){return"undefined"!=typeof Polymer&&"function"==typeof wrap?wrap(e):e}function a(e){return"undefined"!=typeof Polymer&&"function"==typeof unwrap?unwrap(e):e}function l(e){var t=0;if(e.previousSibling)for(;e=e.previousSibling;)++t;return t}function u(e,t){var n=document.querySelector(".ht_clone_"+e);return n?n.contains(t):null}function c(e){var t=0,n=[];if(!e||!e.length)return n;for(;e[t];)n.push(e[t]),t++;return n}function d(e,t){return K(e,t)}function h(e,t){return X(e,t)}function f(e,t){return q(e,t)}function p(e,t){if(3===e.nodeType)t.removeChild(e);else if(["TABLE","THEAD","TBODY","TFOOT","TR"].indexOf(e.nodeName)>-1)for(var n=e.childNodes,o=n.length-1;o>=0;o--)p(n[o],e)}function g(e){for(var t;t=e.lastChild;)e.removeChild(t)}function m(e,t){se.test(t)?e.innerHTML=t:v(e,t)}function v(e,t){var n=e.firstChild;n&&3===n.nodeType&&null===n.nextSibling?ae?n.textContent=t:n.data=t:(g(e),e.appendChild(document.createTextNode(t)))}function w(e){for(var t=e;a(t)!==document.documentElement;){if(null===t)return!1;if(t.nodeType===Node.DOCUMENT_FRAGMENT_NODE){if(t.host){if(t.host.impl)return w(t.host.impl);if(t.host)return w(t.host);throw new Error("Lost in Web Components world")}return!1}if("none"===t.style.display)return!1;t=t.parentNode}return!0}function y(e){var t,n,o,r,i;if(r=document.documentElement,te()&&e.firstChild&&"CAPTION"===e.firstChild.nodeName)return i=e.getBoundingClientRect(),{top:i.top+(window.pageYOffset||r.scrollTop)-(r.clientTop||0),left:i.left+(window.pageXOffset||r.scrollLeft)-(r.clientLeft||0)};for(t=e.offsetLeft,n=e.offsetTop,o=e;(e=e.offsetParent)&&e!==document.body;)t+=e.offsetLeft,n+=e.offsetTop,o=e;return o&&"fixed"===o.style.position&&(t+=window.pageXOffset||r.scrollLeft,n+=window.pageYOffset||r.scrollTop),{left:t,top:n}}function b(){var e=window.scrollY;return void 0===e&&(e=document.documentElement.scrollTop),e}function C(){var e=window.scrollX;return void 0===e&&(e=document.documentElement.scrollLeft),e}function R(e){return e===window?b():e.scrollTop}function _(e){return e===window?C():e.scrollLeft}function S(e){for(var t,n,o,r=e.parentNode,i=["auto","scroll"],s="",a="",l="",u="";r&&r.style&&document.body!==r;){if(t=r.style.overflow,n=r.style.overflowX,o=r.style.overflowY,"scroll"==t||"scroll"==n||"scroll"==o)return r;if(window.getComputedStyle&&(s=window.getComputedStyle(r),a=s.getPropertyValue("overflow"),l=s.getPropertyValue("overflow-y"),u=s.getPropertyValue("overflow-x"),"scroll"===a||"scroll"===u||"scroll"===l))return r;if(r.clientHeight<=r.scrollHeight&&(-1!==i.indexOf(o)||-1!==i.indexOf(t)||-1!==i.indexOf(a)||-1!==i.indexOf(l)))return r;if(r.clientWidth<=r.scrollWidth&&(-1!==i.indexOf(n)||-1!==i.indexOf(t)||-1!==i.indexOf(a)||-1!==i.indexOf(u)))return r;r=r.parentNode}return window}function E(e){for(var t=e.parentNode;t&&t.style&&document.body!==t;){if("visible"!==t.style.overflow&&""!==t.style.overflow)return t;if(window.getComputedStyle){var n=window.getComputedStyle(t);if("visible"!==n.getPropertyValue("overflow")&&""!==n.getPropertyValue("overflow"))return t;
}t=t.parentNode}return window}function T(e,t){if(e){if(e!==window){var n,o=e.style[t];return""!==o&&void 0!==o?o:(n=O(e),""!==n[t]&&void 0!==n[t]?n[t]:void 0)}if("width"===t)return window.innerWidth+"px";if("height"===t)return window.innerHeight+"px"}}function O(e){return e.currentStyle||document.defaultView.getComputedStyle(e)}function M(e){return e.offsetWidth}function k(e){return te()&&e.firstChild&&"CAPTION"===e.firstChild.nodeName?e.offsetHeight+e.firstChild.offsetHeight:e.offsetHeight}function H(e){return e.clientHeight||e.innerHeight}function D(e){return e.clientWidth||e.innerWidth}function x(e,t,n){window.addEventListener?e.addEventListener(t,n,!1):e.attachEvent("on"+t,n)}function A(e,t,n){window.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent("on"+t,n)}function P(e){if(e.selectionStart)return e.selectionStart;if(document.selection){e.focus();var t=document.selection.createRange();if(null==t)return 0;var n=e.createTextRange(),o=n.duplicate();return n.moveToBookmark(t.getBookmark()),o.setEndPoint("EndToStart",n),o.text.length}return 0}function N(e){if(e.selectionEnd)return e.selectionEnd;if(document.selection){var t=document.selection.createRange();if(null==t)return 0;var n=e.createTextRange();return n.text.indexOf(t.text)+t.text.length}}function L(){var e="";return window.getSelection?e=window.getSelection().toString():document.selection&&"Control"!==document.selection.type&&(e=document.selection.createRange().text),e}function W(e,t,n){if(void 0===n&&(n=t),e.setSelectionRange){e.focus();try{e.setSelectionRange(t,n)}catch(o){var r=e.parentNode,i=r.style.display;r.style.display="block",e.setSelectionRange(t,n),r.style.display=i}}else if(e.createTextRange){var s=e.createTextRange();s.collapse(!0),s.moveEnd("character",n),s.moveStart("character",t),s.select()}}function I(){var e=document.createElement("p");e.style.width="100%",e.style.height="200px";var t=document.createElement("div");t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.appendChild(e),(document.body||document.documentElement).appendChild(t);var n=e.offsetWidth;t.style.overflow="scroll";var o=e.offsetWidth;return n==o&&(o=t.clientWidth),(document.body||document.documentElement).removeChild(t),n-o}function j(){return void 0===ie&&(ie=I()),ie}function B(e,t,n){J()||Q()?(e.style.top=n,e.style.left=t):ee()?e.style["-webkit-transform"]="translate3d("+t+","+n+",0)":e.style.transform="translate3d("+t+","+n+",0)"}function F(e){var t;return e.style.transform&&""!==(t=e.style.transform)?["transform",t]:e.style["-webkit-transform"]&&""!==(t=e.style["-webkit-transform"])?["-webkit-transform",t]:-1}function V(e){e.transform&&""!==e.transform?e.transform="":e["-webkit-transform"]&&""!==e["-webkit-transform"]&&(e["-webkit-transform"]="")}function z(e){var t=["INPUT","SELECT","TEXTAREA"];return t.indexOf(e.nodeName)>-1||"true"===e.contentEditable}function Y(e){return z(e)&&-1==e.className.indexOf("handsontableInput")&&-1==e.className.indexOf("copyPaste")}function U(e){return ce.call(window,e)}function G(e){de.call(window,e)}Object.defineProperties(n,{closest:{get:function(){return o}},isChildOf:{get:function(){return r}},isChildOfWebComponentTable:{get:function(){return i}},polymerWrap:{get:function(){return s}},polymerUnwrap:{get:function(){return a}},index:{get:function(){return l}},overlayContainsElement:{get:function(){return u}},hasClass:{get:function(){return d}},addClass:{get:function(){return h}},removeClass:{get:function(){return f}},removeTextNodes:{get:function(){return p}},empty:{get:function(){return g}},HTML_CHARACTERS:{get:function(){return se}},fastInnerHTML:{get:function(){return m}},fastInnerText:{get:function(){return v}},isVisible:{get:function(){return w}},offset:{get:function(){return y}},getWindowScrollTop:{get:function(){return b}},getWindowScrollLeft:{get:function(){return C}},getScrollTop:{get:function(){return R}},getScrollLeft:{get:function(){return _}},getScrollableElement:{get:function(){return S}},getTrimmingContainer:{get:function(){return E}},getStyle:{get:function(){return T}},getComputedStyle:{get:function(){return O}},outerWidth:{get:function(){return M}},outerHeight:{get:function(){return k}},innerHeight:{get:function(){return H}},innerWidth:{get:function(){return D}},addEvent:{get:function(){return x}},removeEvent:{get:function(){return A}},getCaretPosition:{get:function(){return P}},getSelectionEndPosition:{get:function(){return N}},getSelectionText:{get:function(){return L}},setCaretPosition:{get:function(){return W}},getScrollbarWidth:{get:function(){return j}},setOverlayPosition:{get:function(){return B}},getCssTransform:{get:function(){return F}},resetCssTransform:{get:function(){return V}},isInput:{get:function(){return z}},isOutsideInput:{get:function(){return Y}},requestAnimationFrame:{get:function(){return U}},cancelAnimationFrame:{get:function(){return G}},__esModule:{value:!0}});var $,K,X,q,Z=($=e("../browser"),$&&$.__esModule&&$||{"default":$}),J=Z.isIE8,Q=Z.isIE9,ee=Z.isSafari,te=Z.hasCaptionProblem,ne=document.documentElement.classList?!0:!1;if(ne){var oe=function(){var e=document.createElement("div");return e.classList.add("test","test2"),e.classList.contains("test2")}();K=function(e,t){return""===t?!1:e.classList.contains(t)},X=function(e,t){var n=0;if("string"==typeof t&&(t=t.split(" ")),t=c(t),oe)e.classList.add.apply(e.classList,t);else for(;t&&t[n];)e.classList.add(t[n]),n++},q=function(e,t){var n=0;if("string"==typeof t&&(t=t.split(" ")),t=c(t),oe)e.classList.remove.apply(e.classList,t);else for(;t&&t[n];)e.classList.remove(t[n]),n++}}else{var re=function(e){return new RegExp("(\\s|^)"+e+"(\\s|$)")};K=function(e,t){return e.className.match(re(t))?!0:!1},X=function(e,t){var n=0,o=e.className;if("string"==typeof t&&(t=t.split(" ")),""===o)o=t.join(" ");else for(;t&&t[n];)re(t[n]).test(o)||(o+=" "+t[n]),n++;e.className=o},q=function(e,t){var n=0,o=e.className;for("string"==typeof t&&(t=t.split(" "));t&&t[n];)o=o.replace(re(t[n])," ").trim(),n++;e.className!==o&&(e.className=o)}}for(var ie,se=/(<(.*)>|&(.*);)/,ae=document.createTextNode("test").textContent?!0:!1,le=0,ue=["ms","moz","webkit","o"],ce=window.requestAnimationFrame,de=window.cancelAnimationFrame,he=0;he<ue.length&&!ce;++he)ce=window[ue[he]+"RequestAnimationFrame"],de=window[ue[he]+"CancelAnimationFrame"]||window[ue[he]+"CancelRequestAnimationFrame"];ce||(ce=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-le)),o=window.setTimeout(function(){e(t+n)},n);return le=t+n,o}),de||(de=function(e){clearTimeout(e)})},{"../browser":43}],46:[function(e,t,n){"use strict";function o(e){e.isImmediatePropagationEnabled=!1,e.cancelBubble=!0}function r(e){return e.isImmediatePropagationEnabled===!1}function i(e){"function"==typeof e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function s(e){if(e.pageX)return e.pageX;var t=getWindowScrollLeft(),n=e.clientX+t;return n}function a(e){if(e.pageY)return e.pageY;var t=getWindowScrollTop(),n=e.clientY+t;return n}Object.defineProperties(n,{stopImmediatePropagation:{get:function(){return o}},isImmediatePropagationStopped:{get:function(){return r}},stopPropagation:{get:function(){return i}},pageX:{get:function(){return s}},pageY:{get:function(){return a}},__esModule:{value:!0}})},{}],47:[function(e,t,n){"use strict";function o(e,t){return function(){return e.apply(t,arguments)}}function r(e){function t(){var t=this,s=arguments,a=Date.now(),l=!1;r.lastCallThrottled=!0,o||(o=a,l=!0);var u=n-(a-o);return l?(r.lastCallThrottled=!1,e.apply(this,s)):(i&&clearTimeout(i),i=setTimeout(function(){r.lastCallThrottled=!1,e.apply(t,s),o=0,i=void 0},u)),r}var n=void 0!==arguments[1]?arguments[1]:200,o=0,r={lastCallThrottled:!0},i=null;return t}function i(e){function t(){a=i}function n(){return a?(a--,e.apply(this,arguments)):s.apply(this,arguments)}var o=void 0!==arguments[1]?arguments[1]:200,i=void 0!==arguments[2]?arguments[2]:10,s=r(e,o),a=i;return n.clearHits=t,n}Object.defineProperties(n,{proxy:{get:function(){return o}},throttle:{get:function(){return r}},throttleAfterHits:{get:function(){return i}},__esModule:{value:!0}})},{}],48:[function(e,t,n){"use strict";function o(e){switch(typeof e){case"string":case"number":return e+"";case"object":return null===e?"":e.toString();case"undefined":return"";default:return e.toString()}}Object.defineProperties(n,{stringify:{get:function(){return o}},__esModule:{value:!0}})},{}],49:[function(e,t,n){"use strict";function o(e){var t=typeof e;return"number"==t?!isNaN(e)&&isFinite(e):"string"==t?e.length?1==e.length?/\d/.test(e):/^\s*[+-]?\s*(?:(?:\d+(?:\.\d+)?(?:e[+-]?\d+)?)|(?:0x[a-f\d]+))\s*$/i.test(e):!1:"object"==t?!(!e||"number"!=typeof e.valueOf()||e instanceof Date):!1}function r(e,t,n){var o=-1;for("function"==typeof t?(n=t,t=e):o=e-1;++o<=t&&n(o)!==!1;);}function i(e,t,n){var o=e+1;for("function"==typeof t&&(n=t,t=0);--o>=t&&n(o)!==!1;);}function s(e,t){return t=parseInt(t.toString().replace("%",""),10),t=parseInt(e*t/100)}Object.defineProperties(n,{isNumeric:{get:function(){return o}},rangeEach:{get:function(){return r}},rangeEachReverse:{get:function(){return i}},valueAccordingPercent:{get:function(){return s}},__esModule:{value:!0}})},{}],50:[function(e,t,n){"use strict";function o(e){var t;return Array.isArray(e)?t=[]:(t={},p(e,function(e,n){e&&"object"==typeof e&&!Array.isArray(e)?t[n]=o(e):Array.isArray(e)?e.length&&"object"==typeof e[0]&&!Array.isArray(e[0])?t[n]=[o(e[0])]:t[n]=[]:t[n]=null})),t}function r(e,t){return t.prototype.constructor=t,e.prototype=new t,e.prototype.constructor=e,e}function i(e,t){return p(t,function(t,n){e[n]=t}),e}function s(e,t){p(t,function(n,o){t[o]&&"object"==typeof t[o]?(e[o]||(Array.isArray(t[o])?e[o]=[]:e[o]={}),s(e[o],t[o])):e[o]=t[o]})}function a(e){return"object"==typeof e?JSON.parse(JSON.stringify(e)):e}function l(e){var t={};return p(e,function(e,n){return t[n]=e}),t}function u(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return e.MIXINS||(e.MIXINS=[]),v(t,function(t){e.MIXINS.push(t.MIXIN_NAME),p(t,function(t,n){if(void 0!==e.prototype[n])throw new Error("Mixin conflict. Property '"+n+"' already exist and cannot be overwritten.");if("function"==typeof t)e.prototype[n]=t;else{var o=function(e,t){e="_"+e;var n=function(e){return(Array.isArray(e)||d(e))&&(e=a(e)),e};return function(){return void 0===this[e]&&(this[e]=n(t)),this[e]}},r=function(e){return e="_"+e,function(t){this[e]=t}};Object.defineProperty(e.prototype,n,{get:o(n,t),set:r(n),configurable:!0})}})}),e}function c(e,t){return JSON.stringify(e)===JSON.stringify(t)}function d(e){return"[object Object]"==Object.prototype.toString.call(e)}function h(e){var t;if("object"==typeof e.__proto__)t=e.__proto__;else{var n,o=e.constructor;"function"==typeof e.constructor&&(n=o,delete e.constructor&&(o=e.constructor,e.constructor=n)),t=o?o.prototype:null}return t}function f(e,t,n,o){o.value=n,o.writable=o.writable!==!1,o.enumerable=o.enumerable!==!1,o.configurable=o.configurable!==!1,Object.defineProperty(e,t,o)}function p(e,t){for(var n in e)if((!e.hasOwnProperty||e.hasOwnProperty&&e.hasOwnProperty(n))&&t(e[n],n,e)===!1)break;return e}function g(e,t){var n=t.split("."),o=e;return p(n,function(e){return o=o[e],void 0===o?(o=void 0,!1):void 0}),o}Object.defineProperties(n,{duckSchema:{get:function(){return o}},inherit:{get:function(){return r}},extend:{get:function(){return i}},deepExtend:{get:function(){return s}},deepClone:{get:function(){return a}},clone:{get:function(){return l}},mixin:{get:function(){return u}},isObjectEquals:{get:function(){return c}},isObject:{get:function(){return d}},getPrototypeOf:{get:function(){return h}},defineGetter:{get:function(){return f}},objectEach:{get:function(){return p}},getProperty:{get:function(){return g}},__esModule:{value:!0}});var m,v=(m=e("array"),m&&m.__esModule&&m||{"default":m}).arrayEach},{array:42}],51:[function(e,t,n){"use strict";function o(e,t){function n(){}i(n,e);for(var o=0,r=t.length;r>o;o++)n.prototype[t[o]]=void 0;return n}Object.defineProperties(n,{columnFactory:{get:function(){return o}},__esModule:{value:!0}});var r,i=(r=e("object"),r&&r.__esModule&&r||{"default":r}).inherit},{object:50}],52:[function(e,t,n){"use strict";function o(e){return e[0].toUpperCase()+e.substr(1)}function r(e,t){var n=!0;return h(t.length-1,function(o){return e.charAt(o)!==t.charAt(o)?(n=!1,!1):void 0}),n}function i(e,t){var n=!0,o=t.length-1,r=e.length-1;return h(o,function(i){var s=r-i,a=o-i;return e.charAt(s)!==t.charAt(a)?(n=!1,!1):void 0}),n}function s(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=[],o=e.length;o--;){var r=d(e[o]).toLowerCase();-1===n.indexOf(r)&&n.push(r)}return 1===n.length}function a(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return e()+e()+e()+e()}function l(e){return/^([0-9][0-9]?\%$)|(^100\%$)/.test(e)}Object.defineProperties(n,{toUpperCaseFirst:{get:function(){return o}},startsWith:{get:function(){return r}},endsWith:{get:function(){return i}},equalsIgnoreCase:{get:function(){return s}},randomString:{get:function(){return a}},isPercentValue:{get:function(){return l}},__esModule:{value:!0}});var u,c,d=(u=e("mixed"),u&&u.__esModule&&u||{"default":u}).stringify,h=(c=e("number"),c&&c.__esModule&&c||{"default":c}).rangeEach},{mixed:48,number:49}],53:[function(e,t,n){"use strict";function o(e){return 32==e||e>=48&&57>=e||e>=96&&111>=e||e>=186&&192>=e||e>=219&&222>=e||e>=226||e>=65&&90>=e}function r(e){var t=[u.ARROW_DOWN,u.ARROW_UP,u.ARROW_LEFT,u.ARROW_RIGHT,u.HOME,u.END,u.DELETE,u.BACKSPACE,u.F1,u.F2,u.F3,u.F4,u.F5,u.F6,u.F7,u.F8,u.F9,u.F10,u.F11,u.F12,u.TAB,u.PAGE_DOWN,u.PAGE_UP,u.ENTER,u.ESCAPE,u.SHIFT,u.CAPS_LOCK,u.ALT];return-1!==t.indexOf(e)}function i(e){return-1!==[u.CONTROL_LEFT,224,u.COMMAND_LEFT,u.COMMAND_RIGHT].indexOf(e)}function s(e,t){var n=t.split("|"),o=!1;return l(n,function(t){return e===u[t]?(o=!0,!1):void 0}),o}Object.defineProperties(n,{KEY_CODES:{get:function(){return u}},isPrintableChar:{get:function(){return o}},isMetaKey:{get:function(){return r}},isCtrlKey:{get:function(){return i}},isKey:{get:function(){return s}},__esModule:{value:!0}});var a,l=(a=e("array"),a&&a.__esModule&&a||{"default":a}).arrayEach,u={MOUSE_LEFT:1,MOUSE_RIGHT:3,MOUSE_MIDDLE:2,BACKSPACE:8,COMMA:188,INSERT:45,DELETE:46,END:35,ENTER:13,ESCAPE:27,CONTROL_LEFT:91,COMMAND_LEFT:17,COMMAND_RIGHT:93,ALT:18,HOME:36,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,SPACE:32,SHIFT:16,CAPS_LOCK:20,TAB:9,ARROW_RIGHT:39,ARROW_LEFT:37,ARROW_UP:38,ARROW_DOWN:40,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,A:65,X:88,C:67,V:86}},{array:42}],54:[function(e,t,n){"use strict";Object.defineProperties(n,{localHooks:{get:function(){return l}},__esModule:{value:!0}});var o,r,i=(o=e("helpers/array"),o&&o.__esModule&&o||{"default":o}).arrayEach,s=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}).defineGetter,a="localHooks",l={_localHooks:Object.create(null),addLocalHook:function(e,t){this._localHooks[e]||(this._localHooks[e]=[]),this._localHooks[e].push(t)},runLocalHooks:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=this;this._localHooks[e]&&i(this._localHooks[e],function(e){return e.apply(o,t)})},clearLocalHooks:function(){this._localHooks={}}};s(l,"MIXIN_NAME",a,{writable:!1,enumerable:!1}),Handsontable.utils=Handsontable.utils||{},Handsontable.utils.localHooks=l},{"helpers/array":42,"helpers/object":50}],55:[function(e,t,n){"use strict";function o(){function e(e){return null!==e&&!n(e)&&("string"==typeof e||"number"==typeof e)}function t(e){return null!==e&&("object"==typeof e||"function"==typeof e)}function n(e){return e!==e}var o={arrayMap:[],weakMap:new WeakMap};return{get:function(n){return e(n)?o.arrayMap[n]:t(n)?o.weakMap.get(n):void 0},set:function(n,r){if(e(n))o.arrayMap[n]=r;else{if(!t(n))throw new Error("Invalid key type");o.weakMap.set(n,r)}},"delete":function(n){e(n)?delete o.arrayMap[n]:t(n)&&o.weakMap["delete"](n)}}}Object.defineProperties(n,{MultiMap:{get:function(){return o}},__esModule:{value:!0}}),window.MultiMap=o},{}],56:[function(e,t,n){"use strict";Object.defineProperties(n,{Hooks:{get:function(){return l}},__esModule:{value:!0}});var o,r,i=["afterCellMetaReset","afterChange","afterChangesObserved","afterColumnMove","afterColumnResize","afterContextMenuDefaultOptions","afterContextMenuHide","afterContextMenuShow","afterCopyLimit","afterCreateCol","afterCreateRow","afterDeselect","afterDestroy","afterDocumentKeyDown","afterGetCellMeta","afterGetColHeader","afterGetRowHeader","afterInit","afterIsMultipleSelectionCheck","afterLoadData","afterMomentumScroll","afterOnCellCornerMouseDown","afterOnCellMouseDown","afterOnCellMouseOver","afterRemoveCol","afterRemoveRow","afterRender","afterRenderer","afterScrollHorizontally","afterScrollVertically","afterSelection","afterSelectionByProp","afterSelectionEnd","afterSelectionEndByProp","afterSetCellMeta","afterUpdateSettings","afterValidate","beforeAutofill","beforeCellAlignment","beforeChange","beforeChangeRender","beforeDrawBorders","beforeGetCellMeta","beforeInit","beforeInitWalkontable","beforeKeyDown","beforeOnCellMouseDown","beforeRemoveCol","beforeRemoveRow","beforeRender","beforeSetRangeEnd","beforeTouchScroll","beforeValidate","construct","init","modifyCol","modifyColumnHeader","modifyColWidth","modifyRow","modifyRowHeader","modifyRowHeight","persistentStateLoad","persistentStateReset","persistentStateSave","beforeColumnSort","afterColumnSort","afterAutofillApplyValues","modifyCopyableRange","beforeColumnMove","afterColumnMove","beforeRowMove","afterRowMove","beforeColumnResize","afterColumnResize","beforeRowResize","afterRowResize"],s=(o=e("helpers/array"),o&&o.__esModule&&o||{"default":o}).arrayEach,a=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}).objectEach,l=function(){this.globalBucket=this.createEmptyBucket()};$traceurRuntime.createClass(l,{createEmptyBucket:function(){var e=Object.create(null);return s(i,function(t){return e[t]=[]}),e},getBucket:function(){var e=void 0!==arguments[0]?arguments[0]:null;return e?(e.pluginHookBucket||(e.pluginHookBucket=this.createEmptyBucket()),e.pluginHookBucket):this.globalBucket},add:function(e,t){var n=void 0!==arguments[2]?arguments[2]:null,o=this;if(Array.isArray(t))s(t,function(t){return o.add(e,t,n)});else{var r=this.getBucket(n);"undefined"==typeof r[e]&&(this.register(e),r[e]=[]),t.skip=!1,-1===r[e].indexOf(t)&&r[e].push(t)}return this},once:function(e,t){var n=void 0!==arguments[2]?arguments[2]:null,o=this;Array.isArray(t)?s(t,function(t){return o.once(e,t,n)}):(t.runOnce=!0,this.add(e,t,n))},remove:function(e,t){var n=void 0!==arguments[2]?arguments[2]:null,o=this.getBucket(n);return"undefined"!=typeof o[e]&&o[e].indexOf(t)>=0?(t.skip=!0,!0):!1},has:function(e){var t=void 0!==arguments[1]?arguments[1]:null,n=this.getBucket(t);return void 0!==n[e]&&n[e].length?!0:!1},run:function(e,t,n,o,r,i,s,a){var l=this.globalBucket[t],u=-1,c=l?l.length:0;if(c)for(;++u<c;)if(l[u]&&!l[u].skip){var d=l[u].call(e,n,o,r,i,s,a);void 0!==d&&(n=d),l[u]&&l[u].runOnce&&this.remove(t,l[u])}var h=this.getBucket(e)[t],f=-1,p=h?h.length:0;if(p)for(;++f<p;)if(h[f]&&!h[f].skip){var g=h[f].call(e,n,o,r,i,s,a);void 0!==g&&(n=g),h[f]&&h[f].runOnce&&this.remove(t,h[f],e)}return n},destroy:function(){var e=void 0!==arguments[0]?arguments[0]:null;a(this.getBucket(e),function(e,t,n){return n[t].length=0})},register:function(e){this.isRegistered(e)||i.push(e)},deregister:function(e){this.isRegistered(e)&&i.splice(i.indexOf(e),1)},isRegistered:function(e){return i.indexOf(e)>=0},getRegistered:function(){return i}},{}),Handsontable.utils=Handsontable.utils||{},Handsontable.utils.Hooks=l},{"helpers/array":42,"helpers/object":50}],57:[function(e,t,n){"use strict";function o(e,t){e=c(e),Handsontable.plugins[e]=t,Handsontable.hooks.add("construct",function(){var n;d.has(this)||d.set(this,{}),n=d.get(this),n[e]||(n[e]=new t(this))}),Handsontable.hooks.add("afterDestroy",function(){if(d.has(this)){var e=d.get(this);u(e,function(e){return e.destroy()}),d["delete"](this)}})}function r(e,t){if("string"!=typeof t)throw Error('Only strings can be passed as "plugin" parameter');var n=c(t);if(d.has(e)&&d.get(e)[n])return d.get(e)[n]}function i(e){return d.has(e)?Object.keys(d.get(e)):[]}function s(e,t){var n=null;return d.has(e)&&u(d.get(e),function(e,o){e===t&&(n=o)}),n}Object.defineProperties(n,{registerPlugin:{get:function(){return o}},getPlugin:{get:function(){return r}},getRegistredPluginNames:{get:function(){return i}},getPluginName:{get:function(){return s}},__esModule:{value:!0}});var a,l,u=(a=e("helpers/object"),a&&a.__esModule&&a||{"default":a}).objectEach,c=(l=e("helpers/string"),l&&l.__esModule&&l||{"default":l}).toUpperCaseFirst,d=new WeakMap;Handsontable.plugins=Handsontable.plugins||{},Handsontable.plugins.utils=Handsontable.plugins.utils||{},Handsontable.plugins.registerPlugin=o},{"helpers/object":50,"helpers/string":52}],58:[function(e,t,n){"use strict";Object.defineProperties(n,{"default":{get:function(){return m}},__esModule:{value:!0}});var o,r,i,s=(o=e("helpers/object"),o&&o.__esModule&&o||{"default":o}),a=s.defineGetter,l=s.objectEach,u=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}).arrayEach,c=(i=e("plugins"),i&&i.__esModule&&i||{"default":i}),d=c.getRegistredPluginNames,h=c.getPluginName,f=new WeakMap,p=null,g=function(e){var t=this;a(this,"hot",e,{writable:!1}),f.set(this,{hooks:{}}),p=null,this.pluginName=null,this.pluginsInitializedCallbacks=[],this.isPluginsReady=!1,this.enabled=!1,this.initialized=!1,this.hot.addHook("afterPluginsInitialized",function(){return t.onAfterPluginsInitialized()}),this.hot.addHook("afterUpdateSettings",function(){return t.onUpdateSettings()}),this.hot.addHook("beforeInit",function(){return t.init()})};$traceurRuntime.createClass(g,{init:function(){this.pluginName=h(this.hot,this),this.isEnabled&&this.isEnabled()&&this.enablePlugin(),p||(p=d(this.hot)),p.indexOf(this.pluginName)>=0&&p.splice(p.indexOf(this.pluginName),1),p.length||this.hot.runHooks("afterPluginsInitialized"),this.initialized=!0},enablePlugin:function(){this.enabled=!0},disablePlugin:function(){this.eventManager&&this.eventManager.clear(),this.clearHooks(),this.enabled=!1},addHook:function(e,t){var n=f.get(this).hooks[e]=f.get(this).hooks[e]||[];this.hot.addHook(e,t),n.push(t),f.get(this).hooks[e]=n},removeHooks:function(e){var t=this;u(f.get(this).hooks[e]||[],function(n){t.hot.removeHook(e,n)})},clearHooks:function(){var e=this,t=f.get(this).hooks;l(t,function(t,n){return e.removeHooks(n)}),t.length=0},callOnPluginsReady:function(e){this.isPluginsReady?e():this.pluginsInitializedCallbacks.push(e)},onAfterPluginsInitialized:function(){u(this.pluginsInitializedCallbacks,function(e){return e()}),this.pluginsInitializedCallbacks.length=0,this.isPluginsReady=!0},onUpdateSettings:function(){this.isEnabled&&(this.enabled&&!this.isEnabled()&&this.disablePlugin(),!this.enabled&&this.isEnabled()&&this.enablePlugin(),this.enabled&&this.isEnabled()&&this.updatePlugin())},updatePlugin:function(){},destroy:function(){var e=this;this.eventManager&&this.eventManager.destroy(),this.clearHooks(),l(this,function(t,n){"hot"!==n&&(e[n]=null)}),delete this.hot}},{});var m=g;Handsontable.plugins.BasePlugin=g},{"helpers/array":42,"helpers/object":50,plugins:57}],59:[function(e,t,n){"use strict";Object.defineProperties(n,{AutoColumnSize:{get:function(){return D}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c,d,h,f=(o=e("_base"),o&&o.__esModule&&o||{"default":o})["default"],p=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}),g=p.arrayEach,m=p.arrayFilter,v=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}),w=v.cancelAnimationFrame,y=v.requestAnimationFrame,b=v.isVisible,C=(s=e("utils/ghostTable"),s&&s.__esModule&&s||{"default":s}).GhostTable,R=(a=e("helpers/object"),a&&a.__esModule&&a||{"default":a}),_=R.isObject,S=(R.objectEach,l=e("helpers/number"),l&&l.__esModule&&l||{"default":l}),E=S.valueAccordingPercent,T=S.rangeEach,O=(u=e("plugins"),u&&u.__esModule&&u||{"default":u}).registerPlugin,M=(c=e("utils/samplesGenerator"),c&&c.__esModule&&c||{"default":c}).SamplesGenerator,k=(d=e("helpers/string"),d&&d.__esModule&&d||{"default":d}).isPercentValue,H=(h=e("3rdparty/walkontable/src/calculator/viewportColumns"),h&&h.__esModule&&h||{"default":h}).WalkontableViewportColumnsCalculator,D=function(e){var t=this;$traceurRuntime.superConstructor(x).call(this,e),this.widths=[],this.ghostTable=new C(this.hot),this.samplesGenerator=new M(function(e,n){return t.hot.getDataAtCell(e,n)}),this.firstCalculation=!0,this.inProgress=!1,this.addHook("beforeColumnResize",function(e,n,o){return t.onBeforeColumnResize(e,n,o)})},x=D;$traceurRuntime.createClass(D,{isEnabled:function(){return this.hot.getSettings().autoColumnSize!==!1&&!this.hot.getSettings().colWidths},enablePlugin:function(){var e=this;this.enabled||(this.addHook("afterLoadData",function(){return e.onAfterLoadData()}),this.addHook("beforeChange",function(t){return e.onBeforeChange(t)}),this.addHook("beforeRender",function(t){return e.onBeforeRender(t)}),this.addHook("modifyColWidth",function(t,n){return e.getColumnWidth(n,t)}),$traceurRuntime.superGet(this,x.prototype,"enablePlugin").call(this))},disablePlugin:function(){$traceurRuntime.superGet(this,x.prototype,"disablePlugin").call(this)},calculateColumnsWidth:function(){var e=void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countCols()-1},t=void 0!==arguments[1]?arguments[1]:{from:0,to:this.hot.countRows()-1},n=void 0!==arguments[2]?arguments[2]:!1,o=this;"number"==typeof e&&(e={from:e,to:e}),"number"==typeof t&&(t={from:t,to:t}),T(e.from,e.to,function(e){if(n||void 0===o.widths[e]&&!o.hot._getColWidthFromSettings(e)){var r=o.samplesGenerator.generateColumnSamples(e,t);r.forEach(function(e,t){return o.ghostTable.addColumn(t,e)})}}),this.ghostTable.columns.length&&(this.ghostTable.getWidths(function(e,t){return o.widths[e]=t}),this.ghostTable.clean())},calculateAllColumnsWidth:function(){var e=void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countRows()-1},t=this,n=0,o=this.hot.countCols()-1,r=null;this.inProgress=!0;var i=function(){return t.hot?(t.calculateColumnsWidth({from:n,to:Math.min(n+x.CALCULATION_STEP,o)},e),n=n+x.CALCULATION_STEP+1,void(o>n?r=y(i):(w(r),t.inProgress=!1,t.hot.view.wt.wtOverlays.adjustElementsSize(!0),t.hot.view.wt.wtOverlays.leftOverlay.needFullRender&&t.hot.view.wt.wtOverlays.leftOverlay.clone.draw()))):(w(r),void(t.inProgress=!1))};this.firstCalculation&&this.getSyncCalculationLimit()&&(this.calculateColumnsWidth({from:0,to:this.getSyncCalculationLimit()},e),this.firstCalculation=!1,n=this.getSyncCalculationLimit()+1),o>n?i():this.inProgress=!1},recalculateAllColumnsWidth:function(){this.hot.view&&b(this.hot.view.wt.wtTable.TABLE)&&(this.clearCache(),this.calculateAllColumnsWidth())},getSyncCalculationLimit:function(){var e=x.SYNC_CALCULATION_LIMIT,t=this.hot.countCols()-1;return _(this.hot.getSettings().autoColumnSize)&&(e=this.hot.getSettings().autoColumnSize.syncLimit,k(e)?e=E(t,e):e>>=0),Math.min(e,t)},getColumnWidth:function(e){var t=arguments[1],n=void 0!==arguments[2]?arguments[2]:!0,o=t;return void 0===o&&(o=this.widths[e],n&&"number"==typeof o&&(o=Math.max(o,H.DEFAULT_WIDTH))),o},getFirstVisibleColumn:function(){var e=this.hot.view.wt;return e.wtViewport.columnsVisibleCalculator?e.wtTable.getFirstVisibleColumn():e.wtViewport.columnsRenderCalculator?e.wtTable.getFirstRenderedColumn():-1},getLastVisibleColumn:function(){var e=this.hot.view.wt;return e.wtViewport.columnsVisibleCalculator?e.wtTable.getLastVisibleColumn():e.wtViewport.columnsRenderCalculator?e.wtTable.getLastRenderedColumn():-1},clearCache:function(){this.widths.length=0},isNeedRecalculate:function(){return m(this.widths,function(e){return void 0===e}).length?!0:!1},onBeforeRender:function(){var e=this.hot.renderCall;this.calculateColumnsWidth({from:this.getFirstVisibleColumn(),to:this.getLastVisibleColumn()},void 0,e),this.isNeedRecalculate()&&!this.inProgress&&this.calculateAllColumnsWidth()},onAfterLoadData:function(){var e=this;this.hot.view?this.recalculateAllColumnsWidth():setTimeout(function(){e.hot&&e.recalculateAllColumnsWidth()},0)},onBeforeChange:function(e){var t=this;g(e,function(e){return t.widths[e[1]]=void 0})},onBeforeColumnResize:function(e,t,n){return n&&(this.calculateColumnsWidth(e,void 0,!0),t=this.getColumnWidth(e,void 0,!1)),t},destroy:function(){this.ghostTable.clean(),$traceurRuntime.superGet(this,x.prototype,"destroy").call(this)}},{get CALCULATION_STEP(){return 50},get SYNC_CALCULATION_LIMIT(){return 50}},f),O("autoColumnSize",D)},{"3rdparty/walkontable/src/calculator/viewportColumns":3,_base:58,"helpers/array":42,"helpers/dom/element":45,"helpers/number":49,"helpers/object":50,"helpers/string":52,plugins:57,"utils/ghostTable":98,"utils/samplesGenerator":99}],60:[function(e,t,n){"use strict";Object.defineProperties(n,{AutoRowSize:{get:function(){return M}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c,d,h=(o=e("_base"),o&&o.__esModule&&o||{"default":o})["default"],f=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}),p=(f.arrayEach,f.arrayFilter),g=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}),m=g.cancelAnimationFrame,v=g.requestAnimationFrame,w=g.isVisible,y=(s=e("utils/ghostTable"),s&&s.__esModule&&s||{"default":s}).GhostTable,b=(a=e("helpers/object"),a&&a.__esModule&&a||{"default":a}),C=b.isObject,R=(b.objectEach,l=e("helpers/number"),l&&l.__esModule&&l||{"default":l}),_=R.valueAccordingPercent,S=R.rangeEach,E=(u=e("plugins"),u&&u.__esModule&&u||{"default":u}).registerPlugin,T=(c=e("utils/samplesGenerator"),c&&c.__esModule&&c||{"default":c}).SamplesGenerator,O=(d=e("helpers/string"),d&&d.__esModule&&d||{"default":d}).isPercentValue,M=function(e){var t=this;$traceurRuntime.superConstructor(k).call(this,e),this.heights=[],this.ghostTable=new y(this.hot),this.samplesGenerator=new T(function(e,n){return t.hot.getDataAtCell(e,n)}),this.firstCalculation=!0,this.inProgress=!1,this.addHook("beforeRowResize",function(e,n,o){return t.onBeforeRowResize(e,n,o)})},k=M;$traceurRuntime.createClass(M,{isEnabled:function(){return this.hot.getSettings().autoRowSize===!0||C(this.hot.getSettings().autoRowSize)},enablePlugin:function(){var e=this;this.enabled||(this.addHook("afterLoadData",function(){return e.onAfterLoadData()}),this.addHook("beforeChange",function(t){return e.onBeforeChange(t)}),this.addHook("beforeColumnMove",function(){return e.recalculateAllRowsHeight()}),this.addHook("beforeColumnResize",function(){return e.recalculateAllRowsHeight()}),this.addHook("beforeColumnSort",function(){return e.clearCache()}),this.addHook("beforeRender",function(t){return e.onBeforeRender(t)}),this.addHook("beforeRowMove",function(t,n){return e.onBeforeRowMove(t,n)}),this.addHook("modifyRowHeight",function(t,n){return e.getRowHeight(n,t)}),$traceurRuntime.superGet(this,k.prototype,"enablePlugin").call(this))},disablePlugin:function(){$traceurRuntime.superGet(this,k.prototype,"disablePlugin").call(this)},calculateRowsHeight:function(){var e=void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countRows()-1},t=void 0!==arguments[1]?arguments[1]:{from:0,to:this.hot.countCols()-1},n=void 0!==arguments[2]?arguments[2]:!1,o=this;"number"==typeof e&&(e={from:e,to:e}),"number"==typeof t&&(t={from:t,to:t}),S(e.from,e.to,function(e){if(n||void 0===o.heights[e]){var r=o.samplesGenerator.generateRowSamples(e,t);r.forEach(function(e,t){return o.ghostTable.addRow(t,e)})}}),this.ghostTable.rows.length&&(this.ghostTable.getHeights(function(e,t){return o.heights[e]=t}),this.ghostTable.clean())},calculateAllRowsHeight:function(){var e=void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countCols()-1},t=this,n=0,o=this.hot.countRows()-1,r=null;this.inProgress=!0;var i=function(){return t.hot?(t.calculateRowsHeight({from:n,
to:Math.min(n+k.CALCULATION_STEP,o)},e),n=n+k.CALCULATION_STEP+1,void(o>n?r=v(i):(m(r),t.inProgress=!1,t.hot.view.wt.wtOverlays.adjustElementsSize(!0),t.hot.view.wt.wtOverlays.leftOverlay.needFullRender&&t.hot.view.wt.wtOverlays.leftOverlay.clone.draw()))):(m(r),void(t.inProgress=!1))};this.firstCalculation&&this.getSyncCalculationLimit()&&(this.calculateRowsHeight({from:0,to:this.getSyncCalculationLimit()},e),this.firstCalculation=!1,n=this.getSyncCalculationLimit()+1),o>n?i():this.inProgress=!1},recalculateAllRowsHeight:function(){w(this.hot.view.wt.wtTable.TABLE)&&(this.clearCache(),this.calculateAllRowsHeight())},getSyncCalculationLimit:function(){var e=k.SYNC_CALCULATION_LIMIT,t=this.hot.countRows()-1;return C(this.hot.getSettings().autoRowSize)&&(e=this.hot.getSettings().autoRowSize.syncLimit,O(e)?e=_(t,e):e>>=0),Math.min(e,t)},getRowHeight:function(e){var t=arguments[1],n=t;return void 0!==this.heights[e]&&this.heights[e]>(t||0)&&(n=this.heights[e]),n},getFirstVisibleRow:function(){var e=this.hot.view.wt;return e.wtViewport.rowsVisibleCalculator?e.wtTable.getFirstVisibleRow():e.wtViewport.rowsRenderCalculator?e.wtTable.getFirstRenderedRow():-1},getLastVisibleRow:function(){var e=this.hot.view.wt;return e.wtViewport.rowsVisibleCalculator?e.wtTable.getLastVisibleRow():e.wtViewport.rowsRenderCalculator?e.wtTable.getLastRenderedRow():-1},clearCache:function(){this.heights.length=0},clearCacheByRange:function(e){var t=this;"number"==typeof e&&(e={from:e,to:e}),S(Math.min(e.from,e.to),Math.max(e.from,e.to),function(e){return t.heights[e]=void 0})},isNeedRecalculate:function(){return p(this.heights,function(e){return void 0===e}).length?!0:!1},onBeforeRender:function(){var e=this.hot.renderCall;this.calculateRowsHeight({from:this.getFirstVisibleRow(),to:this.getLastVisibleRow()},void 0,e),this.isNeedRecalculate()&&!this.inProgress&&this.calculateAllRowsHeight()},onBeforeRowMove:function(e,t){this.clearCacheByRange({from:e,to:t}),this.calculateAllRowsHeight()},onBeforeRowResize:function(e,t,n){return n&&(this.calculateRowsHeight(e,void 0,!0),t=this.getRowHeight(e)),t},onAfterLoadData:function(){var e=this;this.hot.view?this.recalculateAllRowsHeight():setTimeout(function(){e.hot&&e.recalculateAllRowsHeight()},0)},onBeforeChange:function(e){var t=null;1===e.length?t=e[0][0]:e.length>1&&(t={from:e[0][0],to:e[e.length-1][0]}),null!==t&&this.clearCacheByRange(t)},destroy:function(){this.ghostTable.clean(),$traceurRuntime.superGet(this,k.prototype,"destroy").call(this)}},{get CALCULATION_STEP(){return 50},get SYNC_CALCULATION_LIMIT(){return 500}},h),E("autoRowSize",M)},{_base:58,"helpers/array":42,"helpers/dom/element":45,"helpers/number":49,"helpers/object":50,"helpers/string":52,plugins:57,"utils/ghostTable":98,"utils/samplesGenerator":99}],61:[function(e,t,n){"use strict";function o(e,t,n,o){var r,i,s,a,l,u=n.length,c=n?n[0].length:0,d=[],h=[];if(r=t.row-e.row,i=t.col-e.col,-1!==["down","up"].indexOf(o)){for(var f=0;i>=f;f++)s=parseInt(n[0][f],10),a=parseInt(n[u-1][f],10),l=("down"===o?a-s:s-a)/(u-1)||0,h.push(l);d.push(h)}if(-1!==["right","left"].indexOf(o))for(var p=0;r>=p;p++)s=parseInt(n[p][0],10),a=parseInt(n[p][c-1],10),l=("right"===o?a-s:s-a)/(c-1)||0,h=[],h.push(l),d.push(h);return d}function r(e){function t(t){return e.autofill?void(e.autofill.handle&&e.autofill.handle.isDragged&&(e.autofill.handle.isDragged>1&&e.autofill.apply(),e.autofill.handle.isDragged=0,a=!1)):!0}function n(e){var t,n;return s.instance.autofill?(t=c(s.instance.table).top-(window.pageYOffset||document.documentElement.scrollTop)+d(s.instance.table),n=c(s.instance.table).left-(window.pageXOffset||document.documentElement.scrollLeft)+h(s.instance.table),s.addingStarted===!1&&s.instance.autofill.handle.isDragged>0&&e.clientY>t&&e.clientX<=n?(s.instance.mouseDragOutside=!0,s.addingStarted=!0):s.instance.mouseDragOutside=!1,void(s.instance.mouseDragOutside&&setTimeout(function(){s.addingStarted=!1,s.instance.alter("insert_row")},200))):!1}var o,r,i,s=this,a=!1;this.instance=e,this.addingStarted=!1,i=f(e),i.addEventListener(document,"mouseup",t),i.addEventListener(document,"mousemove",n),o=this.instance.view.wt.wtSettings.settings.onCellCornerMouseDown,this.instance.view.wt.wtSettings.settings.onCellCornerMouseDown=function(t){e.autofill.handle.isDragged=1,a=!0,o(t)},r=this.instance.view.wt.wtSettings.settings.onCellMouseOver,this.instance.view.wt.wtSettings.settings.onCellMouseOver=function(t,n,o,i){e.autofill&&a&&!e.view.isMouseDown()&&e.autofill.handle&&e.autofill.handle.isDragged&&(e.autofill.handle.isDragged++,e.autofill.showBorder(n),e.autofill.checkIfNewRowNeeded()),r(t,n,o,i)},this.instance.view.wt.wtSettings.settings.onCellCornerDblClick=function(){e.autofill.selectAdjacent()}}Object.defineProperties(n,{Autofill:{get:function(){return r}},__esModule:{value:!0}});var i,s,a,l,u=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}),c=u.offset,d=u.outerHeight,h=u.outerWidth,f=(s=e("eventManager"),s&&s.__esModule&&s||{"default":s}).eventManager,p=((a=e("plugins"),a&&a.__esModule&&a||{"default":a}).registerPlugin,(l=e("3rdparty/walkontable/src/cell/coords"),l&&l.__esModule&&l||{"default":l}).WalkontableCellCoords);r.prototype.init=function(){this.handle={}},r.prototype.disable=function(){this.handle.disabled=!0},r.prototype.selectAdjacent=function(){var e,t,n,o,r;e=this.instance.selection.isMultiple()?this.instance.view.wt.selections.area.getCorners():this.instance.view.wt.selections.current.getCorners(),t=this.instance.getData();e:for(n=e[2]+1;n<this.instance.countRows();n++){for(r=e[1];r<=e[3];r++)if(t[n][r])break e;(t[n][e[1]-1]||t[n][e[3]+1])&&(o=n)}o&&(this.instance.view.wt.selections.fill.clear(),this.instance.view.wt.selections.fill.add(new p(e[0],e[1])),this.instance.view.wt.selections.fill.add(new p(o,e[3])),this.apply())},r.prototype.apply=function(){var e,t,n,r,i,s,a,l;this.handle.isDragged=0,this.instance.view.wt.selections.fill.isEmpty()||(e=this.instance.view.wt.selections.fill.getCorners(),this.instance.view.wt.selections.fill.clear(),t=this.instance.selection.isMultiple()?this.instance.view.wt.selections.area.getCorners():this.instance.view.wt.selections.current.getCorners(),Handsontable.hooks.run(this.instance,"afterAutofillApplyValues",t,e),e[0]===t[0]&&e[1]<t[1]?(s="left",n=new p(e[0],e[1]),r=new p(e[2],t[1]-1)):e[0]===t[0]&&e[3]>t[3]?(s="right",n=new p(e[0],t[3]+1),r=new p(e[2],e[3])):e[0]<t[0]&&e[1]===t[1]?(s="up",n=new p(e[0],e[1]),r=new p(t[0]-1,e[3])):e[2]>t[2]&&e[1]===t[1]&&(s="down",n=new p(t[2]+1,e[1]),r=new p(e[2],e[3])),n&&n.row>-1&&n.col>-1?(l={from:this.instance.getSelectedRange().from,to:this.instance.getSelectedRange().to},i=this.instance.getData(l.from.row,l.from.col,l.to.row,l.to.col),a=o(n,r,i,s),Handsontable.hooks.run(this.instance,"beforeAutofill",n,r,i),this.instance.populateFromArray(n.row,n.col,i,r.row,r.col,"autofill",null,s,a),this.instance.selection.setRangeStart(new p(e[0],e[1])),this.instance.selection.setRangeEnd(new p(e[2],e[3]))):this.instance.selection.refreshBorders())},r.prototype.showBorder=function(e){var t=this.instance.getSelectedRange().getTopLeftCorner(),n=this.instance.getSelectedRange().getBottomRightCorner();if("horizontal"!==this.instance.getSettings().fillHandle&&(n.row<e.row||t.row>e.row))e=new p(e.row,n.col);else{if("vertical"===this.instance.getSettings().fillHandle)return;e=new p(n.row,e.col)}this.instance.view.wt.selections.fill.clear(),this.instance.view.wt.selections.fill.add(this.instance.getSelectedRange().from),this.instance.view.wt.selections.fill.add(this.instance.getSelectedRange().to),this.instance.view.wt.selections.fill.add(e),this.instance.view.render()},r.prototype.checkIfNewRowNeeded=function(){var e,t,n=this.instance.countRows(),o=this;this.instance.view.wt.selections.fill.cellRange&&this.addingStarted===!1&&(t=this.instance.getSelected(),e=this.instance.view.wt.selections.fill.getCorners(),t[2]<n-1&&e[2]===n-1&&(this.addingStarted=!0,this.instance._registerTimeout(setTimeout(function(){o.instance.alter("insert_row"),o.addingStarted=!1},200))))},Handsontable.hooks.add("afterInit",function(){var e=new r(this);"undefined"!=typeof this.getSettings().fillHandle&&(e.handle&&this.getSettings().fillHandle===!1?e.disable():e.handle||this.getSettings().fillHandle===!1||(this.autofill=e,this.autofill.init()))}),Handsontable.Autofill=r},{"3rdparty/walkontable/src/cell/coords":5,eventManager:41,"helpers/dom/element":45,plugins:57}],62:[function(e,t,n){"use strict";Object.defineProperties(n,{ColumnSorting:{get:function(){return b}},__esModule:{value:!0}});var o,r,i,s,a,l=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),u=l.addClass,c=l.closest,d=l.hasClass,h=l.index,f=l.removeClass,p=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}),g=(p.arrayEach,p.arrayMap),m=p.arrayReduce,v=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,w=(s=e("_base"),s&&s.__esModule&&s||{"default":s})["default"],y=(a=e("plugins"),a&&a.__esModule&&a||{"default":a}).registerPlugin;Handsontable.hooks.register("beforeColumnSort"),Handsontable.hooks.register("afterColumnSort");var b=function(e){$traceurRuntime.superConstructor(C).call(this,e),this.sortIndicators=[]},C=b;$traceurRuntime.createClass(b,{isEnabled:function(){return!!this.hot.getSettings().columnSorting},enablePlugin:function(){var e=this;if(!this.enabled){var t=this;this.hot.sortIndex=[],this.hot.sort=function(){var e=Array.prototype.slice.call(arguments);return t.sortByColumn.apply(t,e)},"undefined"==typeof this.hot.getSettings().observeChanges&&this.enableObserveChangesPlugin(),this.bindColumnSortingAfterClick(),this.addHook("afterTrimRow",function(t){return e.sort()}),this.addHook("afterUntrimRow",function(t){return e.sort()}),this.addHook("modifyRow",function(t){return e.translateRow(t)}),this.addHook("afterUpdateSettings",function(){return e.onAfterUpdateSettings()}),this.addHook("afterGetColHeader",function(t,n){return e.getColHeader(t,n)}),this.addHook("afterCreateRow",function(){t.afterCreateRow.apply(t,arguments)}),this.addHook("afterRemoveRow",function(){t.afterRemoveRow.apply(t,arguments)}),this.addHook("afterInit",function(){return e.sortBySettings()}),this.addHook("afterLoadData",function(){e.hot.sortIndex=[],e.hot.view&&e.sortBySettings()}),this.hot.view&&this.sortBySettings(),$traceurRuntime.superGet(this,C.prototype,"enablePlugin").call(this)}},disablePlugin:function(){this.hot.sort=void 0,$traceurRuntime.superGet(this,C.prototype,"disablePlugin").call(this)},onAfterUpdateSettings:function(){this.sortBySettings()},sortBySettings:function(){var e,t,n=this.hot.getSettings().columnSorting,o=this.loadSortingState();"undefined"==typeof o?(e=n.column,t=n.sortOrder):(e=o.sortColumn,t=o.sortOrder),this.sortByColumn(e,t)},setSortingColumn:function(e,t){return"undefined"==typeof e?(this.hot.sortColumn=void 0,void(this.hot.sortOrder=void 0)):(this.hot.sortColumn===e&&"undefined"==typeof t?this.hot.sortOrder===!1?this.hot.sortOrder=void 0:this.hot.sortOrder=!this.hot.sortOrder:this.hot.sortOrder="undefined"==typeof t?!0:t,void(this.hot.sortColumn=e))},sortByColumn:function(e,t){this.setSortingColumn(e,t),"undefined"!=typeof this.hot.sortColumn&&(Handsontable.hooks.run(this.hot,"beforeColumnSort",this.hot.sortColumn,this.hot.sortOrder),this.sort(),this.hot.render(),this.saveSortingState(),Handsontable.hooks.run(this.hot,"afterColumnSort",this.hot.sortColumn,this.hot.sortOrder))},saveSortingState:function(){var e={};"undefined"!=typeof this.hot.sortColumn&&(e.sortColumn=this.hot.sortColumn),"undefined"!=typeof this.hot.sortOrder&&(e.sortOrder=this.hot.sortOrder),(e.hasOwnProperty("sortColumn")||e.hasOwnProperty("sortOrder"))&&Handsontable.hooks.run(this.hot,"persistentStateSave","columnSorting",e)},loadSortingState:function(){var e={};return Handsontable.hooks.run(this.hot,"persistentStateLoad","columnSorting",e),e.value},bindColumnSortingAfterClick:function(){function e(){var e=o.hot.view.TBODY.querySelector("tr"),t=1;return e&&(t=e.querySelectorAll("th").length),t}function t(t){var n=c(t,"TH");return o.hot.view.wt.wtTable.getFirstRenderedColumn()+h(n)-e()}if(!this.bindedSortEvent){var n=v(this.hot),o=this;this.bindedSortEvent=!0,n.addEventListener(this.hot.rootElement,"click",function(e){if(d(e.target,"columnSorting")){var n=t(e.target);if(n===this.lastSortedColumn)switch(o.hot.sortOrder){case void 0:o.sortOrderClass="ascending";break;case!0:o.sortOrderClass="descending";break;case!1:o.sortOrderClass=void 0}else o.sortOrderClass="ascending";this.lastSortedColumn=n,o.sortByColumn(n)}})}},enableObserveChangesPlugin:function(){var e=this;this.hot._registerTimeout(setTimeout(function(){e.hot.updateSettings({observeChanges:!0})},0))},defaultSort:function(e){return function(t,n){return"string"==typeof t[1]&&(t[1]=t[1].toLowerCase()),"string"==typeof n[1]&&(n[1]=n[1].toLowerCase()),t[1]===n[1]?0:null===t[1]||""===t[1]?1:null===n[1]||""===n[1]?-1:isNaN(t[1])&&!isNaN(n[1])?e?1:-1:!isNaN(t[1])&&isNaN(n[1])?e?-1:1:t[1]<n[1]?e?-1:1:t[1]>n[1]?e?1:-1:0}},dateSort:function(e){return function(t,n){if(t[1]===n[1])return 0;if(null===t[1]||""===t[1])return 1;if(null===n[1]||""===n[1])return-1;var o=new Date(t[1]),r=new Date(n[1]);return r>o?e?-1:1:o>r?e?1:-1:0}},sort:function(){if("undefined"==typeof this.hot.sortOrder)return void(this.hot.sortIndex.length=0);var e,t;this.hot.sortingEnabled=!1,this.hot.sortIndex.length=0;for(var n=this.hot.colOffset(),o=0,r=this.hot.countRows()-this.hot.getSettings().minSpareRows;r>o;o++)this.hot.sortIndex.push([o,this.hot.getDataAtCell(o,this.hot.sortColumn+n)]);switch(e=this.hot.getCellMeta(0,this.hot.sortColumn),this.sortIndicators[this.hot.sortColumn]=e.sortIndicator,e.type){case"date":t=this.dateSort;break;default:t=this.defaultSort}this.hot.sortIndex.sort(t(this.hot.sortOrder));for(var o=this.hot.sortIndex.length;o<this.hot.countRows();o++)this.hot.sortIndex.push([o,this.hot.getDataAtCell(o,this.hot.sortColumn+n)]);this.hot.sortingEnabled=!0},translateRow:function(e){return this.hot.sortingEnabled&&"undefined"!=typeof this.hot.sortOrder&&this.hot.sortIndex&&this.hot.sortIndex.length&&this.hot.sortIndex[e]?this.hot.sortIndex[e][0]:e},untranslateRow:function(e){if(this.hot.sortingEnabled&&this.hot.sortIndex&&this.hot.sortIndex.length)for(var t=0;t<this.hot.sortIndex.length;t++)if(this.hot.sortIndex[t][0]==e)return t},getColHeader:function(e,t){var n=t.querySelector(".colHeader"),o=(t.getAttribute("colspan"),t.parentNode.parentNode.childNodes),r=Array.prototype.indexOf.call(o,t.parentNode);r-=o.length,n&&(this.hot.getSettings().columnSorting&&e>=0&&-1===r&&u(n,"columnSorting"),f(n,"descending"),f(n,"ascending"),this.sortIndicators[e]&&e===this.hot.sortColumn&&("ascending"===this.sortOrderClass?u(n,"ascending"):"descending"===this.sortOrderClass&&u(n,"descending")))},isSorted:function(){return"undefined"!=typeof this.hot.sortColumn},afterCreateRow:function(e,t){if(this.isSorted()){for(var n=0;n<this.hot.sortIndex.length;n++)this.hot.sortIndex[n][0]>=e&&(this.hot.sortIndex[n][0]+=t);for(var n=0;t>n;n++)this.hot.sortIndex.splice(e+n,0,[e+n,this.hot.getSourceData()[e+n][this.hot.sortColumn+this.hot.colOffset()]]);this.saveSortingState()}},afterRemoveRow:function(e,t){function n(e){return m(o,function(t,n){return e>n&&t++,t},0)}if(this.isSorted()){var o=this.hot.sortIndex.splice(e,t);o=g(o,function(e){return e[0]}),this.hot.sortIndex=g(this.hot.sortIndex,function(e,t){var o=n(e[0]);return o&&(e[0]-=o),e}),this.saveSortingState()}}},{},w),y("columnSorting",b)},{_base:58,eventManager:41,"helpers/array":42,"helpers/dom/element":45,plugins:57}],63:[function(e,t,n){"use strict";Object.defineProperties(n,{CommentEditor:{get:function(){return i}},__esModule:{value:!0}});var o,r=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}).addClass,i=function(){this.editor=this.createEditor(),this.editorStyle=this.editor.style,this.editorStyle.position="absolute",this.editorStyle.zIndex=100,this.hide()},s=i;$traceurRuntime.createClass(i,{setPosition:function(e,t){this.editorStyle.left=e+"px",this.editorStyle.top=t+"px"},show:function(){this.editorStyle.display="block"},hide:function(){this.editorStyle.display="none"},isVisible:function(){return"block"===this.editorStyle.display},setValue:function(){var e=void 0!==arguments[0]?arguments[0]:"";e=e||"",this.getInputElement().value=e},getValue:function(){return this.getInputElement().value},isFocused:function(){return document.activeElement===this.getInputElement()},focus:function(){this.getInputElement().focus()},createEditor:function(){var e,t,n=document.querySelector("."+s.CLASS_EDITOR_CONTAINER);return n||(n=document.createElement("div"),r(n,s.CLASS_EDITOR_CONTAINER),document.body.appendChild(n)),e=document.createElement("div"),r(e,s.CLASS_EDITOR),t=document.createElement("textarea"),r(t,s.CLASS_INPUT),e.appendChild(t),n.appendChild(e),e},getInputElement:function(){return this.editor.querySelector("."+s.CLASS_INPUT)},destroy:function(){this.editor.parentNode.removeChild(this.editor),this.editor=null,this.editorStyle=null}},{get CLASS_EDITOR_CONTAINER(){return"htCommentsContainer"},get CLASS_EDITOR(){return"htComments"},get CLASS_INPUT(){return"htCommentTextArea"},get CLASS_CELL(){return"htCommentCell"}})},{"helpers/dom/element":45}],64:[function(e,t,n){"use strict";Object.defineProperties(n,{Comments:{get:function(){return R}},__esModule:{value:!0}});var o,r,i,s,a,l,u=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),c=u.addClass,d=u.closest,h=u.getWindowScrollLeft,f=u.getWindowScrollTop,p=u.hasClass,g=u.offset,m=(r=e("eventManager"),r&&r.__esModule&&r||{"default":r}).EventManager,v=(i=e("3rdparty/walkontable/src/cell/coords"),i&&i.__esModule&&i||{"default":i}).WalkontableCellCoords,w=(s=e("plugins"),s&&s.__esModule&&s||{"default":s}),y=w.registerPlugin,b=(w.getPlugin,(a=e("_base"),a&&a.__esModule&&a||{"default":a})["default"]),C=(l=e("commentEditor"),l&&l.__esModule&&l||{"default":l}).CommentEditor,R=function(e){$traceurRuntime.superConstructor(_).call(this,e),this.editor=null,this.eventManager=null,this.range={},this.mouseDown=!1,this.contextMenuEvent=!1,this.timer=null},_=R;$traceurRuntime.createClass(R,{isEnabled:function(){return this.hot.getSettings().comments},enablePlugin:function(){var e=this;this.enabled||(this.editor||(this.editor=new C),this.eventManager||(this.eventManager=new m(this)),this.addHook("afterContextMenuDefaultOptions",function(t){return e.addToContextMenu(t)}),this.addHook("afterRenderer",function(t,n,o,r,i,s){return e.onAfterRenderer(t,s)}),this.addHook("afterScrollVertically",function(){return e.refreshEditorPosition()}),this.addHook("afterColumnResize",function(){return e.refreshEditorPosition()}),this.addHook("afterRowResize",function(){return e.refreshEditorPosition()}),this.registerListeners(),$traceurRuntime.superGet(this,_.prototype,"enablePlugin").call(this))},disablePlugin:function(){$traceurRuntime.superGet(this,_.prototype,"disablePlugin").call(this)},registerListeners:function(){var e=this;this.eventManager.addEventListener(document,"mouseover",function(t){return e.onMouseOver(t)}),this.eventManager.addEventListener(document,"mousedown",function(t){return e.onMouseDown(t)}),this.eventManager.addEventListener(document,"mousemove",function(t){return e.onMouseMove(t)}),this.eventManager.addEventListener(document,"mouseup",function(t){return e.onMouseUp(t)}),this.eventManager.addEventListener(this.editor.getInputElement(),"blur",function(t){return e.onEditorBlur(t)})},setRange:function(e){this.range=e},clearRange:function(){this.range={}},targetIsCellWithComment:function(e){return p(e.target,"htCommentCell")&&d(e.target,[this.hot.rootElement])?!0:!1},targetIsCommentTextArea:function(e){return this.editor.getInputElement()===e.target},saveComment:function(){if(!this.range.from)throw new Error('Before using this method, first set cell range (hot.getPlugin("comment").setRange())');var e=this.editor.getValue(),t=this.range.from.row,n=this.range.from.col;this.hot.setCellMeta(t,n,"comment",e),this.hot.render()},saveCommentAtCell:function(e,t){this.setRange({from:new v(e,t)}),this.saveComment()},removeComment:function(){if(!this.range.from)throw new Error('Before using this method, first set cell range (hot.getPlugin("comment").setRange())');this.hot.removeCellMeta(this.range.from.row,this.range.from.col,"comment"),this.hot.render(),this.hide()},removeCommentAtCell:function(e,t){this.setRange({from:new v(e,t)}),this.removeComment()},show:function(){if(!this.range.from)throw new Error('Before using this method, first set cell range (hot.getPlugin("comment").setRange())');var e=this.hot.getCellMeta(this.range.from.row,this.range.from.col);return this.refreshEditorPosition(!0),this.editor.setValue(e.comment||""),this.editor.show(),!0},showAtCell:function(e,t){return this.setRange({from:new v(e,t)}),this.show()},hide:function(){this.editor.hide()},refreshEditorPosition:function(){var e=void 0!==arguments[0]?arguments[0]:!1;if(e||this.range.from&&this.editor.isVisible()){var t=this.hot.view.wt.wtTable.getCell(this.range.from),n=g(t),o=this.hot.getColWidth(this.range.from.col),r=n.top,i=n.left,s=0,a=0;this.hot.view.wt.wtViewport.hasVerticalScroll()&&(r-=this.hot.view.wt.wtOverlays.topOverlay.getScrollPosition(),s=20),this.hot.view.wt.wtViewport.hasHorizontalScroll()&&(i-=this.hot.view.wt.wtOverlays.leftOverlay.getScrollPosition(),a=20);var l=i+o,u=r,c=this.hot.view.wt.wtTable.holder.getBoundingClientRect(),d={left:c.left+h()+a,right:c.right+h()-15,top:c.top+f()+s,bottom:c.bottom+f()};l<=d.left||l>d.right||u<=d.top||u>d.bottom?this.hide():this.editor.setPosition(l,u)}},onMouseDown:function(e){this.mouseDown=!0,this.hot.view&&this.hot.view.wt&&(this.contextMenuEvent||this.targetIsCommentTextArea(e)||this.targetIsCellWithComment(e)||this.hide(),this.contextMenuEvent=!1)},onMouseOver:function(e){if(!this.mouseDown&&!this.editor.isFocused())if(this.targetIsCellWithComment(e)){var t=this.hot.view.wt.wtTable.getCoords(e.target),n={from:new v(t.row,t.col)};this.setRange(n),this.show()}else this.targetIsCommentTextArea(e)||this.editor.isFocused()||this.hide()},onMouseMove:function(e){var t=this;this.targetIsCommentTextArea(e)&&(this.mouseDown=!0,clearTimeout(this.timer),this.timer=setTimeout(function(){t.mouseDown=!1},200))},onMouseUp:function(e){this.mouseDown=!1},onAfterRenderer:function(e,t){t.comment&&c(e,t.commentedCellClassName)},onEditorBlur:function(e){this.saveComment()},checkSelectionCommentsConsistency:function(){var e=this.hot.getSelectedRange();if(!e)return!1;var t=!1,n=e.from;return this.hot.getCellMeta(n.row,n.col).comment&&(t=!0),t},onContextMenuAddComment:function(){var e=this,t=this.hot.getSelectedRange();this.contextMenuEvent=!0,this.setRange({from:t.from}),this.show(),setTimeout(function(){e.hot&&(e.hot.deselectCell(),e.editor.focus())},10)},onContextMenuRemoveComment:function(e,t){this.contextMenuEvent=!0,this.removeCommentAtCell(t.start.row,t.start.col)},addToContextMenu:function(e){var t=this;e.items.push(Handsontable.plugins.ContextMenu.SEPARATOR,{key:"commentsAddEdit",name:function(){return t.checkSelectionCommentsConsistency()?"Edit Comment":"Add Comment"},callback:function(){return t.onContextMenuAddComment()},disabled:function(){return this.getSelected()?!1:!0}},{key:"commentsRemove",name:function(){return"Delete Comment"},callback:function(e,n){return t.onContextMenuRemoveComment(e,n)},disabled:function(){return!t.checkSelectionCommentsConsistency()}})},destroy:function(){this.editor&&this.editor.destroy(),$traceurRuntime.superGet(this,_.prototype,"destroy").call(this)}},{},b),y("comments",R)},{"3rdparty/walkontable/src/cell/coords":5,_base:58,commentEditor:63,eventManager:41,"helpers/dom/element":45,plugins:57}],65:[function(e,t,n){"use strict";function o(e,t){var n;return i(t,function(t){var o=t.key?t.key.split(":"):null;return Array.isArray(o)&&o[1]===e?(n=t,!1):void 0}),n}Object.defineProperties(n,{CommandExecutor:{get:function(){return s}},__esModule:{value:!0}});var r,i=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}).arrayEach,s=function(e){this.hot=e,this.commands={},this.commonCallback=null};$traceurRuntime.createClass(s,{registerCommand:function(e,t){this.commands[e]=t},setCommonCallback:function(e){this.commonCallback=e},execute:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=this,s=e.split(":");e=s[0];var a=2===s.length?s[1]:null,l=this.commands[e];if(!l)throw new Error("Menu command '"+e+"' not exists.");if(a&&l.submenu&&(l=o(a,l.submenu.items)),l.disabled!==!0&&("function"!=typeof l.disabled||l.disabled.call(this.hot)!==!0)&&!l.hasOwnProperty("submenu")){var u=[];"function"==typeof l.callback&&u.push(l.callback),"function"==typeof this.commonCallback&&u.push(this.commonCallback),t.unshift(s.join(":")),i(u,function(e){return e.apply(r.hot,t)})}}},{})},{"helpers/array":42}],66:[function(e,t,n){"use strict";Object.defineProperties(n,{ContextMenu:{get:function(){return L}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c,d,h,f,p=(o=e("_base"),o&&o.__esModule&&o||{"default":o})["default"],g=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}).arrayEach,m=(i=e("commandExecutor"),i&&i.__esModule&&i||{"default":i}).CommandExecutor,v=(s=e("eventManager"),s&&s.__esModule&&s||{"default":s}).EventManager,w=(a=e("helpers/dom/element"),a&&a.__esModule&&a||{"default":a}).hasClass,y=(l=e("itemsFactory"),l&&l.__esModule&&l||{"default":l}).ItemsFactory,b=(u=e("menu"),u&&u.__esModule&&u||{"default":u}).Menu,C=(c=e("helpers/object"),c&&c.__esModule&&c||{"default":c}),R=(C.objectEach,C.mixin,(d=e("plugins"),d&&d.__esModule&&d||{"default":d}).registerPlugin),_=(h=e("helpers/dom/event"),h&&h.__esModule&&h||{"default":h}).stopPropagation,S=(f=e("predefinedItems"),f&&f.__esModule&&f||{"default":f}),E=S.ROW_ABOVE,T=S.ROW_BELOW,O=S.COLUMN_LEFT,M=S.COLUMN_RIGHT,k=S.REMOVE_ROW,H=S.REMOVE_COLUMN,D=S.UNDO,x=S.REDO,A=S.READ_ONLY,P=S.ALIGNMENT,N=S.SEPARATOR,L=(S.predefinedItems,function(e){$traceurRuntime.superConstructor(W).call(this,e),this.eventManager=new v(this),this.commandExecutor=new m(this.hot),this.itemsFactory=null,this.menu=null}),W=L;$traceurRuntime.createClass(L,{isEnabled:function(){return this.hot.getSettings().contextMenu},enablePlugin:function(){var e=this;if(!this.enabled){this.itemsFactory=new y(this.hot,W.DEFAULT_ITEMS);var t=this.hot.getSettings().contextMenu,n={items:this.itemsFactory.getVisibleItems(t)};this.registerEvents(),"function"==typeof t.callback&&this.commandExecutor.setCommonCallback(t.callback),$traceurRuntime.superGet(this,W.prototype,"enablePlugin").call(this),this.callOnPluginsReady(function(){e.hot.runHooks("afterContextMenuDefaultOptions",n),e.itemsFactory.setPredefinedItems(n.items);var o=e.itemsFactory.getVisibleItems(t);e.menu=new b(e.hot,{className:"htContextMenu"}),e.menu.setMenuItems(o),e.menu.addLocalHook("afterOpen",function(){return e.onMenuAfterOpen()}),e.menu.addLocalHook("afterClose",function(){return e.onMenuAfterClose()}),e.menu.addLocalHook("executeCommand",function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.executeCommand.apply(e,t)}),g(o,function(t){return e.commandExecutor.registerCommand(t.key,t)})})}},disablePlugin:function(){this.close(),this.menu&&(this.menu.destroy(),this.menu=null),$traceurRuntime.superGet(this,W.prototype,"disablePlugin").call(this)},registerEvents:function(){var e=this;this.eventManager.addEventListener(this.hot.rootElement,"contextmenu",function(t){return e.onContextMenu(t)})},open:function(e){this.menu&&(this.menu.open(),this.menu.setPosition(e),this.menu.hotMenu.isHotTableEnv=this.hot.isHotTableEnv,Handsontable.eventManager.isHotTableEnv=this.hot.isHotTableEnv)},close:function(){this.menu&&this.menu.close()},executeCommand:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.commandExecutor.execute.apply(this.commandExecutor,e)},onContextMenu:function(e){function t(e){return"TD"===e.nodeName||"TD"===e.parentNode.nodeName}var n=this.hot.getSettings(),o=n.rowHeaders,r=n.colHeaders,i=e.realTarget;if(this.close(),e.preventDefault(),_(e),o||r){if(o&&r){var s=i.parentNode.querySelectorAll(".cornerHeader").length>0;if(s)return}}else if(!(t(i)||w(i,"current")&&w(i,"wtBorder")))return;this.open(e)},onMenuAfterOpen:function(){this.hot.runHooks("afterContextMenuShow",this)},onMenuAfterClose:function(){this.hot.listen(),this.hot.runHooks("afterContextMenuHide",this)},destroy:function(){this.close(),this.menu&&this.menu.destroy(),$traceurRuntime.superGet(this,W.prototype,"destroy").call(this)}},{get DEFAULT_ITEMS(){return[E,T,N,O,M,N,k,H,N,D,x,N,A,N,P]}},p),L.SEPARATOR={name:N},Handsontable.hooks.register("afterContextMenuDefaultOptions"),Handsontable.hooks.register("afterContextMenuShow"),Handsontable.hooks.register("afterContextMenuHide"),Handsontable.hooks.register("afterContextMenuExecute"),R("contextMenu",L)},{_base:58,commandExecutor:65,eventManager:41,"helpers/array":42,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/object":50,itemsFactory:68,menu:69,plugins:57,predefinedItems:70}],67:[function(e,t,n){"use strict";Object.defineProperties(n,{Cursor:{get:function(){return d}},__esModule:{value:!0}});var o,r,i=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),s=i.getWindowScrollLeft,a=i.getWindowScrollTop,l=(r=e("helpers/dom/event"),r&&r.__esModule&&r||{"default":r}),u=l.pageX,c=l.pageY,d=function(e){var t,n,o,r,i,l,d,h,f=a(),p=s();this.type=this.getSourceType(e),"literal"===this.type?(t=parseInt(e.top,10),o=parseInt(e.left,10),d=e.height,h=e.width):"event"===this.type&&(t=parseInt(c(e),10),o=parseInt(u(e),10),d=e.target.clientHeight,h=e.target.clientWidth),n=t-f,r=o-p,i=f,l=p,this.top=t,this.topRelative=n,this.left=o,this.leftRelative=r,this.scrollTop=i,this.scrollLeft=l,this.cellHeight=d,this.cellWidth=h};$traceurRuntime.createClass(d,{getSourceType:function(e){var t="literal";return e instanceof Event&&(t="event"),t},fitsAbove:function(e){return this.topRelative>=e.offsetHeight},fitsBelow:function(e){var t=void 0!==arguments[1]?arguments[1]:window.innerHeight;return this.topRelative+e.offsetHeight<=t},fitsOnRight:function(e){var t=void 0!==arguments[1]?arguments[1]:window.innerWidth;return this.leftRelative+this.cellWidth+e.offsetWidth<=t},fitsOnLeft:function(e){return this.leftRelative>=e.offsetWidth}},{}),Handsontable.plugins.utils=Handsontable.plugins.utils||{},Handsontable.plugins.utils.Cursor=d},{"helpers/dom/element":45,"helpers/dom/event":46}],68:[function(e,t,n){"use strict";function o(){var e=void 0!==arguments[0]?arguments[0]:null,t=void 0!==arguments[1]?arguments[1]:[],n=void 0!==arguments[2]?arguments[2]:{},o=[];return e&&e.items?e=e.items:Array.isArray(e)||(e=t),u(e)?l(e,function(e,t){var r=n["string"==typeof e?e:t];r||(r=e),u(e)?c(r,e):"string"==typeof r&&(r={name:r}),void 0===r.key&&(r.key=t),o.push(r)}):d(e,function(e,t){var r=n[e];!r&&p.indexOf(e)>=0||(r||(r={name:e,key:t+""}),u(e)&&c(r,e),void 0===r.key&&(r.key=t),o.push(r))}),o[0].name===f&&o.shift(),o}Object.defineProperties(n,{ItemsFactory:{get:function(){return m}},__esModule:{value:!0}});var r,i,s,a=(r=e("helpers/object"),r&&r.__esModule&&r||{"default":r}),l=a.objectEach,u=a.isObject,c=a.extend,d=(i=e("helpers/array"),i&&i.__esModule&&i||{"default":i}).arrayEach,h=(s=e("predefinedItems"),s&&s.__esModule&&s||{"default":s}),f=h.SEPARATOR,p=h.ITEMS,g=h.predefinedItems,m=function(e){var t=void 0!==arguments[1]?arguments[1]:null;this.hot=e,this.predefinedItems=g(),this.defaultOrderPattern=t};$traceurRuntime.createClass(m,{setPredefinedItems:function(e){var t=this,n={};this.defaultOrderPattern.length=0,l(e,function(e,o){var r="";e.name===f?(n[f]=e,r=f):isNaN(parseInt(o,10))?(e.key=void 0===e.key?o:e.key,n[o]=e,r=e.key):(n[e.key]=e,r=e.key),t.defaultOrderPattern.push(r)}),this.predefinedItems=n},getVisibleItems:function(){var e=void 0!==arguments[0]?arguments[0]:null,t=this,n={};return l(this.predefinedItems,function(e,o){(!e.hidden||e.hidden&&!e.hidden.apply(t.hot))&&(n[o]=e)}),o(e,this.defaultOrderPattern,n)},getItems:function(){
var e=void 0!==arguments[0]?arguments[0]:null;return o(e,this.defaultOrderPattern,this.predefinedItems)}},{})},{"helpers/array":42,"helpers/object":50,predefinedItems:70}],69:[function(e,t,n){"use strict";Object.defineProperties(n,{Menu:{get:function(){return W}},__esModule:{value:!0}});var o,r,i,s,a,l,u,c,d,h,f=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),p=f.addClass,g=f.empty,m=f.fastInnerHTML,v=(f.getComputedStyle,f.getScrollbarWidth),w=(f.getWindowScrollLeft,f.getWindowScrollTop,f.hasClass,f.isChildOf),y=f.removeClass,b=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}).arrayEach,C=(i=e("cursor"),i&&i.__esModule&&i||{"default":i}).Cursor,R=(s=e("eventManager"),s&&s.__esModule&&s||{"default":s}).EventManager,_=(a=e("helpers/object"),a&&a.__esModule&&a||{"default":a}),S=(_.extend,_.isObject,_.objectEach,_.mixin),E=(l=e("utils"),l&&l.__esModule&&l||{"default":l}),T=E.isSeparator,O=E.isDisabled,M=E.isSelectionDisabled,k=E.hasSubMenu,H=E.normalizeSelection,D=(u=e("helpers/unicode"),u&&u.__esModule&&u||{"default":u}).KEY_CODES,x=(c=e("mixins/localHooks"),c&&c.__esModule&&c||{"default":c}).localHooks,A=(d=e("predefinedItems"),d&&d.__esModule&&d||{"default":d}),P=A.SEPARATOR,N=(A.predefinedItems,h=e("helpers/dom/event"),h&&h.__esModule&&h||{"default":h}),L=(N.stopPropagation,N.stopImmediatePropagation),W=(N.pageX,N.pageY,function(e){var t=void 0!==arguments[1]?arguments[1]:{parent:null,name:null,className:"",keepInViewport:!0};this.hot=e,this.options=t,this.eventManager=new R(this),this.container=this.createContainer(this.options.name),this.hotMenu=null,this.hotSubMenus={},this.parentMenu=this.options.parent||null,this.menuItems=null,this.origOutsideClickDeselects=null,this._afterScrollCallback=null,this.registerEvents()}),I=W;$traceurRuntime.createClass(W,{registerEvents:function(){var e=this;this.eventManager.addEventListener(document.documentElement,"mousedown",function(t){return e.onDocumentMouseDown(t)})},setMenuItems:function(e){this.menuItems=e},isSubMenu:function(){return null!==this.parentMenu},open:function(){var e=this;this.container.removeAttribute("style"),this.container.style.display="block";var t={data:this.menuItems,colHeaders:!1,colWidths:[200],autoRowSize:!1,readOnly:!0,copyPaste:!1,columns:[{data:"name",renderer:function(t,n,o,r,i,s){return e.menuItemRenderer(t,n,o,r,i,s)}}],renderAllRows:!0,fragmentSelection:"cell",beforeKeyDown:function(t){return e.onBeforeKeyDown(t)},afterOnCellMouseOver:function(t,n,o){return e.openSubMenu(n.row)}};this.origOutsideClickDeselects=this.hot.getSettings().outsideClickDeselects,this.hot.getSettings().outsideClickDeselects=!1,this.hotMenu=new Handsontable.Core(this.container,t),this.hotMenu.addHook("afterInit",function(){return e.onAfterInit()}),this.hotMenu.init(),this.hotMenu.listen(),this.blockMainTableCallbacks(),this.runLocalHooks("afterOpen")},close:function(){var e=void 0!==arguments[0]?arguments[0]:!1;this.isOpened()&&(e&&this.parentMenu?this.parentMenu.close():(this.closeAllSubMenus(),this.container.style.display="none",this.releaseMainTableCallbacks(),this.hotMenu.destroy(),this.hotMenu=null,this.hot.getSettings().outsideClickDeselects=this.origOutsideClickDeselects,this.runLocalHooks("afterClose")))},openSubMenu:function(e){var t=this.hotMenu.getCell(e,0);if(this.closeAllSubMenus(),!t||!k(t))return!1;var n=this.hotMenu.getSourceDataAtRow(e),o=new I(this.hot,{parent:this,name:n.name,className:this.options.className});return o.setMenuItems(n.submenu.items),o.open(),o.setPosition(t.getBoundingClientRect()),this.hotSubMenus[n.key]=o,o},closeSubMenu:function(e){var t=this.hotMenu.getSourceDataAtRow(e),n=this.hotSubMenus[t.key];n&&(n.destroy(),delete this.hotSubMenus[t.key])},closeAllSubMenus:function(){var e=this;b(this.hotMenu.getData(),function(t,n){return e.closeSubMenu(n)})},isAllSubMenusClosed:function(){return 0===Object.keys(this.hotSubMenus).length},destroy:function(){this.clearLocalHooks(),this.close(),this.parentMenu=null,this.eventManager.destroy()},isOpened:function(){return null!==this.hotMenu},executeCommand:function(e){if(this.isOpened()&&this.hotMenu.getSelected()){var t=this.hotMenu.getSourceDataAtRow(this.hotMenu.getSelected()[0]);if(this.runLocalHooks("select",t,e),t.isCommand!==!1){var n=this.hot.getSelectedRange(),o=n?H(n):{};this.runLocalHooks("executeCommand",t.key,o,e),this.isSubMenu()&&this.parentMenu.runLocalHooks("executeCommand",t.key,o,e),this.close(!0)}}},setPosition:function(e){var t=new C(e);this.options.keepInViewport?(t.fitsBelow(this.container)?this.setPositionBelowCursor(t):t.fitsAbove(this.container)?this.setPositionAboveCursor(t):this.setPositionBelowCursor(t),t.fitsOnRight(this.container)?this.setPositionOnRightOfCursor(t):this.setPositionOnLeftOfCursor(t)):(this.setPositionBelowCursor(t),this.setPositionOnRightOfCursor(t))},setPositionAboveCursor:function(e){var t=e.top-this.container.offsetHeight;this.isSubMenu()&&(t=window.scrollY+e.top+e.cellHeight-this.container.offsetHeight+3),this.container.style.top=t+"px"},setPositionBelowCursor:function(e){var t=e.top-1;this.isSubMenu()&&(t=e.top+window.scrollY-1),this.container.style.top=t+"px"},setPositionOnRightOfCursor:function(e){var t;t=this.isSubMenu()?window.scrollX+1+e.left+e.cellWidth:1+e.left,this.container.style.left=t+"px"},setPositionOnLeftOfCursor:function(e){this.container.style.left=e.left-this.container.offsetWidth+v()+4+"px"},selectFirstCell:function(){var e=this.hotMenu.getCell(0,0);T(e)||O(e)||M(e)?this.selectNextCell(0,0):this.hotMenu.selectCell(0,0)},selectLastCell:function(){var e=this.hotMenu.countRows()-1,t=this.hotMenu.getCell(e,0);T(t)||O(t)||M(t)?this.selectPrevCell(e,0):this.hotMenu.selectCell(e,0)},selectNextCell:function(e,t){var n=e+1,o=n<this.hotMenu.countRows()?this.hotMenu.getCell(n,t):null;o&&(T(o)||O(o)||M(o)?this.selectNextCell(n,t):this.hotMenu.selectCell(n,t))},selectPrevCell:function(e,t){var n=e-1,o=n>=0?this.hotMenu.getCell(n,t):null;o&&(T(o)||O(o)||M(o)?this.selectPrevCell(n,t):this.hotMenu.selectCell(n,t))},menuItemRenderer:function(e,t,n,o,r,i){var s=this,a=e.getSourceDataAtRow(n),l=document.createElement("div"),u=function(e){return e.hasOwnProperty("submenu")},c=function(e){return new RegExp(P,"i").test(e.name)},d=function(e){return e.disabled===!0||"function"==typeof e.disabled&&e.disabled.call(s.hot)===!0},h=function(e){return"function"==typeof e.hidden&&e.hidden.call(s.hot)===!0},f=function(e){return e.disableSelection},v=h(a);"function"==typeof i&&(i=i.call(this.hot)),g(t),p(l,"htItemWrapper"),t.appendChild(l),c(a)?p(t,"htSeparator"):v||"function"!=typeof a.renderer?m(l,i):(p(t,"htCustomMenuRenderer"),t.appendChild(a.renderer(e,l,n,o,r,i))),v?t.parentNode&&p(t.parentNode,"htHidden"):d(a)?(p(t,"htDisabled"),this.eventManager.addEventListener(l,"mouseenter",function(){return e.deselectCell()})):f(a)?(p(t,"htSelectionDisabled"),this.eventManager.addEventListener(l,"mouseenter",function(){return e.deselectCell()})):u(a)?(p(t,"htSubmenu"),f(a)?this.eventManager.addEventListener(l,"mouseenter",function(){return e.deselectCell()}):this.eventManager.addEventListener(l,"mouseenter",function(){return e.selectCell(n,o,void 0,void 0,void 0,!1)})):(y(t,"htSubmenu"),y(t,"htDisabled"),f(a)?this.eventManager.addEventListener(l,"mouseenter",function(){return e.deselectCell()}):this.eventManager.addEventListener(l,"mouseenter",function(){return e.selectCell(n,o,void 0,void 0,void 0,!1)}))},createContainer:function(){var e=void 0!==arguments[0]?arguments[0]:null;e&&(e=e.replace(/ /g,"_"),e=this.options.className+"Sub_"+e);var t;return t=e?document.querySelector("."+this.options.className+"."+e):document.querySelector("."+this.options.className),t||(t=document.createElement("div"),p(t,"htMenu "+this.options.className),e&&p(t,e),document.getElementsByTagName("body")[0].appendChild(t)),t},blockMainTableCallbacks:function(){this._afterScrollCallback=function(){},this.hot.addHook("afterScrollVertically",this._afterScrollCallback),this.hot.addHook("afterScrollHorizontally",this._afterScrollCallback)},releaseMainTableCallbacks:function(){this._afterScrollCallback&&(this.hot.removeHook("afterScrollVertically",this._afterScrollCallback),this.hot.removeHook("afterScrollHorizontally",this._afterScrollCallback),this._afterScrollCallback=null)},onBeforeKeyDown:function(e){var t=this.hotMenu.getSelected(),n=!1;switch(e.keyCode){case D.ESCAPE:this.close(),n=!0;break;case D.ENTER:t&&(this.hotMenu.getSourceDataAtRow(t[0]).submenu?n=!0:(this.executeCommand(e),this.close(!0)));break;case D.ARROW_DOWN:t?this.selectNextCell(t[0],t[1]):this.selectFirstCell(),n=!0;break;case D.ARROW_UP:t?this.selectPrevCell(t[0],t[1]):this.selectLastCell(),n=!0;break;case D.ARROW_RIGHT:if(t){var o=this.openSubMenu(t[0]);o&&o.selectFirstCell()}n=!0;break;case D.ARROW_LEFT:t&&this.isSubMenu()&&(this.close(),this.parentMenu&&this.parentMenu.hotMenu.listen(),n=!0)}n&&(e.preventDefault(),L(e))},onAfterInit:function(){var e=this.hotMenu.getSettings().data,t=this.hotMenu.view.wt.wtTable.hider.style,n=this.hotMenu.view.wt.wtTable.holder.style,o=parseInt(t.width,10),r=0;b(e,function(e){return r+=e.name===P?1:26}),n.width=o+22+"px",n.height=r+4+"px"},onDocumentMouseDown:function(e){this.isOpened()&&(this.container&&w(e.target,this.container)&&this.executeCommand(e),(this.isAllSubMenusClosed()||this.isSubMenu())&&!w(e.target,".htMenu")&&w(e.target,document)&&this.close(!0))}},{}),S(W,x)},{cursor:67,eventManager:41,"helpers/array":42,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/object":50,"helpers/unicode":53,"mixins/localHooks":54,predefinedItems:70,utils:71}],70:[function(e,t,n){"use strict";function o(){var e={};return c(D,function(t,n){return e[n]=d(t)}),e}function r(e,t){-1===H.indexOf(e)&&(D[e]=t)}var i;Object.defineProperties(n,{ROW_ABOVE:{get:function(){return w}},ROW_BELOW:{get:function(){return y}},COLUMN_LEFT:{get:function(){return b}},COLUMN_RIGHT:{get:function(){return C}},CLEAR_COLUMN:{get:function(){return R}},REMOVE_ROW:{get:function(){return _}},REMOVE_COLUMN:{get:function(){return S}},UNDO:{get:function(){return E}},REDO:{get:function(){return T}},READ_ONLY:{get:function(){return O}},ALIGNMENT:{get:function(){return M}},SEPARATOR:{get:function(){return k}},ITEMS:{get:function(){return H}},predefinedItems:{get:function(){return o}},addItem:{get:function(){return r}},__esModule:{value:!0}});var s,a,l,u=(s=e("helpers/object"),s&&s.__esModule&&s||{"default":s}),c=u.objectEach,d=u.clone,h=((a=e("helpers/number"),a&&a.__esModule&&a||{"default":a}).rangeEach,l=e("utils"),l&&l.__esModule&&l||{"default":l}),f=h.align,p=h.getAlignmentClasses,g=h.getValidSelection,m=h.checkSelectionConsistency,v=h.markLabelAsSelected,w="row_above",y="row_below",b="col_left",C="col_right",R="clear_column",_="remove_row",S="remove_col",E="undo",T="redo",O="make_read_only",M="alignment",k="---------",H=[w,y,b,C,R,_,S,E,T,O,M,k],D=(i={},Object.defineProperty(i,k,{value:{name:k},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,w,{value:{key:w,name:"Insert row above",callback:function(e,t){this.alter("insert_row",t.start.row)},disabled:function(){var e=g(this);if(!e)return!0;var t=this.countRows(),n=[0,e[1],t-1,e[1]];return n.join(",")===e.join(",")&&t>1},hidden:function(){return!this.getSettings().allowInsertRow}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,y,{value:{key:y,name:"Insert row below",callback:function(e,t){this.alter("insert_row",t.end.row+1)},disabled:function(){var e=g(this);if(!e)return!0;var t=this.countRows(),n=[0,e[1],t-1,e[1]];return n.join(",")===e.join(",")&&t>1},hidden:function(){return!this.getSettings().allowInsertRow}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,b,{value:{key:b,name:"Insert column on the left",callback:function(e,t){this.alter("insert_col",t.start.col)},disabled:function(){var e=g(this);if(!e)return!0;if(!this.isColumnModificationAllowed())return!0;var t=[e[0],0,e[0],this.countCols()-1],n=t.join(",")==e.join(",");return e[1]<0||this.countCols()>=this.getSettings().maxCols||n},hidden:function(){return!this.getSettings().allowInsertColumn}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,C,{value:{key:C,name:"Insert column on the right",callback:function(e,t){this.alter("insert_col",t.end.col+1)},disabled:function(){var e=g(this);if(!e)return!0;if(!this.isColumnModificationAllowed())return!0;var t=[e[0],0,e[0],this.countCols()-1],n=t.join(",")==e.join(",");return e[1]<0||this.countCols()>=this.getSettings().maxCols||n},hidden:function(){return!this.getSettings().allowInsertColumn}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,R,{value:{key:R,name:"Clear column",callback:function(e,t){var n=t.start.col;this.countRows()&&this.populateFromArray(0,n,[[null]],Math.max(t.start.row,t.end.row),n)},disabled:function(){var e=g(this);if(!e)return!0;var t=[e[0],0,e[0],this.countCols()-1],n=t.join(",")==e.join(",");return e[1]<0||this.countCols()>=this.getSettings().maxCols||n}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,_,{value:{key:_,name:"Remove row",callback:function(e,t){var n=t.end.row-t.start.row+1;this.alter("remove_row",t.start.row,n)},disabled:function(){var e=g(this);if(!e)return!0;var t=[0,e[1],this.countRows()-1,e[1]];return t.join(",")===e.join(",")},hidden:function(){return!this.getSettings().allowRemoveRow}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,S,{value:{key:S,name:"Remove column",callback:function(e,t){var n=t.end.col-t.start.col+1;this.alter("remove_col",t.start.col,n)},disabled:function(){var e=g(this);if(!e)return!0;if(!this.isColumnModificationAllowed())return!0;var t=[e[0],0,e[0],this.countCols()-1],n=t.join(",")==e.join(",");return e[1]<0||n},hidden:function(){return!this.getSettings().allowRemoveColumn}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,E,{value:{key:E,name:"Undo",callback:function(){this.undo()},disabled:function(){return this.undoRedo&&!this.undoRedo.isUndoAvailable()}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,T,{value:{key:T,name:"Redo",callback:function(){this.redo()},disabled:function(){return this.undoRedo&&!this.undoRedo.isRedoAvailable()}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,O,{value:{key:O,name:function(){var e=this,t="Read only",n=m(this.getSelectedRange(),function(t,n){return e.getCellMeta(t,n).readOnly});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=m(t,function(t,n){return e.getCellMeta(t,n).readOnly});t.forAll(function(t,o){e.getCellMeta(t,o).readOnly=n?!1:!0}),this.render()},disabled:function(){return this.getSelectedRange()?!1:!0}},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,M,{value:{key:M,name:"Alignment",disabled:function(){return this.getSelectedRange()?!1:!0},submenu:{items:[{key:M+":left",name:function(){var e=this,t="Left",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htLeft")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="horizontal",r="htLeft";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1},{key:M+":center",name:function(){var e=this,t="Center",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htCenter")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="horizontal",r="htCenter";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1},{key:M+":right",name:function(){var e=this,t="Right",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htRight")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="horizontal",r="htRight";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1},{key:M+":justify",name:function(){var e=this,t="Justify",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htJustify")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="horizontal",r="htJustify";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1},{name:k},{key:M+":top",name:function(){var e=this,t="Top",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htTop")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="vertical",r="htTop";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1},{key:M+":middle",name:function(){var e=this,t="Middle",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htMiddle")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="vertical",r="htMiddle";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1},{key:M+":bottom",name:function(){var e=this,t="Bottom",n=m(this.getSelectedRange(),function(t,n){var o=e.getCellMeta(t,n).className;return o&&-1!==o.indexOf("htBottom")?!0:void 0});return n&&(t=v(t)),t},callback:function(){var e=this,t=this.getSelectedRange(),n=p(t,function(t,n){return e.getCellMeta(t,n).className}),o="vertical",r="htBottom";this.runHooks("beforeCellAlignment",n,t,o,r),f(t,o,r,function(t,n){return e.getCellMeta(t,n)}),this.render()},disabled:!1}]}},configurable:!0,enumerable:!0,writable:!0}),i)},{"helpers/number":49,"helpers/object":50,utils:71}],71:[function(e,t,n){"use strict";function o(e){return{start:e.getTopLeftCorner(),end:e.getBottomRightCorner()}}function r(e){return v(e,"htSeparator")}function i(e){return v(e,"htSubmenu")}function s(e){return v(e,"htDisabled")}function a(e){return v(e,"htSelectionDisabled")}function l(e){var t=e.getSelected();return t?t[0]<0?null:e.countRows()>=e.getSettings().maxRows?null:t:null}function u(e,t){return-1!=e.indexOf(t)?e:(e=e.replace("htTop","").replace("htMiddle","").replace("htBottom","").replace("  ",""),e+=" "+t)}function c(e,t){return-1!=e.indexOf(t)?e:(e=e.replace("htLeft","").replace("htCenter","").replace("htRight","").replace("htJustify","").replace("  ",""),e+=" "+t)}function d(e,t){for(var n={},o=e.from.row;o<=e.to.row;o++)for(var r=e.from.col;r<=e.to.col;r++)n[o]||(n[o]=[]),n[o][r]=t(o,r);return n}function h(e,t,n,o){if(e.from.row==e.to.row&&e.from.col==e.to.col)f(e.from.row,e.from.col,t,n,o);else for(var r=e.from.row;r<=e.to.row;r++)for(var i=e.from.col;i<=e.to.col;i++)f(r,i,t,n,o)}function f(e,t,n,o,r){var i=r(e,t),s=o;i.className&&(s="vertical"===n?u(i.className,o):c(i.className,o)),i.className=s}function p(e,t){var n=!1;return e&&e.forAll(function(e,o){return t(e,o)?(n=!0,!1):void 0}),n}function g(e){return'<span class="selected">'+String.fromCharCode(10003)+"</span>"+e}Object.defineProperties(n,{normalizeSelection:{get:function(){return o}},isSeparator:{get:function(){return r}},hasSubMenu:{get:function(){return i}},isDisabled:{get:function(){return s}},isSelectionDisabled:{get:function(){return a}},getValidSelection:{get:function(){return l}},prepareVerticalAlignClass:{get:function(){return u}},prepareHorizontalAlignClass:{get:function(){return c}},getAlignmentClasses:{get:function(){return d}},align:{get:function(){return h}},checkSelectionConsistency:{get:function(){return p}},markLabelAsSelected:{get:function(){return g}},__esModule:{value:!0}});var m,v=(m=e("helpers/dom/element"),m&&m.__esModule&&m||{"default":m}).hasClass},{"helpers/dom/element":45}],72:[function(e,t,n){"use strict";Object.defineProperties(n,{ContextMenuCopyPaste:{get:function(){return g}},__esModule:{value:!0}});var o,r,i,s,a,l,u=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}).removeClass,c=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}).arrayEach,d=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).EventManager,h=(s=e("plugins"),s&&s.__esModule&&s||{"default":s}).registerPlugin,f=(a=e("_base"),a&&a.__esModule&&a||{"default":a})["default"],p=(l=e("zeroclipboard"),l&&l.__esModule&&l||{"default":l})["default"],g=function(e){$traceurRuntime.superConstructor(m).call(this,e),this.eventManager=new d(this),this.swfPath=null,this.outsideClickDeselectsCache=null},m=g;$traceurRuntime.createClass(g,{isEnabled:function(){return this.hot.getSettings().contextMenuCopyPaste},enablePlugin:function(){var e=this;if(!this.enabled){"object"==typeof this.hot.getSettings().contextMenuCopyPaste&&(this.swfPath=this.hot.getSettings().contextMenuCopyPaste.swfPath),"undefined"==typeof p&&console.error("To be able to use the Copy/Paste feature from the context menu, you need to manually include ZeroClipboard.js file to your website.");try{new ActiveXObject("ShockwaveFlash.ShockwaveFlash")}catch(t){"undefined"==typeof navigator.mimeTypes["application/x-shockwave-flash"]&&console.error("To be able to use the Copy/Paste feature from the context menu, your browser needs to have Flash Plugin installed.")}this.swfPath&&p.config({swfPath:this.swfPath}),this.hot.addHook("afterContextMenuShow",function(){return e.onAfterContextMenuShow()}),this.hot.addHook("afterContextMenuDefaultOptions",function(t){return e.onAfterContextMenuDefaultOptions(t)}),this.registerEvents(),$traceurRuntime.superGet(this,m.prototype,"enablePlugin").call(this)}},disablePlugin:function(){$traceurRuntime.superGet(this,m.prototype,"disablePlugin").call(this)},registerEvents:function(){var e=this;this.eventManager.addEventListener(document,"mouseenter",function(){return e.removeCurrentClass()}),this.eventManager.addEventListener(document,"mouseleave",function(){return e.removeZeroClipboardClass()})},getCopyValue:function(){return this.hot.copyPaste.setCopyableText(),this.hot.copyPaste.copyPasteInstance.elTextarea.value},onAfterContextMenuDefaultOptions:function(e){e.items.unshift({key:"copy",name:"Copy"},{key:"paste",name:"Paste",callback:function(){this.copyPaste.triggerPaste()}},Handsontable.plugins.ContextMenu.SEPARATOR)},onAfterContextMenuShow:function(){var e=this,t=this.hot.getPlugin("contextMenu"),n=t.menu.hotMenu.getSourceData();c(n,function(n,o){if("copy"===n.key){var r=new p(t.menu.hotMenu.getCell(o,0));return r.off(),r.on("copy",function(t){var n=t.clipboardData;n.setData("text/plain",e.getCopyValue()),e.hot.getSettings().outsideClickDeselects=e.outsideClickDeselectsCache}),!1}})},removeCurrentClass:function(){var e=this.hot.getPlugin("contextMenu");if(e.menu.isOpened()){var t=e.menu.hotMenu.rootElement.querySelector("td.current");t&&u(t,"current")}this.outsideClickDeselectsCache=this.hot.getSettings().outsideClickDeselects,this.hot.getSettings().outsideClickDeselects=!1},removeZeroClipboardClass:function(){var e=this.hot.getPlugin("contextMenu");if(e.menu.isOpened()){var t=e.menu.hotMenu.rootElement.querySelector("td.zeroclipboard-is-hover");t&&u(t,"zeroclipboard-is-hover")}this.hot.getSettings().outsideClickDeselects=this.outsideClickDeselectsCache}},{},f),h("contextMenuCopyPaste",g)},{_base:58,eventManager:41,"helpers/array":42,"helpers/dom/element":45,plugins:57,zeroclipboard:"zeroclipboard"}],73:[function(e,t,n){"use strict";function o(e){function t(){e.isListening()&&e.selection.empty()}function n(t){var n,o,r,i,s,a,l,u,c,d;if(e.isListening()&&e.selection.isSelected()){n=t,o=v.parse(n),r=e.getSelected(),i=new M(r[0],r[1]),s=new M(r[2],r[3]),a=new k(i,i,s),l=a.getTopLeftCorner(),u=a.getBottomRightCorner(),c=l,d=new M(Math.max(u.row,o.length-1+l.row),Math.max(u.col,o[0].length-1+l.col));var h=s.row-i.row>=o.length-1,f=s.col-i.col>=o[0].length-1;e.addHookOnce("afterChange",function(t,n){var o=t?t.length:0;if(o){var r={row:0,col:0},i=-1;C(t,function(e,n){var s=o>n+1?t[n+1]:null;s&&(h||(r.row=r.row+Math.max(s[0]-e[0]-1,0)),!f&&e[1]>i&&(i=e[1],r.col=r.col+Math.max(s[1]-e[1]-1,0)))}),e.selectCell(c.row,c.col,d.row+r.row,d.col+r.col)}}),e.populateFromArray(c.row,c.col,o,d.row,d.col,"paste",e.getSettings().pasteMode)}}function o(t){if(e.getSelected()&&!(e.getActiveEditor()&&e.getActiveEditor().isOpened()||E(t))){if(b(t.keyCode)){if(e.getSettings().fragmentSelection&&T())return;return r.setCopyableText(),void S(t)}var n=(t.ctrlKey||t.metaKey)&&!t.altKey;t.keyCode==y.A&&n&&e._registerTimeout(setTimeout(O(r.setCopyableText,r),0))}}var r=this;this.copyPasteInstance=m(),this.copyPasteInstance.onCut(t),this.copyPasteInstance.onPaste(n),this.onPaste=n,e.addHook("beforeKeyDown",o),this.destroy=function(){this.copyPasteInstance&&(this.copyPasteInstance.removeCallback(t),this.copyPasteInstance.removeCallback(n),this.copyPasteInstance.destroy(),this.copyPasteInstance=null),e.removeHook("beforeKeyDown",o)},e.addHook("afterDestroy",O(this.destroy,this)),this.triggerPaste=O(this.copyPasteInstance.triggerPaste,this.copyPasteInstance),this.triggerCut=O(this.copyPasteInstance.triggerCut,this.copyPasteInstance),this.setCopyableText=function(){var t=e.getSettings(),n=t.copyRowsLimit,o=t.copyColsLimit,r=e.getSelectedRange(),i=r.getTopLeftCorner(),s=r.getBottomRightCorner(),a=i.row,l=i.col,u=s.row,c=s.col,d=Math.min(u,a+n-1),h=Math.min(c,l+o-1),f=[];f.push({startRow:a,startCol:l,endRow:d,endCol:h}),f=Handsontable.hooks.run(e,"modifyCopyableRange",f);var p=this.getRangedCopyableData(f);e.copyPaste.copyPasteInstance.copyable(p),(u!==d||c!==h)&&Handsontable.hooks.run(e,"afterCopyLimit",u-a+1,c-l+1,n,o)},this.getRangedCopyableData=function(t){var n=[],o=[],r=[];return C(t,function(e){R(e.startRow,e.endRow,function(e){-1===o.indexOf(e)&&o.push(e)}),R(e.startCol,e.endCol,function(e){-1===r.indexOf(e)&&r.push(e)})}),C(o,function(t){var o=[];C(r,function(n){o.push(e.getCopyableData(t,n))}),n.push(o)}),v.stringify(n)}}function r(){var e=this,t=e.getSettings().copyPaste!==!1;t&&!e.copyPaste?e.copyPaste=new o(e):!t&&e.copyPaste&&(e.copyPaste.destroy(),e.copyPaste=null)}Object.defineProperties(n,{CopyPastePlugin:{get:function(){return o}},__esModule:{value:!0}});var i,s,a,l,u,c,d,h,f,p,g,m=(i=e("copyPaste"),i&&i.__esModule&&i||{"default":i})["default"],v=(s=e("SheetClip"),s&&s.__esModule&&s||{"default":s})["default"],w=(a=e("helpers/unicode"),a&&a.__esModule&&a||{"default":a}),y=w.KEY_CODES,b=w.isCtrlKey,C=(l=e("helpers/array"),l&&l.__esModule&&l||{"default":l}).arrayEach,R=(u=e("helpers/number"),u&&u.__esModule&&u||{"default":u}).rangeEach,_=(c=e("helpers/dom/event"),c&&c.__esModule&&c||{"default":c}),S=_.stopImmediatePropagation,E=_.isImmediatePropagationStopped,T=(d=e("helpers/dom/element"),d&&d.__esModule&&d||{"default":d}).getSelectionText,O=(h=e("helpers/function"),h&&h.__esModule&&h||{"default":h}).proxy,M=((f=e("plugins"),f&&f.__esModule&&f||{"default":f}).registerPlugin,(p=e("3rdparty/walkontable/src/cell/coords"),p&&p.__esModule&&p||{"default":p}).WalkontableCellCoords),k=(g=e("3rdparty/walkontable/src/cell/range"),g&&g.__esModule&&g||{"default":g}).WalkontableCellRange;Handsontable.hooks.add("afterInit",r),Handsontable.hooks.add("afterUpdateSettings",r),Handsontable.hooks.register("afterCopyLimit"),Handsontable.hooks.register("modifyCopyableRange")},{"3rdparty/walkontable/src/cell/coords":5,"3rdparty/walkontable/src/cell/range":6,SheetClip:"SheetClip",copyPaste:"copyPaste","helpers/array":42,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/function":47,"helpers/number":49,"helpers/unicode":53,plugins:57}],74:[function(e,t,n){"use strict";function o(){}var r,i,s,a,l=((r=e("plugins"),r&&r.__esModule&&r||{"default":r}).registerPlugin,(i=e("3rdparty/walkontable/src/cell/range"),i&&i.__esModule&&i||{"default":i}).WalkontableCellRange),u=(s=e("3rdparty/walkontable/src/selection"),s&&s.__esModule&&s||{"default":s}).WalkontableSelection,c=function(e){return"boolean"==typeof e&&e===!0?!0:"object"==typeof e&&e.length>0?!0:!1},d=function(){c(this.getSettings().customBorders)&&(this.customBorders||(a=this,this.customBorders=new o))},h=function(e){for(var t=0;t<a.view.wt.selections.length;t++)if(a.view.wt.selections[t].settings.className==e)return t;return-1},f=function(e){var t={row:e.row,col:e.col},n=new u(e,new l(t,t,t)),o=h(e.className);o>=0?a.view.wt.selections[o]=n:a.view.wt.selections.push(n)},p=function(e,t,n){var o=b(e,t);o=C(o,n),this.setCellMeta(e,t,"borders",o),f(o)},g=function(e){for(var t=e.range,n=t.from.row;n<=t.to.row;n++)for(var o=t.from.col;o<=t.to.col;o++){var r=b(n,o),i=0;n==t.from.row&&(i++,e.hasOwnProperty("top")&&(r.top=e.top)),n==t.to.row&&(i++,e.hasOwnProperty("bottom")&&(r.bottom=e.bottom)),o==t.from.col&&(i++,e.hasOwnProperty("left")&&(r.left=e.left)),o==t.to.col&&(i++,e.hasOwnProperty("right")&&(r.right=e.right)),i>0&&(this.setCellMeta(n,o,"borders",r),f(r))}},m=function(e,t){return"border_row"+e+"col"+t},v=function(){return{width:1,color:"#000"}},w=function(){return{hide:!0}},y=function(){return{width:1,color:"#000",cornerVisible:!1}},b=function(e,t){return{className:m(e,t),border:y(),row:e,col:t,top:w(),right:w(),bottom:w(),left:w()}},C=function(e,t){return t.hasOwnProperty("border")&&(e.border=t.border),t.hasOwnProperty("top")&&(e.top=t.top),t.hasOwnProperty("right")&&(e.right=t.right),t.hasOwnProperty("bottom")&&(e.bottom=t.bottom),t.hasOwnProperty("left")&&(e.left=t.left),e},R=function(e){for(var t=document.querySelectorAll("."+e),n=0;n<t.length;n++)if(t[n]&&"TD"!=t[n].nodeName){var o=t[n].parentNode;o.parentNode&&o.parentNode.removeChild(o)}},_=function(e,t){var n=m(e,t);R(n),this.removeCellMeta(e,t,"borders")},S=function(e,t,n,o){var r=this.getCellMeta(e,t).borders;r&&void 0!=r.border||(r=b(e,t)),o?r[n]=w():r[n]=v(),this.setCellMeta(e,t,"borders",r);var i=m(e,t);R(i),f(r),this.render()},E=function(e,t,n){if(e.from.row==e.to.row&&e.from.col==e.to.col)"noBorders"==t?_.call(this,e.from.row,e.from.col):S.call(this,e.from.row,e.from.col,t,n);else switch(t){case"noBorders":for(var o=e.from.col;o<=e.to.col;o++)for(var r=e.from.row;r<=e.to.row;r++)_.call(this,r,o);break;case"top":for(var i=e.from.col;i<=e.to.col;i++)S.call(this,e.from.row,i,t,n);break;case"right":for(var s=e.from.row;s<=e.to.row;s++)S.call(this,s,e.to.col,t);break;case"bottom":for(var a=e.from.col;a<=e.to.col;a++)S.call(this,e.to.row,a,t);break;case"left":for(var l=e.from.row;l<=e.to.row;l++)S.call(this,l,e.from.col,t)}},T=function(e,t){var n=!1;return e.getSelectedRange().forAll(function(o,r){var i=e.getCellMeta(o,r).borders;if(i){if(!t)return n=!0,!1;if(!i[t].hasOwnProperty("hide"))return n=!0,!1}}),n},O=function(e){return'<span class="selected">'+String.fromCharCode(10003)+"</span>"+e},M=function(e){this.getSettings().customBorders&&(e.items.push(Handsontable.plugins.ContextMenu.SEPARATOR),e.items.push({key:"borders",name:"Borders",submenu:{items:[{key:"borders:top",name:function(){var e="Top",t=T(this,"top");return t&&(e=O(e)),e},callback:function(){var e=T(this,"top");E.call(this,this.getSelectedRange(),"top",e)},disabled:!1},{key:"borders:right",name:function(){var e="Right",t=T(this,"right");return t&&(e=O(e)),e},callback:function(){var e=T(this,"right");E.call(this,this.getSelectedRange(),"right",e)},disabled:!1},{key:"borders:bottom",name:function(){var e="Bottom",t=T(this,"bottom");return t&&(e=O(e)),e},callback:function(){var e=T(this,"bottom");E.call(this,this.getSelectedRange(),"bottom",e)},disabled:!1},{key:"borders:left",name:function(){var e="Left",t=T(this,"left");return t&&(e=O(e)),e},callback:function(){var e=T(this,"left");E.call(this,this.getSelectedRange(),"left",e);
},disabled:!1},{key:"borders:no_borders",name:"Remove border(s)",callback:function(){E.call(this,this.getSelectedRange(),"noBorders")},disabled:function(){return!T(this)}}]}}))};Handsontable.hooks.add("beforeInit",d),Handsontable.hooks.add("afterContextMenuDefaultOptions",M),Handsontable.hooks.add("afterInit",function(){var e=this.getSettings().customBorders;if(e){for(var t=0;t<e.length;t++)e[t].range?g.call(this,e[t]):p.call(this,e[t].row,e[t].col,e[t]);this.render(),this.view.wt.draw(!0)}}),Handsontable.CustomBorders=o},{"3rdparty/walkontable/src/cell/range":6,"3rdparty/walkontable/src/selection":18,plugins:57}],75:[function(e,t,n){"use strict";function o(){this.boundaries=null,this.callback=null}Object.defineProperties(n,{DragToScroll:{get:function(){return o}},__esModule:{value:!0}});var r,i,s=(r=e("eventManager"),r&&r.__esModule&&r||{"default":r}).eventManager;(i=e("plugins"),i&&i.__esModule&&i||{"default":i}).registerPlugin;Handsontable.plugins.DragToScroll=o,o.prototype.setBoundaries=function(e){this.boundaries=e},o.prototype.setCallback=function(e){this.callback=e},o.prototype.check=function(e,t){var n=0,o=0;t<this.boundaries.top?o=t-this.boundaries.top:t>this.boundaries.bottom&&(o=t-this.boundaries.bottom),e<this.boundaries.left?n=e-this.boundaries.left:e>this.boundaries.right&&(n=e-this.boundaries.right),this.callback(n,o)};var a,l=function(e){e.dragToScrollListening=!1;var t=e.view.wt.wtTable.holder;a=new o,t!==window&&(a.setBoundaries(t.getBoundingClientRect()),a.setCallback(function(e,n){0>e?t.scrollLeft-=50:e>0&&(t.scrollLeft+=50),0>n?t.scrollTop-=20:n>0&&(t.scrollTop+=20)}),e.dragToScrollListening=!0)};Handsontable.hooks.add("afterInit",function(){var e=this,t=s(this);t.addEventListener(document,"mouseup",function(){e.dragToScrollListening=!1}),t.addEventListener(document,"mousemove",function(t){e.dragToScrollListening&&a.check(t.clientX,t.clientY)})}),Handsontable.hooks.add("afterDestroy",function(){s(this).clear()}),Handsontable.hooks.add("afterOnCellMouseDown",function(){l(this)}),Handsontable.hooks.add("afterOnCellCornerMouseDown",function(){l(this)}),Handsontable.plugins.DragToScroll=o},{eventManager:41,plugins:57}],76:[function(e,t,n){"use strict";Object.defineProperties(n,{ManualColumnFreeze:{get:function(){return a}},__esModule:{value:!0}});var o,r,i=(o=e("_base"),o&&o.__esModule&&o||{"default":o})["default"],s=(r=e("plugins"),r&&r.__esModule&&r||{"default":r}).registerPlugin,a=function(e){$traceurRuntime.superConstructor(l).call(this,e)},l=a;$traceurRuntime.createClass(a,{isEnabled:function(){return!!this.hot.getSettings().manualColumnFreeze},enablePlugin:function(){var e=this;this.enabled||(this.addHook("modifyCol",function(t){return e.onModifyCol(t)}),this.addHook("afterContextMenuDefaultOptions",function(t){return e.addContextMenuEntry(t)}),$traceurRuntime.superGet(this,l.prototype,"enablePlugin").call(this))},disablePlugin:function(){$traceurRuntime.superGet(this,l.prototype,"disablePlugin").call(this)},init:function(){$traceurRuntime.superGet(this,l.prototype,"init").call(this),"undefined"==typeof this.hot.manualColumnPositionsPluginUsages?this.hot.manualColumnPositionsPluginUsages=["manualColumnFreeze"]:this.hot.manualColumnPositionsPluginUsages.push("manualColumnFreeze"),this.fixedColumnsCount=this.hot.getSettings().fixedColumnsLeft},onModifyCol:function(e){return this.hot.manualColumnPositionsPluginUsages.length>1?e:this.getModifiedColumnIndex(e)},getModifiedColumnIndex:function(e){return this.hot.manualColumnPositions[e]},addContextMenuEntry:function(e){var t=this;e.items.push(Handsontable.plugins.ContextMenu.SEPARATOR,{key:"freeze_column",name:function(){var e=t.hot.getSelected()[1];return e>t.fixedColumnsCount-1?"Freeze this column":"Unfreeze this column"},disabled:function(){var e=t.hot.getSelected();return e[1]!==e[3]},callback:function(){var e=t.hot.getSelected()[1];e>t.fixedColumnsCount-1?t.freezeColumn(e):t.unfreezeColumn(e)}})},freezeColumn:function(e){if(!(e<=this.fixedColumnsCount-1)){var t=this.getModifiedColumnIndex(e)||e;this.checkPositionData(t),this.modifyColumnOrder(t,e,null,"freeze"),this.addFixedColumn(),this.hot.view.wt.wtOverlays.leftOverlay.refresh(),this.hot.view.wt.wtOverlays.adjustElementsSize()}},unfreezeColumn:function(e){if(!(e>this.fixedColumnsCount-1)){var t=this.getBestColumnReturnPosition(e),n=this.getModifiedColumnIndex(e)||e;this.checkPositionData(n),this.modifyColumnOrder(n,e,t,"unfreeze"),this.removeFixedColumn(),this.hot.view.wt.wtOverlays.leftOverlay.refresh(),this.hot.view.wt.wtOverlays.adjustElementsSize()}},addFixedColumn:function(){this.hot.updateSettings({fixedColumnsLeft:this.fixedColumnsCount+1}),this.fixedColumnsCount++},removeFixedColumn:function(){this.hot.updateSettings({fixedColumnsLeft:this.fixedColumnsCount-1}),this.fixedColumnsCount--},checkPositionData:function(e){this.hot.manualColumnPositions&&0!==this.hot.manualColumnPositions.length||this.hot.manualColumnPositions||(this.hot.manualColumnPositions=[]),e?this.hot.manualColumnPositions[e]||this.createPositionData(e+1):this.createPositionData(this.hot.countCols())},createPositionData:function(e){if(this.hot.manualColumnPositions.length<e)for(var t=this.hot.manualColumnPositions.length;e>t;t++)this.hot.manualColumnPositions[t]=t},modifyColumnOrder:function(e,t,n,o){null==n&&(n=e),"freeze"===o?this.hot.manualColumnPositions.splice(this.fixedColumnsCount,0,this.hot.manualColumnPositions.splice(t,1)[0]):"unfreeze"===o&&this.hot.manualColumnPositions.splice(n,0,this.hot.manualColumnPositions.splice(t,1)[0])},getBestColumnReturnPosition:function(e){for(var t=this.fixedColumnsCount,n=this.getModifiedColumnIndex(t),o=this.getModifiedColumnIndex(e);o>n;)t++,n=this.getModifiedColumnIndex(t);return t-1}},{},i),s("manualColumnFreeze",a)},{_base:58,plugins:57}],77:[function(e,t,n){"use strict";function o(){function e(e){g=this,m=e;var t=this.view.wt.wtTable.getCoords(e).col;if(t>=0){f=t;var n=m.getBoundingClientRect();l=n.left,v.style.top=n.top+"px",v.style.left=l+"px",g.rootElement.appendChild(v)}}function t(e,t){var n=e.getBoundingClientRect(),o=6;t>0?v.style.left=n.left+n.width-o+"px":v.style.left=n.left+"px"}function n(){var e=this;u(v,"active"),u(w,"active");var t=m.getBoundingClientRect();w.style.width=t.width+"px",w.style.height=e.view.maximumVisibleElementHeight(0)+"px",w.style.top=v.style.top,w.style.left=l+"px",e.rootElement.appendChild(w)}function o(e){w.style.left=l+e+"px"}function r(){d(v,"active"),d(w,"active")}var i,s,a,l,f,g,m,v=document.createElement("DIV"),w=document.createElement("DIV"),y=h(this);v.className="manualColumnMover",w.className="manualColumnMoverGuide";var b=function(){var e=this;Handsontable.hooks.run(e,"persistentStateSave","manualColumnPositions",e.manualColumnPositions)},C=function(){var e=this,t={};return Handsontable.hooks.run(e,"persistentStateLoad","manualColumnPositions",t),t.value},R=function(e){return"BODY"!=e.tagName?"THEAD"==e.parentNode.tagName?!0:(e=e.parentNode,R(e)):!1},_=function(e){return"TABLE"!=e.tagName?"TH"==e.tagName?e:_(e.parentNode):null},S=function(){var l,u=this;y.addEventListener(u.rootElement,"mouseover",function(n){if(R(n.target)){var o=_(n.target);if(o)if(l){var r=u.view.wt.wtTable.getCoords(o).col;r>=0&&(s=r,t(n.target,s-i))}else e.call(u,o)}}),y.addEventListener(u.rootElement,"mousedown",function(e){c(e.target,"manualColumnMover")&&(a=p(e),n.call(u),l=u,i=f,s=f)}),y.addEventListener(window,"mousemove",function(e){l&&o(p(e)-a)}),y.addEventListener(window,"mouseup",function(t){l&&(r(),l=!1,T(u.manualColumnPositions,u.countCols()),u.manualColumnPositions.splice(s,0,u.manualColumnPositions.splice(i,1)[0]),Handsontable.hooks.run(u,"beforeColumnMove",i,s),u.forceFullRender=!0,u.view.render(),b.call(u),Handsontable.hooks.run(u,"afterColumnMove",i,s),e.call(u,m))}),u.addHook("afterDestroy",E)},E=function(){y.clear()},T=function(e,t){if(e.length<t)for(var n=e.length;t>n;n++)e[n]=n};this.beforeInit=function(){this.manualColumnPositions=[]},this.init=function(e){var t=this,n=!!this.getSettings().manualColumnMove;if(n){var o=this.getSettings().manualColumnMove,r=C.call(t);"undefined"!=typeof r?this.manualColumnPositions=r:Array.isArray(o)?this.manualColumnPositions=o:this.manualColumnPositions=[],("afterInit"===e||"afterUpdateSettings"===e&&0===y.context.eventListeners.length)&&("undefined"==typeof t.manualColumnPositionsPluginUsages?t.manualColumnPositionsPluginUsages=["manualColumnMove"]:t.manualColumnPositionsPluginUsages.push("manualColumnMove"),S.call(this),this.manualColumnPositions.length>0&&(this.forceFullRender=!0,this.render()))}else{var i=t.manualColumnPositionsPluginUsages?t.manualColumnPositionsPluginUsages.indexOf("manualColumnMove"):-1;i>-1&&(E.call(this),this.manualColumnPositions=[],t.manualColumnPositionsPluginUsages[i]=void 0)}},this.modifyCol=function(e){return this.getSettings().manualColumnMove?("undefined"==typeof this.manualColumnPositions[e]&&T(this.manualColumnPositions,e+1),this.manualColumnPositions[e]):e},this.afterRemoveCol=function(e,t){if(this.getSettings().manualColumnMove){var n,o=this.manualColumnPositions;n=o.splice(e,t),o=o.map(function(e){var t,o=e;for(t=0;t<n.length;t++)e>n[t]&&o--;return o}),this.manualColumnPositions=o}},this.afterCreateCol=function(e,t){if(this.getSettings().manualColumnMove){var n=this.manualColumnPositions;if(n.length){for(var o=[],r=0;t>r;r++)o.push(e+r);e>=n.length?n.concat(o):(n=n.map(function(n){return n>=e?n+t:n}),n.splice.apply(n,[e,0].concat(o))),this.manualColumnPositions=n}}}}Object.defineProperties(n,{ManualColumnMove:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),u=l.addClass,c=l.hasClass,d=l.removeClass,h=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,f=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),p=f.pageX,g=(f.pageY,(a=e("plugins"),a&&a.__esModule&&a||{"default":a}).registerPlugin,new o);Handsontable.hooks.add("beforeInit",g.beforeInit),Handsontable.hooks.add("afterInit",function(){g.init.call(this,"afterInit")}),Handsontable.hooks.add("afterUpdateSettings",function(){g.init.call(this,"afterUpdateSettings")}),Handsontable.hooks.add("modifyCol",g.modifyCol),Handsontable.hooks.add("afterRemoveCol",g.afterRemoveCol),Handsontable.hooks.add("afterCreateCol",g.afterCreateCol),Handsontable.hooks.register("beforeColumnMove"),Handsontable.hooks.register("afterColumnMove")},{eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,plugins:57}],78:[function(e,t,n){"use strict";Object.defineProperties(n,{ManualColumnResize:{get:function(){return v}},__esModule:{value:!0}});var o,r,i,s,a,l=(o=e("_base.js"),o&&o.__esModule&&o||{"default":o})["default"],u=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),c=u.addClass,d=u.hasClass,h=u.removeClass,f=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,p=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),g=p.pageX,m=(p.pageY,(a=e("plugins"),a&&a.__esModule&&a||{"default":a}).registerPlugin),v=function(e){$traceurRuntime.superConstructor(w).call(this,e),this.currentTH=null,this.currentCol=null,this.currentWidth=null,this.newSize=null,this.startY=null,this.startWidth=null,this.startOffset=null,this.handle=document.createElement("DIV"),this.guide=document.createElement("DIV"),this.eventManager=f(this),this.pressed=null,this.dblclick=0,this.autoresizeTimeout=null,this.manualColumnWidths=[],c(this.handle,"manualColumnResizer"),c(this.guide,"manualColumnResizerGuide")},w=v;$traceurRuntime.createClass(v,{isEnabled:function(){return this.hot.getSettings().manualColumnResize},enablePlugin:function(){var e=this;if(!this.enabled){this.manualColumnWidths=[];var t=this.hot.getSettings().manualColumnResize,n=this.loadManualColumnWidths();this.addHook("modifyColWidth",function(t,n){return e.onModifyColWidth(t,n)}),"undefined"!=typeof n?this.manualColumnWidths=n:Array.isArray(t)?this.manualColumnWidths=t:this.manualColumnWidths=[],Handsontable.hooks.register("beforeColumnResize"),Handsontable.hooks.register("afterColumnResize"),this.bindEvents(),$traceurRuntime.superGet(this,w.prototype,"enablePlugin").call(this)}},updatePlugin:function(){var e=this.hot.getSettings().manualColumnResize;Array.isArray(e)?this.manualColumnWidths=e:this.manualColumnWidths=[]},disablePlugin:function(){$traceurRuntime.superGet(this,w.prototype,"disablePlugin").call(this)},saveManualColumnWidths:function(){this.hot.runHooks("persistentStateSave","manualColumnWidths",this.manualColumnWidths)},loadManualColumnWidths:function(){var e={};return this.hot.runHooks("persistentStateLoad","manualColumnWidths",e),e.value},setupHandlePosition:function(e){this.currentTH=e;var t=this.hot.view.wt.wtTable.getCoords(e).col;if(t>=0){var n=this.currentTH.getBoundingClientRect();this.currentCol=t,this.startOffset=n.left-6,this.startWidth=parseInt(n.width,10),this.handle.style.top=n.top+"px",this.handle.style.left=this.startOffset+this.startWidth+"px",this.hot.rootElement.appendChild(this.handle)}},refreshHandlePosition:function(){this.handle.style.left=this.startOffset+this.currentWidth+"px"},setupGuidePosition:function(){c(this.handle,"active"),c(this.guide,"active"),this.guide.style.top=this.handle.style.top,this.guide.style.left=this.handle.style.left,this.guide.style.height=this.hot.view.maximumVisibleElementHeight(0)+"px",this.hot.rootElement.appendChild(this.guide)},refreshGuidePosition:function(){this.guide.style.left=this.handle.style.left},hideHandleAndGuide:function(){h(this.handle,"active"),h(this.guide,"active")},checkIfColumnHeader:function(e){return"BODY"!=e.tagName?"THEAD"==e.parentNode.tagName?!0:(e=e.parentNode,this.checkIfColumnHeader(e)):!1},getTHFromTargetElement:function(e){return"TABLE"!=e.tagName?"TH"==e.tagName?e:this.getTHFromTargetElement(e.parentNode):null},onMouseOver:function(e){if(this.checkIfColumnHeader(e.target)){var t=this.getTHFromTargetElement(e.target);if(!t)return;var n=t.getAttribute("colspan");!t||null!==n&&1!==n||this.pressed||this.setupHandlePosition(t)}},afterMouseDownTimeout:function(){if(this.dblclick>=2){var e=this.hot.runHooks("beforeColumnResize",this.currentCol,this.newSize,!0);void 0!==e&&(this.newSize=e),this.setManualSize(this.currentCol,this.newSize),this.hot.forceFullRender=!0,this.hot.view.render(),this.hot.view.wt.wtOverlays.adjustElementsSize(!0),this.hot.runHooks("afterColumnResize",this.currentCol,this.newSize,!0)}this.dblclick=0,this.autoresizeTimeout=null},onMouseDown:function(e){var t=this;d(e.target,"manualColumnResizer")&&(this.setupGuidePosition(),this.pressed=this.hot,null===this.autoresizeTimeout&&(this.autoresizeTimeout=setTimeout(function(){return t.afterMouseDownTimeout()},500),this.hot._registerTimeout(this.autoresizeTimeout)),this.dblclick++,this.startX=g(e),this.newSize=this.startWidth)},onMouseMove:function(e){this.pressed&&(this.currentWidth=this.startWidth+(g(e)-this.startX),this.newSize=this.setManualSize(this.currentCol,this.currentWidth),this.refreshHandlePosition(),this.refreshGuidePosition())},onMouseUp:function(e){this.pressed&&(this.hideHandleAndGuide(),this.pressed=!1,this.newSize!=this.startWidth&&(this.hot.runHooks("beforeColumnResize",this.currentCol,this.newSize),this.hot.forceFullRender=!0,this.hot.view.render(),this.hot.view.wt.wtOverlays.adjustElementsSize(!0),this.saveManualColumnWidths(),this.hot.runHooks("afterColumnResize",this.currentCol,this.newSize)),this.setupHandlePosition(this.currentTH))},bindEvents:function(){var e=this;this.eventManager.addEventListener(this.hot.rootElement,"mouseover",function(t){return e.onMouseOver(t)}),this.eventManager.addEventListener(this.hot.rootElement,"mousedown",function(t){return e.onMouseDown(t)}),this.eventManager.addEventListener(window,"mousemove",function(t){return e.onMouseMove(t)}),this.eventManager.addEventListener(window,"mouseup",function(t){return e.onMouseUp(t)})},setManualSize:function(e,t){return t=Math.max(t,20),e=this.hot.runHooks("modifyCol",e),this.manualColumnWidths[e]=t,t},onModifyColWidth:function(e,t){return this.enabled&&(t=this.hot.runHooks("modifyCol",t),this.hot.getSettings().manualColumnResize&&this.manualColumnWidths[t])?this.manualColumnWidths[t]:e}},{},l),m("manualColumnResize",v)},{"_base.js":58,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,plugins:57}],79:[function(e,t,n){"use strict";function o(){function e(e){var t=this;g=e;var n=this.view.wt.wtTable.getCoords(e).row;if(n>=0){f=n;var o=g.getBoundingClientRect();l=o.top,m.style.top=l+"px",m.style.left=o.left+"px",t.rootElement.appendChild(m)}}function t(e,t){var n=e.getBoundingClientRect(),o=6;t>0?m.style.top=n.top+n.height-o+"px":m.style.top=n.top+"px"}function n(){var e=this;u(m,"active"),u(v,"active");var t=g.getBoundingClientRect();v.style.width=e.view.maximumVisibleElementWidth(0)+"px",v.style.height=t.height+"px",v.style.top=l+"px",v.style.left=m.style.left,e.rootElement.appendChild(v)}function o(e){v.style.top=l+e+"px"}function r(){d(m,"active"),d(v,"active")}var i,s,a,l,f,g,m=document.createElement("DIV"),v=document.createElement("DIV"),w=h(this);m.className="manualRowMover",v.className="manualRowMoverGuide";var y=function(){var e=this;Handsontable.hooks.run(e,"persistentStateSave","manualRowPositions",e.manualRowPositions)},b=function(){var e=this,t={};return Handsontable.hooks.run(e,"persistentStateLoad","manualRowPositions",t),t.value},C=function(e){return"BODY"!=e.tagName?"TBODY"==e.parentNode.tagName?!0:(e=e.parentNode,C(e)):!1},R=function(e){return"TABLE"!=e.tagName?"TH"==e.tagName?e:R(e.parentNode):null},_=function(){var l,u=this;w.addEventListener(u.rootElement,"mouseover",function(n){if(C(n.target)){var o=R(n.target);o&&(l?(s=u.view.wt.wtTable.getCoords(o).row,t(o,s-i)):e.call(u,o))}}),w.addEventListener(u.rootElement,"mousedown",function(e){c(e.target,"manualRowMover")&&(a=p(e),n.call(u),l=u,i=f,s=f)}),w.addEventListener(window,"mousemove",function(e){l&&o(p(e)-a)}),w.addEventListener(window,"mouseup",function(t){l&&(r(),l=!1,E(u.manualRowPositions,u.countRows()),u.manualRowPositions.splice(s,0,u.manualRowPositions.splice(i,1)[0]),Handsontable.hooks.run(u,"beforeRowMove",i,s),u.forceFullRender=!0,u.view.render(),y.call(u),Handsontable.hooks.run(u,"afterRowMove",i,s),e.call(u,g))}),u.addHook("afterDestroy",S)},S=function(){w.clear()},E=function(e,t){if(e.length<t)for(var n=e.length;t>n;n++)e[n]=n};this.beforeInit=function(){this.manualRowPositions=[]},this.init=function(e){var t=this,n=!!t.getSettings().manualRowMove;if(n){var o=t.getSettings().manualRowMove,r=b.call(t);"undefined"==typeof t.manualRowPositionsPluginUsages?t.manualRowPositionsPluginUsages=["manualColumnMove"]:t.manualRowPositionsPluginUsages.push("manualColumnMove"),"undefined"!=typeof r?this.manualRowPositions=r:Array.isArray(o)?this.manualRowPositions=o:this.manualRowPositions=[],("afterInit"===e||"afterUpdateSettings"===e&&0===w.context.eventListeners.length)&&(_.call(this),this.manualRowPositions.length>0&&(t.forceFullRender=!0,t.render()))}else{var i=t.manualRowPositionsPluginUsages?t.manualRowPositionsPluginUsages.indexOf("manualColumnMove"):-1;i>-1&&(S.call(this),t.manualRowPositions=[],t.manualRowPositionsPluginUsages[i]=void 0)}},this.modifyRow=function(e){var t=this;return t.getSettings().manualRowMove?("undefined"==typeof t.manualRowPositions[e]&&E(this.manualRowPositions,e+1),t.manualRowPositions[e]):e}}Object.defineProperties(n,{ManualRowMove:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),u=l.addClass,c=l.hasClass,d=l.removeClass,h=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,f=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),p=(f.pageX,f.pageY),g=((a=e("plugins"),a&&a.__esModule&&a||{"default":a}).registerPlugin,new o);Handsontable.hooks.add("beforeInit",g.beforeInit),Handsontable.hooks.add("afterInit",function(){g.init.call(this,"afterInit")}),Handsontable.hooks.add("afterUpdateSettings",function(){g.init.call(this,"afterUpdateSettings")}),Handsontable.hooks.add("modifyRow",g.modifyRow),Handsontable.hooks.register("beforeRowMove"),Handsontable.hooks.register("afterRowMove")},{eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,plugins:57}],80:[function(e,t,n){"use strict";Object.defineProperties(n,{ManualRowResize:{get:function(){return v}},__esModule:{value:!0}});var o,r,i,s,a,l=(o=e("_base.js"),o&&o.__esModule&&o||{"default":o})["default"],u=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),c=u.addClass,d=u.hasClass,h=u.removeClass,f=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,p=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),g=(p.pageX,p.pageY),m=(a=e("plugins"),a&&a.__esModule&&a||{"default":a}).registerPlugin,v=function(e){$traceurRuntime.superConstructor(w).call(this,e),this.currentTH=null,this.currentRow=null,this.currentHeight=null,this.newSize=null,this.startY=null,this.startHeight=null,this.startOffset=null,this.handle=document.createElement("DIV"),this.guide=document.createElement("DIV"),this.eventManager=f(this),this.pressed=null,this.dblclick=0,this.autoresizeTimeout=null,this.manualRowHeights=[],c(this.handle,"manualRowResizer"),c(this.guide,"manualRowResizerGuide")},w=v;$traceurRuntime.createClass(v,{isEnabled:function(){return this.hot.getSettings().manualRowResize},enablePlugin:function(){var e=this;if(!this.enabled){this.manualRowHeights=[];var t=this.hot.getSettings().manualRowResize,n=this.loadManualRowHeights();"undefined"!=typeof n?this.manualRowHeights=n:Array.isArray(t)?this.manualRowHeights=t:this.manualRowHeights=[],this.addHook("modifyRowHeight",function(t,n){return e.onModifyRowHeight(t,n)}),Handsontable.hooks.register("beforeRowResize"),Handsontable.hooks.register("afterRowResize"),this.bindEvents(),$traceurRuntime.superGet(this,w.prototype,"enablePlugin").call(this)}},updatePlugin:function(){var e=this.hot.getSettings().manualRowResize;Array.isArray(e)?this.manualRowHeights=e:this.manualRowHeights=[]},disablePlugin:function(){$traceurRuntime.superGet(this,w.prototype,"disablePlugin").call(this)},saveManualRowHeights:function(){this.hot.runHooks("persistentStateSave","manualRowHeights",this.manualRowHeights)},loadManualRowHeights:function(){var e={};return this.hot.runHooks("persistentStateLoad","manualRowHeights",e),e.value},setupHandlePosition:function(e){this.currentTH=e;var t=this.hot.view.wt.wtTable.getCoords(e).row;if(t>=0){var n=this.currentTH.getBoundingClientRect();this.currentRow=t,this.startOffset=n.top-6,this.startHeight=parseInt(n.height,10),this.handle.style.left=n.left+"px",this.handle.style.top=this.startOffset+this.startHeight+"px",this.hot.rootElement.appendChild(this.handle)}},refreshHandlePosition:function(){this.handle.style.top=this.startOffset+this.currentHeight+"px"},setupGuidePosition:function(){c(this.handle,"active"),c(this.guide,"active"),this.guide.style.top=this.handle.style.top,this.guide.style.left=this.handle.style.left,this.guide.style.width=this.hot.view.maximumVisibleElementWidth(0)+"px",this.hot.rootElement.appendChild(this.guide)},refreshGuidePosition:function(){this.guide.style.top=this.handle.style.top},hideHandleAndGuide:function(){h(this.handle,"active"),h(this.guide,"active")},checkIfRowHeader:function(e){return"BODY"!=e.tagName?"TBODY"==e.parentNode.tagName?!0:(e=e.parentNode,this.checkIfRowHeader(e)):!1},getTHFromTargetElement:function(e){return"TABLE"!=e.tagName?"TH"==e.tagName?e:this.getTHFromTargetElement(e.parentNode):null},onMouseOver:function(e){if(this.checkIfRowHeader(e.target)){var t=this.getTHFromTargetElement(e.target);t&&(this.pressed||this.setupHandlePosition(t))}},afterMouseDownTimeout:function(){if(this.dblclick>=2){var e=this.hot.runHooks("beforeRowResize",this.currentRow,this.newSize,!0);void 0!==e&&(this.newSize=e),this.setManualSize(this.currentRow,this.newSize),this.hot.forceFullRender=!0,this.hot.view.render(),this.hot.view.wt.wtOverlays.adjustElementsSize(!0),this.hot.runHooks("afterRowResize",this.currentRow,this.newSize,!0)}this.dblclick=0,this.autoresizeTimeout=null},onMouseDown:function(e){var t=this;d(e.target,"manualRowResizer")&&(this.setupGuidePosition(),this.pressed=this.hot,null==this.autoresizeTimeout&&(this.autoresizeTimeout=setTimeout(function(){return t.afterMouseDownTimeout()},500),this.hot._registerTimeout(this.autoresizeTimeout)),this.dblclick++,this.startY=g(e),this.newSize=this.startHeight)},onMouseMove:function(e){this.pressed&&(this.currentHeight=this.startHeight+(g(e)-this.startY),this.newSize=this.setManualSize(this.currentRow,this.currentHeight),this.refreshHandlePosition(),this.refreshGuidePosition())},onMouseUp:function(e){this.pressed&&(this.hideHandleAndGuide(),this.pressed=!1,this.newSize!=this.startHeight&&(this.hot.runHooks("beforeRowResize",this.currentRow,this.newSize),this.hot.forceFullRender=!0,this.hot.view.render(),this.hot.view.wt.wtOverlays.adjustElementsSize(!0),this.saveManualRowHeights(),this.hot.runHooks("afterRowResize",this.currentRow,this.newSize)),this.setupHandlePosition(this.currentTH))},bindEvents:function(){var e=this;this.eventManager.addEventListener(this.hot.rootElement,"mouseover",function(t){return e.onMouseOver(t)}),this.eventManager.addEventListener(this.hot.rootElement,"mousedown",function(t){return e.onMouseDown(t)}),this.eventManager.addEventListener(window,"mousemove",function(t){return e.onMouseMove(t)}),this.eventManager.addEventListener(window,"mouseup",function(t){return e.onMouseUp(t)})},setManualSize:function(e,t){return e=this.hot.runHooks("modifyRow",e),this.manualRowHeights[e]=t,t},onModifyRowHeight:function(e,t){if(this.enabled){var n=this.hot.getPlugin("autoRowSize"),o=n?n.heights[t]:null;t=this.hot.runHooks("modifyRow",t);var r=this.manualRowHeights[t];if(void 0!==r&&(r===o||r>(e||0)))return r}return e}},{},l),m("manualRowResize",v)},{"_base.js":58,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,plugins:57}],81:[function(e,t,n){"use strict";function o(){var e=[];return e.getInfo=function(e,t){for(var n=0,o=this.length;o>n;n++)if(this[n].row<=e&&this[n].row+this[n].rowspan-1>=e&&this[n].col<=t&&this[n].col+this[n].colspan-1>=t)return this[n]},e.setInfo=function(e){for(var t=0,n=this.length;n>t;t++)if(this[t].row===e.row&&this[t].col===e.col)return void(this[t]=e);this.push(e)},e.removeInfo=function(e,t){for(var n=0,o=this.length;o>n;n++)if(this[n].row===e&&this[n].col===t){this.splice(n,1);break}},e}function r(e){if(this.mergedCellInfoCollection=new o,Array.isArray(e))for(var t=0,n=e.length;n>t;t++)this.mergedCellInfoCollection.setInfo(e[t])}function i(e,t){var n=this.getSettings().mergeCells;if(n&&!this.selection.isMultiple()){var o=this.mergeCells.mergedCellInfoCollection.getInfo(e[0],e[1]);o&&(e[0]=o.row,e[1]=o.col,e[2]=o.row+o.rowspan-1,e[3]=o.col+o.colspan-1)}}function s(e,t){this.mergeCells&&this.mergeCells.shiftCollection("right",e,t)}function a(e,t){this.mergeCells&&this.mergeCells.shiftCollection("left",e,t)}function l(e,t){this.mergeCells&&this.mergeCells.shiftCollection("down",e,t)}function u(e,t){this.mergeCells&&this.mergeCells.shiftCollection("up",e,t)}Object.defineProperties(n,{MergeCells:{get:function(){return r}},__esModule:{value:!0}});var c,d,h,f,p,g=((c=e("plugins"),c&&c.__esModule&&c||{"default":c}).registerPlugin,(d=e("helpers/dom/event"),d&&d.__esModule&&d||{"default":d}).stopImmediatePropagation),m=(h=e("3rdparty/walkontable/src/cell/coords"),h&&h.__esModule&&h||{"default":h}).WalkontableCellCoords,v=(f=e("3rdparty/walkontable/src/cell/range"),f&&f.__esModule&&f||{"default":f}).WalkontableCellRange,w=(p=e("3rdparty/walkontable/src/table"),p&&p.__esModule&&p||{"default":p}).WalkontableTable;r.prototype.canMergeRange=function(e){return!e.isSingle()},r.prototype.mergeRange=function(e){if(this.canMergeRange(e)){var t=e.getTopLeftCorner(),n=e.getBottomRightCorner(),o={};o.row=t.row,o.col=t.col,o.rowspan=n.row-t.row+1,o.colspan=n.col-t.col+1,this.mergedCellInfoCollection.setInfo(o)}},r.prototype.mergeOrUnmergeSelection=function(e){var t=this.mergedCellInfoCollection.getInfo(e.from.row,e.from.col);t?this.unmergeSelection(e.from):this.mergeSelection(e)},r.prototype.mergeSelection=function(e){this.mergeRange(e)},r.prototype.unmergeSelection=function(e){var t=this.mergedCellInfoCollection.getInfo(e.row,e.col);this.mergedCellInfoCollection.removeInfo(t.row,t.col)},r.prototype.applySpanProperties=function(e,t,n){var o=this.mergedCellInfoCollection.getInfo(t,n);o?o.row===t&&o.col===n?(e.setAttribute("rowspan",o.rowspan),e.setAttribute("colspan",o.colspan)):(e.removeAttribute("rowspan"),e.removeAttribute("colspan"),e.style.display="none"):(e.removeAttribute("rowspan"),e.removeAttribute("colspan"))},r.prototype.modifyTransform=function(e,t,n){var o=function(e,t){return t.row>=e.row&&t.row<=e.row+e.rowspan-1?!0:!1},r=function(e,t){return t.col>=e.col&&t.col<=e.col+e.colspan-1?!0:!1},i=function(e){return new m(t.to.row+e.row,t.to.col+e.col)},s={row:n.row,col:n.col};if("modifyTransformStart"==e){this.lastDesiredCoords||(this.lastDesiredCoords=new m(null,null));for(var a,l=new m(t.highlight.row,t.highlight.col),u=this.mergedCellInfoCollection.getInfo(l.row,l.col),c=0,d=this.mergedCellInfoCollection.length;d>c;c++){var h=this.mergedCellInfoCollection[c];if(h=new m(h.row+h.rowspan-1,h.col+h.colspan-1),t.includes(h)){a=!0;break}}if(u){var f=new m(u.row,u.col),p=new m(u.row+u.rowspan-1,u.col+u.colspan-1),g=new v(f,f,p);g.includes(this.lastDesiredCoords)||(this.lastDesiredCoords=new m(null,null)),s.row=this.lastDesiredCoords.row?this.lastDesiredCoords.row-l.row:s.row,s.col=this.lastDesiredCoords.col?this.lastDesiredCoords.col-l.col:s.col,n.row>0?s.row=u.row+u.rowspan-1-l.row+n.row:n.row<0&&(s.row=l.row-u.row+n.row),n.col>0?s.col=u.col+u.colspan-1-l.col+n.col:n.col<0&&(s.col=l.col-u.col+n.col)}var w=new m(t.highlight.row+s.row,t.highlight.col+s.col),y=this.mergedCellInfoCollection.getInfo(w.row,w.col);y&&(this.lastDesiredCoords=w,s={row:y.row-l.row,col:y.col-l.col})}else if("modifyTransformEnd"==e)for(var c=0,d=this.mergedCellInfoCollection.length;d>c;c++){var b=this.mergedCellInfoCollection[c],f=new m(b.row,b.col),p=new m(b.row+b.rowspan-1,b.col+b.colspan-1),C=new v(f,f,p),R=t.getBordersSharedWith(C);if(C.isEqual(t))t.setDirection("NW-SE");else if(R.length>0){var _=t.highlight.isEqual(C.from);R.indexOf("top")>-1?t.to.isSouthEastOf(C.from)&&_?t.setDirection("NW-SE"):t.to.isSouthWestOf(C.from)&&_&&t.setDirection("NE-SW"):R.indexOf("bottom")>-1&&(t.to.isNorthEastOf(C.from)&&_?t.setDirection("SW-NE"):t.to.isNorthWestOf(C.from)&&_&&t.setDirection("SE-NW"))}var w=i(s),S=o(b,w),E=r(b,w);t.includesRange(C)&&(C.includes(w)||S||E)&&(S&&(s.row<0?s.row-=b.rowspan-1:s.row>0&&(s.row+=b.rowspan-1)),E&&(s.col<0?s.col-=b.colspan-1:s.col>0&&(s.col+=b.colspan-1)))}0!==s.row&&(n.row=s.row),0!==s.col&&(n.col=s.col)},r.prototype.shiftCollection=function(e,t,n){var o=[0,0];switch(e){case"right":o[0]+=1;break;case"left":o[0]-=1;break;case"down":o[1]+=1;break;case"up":o[1]-=1}for(var r=0;r<this.mergedCellInfoCollection.length;r++){var i=this.mergedCellInfoCollection[r];"right"===e||"left"===e?t<=i.col&&(i.col+=o[0]):t<=i.row&&(i.row+=o[1])}};var y=function(){var e=this,t=e.getSettings().mergeCells;t&&(e.mergeCells||(e.mergeCells=new r(t)))},b=function(){var e=this;e.mergeCells&&(e.view.wt.wtTable.getCell=function(t){if(e.getSettings().mergeCells){var n=e.mergeCells.mergedCellInfoCollection.getInfo(t.row,t.col);n&&(t=n)}return w.prototype.getCell.call(this,t)})},C=function(){var e=this,t=e.getSettings().mergeCells;if(t)if(e.mergeCells){if(e.mergeCells.mergedCellInfoCollection=new o,Array.isArray(t))for(var n=0,i=t.length;i>n;n++)e.mergeCells.mergedCellInfoCollection.setInfo(t[n])}else e.mergeCells=new r(t);else e.mergeCells&&(e.mergeCells.mergedCellInfoCollection=new o)},R=function(e){if(this.mergeCells){var t=(e.ctrlKey||e.metaKey)&&!e.altKey;t&&77===e.keyCode&&(this.mergeCells.mergeOrUnmergeSelection(this.getSelectedRange()),this.render(),
g(e))}},_=function(e){this.getSettings().mergeCells&&(e.items.push(Handsontable.plugins.ContextMenu.SEPARATOR),e.items.push({key:"mergeCells",name:function(){var e=this.getSelected(),t=this.mergeCells.mergedCellInfoCollection.getInfo(e[0],e[1]);return t?"Unmerge cells":"Merge cells"},callback:function(){this.mergeCells.mergeOrUnmergeSelection(this.getSelectedRange()),this.render()},disabled:function(){return!1}}))},S=function(e,t,n,o,r,i){this.mergeCells&&this.mergeCells.applySpanProperties(e,t,n)},E=function(e){return function(t){var n=this.getSettings().mergeCells;if(n){var o=this.getSelectedRange();if(this.mergeCells.modifyTransform(e,o,t),"modifyTransformEnd"===e){var r=this.countRows(),i=this.countCols();o.from.row<0?o.from.row=0:o.from.row>0&&o.from.row>=r&&(o.from.row=o.from-1),o.from.col<0?o.from.col=0:o.from.col>0&&o.from.col>=i&&(o.from.col=i-1)}}}},T=function(e){this.lastDesiredCoords=null;var t=this.getSettings().mergeCells;if(t){var n=this.getSelectedRange();n.highlight=new m(n.highlight.row,n.highlight.col),n.to=e;var o=!1;do{o=!1;for(var r=0,i=this.mergeCells.mergedCellInfoCollection.length;i>r;r++){var s=this.mergeCells.mergedCellInfoCollection[r],a=new m(s.row,s.col),l=new m(s.row+s.rowspan-1,s.col+s.colspan-1),u=new v(a,a,l);n.expandByRange(u)&&(e.row=n.to.row,e.col=n.to.col,o=!0)}}while(o)}},O=function(e,t){if(t&&"area"==t){var n=this.getSettings().mergeCells;if(n)for(var o=this.getSelectedRange(),r=new v(o.from,o.from,o.from),i=new v(o.to,o.to,o.to),s=0,a=this.mergeCells.mergedCellInfoCollection.length;a>s;s++){var l=this.mergeCells.mergedCellInfoCollection[s],u=new m(l.row,l.col),c=new m(l.row+l.rowspan-1,l.col+l.colspan-1),d=new v(u,u,c);r.expandByRange(d)&&(e[0]=r.from.row,e[1]=r.from.col),i.expandByRange(d)&&(e[2]=i.from.row,e[3]=i.from.col)}}},M=function(e,t,n){var o=this.getSettings().mergeCells;if(o){var r=this.mergeCells.mergedCellInfoCollection.getInfo(e,t);!r||r.row==e&&r.col==t||(n.copyable=!1)}},k=function(e){var t=this.getSettings().mergeCells;if(t)for(var n,o=this.countCols(),r=0;o>r;r++){if(n=this.mergeCells.mergedCellInfoCollection.getInfo(e.startRow,r),n&&n.row<e.startRow)return e.startRow=n.row,k.call(this,e);if(n=this.mergeCells.mergedCellInfoCollection.getInfo(e.endRow,r)){var i=n.row+n.rowspan-1;if(i>e.endRow)return e.endRow=i,k.call(this,e)}}},H=function(e){var t=this.getSettings().mergeCells;if(t)for(var n,o=this.countRows(),r=0;o>r;r++){if(n=this.mergeCells.mergedCellInfoCollection.getInfo(r,e.startColumn),n&&n.col<e.startColumn)return e.startColumn=n.col,H.call(this,e);if(n=this.mergeCells.mergedCellInfoCollection.getInfo(r,e.endColumn)){var i=n.col+n.colspan-1;if(i>e.endColumn)return e.endColumn=i,H.call(this,e)}}},D=function(e){if(e&&this.mergeCells){var t=this.mergeCells.mergedCellInfoCollection,n=this.getSelectedRange();for(var o in t)if(n.highlight.row==t[o].row&&n.highlight.col==t[o].col&&n.to.row==t[o].row+t[o].rowspan-1&&n.to.col==t[o].col+t[o].colspan-1)return!1}return e};Handsontable.hooks.add("beforeInit",y),Handsontable.hooks.add("afterInit",b),Handsontable.hooks.add("afterUpdateSettings",C),Handsontable.hooks.add("beforeKeyDown",R),Handsontable.hooks.add("modifyTransformStart",E("modifyTransformStart")),Handsontable.hooks.add("modifyTransformEnd",E("modifyTransformEnd")),Handsontable.hooks.add("beforeSetRangeEnd",T),Handsontable.hooks.add("beforeDrawBorders",O),Handsontable.hooks.add("afterIsMultipleSelection",D),Handsontable.hooks.add("afterRenderer",S),Handsontable.hooks.add("afterContextMenuDefaultOptions",_),Handsontable.hooks.add("afterGetCellMeta",M),Handsontable.hooks.add("afterViewportRowCalculatorOverride",k),Handsontable.hooks.add("afterViewportColumnCalculatorOverride",H),Handsontable.hooks.add("afterAutofillApplyValues",i),Handsontable.hooks.add("afterCreateCol",s),Handsontable.hooks.add("afterRemoveCol",a),Handsontable.hooks.add("afterCreateRow",l),Handsontable.hooks.add("afterRemoveRow",u),Handsontable.MergeCells=r},{"3rdparty/walkontable/src/cell/coords":5,"3rdparty/walkontable/src/cell/range":6,"3rdparty/walkontable/src/table":20,"helpers/dom/event":46,plugins:57}],82:[function(e,t,n){"use strict";Object.defineProperties(n,{MultipleSelectionHandles:{get:function(){return p}},__esModule:{value:!0}});var o,r,i,s,a=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),l=a.getWindowScrollTop,u=a.hasClass,c=a.getWindowScrollLeft,d=(r=e("_base"),r&&r.__esModule&&r||{"default":r})["default"],h=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).EventManager,f=(s=e("plugins"),s&&s.__esModule&&s||{"default":s}).registerPlugin,p=function(e){$traceurRuntime.superConstructor(g).call(this,e),this.dragged=[],this.eventManager=null,this.lastSetCell=null},g=p;$traceurRuntime.createClass(p,{isEnabled:function(){return Handsontable.mobileBrowser},enablePlugin:function(){this.enabled||(this.eventManager||(this.eventManager=new h(this)),this.registerListeners(),$traceurRuntime.superGet(this,g.prototype,"enablePlugin").call(this))},registerListeners:function(){function e(e){if(1===t.dragged.length)return t.dragged.splice(0,t.dragged.length),!0;var n=t.dragged.indexOf(e);return-1==n?!1:void(0===n?t.dragged=t.dragged.slice(0,1):1==n&&(t.dragged=t.dragged.slice(-1)))}var t=this;this.eventManager.addEventListener(this.hot.rootElement,"touchstart",function(e){var n;return u(e.target,"topLeftSelectionHandle-HitArea")?(n=t.hot.getSelectedRange(),t.dragged.push("topLeft"),t.touchStartRange={width:n.getWidth(),height:n.getHeight(),direction:n.getDirection()},e.preventDefault(),!1):u(e.target,"bottomRightSelectionHandle-HitArea")?(n=t.hot.getSelectedRange(),t.dragged.push("bottomRight"),t.touchStartRange={width:n.getWidth(),height:n.getHeight(),direction:n.getDirection()},e.preventDefault(),!1):void 0}),this.eventManager.addEventListener(this.hot.rootElement,"touchend",function(n){return u(n.target,"topLeftSelectionHandle-HitArea")?(e.call(t,"topLeft"),t.touchStartRange=void 0,n.preventDefault(),!1):u(n.target,"bottomRightSelectionHandle-HitArea")?(e.call(t,"bottomRight"),t.touchStartRange=void 0,n.preventDefault(),!1):void 0}),this.eventManager.addEventListener(this.hot.rootElement,"touchmove",function(e){var n,o,r,i,s,a,u,d=l(),h=c();0!==t.dragged.length&&(n=document.elementFromPoint(e.touches[0].screenX-h,e.touches[0].screenY-d),n&&n!==t.lastSetCell&&(("TD"==n.nodeName||"TH"==n.nodeName)&&(o=t.hot.getCoords(n),-1==o.col&&(o.col=0),r=t.hot.getSelectedRange(),i=r.getWidth(),s=r.getHeight(),a=r.getDirection(),1==i&&1==s&&t.hot.selection.setRangeEnd(o),u=t.getCurrentRangeCoords(r,o,t.touchStartRange.direction,a,t.dragged[0]),null!==u.start&&t.hot.selection.setRangeStart(u.start),t.hot.selection.setRangeEnd(u.end),t.lastSetCell=n),e.preventDefault()))})},getCurrentRangeCoords:function(e,t,n,o,r){var i=e.getTopLeftCorner(),s=e.getBottomRightCorner(),a=e.getBottomLeftCorner(),l=e.getTopRightCorner(),u={start:null,end:null};switch(n){case"NE-SW":switch(o){case"NE-SW":case"NW-SE":u="topLeft"==r?{start:new WalkontableCellCoords(t.row,e.highlight.col),end:new WalkontableCellCoords(a.row,t.col)}:{start:new WalkontableCellCoords(e.highlight.row,t.col),end:new WalkontableCellCoords(t.row,i.col)};break;case"SE-NW":"bottomRight"==r&&(u={start:new WalkontableCellCoords(s.row,t.col),end:new WalkontableCellCoords(t.row,i.col)})}break;case"NW-SE":switch(o){case"NE-SW":"topLeft"==r?u={start:t,end:a}:u.end=t;break;case"NW-SE":"topLeft"==r?u={start:t,end:s}:u.end=t;break;case"SE-NW":"topLeft"==r?u={start:t,end:i}:u.end=t;break;case"SW-NE":"topLeft"==r?u={start:t,end:l}:u.end=t}break;case"SW-NE":switch(o){case"NW-SE":u="bottomRight"==r?{start:new WalkontableCellCoords(t.row,i.col),end:new WalkontableCellCoords(a.row,t.col)}:{start:new WalkontableCellCoords(i.row,t.col),end:new WalkontableCellCoords(t.row,s.col)};break;case"SW-NE":u="topLeft"==r?{start:new WalkontableCellCoords(e.highlight.row,t.col),end:new WalkontableCellCoords(t.row,s.col)}:{start:new WalkontableCellCoords(t.row,i.col),end:new WalkontableCellCoords(i.row,t.col)};break;case"SE-NW":"bottomRight"==r?u={start:new WalkontableCellCoords(t.row,l.col),end:new WalkontableCellCoords(i.row,t.col)}:"topLeft"==r&&(u={start:a,end:t})}break;case"SE-NW":switch(o){case"NW-SE":case"NE-SW":case"SW-NE":"topLeft"==r&&(u.end=t);break;case"SE-NW":"topLeft"==r?u.end=t:u={start:t,end:i}}}return u},isDragged:function(){return this.dragged.length>0}},{},d),f("multipleSelectionHandles",p)},{_base:58,eventManager:41,"helpers/dom/element":45,plugins:57}],83:[function(e,t,n){"use strict";function o(){}function r(){var e=this,t=e.getSettings().observeChanges;t?(e.observer&&a.call(e),i.call(e),u.call(e)):t||a.call(e)}function i(){var e=this;e.observeChangesActive=!0,e.pauseObservingChanges=function(){e.observeChangesActive=!1},e.resumeObservingChanges=function(){e.observeChangesActive=!0},e.observedData=e.getSourceData(),e.observer=p.observe(e.observedData,function(t){e.observeChangesActive&&(s.call(e,t),e.render()),e.runHooks("afterChangesObserved")})}function s(e){function t(e){var t;return t=o(e),t=n(t)}function n(e){var t=[];return e.filter(function(e){var n=r(e.path);if(-1!=["add","remove"].indexOf(e.op)&&!isNaN(n.col)){if(-1!=t.indexOf(n.col))return!1;t.push(n.col)}return!0})}function o(e){return e.filter(function(e){return!/[\/]length/gi.test(e.path)})}function r(e){var t=e.match(/^\/(\d+)\/?(.*)?$/);return{row:parseInt(t[1],10),col:/^\d*$/.test(t[2])?parseInt(t[2],10):t[2]}}for(var i=this,s=t(e),a=0,l=s.length;l>a;a++){var u=s[a],c=r(u.path);switch(u.op){case"add":isNaN(c.col)?i.runHooks("afterCreateRow",c.row):i.runHooks("afterCreateCol",c.col);break;case"remove":isNaN(c.col)?i.runHooks("afterRemoveRow",c.row,1):i.runHooks("afterRemoveCol",c.col,1);break;case"replace":i.runHooks("afterChange",[c.row,c.col,null,u.value],"external")}}}function a(){var e=this;e.observer&&(l.call(e),c.call(e))}function l(){var e=this;p.unobserve(e.observedData,e.observer),delete e.observedData,delete e.observeChangesActive,delete e.pauseObservingChanges,delete e.resumeObservingChanges}function u(){var e=this;e.addHook("afterDestroy",a),e.addHook("afterCreateRow",d),e.addHook("afterRemoveRow",d),e.addHook("afterCreateCol",d),e.addHook("afterRemoveCol",d),e.addHook("afterChange",function(e,t){"loadData"!=t&&d.call(this)})}function c(){var e=this;e.removeHook("afterDestroy",a),e.removeHook("afterCreateRow",d),e.removeHook("afterRemoveRow",d),e.removeHook("afterCreateCol",d),e.removeHook("afterRemoveCol",d),e.removeHook("afterChange",d)}function d(){var e=this;e.pauseObservingChanges(),e.addHookOnce("afterChangesObserved",function(){e.resumeObservingChanges()})}Object.defineProperties(n,{ObserveChanges:{get:function(){return o}},__esModule:{value:!0}});var h,f,p=((h=e("plugins"),h&&h.__esModule&&h||{"default":h}).registerPlugin,(f=e("jsonpatch"),f&&f.__esModule&&f||{"default":f})["default"]);Handsontable.hooks.add("afterLoadData",r),Handsontable.hooks.add("afterUpdateSettings",r),Handsontable.hooks.register("afterChangesObserved")},{jsonpatch:"jsonpatch",plugins:57}],84:[function(e,t,n){"use strict";function o(e){var t,n=function(){window.localStorage[e+"__persistentStateKeys"]=JSON.stringify(t)},o=function(){var n=window.localStorage[e+"__persistentStateKeys"],o="string"==typeof n?JSON.parse(n):void 0;t=o?o:[]},r=function(){t=[],n()};o(),this.saveValue=function(o,r){window.localStorage[e+"_"+o]=JSON.stringify(r),-1==t.indexOf(o)&&(t.push(o),n())},this.loadValue=function(t,n){t="undefined"==typeof t?n:t;var o=window.localStorage[e+"_"+t];return"undefined"==typeof o?void 0:JSON.parse(o)},this.reset=function(t){window.localStorage.removeItem(e+"_"+t)},this.resetAll=function(){for(var n=0;n<t.length;n++)window.localStorage.removeItem(e+"_"+t[n]);r()}}function r(){function e(){var e=this;for(var t in r)r.hasOwnProperty(t)&&e.addHook(t,r[t])}function t(){var e=this;for(var t in r)r.hasOwnProperty(t)&&e.removeHook(t,r[t])}var n=this;this.init=function(){var r=this,i=r.getSettings().persistentState;return n.enabled=!!i,n.enabled?(r.storage||(r.storage=new o(r.rootElement.id)),r.resetState=n.resetValue,void e.call(r)):void t.call(r)},this.saveValue=function(e,t){var n=this;n.storage.saveValue(e,t)},this.loadValue=function(e,t){var n=this;t.value=n.storage.loadValue(e)},this.resetValue=function(e){var t=this;"undefined"==typeof e?t.storage.resetAll():t.storage.reset(e)};var r={persistentStateSave:n.saveValue,persistentStateLoad:n.loadValue,persistentStateReset:n.resetValue};for(var i in r)r.hasOwnProperty(i)&&Handsontable.hooks.register(i)}Object.defineProperties(n,{HandsontablePersistentState:{get:function(){return r}},__esModule:{value:!0}});var i,s=((i=e("plugins"),i&&i.__esModule&&i||{"default":i}).registerPlugin,new r);Handsontable.hooks.add("beforeInit",s.init),Handsontable.hooks.add("afterUpdateSettings",s.init)},{plugins:57}],85:[function(e,t,n){"use strict";function o(){var e=this,t=!!e.getSettings().search;t?e.search=new Handsontable.Search(e):delete e.search}var r,i,s=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),a=s.addClass,l=s.removeClass,u=(i=e("renderers"),i&&i.__esModule&&i||{"default":i}),c=u.registerRenderer,d=u.getRenderer;Handsontable.Search=function(e){this.query=function(t,n,o){var r=e.countRows(),i=e.countCols(),s=[];n||(n=Handsontable.Search.global.getDefaultCallback()),o||(o=Handsontable.Search.global.getDefaultQueryMethod());for(var a=0;r>a;a++)for(var l=0;i>l;l++){var u=e.getDataAtCell(a,l),c=e.getCellMeta(a,l),d=c.search.callback||n,h=c.search.queryMethod||o,f=h(t,u);if(f){var p={row:a,col:l,data:u};s.push(p)}d&&d(e,a,l,u,f)}return s}},Handsontable.Search.DEFAULT_CALLBACK=function(e,t,n,o,r){e.getCellMeta(t,n).isSearchResult=r},Handsontable.Search.DEFAULT_QUERY_METHOD=function(e,t){return"undefined"!=typeof e&&null!=e&&e.toLowerCase&&0!==e.length?"undefined"==typeof t||null==t?!1:-1!=t.toString().toLowerCase().indexOf(e.toLowerCase()):!1},Handsontable.Search.DEFAULT_SEARCH_RESULT_CLASS="htSearchResult",Handsontable.Search.global=function(){var e=Handsontable.Search.DEFAULT_CALLBACK,t=Handsontable.Search.DEFAULT_QUERY_METHOD,n=Handsontable.Search.DEFAULT_SEARCH_RESULT_CLASS;return{getDefaultCallback:function(){return e},setDefaultCallback:function(t){e=t},getDefaultQueryMethod:function(){return t},setDefaultQueryMethod:function(e){t=e},getDefaultSearchResultClass:function(){return n},setDefaultSearchResultClass:function(e){n=e}}}(),Handsontable.SearchCellDecorator=function(e,t,n,o,r,i,s){var u=null!==s.search&&"object"==typeof s.search&&s.search.searchResultClass||Handsontable.Search.global.getDefaultSearchResultClass();s.isSearchResult?a(t,u):l(t,u)};var h=d("base");c("base",function(e,t,n,o,r,i,s){h.apply(this,arguments),Handsontable.SearchCellDecorator.apply(this,arguments)}),Handsontable.hooks.add("afterInit",o),Handsontable.hooks.add("afterUpdateSettings",o)},{"helpers/dom/element":45,renderers:88}],86:[function(e,t,n){"use strict";Object.defineProperties(n,{TouchScroll:{get:function(){return d}},__esModule:{value:!0}});var o,r,i,s=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),a=s.addClass,l=s.removeClass,u=(r=e("_base"),r&&r.__esModule&&r||{"default":r})["default"],c=(i=e("plugins"),i&&i.__esModule&&i||{"default":i}).registerPlugin,d=function(e){var t=this;$traceurRuntime.superConstructor(h).call(this,e),this.hot.addHook("afterInit",function(){return t.afterInit()}),this.hot.addHook("afterUpdateSettings",function(){return t.onAfterUpdateSettings()}),this.scrollbars=[],this.clones=[]},h=d;$traceurRuntime.createClass(d,{afterInit:function(){this.registerEvents(),this.onAfterUpdateSettings()},onAfterUpdateSettings:function(){var e=this;this.hot.addHookOnce("afterRender",function(){var t=e.hot.view.wt.wtOverlays;e.scrollbars=[],e.scrollbars.push(t.topOverlay),t.bottomOverlay.clone&&e.scrollbars.push(t.bottomOverlay),e.scrollbars.push(t.leftOverlay),t.topLeftCornerOverlay&&e.scrollbars.push(t.topLeftCornerOverlay),t.bottomLeftCornerOverlay&&t.bottomLeftCornerOverlay.clone&&e.scrollbars.push(t.bottomLeftCornerOverlay),e.clones=[],t.topOverlay.needFullRender&&e.clones.push(t.topOverlay.clone.wtTable.holder.parentNode),t.bottomOverlay.needFullRender&&e.clones.push(t.bottomOverlay.clone.wtTable.holder.parentNode),t.leftOverlay.needFullRender&&e.clones.push(t.leftOverlay.clone.wtTable.holder.parentNode),t.topLeftCornerOverlay&&e.clones.push(t.topLeftCornerOverlay.clone.wtTable.holder.parentNode),t.bottomLeftCornerOverlay&&t.bottomLeftCornerOverlay.clone&&e.clones.push(t.bottomLeftCornerOverlay.clone.wtTable.holder.parentNode)})},registerEvents:function(){var e=this;this.hot.addHook("beforeTouchScroll",function(){return e.onBeforeTouchScroll()}),this.hot.addHook("afterMomentumScroll",function(){return e.onAfterMomentumScroll()})},onBeforeTouchScroll:function(){Handsontable.freezeOverlays=!0;for(var e=0,t=this.clones.length;t>e;e++)a(this.clones[e],"hide-tween")},onAfterMomentumScroll:function(){Handsontable.freezeOverlays=!1;for(var e=this,t=0,n=this.clones.length;n>t;t++)l(this.clones[t],"hide-tween");for(var o=0,r=this.clones.length;r>o;o++)a(this.clones[o],"show-tween");setTimeout(function(){for(var t=0,n=e.clones.length;n>t;t++)l(e.clones[t],"show-tween")},400);for(var i=0,s=this.scrollbars.length;s>i;i++)this.scrollbars[i].refresh(),this.scrollbars[i].resetFixedPosition();this.hot.view.wt.wtOverlays.syncScrollWithMaster()}},{},u),c("touchScroll",d)},{_base:58,"helpers/dom/element":45,plugins:57}],87:[function(e,t,n){"use strict";function o(){var e=this,t="undefined"==typeof e.getSettings().undo||e.getSettings().undo;t?e.undoRedo||(e.undoRedo=new Handsontable.UndoRedo(e),s(e),e.addHook("beforeKeyDown",r),e.addHook("afterChange",i)):e.undoRedo&&(delete e.undoRedo,a(e),e.removeHook("beforeKeyDown",r),e.removeHook("afterChange",i))}function r(e){var t=this,n=(e.ctrlKey||e.metaKey)&&!e.altKey;n&&(89===e.keyCode||e.shiftKey&&90===e.keyCode?(t.undoRedo.redo(),f(e)):90===e.keyCode&&(t.undoRedo.undo(),f(e)))}function i(e,t){var n=this;return"loadData"==t?n.undoRedo.clear():void 0}function s(e){e.undo=function(){return e.undoRedo.undo()},e.redo=function(){return e.undoRedo.redo()},e.isUndoAvailable=function(){return e.undoRedo.isUndoAvailable()},e.isRedoAvailable=function(){return e.undoRedo.isRedoAvailable()},e.clearUndo=function(){return e.undoRedo.clear()}}function a(e){delete e.undo,delete e.redo,delete e.isUndoAvailable,delete e.isRedoAvailable,delete e.clearUndo}var l,u,c=(l=e("helpers/object"),l&&l.__esModule&&l||{"default":l}),d=c.inherit,h=c.deepClone,f=(u=e("helpers/dom/event"),u&&u.__esModule&&u||{"default":u}).stopImmediatePropagation;Handsontable.UndoRedo=function(e){var t=this;this.instance=e,this.doneActions=[],this.undoneActions=[],this.ignoreNewActions=!1,e.addHook("afterChange",function(e,n){if(e){var o=new Handsontable.UndoRedo.ChangeAction(e);t.done(o)}}),e.addHook("afterCreateRow",function(e,n,o){if(!o){var r=new Handsontable.UndoRedo.CreateRowAction(e,n);t.done(r)}}),e.addHook("beforeRemoveRow",function(e,n){var o=t.instance.getSourceData();e=(o.length+e)%o.length;var r=o.slice(e,e+n),i=new Handsontable.UndoRedo.RemoveRowAction(e,r);t.done(i)}),e.addHook("afterCreateCol",function(e,n,o){if(!o){var r=new Handsontable.UndoRedo.CreateColumnAction(e,n);t.done(r)}}),e.addHook("beforeRemoveCol",function(n,o){var r=t.instance.getSourceData();n=(t.instance.countCols()+n)%t.instance.countCols();for(var i=[],s=0,a=r.length;a>s;s++)i[s]=r[s].slice(n,n+o);var l;Array.isArray(e.getSettings().colHeaders)&&(l=e.getSettings().colHeaders.slice(n,n+i.length));var u=new Handsontable.UndoRedo.RemoveColumnAction(n,i,l);t.done(u)}),e.addHook("beforeCellAlignment",function(e,n,o,r){var i=new Handsontable.UndoRedo.CellAlignmentAction(e,n,o,r);t.done(i)})},Handsontable.UndoRedo.prototype.done=function(e){this.ignoreNewActions||(this.doneActions.push(e),this.undoneActions.length=0)},Handsontable.UndoRedo.prototype.undo=function(){if(this.isUndoAvailable()){var e=this.doneActions.pop();this.ignoreNewActions=!0;var t=this;e.undo(this.instance,function(){t.ignoreNewActions=!1,t.undoneActions.push(e)})}},Handsontable.UndoRedo.prototype.redo=function(){if(this.isRedoAvailable()){var e=this.undoneActions.pop();this.ignoreNewActions=!0;var t=this;e.redo(this.instance,function(){t.ignoreNewActions=!1,t.doneActions.push(e)})}},Handsontable.UndoRedo.prototype.isUndoAvailable=function(){return this.doneActions.length>0},Handsontable.UndoRedo.prototype.isRedoAvailable=function(){return this.undoneActions.length>0},Handsontable.UndoRedo.prototype.clear=function(){this.doneActions.length=0,this.undoneActions.length=0},Handsontable.UndoRedo.Action=function(){},Handsontable.UndoRedo.Action.prototype.undo=function(){},Handsontable.UndoRedo.Action.prototype.redo=function(){},Handsontable.UndoRedo.ChangeAction=function(e){this.changes=e},d(Handsontable.UndoRedo.ChangeAction,Handsontable.UndoRedo.Action),Handsontable.UndoRedo.ChangeAction.prototype.undo=function(e,t){for(var n=h(this.changes),o=e.countEmptyRows(!0),r=e.countEmptyCols(!0),i=0,s=n.length;s>i;i++)n[i].splice(3,1);e.addHookOnce("afterChange",t),e.setDataAtRowProp(n,null,null,"undo");for(var i=0,s=n.length;s>i;i++)e.getSettings().minSpareRows&&n[i][0]+1+e.getSettings().minSpareRows===e.countRows()&&o==e.getSettings().minSpareRows&&(e.alter("remove_row",parseInt(n[i][0]+1,10),e.getSettings().minSpareRows),e.undoRedo.doneActions.pop()),e.getSettings().minSpareCols&&n[i][1]+1+e.getSettings().minSpareCols===e.countCols()&&r==e.getSettings().minSpareCols&&(e.alter("remove_col",parseInt(n[i][1]+1,10),e.getSettings().minSpareCols),e.undoRedo.doneActions.pop())},Handsontable.UndoRedo.ChangeAction.prototype.redo=function(e,t){for(var n=h(this.changes),o=0,r=n.length;r>o;o++)n[o].splice(2,1);e.addHookOnce("afterChange",t),e.setDataAtRowProp(n,null,null,"redo")},Handsontable.UndoRedo.CreateRowAction=function(e,t){this.index=e,this.amount=t},d(Handsontable.UndoRedo.CreateRowAction,Handsontable.UndoRedo.Action),Handsontable.UndoRedo.CreateRowAction.prototype.undo=function(e,t){var n=e.countRows(),o=e.getSettings().minSpareRows;this.index>=n&&this.index-o<n&&(this.index-=o),e.addHookOnce("afterRemoveRow",t),e.alter("remove_row",this.index,this.amount)},Handsontable.UndoRedo.CreateRowAction.prototype.redo=function(e,t){e.addHookOnce("afterCreateRow",t),e.alter("insert_row",this.index+1,this.amount)},Handsontable.UndoRedo.RemoveRowAction=function(e,t){this.index=e,this.data=t},d(Handsontable.UndoRedo.RemoveRowAction,Handsontable.UndoRedo.Action),Handsontable.UndoRedo.RemoveRowAction.prototype.undo=function(e,t){var n=[this.index,0];Array.prototype.push.apply(n,this.data),Array.prototype.splice.apply(e.getSourceData(),n),e.addHookOnce("afterRender",t),e.render()},Handsontable.UndoRedo.RemoveRowAction.prototype.redo=function(e,t){e.addHookOnce("afterRemoveRow",t),e.alter("remove_row",this.index,this.data.length)},Handsontable.UndoRedo.CreateColumnAction=function(e,t){this.index=e,this.amount=t},d(Handsontable.UndoRedo.CreateColumnAction,Handsontable.UndoRedo.Action),Handsontable.UndoRedo.CreateColumnAction.prototype.undo=function(e,t){e.addHookOnce("afterRemoveCol",t),e.alter("remove_col",this.index,this.amount)},Handsontable.UndoRedo.CreateColumnAction.prototype.redo=function(e,t){e.addHookOnce("afterCreateCol",t),e.alter("insert_col",this.index+1,this.amount)},Handsontable.UndoRedo.CellAlignmentAction=function(e,t,n,o){this.stateBefore=e,this.range=t,this.type=n,this.alignment=o},Handsontable.UndoRedo.CellAlignmentAction.prototype.undo=function(e,t){if(e.getPlugin("contextMenu").isEnabled()){for(var n=this.range.from.row;n<=this.range.to.row;n++)for(var o=this.range.from.col;o<=this.range.to.col;o++)e.setCellMeta(n,o,"className",this.stateBefore[n][o]||" htLeft");e.addHookOnce("afterRender",t),e.render()}},Handsontable.UndoRedo.CellAlignmentAction.prototype.redo=function(e,t){e.getPlugin("contextMenu").isEnabled()&&(e.selectCell(this.range.from.row,this.range.from.col,this.range.to.row,this.range.to.col),e.getPlugin("contextMenu").executeCommand("alignment:"+this.alignment.replace("ht","").toLowerCase()),e.addHookOnce("afterRender",t),e.render())},Handsontable.UndoRedo.RemoveColumnAction=function(e,t,n){this.index=e,this.data=t,this.amount=this.data[0].length,this.headers=n},d(Handsontable.UndoRedo.RemoveColumnAction,Handsontable.UndoRedo.Action),Handsontable.UndoRedo.RemoveColumnAction.prototype.undo=function(e,t){for(var n,o,r=0,i=e.getSourceData().length;i>r;r++)n=e.getSourceDataAtRow(r),o=[this.index,0],Array.prototype.push.apply(o,this.data[r]),Array.prototype.splice.apply(n,o);"undefined"!=typeof this.headers&&(o=[this.index,0],Array.prototype.push.apply(o,this.headers),Array.prototype.splice.apply(e.getSettings().colHeaders,o)),e.addHookOnce("afterRender",t),e.render()},Handsontable.UndoRedo.RemoveColumnAction.prototype.redo=function(e,t){e.addHookOnce("afterRemoveCol",t),e.alter("remove_col",this.index,this.amount)},Handsontable.hooks.add("afterInit",o),Handsontable.hooks.add("afterUpdateSettings",o)},{"helpers/dom/event":46,"helpers/object":50}],88:[function(e,t,n){"use strict";function o(e,t){var n;l[e]=t,n=a(e)+"Renderer",Handsontable.renderers[n]=t,Handsontable[n]=t}function r(e){if("function"==typeof e)return e;if("string"!=typeof e)throw Error('Only strings and functions can be passed as "renderer" parameter');if(!(e in l))throw Error('No editor registered under name "'+e+'"');return l[e]}function i(e){return e in l}Object.defineProperties(n,{registerRenderer:{get:function(){return o}},getRenderer:{get:function(){return r}},hasRenderer:{get:function(){return i}},__esModule:{value:!0}});var s,a=(s=e("helpers/string"),s&&s.__esModule&&s||{"default":s}).toUpperCaseFirst,l={};Handsontable.renderers=Handsontable.renderers||{},Handsontable.renderers.registerRenderer=o,Handsontable.renderers.getRenderer=r},{"helpers/string":52}],89:[function(e,t,n){"use strict";function o(e,t,n,o,r,i,s){s.className&&(t.className?t.className=t.className+" "+s.className:t.className=s.className),s.readOnly&&a(t,s.readOnlyCellClassName),s.valid===!1&&s.invalidCellClassName?a(t,s.invalidCellClassName):l(t,s.invalidCellClassName),s.wordWrap===!1&&s.noWordWrapClassName&&a(t,s.noWordWrapClassName),!i&&s.placeholder&&a(t,s.placeholderCellClassName)}Object.defineProperties(n,{cellDecorator:{get:function(){return o}},__esModule:{value:!0}});var r,i,s=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),a=s.addClass,l=s.removeClass,u=(i=e("renderers"),i&&i.__esModule&&i||{"default":i}).registerRenderer;u("base",o),Handsontable.renderers.cellDecorator=o},{"helpers/dom/element":45,renderers:88}],90:[function(e,t,n){"use strict";function o(e,t,n,o,r,i,s){var a=(m.cloneNode(!0),v.cloneNode(!0));if(f("text")(e,t,n,o,r,i,s),t.appendChild(a),u(t,"htAutocomplete"),t.firstChild||t.appendChild(document.createTextNode(String.fromCharCode(160))),!e.acArrowListener){var l=d(e);e.acArrowListener=function(r){c(r.target,"htAutocompleteArrow")&&e.view.wt.getSetting("onCellDblClick",null,new g(n,o),t)},l.addEventListener(e.rootElement,"mousedown",e.acArrowListener),e.addHookOnce("afterDestroy",function(){l.destroy()})}}Object.defineProperties(n,{autocompleteRenderer:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),u=l.addClass,c=l.hasClass,d=(l.empty,(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager),h=(s=e("renderers"),s&&s.__esModule&&s||{"default":s}),f=h.getRenderer,p=h.registerRenderer,g=(a=e("3rdparty/walkontable/src/cell/coords"),a&&a.__esModule&&a||{"default":a}).WalkontableCellCoords,m=document.createElement("DIV");m.className="htAutocompleteWrapper";var v=document.createElement("DIV");v.className="htAutocompleteArrow",v.appendChild(document.createTextNode(String.fromCharCode(9660)));p("autocomplete",o)},{"3rdparty/walkontable/src/cell/coords":5,eventManager:41,"helpers/dom/element":45,renderers:88}],91:[function(e,t,n){"use strict";function o(e,t,n,o,a,l,u){function c(e){var t=[C.SPACE,C.ENTER,C.DELETE,C.BACKSPACE];-1===t.indexOf(e.keyCode)||E(e)||f(function(){S(e),e.preventDefault()}),(e.keyCode==C.SPACE||e.keyCode==C.ENTER)&&d(),(e.keyCode==C.DELETE||e.keyCode==C.BACKSPACE)&&d(!1)}function d(){var e=void 0!==arguments[0]?arguments[0]:null;f(function(t){for(var n=0,o=t.length;o>n;n++){if(m(t[n],O)&&null===e)return;h(t[n],e)}})}function h(e){var t=void 0!==arguments[1]?arguments[1]:null;null===t?e.checked=!e.checked:e.checked=t,y.fireEvent(e,"change")}function f(t){for(var n=e.getSelectedRange(),o=n.getTopLeftCorner(),r=n.getBottomRightCorner(),i=o.row;i<=r.row;i++)for(var s=o.col;s<=r.col;s++){var a=e.getCell(i,s),l=e.getCellMeta(i,s),u=a.querySelectorAll("input[type=checkbox]");u.length>0&&!l.readOnly&&t(u)}}var y=new w(e),b=r(),R=u.label,M=!1;if("undefined"==typeof u.checkedTemplate&&(u.checkedTemplate=!0),"undefined"==typeof u.uncheckedTemplate&&(u.uncheckedTemplate=!1),p(t),l===u.checkedTemplate||v(l,u.checkedTemplate)?b.checked=!0:l===u.uncheckedTemplate||v(l,u.uncheckedTemplate)?b.checked=!1:null===l?g(b,"noValue"):(b.style.display="none",g(b,O),M=!0),!M&&R){var k="";R.value?k="function"==typeof R.value?R.value.call(this,n,o,a,l):R.value:R.property&&(k=e.getDataAtRowProp(n,R.property));var H=i(k);"before"===R.position?H.appendChild(b):H.insertBefore(b,H.firstChild),b=H}t.appendChild(b),M&&t.appendChild(document.createTextNode("#bad-value#")),u.readOnly?y.addEventListener(b,"click",s):(y.addEventListener(b,"mousedown",_),y.addEventListener(b,"mouseup",_),y.addEventListener(b,"change",function(t){e.setDataAtRowProp(n,a,t.target.checked?u.checkedTemplate:u.uncheckedTemplate)})),T.has(e)||(T.set(e,!0),e.addHook("beforeKeyDown",c))}function r(){var e=document.createElement("input");return e.className="htCheckboxRendererInput",e.type="checkbox",e.setAttribute("autocomplete","off"),e.cloneNode(!1)}function i(e){var t=document.createElement("label");return t.className="htCheckboxRendererLabel",t.appendChild(document.createTextNode(e)),t.cloneNode(!0)}function s(e){e.preventDefault()}Object.defineProperties(n,{checkboxRenderer:{get:function(){return o}},__esModule:{value:!0}});var a,l,u,c,d,h,f=(a=e("helpers/dom/element"),a&&a.__esModule&&a||{"default":a}),p=f.empty,g=f.addClass,m=f.hasClass,v=(l=e("helpers/string"),l&&l.__esModule&&l||{"default":l}).equalsIgnoreCase,w=(u=e("eventManager"),u&&u.__esModule&&u||{"default":u}).EventManager,y=(c=e("renderers"),c&&c.__esModule&&c||{"default":c}),b=(y.getRenderer,y.registerRenderer),C=(d=e("helpers/unicode"),d&&d.__esModule&&d||{"default":d}).KEY_CODES,R=(h=e("helpers/dom/event"),h&&h.__esModule&&h||{"default":h}),_=R.stopPropagation,S=R.stopImmediatePropagation,E=R.isImmediatePropagationStopped,T=new WeakMap,O="htBadValue";b("checkbox",o)},{eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46,"helpers/string":52,"helpers/unicode":53,renderers:88}],92:[function(e,t,n){"use strict";function o(e,t,n,o,r,i,a){l("base").apply(this,arguments),s(t,i)}Object.defineProperties(n,{htmlRenderer:{get:function(){return o}},__esModule:{value:!0}});var r,i,s=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}).fastInnerHTML,a=(i=e("renderers"),i&&i.__esModule&&i||{"default":i}),l=a.getRenderer,u=a.registerRenderer;u("html",o)},{"helpers/dom/element":45,renderers:88}],93:[function(e,t,n){"use strict";function o(e,t,n,o,r,i,s){f(i)&&("undefined"!=typeof s.language&&l.language(s.language),i=l(i).format(s.format||"0"),u(t,"htNumeric")),d("text")(e,t,n,o,r,i,s)}Object.defineProperties(n,{numericRenderer:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l=(r=e("numeral"),r&&r.__esModule&&r||{"default":r})["default"],u=(i=e("helpers/dom/element"),i&&i.__esModule&&i||{"default":i}).addClass,c=(s=e("renderers"),s&&s.__esModule&&s||{"default":s}),d=c.getRenderer,h=c.registerRenderer,f=(a=e("helpers/number"),a&&a.__esModule&&a||{"default":a}).isNumeric;h("numeric",o)},{"helpers/dom/element":45,"helpers/number":49,numeral:"numeral",renderers:88
}],94:[function(e,t,n){"use strict";function o(e,t,n,o,r,i,a){l("text").apply(this,arguments),i=t.innerHTML;var u,c=a.hashLength||i.length,d=a.hashSymbol||"*";for(u="";u.split(d).length-1<c;u+=d);s(t,u)}Object.defineProperties(n,{passwordRenderer:{get:function(){return o}},__esModule:{value:!0}});var r,i,s=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}).fastInnerHTML,a=(i=e("renderers"),i&&i.__esModule&&i||{"default":i}),l=a.getRenderer,u=a.registerRenderer;u("password",o)},{"helpers/dom/element":45,renderers:88}],95:[function(e,t,n){"use strict";function o(e,t,n,o,r,i,s){h("base").apply(this,arguments),!i&&s.placeholder&&(i=s.placeholder);var a=c(i);if(e.getSettings().trimWhitespace||(a=a.replace(/ /g,String.fromCharCode(160))),s.rendererTemplate){l(t);var d=document.createElement("TEMPLATE");d.setAttribute("bind","{{}}"),d.innerHTML=s.rendererTemplate,HTMLTemplateElement.decorate(d),d.model=e.getSourceDataAtRow(n),t.appendChild(d)}else u(t,a)}Object.defineProperties(n,{textRenderer:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),l=a.empty,u=a.fastInnerText,c=(i=e("helpers/mixed"),i&&i.__esModule&&i||{"default":i}).stringify,d=(s=e("renderers"),s&&s.__esModule&&s||{"default":s}),h=d.getRenderer,f=d.registerRenderer;f("text",o)},{"helpers/dom/element":45,"helpers/mixed":48,renderers:88}],96:[function(e,t,n){"use strict";!function(e){function t(e){return{configurable:!0,enumerable:!1,value:e,writable:!0}}function n(){return"__$"+Math.floor(1e9*Math.random())+"$"+ ++Y+"$__"}function o(e){return X[e]}function r(){var e=n();return X[e]=!0,e}function i(e){return"object"==typeof e&&e instanceof l}function s(e){return i(e)?"symbol":typeof e}function a(e){var t=new l(e);if(!(this instanceof a))return t;throw new TypeError("Symbol cannot be new'ed")}function l(e){var t=n();P(this,$,{value:this}),P(this,U,{value:t}),P(this,G,{value:e}),c(this),K[t]=this}function u(e){var t=e[q];return t&&t.self===e?t:V(e)?(J.hash.value=Q++,J.self.value=e,Z.value=x(null,J),P(e,q,Z),Z.value):void 0}function c(e){return u(e),N.apply(this,arguments)}function d(e){return u(e),B.apply(this,arguments)}function h(e){return u(e),F.apply(this,arguments)}function f(e){return K[e]||X[e]}function p(e){return i(e)?e[U]:e}function g(e){for(var t=[],n=0;n<e.length;n++)f(e[n])||t.push(e[n]);return t}function m(e){return g(W(e))}function v(e){return g(I(e))}function w(e){for(var t=[],n=W(e),o=0;o<n.length;o++){var r=K[n[o]];r&&t.push(r)}return t}function y(e,t){return L(e,p(t))}function b(e){return j.call(this,p(e))}function C(t){return e.traceur&&e.traceur.options[t]}function R(e,t,n){return i(t)&&(t=t[U]),P(e,t,n),e}function _(e){P(e,"defineProperty",{value:R}),P(e,"getOwnPropertyNames",{value:m}),P(e,"getOwnPropertyDescriptor",{value:y}),P(e.prototype,"hasOwnProperty",{value:b}),P(e,"freeze",{value:c}),P(e,"preventExtensions",{value:d}),P(e,"seal",{value:h}),P(e,"keys",{value:v})}function S(e){for(var t=1;t<arguments.length;t++)for(var n=W(arguments[t]),o=0;o<n.length;o++){var r=n[o];f(r)||!function(t,n){P(e,n,{get:function(){return t[n]},enumerable:!0})}(arguments[t],n[o])}return e}function E(e){return null!=e&&("object"==typeof e||"function"==typeof e)}function T(e){if(null==e)throw D();return H(e)}function O(e){if(null==e)throw new TypeError("Value cannot be converted to an Object");return e}function M(e,t){e.Symbol||(e.Symbol=t,Object.getOwnPropertySymbols=w),e.Symbol.iterator||(e.Symbol.iterator=t("Symbol.iterator"))}function k(e){M(e,a),e.Reflect=e.Reflect||{},e.Reflect.global=e.Reflect.global||e,_(e.Object)}if(!e.$traceurRuntime){var H=Object,D=TypeError,x=H.create,A=H.defineProperties,P=H.defineProperty,N=H.freeze,L=H.getOwnPropertyDescriptor,W=H.getOwnPropertyNames,I=H.keys,j=H.prototype.hasOwnProperty,B=Object.preventExtensions,F=Object.seal,V=Object.isExtensible,z=t,Y=0,U=n(),G=n(),$=n(),K=x(null),X=x(null);P(a.prototype,"constructor",t(a)),P(a.prototype,"toString",z(function(){var e=this[$];if(!C("symbols"))return e[U];if(!e)throw TypeError("Conversion from symbol to string");var t=e[G];return void 0===t&&(t=""),"Symbol("+t+")"})),P(a.prototype,"valueOf",z(function(){var e=this[$];if(!e)throw TypeError("Conversion from symbol to string");return C("symbols")?e:e[U]})),P(l.prototype,"constructor",t(a)),P(l.prototype,"toString",{value:a.prototype.toString,enumerable:!1}),P(l.prototype,"valueOf",{value:a.prototype.valueOf,enumerable:!1});var q=r(),Z={value:void 0},J={hash:{value:void 0},self:{value:void 0}},Q=0;c(l.prototype),k(e),e.$traceurRuntime={checkObjectCoercible:O,createPrivateName:r,defineProperties:A,defineProperty:P,exportStar:S,getOwnHashObject:u,getOwnPropertyDescriptor:L,getOwnPropertyNames:W,isObject:E,isPrivateName:o,isSymbolString:f,keys:I,setupGlobals:k,toObject:T,toProperty:p,"typeof":s}}}(window),function(){function e(){for(var e,n=[],o=0,r=0;r<arguments.length;r++){var i=$traceurRuntime.checkObjectCoercible(arguments[r]);if("function"!=typeof i[t(Symbol.iterator)])throw new TypeError("Cannot spread non-iterable object.");for(var s=i[t(Symbol.iterator)]();!(e=s.next()).done;)n[o++]=e.value}return n}var t=$traceurRuntime.toProperty;$traceurRuntime.spread=e}(),function(){function e(e,t){var n=g(e);do{var o=p(n,t);if(o)return o;n=g(n)}while(n)}function t(e){return e.__proto__}function n(e,t,n,r){return o(e,t,n).apply(e,r)}function o(t,n,o){var r=e(n,o);return r?r.get?r.get.call(t):r.value:void 0}function r(t,n,o,r){var i=e(n,o);if(i&&i.set)return i.set.call(t,r),r;throw c("super has no setter '"+o+"'.")}function i(e){for(var t={},n=w(e),o=0;o<n.length;o++){var r=n[o];t[r]=p(e,r)}for(var i=y(e),o=0;o<i.length;o++){var s=i[o];t[m(s)]=p(e,m(s))}return t}function s(e,t,n,o){return f(t,"constructor",{value:e,configurable:!0,enumerable:!1,writable:!0}),arguments.length>3?("function"==typeof o&&(e.__proto__=o),e.prototype=d(a(o),i(t))):e.prototype=t,f(e,"prototype",{configurable:!1,writable:!1}),h(e,i(n))}function a(e){if("function"==typeof e){var t=e.prototype;if(u(t)===t||null===t)return e.prototype;throw new c("super prototype must be an Object or null")}if(null===e)return null;throw new c("Super expression must either be null or a function, not "+typeof e+".")}function l(e,t,o){null!==g(t)&&n(e,t,"constructor",o)}var u=Object,c=TypeError,d=u.create,h=$traceurRuntime.defineProperties,f=$traceurRuntime.defineProperty,p=$traceurRuntime.getOwnPropertyDescriptor,g=Object.getPrototypeOf,m=$traceurRuntime.toProperty,v=Object,w=v.getOwnPropertyNames,y=v.getOwnPropertySymbols;$traceurRuntime.createClass=s,$traceurRuntime.defaultSuperCall=l,$traceurRuntime.superCall=n,$traceurRuntime.superConstructor=t,$traceurRuntime.superGet=o,$traceurRuntime.superSet=r}()},{}],97:[function(e,t,n){"use strict";function o(e){var t=this;this.eventManager=b(e),this.instance=e,this.settings=e.getSettings(),this.selectionMouseDown=!1;var n=e.rootElement.getAttribute("style");n&&e.rootElement.setAttribute("data-originalstyle",n),d(e.rootElement,"handsontable");var o=document.createElement("TABLE");d(o,"htCore"),e.getSettings().tableClassName&&d(o,e.getSettings().tableClassName),this.THEAD=document.createElement("THEAD"),o.appendChild(this.THEAD),this.TBODY=document.createElement("TBODY"),o.appendChild(this.TBODY),e.table=o,e.container.insertBefore(o,e.container.firstChild),this.eventManager.addEventListener(e.rootElement,"mousedown",function(e){this.selectionMouseDown=!0,t.isTextSelectionAllowed(e.target)||(i(),e.preventDefault(),window.focus())}),this.eventManager.addEventListener(e.rootElement,"mouseup",function(e){this.selectionMouseDown=!1}),this.eventManager.addEventListener(e.rootElement,"mousemove",function(e){this.selectionMouseDown&&!t.isTextSelectionAllowed(e.target)&&(i(),e.preventDefault())}),this.eventManager.addEventListener(document.documentElement,"keyup",function(t){e.selection.isInProgress()&&!t.shiftKey&&e.selection.finish()});var r;this.isMouseDown=function(){return r},this.eventManager.addEventListener(document.documentElement,"mouseup",function(t){e.selection.isInProgress()&&1===t.which&&e.selection.finish(),r=!1,y(document.activeElement)&&e.unlisten()}),this.eventManager.addEventListener(document.documentElement,"mousedown",function(n){var o=n.target,i=n.x||n.clientX,s=n.y||n.clientY;if(!r&&e.rootElement){if(o===e.view.wt.wtTable.holder){var a=g();if(document.elementFromPoint(i+a,s)!==e.view.wt.wtTable.holder||document.elementFromPoint(i,s+a)!==e.view.wt.wtTable.holder)return}else for(;o!==document.documentElement;){if(null===o){if(n.isTargetWebComponent)break;return}if(o===e.rootElement)return;o=o.parentNode}t.settings.outsideClickDeselects?e.deselectCell():e.destroyEditor()}}),this.eventManager.addEventListener(o,"selectstart",function(e){t.settings.fragmentSelection||w(e.target)||e.preventDefault()});var i=function(){window.getSelection?window.getSelection().empty?window.getSelection().empty():window.getSelection().removeAllRanges&&window.getSelection().removeAllRanges():document.selection&&document.selection.empty()},s=[new E({className:"current",border:{width:2,color:"#5292F7",cornerVisible:function(){return t.settings.fillHandle&&!t.isCellEdited()&&!e.selection.isMultiple()},multipleSelectionHandlesVisible:function(){return!t.isCellEdited()&&!e.selection.isMultiple()}}}),new E({className:"area",border:{width:1,color:"#89AFF9",cornerVisible:function(){return t.settings.fillHandle&&!t.isCellEdited()&&e.selection.isMultiple()},multipleSelectionHandlesVisible:function(){return!t.isCellEdited()&&e.selection.isMultiple()}}}),new E({className:"highlight",highlightRowClassName:t.settings.currentRowClassName,highlightColumnClassName:t.settings.currentColClassName}),new E({className:"fill",border:{width:1,color:"red"}})];s.current=s[0],s.area=s[1],s.highlight=s[2],s.fill=s[3];var a={debug:function(){return t.settings.debug},externalRowCalculator:this.instance.getPlugin("autoRowSize")&&this.instance.getPlugin("autoRowSize").isEnabled(),table:o,stretchH:this.settings.stretchH,data:e.getDataAtCell,totalRows:function(){return e.countRows()},totalColumns:function(){return e.countCols()},fixedColumnsLeft:function(){return t.settings.fixedColumnsLeft},fixedRowsTop:function(){return t.settings.fixedRowsTop},fixedRowsBottom:function(){return t.settings.fixedRowsBottom},minSpareRows:function(){return t.settings.minSpareRows},renderAllRows:t.settings.renderAllRows,rowHeaders:function(){var n=[];return e.hasRowHeaders()&&n.push(function(e,n){t.appendRowHeader(e,n)}),Handsontable.hooks.run(e,"afterGetRowHeaderRenderers",n),n},columnHeaders:function(){var n=[];return e.hasColHeaders()&&n.push(function(e,n){t.appendColHeader(e,n)}),Handsontable.hooks.run(e,"afterGetColumnHeaderRenderers",n),n},columnWidth:e.getColWidth,rowHeight:e.getRowHeight,cellRenderer:function(e,n,o){var r=t.instance.colToProp(n),i=t.instance.getCellMeta(e,n),s=t.instance.getCellRenderer(i),a=t.instance.getDataAtRowProp(e,r);s(t.instance,o,e,n,r,a,i),Handsontable.hooks.run(t.instance,"afterRenderer",o,e,n,r,a,i)},selections:s,hideBorderOnMouseDownOver:function(){return t.settings.fragmentSelection},onCellMouseDown:function(n,o,i,s){var a,l,u=i.parentNode,c=u.parentNode;if(e.listen(),t.activeWt=s,r=!0,Handsontable.hooks.run(e,"beforeOnCellMouseDown",n,o,i),e.selection.setSelectedHeaders(!1,!1),!_(n)){if(2===n.button&&e.selection.inInSelection(o));else n.shiftKey?o.row>=0&&o.col>=0&&e.selection.setRangeEnd(o):(o.row<0||o.col<0)&&(o.row>=0||o.col>=0)?(o.row<0&&(a=c.childNodes.length-Array.prototype.indexOf.call(c.childNodes,u)-1,l=e.getHeaderColspan(o.col,a),e.selection.setSelectedHeaders(!1,!0),e.selectCell(0,o.col,e.countRows()-1,o.col+Math.max(0,l-1))),o.col<0&&(e.selection.setSelectedHeaders(!0,!1),e.selectCell(o.row,0,o.row,e.countCols()-1))):(o.row=o.row<0?0:o.row,o.col=o.col<0?0:o.col,e.selection.setRangeStart(o));Handsontable.hooks.run(e,"afterOnCellMouseDown",n,o,i),t.activeWt=t.wt}},onCellMouseOver:function(n,o,i,s){t.activeWt=s,o.row>=0&&o.col>=0?r&&e.selection.setRangeEnd(o):r&&(o.row<0&&(e.selection.selectedHeader.cols?(e.selection.setRangeEnd(new S(e.countRows()-1,o.col)),e.selection.setSelectedHeaders(!1,!0)):e.selection.setRangeEnd(new S(o.row,o.col))),o.col<0&&(e.selection.selectedHeader.rows?(e.selection.setRangeEnd(new S(o.row,e.countCols()-1)),e.selection.setSelectedHeaders(!0,!1)):e.selection.setRangeEnd(new S(o.row,o.col)))),Handsontable.hooks.run(e,"afterOnCellMouseOver",n,o,i),t.activeWt=t.wt},onCellCornerMouseDown:function(t){t.preventDefault(),Handsontable.hooks.run(e,"afterOnCellCornerMouseDown",t)},beforeDraw:function(e){t.beforeRender(e)},onDraw:function(e){t.onDraw(e)},onScrollVertically:function(){e.runHooks("afterScrollVertically")},onScrollHorizontally:function(){e.runHooks("afterScrollHorizontally")},onBeforeDrawBorders:function(t,n){e.runHooks("beforeDrawBorders",t,n)},onBeforeTouchScroll:function(){e.runHooks("beforeTouchScroll")},onAfterMomentumScroll:function(){e.runHooks("afterMomentumScroll")},viewportRowCalculatorOverride:function(n){var o=e.countRows(),r=t.settings.viewportRowRenderingOffset;if("auto"===r&&t.settings.fixedRowsTop&&(r=10),"number"==typeof r&&(n.startRow=Math.max(n.startRow-r,0),n.endRow=Math.min(n.endRow+r,o-1)),"auto"===r){var i=n.startRow+n.endRow-n.startRow,s=Math.ceil(i/o*12);n.startRow=Math.max(n.startRow-s,0),n.endRow=Math.min(n.endRow+s,o-1)}e.runHooks("afterViewportRowCalculatorOverride",n)},viewportColumnCalculatorOverride:function(n){var o=e.countCols(),r=t.settings.viewportColumnRenderingOffset;if("auto"===r&&t.settings.fixedColumnsLeft&&(r=10),"number"==typeof r&&(n.startColumn=Math.max(n.startColumn-r,0),n.endColumn=Math.min(n.endColumn+r,o-1)),"auto"===r){var i=n.startColumn+n.endColumn-n.startColumn,s=Math.ceil(i/o*12);n.startRow=Math.max(n.startColumn-s,0),n.endColumn=Math.min(n.endColumn+s,o-1)}e.runHooks("afterViewportColumnCalculatorOverride",n)}};Handsontable.hooks.run(e,"beforeInitWalkontable",a),this.wt=new T(a),this.activeWt=this.wt,this.eventManager.addEventListener(t.wt.wtTable.spreader,"mousedown",function(e){e.target===t.wt.wtTable.spreader&&3===e.which&&R(e)}),this.eventManager.addEventListener(t.wt.wtTable.spreader,"contextmenu",function(e){e.target===t.wt.wtTable.spreader&&3===e.which&&R(e)}),this.eventManager.addEventListener(document.documentElement,"click",function(){t.settings.observeDOMVisibility&&t.wt.drawInterrupted&&(t.instance.forceFullRender=!0,t.render())})}Object.defineProperties(n,{TableView:{get:function(){return o}},__esModule:{value:!0}});var r,i,s,a,l,u,c=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),d=c.addClass,h=c.empty,f=c.fastInnerHTML,p=c.fastInnerText,g=c.getScrollbarWidth,m=c.hasClass,v=c.isChildOf,w=c.isInput,y=c.isOutsideInput,b=(i=e("eventManager"),i&&i.__esModule&&i||{"default":i}).eventManager,C=(s=e("helpers/dom/event"),s&&s.__esModule&&s||{"default":s}),R=C.stopPropagation,_=C.isImmediatePropagationStopped,S=(a=e("3rdparty/walkontable/src/cell/coords"),a&&a.__esModule&&a||{"default":a}).WalkontableCellCoords,E=(l=e("3rdparty/walkontable/src/selection"),l&&l.__esModule&&l||{"default":l}).WalkontableSelection,T=(u=e("3rdparty/walkontable/src/core"),u&&u.__esModule&&u||{"default":u}).Walkontable;Handsontable.TableView=o,o.prototype.isTextSelectionAllowed=function(e){if(w(e))return!0;var t=v(e,this.instance.view.wt.wtTable.spreader);return this.settings.fragmentSelection===!0&&t?!0:"cell"===this.settings.fragmentSelection&&this.isSelectedOnlyCell()&&t?!0:!1},o.prototype.isSelectedOnlyCell=function(){var e=this.instance.getSelected()||[],t=e[0],n=e[1],o=e[2],r=e[3];return void 0!==t&&t===o&&n===r},o.prototype.isCellEdited=function(){var e=this.instance.getActiveEditor();return e&&e.isOpened()},o.prototype.beforeRender=function(e){e&&Handsontable.hooks.run(this.instance,"beforeRender",this.instance.forceFullRender)},o.prototype.onDraw=function(e){e&&Handsontable.hooks.run(this.instance,"afterRender",this.instance.forceFullRender)},o.prototype.render=function(){this.wt.draw(!this.instance.forceFullRender),this.instance.forceFullRender=!1,this.instance.renderCall=!1},o.prototype.getCellAtCoords=function(e,t){var n=this.wt.getCell(e,t);return 0>n?null:n},o.prototype.scrollViewport=function(e){this.wt.scrollViewport(e)},o.prototype.appendRowHeader=function(e,t){if(t.firstChild){var n=t.firstChild;if(!m(n,"relative"))return h(t),void this.appendRowHeader(e,t);this.updateCellHeader(n.querySelector(".rowHeader"),e,this.instance.getRowHeader)}else{var o=document.createElement("div"),r=document.createElement("span");o.className="relative",r.className="rowHeader",this.updateCellHeader(r,e,this.instance.getRowHeader),o.appendChild(r),t.appendChild(o)}Handsontable.hooks.run(this.instance,"afterGetRowHeader",e,t)},o.prototype.appendColHeader=function(e,t){if(t.firstChild){var n=t.firstChild;m(n,"relative")?this.updateCellHeader(n.querySelector(".colHeader"),e,this.instance.getColHeader):(h(t),this.appendColHeader(e,t))}else{var o=document.createElement("div"),r=document.createElement("span");o.className="relative",r.className="colHeader",this.updateCellHeader(r,e,this.instance.getColHeader),o.appendChild(r),t.appendChild(o)}Handsontable.hooks.run(this.instance,"afterGetColHeader",e,t)},o.prototype.updateCellHeader=function(e,t,n){t>-1?f(e,n(t)):(p(e,String.fromCharCode(160)),d(e,"cornerHeader"))},o.prototype.maximumVisibleElementWidth=function(e){var t=this.wt.wtViewport.getWorkspaceWidth(),n=t-e;return n>0?n:0},o.prototype.maximumVisibleElementHeight=function(e){var t=this.wt.wtViewport.getWorkspaceHeight(),n=t-e;return n>0?n:0},o.prototype.mainViewIsActive=function(){return this.wt===this.activeWt},o.prototype.destroy=function(){this.wt.destroy(),this.eventManager.destroy()}},{"3rdparty/walkontable/src/cell/coords":5,"3rdparty/walkontable/src/core":7,"3rdparty/walkontable/src/selection":18,eventManager:41,"helpers/dom/element":45,"helpers/dom/event":46}],98:[function(e,t,n){"use strict";Object.defineProperties(n,{GhostTable:{get:function(){return f}},__esModule:{value:!0}});var o,r,i,s,a,l=(o=e("helpers/dom/element"),o&&o.__esModule&&o||{"default":o}),u=l.addClass,c=l.outerHeight,d=l.outerWidth,h=(r=e("helpers/array"),r&&r.__esModule&&r||{"default":r}).arrayEach,f=((i=e("helpers/object"),i&&i.__esModule&&i||{"default":i}).objectEach,(s=e("helpers/number"),s&&s.__esModule&&s||{"default":s}).rangeEach,(a=e("helpers/mixed"),a&&a.__esModule&&a||{"default":a}).stringify,function(e){this.hot=e,this.container=null,this.injected=!1,this.rows=[],this.columns=[],this.samples=null});$traceurRuntime.createClass(f,{addRow:function(e,t){if(this.columns.length)throw new Error("Doesn't support multi-dimensional table");this.rows.length||(this.container=this.createContainer(this.hot.rootElement.className));var n={row:e};this.rows.push(n),this.samples=t,this.table=this.createTable(this.hot.table.className),this.table.colGroup.appendChild(this.createColGroupsCol()),this.table.tr.appendChild(this.createRow(e)),this.container.container.appendChild(this.table.fragment),n.table=this.table.table},addColumn:function(e,t){if(this.rows.length)throw new Error("Doesn't support multi-dimensional table");this.columns.length||(this.container=this.createContainer(this.hot.rootElement.className));var n={col:e};this.columns.push(n),this.samples=t,this.table=this.createTable(this.hot.table.className),null!==this.hot.getColHeader(e)&&this.hot.view.appendColHeader(e,this.table.th),this.table.tBody.appendChild(this.createCol(e)),this.container.container.appendChild(this.table.fragment),n.table=this.table.table},getHeights:function(e){this.injected||this.injectTable(),h(this.rows,function(t){e(t.row,c(t.table)-1)})},getWidths:function(e){this.injected||this.injectTable(),h(this.columns,function(t){e(t.col,d(t.table))})},createColGroupsCol:function(){var e=this,t=document,n=t.createDocumentFragment();return n.appendChild(this.createColElement(-1)),this.samples.forEach(function(t){h(t.strings,function(t){n.appendChild(e.createColElement(t.col))})}),n},createRow:function(e){var t=this,n=document,o=n.createDocumentFragment(),r=n.createElement("th");return null!==this.hot.getRowHeader(e)&&this.hot.view.appendRowHeader(e,r),o.appendChild(r),this.samples.forEach(function(r){h(r.strings,function(r){var i=r.col,s=t.hot.getCellMeta(e,i);s.col=i,s.row=e;var a=t.hot.getCellRenderer(s),l=n.createElement("td");a(t.hot,l,e,i,t.hot.colToProp(i),r.value,s),o.appendChild(l)})}),o},createCol:function(e){var t=this,n=document,o=n.createDocumentFragment();return this.samples.forEach(function(r){h(r.strings,function(r){var i=r.row,s=t.hot.getCellMeta(i,e);s.col=e,s.row=i;var a=t.hot.getCellRenderer(s),l=n.createElement("td"),u=n.createElement("tr");a(t.hot,l,i,e,t.hot.colToProp(i),r.value,s),u.appendChild(l),o.appendChild(u)})}),o},clean:function(){this.rows.length=0,this.columns.length=0,this.samples&&this.samples.clear(),this.samples=null,this.removeTable()},injectTable:function(){var e=void 0!==arguments[0]?arguments[0]:null;this.injected||((e||this.hot.rootElement).appendChild(this.container.fragment),this.injected=!0)},removeTable:function(){this.injected&&this.container.container.parentNode&&(this.container.container.parentNode.removeChild(this.container.container),this.container=null,this.injected=!1)},createColElement:function(e){var t=document,n=t.createElement("col");return n.style.width=this.hot.view.wt.wtTable.getStretchedColumnWidth(e)+"px",n},createTable:function(){var e=void 0!==arguments[0]?arguments[0]:"",t=document,n=t.createDocumentFragment(),o=t.createElement("table"),r=t.createElement("thead"),i=t.createElement("tbody"),s=t.createElement("colgroup"),a=t.createElement("tr"),l=t.createElement("th");return this.isVertical()&&o.appendChild(s),this.isHorizontal()&&(a.appendChild(l),r.appendChild(a),o.style.tableLayout="auto",o.style.width="auto"),o.appendChild(r),this.isVertical()&&i.appendChild(a),o.appendChild(i),u(o,e),n.appendChild(o),{fragment:n,table:o,tHead:r,tBody:i,colGroup:s,tr:a,th:l}},createContainer:function(){var e=void 0!==arguments[0]?arguments[0]:"",t=document,n=t.createDocumentFragment(),o=t.createElement("div");return e="htGhostTable htAutoSize "+e.trim(),u(o,e),n.appendChild(o),{fragment:n,container:o}},isVertical:function(){return this.rows.length&&!this.columns.length?!0:!1},isHorizontal:function(){return this.columns.length&&!this.rows.length?!0:!1}},{}),Handsontable.utils=Handsontable.utils||{},Handsontable.utils.GhostTable=f},{"helpers/array":42,"helpers/dom/element":45,"helpers/mixed":48,"helpers/number":49,"helpers/object":50}],99:[function(e,t,n){"use strict";var o;Object.defineProperties(n,{SamplesGenerator:{get:function(){return h}},__esModule:{value:!0}});var r,i,s,a,l,u=(r=e("helpers/dom/element"),r&&r.__esModule&&r||{"default":r}),c=(u.addClass,u.outerHeight,u.outerWidth,(i=e("helpers/array"),i&&i.__esModule&&i||{"default":i}).arrayEach,(s=e("helpers/object"),s&&s.__esModule&&s||{"default":s}).objectEach,(a=e("helpers/number"),a&&a.__esModule&&a||{"default":a}).rangeEach),d=(l=e("helpers/mixed"),l&&l.__esModule&&l||{"default":l}).stringify,h=function(e){this.samples=null,this.dataFactory=e},f=h;$traceurRuntime.createClass(h,(o={},Object.defineProperty(o,"generateRowSamples",{value:function(e,t){return this.generateSamples("row",t,e)},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(o,"generateColumnSamples",{value:function(e,t){return this.generateSamples("col",t,e)},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(o,"generateSamples",{value:function(e,t,n){var o=this,r=new Map;return"number"==typeof n&&(n={from:n,to:n}),c(n.from,n.to,function(n){var i=o.generateSample(e,t,n);r.set(n,i)}),r},configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(o,"generateSample",{value:function(e,t,n){var o=this,r=new Map;return c(t.from,t.to,function(t){var i,s;if("row"===e)s=o.dataFactory(n,t);else{if("col"!==e)throw new Error("Unsupported sample type");s=o.dataFactory(t,n)}Array.isArray(s)||(s=d(s));var a=s.length;r.has(a)||r.set(a,{needed:f.SAMPLE_COUNT,strings:[]});var l=r.get(a);if(l.needed){var u="row"===e?"col":"row";l.strings.push((i={},Object.defineProperty(i,"value",{value:s,configurable:!0,enumerable:!0,writable:!0}),Object.defineProperty(i,u,{value:t,configurable:!0,enumerable:!0,writable:!0}),i)),l.needed--}}),r},configurable:!0,enumerable:!0,writable:!0}),o),{get SAMPLE_COUNT(){return 3}}),Handsontable.utils=Handsontable.utils||{},Handsontable.utils.SamplesGenerator=h},{"helpers/array":42,"helpers/dom/element":45,"helpers/mixed":48,"helpers/number":49,"helpers/object":50}],100:[function(e,t,n){"use strict";function o(e,t){var n=e,o="string"==typeof n?n.toLowerCase():null;return function(e){for(var r=!1,s=0,a=e.length;a>s;s++){if(n===e[s]){r=!0;break}if(o===i(e[s]).toLowerCase()){r=!0;break}}t(r)}}var r,i=(r=e("helpers/mixed"),r&&r.__esModule&&r||{"default":r}).stringify;Handsontable.AutocompleteValidator=function(e,t){this.strict&&this.source?"function"==typeof this.source?this.source(e,o(e,t)):o(e,t)(this.source):t(!0)}},{"helpers/mixed":48}],101:[function(e,t,n){"use strict";var o,r,i=(o=e("moment"),o&&o.__esModule&&o||{"default":o})["default"],s=(r=e("editors"),r&&r.__esModule&&r||{"default":r}).getEditor;Handsontable.DateValidator=function(e,t){var n=!0,o=s("date",this.instance);null===e&&(e="");var r=i(new Date(e)).isValid(),l=i(e,this.dateFormat||o.defaultDateFormat,!0).isValid();if(r||(n=!1),!r&&l&&(n=!0),r&&!l)if(this.correctFormat===!0){var u=a(e,this.dateFormat);this.instance.setDataAtCell(this.row,this.col,u,"dateValidator"),n=!0}else n=!1;t(n)};var a=function(e,t){var n=i(new Date(e)),o=n.format("YYYY"),r=i().format("YYYY");return o.substr(0,2)!==r.substr(0,2)?e.match(new RegExp(o))||n.year(o.replace(o.substr(0,2),r.substr(0,2))):o.length>4&&n.year((n.year()+"").substr(0,4)),n.format(t)}},{editors:29,moment:"moment"}],102:[function(e,t,n){"use strict";Handsontable.NumericValidator=function(e,t){null===e&&(e=""),t(/^-?\d*(\.|\,)?\d*$/.test(e))}},{}],SheetClip:[function(e,t,n){"use strict";!function(e){function t(e){return e.split('"').length-1}var o={parse:function(e){var n,o,r,i,s,a,l,u=[],c=0;for(r=e.split("\n"),r.length>1&&""===r[r.length-1]&&r.pop(),n=0,o=r.length;o>n;n+=1){for(r[n]=r[n].split("	"),i=0,s=r[n].length;s>i;i+=1)u[c]||(u[c]=[]),a&&0===i?(l=u[c].length-1,u[c][l]=u[c][l]+"\n"+r[n][0],a&&1&t(r[n][0])&&(a=!1,u[c][l]=u[c][l].substring(0,u[c][l].length-1).replace(/""/g,'"'))):i===s-1&&0===r[n][i].indexOf('"')&&1&t(r[n][i])?(u[c].push(r[n][i].substring(1).replace(/""/g,'"')),a=!0):(u[c].push(r[n][i].replace(/""/g,'"')),a=!1);a||(c+=1)}return u},stringify:function(e){var t,n,o,r,i,s="";for(t=0,n=e.length;n>t;t+=1){for(r=e[t].length,o=0;r>o;o+=1)o>0&&(s+="	"),i=e[t][o],s+="string"==typeof i?i.indexOf("\n")>-1?'"'+i.replace(/"/g,'""')+'"':i:null===i||void 0===i?"":i;s+="\n"}return s}};"undefined"!=typeof n?(n.parse=o.parse,n.stringify=o.stringify):e.SheetClip=o}(window)},{}],autoResize:[function(e,t,n){"use strict";function o(){var e,t={minHeight:200,maxHeight:300,minWidth:100,maxWidth:300},n=document.body,o=document.createTextNode(""),r=document.createElement("SPAN"),i=function(e,t,n){window.attachEvent?e.attachEvent("on"+t,n):e.addEventListener(t,n,!1)},s=function(e,t,n){window.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent("on"+t,n)},a=function(i){var s,a;i?/^[a-zA-Z \.,\\\/\|0-9]$/.test(i)||(i="."):i="",void 0!==o.textContent?o.textContent=e.value+i:o.data=e.value+i,r.style.fontSize=Handsontable.Dom.getComputedStyle(e).fontSize,r.style.fontFamily=Handsontable.Dom.getComputedStyle(e).fontFamily,r.style.whiteSpace="pre",n.appendChild(r),s=r.clientWidth+2,n.removeChild(r),e.style.height=t.minHeight+"px",t.minWidth>s?e.style.width=t.minWidth+"px":s>t.maxWidth?e.style.width=t.maxWidth+"px":e.style.width=s+"px",a=e.scrollHeight?e.scrollHeight-1:0,t.minHeight>a?e.style.height=t.minHeight+"px":t.maxHeight<a?(e.style.height=t.maxHeight+"px",e.style.overflowY="visible"):e.style.height=a+"px"},l=function(){window.setTimeout(a,0)},u=function(n){if(n&&n.minHeight)if("inherit"==n.minHeight)t.minHeight=e.clientHeight;else{var i=parseInt(n.minHeight);isNaN(i)||(t.minHeight=i)}if(n&&n.maxHeight)if("inherit"==n.maxHeight)t.maxHeight=e.clientHeight;else{var s=parseInt(n.maxHeight);isNaN(s)||(t.maxHeight=s)}if(n&&n.minWidth)if("inherit"==n.minWidth)t.minWidth=e.clientWidth;else{var a=parseInt(n.minWidth);isNaN(a)||(t.minWidth=a)}if(n&&n.maxWidth)if("inherit"==n.maxWidth)t.maxWidth=e.clientWidth;else{var l=parseInt(n.maxWidth);isNaN(l)||(t.maxWidth=l)}r.firstChild||(r.className="autoResize",r.style.display="inline-block",r.appendChild(o))},c=function(n,o,r){e=n,u(o),"TEXTAREA"==e.nodeName&&(e.style.resize="none",e.style.overflowY="",e.style.height=t.minHeight+"px",e.style.minWidth=t.minWidth+"px",e.style.maxWidth=t.maxWidth+"px",e.style.overflowY="hidden"),r&&(i(e,"change",a),i(e,"cut",l),i(e,"paste",l),i(e,"drop",l),i(e,"keydown",l)),a()};return{init:function(e,t,n){c(e,t,n)},unObserve:function(){s(e,"change",a),s(e,"cut",l),s(e,"paste",l),s(e,"drop",l),s(e,"keydown",l)},resize:a}}"undefined"!=typeof n&&(t.exports=o)},{}],copyPaste:[function(e,t,n){"use strict";function o(){return i?i.hasBeenDestroyed()&&i.init():i=new r,i.refCounter++,i}function r(){this.refCounter=0,this.init()}var i;"undefined"!=typeof n&&(t.exports=o),r.prototype.init=function(){var e,t;this.copyCallbacks=[],this.cutCallbacks=[],this.pasteCallbacks=[],t=document.body,document.getElementById("CopyPasteDiv")?(this.elDiv=document.getElementById("CopyPasteDiv"),this.elTextarea=this.elDiv.firstChild):(this.elDiv=document.createElement("div"),this.elDiv.id="CopyPasteDiv",e=this.elDiv.style,e.position="fixed",e.top="-10000px",e.left="-10000px",t.appendChild(this.elDiv),this.elTextarea=document.createElement("textarea"),this.elTextarea.className="copyPaste",this.elTextarea.onpaste=function(e){var t,n;return"WebkitAppearance"in document.documentElement.style?(t=e.clipboardData.getData("Text"),-1!==navigator.userAgent.indexOf("Safari")&&-1===navigator.userAgent.indexOf("Chrome")&&(n=t.split("\n"),""===n[n.length-1]&&n.pop(),t=n.join("\n")),this.value=t,!1):void 0},e=this.elTextarea.style,e.width="10000px",e.height="10000px",e.overflow="hidden",this.elDiv.appendChild(this.elTextarea),"undefined"!=typeof e.opacity&&(e.opacity=0)),this.onKeyDownRef=this.onKeyDown.bind(this),document.documentElement.addEventListener("keydown",this.onKeyDownRef,!1)},r.prototype.onKeyDown=function(e){function t(){var e=document.activeElement;return e.shadowRoot&&e.shadowRoot.activeElement&&(e=e.shadowRoot.activeElement),["INPUT","SELECT","TEXTAREA"].indexOf(e.nodeName)>-1||"true"===e.contentEditable}var n=this,o=!1;if(e.metaKey?o=!0:e.ctrlKey&&-1===navigator.userAgent.indexOf("Mac")&&(o=!0),o){if(document.activeElement!==this.elTextarea&&(""!==this.getSelectionText()||t()))return;this.selectNodeText(this.elTextarea),setTimeout(function(){document.activeElement!==n.elTextarea&&n.selectNodeText(n.elTextarea)},0)}!o||67!==e.keyCode&&86!==e.keyCode&&88!==e.keyCode||(88===e.keyCode?setTimeout(function(){n.triggerCut(e)},0):86===e.keyCode&&setTimeout(function(){n.triggerPaste(e)},0))},r.prototype.selectNodeText=function(e){e&&e.select()},r.prototype.getSelectionText=function(){var e="";return window.getSelection?e=window.getSelection().toString():document.selection&&"Control"!==document.selection.type&&(e=document.selection.createRange().text),e},r.prototype.copyable=function(e){if("string"!=typeof e&&void 0===e.toString)throw new Error("copyable requires string parameter");this.elTextarea.value=e,this.selectNodeText(this.elTextarea)},r.prototype.onCut=function(e){this.cutCallbacks.push(e)},r.prototype.onPaste=function(e){this.pasteCallbacks.push(e)},r.prototype.removeCallback=function(e){var t,n;for(t=0,n=this.copyCallbacks.length;n>t;t++)if(this.copyCallbacks[t]===e)return this.copyCallbacks.splice(t,1),!0;for(t=0,n=this.cutCallbacks.length;n>t;t++)if(this.cutCallbacks[t]===e)return this.cutCallbacks.splice(t,1),
!0;for(t=0,n=this.pasteCallbacks.length;n>t;t++)if(this.pasteCallbacks[t]===e)return this.pasteCallbacks.splice(t,1),!0;return!1},r.prototype.triggerCut=function(e){var t=this;t.cutCallbacks&&setTimeout(function(){for(var n=0,o=t.cutCallbacks.length;o>n;n++)t.cutCallbacks[n](e)},50)},r.prototype.triggerPaste=function(e,t){var n=this;n.pasteCallbacks&&setTimeout(function(){for(var o=t||n.elTextarea.value,r=0,i=n.pasteCallbacks.length;i>r;r++)n.pasteCallbacks[r](o,e)},50)},r.prototype.destroy=function(){this.hasBeenDestroyed()||0!==--this.refCounter||(this.elDiv&&this.elDiv.parentNode&&(this.elDiv.parentNode.removeChild(this.elDiv),this.elDiv=null,this.elTextarea=null),document.documentElement.removeEventListener("keydown",this.onKeyDownRef),this.onKeyDownRef=null)},r.prototype.hasBeenDestroyed=function(){return!this.refCounter}},{}],es6collections:[function(e,t,n){"use strict";!function(e){function t(e,t){function o(e){return this&&this.constructor===o?(this._keys=[],this._values=[],this._itp=[],this.objectOnly=t,void(e&&n.call(this,e))):new o(e)}return t||y(e,"size",{get:m}),e.constructor=o,o.prototype=e,o}function n(e){this.add?e.forEach(this.add,this):e.forEach(function(e){this.set(e[0],e[1])},this)}function o(e){return this.has(e)&&(this._keys.splice(w,1),this._values.splice(w,1),this._itp.forEach(function(e){w<e[0]&&e[0]--})),w>-1}function r(e){return this.has(e)?this._values[w]:void 0}function i(e,t){if(this.objectOnly&&t!==Object(t))throw new TypeError("Invalid value used as weak collection key");if(t!=t||0===t)for(w=e.length;w--&&!b(e[w],t););else w=e.indexOf(t);return w>-1}function s(e){return i.call(this,this._values,e)}function a(e){return i.call(this,this._keys,e)}function l(e,t){return this.has(e)?this._values[w]=t:this._values[this._keys.push(e)-1]=t,this}function u(e){return this.has(e)||this._values.push(e),this}function c(){this._values.length=0}function d(){return g(this._itp,this._keys)}function h(){return g(this._itp,this._values)}function f(){return g(this._itp,this._keys,this._values)}function p(){return g(this._itp,this._values,this._values)}function g(e,t,n){var o=[0],r=!1;return e.push(o),{next:function(){var i,s=o[0];return!r&&s<t.length?(i=n?[t[s],n[s]]:t[s],o[0]++):(r=!0,e.splice(e.indexOf(o),1)),{done:r,value:i}}}}function m(){return this._values.length}function v(e,t){for(var n=this.entries();;){var o=n.next();if(o.done)break;e.call(t,o.value[1],o.value[0],this)}}var w,y=Object.defineProperty,b=function(e,t){return isNaN(e)?isNaN(t):e===t};"undefined"==typeof WeakMap&&(e.WeakMap=t({"delete":o,clear:c,get:r,has:a,set:l},!0)),"undefined"==typeof Map&&(e.Map=t({"delete":o,has:a,get:r,set:l,keys:d,values:h,entries:f,forEach:v,clear:c})),"undefined"==typeof Set&&(e.Set=t({has:s,add:u,"delete":o,clear:c,keys:h,values:h,entries:p,forEach:v})),"undefined"==typeof WeakSet&&(e.WeakSet=t({"delete":o,add:u,clear:c,has:s},!0))}("undefined"!=typeof n&&"undefined"!=typeof global?global:window)},{}],jsonpatch:[function(e,t,n){"use strict";var o;!function(e){function t(e){return-1===e.indexOf("/")&&-1===e.indexOf("~")?e:e.replace(/~/g,"~0").replace(/\//g,"~1")}function n(e,o){var r;for(var i in e)if(e.hasOwnProperty(i)){if(e[i]===o)return t(i)+"/";if("object"==typeof e[i]&&(r=n(e[i],o),""!=r))return t(i)+"/"+r}return""}function o(e,t){if(e===t)return"/";var o=n(e,t);if(""===o)throw new Error("Object not found in root");return"/"+o}function r(e){for(var t=0,n=w.length;n>t;t++)if(w[t].obj===e)return w[t]}function i(e){for(var t=0,n=w.length;n>t;t++)w[t]===e&&w.splice(t,1)}function s(e,t){for(var n=0,o=e.observers.length;o>n;n++)if(e.observers[n].callback===t)return e.observers[n].observer}function a(e,t){for(var n=0,o=e.observers.length;o>n;n++)if(e.observers[n].observer===t)return e.observers.splice(n,1),void(e.observers.length||i(e))}function l(e,t){h(t),Object.observe?d(t,e):clearTimeout(t.next);var n=r(e);a(n,t)}function u(e,t){var n,i=[],a=e,l=r(e);if(l?n=s(l,t):(l=new y(e),w.push(l)),n)return n;if(Object.observe)n=function(r){d(n,e),c(n,e);for(var s=0,l=r.length;l>s;){if(("length"!==r[s].name||!R(r[s].object))&&"__Jasmine_been_here_before__"!==r[s].name){var u=r[s].type;switch(u){case"new":u="add";break;case"deleted":u="delete";break;case"updated":u="update"}v[u].call(r[s],i,o(a,r[s].object))}s++}i&&t&&t(i),n.patches=i,i=[]};else if(n={},l.value=JSON.parse(JSON.stringify(e)),t){n.callback=t,n.next=null;var u=this.intervals||[100,1e3,1e4,6e4],f=0,p=function(){h(n)},g=function(){clearTimeout(n.next),n.next=setTimeout(function(){p(),f=0,n.next=setTimeout(m,u[f++])},0)},m=function(){p(),f==u.length&&(f=u.length-1),n.next=setTimeout(m,u[f++])};"undefined"!=typeof window&&(window.addEventListener?(window.addEventListener("mousedown",g),window.addEventListener("mouseup",g),window.addEventListener("keydown",g)):(window.attachEvent("onmousedown",g),window.attachEvent("onmouseup",g),window.attachEvent("onkeydown",g))),n.next=setTimeout(m,u[f++])}return n.patches=i,n.object=e,l.observers.push(new b(t,n)),c(n,e)}function c(e,t){if(Object.observe){Object.observe(t,e);for(var n in t)if(t.hasOwnProperty(n)){var o=t[n];o&&"object"==typeof o&&c(e,o)}}return e}function d(e,t){if(Object.observe){Object.unobserve(t,e);for(var n in t)if(t.hasOwnProperty(n)){var o=t[n];o&&"object"==typeof o&&d(e,o)}}return e}function h(e){if(Object.observe)Object.deliverChangeRecords(e);else{for(var t,n=0,o=w.length;o>n;n++)if(w[n].obj===e.object){t=w[n];break}t&&f(t.value,e.object,e.patches,"")}var r=e.patches;return r.length>0&&(e.patches=[],e.callback&&e.callback(r)),r}function f(e,n,o,r){for(var i=C(n),s=C(e),a=!1,l=!1,u=s.length-1;u>=0;u--){var c=s[u],d=e[c];if(n.hasOwnProperty(c)){var h=n[c];d instanceof Object?f(d,h,o,r+"/"+t(c)):d!=h&&(a=!0,o.push({op:"replace",path:r+"/"+t(c),value:h}),e[c]=h)}else o.push({op:"remove",path:r+"/"+t(c)}),delete e[c],l=!0}if(l||i.length!=s.length)for(var u=0;u<i.length;u++){var c=i[u];e.hasOwnProperty(c)||(o.push({op:"add",path:r+"/"+t(c),value:n[c]}),e[c]=JSON.parse(JSON.stringify(n[c])))}}function p(e,t){for(var n,o=!1,r=0,i=t.length;i>r;){n=t[r];for(var s=n.path.split("/"),a=e,l=1,u=s.length;;)if(R(a)){var c=parseInt(s[l],10);if(l++,l>=u){o=m[n.op].call(n,a,c,e);break}a=a[c]}else{var d=s[l];if(-1!=d.indexOf("~")&&(d=d.replace(/~1/g,"/").replace(/~0/g,"~")),l++,l>=u){o=g[n.op].call(n,a,d,e);break}a=a[d]}r++}return o}var g={add:function(e,t){return e[t]=this.value,!0},remove:function(e,t){return delete e[t],!0},replace:function(e,t){return e[t]=this.value,!0},move:function(e,t,n){var o={op:"_get",path:this.from};return p(n,[o]),p(n,[{op:"remove",path:this.from}]),p(n,[{op:"add",path:this.path,value:o.value}]),!0},copy:function(e,t,n){var o={op:"_get",path:this.from};return p(n,[o]),p(n,[{op:"add",path:this.path,value:o.value}]),!0},test:function(e,t){return JSON.stringify(e[t])===JSON.stringify(this.value)},_get:function(e,t){this.value=e[t]}},m={add:function(e,t){return e.splice(t,0,this.value),!0},remove:function(e,t){return e.splice(t,1),!0},replace:function(e,t){return e[t]=this.value,!0},move:g.move,copy:g.copy,test:g.test,_get:g._get},v={add:function(e,n){var o={op:"add",path:n+t(this.name),value:this.object[this.name]};e.push(o)},"delete":function(e,n){var o={op:"remove",path:n+t(this.name)};e.push(o)},update:function(e,n){var o={op:"replace",path:n+t(this.name),value:this.object[this.name]};e.push(o)}},w=[];e.intervals;var y=function(){function e(e){this.observers=[],this.obj=e}return e}(),b=function(){function e(e,t){this.callback=e,this.observer=t}return e}();e.unobserve=l,e.observe=u,e.generate=h;var C;C=Object.keys?Object.keys:function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t};var R;R=Array.isArray?Array.isArray:function(e){return e.push&&"number"==typeof e.length},e.apply=p}(o||(o={})),"undefined"!=typeof n&&(n.apply=o.apply,n.observe=o.observe,n.unobserve=o.unobserve,n.generate=o.generate)},{}],moment:[function(t,n,o){
//! moment.js
//! version : 2.10.6
//! authors : Tim Wood, Iskren Chernev, Moment.js contributors
//! license : MIT
//! momentjs.com
!function(t,r){"object"==typeof o&&"undefined"!=typeof n?n.exports=r():"function"==typeof e&&e.amd?e(r):t.moment=r()}(this,function(){"use strict";function e(){return Ln.apply(null,arguments)}function o(e){Ln=e}function r(e){return"[object Array]"===Object.prototype.toString.call(e)}function i(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function s(e,t){var n,o=[];for(n=0;n<e.length;++n)o.push(t(e[n],n));return o}function a(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function l(e,t){for(var n in t)a(t,n)&&(e[n]=t[n]);return a(t,"toString")&&(e.toString=t.toString),a(t,"valueOf")&&(e.valueOf=t.valueOf),e}function u(e,t,n,o){return He(e,t,n,o,!0).utc()}function c(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1}}function d(e){return null==e._pf&&(e._pf=c()),e._pf}function h(e){if(null==e._isValid){var t=d(e);e._isValid=!(isNaN(e._d.getTime())||!(t.overflow<0)||t.empty||t.invalidMonth||t.invalidWeekday||t.nullInput||t.invalidFormat||t.userInvalidated),e._strict&&(e._isValid=e._isValid&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour)}return e._isValid}function f(e){var t=u(NaN);return null!=e?l(d(t),e):d(t).userInvalidated=!0,t}function p(e,t){var n,o,r;if("undefined"!=typeof t._isAMomentObject&&(e._isAMomentObject=t._isAMomentObject),"undefined"!=typeof t._i&&(e._i=t._i),"undefined"!=typeof t._f&&(e._f=t._f),"undefined"!=typeof t._l&&(e._l=t._l),"undefined"!=typeof t._strict&&(e._strict=t._strict),"undefined"!=typeof t._tzm&&(e._tzm=t._tzm),"undefined"!=typeof t._isUTC&&(e._isUTC=t._isUTC),"undefined"!=typeof t._offset&&(e._offset=t._offset),"undefined"!=typeof t._pf&&(e._pf=d(t)),"undefined"!=typeof t._locale&&(e._locale=t._locale),In.length>0)for(n in In)o=In[n],r=t[o],"undefined"!=typeof r&&(e[o]=r);return e}function g(t){p(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),jn===!1&&(jn=!0,e.updateOffset(this),jn=!1)}function m(e){return e instanceof g||null!=e&&null!=e._isAMomentObject}function v(e){return 0>e?Math.ceil(e):Math.floor(e)}function w(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=v(t)),n}function y(e,t,n){var o,r=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),s=0;for(o=0;r>o;o++)(n&&e[o]!==t[o]||!n&&w(e[o])!==w(t[o]))&&s++;return s+i}function b(){}function C(e){return e?e.toLowerCase().replace("_","-"):e}function R(e){for(var t,n,o,r,i=0;i<e.length;){for(r=C(e[i]).split("-"),t=r.length,n=C(e[i+1]),n=n?n.split("-"):null;t>0;){if(o=_(r.slice(0,t).join("-")))return o;if(n&&n.length>=t&&y(r,n,!0)>=t-1)break;t--}i++}return null}function _(e){var o=null;if(!Bn[e]&&"undefined"!=typeof n&&n&&n.exports)try{o=Wn._abbr,t("./locale/"+e),S(o)}catch(r){}return Bn[e]}function S(e,t){var n;return e&&(n="undefined"==typeof t?T(e):E(e,t),n&&(Wn=n)),Wn._abbr}function E(e,t){return null!==t?(t.abbr=e,Bn[e]=Bn[e]||new b,Bn[e].set(t),S(e),Bn[e]):(delete Bn[e],null)}function T(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Wn;if(!r(e)){if(t=_(e))return t;e=[e]}return R(e)}function O(e,t){var n=e.toLowerCase();Fn[n]=Fn[n+"s"]=Fn[t]=e}function M(e){return"string"==typeof e?Fn[e]||Fn[e.toLowerCase()]:void 0}function k(e){var t,n,o={};for(n in e)a(e,n)&&(t=M(n),t&&(o[t]=e[n]));return o}function H(t,n){return function(o){return null!=o?(x(this,t,o),e.updateOffset(this,n),this):D(this,t)}}function D(e,t){return e._d["get"+(e._isUTC?"UTC":"")+t]()}function x(e,t,n){return e._d["set"+(e._isUTC?"UTC":"")+t](n)}function A(e,t){var n;if("object"==typeof e)for(n in e)this.set(n,e[n]);else if(e=M(e),"function"==typeof this[e])return this[e](t);return this}function P(e,t,n){var o=""+Math.abs(e),r=t-o.length,i=e>=0;return(i?n?"+":"":"-")+Math.pow(10,Math.max(0,r)).toString().substr(1)+o}function N(e,t,n,o){var r=o;"string"==typeof o&&(r=function(){return this[o]()}),e&&(Un[e]=r),t&&(Un[t[0]]=function(){return P(r.apply(this,arguments),t[1],t[2])}),n&&(Un[n]=function(){return this.localeData().ordinal(r.apply(this,arguments),e)})}function L(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function W(e){var t,n,o=e.match(Vn);for(t=0,n=o.length;n>t;t++)Un[o[t]]?o[t]=Un[o[t]]:o[t]=L(o[t]);return function(r){var i="";for(t=0;n>t;t++)i+=o[t]instanceof Function?o[t].call(r,e):o[t];return i}}function I(e,t){return e.isValid()?(t=j(t,e.localeData()),Yn[t]=Yn[t]||W(t),Yn[t](e)):e.localeData().invalidDate()}function j(e,t){function n(e){return t.longDateFormat(e)||e}var o=5;for(zn.lastIndex=0;o>=0&&zn.test(e);)e=e.replace(zn,n),zn.lastIndex=0,o-=1;return e}function B(e){return"function"==typeof e&&"[object Function]"===Object.prototype.toString.call(e)}function F(e,t,n){so[e]=B(t)?t:function(e){return e&&n?n:t}}function V(e,t){return a(so,e)?so[e](t._strict,t._locale):new RegExp(z(e))}function z(e){return e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,o,r){return t||n||o||r}).replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function Y(e,t){var n,o=t;for("string"==typeof e&&(e=[e]),"number"==typeof t&&(o=function(e,n){n[t]=w(e)}),n=0;n<e.length;n++)ao[e[n]]=o}function U(e,t){Y(e,function(e,n,o,r){o._w=o._w||{},t(e,o._w,o,r)})}function G(e,t,n){null!=t&&a(ao,e)&&ao[e](t,n._a,n,e)}function $(e,t){return new Date(Date.UTC(e,t+1,0)).getUTCDate()}function K(e){return this._months[e.month()]}function X(e){return this._monthsShort[e.month()]}function q(e,t,n){var o,r,i;for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),o=0;12>o;o++){if(r=u([2e3,o]),n&&!this._longMonthsParse[o]&&(this._longMonthsParse[o]=new RegExp("^"+this.months(r,"").replace(".","")+"$","i"),this._shortMonthsParse[o]=new RegExp("^"+this.monthsShort(r,"").replace(".","")+"$","i")),n||this._monthsParse[o]||(i="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[o]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[o].test(e))return o;if(n&&"MMM"===t&&this._shortMonthsParse[o].test(e))return o;if(!n&&this._monthsParse[o].test(e))return o}}function Z(e,t){var n;return"string"==typeof t&&(t=e.localeData().monthsParse(t),"number"!=typeof t)?e:(n=Math.min(e.date(),$(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n),e)}function J(t){return null!=t?(Z(this,t),e.updateOffset(this,!0),this):D(this,"Month")}function Q(){return $(this.year(),this.month())}function ee(e){var t,n=e._a;return n&&-2===d(e).overflow&&(t=n[uo]<0||n[uo]>11?uo:n[co]<1||n[co]>$(n[lo],n[uo])?co:n[ho]<0||n[ho]>24||24===n[ho]&&(0!==n[fo]||0!==n[po]||0!==n[go])?ho:n[fo]<0||n[fo]>59?fo:n[po]<0||n[po]>59?po:n[go]<0||n[go]>999?go:-1,d(e)._overflowDayOfYear&&(lo>t||t>co)&&(t=co),d(e).overflow=t),e}function te(t){e.suppressDeprecationWarnings===!1&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function ne(e,t){var n=!0;return l(function(){return n&&(te(e+"\n"+(new Error).stack),n=!1),t.apply(this,arguments)},t)}function oe(e,t){wo[e]||(te(t),wo[e]=!0)}function re(e){var t,n,o=e._i,r=yo.exec(o);if(r){for(d(e).iso=!0,t=0,n=bo.length;n>t;t++)if(bo[t][1].exec(o)){e._f=bo[t][0];break}for(t=0,n=Co.length;n>t;t++)if(Co[t][1].exec(o)){e._f+=(r[6]||" ")+Co[t][0];break}o.match(oo)&&(e._f+="Z"),_e(e)}else e._isValid=!1}function ie(t){var n=Ro.exec(t._i);return null!==n?void(t._d=new Date(+n[1])):(re(t),void(t._isValid===!1&&(delete t._isValid,e.createFromInputFallback(t))))}function se(e,t,n,o,r,i,s){var a=new Date(e,t,n,o,r,i,s);return 1970>e&&a.setFullYear(e),a}function ae(e){var t=new Date(Date.UTC.apply(null,arguments));return 1970>e&&t.setUTCFullYear(e),t}function le(e){return ue(e)?366:365}function ue(e){return e%4===0&&e%100!==0||e%400===0}function ce(){return ue(this.year())}function de(e,t,n){var o,r=n-t,i=n-e.day();return i>r&&(i-=7),r-7>i&&(i+=7),o=De(e).add(i,"d"),{week:Math.ceil(o.dayOfYear()/7),year:o.year()}}function he(e){return de(e,this._week.dow,this._week.doy).week}function fe(){return this._week.dow}function pe(){return this._week.doy}function ge(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")}function me(e){var t=de(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")}function ve(e,t,n,o,r){var i,s=6+r-o,a=ae(e,0,1+s),l=a.getUTCDay();return r>l&&(l+=7),n=null!=n?1*n:r,i=1+s+7*(t-1)-l+n,{year:i>0?e:e-1,dayOfYear:i>0?i:le(e-1)+i}}function we(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")}function ye(e,t,n){return null!=e?e:null!=t?t:n}function be(e){var t=new Date;return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function Ce(e){var t,n,o,r,i=[];if(!e._d){for(o=be(e),e._w&&null==e._a[co]&&null==e._a[uo]&&Re(e),e._dayOfYear&&(r=ye(e._a[lo],o[lo]),e._dayOfYear>le(r)&&(d(e)._overflowDayOfYear=!0),n=ae(r,0,e._dayOfYear),e._a[uo]=n.getUTCMonth(),e._a[co]=n.getUTCDate()),t=0;3>t&&null==e._a[t];++t)e._a[t]=i[t]=o[t];for(;7>t;t++)e._a[t]=i[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[ho]&&0===e._a[fo]&&0===e._a[po]&&0===e._a[go]&&(e._nextDay=!0,e._a[ho]=0),e._d=(e._useUTC?ae:se).apply(null,i),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[ho]=24)}}function Re(e){var t,n,o,r,i,s,a;t=e._w,null!=t.GG||null!=t.W||null!=t.E?(i=1,s=4,n=ye(t.GG,e._a[lo],de(De(),1,4).year),o=ye(t.W,1),r=ye(t.E,1)):(i=e._locale._week.dow,s=e._locale._week.doy,n=ye(t.gg,e._a[lo],de(De(),i,s).year),o=ye(t.w,1),null!=t.d?(r=t.d,i>r&&++o):r=null!=t.e?t.e+i:i),a=ve(n,o,r,s,i),e._a[lo]=a.year,e._dayOfYear=a.dayOfYear}function _e(t){if(t._f===e.ISO_8601)return void re(t);t._a=[],d(t).empty=!0;var n,o,r,i,s,a=""+t._i,l=a.length,u=0;for(r=j(t._f,t._locale).match(Vn)||[],n=0;n<r.length;n++)i=r[n],o=(a.match(V(i,t))||[])[0],o&&(s=a.substr(0,a.indexOf(o)),s.length>0&&d(t).unusedInput.push(s),a=a.slice(a.indexOf(o)+o.length),u+=o.length),Un[i]?(o?d(t).empty=!1:d(t).unusedTokens.push(i),G(i,o,t)):t._strict&&!o&&d(t).unusedTokens.push(i);d(t).charsLeftOver=l-u,a.length>0&&d(t).unusedInput.push(a),d(t).bigHour===!0&&t._a[ho]<=12&&t._a[ho]>0&&(d(t).bigHour=void 0),t._a[ho]=Se(t._locale,t._a[ho],t._meridiem),Ce(t),ee(t)}function Se(e,t,n){var o;return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?(o=e.isPM(n),o&&12>t&&(t+=12),o||12!==t||(t=0),t):t}function Ee(e){var t,n,o,r,i;if(0===e._f.length)return d(e).invalidFormat=!0,void(e._d=new Date(NaN));for(r=0;r<e._f.length;r++)i=0,t=p({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[r],_e(t),h(t)&&(i+=d(t).charsLeftOver,i+=10*d(t).unusedTokens.length,d(t).score=i,(null==o||o>i)&&(o=i,n=t));l(e,n||t)}function Te(e){if(!e._d){var t=k(e._i);e._a=[t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],Ce(e)}}function Oe(e){var t=new g(ee(Me(e)));return t._nextDay&&(t.add(1,"d"),t._nextDay=void 0),t}function Me(e){var t=e._i,n=e._f;return e._locale=e._locale||T(e._l),null===t||void 0===n&&""===t?f({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),m(t)?new g(ee(t)):(r(n)?Ee(e):n?_e(e):i(t)?e._d=t:ke(e),e))}function ke(t){var n=t._i;void 0===n?t._d=new Date:i(n)?t._d=new Date(+n):"string"==typeof n?ie(t):r(n)?(t._a=s(n.slice(0),function(e){return parseInt(e,10)}),Ce(t)):"object"==typeof n?Te(t):"number"==typeof n?t._d=new Date(n):e.createFromInputFallback(t)}function He(e,t,n,o,r){var i={};return"boolean"==typeof n&&(o=n,n=void 0),i._isAMomentObject=!0,i._useUTC=i._isUTC=r,i._l=n,i._i=e,i._f=t,i._strict=o,Oe(i)}function De(e,t,n,o){return He(e,t,n,o,!1)}function xe(e,t){var n,o;if(1===t.length&&r(t[0])&&(t=t[0]),!t.length)return De();for(n=t[0],o=1;o<t.length;++o)(!t[o].isValid()||t[o][e](n))&&(n=t[o]);return n}function Ae(){var e=[].slice.call(arguments,0);return xe("isBefore",e)}function Pe(){var e=[].slice.call(arguments,0);return xe("isAfter",e)}function Ne(e){var t=k(e),n=t.year||0,o=t.quarter||0,r=t.month||0,i=t.week||0,s=t.day||0,a=t.hour||0,l=t.minute||0,u=t.second||0,c=t.millisecond||0;this._milliseconds=+c+1e3*u+6e4*l+36e5*a,this._days=+s+7*i,this._months=+r+3*o+12*n,this._data={},this._locale=T(),this._bubble()}function Le(e){return e instanceof Ne}function We(e,t){N(e,0,0,function(){var e=this.utcOffset(),n="+";return 0>e&&(e=-e,n="-"),n+P(~~(e/60),2)+t+P(~~e%60,2)})}function Ie(e){var t=(e||"").match(oo)||[],n=t[t.length-1]||[],o=(n+"").match(Oo)||["-",0,0],r=+(60*o[1])+w(o[2]);return"+"===o[0]?r:-r}function je(t,n){var o,r;return n._isUTC?(o=n.clone(),r=(m(t)||i(t)?+t:+De(t))-+o,o._d.setTime(+o._d+r),e.updateOffset(o,!1),o):De(t).local()}function Be(e){return 15*-Math.round(e._d.getTimezoneOffset()/15)}function Fe(t,n){var o,r=this._offset||0;return null!=t?("string"==typeof t&&(t=Ie(t)),Math.abs(t)<16&&(t=60*t),!this._isUTC&&n&&(o=Be(this)),this._offset=t,this._isUTC=!0,null!=o&&this.add(o,"m"),r!==t&&(!n||this._changeInProgress?ot(this,Je(t-r,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,e.updateOffset(this,!0),this._changeInProgress=null)),this):this._isUTC?r:Be(this)}function Ve(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()}function ze(e){return this.utcOffset(0,e)}function Ye(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Be(this),"m")),this}function Ue(){return this._tzm?this.utcOffset(this._tzm):"string"==typeof this._i&&this.utcOffset(Ie(this._i)),this}function Ge(e){return e=e?De(e).utcOffset():0,(this.utcOffset()-e)%60===0}function $e(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Ke(){if("undefined"!=typeof this._isDSTShifted)return this._isDSTShifted;var e={};if(p(e,this),e=Me(e),e._a){var t=e._isUTC?u(e._a):De(e._a);this._isDSTShifted=this.isValid()&&y(e._a,t.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted}function Xe(){return!this._isUTC}function qe(){return this._isUTC}function Ze(){return this._isUTC&&0===this._offset}function Je(e,t){var n,o,r,i=e,s=null;return Le(e)?i={ms:e._milliseconds,d:e._days,M:e._months}:"number"==typeof e?(i={},t?i[t]=e:i.milliseconds=e):(s=Mo.exec(e))?(n="-"===s[1]?-1:1,i={y:0,d:w(s[co])*n,h:w(s[ho])*n,m:w(s[fo])*n,s:w(s[po])*n,ms:w(s[go])*n}):(s=ko.exec(e))?(n="-"===s[1]?-1:1,i={y:Qe(s[2],n),M:Qe(s[3],n),d:Qe(s[4],n),h:Qe(s[5],n),m:Qe(s[6],n),s:Qe(s[7],n),w:Qe(s[8],n)}):null==i?i={}:"object"==typeof i&&("from"in i||"to"in i)&&(r=tt(De(i.from),De(i.to)),i={},i.ms=r.milliseconds,i.M=r.months),o=new Ne(i),Le(e)&&a(e,"_locale")&&(o._locale=e._locale),o}function Qe(e,t){var n=e&&parseFloat(e.replace(",","."));return(isNaN(n)?0:n)*t}function et(e,t){var n={milliseconds:0,months:0};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function tt(e,t){var n;return t=je(t,e),e.isBefore(t)?n=et(e,t):(n=et(t,e),n.milliseconds=-n.milliseconds,n.months=-n.months),n}function nt(e,t){return function(n,o){var r,i;return null===o||isNaN(+o)||(oe(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period)."),i=n,n=o,o=i),n="string"==typeof n?+n:n,r=Je(n,o),ot(this,r,e),this}}function ot(t,n,o,r){var i=n._milliseconds,s=n._days,a=n._months;r=null==r?!0:r,i&&t._d.setTime(+t._d+i*o),s&&x(t,"Date",D(t,"Date")+s*o),a&&Z(t,D(t,"Month")+a*o),r&&e.updateOffset(t,s||a)}function rt(e,t){var n=e||De(),o=je(n,this).startOf("day"),r=this.diff(o,"days",!0),i=-6>r?"sameElse":-1>r?"lastWeek":0>r?"lastDay":1>r?"sameDay":2>r?"nextDay":7>r?"nextWeek":"sameElse";return this.format(t&&t[i]||this.localeData().calendar(i,this,De(n)))}function it(){return new g(this)}function st(e,t){var n;return t=M("undefined"!=typeof t?t:"millisecond"),"millisecond"===t?(e=m(e)?e:De(e),+this>+e):(n=m(e)?+e:+De(e),n<+this.clone().startOf(t))}function at(e,t){var n;return t=M("undefined"!=typeof t?t:"millisecond"),"millisecond"===t?(e=m(e)?e:De(e),+e>+this):(n=m(e)?+e:+De(e),+this.clone().endOf(t)<n)}function lt(e,t,n){return this.isAfter(e,n)&&this.isBefore(t,n)}function ut(e,t){var n;return t=M(t||"millisecond"),"millisecond"===t?(e=m(e)?e:De(e),+this===+e):(n=+De(e),+this.clone().startOf(t)<=n&&n<=+this.clone().endOf(t))}function ct(e,t,n){var o,r,i=je(e,this),s=6e4*(i.utcOffset()-this.utcOffset());return t=M(t),"year"===t||"month"===t||"quarter"===t?(r=dt(this,i),"quarter"===t?r/=3:"year"===t&&(r/=12)):(o=this-i,r="second"===t?o/1e3:"minute"===t?o/6e4:"hour"===t?o/36e5:"day"===t?(o-s)/864e5:"week"===t?(o-s)/6048e5:o),n?r:v(r)}function dt(e,t){var n,o,r=12*(t.year()-e.year())+(t.month()-e.month()),i=e.clone().add(r,"months");return 0>t-i?(n=e.clone().add(r-1,"months"),o=(t-i)/(i-n)):(n=e.clone().add(r+1,"months"),o=(t-i)/(n-i)),-(r+o)}function ht(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function ft(){var e=this.clone().utc();return 0<e.year()&&e.year()<=9999?"function"==typeof Date.prototype.toISOString?this.toDate().toISOString():I(e,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):I(e,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")}function pt(t){var n=I(this,t||e.defaultFormat);return this.localeData().postformat(n)}function gt(e,t){return this.isValid()?Je({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function mt(e){return this.from(De(),e)}function vt(e,t){return this.isValid()?Je({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function wt(e){return this.to(De(),e)}function yt(e){var t;return void 0===e?this._locale._abbr:(t=T(e),null!=t&&(this._locale=t),this)}function bt(){return this._locale}function Ct(e){switch(e=M(e)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e&&this.weekday(0),"isoWeek"===e&&this.isoWeekday(1),"quarter"===e&&this.month(3*Math.floor(this.month()/3)),this}function Rt(e){return e=M(e),void 0===e||"millisecond"===e?this:this.startOf(e).add(1,"isoWeek"===e?"week":e).subtract(1,"ms")}function _t(){return+this._d-6e4*(this._offset||0)}function St(){return Math.floor(+this/1e3)}function Et(){return this._offset?new Date(+this):this._d}function Tt(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]}function Ot(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}}function Mt(){return h(this)}function kt(){return l({},d(this))}function Ht(){return d(this).overflow}function Dt(e,t){N(0,[e,e.length],0,t)}function xt(e,t,n){return de(De([e,11,31+t-n]),t,n).week}function At(e){var t=de(this,this.localeData()._week.dow,this.localeData()._week.doy).year;return null==e?t:this.add(e-t,"y")}function Pt(e){var t=de(this,1,4).year;return null==e?t:this.add(e-t,"y")}function Nt(){return xt(this.year(),1,4)}function Lt(){var e=this.localeData()._week;return xt(this.year(),e.dow,e.doy)}function Wt(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)}function It(e,t){return"string"!=typeof e?e:isNaN(e)?(e=t.weekdaysParse(e),"number"==typeof e?e:null):parseInt(e,10)}function jt(e){return this._weekdays[e.day()]}function Bt(e){return this._weekdaysShort[e.day()]}function Ft(e){return this._weekdaysMin[e.day()]}function Vt(e){var t,n,o;for(this._weekdaysParse=this._weekdaysParse||[],t=0;7>t;t++)if(this._weekdaysParse[t]||(n=De([2e3,1]).day(t),o="^"+this.weekdays(n,"")+"|^"+this.weekdaysShort(n,"")+"|^"+this.weekdaysMin(n,""),this._weekdaysParse[t]=new RegExp(o.replace(".",""),"i")),this._weekdaysParse[t].test(e))return t}function zt(e){var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=It(e,this.localeData()),this.add(e-t,"d")):t}function Yt(e){var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")}function Ut(e){return null==e?this.day()||7:this.day(this.day()%7?e:e-7)}function Gt(e,t){N(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function $t(e,t){return t._meridiemParse}function Kt(e){return"p"===(e+"").toLowerCase().charAt(0)}function Xt(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"}function qt(e,t){t[go]=w(1e3*("0."+e))}function Zt(){return this._isUTC?"UTC":""}function Jt(){return this._isUTC?"Coordinated Universal Time":""}function Qt(e){return De(1e3*e)}function en(){return De.apply(null,arguments).parseZone()}function tn(e,t,n){var o=this._calendar[e];return"function"==typeof o?o.call(t,n):o}function nn(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e])}function on(){return this._invalidDate}function rn(e){return this._ordinal.replace("%d",e)}function sn(e){return e}function an(e,t,n,o){var r=this._relativeTime[n];return"function"==typeof r?r(e,t,n,o):r.replace(/%d/i,e)}function ln(e,t){var n=this._relativeTime[e>0?"future":"past"];return"function"==typeof n?n(t):n.replace(/%s/i,t)}function un(e){var t,n;for(n in e)t=e[n],"function"==typeof t?this[n]=t:this["_"+n]=t;this._ordinalParseLenient=new RegExp(this._ordinalParse.source+"|"+/\d{1,2}/.source)}function cn(e,t,n,o){var r=T(),i=u().set(o,t);return r[n](i,e)}function dn(e,t,n,o,r){if("number"==typeof e&&(t=e,e=void 0),e=e||"",null!=t)return cn(e,t,n,r);var i,s=[];for(i=0;o>i;i++)s[i]=cn(e,i,n,r);return s}function hn(e,t){return dn(e,t,"months",12,"month")}function fn(e,t){return dn(e,t,"monthsShort",12,"month")}function pn(e,t){return dn(e,t,"weekdays",7,"day")}function gn(e,t){return dn(e,t,"weekdaysShort",7,"day")}function mn(e,t){return dn(e,t,"weekdaysMin",7,"day")}function vn(){var e=this._data;return this._milliseconds=Jo(this._milliseconds),this._days=Jo(this._days),this._months=Jo(this._months),e.milliseconds=Jo(e.milliseconds),e.seconds=Jo(e.seconds),e.minutes=Jo(e.minutes),e.hours=Jo(e.hours),e.months=Jo(e.months),e.years=Jo(e.years),this}function wn(e,t,n,o){var r=Je(t,n);return e._milliseconds+=o*r._milliseconds,e._days+=o*r._days,e._months+=o*r._months,e._bubble()}function yn(e,t){return wn(this,e,t,1)}function bn(e,t){return wn(this,e,t,-1)}function Cn(e){return 0>e?Math.floor(e):Math.ceil(e)}function Rn(){var e,t,n,o,r,i=this._milliseconds,s=this._days,a=this._months,l=this._data;return i>=0&&s>=0&&a>=0||0>=i&&0>=s&&0>=a||(i+=864e5*Cn(Sn(a)+s),s=0,a=0),l.milliseconds=i%1e3,e=v(i/1e3),l.seconds=e%60,t=v(e/60),l.minutes=t%60,n=v(t/60),l.hours=n%24,s+=v(n/24),r=v(_n(s)),a+=r,s-=Cn(Sn(r)),o=v(a/12),a%=12,l.days=s,l.months=a,l.years=o,this}function _n(e){return 4800*e/146097}function Sn(e){return 146097*e/4800}function En(e){var t,n,o=this._milliseconds;if(e=M(e),"month"===e||"year"===e)return t=this._days+o/864e5,n=this._months+_n(t),"month"===e?n:n/12;switch(t=this._days+Math.round(Sn(this._months)),e){case"week":return t/7+o/6048e5;case"day":return t+o/864e5;case"hour":return 24*t+o/36e5;case"minute":return 1440*t+o/6e4;case"second":return 86400*t+o/1e3;case"millisecond":return Math.floor(864e5*t)+o;default:throw new Error("Unknown unit "+e)}}function Tn(){return this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*w(this._months/12)}function On(e){return function(){return this.as(e)}}function Mn(e){return e=M(e),this[e+"s"]()}function kn(e){return function(){return this._data[e]}}function Hn(){return v(this.days()/7)}function Dn(e,t,n,o,r){return r.relativeTime(t||1,!!n,e,o)}function xn(e,t,n){var o=Je(e).abs(),r=pr(o.as("s")),i=pr(o.as("m")),s=pr(o.as("h")),a=pr(o.as("d")),l=pr(o.as("M")),u=pr(o.as("y")),c=r<gr.s&&["s",r]||1===i&&["m"]||i<gr.m&&["mm",i]||1===s&&["h"]||s<gr.h&&["hh",s]||1===a&&["d"]||a<gr.d&&["dd",a]||1===l&&["M"]||l<gr.M&&["MM",l]||1===u&&["y"]||["yy",u];return c[2]=t,c[3]=+e>0,c[4]=n,Dn.apply(null,c)}function An(e,t){return void 0===gr[e]?!1:void 0===t?gr[e]:(gr[e]=t,!0)}function Pn(e){var t=this.localeData(),n=xn(this,!e,t);return e&&(n=t.pastFuture(+this,n)),t.postformat(n)}function Nn(){var e,t,n,o=mr(this._milliseconds)/1e3,r=mr(this._days),i=mr(this._months);e=v(o/60),t=v(e/60),o%=60,e%=60,n=v(i/12),i%=12;var s=n,a=i,l=r,u=t,c=e,d=o,h=this.asSeconds();return h?(0>h?"-":"")+"P"+(s?s+"Y":"")+(a?a+"M":"")+(l?l+"D":"")+(u||c||d?"T":"")+(u?u+"H":"")+(c?c+"M":"")+(d?d+"S":""):"P0D"}var Ln,Wn,In=e.momentProperties=[],jn=!1,Bn={},Fn={},Vn=/(\[[^\[]*\])|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Q|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,zn=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,Yn={},Un={},Gn=/\d/,$n=/\d\d/,Kn=/\d{3}/,Xn=/\d{4}/,qn=/[+-]?\d{6}/,Zn=/\d\d?/,Jn=/\d{1,3}/,Qn=/\d{1,4}/,eo=/[+-]?\d{1,6}/,to=/\d+/,no=/[+-]?\d+/,oo=/Z|[+-]\d\d:?\d\d/gi,ro=/[+-]?\d+(\.\d{1,3})?/,io=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,so={},ao={},lo=0,uo=1,co=2,ho=3,fo=4,po=5,go=6;N("M",["MM",2],"Mo",function(){return this.month()+1}),N("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),N("MMMM",0,0,function(e){return this.localeData().months(this,e)}),O("month","M"),F("M",Zn),F("MM",Zn,$n),F("MMM",io),F("MMMM",io),Y(["M","MM"],function(e,t){t[uo]=w(e)-1}),Y(["MMM","MMMM"],function(e,t,n,o){var r=n._locale.monthsParse(e,o,n._strict);null!=r?t[uo]=r:d(n).invalidMonth=e});var mo="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),vo="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),wo={};e.suppressDeprecationWarnings=!1;var yo=/^\s*(?:[+-]\d{6}|\d{4})-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d+)?)?)?)?([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,bo=[["YYYYYY-MM-DD",/[+-]\d{6}-\d{2}-\d{2}/],["YYYY-MM-DD",/\d{4}-\d{2}-\d{2}/],["GGGG-[W]WW-E",/\d{4}-W\d{2}-\d/],["GGGG-[W]WW",/\d{4}-W\d{2}/],["YYYY-DDD",/\d{4}-\d{3}/]],Co=[["HH:mm:ss.SSSS",/(T| )\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss",/(T| )\d\d:\d\d:\d\d/],["HH:mm",/(T| )\d\d:\d\d/],["HH",/(T| )\d\d/]],Ro=/^\/?Date\((\-?\d+)/i;e.createFromInputFallback=ne("moment construction falls back to js Date. This is discouraged and will be removed in upcoming major release. Please refer to https://github.com/moment/moment/issues/1407 for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),N(0,["YY",2],0,function(){return this.year()%100}),N(0,["YYYY",4],0,"year"),N(0,["YYYYY",5],0,"year"),N(0,["YYYYYY",6,!0],0,"year"),O("year","y"),F("Y",no),F("YY",Zn,$n),F("YYYY",Qn,Xn),F("YYYYY",eo,qn),F("YYYYYY",eo,qn),Y(["YYYYY","YYYYYY"],lo),Y("YYYY",function(t,n){n[lo]=2===t.length?e.parseTwoDigitYear(t):w(t)}),Y("YY",function(t,n){n[lo]=e.parseTwoDigitYear(t)}),e.parseTwoDigitYear=function(e){return w(e)+(w(e)>68?1900:2e3)};var _o=H("FullYear",!1);N("w",["ww",2],"wo","week"),N("W",["WW",2],"Wo","isoWeek"),O("week","w"),O("isoWeek","W"),F("w",Zn),F("ww",Zn,$n),F("W",Zn),F("WW",Zn,$n),U(["w","ww","W","WW"],function(e,t,n,o){t[o.substr(0,1)]=w(e)});var So={dow:0,doy:6};N("DDD",["DDDD",3],"DDDo","dayOfYear"),O("dayOfYear","DDD"),F("DDD",Jn),F("DDDD",Kn),Y(["DDD","DDDD"],function(e,t,n){n._dayOfYear=w(e)}),e.ISO_8601=function(){};var Eo=ne("moment().min is deprecated, use moment.min instead. https://github.com/moment/moment/issues/1548",function(){var e=De.apply(null,arguments);return this>e?this:e}),To=ne("moment().max is deprecated, use moment.max instead. https://github.com/moment/moment/issues/1548",function(){var e=De.apply(null,arguments);return e>this?this:e});We("Z",":"),We("ZZ",""),F("Z",oo),F("ZZ",oo),Y(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Ie(e)});var Oo=/([\+\-]|\d\d)/gi;e.updateOffset=function(){};var Mo=/(\-)?(?:(\d*)\.)?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?)?/,ko=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/;Je.fn=Ne.prototype;var Ho=nt(1,"add"),Do=nt(-1,"subtract");e.defaultFormat="YYYY-MM-DDTHH:mm:ssZ";var xo=ne("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});N(0,["gg",2],0,function(){return this.weekYear()%100}),N(0,["GG",2],0,function(){return this.isoWeekYear()%100}),Dt("gggg","weekYear"),Dt("ggggg","weekYear"),Dt("GGGG","isoWeekYear"),Dt("GGGGG","isoWeekYear"),O("weekYear","gg"),O("isoWeekYear","GG"),F("G",no),F("g",no),F("GG",Zn,$n),F("gg",Zn,$n),F("GGGG",Qn,Xn),F("gggg",Qn,Xn),F("GGGGG",eo,qn),F("ggggg",eo,qn),U(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,o){t[o.substr(0,2)]=w(e)}),U(["gg","GG"],function(t,n,o,r){n[r]=e.parseTwoDigitYear(t)}),N("Q",0,0,"quarter"),O("quarter","Q"),F("Q",Gn),Y("Q",function(e,t){t[uo]=3*(w(e)-1)}),N("D",["DD",2],"Do","date"),O("date","D"),F("D",Zn),F("DD",Zn,$n),F("Do",function(e,t){return e?t._ordinalParse:t._ordinalParseLenient}),Y(["D","DD"],co),Y("Do",function(e,t){t[co]=w(e.match(Zn)[0],10)});var Ao=H("Date",!0);N("d",0,"do","day"),N("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),N("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),N("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),N("e",0,0,"weekday"),N("E",0,0,"isoWeekday"),O("day","d"),O("weekday","e"),O("isoWeekday","E"),F("d",Zn),F("e",Zn),F("E",Zn),F("dd",io),F("ddd",io),F("dddd",io),U(["dd","ddd","dddd"],function(e,t,n){var o=n._locale.weekdaysParse(e);null!=o?t.d=o:d(n).invalidWeekday=e}),U(["d","e","E"],function(e,t,n,o){t[o]=w(e)});var Po="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),No="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Lo="Su_Mo_Tu_We_Th_Fr_Sa".split("_");N("H",["HH",2],0,"hour"),N("h",["hh",2],0,function(){return this.hours()%12||12}),Gt("a",!0),Gt("A",!1),O("hour","h"),F("a",$t),F("A",$t),F("H",Zn),F("h",Zn),F("HH",Zn,$n),F("hh",Zn,$n),Y(["H","HH"],ho),Y(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),Y(["h","hh"],function(e,t,n){t[ho]=w(e),d(n).bigHour=!0});var Wo=/[ap]\.?m?\.?/i,Io=H("Hours",!0);N("m",["mm",2],0,"minute"),O("minute","m"),F("m",Zn),F("mm",Zn,$n),Y(["m","mm"],fo);var jo=H("Minutes",!1);N("s",["ss",2],0,"second"),O("second","s"),F("s",Zn),F("ss",Zn,$n),Y(["s","ss"],po);var Bo=H("Seconds",!1);N("S",0,0,function(){return~~(this.millisecond()/100)}),N(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),N(0,["SSS",3],0,"millisecond"),N(0,["SSSS",4],0,function(){return 10*this.millisecond()}),N(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),N(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),N(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),N(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),N(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),O("millisecond","ms"),F("S",Jn,Gn),F("SS",Jn,$n),F("SSS",Jn,Kn);var Fo;for(Fo="SSSS";Fo.length<=9;Fo+="S")F(Fo,to);for(Fo="S";Fo.length<=9;Fo+="S")Y(Fo,qt);var Vo=H("Milliseconds",!1);N("z",0,0,"zoneAbbr"),N("zz",0,0,"zoneName");var zo=g.prototype;zo.add=Ho,zo.calendar=rt,zo.clone=it,zo.diff=ct,zo.endOf=Rt,zo.format=pt,zo.from=gt,zo.fromNow=mt,zo.to=vt,zo.toNow=wt,zo.get=A,zo.invalidAt=Ht,zo.isAfter=st,zo.isBefore=at,zo.isBetween=lt,zo.isSame=ut,zo.isValid=Mt,zo.lang=xo,zo.locale=yt,zo.localeData=bt,zo.max=To,zo.min=Eo,zo.parsingFlags=kt,zo.set=A,zo.startOf=Ct,zo.subtract=Do,zo.toArray=Tt,zo.toObject=Ot,zo.toDate=Et,zo.toISOString=ft,zo.toJSON=ft,zo.toString=ht,zo.unix=St,zo.valueOf=_t,zo.year=_o,zo.isLeapYear=ce,zo.weekYear=At,zo.isoWeekYear=Pt,zo.quarter=zo.quarters=Wt,zo.month=J,zo.daysInMonth=Q,zo.week=zo.weeks=ge,zo.isoWeek=zo.isoWeeks=me,zo.weeksInYear=Lt,zo.isoWeeksInYear=Nt,zo.date=Ao,zo.day=zo.days=zt,zo.weekday=Yt,zo.isoWeekday=Ut,zo.dayOfYear=we,zo.hour=zo.hours=Io,zo.minute=zo.minutes=jo,zo.second=zo.seconds=Bo,zo.millisecond=zo.milliseconds=Vo,zo.utcOffset=Fe,
zo.utc=ze,zo.local=Ye,zo.parseZone=Ue,zo.hasAlignedHourOffset=Ge,zo.isDST=$e,zo.isDSTShifted=Ke,zo.isLocal=Xe,zo.isUtcOffset=qe,zo.isUtc=Ze,zo.isUTC=Ze,zo.zoneAbbr=Zt,zo.zoneName=Jt,zo.dates=ne("dates accessor is deprecated. Use date instead.",Ao),zo.months=ne("months accessor is deprecated. Use month instead",J),zo.years=ne("years accessor is deprecated. Use year instead",_o),zo.zone=ne("moment().zone is deprecated, use moment().utcOffset instead. https://github.com/moment/moment/issues/1779",Ve);var Yo=zo,Uo={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},Go={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},$o="Invalid date",Ko="%d",Xo=/\d{1,2}/,qo={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},Zo=b.prototype;Zo._calendar=Uo,Zo.calendar=tn,Zo._longDateFormat=Go,Zo.longDateFormat=nn,Zo._invalidDate=$o,Zo.invalidDate=on,Zo._ordinal=Ko,Zo.ordinal=rn,Zo._ordinalParse=Xo,Zo.preparse=sn,Zo.postformat=sn,Zo._relativeTime=qo,Zo.relativeTime=an,Zo.pastFuture=ln,Zo.set=un,Zo.months=K,Zo._months=mo,Zo.monthsShort=X,Zo._monthsShort=vo,Zo.monthsParse=q,Zo.week=he,Zo._week=So,Zo.firstDayOfYear=pe,Zo.firstDayOfWeek=fe,Zo.weekdays=jt,Zo._weekdays=Po,Zo.weekdaysMin=Ft,Zo._weekdaysMin=Lo,Zo.weekdaysShort=Bt,Zo._weekdaysShort=No,Zo.weekdaysParse=Vt,Zo.isPM=Kt,Zo._meridiemParse=Wo,Zo.meridiem=Xt,S("en",{ordinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,n=1===w(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}}),e.lang=ne("moment.lang is deprecated. Use moment.locale instead.",S),e.langData=ne("moment.langData is deprecated. Use moment.localeData instead.",T);var Jo=Math.abs,Qo=On("ms"),er=On("s"),tr=On("m"),nr=On("h"),or=On("d"),rr=On("w"),ir=On("M"),sr=On("y"),ar=kn("milliseconds"),lr=kn("seconds"),ur=kn("minutes"),cr=kn("hours"),dr=kn("days"),hr=kn("months"),fr=kn("years"),pr=Math.round,gr={s:45,m:45,h:22,d:26,M:11},mr=Math.abs,vr=Ne.prototype;vr.abs=vn,vr.add=yn,vr.subtract=bn,vr.as=En,vr.asMilliseconds=Qo,vr.asSeconds=er,vr.asMinutes=tr,vr.asHours=nr,vr.asDays=or,vr.asWeeks=rr,vr.asMonths=ir,vr.asYears=sr,vr.valueOf=Tn,vr._bubble=Rn,vr.get=Mn,vr.milliseconds=ar,vr.seconds=lr,vr.minutes=ur,vr.hours=cr,vr.days=dr,vr.weeks=Hn,vr.months=hr,vr.years=fr,vr.humanize=Pn,vr.toISOString=Nn,vr.toString=Nn,vr.toJSON=Nn,vr.locale=yt,vr.localeData=bt,vr.toIsoString=ne("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Nn),vr.lang=xo,N("X",0,0,"unix"),N("x",0,0,"valueOf"),F("x",no),F("X",ro),Y("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e,10))}),Y("x",function(e,t,n){n._d=new Date(w(e))}),e.version="2.10.6",o(De),e.fn=Yo,e.min=Ae,e.max=Pe,e.utc=u,e.unix=Qt,e.months=hn,e.isDate=i,e.locale=S,e.invalid=f,e.duration=Je,e.isMoment=m,e.weekdays=pn,e.parseZone=en,e.localeData=T,e.isDuration=Le,e.monthsShort=fn,e.weekdaysMin=mn,e.defineLocale=E,e.weekdaysShort=gn,e.normalizeUnits=M,e.relativeTimeThreshold=An;var wr=e;return wr})},{}],numeral:[function(t,n,o){"use strict";(function(){function t(e){this._value=e}function o(e,t,n,o){var r,i,s=Math.pow(10,t);return i=(n(e*s)/s).toFixed(t),o&&(r=new RegExp("0{1,"+o+"}$"),i=i.replace(r,"")),i}function r(e,t,n){var o;return o=t.indexOf("$")>-1?s(e,t,n):t.indexOf("%")>-1?a(e,t,n):t.indexOf(":")>-1?l(e,t):c(e._value,t,n)}function i(e,t){var n,o,r,i,s,a=t,l=["KB","MB","GB","TB","PB","EB","ZB","YB"],c=!1;if(t.indexOf(":")>-1)e._value=u(t);else if(t===w)e._value=0;else{for("."!==m[v].delimiters.decimal&&(t=t.replace(/\./g,"").replace(m[v].delimiters.decimal,".")),n=new RegExp("[^a-zA-Z]"+m[v].abbreviations.thousand+"(?:\\)|(\\"+m[v].currency.symbol+")?(?:\\))?)?$"),o=new RegExp("[^a-zA-Z]"+m[v].abbreviations.million+"(?:\\)|(\\"+m[v].currency.symbol+")?(?:\\))?)?$"),r=new RegExp("[^a-zA-Z]"+m[v].abbreviations.billion+"(?:\\)|(\\"+m[v].currency.symbol+")?(?:\\))?)?$"),i=new RegExp("[^a-zA-Z]"+m[v].abbreviations.trillion+"(?:\\)|(\\"+m[v].currency.symbol+")?(?:\\))?)?$"),s=0;s<=l.length&&!(c=t.indexOf(l[s])>-1?Math.pow(1024,s+1):!1);s++);e._value=(c?c:1)*(a.match(n)?Math.pow(10,3):1)*(a.match(o)?Math.pow(10,6):1)*(a.match(r)?Math.pow(10,9):1)*(a.match(i)?Math.pow(10,12):1)*(t.indexOf("%")>-1?.01:1)*((t.split("-").length+Math.min(t.split("(").length-1,t.split(")").length-1))%2?1:-1)*Number(t.replace(/[^0-9\.]+/g,"")),e._value=c?Math.ceil(e._value):e._value}return e._value}function s(e,t,n){var o,r,i=t.indexOf("$"),s=t.indexOf("("),a=t.indexOf("-"),l="";return t.indexOf(" $")>-1?(l=" ",t=t.replace(" $","")):t.indexOf("$ ")>-1?(l=" ",t=t.replace("$ ","")):t=t.replace("$",""),r=c(e._value,t,n),1>=i?r.indexOf("(")>-1||r.indexOf("-")>-1?(r=r.split(""),o=1,(s>i||a>i)&&(o=0),r.splice(o,0,m[v].currency.symbol+l),r=r.join("")):r=m[v].currency.symbol+l+r:r.indexOf(")")>-1?(r=r.split(""),r.splice(-1,0,l+m[v].currency.symbol),r=r.join("")):r=r+l+m[v].currency.symbol,r}function a(e,t,n){var o,r="",i=100*e._value;return t.indexOf(" %")>-1?(r=" ",t=t.replace(" %","")):t=t.replace("%",""),o=c(i,t,n),o.indexOf(")")>-1?(o=o.split(""),o.splice(-1,0,r+"%"),o=o.join("")):o=o+r+"%",o}function l(e){var t=Math.floor(e._value/60/60),n=Math.floor((e._value-60*t*60)/60),o=Math.round(e._value-60*t*60-60*n);return t+":"+(10>n?"0"+n:n)+":"+(10>o?"0"+o:o)}function u(e){var t=e.split(":"),n=0;return 3===t.length?(n+=60*Number(t[0])*60,n+=60*Number(t[1]),n+=Number(t[2])):2===t.length&&(n+=60*Number(t[0]),n+=Number(t[1])),Number(n)}function c(e,t,n){var r,i,s,a,l,u,c=!1,d=!1,h=!1,f="",p=!1,g=!1,y=!1,b=!1,C=!1,R="",_="",S=Math.abs(e),E=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],T="",O=!1;if(0===e&&null!==w)return w;if(t.indexOf("(")>-1?(c=!0,t=t.slice(1,-1)):t.indexOf("+")>-1&&(d=!0,t=t.replace(/\+/g,"")),t.indexOf("a")>-1&&(p=t.indexOf("aK")>=0,g=t.indexOf("aM")>=0,y=t.indexOf("aB")>=0,b=t.indexOf("aT")>=0,C=p||g||y||b,t.indexOf(" a")>-1?(f=" ",t=t.replace(" a","")):t=t.replace("a",""),S>=Math.pow(10,12)&&!C||b?(f+=m[v].abbreviations.trillion,e/=Math.pow(10,12)):S<Math.pow(10,12)&&S>=Math.pow(10,9)&&!C||y?(f+=m[v].abbreviations.billion,e/=Math.pow(10,9)):S<Math.pow(10,9)&&S>=Math.pow(10,6)&&!C||g?(f+=m[v].abbreviations.million,e/=Math.pow(10,6)):(S<Math.pow(10,6)&&S>=Math.pow(10,3)&&!C||p)&&(f+=m[v].abbreviations.thousand,e/=Math.pow(10,3))),t.indexOf("b")>-1)for(t.indexOf(" b")>-1?(R=" ",t=t.replace(" b","")):t=t.replace("b",""),s=0;s<=E.length;s++)if(r=Math.pow(1024,s),i=Math.pow(1024,s+1),e>=r&&i>e){R+=E[s],r>0&&(e/=r);break}return t.indexOf("o")>-1&&(t.indexOf(" o")>-1?(_=" ",t=t.replace(" o","")):t=t.replace("o",""),_+=m[v].ordinal(e)),t.indexOf("[.]")>-1&&(h=!0,t=t.replace("[.]",".")),a=e.toString().split(".")[0],l=t.split(".")[1],u=t.indexOf(","),l?(l.indexOf("[")>-1?(l=l.replace("]",""),l=l.split("["),T=o(e,l[0].length+l[1].length,n,l[1].length)):T=o(e,l.length,n),a=T.split(".")[0],T=T.split(".")[1].length?m[v].delimiters.decimal+T.split(".")[1]:"",h&&0===Number(T.slice(1))&&(T="")):a=o(e,null,n),a.indexOf("-")>-1&&(a=a.slice(1),O=!0),u>-1&&(a=a.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+m[v].delimiters.thousands)),0===t.indexOf(".")&&(a=""),(c&&O?"(":"")+(!c&&O?"-":"")+(!O&&d?"+":"")+a+T+(_?_:"")+(f?f:"")+(R?R:"")+(c&&O?")":"")}function d(e,t){m[e]=t}function h(e){var t=e.toString().split(".");return t.length<2?1:Math.pow(10,t[1].length)}function f(){var e=Array.prototype.slice.call(arguments);return e.reduce(function(e,t){var n=h(e),o=h(t);return n>o?n:o},-(1/0))}var p,g="1.5.3",m={},v="en",w=null,y="0,0",b="undefined"!=typeof n&&n.exports;p=function(e){return p.isNumeral(e)?e=e.value():0===e||"undefined"==typeof e?e=0:Number(e)||(e=p.fn.unformat(e)),new t(Number(e))},p.version=g,p.isNumeral=function(e){return e instanceof t},p.language=function(e,t){if(!e)return v;if(e&&!t){if(!m[e])throw new Error("Unknown language : "+e);v=e}return(t||!m[e])&&d(e,t),p},p.languageData=function(e){if(!e)return m[v];if(!m[e])throw new Error("Unknown language : "+e);return m[e]},p.language("en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(e){var t=e%10;return 1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"$"}}),p.zeroFormat=function(e){w="string"==typeof e?e:null},p.defaultFormat=function(e){y="string"==typeof e?e:"0.0"},p.validate=function(e,t){var n,o,r,i,s,a,l,u;if("string"!=typeof e&&(e+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",e)),e=e.trim(),""===e)return!1;e=e.replace(/^[+-]?/,"");try{l=p.languageData(t)}catch(c){l=p.languageData(p.language())}return r=l.currency.symbol,s=l.abbreviations,n=l.delimiters.decimal,o="."===l.delimiters.thousands?"\\.":l.delimiters.thousands,u=e.match(/^[^\d\.\,]+/),null!==u&&(e=e.substr(1),u[0]!==r)?!1:(u=e.match(/[^\d]+$/),null!==u&&(e=e.slice(0,-1),u[0]!==s.thousand&&u[0]!==s.million&&u[0]!==s.billion&&u[0]!==s.trillion)?!1:e.match(/^\d+$/)?!0:(a=new RegExp(o+"{2}"),e.match(/[^\d.,]/g)?!1:(i=e.split(n),i.length>2?!1:i.length<2?!!i[0].match(/^\d+.*\d$/)&&!i[0].match(a):""===i[0]?!i[0].match(a)&&!!i[1].match(/^\d+$/):1===i[0].length?!!i[0].match(/^\d+$/)&&!i[0].match(a)&&!!i[1].match(/^\d+$/):!!i[0].match(/^\d+.*\d$/)&&!i[0].match(a)&&!!i[1].match(/^\d+$/))))},"function"!=typeof Array.prototype.reduce&&(Array.prototype.reduce=function(e,t){if(null===this||"undefined"==typeof this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof e)throw new TypeError(e+" is not a function");var n,o,r=this.length>>>0,i=!1;for(1<arguments.length&&(o=t,i=!0),n=0;r>n;++n)this.hasOwnProperty(n)&&(i?o=e(o,this[n],n,this):(o=this[n],i=!0));if(!i)throw new TypeError("Reduce of empty array with no initial value");return o}),p.fn=t.prototype={clone:function(){return p(this)},format:function(e,t){return r(this,e?e:y,void 0!==t?t:Math.round)},unformat:function(e){return"[object Number]"===Object.prototype.toString.call(e)?e:i(this,e?e:y)},value:function(){return this._value},valueOf:function(){return this._value},set:function(e){return this._value=Number(e),this},add:function(e){function t(e,t,o,r){return e+n*t}var n=f.call(null,this._value,e);return this._value=[this._value,e].reduce(t,0)/n,this},subtract:function(e){function t(e,t,o,r){return e-n*t}var n=f.call(null,this._value,e);return this._value=[e].reduce(t,this._value*n)/n,this},multiply:function(e){function t(e,t,n,o){var r=f(e,t);return e*r*(t*r)/(r*r)}return this._value=[this._value,e].reduce(t,1),this},divide:function(e){function t(e,t,n,o){var r=f(e,t);return e*r/(t*r)}return this._value=[this._value,e].reduce(t),this},difference:function(e){return Math.abs(p(this._value).subtract(e).value())}},b&&(n.exports=p),"undefined"==typeof ender&&(this.numeral=p),"function"==typeof e&&e.amd&&e([],function(){return p})}).call(window)},{}],pikaday:[function(t,n,o){/*!
 * Pikaday
 *
 * Copyright © 2014 David Bushell | BSD & MIT license | https://github.com/dbushell/Pikaday
 */
!function(r,i){"use strict";var s;if("object"==typeof o){try{s=t("moment")}catch(a){}n.exports=i(s)}else"function"==typeof e&&e.amd?e(function(e){var t="moment";try{s=e(t)}catch(n){}return i(s)}):r.Pikaday=i(r.moment)}(this,function(e){"use strict";var t="function"==typeof e,n=!!window.addEventListener,o=window.document,r=window.setTimeout,i=function(e,t,o,r){n?e.addEventListener(t,o,!!r):e.attachEvent("on"+t,o)},s=function(e,t,o,r){n?e.removeEventListener(t,o,!!r):e.detachEvent("on"+t,o)},a=function(e,t,n){var r;o.createEvent?(r=o.createEvent("HTMLEvents"),r.initEvent(t,!0,!1),r=y(r,n),e.dispatchEvent(r)):o.createEventObject&&(r=o.createEventObject(),r=y(r,n),e.fireEvent("on"+t,r))},l=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},u=function(e,t){return-1!==(" "+e.className+" ").indexOf(" "+t+" ")},c=function(e,t){u(e,t)||(e.className=""===e.className?t:e.className+" "+t)},d=function(e,t){e.className=l((" "+e.className+" ").replace(" "+t+" "," "))},h=function(e){return/Array/.test(Object.prototype.toString.call(e))},f=function(e){return/Date/.test(Object.prototype.toString.call(e))&&!isNaN(e.getTime())},p=function(e){var t=e.getDay();return 0===t||6===t},g=function(e){return e%4===0&&e%100!==0||e%400===0},m=function(e,t){return[31,g(e)?29:28,31,30,31,30,31,31,30,31,30,31][t]},v=function(e){f(e)&&e.setHours(0,0,0,0)},w=function(e,t){return e.getTime()===t.getTime()},y=function(e,t,n){var o,r;for(o in t)r=void 0!==e[o],r&&"object"==typeof t[o]&&null!==t[o]&&void 0===t[o].nodeName?f(t[o])?n&&(e[o]=new Date(t[o].getTime())):h(t[o])?n&&(e[o]=t[o].slice(0)):e[o]=y({},t[o],n):(n||!r)&&(e[o]=t[o]);return e},b=function(e){return e.month<0&&(e.year-=Math.ceil(Math.abs(e.month)/12),e.month+=12),e.month>11&&(e.year+=Math.floor(Math.abs(e.month)/12),e.month-=12),e},C={field:null,bound:void 0,position:"bottom left",reposition:!0,format:"YYYY-MM-DD",defaultDate:null,setDefaultDate:!1,firstDay:0,minDate:null,maxDate:null,yearRange:10,showWeekNumber:!1,minYear:0,maxYear:9999,minMonth:void 0,maxMonth:void 0,startRange:null,endRange:null,isRTL:!1,yearSuffix:"",showMonthAfterYear:!1,numberOfMonths:1,mainCalendar:"left",container:void 0,i18n:{previousMonth:"Previous Month",nextMonth:"Next Month",months:["January","February","March","April","May","June","July","August","September","October","November","December"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],weekdaysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},theme:null,onSelect:null,onOpen:null,onClose:null,onDraw:null},R=function(e,t,n){for(t+=e.firstDay;t>=7;)t-=7;return n?e.i18n.weekdaysShort[t]:e.i18n.weekdays[t]},_=function(e){if(e.isEmpty)return'<td class="is-empty"></td>';var t=[];return e.isDisabled&&t.push("is-disabled"),e.isToday&&t.push("is-today"),e.isSelected&&t.push("is-selected"),e.isInRange&&t.push("is-inrange"),e.isStartRange&&t.push("is-startrange"),e.isEndRange&&t.push("is-endrange"),'<td data-day="'+e.day+'" class="'+t.join(" ")+'"><button class="pika-button pika-day" type="button" data-pika-year="'+e.year+'" data-pika-month="'+e.month+'" data-pika-day="'+e.day+'">'+e.day+"</button></td>"},S=function(e,t,n){var o=new Date(n,0,1),r=Math.ceil(((new Date(n,t,e)-o)/864e5+o.getDay()+1)/7);return'<td class="pika-week">'+r+"</td>"},E=function(e,t){return"<tr>"+(t?e.reverse():e).join("")+"</tr>"},T=function(e){return"<tbody>"+e.join("")+"</tbody>"},O=function(e){var t,n=[];for(e.showWeekNumber&&n.push("<th></th>"),t=0;7>t;t++)n.push('<th scope="col"><abbr title="'+R(e,t)+'">'+R(e,t,!0)+"</abbr></th>");return"<thead>"+(e.isRTL?n.reverse():n).join("")+"</thead>"},M=function(e,t,n,o,r){var i,s,a,l,u,c=e._o,d=n===c.minYear,f=n===c.maxYear,p='<div class="pika-title">',g=!0,m=!0;for(a=[],i=0;12>i;i++)a.push('<option value="'+(n===r?i-t:12+i-t)+'"'+(i===o?" selected":"")+(d&&i<c.minMonth||f&&i>c.maxMonth?"disabled":"")+">"+c.i18n.months[i]+"</option>");for(l='<div class="pika-label">'+c.i18n.months[o]+'<select class="pika-select pika-select-month" tabindex="-1">'+a.join("")+"</select></div>",h(c.yearRange)?(i=c.yearRange[0],s=c.yearRange[1]+1):(i=n-c.yearRange,s=1+n+c.yearRange),a=[];s>i&&i<=c.maxYear;i++)i>=c.minYear&&a.push('<option value="'+i+'"'+(i===n?" selected":"")+">"+i+"</option>");return u='<div class="pika-label">'+n+c.yearSuffix+'<select class="pika-select pika-select-year" tabindex="-1">'+a.join("")+"</select></div>",p+=c.showMonthAfterYear?u+l:l+u,d&&(0===o||c.minMonth>=o)&&(g=!1),f&&(11===o||c.maxMonth<=o)&&(m=!1),0===t&&(p+='<button class="pika-prev'+(g?"":" is-disabled")+'" type="button">'+c.i18n.previousMonth+"</button>"),t===e._o.numberOfMonths-1&&(p+='<button class="pika-next'+(m?"":" is-disabled")+'" type="button">'+c.i18n.nextMonth+"</button>"),p+="</div>"},k=function(e,t){return'<table cellpadding="0" cellspacing="0" class="pika-table">'+O(e)+T(t)+"</table>"},H=function(s){var a=this,l=a.config(s);a._onMouseDown=function(e){if(a._v){e=e||window.event;var t=e.target||e.srcElement;if(t)if(u(t,"is-disabled")||(u(t,"pika-button")&&!u(t,"is-empty")?(a.setDate(new Date(t.getAttribute("data-pika-year"),t.getAttribute("data-pika-month"),t.getAttribute("data-pika-day"))),l.bound&&r(function(){a.hide(),l.field&&l.field.blur()},100)):u(t,"pika-prev")?a.prevMonth():u(t,"pika-next")&&a.nextMonth()),u(t,"pika-select"))a._c=!0;else{if(!e.preventDefault)return e.returnValue=!1,!1;e.preventDefault()}}},a._onChange=function(e){e=e||window.event;var t=e.target||e.srcElement;t&&(u(t,"pika-select-month")?a.gotoMonth(t.value):u(t,"pika-select-year")&&a.gotoYear(t.value))},a._onInputChange=function(n){var o;n.firedBy!==a&&(t?(o=e(l.field.value,l.format),o=o&&o.isValid()?o.toDate():null):o=new Date(Date.parse(l.field.value)),f(o)&&a.setDate(o),a._v||a.show())},a._onInputFocus=function(){a.show()},a._onInputClick=function(){a.show()},a._onInputBlur=function(){var e=o.activeElement;do if(u(e,"pika-single"))return;while(e=e.parentNode);a._c||(a._b=r(function(){a.hide()},50)),a._c=!1},a._onClick=function(e){e=e||window.event;var t=e.target||e.srcElement,o=t;if(t){!n&&u(t,"pika-select")&&(t.onchange||(t.setAttribute("onchange","return;"),i(t,"change",a._onChange)));do if(u(o,"pika-single")||o===l.trigger)return;while(o=o.parentNode);a._v&&t!==l.trigger&&o!==l.trigger&&a.hide()}},a.el=o.createElement("div"),a.el.className="pika-single"+(l.isRTL?" is-rtl":"")+(l.theme?" "+l.theme:""),i(a.el,"mousedown",a._onMouseDown,!0),i(a.el,"touchend",a._onMouseDown,!0),i(a.el,"change",a._onChange),l.field&&(l.container?l.container.appendChild(a.el):l.bound?o.body.appendChild(a.el):l.field.parentNode.insertBefore(a.el,l.field.nextSibling),i(l.field,"change",a._onInputChange),l.defaultDate||(t&&l.field.value?l.defaultDate=e(l.field.value,l.format).toDate():l.defaultDate=new Date(Date.parse(l.field.value)),l.setDefaultDate=!0));var c=l.defaultDate;f(c)?l.setDefaultDate?a.setDate(c,!0):a.gotoDate(c):a.gotoDate(new Date),l.bound?(this.hide(),a.el.className+=" is-bound",i(l.trigger,"click",a._onInputClick),i(l.trigger,"focus",a._onInputFocus),i(l.trigger,"blur",a._onInputBlur)):this.show()};return H.prototype={config:function(e){this._o||(this._o=y({},C,!0));var t=y(this._o,e,!0);t.isRTL=!!t.isRTL,t.field=t.field&&t.field.nodeName?t.field:null,t.theme="string"==typeof t.theme&&t.theme?t.theme:null,t.bound=!!(void 0!==t.bound?t.field&&t.bound:t.field),t.trigger=t.trigger&&t.trigger.nodeName?t.trigger:t.field,t.disableWeekends=!!t.disableWeekends,t.disableDayFn="function"==typeof t.disableDayFn?t.disableDayFn:null;var n=parseInt(t.numberOfMonths,10)||1;if(t.numberOfMonths=n>4?4:n,f(t.minDate)||(t.minDate=!1),f(t.maxDate)||(t.maxDate=!1),t.minDate&&t.maxDate&&t.maxDate<t.minDate&&(t.maxDate=t.minDate=!1),t.minDate&&this.setMinDate(t.minDate),t.maxDate&&this.setMaxDate(t.maxDate),h(t.yearRange)){var o=(new Date).getFullYear()-10;t.yearRange[0]=parseInt(t.yearRange[0],10)||o,t.yearRange[1]=parseInt(t.yearRange[1],10)||o}else t.yearRange=Math.abs(parseInt(t.yearRange,10))||C.yearRange,t.yearRange>100&&(t.yearRange=100);return t},toString:function(n){return f(this._d)?t?e(this._d).format(n||this._o.format):this._d.toDateString():""},getMoment:function(){return t?e(this._d):null},setMoment:function(n,o){t&&e.isMoment(n)&&this.setDate(n.toDate(),o)},getDate:function(){return f(this._d)?new Date(this._d.getTime()):null},setDate:function(e,t){if(!e)return this._d=null,this._o.field&&(this._o.field.value="",a(this._o.field,"change",{firedBy:this})),this.draw();if("string"==typeof e&&(e=new Date(Date.parse(e))),f(e)){var n=this._o.minDate,o=this._o.maxDate;f(n)&&n>e?e=n:f(o)&&e>o&&(e=o),this._d=new Date(e.getTime()),v(this._d),this.gotoDate(this._d),this._o.field&&(this._o.field.value=this.toString(),a(this._o.field,"change",{firedBy:this})),t||"function"!=typeof this._o.onSelect||this._o.onSelect.call(this,this.getDate())}},gotoDate:function(e){var t=!0;if(f(e)){if(this.calendars){var n=new Date(this.calendars[0].year,this.calendars[0].month,1),o=new Date(this.calendars[this.calendars.length-1].year,this.calendars[this.calendars.length-1].month,1),r=e.getTime();o.setMonth(o.getMonth()+1),o.setDate(o.getDate()-1),t=r<n.getTime()||o.getTime()<r}t&&(this.calendars=[{month:e.getMonth(),year:e.getFullYear()}],"right"===this._o.mainCalendar&&(this.calendars[0].month+=1-this._o.numberOfMonths)),this.adjustCalendars()}},adjustCalendars:function(){this.calendars[0]=b(this.calendars[0]);for(var e=1;e<this._o.numberOfMonths;e++)this.calendars[e]=b({month:this.calendars[0].month+e,year:this.calendars[0].year});this.draw()},gotoToday:function(){this.gotoDate(new Date)},gotoMonth:function(e){isNaN(e)||(this.calendars[0].month=parseInt(e,10),this.adjustCalendars())},nextMonth:function(){this.calendars[0].month++,this.adjustCalendars()},prevMonth:function(){this.calendars[0].month--,this.adjustCalendars()},gotoYear:function(e){isNaN(e)||(this.calendars[0].year=parseInt(e,10),this.adjustCalendars())},setMinDate:function(e){v(e),this._o.minDate=e,this._o.minYear=e.getFullYear(),this._o.minMonth=e.getMonth(),this.draw()},setMaxDate:function(e){v(e),this._o.maxDate=e,this._o.maxYear=e.getFullYear(),this._o.maxMonth=e.getMonth(),this.draw()},setStartRange:function(e){this._o.startRange=e},setEndRange:function(e){this._o.endRange=e},draw:function(e){if(this._v||e){var t=this._o,n=t.minYear,o=t.maxYear,i=t.minMonth,s=t.maxMonth,a="";this._y<=n&&(this._y=n,!isNaN(i)&&this._m<i&&(this._m=i)),this._y>=o&&(this._y=o,!isNaN(s)&&this._m>s&&(this._m=s));for(var l=0;l<t.numberOfMonths;l++)a+='<div class="pika-lendar">'+M(this,l,this.calendars[l].year,this.calendars[l].month,this.calendars[0].year)+this.render(this.calendars[l].year,this.calendars[l].month)+"</div>";if(this.el.innerHTML=a,t.bound&&"hidden"!==t.field.type&&r(function(){t.trigger.focus()},1),"function"==typeof this._o.onDraw){var u=this;r(function(){u._o.onDraw.call(u)},0)}}},adjustPosition:function(){var e,t,n,r,i,s,a,l,u,c;if(!this._o.container){if(this.el.style.position="absolute",e=this._o.trigger,t=e,n=this.el.offsetWidth,r=this.el.offsetHeight,i=window.innerWidth||o.documentElement.clientWidth,s=window.innerHeight||o.documentElement.clientHeight,a=window.pageYOffset||o.body.scrollTop||o.documentElement.scrollTop,"function"==typeof e.getBoundingClientRect)c=e.getBoundingClientRect(),l=c.left+window.pageXOffset,u=c.bottom+window.pageYOffset;else for(l=t.offsetLeft,u=t.offsetTop+t.offsetHeight;t=t.offsetParent;)l+=t.offsetLeft,u+=t.offsetTop;(this._o.reposition&&l+n>i||this._o.position.indexOf("right")>-1&&l-n+e.offsetWidth>0)&&(l=l-n+e.offsetWidth),(this._o.reposition&&u+r>s+a||this._o.position.indexOf("top")>-1&&u-r-e.offsetHeight>0)&&(u=u-r-e.offsetHeight),this.el.style.left=l+"px",this.el.style.top=u+"px"}},render:function(e,t){var n=this._o,o=new Date,r=m(e,t),i=new Date(e,t,1).getDay(),s=[],a=[];v(o),n.firstDay>0&&(i-=n.firstDay,0>i&&(i+=7));for(var l=r+i,u=l;u>7;)u-=7;l+=7-u;for(var c=0,d=0;l>c;c++){var h=new Date(e,t,1+(c-i)),g=f(this._d)?w(h,this._d):!1,y=w(h,o),b=i>c||c>=r+i,C=n.startRange&&w(n.startRange,h),R=n.endRange&&w(n.endRange,h),T=n.startRange&&n.endRange&&n.startRange<h&&h<n.endRange,O=n.minDate&&h<n.minDate||n.maxDate&&h>n.maxDate||n.disableWeekends&&p(h)||n.disableDayFn&&n.disableDayFn(h),M={day:1+(c-i),month:t,year:e,isSelected:g,isToday:y,isDisabled:O,isEmpty:b,isStartRange:C,isEndRange:R,isInRange:T};a.push(_(M)),7===++d&&(n.showWeekNumber&&a.unshift(S(c-i,t,e)),s.push(E(a,n.isRTL)),a=[],d=0)}return k(n,s)},isVisible:function(){return this._v},show:function(){this._v||(d(this.el,"is-hidden"),this._v=!0,this.draw(),this._o.bound&&(i(o,"click",this._onClick),this.adjustPosition()),"function"==typeof this._o.onOpen&&this._o.onOpen.call(this))},hide:function(){var e=this._v;e!==!1&&(this._o.bound&&s(o,"click",this._onClick),this.el.style.position="static",this.el.style.left="auto",this.el.style.top="auto",c(this.el,"is-hidden"),this._v=!1,void 0!==e&&"function"==typeof this._o.onClose&&this._o.onClose.call(this))},destroy:function(){this.hide(),s(this.el,"mousedown",this._onMouseDown,!0),s(this.el,"touchend",this._onMouseDown,!0),s(this.el,"change",this._onChange),this._o.field&&(s(this._o.field,"change",this._onInputChange),this._o.bound&&(s(this._o.trigger,"click",this._onInputClick),s(this._o.trigger,"focus",this._onInputFocus),s(this._o.trigger,"blur",this._onInputBlur))),this.el.parentNode&&this.el.parentNode.removeChild(this.el)}},H})},{moment:"moment"}],zeroclipboard:[function(t,n,o){/*!
 * ZeroClipboard
 * The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface.
 * Copyright (c) 2009-2014 Jon Rohan, James M. Greene
 * Licensed MIT
 * http://zeroclipboard.org/
 * v2.2.0
 */
!function(t,o){"use strict";var r,i,s,a=t,l=a.document,u=a.navigator,c=a.setTimeout,d=a.clearTimeout,h=a.setInterval,f=a.clearInterval,p=a.getComputedStyle,g=a.encodeURIComponent,m=a.ActiveXObject,v=a.Error,w=a.Number.parseInt||a.parseInt,y=a.Number.parseFloat||a.parseFloat,b=a.Number.isNaN||a.isNaN,C=a.Date.now,R=a.Object.keys,_=a.Object.defineProperty,S=a.Object.prototype.hasOwnProperty,E=a.Array.prototype.slice,T=function(){var e=function(e){return e};if("function"==typeof a.wrap&&"function"==typeof a.unwrap)try{var t=l.createElement("div"),n=a.unwrap(t);1===t.nodeType&&n&&1===n.nodeType&&(e=a.unwrap)}catch(o){}return e}(),O=function(e){return E.call(e,0)},M=function(){var e,t,n,r,i,s,a=O(arguments),l=a[0]||{};for(e=1,t=a.length;t>e;e++)if(null!=(n=a[e]))for(r in n)S.call(n,r)&&(i=l[r],s=n[r],l!==s&&s!==o&&(l[r]=s));return l},k=function(e){var t,n,o,r;if("object"!=typeof e||null==e||"number"==typeof e.nodeType)t=e;else if("number"==typeof e.length)for(t=[],n=0,o=e.length;o>n;n++)S.call(e,n)&&(t[n]=k(e[n]));else{t={};for(r in e)S.call(e,r)&&(t[r]=k(e[r]))}return t},H=function(e,t){for(var n={},o=0,r=t.length;r>o;o++)t[o]in e&&(n[t[o]]=e[t[o]]);return n},D=function(e,t){var n={};for(var o in e)-1===t.indexOf(o)&&(n[o]=e[o]);return n},x=function(e){if(e)for(var t in e)S.call(e,t)&&delete e[t];return e},A=function(e,t){if(e&&1===e.nodeType&&e.ownerDocument&&t&&(1===t.nodeType&&t.ownerDocument&&t.ownerDocument===e.ownerDocument||9===t.nodeType&&!t.ownerDocument&&t===e.ownerDocument))do{if(e===t)return!0;e=e.parentNode}while(e);return!1},P=function(e){var t;return"string"==typeof e&&e&&(t=e.split("#")[0].split("?")[0],t=e.slice(0,e.lastIndexOf("/")+1)),t},N=function(e){var t,n;return"string"==typeof e&&e&&(n=e.match(/^(?:|[^:@]*@|.+\)@(?=http[s]?|file)|.+?\s+(?: at |@)(?:[^:\(]+ )*[\(]?)((?:http[s]?|file):\/\/[\/]?.+?\/[^:\)]*?)(?::\d+)(?::\d+)?/),n&&n[1]?t=n[1]:(n=e.match(/\)@((?:http[s]?|file):\/\/[\/]?.+?\/[^:\)]*?)(?::\d+)(?::\d+)?/),n&&n[1]&&(t=n[1]))),t},L=function(){var e,t;try{throw new v}catch(n){t=n}return t&&(e=t.sourceURL||t.fileName||N(t.stack)),e},W=function(){var e,t,n;if(l.currentScript&&(e=l.currentScript.src))return e;if(t=l.getElementsByTagName("script"),1===t.length)return t[0].src||o;if("readyState"in t[0])for(n=t.length;n--;)if("interactive"===t[n].readyState&&(e=t[n].src))return e;return"loading"===l.readyState&&(e=t[t.length-1].src)?e:(e=L())?e:o},I=function(){var e,t,n,r=l.getElementsByTagName("script");for(e=r.length;e--;){if(!(n=r[e].src)){t=null;break}if(n=P(n),null==t)t=n;else if(t!==n){t=null;break}}return t||o},j=function(){var e=P(W())||I()||"";return e+"ZeroClipboard.swf"},B=function(){return null==t.opener&&(!!t.top&&t!=t.top||!!t.parent&&t!=t.parent)}(),F={bridge:null,version:"0.0.0",pluginType:"unknown",disabled:null,outdated:null,sandboxed:null,unavailable:null,degraded:null,deactivated:null,overdue:null,ready:null},V="11.0.0",z={},Y={},U=null,G=0,$=0,K={ready:"Flash communication is established",error:{"flash-disabled":"Flash is disabled or not installed. May also be attempting to run Flash in a sandboxed iframe, which is impossible.","flash-outdated":"Flash is too outdated to support ZeroClipboard","flash-sandboxed":"Attempting to run Flash in a sandboxed iframe, which is impossible","flash-unavailable":"Flash is unable to communicate bidirectionally with JavaScript","flash-degraded":"Flash is unable to preserve data fidelity when communicating with JavaScript","flash-deactivated":"Flash is too outdated for your browser and/or is configured as click-to-activate.\nThis may also mean that the ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity.\nMay also be attempting to run Flash in a sandboxed iframe, which is impossible.","flash-overdue":"Flash communication was established but NOT within the acceptable time limit","version-mismatch":"ZeroClipboard JS version number does not match ZeroClipboard SWF version number","clipboard-error":"At least one error was thrown while ZeroClipboard was attempting to inject your data into the clipboard","config-mismatch":"ZeroClipboard configuration does not match Flash's reality","swf-not-found":"The ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity"}},X=["flash-unavailable","flash-degraded","flash-overdue","version-mismatch","config-mismatch","clipboard-error"],q=["flash-disabled","flash-outdated","flash-sandboxed","flash-unavailable","flash-degraded","flash-deactivated","flash-overdue"],Z=new RegExp("^flash-("+q.map(function(e){return e.replace(/^flash-/,"")}).join("|")+")$"),J=new RegExp("^flash-("+q.slice(1).map(function(e){return e.replace(/^flash-/,"")}).join("|")+")$"),Q={swfPath:j(),trustedDomains:t.location.host?[t.location.host]:[],cacheBust:!0,forceEnhancedClipboard:!1,flashLoadTimeout:3e4,autoActivate:!0,bubbleEvents:!0,containerId:"global-zeroclipboard-html-bridge",containerClass:"global-zeroclipboard-container",swfObjectId:"global-zeroclipboard-flash-bridge",hoverClass:"zeroclipboard-is-hover",activeClass:"zeroclipboard-is-active",forceHandCursor:!1,title:null,zIndex:999999999},ee=function(e){if("object"==typeof e&&null!==e)for(var t in e)if(S.call(e,t))if(/^(?:forceHandCursor|title|zIndex|bubbleEvents)$/.test(t))Q[t]=e[t];else if(null==F.bridge)if("containerId"===t||"swfObjectId"===t){if(!ge(e[t]))throw new Error("The specified `"+t+"` value is not valid as an HTML4 Element ID");Q[t]=e[t]}else Q[t]=e[t];{if("string"!=typeof e||!e)return k(Q);if(S.call(Q,e))return Q[e]}},te=function(){return $e(),{browser:H(u,["userAgent","platform","appName"]),flash:D(F,["bridge"]),zeroclipboard:{version:Xe.version,config:Xe.config()}}},ne=function(){return!!(F.disabled||F.outdated||F.sandboxed||F.unavailable||F.degraded||F.deactivated)},oe=function(e,t){var n,i,s,a={};if("string"==typeof e&&e)s=e.toLowerCase().split(/\s+/);else if("object"==typeof e&&e&&"undefined"==typeof t)for(n in e)S.call(e,n)&&"string"==typeof n&&n&&"function"==typeof e[n]&&Xe.on(n,e[n]);if(s&&s.length){for(n=0,i=s.length;i>n;n++)e=s[n].replace(/^on/,""),a[e]=!0,z[e]||(z[e]=[]),z[e].push(t);if(a.ready&&F.ready&&Xe.emit({type:"ready"}),a.error){for(n=0,i=q.length;i>n;n++)if(F[q[n].replace(/^flash-/,"")]===!0){Xe.emit({type:"error",name:q[n]});break}r!==o&&Xe.version!==r&&Xe.emit({type:"error",name:"version-mismatch",jsVersion:Xe.version,swfVersion:r})}}return Xe},re=function(e,t){var n,o,r,i,s;if(0===arguments.length)i=R(z);else if("string"==typeof e&&e)i=e.split(/\s+/);else if("object"==typeof e&&e&&"undefined"==typeof t)for(n in e)S.call(e,n)&&"string"==typeof n&&n&&"function"==typeof e[n]&&Xe.off(n,e[n]);if(i&&i.length)for(n=0,o=i.length;o>n;n++)if(e=i[n].toLowerCase().replace(/^on/,""),s=z[e],s&&s.length)if(t)for(r=s.indexOf(t);-1!==r;)s.splice(r,1),r=s.indexOf(t,r);else s.length=0;return Xe},ie=function(e){var t;return t="string"==typeof e&&e?k(z[e])||null:k(z)},se=function(e){var t,n,o;return e=me(e),e&&!_e(e)?"ready"===e.type&&F.overdue===!0?Xe.emit({type:"error",name:"flash-overdue"}):(t=M({},e),Ce.call(this,t),"copy"===e.type&&(o=De(Y),n=o.data,U=o.formatMap),n):void 0},ae=function(){var e=F.sandboxed;if($e(),"boolean"!=typeof F.ready&&(F.ready=!1),F.sandboxed!==e&&F.sandboxed===!0)F.ready=!1,Xe.emit({type:"error",name:"flash-sandboxed"});else if(!Xe.isFlashUnusable()&&null===F.bridge){var t=Q.flashLoadTimeout;"number"==typeof t&&t>=0&&(G=c(function(){"boolean"!=typeof F.deactivated&&(F.deactivated=!0),F.deactivated===!0&&Xe.emit({type:"error",name:"flash-deactivated"})},t)),F.overdue=!1,ke()}},le=function(){Xe.clearData(),Xe.blur(),Xe.emit("destroy"),He(),Xe.off()},ue=function(e,t){var n;if("object"==typeof e&&e&&"undefined"==typeof t)n=e,Xe.clearData();else{if("string"!=typeof e||!e)return;n={},n[e]=t}for(var o in n)"string"==typeof o&&o&&S.call(n,o)&&"string"==typeof n[o]&&n[o]&&(Y[o]=n[o])},ce=function(e){"undefined"==typeof e?(x(Y),U=null):"string"==typeof e&&S.call(Y,e)&&delete Y[e]},de=function(e){return"undefined"==typeof e?k(Y):"string"==typeof e&&S.call(Y,e)?Y[e]:void 0},he=function(e){if(e&&1===e.nodeType){i&&(je(i,Q.activeClass),i!==e&&je(i,Q.hoverClass)),i=e,Ie(e,Q.hoverClass);var t=e.getAttribute("title")||Q.title;if("string"==typeof t&&t){var n=Me(F.bridge);n&&n.setAttribute("title",t)}var o=Q.forceHandCursor===!0||"pointer"===Be(e,"cursor");Ue(o),Ye()}},fe=function(){var e=Me(F.bridge);e&&(e.removeAttribute("title"),e.style.left="0px",e.style.top="-9999px",e.style.width="1px",e.style.height="1px"),i&&(je(i,Q.hoverClass),je(i,Q.activeClass),i=null)},pe=function(){return i||null},ge=function(e){return"string"==typeof e&&e&&/^[A-Za-z][A-Za-z0-9_:\-\.]*$/.test(e)},me=function(e){var t;if("string"==typeof e&&e?(t=e,e={}):"object"==typeof e&&e&&"string"==typeof e.type&&e.type&&(t=e.type),t){t=t.toLowerCase(),!e.target&&(/^(copy|aftercopy|_click)$/.test(t)||"error"===t&&"clipboard-error"===e.name)&&(e.target=s),M(e,{type:t,target:e.target||i||null,relatedTarget:e.relatedTarget||null,currentTarget:F&&F.bridge||null,timeStamp:e.timeStamp||C()||null});var n=K[e.type];return"error"===e.type&&e.name&&n&&(n=n[e.name]),n&&(e.message=n),"ready"===e.type&&M(e,{target:null,version:F.version}),"error"===e.type&&(Z.test(e.name)&&M(e,{target:null,minimumVersion:V}),J.test(e.name)&&M(e,{version:F.version})),"copy"===e.type&&(e.clipboardData={setData:Xe.setData,clearData:Xe.clearData}),"aftercopy"===e.type&&(e=xe(e,U)),e.target&&!e.relatedTarget&&(e.relatedTarget=ve(e.target)),we(e)}},ve=function(e){var t=e&&e.getAttribute&&e.getAttribute("data-clipboard-target");return t?l.getElementById(t):null},we=function(e){if(e&&/^_(?:click|mouse(?:over|out|down|up|move))$/.test(e.type)){var t=e.target,n="_mouseover"===e.type&&e.relatedTarget?e.relatedTarget:o,r="_mouseout"===e.type&&e.relatedTarget?e.relatedTarget:o,i=Fe(t),s=a.screenLeft||a.screenX||0,u=a.screenTop||a.screenY||0,c=l.body.scrollLeft+l.documentElement.scrollLeft,d=l.body.scrollTop+l.documentElement.scrollTop,h=i.left+("number"==typeof e._stageX?e._stageX:0),f=i.top+("number"==typeof e._stageY?e._stageY:0),p=h-c,g=f-d,m=s+p,v=u+g,w="number"==typeof e.movementX?e.movementX:0,y="number"==typeof e.movementY?e.movementY:0;delete e._stageX,delete e._stageY,M(e,{srcElement:t,fromElement:n,toElement:r,screenX:m,screenY:v,pageX:h,pageY:f,clientX:p,clientY:g,x:p,y:g,movementX:w,movementY:y,offsetX:0,offsetY:0,layerX:0,layerY:0})}return e},ye=function(e){var t=e&&"string"==typeof e.type&&e.type||"";return!/^(?:(?:before)?copy|destroy)$/.test(t)},be=function(e,t,n,o){o?c(function(){e.apply(t,n)},0):e.apply(t,n)},Ce=function(e){if("object"==typeof e&&e&&e.type){var t=ye(e),n=z["*"]||[],o=z[e.type]||[],r=n.concat(o);if(r&&r.length){var i,s,l,u,c,d=this;for(i=0,s=r.length;s>i;i++)l=r[i],u=d,"string"==typeof l&&"function"==typeof a[l]&&(l=a[l]),"object"==typeof l&&l&&"function"==typeof l.handleEvent&&(u=l,l=l.handleEvent),"function"==typeof l&&(c=M({},e),be(l,u,[c],t))}return this}},Re=function(e){var t=null;return(B===!1||e&&"error"===e.type&&e.name&&-1!==X.indexOf(e.name))&&(t=!1),t},_e=function(e){var t=e.target||i||null,n="swf"===e._source;switch(delete e._source,e.type){case"error":var o="flash-sandboxed"===e.name||Re(e);"boolean"==typeof o&&(F.sandboxed=o),-1!==q.indexOf(e.name)?M(F,{disabled:"flash-disabled"===e.name,outdated:"flash-outdated"===e.name,unavailable:"flash-unavailable"===e.name,degraded:"flash-degraded"===e.name,deactivated:"flash-deactivated"===e.name,overdue:"flash-overdue"===e.name,ready:!1}):"version-mismatch"===e.name&&(r=e.swfVersion,M(F,{disabled:!1,outdated:!1,unavailable:!1,degraded:!1,deactivated:!1,overdue:!1,ready:!1})),ze();break;case"ready":r=e.swfVersion;var a=F.deactivated===!0;M(F,{disabled:!1,outdated:!1,sandboxed:!1,unavailable:!1,degraded:!1,deactivated:!1,overdue:a,ready:!a}),ze();break;case"beforecopy":s=t;break;case"copy":var l,u,c=e.relatedTarget;!Y["text/html"]&&!Y["text/plain"]&&c&&(u=c.value||c.outerHTML||c.innerHTML)&&(l=c.value||c.textContent||c.innerText)?(e.clipboardData.clearData(),e.clipboardData.setData("text/plain",l),u!==l&&e.clipboardData.setData("text/html",u)):!Y["text/plain"]&&e.target&&(l=e.target.getAttribute("data-clipboard-text"))&&(e.clipboardData.clearData(),e.clipboardData.setData("text/plain",l));break;case"aftercopy":Se(e),Xe.clearData(),t&&t!==We()&&t.focus&&t.focus();break;case"_mouseover":Xe.focus(t),Q.bubbleEvents===!0&&n&&(t&&t!==e.relatedTarget&&!A(e.relatedTarget,t)&&Ee(M({},e,{type:"mouseenter",bubbles:!1,cancelable:!1})),Ee(M({},e,{type:"mouseover"})));break;case"_mouseout":Xe.blur(),Q.bubbleEvents===!0&&n&&(t&&t!==e.relatedTarget&&!A(e.relatedTarget,t)&&Ee(M({},e,{type:"mouseleave",bubbles:!1,cancelable:!1})),Ee(M({},e,{type:"mouseout"})));break;case"_mousedown":Ie(t,Q.activeClass),Q.bubbleEvents===!0&&n&&Ee(M({},e,{type:e.type.slice(1)}));break;case"_mouseup":je(t,Q.activeClass),Q.bubbleEvents===!0&&n&&Ee(M({},e,{type:e.type.slice(1)}));break;case"_click":s=null,Q.bubbleEvents===!0&&n&&Ee(M({},e,{type:e.type.slice(1)}));break;case"_mousemove":Q.bubbleEvents===!0&&n&&Ee(M({},e,{type:e.type.slice(1)}))}return/^_(?:click|mouse(?:over|out|down|up|move))$/.test(e.type)?!0:void 0},Se=function(e){if(e.errors&&e.errors.length>0){var t=k(e);M(t,{type:"error",name:"clipboard-error"}),delete t.success,c(function(){Xe.emit(t)},0)}},Ee=function(e){if(e&&"string"==typeof e.type&&e){var t,n=e.target||null,o=n&&n.ownerDocument||l,r={view:o.defaultView||a,canBubble:!0,cancelable:!0,detail:"click"===e.type?1:0,button:"number"==typeof e.which?e.which-1:"number"==typeof e.button?e.button:o.createEvent?0:1},i=M(r,e);n&&o.createEvent&&n.dispatchEvent&&(i=[i.type,i.canBubble,i.cancelable,i.view,i.detail,i.screenX,i.screenY,i.clientX,i.clientY,i.ctrlKey,i.altKey,i.shiftKey,i.metaKey,i.button,i.relatedTarget],t=o.createEvent("MouseEvents"),t.initMouseEvent&&(t.initMouseEvent.apply(t,i),t._source="js",n.dispatchEvent(t)))}},Te=function(){var e=Q.flashLoadTimeout;if("number"==typeof e&&e>=0){var t=Math.min(1e3,e/10),n=Q.swfObjectId+"_fallbackContent";$=h(function(){var e=l.getElementById(n);Ve(e)&&(ze(),F.deactivated=null,Xe.emit({type:"error",name:"swf-not-found"}))},t)}},Oe=function(){var e=l.createElement("div");return e.id=Q.containerId,e.className=Q.containerClass,e.style.position="absolute",e.style.left="0px",e.style.top="-9999px",e.style.width="1px",e.style.height="1px",e.style.zIndex=""+Ge(Q.zIndex),e},Me=function(e){for(var t=e&&e.parentNode;t&&"OBJECT"===t.nodeName&&t.parentNode;)t=t.parentNode;return t||null},ke=function(){var e,t=F.bridge,n=Me(t);if(!t){var o=Le(a.location.host,Q),r="never"===o?"none":"all",i=Pe(M({jsVersion:Xe.version},Q)),s=Q.swfPath+Ae(Q.swfPath,Q);n=Oe();var u=l.createElement("div");n.appendChild(u),l.body.appendChild(n);var c=l.createElement("div"),d="activex"===F.pluginType;c.innerHTML='<object id="'+Q.swfObjectId+'" name="'+Q.swfObjectId+'" width="100%" height="100%" '+(d?'classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"':'type="application/x-shockwave-flash" data="'+s+'"')+">"+(d?'<param name="movie" value="'+s+'"/>':"")+'<param name="allowScriptAccess" value="'+o+'"/><param name="allowNetworking" value="'+r+'"/><param name="menu" value="false"/><param name="wmode" value="transparent"/><param name="flashvars" value="'+i+'"/><div id="'+Q.swfObjectId+'_fallbackContent">&nbsp;</div></object>',t=c.firstChild,c=null,T(t).ZeroClipboard=Xe,n.replaceChild(t,u),Te()}return t||(t=l[Q.swfObjectId],t&&(e=t.length)&&(t=t[e-1]),!t&&n&&(t=n.firstChild)),F.bridge=t||null,t},He=function(){var e=F.bridge;if(e){var t=Me(e);t&&("activex"===F.pluginType&&"readyState"in e?(e.style.display="none",function n(){if(4===e.readyState){for(var o in e)"function"==typeof e[o]&&(e[o]=null);e.parentNode&&e.parentNode.removeChild(e),t.parentNode&&t.parentNode.removeChild(t)}else c(n,10)}()):(e.parentNode&&e.parentNode.removeChild(e),t.parentNode&&t.parentNode.removeChild(t))),ze(),F.ready=null,F.bridge=null,F.deactivated=null,r=o}},De=function(e){var t={},n={};if("object"==typeof e&&e){for(var o in e)if(o&&S.call(e,o)&&"string"==typeof e[o]&&e[o])switch(o.toLowerCase()){case"text/plain":case"text":case"air:text":case"flash:text":t.text=e[o],n.text=o;break;case"text/html":case"html":case"air:html":case"flash:html":t.html=e[o],n.html=o;break;case"application/rtf":case"text/rtf":case"rtf":case"richtext":case"air:rtf":case"flash:rtf":t.rtf=e[o],n.rtf=o}return{data:t,formatMap:n}}},xe=function(e,t){if("object"!=typeof e||!e||"object"!=typeof t||!t)return e;var n={};for(var o in e)if(S.call(e,o))if("errors"===o){n[o]=e[o]?e[o].slice():[];for(var r=0,i=n[o].length;i>r;r++)n[o][r].format=t[n[o][r].format]}else if("success"!==o&&"data"!==o)n[o]=e[o];else{n[o]={};var s=e[o];for(var a in s)a&&S.call(s,a)&&S.call(t,a)&&(n[o][t[a]]=s[a])}return n},Ae=function(e,t){var n=null==t||t&&t.cacheBust===!0;return n?(-1===e.indexOf("?")?"?":"&")+"noCache="+C():""},Pe=function(e){var t,n,o,r,i="",s=[];if(e.trustedDomains&&("string"==typeof e.trustedDomains?r=[e.trustedDomains]:"object"==typeof e.trustedDomains&&"length"in e.trustedDomains&&(r=e.trustedDomains)),r&&r.length)for(t=0,n=r.length;n>t;t++)if(S.call(r,t)&&r[t]&&"string"==typeof r[t]){if(o=Ne(r[t]),!o)continue;if("*"===o){s.length=0,s.push(o);break}s.push.apply(s,[o,"//"+o,a.location.protocol+"//"+o])}return s.length&&(i+="trustedOrigins="+g(s.join(","))),e.forceEnhancedClipboard===!0&&(i+=(i?"&":"")+"forceEnhancedClipboard=true"),"string"==typeof e.swfObjectId&&e.swfObjectId&&(i+=(i?"&":"")+"swfObjectId="+g(e.swfObjectId)),"string"==typeof e.jsVersion&&e.jsVersion&&(i+=(i?"&":"")+"jsVersion="+g(e.jsVersion)),i},Ne=function(e){if(null==e||""===e)return null;if(e=e.replace(/^\s+|\s+$/g,""),""===e)return null;var t=e.indexOf("//");e=-1===t?e:e.slice(t+2);var n=e.indexOf("/");return e=-1===n?e:-1===t||0===n?null:e.slice(0,n),e&&".swf"===e.slice(-4).toLowerCase()?null:e||null},Le=function(){var e=function(e){var t,n,o,r=[];if("string"==typeof e&&(e=[e]),"object"!=typeof e||!e||"number"!=typeof e.length)return r;for(t=0,n=e.length;n>t;t++)if(S.call(e,t)&&(o=Ne(e[t]))){if("*"===o){r.length=0,r.push("*");break}-1===r.indexOf(o)&&r.push(o)}return r};return function(t,n){var o=Ne(n.swfPath);null===o&&(o=t);var r=e(n.trustedDomains),i=r.length;if(i>0){if(1===i&&"*"===r[0])return"always";if(-1!==r.indexOf(t))return 1===i&&t===o?"sameDomain":"always"}return"never"}}(),We=function(){try{return l.activeElement}catch(e){return null}},Ie=function(e,t){var n,o,r,i=[];if("string"==typeof t&&t&&(i=t.split(/\s+/)),e&&1===e.nodeType&&i.length>0)if(e.classList)for(n=0,o=i.length;o>n;n++)e.classList.add(i[n]);else if(e.hasOwnProperty("className")){for(r=" "+e.className+" ",n=0,o=i.length;o>n;n++)-1===r.indexOf(" "+i[n]+" ")&&(r+=i[n]+" ");e.className=r.replace(/^\s+|\s+$/g,"")}return e},je=function(e,t){var n,o,r,i=[];if("string"==typeof t&&t&&(i=t.split(/\s+/)),e&&1===e.nodeType&&i.length>0)if(e.classList&&e.classList.length>0)for(n=0,o=i.length;o>n;n++)e.classList.remove(i[n]);else if(e.className){for(r=(" "+e.className+" ").replace(/[\r\n\t]/g," "),n=0,o=i.length;o>n;n++)r=r.replace(" "+i[n]+" "," ");e.className=r.replace(/^\s+|\s+$/g,"")}return e},Be=function(e,t){var n=p(e,null).getPropertyValue(t);return"cursor"!==t||n&&"auto"!==n||"A"!==e.nodeName?n:"pointer"},Fe=function(e){var t={left:0,top:0,width:0,height:0};if(e.getBoundingClientRect){var n=e.getBoundingClientRect(),o=a.pageXOffset,r=a.pageYOffset,i=l.documentElement.clientLeft||0,s=l.documentElement.clientTop||0,u=0,c=0;if("relative"===Be(l.body,"position")){var d=l.body.getBoundingClientRect(),h=l.documentElement.getBoundingClientRect();u=d.left-h.left||0,c=d.top-h.top||0}t.left=n.left+o-i-u,t.top=n.top+r-s-c,t.width="width"in n?n.width:n.right-n.left,t.height="height"in n?n.height:n.bottom-n.top}return t},Ve=function(e){if(!e)return!1;var t=p(e,null),n=y(t.height)>0,o=y(t.width)>0,r=y(t.top)>=0,i=y(t.left)>=0,s=n&&o&&r&&i,a=s?null:Fe(e),l="none"!==t.display&&"collapse"!==t.visibility&&(s||!!a&&(n||a.height>0)&&(o||a.width>0)&&(r||a.top>=0)&&(i||a.left>=0));return l},ze=function(){d(G),G=0,f($),$=0},Ye=function(){var e;if(i&&(e=Me(F.bridge))){var t=Fe(i);M(e.style,{width:t.width+"px",height:t.height+"px",top:t.top+"px",left:t.left+"px",zIndex:""+Ge(Q.zIndex)})}},Ue=function(e){F.ready===!0&&(F.bridge&&"function"==typeof F.bridge.setHandCursor?F.bridge.setHandCursor(e):F.ready=!1)},Ge=function(e){if(/^(?:auto|inherit)$/.test(e))return e;var t;return"number"!=typeof e||b(e)?"string"==typeof e&&(t=Ge(w(e,10))):t=e,"number"==typeof t?t:"auto"},$e=function(e){var n,o,r,i=F.sandboxed,s=null;if(e=e===!0,B===!1)s=!1;else{try{o=t.frameElement||null}catch(a){r={name:a.name,message:a.message}}if(o&&1===o.nodeType&&"IFRAME"===o.nodeName)try{s=o.hasAttribute("sandbox")}catch(a){s=null}else{try{n=document.domain||null}catch(a){n=null}(null===n||r&&"SecurityError"===r.name&&/(^|[\s\(\[@])sandbox(es|ed|ing|[\s\.,!\)\]@]|$)/.test(r.message.toLowerCase()))&&(s=!0)}}return F.sandboxed=s,i===s||e||Ke(m),s},Ke=function(e){function t(e){var t=e.match(/[\d]+/g);return t.length=3,t.join(".")}function n(e){return!!e&&(e=e.toLowerCase())&&(/^(pepflashplayer\.dll|libpepflashplayer\.so|pepperflashplayer\.plugin)$/.test(e)||"chrome.plugin"===e.slice(-13))}function o(e){e&&(a=!0,e.version&&(d=t(e.version)),!d&&e.description&&(d=t(e.description)),e.filename&&(c=n(e.filename)))}var r,i,s,a=!1,l=!1,c=!1,d="";if(u.plugins&&u.plugins.length)r=u.plugins["Shockwave Flash"],o(r),u.plugins["Shockwave Flash 2.0"]&&(a=!0,d="********");else if(u.mimeTypes&&u.mimeTypes.length)s=u.mimeTypes["application/x-shockwave-flash"],r=s&&s.enabledPlugin,o(r);else if("undefined"!=typeof e){l=!0;try{i=new e("ShockwaveFlash.ShockwaveFlash.7"),a=!0,d=t(i.GetVariable("$version"))}catch(h){try{i=new e("ShockwaveFlash.ShockwaveFlash.6"),a=!0,d="6.0.21"}catch(f){try{i=new e("ShockwaveFlash.ShockwaveFlash"),a=!0,d=t(i.GetVariable("$version"))}catch(p){l=!1}}}}F.disabled=a!==!0,F.outdated=d&&y(d)<y(V),F.version=d||"0.0.0",F.pluginType=c?"pepper":l?"activex":a?"netscape":"unknown"};Ke(m),$e(!0);var Xe=function(){return this instanceof Xe?void("function"==typeof Xe._createClient&&Xe._createClient.apply(this,O(arguments))):new Xe};_(Xe,"version",{value:"2.2.0",writable:!1,configurable:!0,enumerable:!0}),Xe.config=function(){return ee.apply(this,O(arguments))},Xe.state=function(){return te.apply(this,O(arguments))},Xe.isFlashUnusable=function(){return ne.apply(this,O(arguments))},Xe.on=function(){return oe.apply(this,O(arguments))},Xe.off=function(){return re.apply(this,O(arguments))},Xe.handlers=function(){return ie.apply(this,O(arguments))},Xe.emit=function(){return se.apply(this,O(arguments))},Xe.create=function(){return ae.apply(this,O(arguments))},Xe.destroy=function(){return le.apply(this,O(arguments))},Xe.setData=function(){return ue.apply(this,O(arguments))},Xe.clearData=function(){return ce.apply(this,O(arguments))},Xe.getData=function(){return de.apply(this,O(arguments))},Xe.focus=Xe.activate=function(){return he.apply(this,O(arguments))},Xe.blur=Xe.deactivate=function(){return fe.apply(this,O(arguments))},Xe.activeElement=function(){return pe.apply(this,O(arguments))};var qe=0,Ze={},Je=0,Qe={},et={};M(Q,{autoActivate:!0});var tt=function(e){var t=this;t.id=""+qe++,Ze[t.id]={instance:t,elements:[],handlers:{}},e&&t.clip(e),Xe.on("*",function(e){return t.emit(e)}),Xe.on("destroy",function(){t.destroy()}),Xe.create()},nt=function(e,t){var n,i,s,a={},l=Ze[this.id],u=l&&l.handlers;if(!l)throw new Error("Attempted to add new listener(s) to a destroyed ZeroClipboard client instance");if("string"==typeof e&&e)s=e.toLowerCase().split(/\s+/);else if("object"==typeof e&&e&&"undefined"==typeof t)for(n in e)S.call(e,n)&&"string"==typeof n&&n&&"function"==typeof e[n]&&this.on(n,e[n]);if(s&&s.length){for(n=0,i=s.length;i>n;n++)e=s[n].replace(/^on/,""),a[e]=!0,u[e]||(u[e]=[]),u[e].push(t);if(a.ready&&F.ready&&this.emit({type:"ready",client:this}),a.error){for(n=0,i=q.length;i>n;n++)if(F[q[n].replace(/^flash-/,"")]){this.emit({type:"error",name:q[n],client:this});break}r!==o&&Xe.version!==r&&this.emit({type:"error",name:"version-mismatch",jsVersion:Xe.version,swfVersion:r})}}return this},ot=function(e,t){var n,o,r,i,s,a=Ze[this.id],l=a&&a.handlers;if(!l)return this;if(0===arguments.length)i=R(l);else if("string"==typeof e&&e)i=e.split(/\s+/);else if("object"==typeof e&&e&&"undefined"==typeof t)for(n in e)S.call(e,n)&&"string"==typeof n&&n&&"function"==typeof e[n]&&this.off(n,e[n]);if(i&&i.length)for(n=0,o=i.length;o>n;n++)if(e=i[n].toLowerCase().replace(/^on/,""),s=l[e],s&&s.length)if(t)for(r=s.indexOf(t);-1!==r;)s.splice(r,1),r=s.indexOf(t,r);else s.length=0;return this},rt=function(e){var t=null,n=Ze[this.id]&&Ze[this.id].handlers;return n&&(t="string"==typeof e&&e?n[e]?n[e].slice(0):[]:k(n)),t},it=function(e){if(ct.call(this,e)){"object"==typeof e&&e&&"string"==typeof e.type&&e.type&&(e=M({},e));var t=M({},me(e),{client:this});dt.call(this,t)}return this},st=function(e){if(!Ze[this.id])throw new Error("Attempted to clip element(s) to a destroyed ZeroClipboard client instance");e=ht(e);for(var t=0;t<e.length;t++)if(S.call(e,t)&&e[t]&&1===e[t].nodeType){e[t].zcClippingId?-1===Qe[e[t].zcClippingId].indexOf(this.id)&&Qe[e[t].zcClippingId].push(this.id):(e[t].zcClippingId="zcClippingId_"+Je++,Qe[e[t].zcClippingId]=[this.id],Q.autoActivate===!0&&ft(e[t]));var n=Ze[this.id]&&Ze[this.id].elements;-1===n.indexOf(e[t])&&n.push(e[t])}return this},at=function(e){var t=Ze[this.id];if(!t)return this;var n,o=t.elements;e="undefined"==typeof e?o.slice(0):ht(e);for(var r=e.length;r--;)if(S.call(e,r)&&e[r]&&1===e[r].nodeType){for(n=0;-1!==(n=o.indexOf(e[r],n));)o.splice(n,1);var i=Qe[e[r].zcClippingId];if(i){for(n=0;-1!==(n=i.indexOf(this.id,n));)i.splice(n,1);0===i.length&&(Q.autoActivate===!0&&pt(e[r]),delete e[r].zcClippingId)}}return this},lt=function(){var e=Ze[this.id];return e&&e.elements?e.elements.slice(0):[]},ut=function(){Ze[this.id]&&(this.unclip(),this.off(),delete Ze[this.id])},ct=function(e){if(!e||!e.type)return!1;if(e.client&&e.client!==this)return!1;var t=Ze[this.id],n=t&&t.elements,o=!!n&&n.length>0,r=!e.target||o&&-1!==n.indexOf(e.target),i=e.relatedTarget&&o&&-1!==n.indexOf(e.relatedTarget),s=e.client&&e.client===this;return t&&(r||i||s)?!0:!1},dt=function(e){var t=Ze[this.id];if("object"==typeof e&&e&&e.type&&t){var n=ye(e),o=t&&t.handlers["*"]||[],r=t&&t.handlers[e.type]||[],i=o.concat(r);if(i&&i.length){var s,l,u,c,d,h=this;for(s=0,l=i.length;l>s;s++)u=i[s],c=h,"string"==typeof u&&"function"==typeof a[u]&&(u=a[u]),"object"==typeof u&&u&&"function"==typeof u.handleEvent&&(c=u,u=u.handleEvent),"function"==typeof u&&(d=M({},e),be(u,c,[d],n))}}},ht=function(e){return"string"==typeof e&&(e=[]),"number"!=typeof e.length?[e]:e},ft=function(e){if(e&&1===e.nodeType){var t=function(e){(e||(e=a.event))&&("js"!==e._source&&(e.stopImmediatePropagation(),e.preventDefault()),delete e._source)},n=function(n){(n||(n=a.event))&&(t(n),Xe.focus(e))};e.addEventListener("mouseover",n,!1),e.addEventListener("mouseout",t,!1),e.addEventListener("mouseenter",t,!1),e.addEventListener("mouseleave",t,!1),e.addEventListener("mousemove",t,!1),et[e.zcClippingId]={mouseover:n,mouseout:t,mouseenter:t,mouseleave:t,mousemove:t}}},pt=function(e){if(e&&1===e.nodeType){var t=et[e.zcClippingId];if("object"==typeof t&&t){for(var n,o,r=["move","leave","enter","out","over"],i=0,s=r.length;s>i;i++)n="mouse"+r[i],o=t[n],"function"==typeof o&&e.removeEventListener(n,o,!1);delete et[e.zcClippingId]}}};Xe._createClient=function(){tt.apply(this,O(arguments))},Xe.prototype.on=function(){return nt.apply(this,O(arguments))},Xe.prototype.off=function(){return ot.apply(this,O(arguments))},Xe.prototype.handlers=function(){return rt.apply(this,O(arguments))},Xe.prototype.emit=function(){return it.apply(this,O(arguments))},Xe.prototype.clip=function(){return st.apply(this,O(arguments))},Xe.prototype.unclip=function(){return at.apply(this,O(arguments))},Xe.prototype.elements=function(){return lt.apply(this,O(arguments))},Xe.prototype.destroy=function(){return ut.apply(this,O(arguments))},Xe.prototype.setText=function(e){if(!Ze[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return Xe.setData("text/plain",e),this},Xe.prototype.setHtml=function(e){if(!Ze[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return Xe.setData("text/html",e),this},Xe.prototype.setRichText=function(e){if(!Ze[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return Xe.setData("application/rtf",e),this},Xe.prototype.setData=function(){if(!Ze[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return Xe.setData.apply(this,O(arguments)),this},Xe.prototype.clearData=function(){if(!Ze[this.id])throw new Error("Attempted to clear pending clipboard data from a destroyed ZeroClipboard client instance");return Xe.clearData.apply(this,O(arguments)),this},Xe.prototype.getData=function(){if(!Ze[this.id])throw new Error("Attempted to get pending clipboard data from a destroyed ZeroClipboard client instance");return Xe.getData.apply(this,O(arguments))},"function"==typeof e&&e.amd?e(function(){return Xe}):"object"==typeof n&&n&&"object"==typeof n.exports&&n.exports?n.exports=Xe:t.ZeroClipboard=Xe}(function(){return this||window}())},{}]},{},[23,59,61,60,62,83,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,84,85,86,87,100,101,102,90,91,92,93,94,95,31,35,32,33,40,34,36,37,38,39])("zeroclipboard")});