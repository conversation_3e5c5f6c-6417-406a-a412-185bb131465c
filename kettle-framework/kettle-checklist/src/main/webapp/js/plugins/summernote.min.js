/*! Summernote v0.7.0 | (c) 2013-2015 <PERSON> and other contributors | MIT license */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof module&&module.exports?module.exports=a(require("jquery")):a(window.jQuery)}(function(a){var b,c=function(){var b=function(a){return function(b){return a===b}},c=function(a,b){return a===b},d=function(a){return function(b,c){return b[a]===c[a]}},e=function(){return!0},f=function(){return!1},g=function(a){return function(){return!a.apply(a,arguments)}},h=function(a,b){return function(c){return a(c)&&b(c)}},i=function(a){return a},j=0,k=function(a){var b=++j+"";return a?a+b:b},l=function(b){var c=a(document);return{top:b.top+c.scrollTop(),left:b.left+c.scrollLeft(),width:b.right-b.left,height:b.bottom-b.top}},m=function(a){var b={};for(var c in a)a.hasOwnProperty(c)&&(b[a[c]]=c);return b},n=function(a,b){return b=b||"",b+a.split(".").map(function(a){return a.substring(0,1).toUpperCase()+a.substring(1)}).join("")};return{eq:b,eq2:c,peq2:d,ok:e,fail:f,self:i,not:g,and:h,uniqueId:k,rect2bnd:l,invertObject:m,namespaceToCamel:n}}(),d=function(){var b=function(a){return a[0]},d=function(a){return a[a.length-1]},e=function(a){return a.slice(0,a.length-1)},f=function(a){return a.slice(1)},g=function(a,b){for(var c=0,d=a.length;d>c;c++){var e=a[c];if(b(e))return e}},h=function(a,b){for(var c=0,d=a.length;d>c;c++)if(!b(a[c]))return!1;return!0},i=function(b,c){return a.inArray(c,b)},j=function(a,b){return-1!==i(a,b)},k=function(a,b){return b=b||c.self,a.reduce(function(a,c){return a+b(c)},0)},l=function(a){for(var b=[],c=-1,d=a.length;++c<d;)b[c]=a[c];return b},m=function(a){return!a||!a.length},n=function(a,c){if(!a.length)return[];var e=f(a);return e.reduce(function(a,b){var e=d(a);return c(d(e),b)?e[e.length]=b:a[a.length]=[b],a},[[b(a)]])},o=function(a){for(var b=[],c=0,d=a.length;d>c;c++)a[c]&&b.push(a[c]);return b},p=function(a){for(var b=[],c=0,d=a.length;d>c;c++)j(b,a[c])||b.push(a[c]);return b},q=function(a,b){var c=i(a,b);return-1===c?null:a[c+1]},r=function(a,b){var c=i(a,b);return-1===c?null:a[c-1]};return{head:b,last:d,initial:e,tail:f,prev:r,next:q,find:g,contains:j,all:h,sum:k,from:l,isEmpty:m,clusterBy:n,compact:o,unique:p}}(),e="function"==typeof define&&define.amd,f=function(b){var c="Comic Sans MS"===b?"Courier New":"Comic Sans MS",d=a("<div>").css({position:"absolute",left:"-9999px",top:"-9999px",fontSize:"200px"}).text("mmmmmmmmmwwwwwww").appendTo(document.body),e=d.css("fontFamily",c).width(),f=d.css("fontFamily",b+","+c).width();return d.remove(),e!==f},g=navigator.userAgent,h=/MSIE|Trident/i.test(g);if(h){var i=/MSIE (\d+[.]\d+)/.exec(g);i&&(b=parseFloat(i[1])),i=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(g),i&&(b=parseFloat(i[1]))}var j={isMac:navigator.appVersion.indexOf("Mac")>-1,isMSIE:h,isFF:/firefox/i.test(g),isWebkit:/webkit/i.test(g),isSafari:/safari/i.test(g),browserVersion:b,jqueryVersion:parseFloat(a.fn.jquery),isSupportAmd:e,hasCodeMirror:e?require.specified("CodeMirror"):!!window.CodeMirror,isFontInstalled:f,isW3CRangeSupport:!!document.createRange},k=String.fromCharCode(160),l="\ufeff",m=function(){var b=function(b){return b&&a(b).hasClass("note-editable")},e=function(b){return b&&a(b).hasClass("note-control-sizing")},f=function(a){return a=a.toUpperCase(),function(b){return b&&b.nodeName.toUpperCase()===a}},g=function(a){return a&&3===a.nodeType},h=function(a){return a&&1===a.nodeType},i=function(a){return a&&/^BR|^IMG|^HR|^IFRAME|^BUTTON/.test(a.nodeName.toUpperCase())},n=function(a){return b(a)?!1:a&&/^DIV|^P|^LI|^H[1-7]/.test(a.nodeName.toUpperCase())},o=function(a){return a&&/^H[1-7]/.test(a.nodeName.toUpperCase())},p=f("LI"),q=function(a){return n(a)&&!p(a)},r=f("TABLE"),s=function(a){return!(x(a)||t(a)||u(a)||n(a)||r(a)||w(a))},t=function(a){return a&&/^UL|^OL/.test(a.nodeName.toUpperCase())},u=f("HR"),v=function(a){return a&&/^TD|^TH/.test(a.nodeName.toUpperCase())},w=f("BLOCKQUOTE"),x=function(a){return v(a)||w(a)||b(a)},y=f("A"),z=function(a){return s(a)&&!!I(a,n)},A=function(a){return s(a)&&!I(a,n)},B=f("BODY"),C=function(a,b){return a.nextSibling===b||a.previousSibling===b},D=function(a,b){b=b||c.ok;var d=[];return a.previousSibling&&b(a.previousSibling)&&d.push(a.previousSibling),d.push(a),a.nextSibling&&b(a.nextSibling)&&d.push(a.nextSibling),d},E=j.isMSIE&&j.browserVersion<11?"&nbsp;":"<br>",F=function(a){return g(a)?a.nodeValue.length:a.childNodes.length},G=function(a){var b=F(a);return 0===b?!0:g(a)||1!==b||a.innerHTML!==E?d.all(a.childNodes,g)&&""===a.innerHTML?!0:!1:!0},H=function(a){i(a)||F(a)||(a.innerHTML=E)},I=function(a,c){for(;a;){if(c(a))return a;if(b(a))break;a=a.parentNode}return null},J=function(a,c){for(a=a.parentNode;a&&1===F(a);){if(c(a))return a;if(b(a))break;a=a.parentNode}return null},K=function(a,d){d=d||c.fail;var e=[];return I(a,function(a){return b(a)||e.push(a),d(a)}),e},L=function(a,b){var c=K(a);return d.last(c.filter(b))},M=function(b,c){for(var d=K(b),e=c;e;e=e.parentNode)if(a.inArray(e,d)>-1)return e;return null},N=function(a,b){b=b||c.fail;for(var d=[];a&&!b(a);)d.push(a),a=a.previousSibling;return d},O=function(a,b){b=b||c.fail;for(var d=[];a&&!b(a);)d.push(a),a=a.nextSibling;return d},P=function(a,b){var d=[];return b=b||c.ok,function e(c){a!==c&&b(c)&&d.push(c);for(var f=0,g=c.childNodes.length;g>f;f++)e(c.childNodes[f])}(a),d},Q=function(b,c){var d=b.parentNode,e=a("<"+c+">")[0];return d.insertBefore(e,b),e.appendChild(b),e},R=function(a,b){var c=b.nextSibling,d=b.parentNode;return c?d.insertBefore(a,c):d.appendChild(a),a},S=function(b,c){return a.each(c,function(a,c){b.appendChild(c)}),b},T=function(a){return 0===a.offset},U=function(a){return a.offset===F(a.node)},V=function(a){return T(a)||U(a)},W=function(a,b){for(;a&&a!==b;){if(0!==$(a))return!1;a=a.parentNode}return!0},X=function(a,b){for(;a&&a!==b;){if($(a)!==F(a.parentNode)-1)return!1;a=a.parentNode}return!0},Y=function(a,b){return T(a)&&W(a.node,b)},Z=function(a,b){return U(a)&&X(a.node,b)},$=function(a){for(var b=0;a=a.previousSibling;)b+=1;return b},_=function(a){return!!(a&&a.childNodes&&a.childNodes.length)},aa=function(a,c){var d,e;if(0===a.offset){if(b(a.node))return null;d=a.node.parentNode,e=$(a.node)}else _(a.node)?(d=a.node.childNodes[a.offset-1],e=F(d)):(d=a.node,e=c?0:a.offset-1);return{node:d,offset:e}},ba=function(a,c){var d,e;if(F(a.node)===a.offset){if(b(a.node))return null;d=a.node.parentNode,e=$(a.node)+1}else _(a.node)?(d=a.node.childNodes[a.offset],e=0):(d=a.node,e=c?F(a.node):a.offset+1);return{node:d,offset:e}},ca=function(a,b){return a.node===b.node&&a.offset===b.offset},da=function(a){if(g(a.node)||!_(a.node)||G(a.node))return!0;var b=a.node.childNodes[a.offset-1],c=a.node.childNodes[a.offset];return b&&!i(b)||c&&!i(c)?!1:!0},ea=function(a,b){for(;a;){if(b(a))return a;a=aa(a)}return null},fa=function(a,b){for(;a;){if(b(a))return a;a=ba(a)}return null},ga=function(a){if(!g(a.node))return!1;var b=a.node.nodeValue.charAt(a.offset-1);return b&&" "!==b&&b!==k},ha=function(a,b,c,d){for(var e=a;e&&(c(e),!ca(e,b));){var f=d&&a.node!==e.node&&b.node!==e.node;e=ba(e,f)}},ia=function(a,b){var d=K(b,c.eq(a));return d.map($).reverse()},ja=function(a,b){for(var c=a,d=0,e=b.length;e>d;d++)c=c.childNodes.length<=b[d]?c.childNodes[c.childNodes.length-1]:c.childNodes[b[d]];return c},ka=function(a,b){var c=b&&b.isSkipPaddingBlankHTML,d=b&&b.isNotSplitEdgePoint;if(V(a)&&(g(a.node)||d)){if(T(a))return a.node;if(U(a))return a.node.nextSibling}if(g(a.node))return a.node.splitText(a.offset);var e=a.node.childNodes[a.offset],f=R(a.node.cloneNode(!1),a.node);return S(f,O(e)),c||(H(a.node),H(f)),f},la=function(a,b,d){var e=K(b.node,c.eq(a));return e.length?1===e.length?ka(b,d):e.reduce(function(a,c){return a===b.node&&(a=ka(b,d)),ka({node:c,offset:a?m.position(a):F(c)},d)}):null},ma=function(a,b){var c,e,f=b?n:x,g=K(a.node,f),h=d.last(g)||a.node;f(h)?(c=g[g.length-2],e=h):(c=h,e=c.parentNode);var i=c&&la(c,a,{isSkipPaddingBlankHTML:b,isNotSplitEdgePoint:b});return i||e!==a.node||(i=a.node.childNodes[a.offset]),{rightNode:i,container:e}},na=function(a){return document.createElement(a)},oa=function(a){return document.createTextNode(a)},pa=function(a,b){if(a&&a.parentNode){if(a.removeNode)return a.removeNode(b);var c=a.parentNode;if(!b){var d,e,f=[];for(d=0,e=a.childNodes.length;e>d;d++)f.push(a.childNodes[d]);for(d=0,e=f.length;e>d;d++)c.insertBefore(f[d],a)}c.removeChild(a)}},qa=function(a,c){for(;a&&!b(a)&&c(a);){var d=a.parentNode;pa(a),a=d}},ra=function(a,b){if(a.nodeName.toUpperCase()===b.toUpperCase())return a;var c=na(b);return a.style.cssText&&(c.style.cssText=a.style.cssText),S(c,d.from(a.childNodes)),R(c,a),pa(a),c},sa=f("TEXTAREA"),ta=function(a,b){var c=sa(a[0])?a.val():a.html();return b?c.replace(/[\n\r]/g,""):c},ua=function(b,c){var d=ta(b);if(c){var e=/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;d=d.replace(e,function(a,b,c){c=c.toUpperCase();var d=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(c)&&!!b,e=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(c);return a+(d||e?"\n":"")}),d=a.trim(d)}return d},va=function(b){var c=a(b),d=c.offset(),e=c.outerHeight(!0);return{left:d.left,top:d.top+e}},wa=function(a,b){Object.keys(b).forEach(function(c){a.on(c,b[c])})},xa=function(a,b){Object.keys(b).forEach(function(c){a.off(c,b[c])})};return{NBSP_CHAR:k,ZERO_WIDTH_NBSP_CHAR:l,blank:E,emptyPara:"<p>"+E+"</p>",makePredByNodeName:f,isEditable:b,isControlSizing:e,isText:g,isElement:h,isVoid:i,isPara:n,isPurePara:q,isHeading:o,isInline:s,isBlock:c.not(s),isBodyInline:A,isBody:B,isParaInline:z,isList:t,isTable:r,isCell:v,isBlockquote:w,isBodyContainer:x,isAnchor:y,isDiv:f("DIV"),isLi:p,isBR:f("BR"),isSpan:f("SPAN"),isB:f("B"),isU:f("U"),isS:f("S"),isI:f("I"),isImg:f("IMG"),isTextarea:sa,isEmpty:G,isEmptyAnchor:c.and(y,G),isClosestSibling:C,withClosestSiblings:D,nodeLength:F,isLeftEdgePoint:T,isRightEdgePoint:U,isEdgePoint:V,isLeftEdgeOf:W,isRightEdgeOf:X,isLeftEdgePointOf:Y,isRightEdgePointOf:Z,prevPoint:aa,nextPoint:ba,isSamePoint:ca,isVisiblePoint:da,prevPointUntil:ea,nextPointUntil:fa,isCharPoint:ga,walkPoint:ha,ancestor:I,singleChildAncestor:J,listAncestor:K,lastAncestor:L,listNext:O,listPrev:N,listDescendant:P,commonAncestor:M,wrap:Q,insertAfter:R,appendChildNodes:S,position:$,hasChildren:_,makeOffsetPath:ia,fromOffsetPath:ja,splitTree:la,splitPoint:ma,create:na,createText:oa,remove:pa,removeWhile:qa,replace:ra,html:ua,value:ta,posFromPlaceholder:va,attachEvents:wa,detachEvents:xa}}(),n=function(b,e){var f=this,g=a.summernote.ui;return this.memos={},this.modules={},this.layoutInfo={},this.options=e,this.initialize=function(){this.layoutInfo=g.createLayout(b,e);var c=a.extend({},this.options.buttons);Object.keys(c).forEach(function(a){f.memo("button."+a,c[a])});var d=a.extend({},this.options.modules,a.summernote.plugins||{});return Object.keys(d).forEach(function(a){f.module(a,d[a],!0)}),Object.keys(this.modules).forEach(function(a){f.initializeModule(a)}),b.hide(),this},this.destroy=function(){Object.keys(this.modules).forEach(function(a){f.removeModule(a)}),Object.keys(this.memos).forEach(function(a){f.removeMemo(a)}),b.removeData("summernote"),g.removeLayout(b,this.layoutInfo)},this.code=function(a){var b=this.invoke("codeview.isActivated");return void 0===a?(this.invoke("codeview.sync"),b?this.layoutInfo.codable.val():this.layoutInfo.editable.html()):void(b?this.layoutInfo.codable.val(a):this.layoutInfo.editable.html(a))},this.triggerEvent=function(){var a=d.head(arguments),e=d.tail(d.from(arguments)),f=this.options.callbacks[c.namespaceToCamel(a,"on")];f&&f.apply(b[0],e),b.trigger("summernote."+a,e)},this.initializeModule=function(a){var d=this.modules[a];d.shouldInitialize=d.shouldInitialize||c.ok,d.shouldInitialize()&&(d.initialize&&d.initialize(),d.events&&m.attachEvents(b,d.events))},this.module=function(a,b,c){return 1===arguments.length?this.modules[a]:(this.modules[a]=new b(this),void(c||this.initializeModule(a)))},this.removeModule=function(a){var c=this.modules[a];c.shouldInitialize()&&(c.events&&m.detachEvents(b,c.events),c.destroy&&c.destroy()),delete this.modules[a],this.modules[a]=null},this.memo=function(a,b){return 1===arguments.length?this.memos[a]:void(this.memos[a]=b)},this.removeMemo=function(a){this.memos[a]&&this.memos[a].destroy&&this.memos[a].destroy(),delete this.memos[a],this.memos[a]=null},this.createInvokeHandler=function(b,c){return function(d){d.preventDefault(),f.invoke(b,c||a(d.target).data("value")||a(d.currentTarget).data("value"))}},this.invoke=function(){var a=d.head(arguments),b=d.tail(d.from(arguments)),c=a.split("."),e=c.length>1,f=e&&d.head(c),g=e?d.last(c):d.head(c),h=this.modules[f||"editor"];return!f&&this[g]?this[g].apply(this,b):h&&h[g]&&h.shouldInitialize()?h[g].apply(h,b):void 0},this.initialize()};a.summernote=a.summernote||{lang:{}},a.fn.extend({summernote:function(){var b=a.type(d.head(arguments)),c="string"===b,e="object"===b,f=e?d.head(arguments):{};f=a.extend({},a.summernote.options,f),f.langInfo=a.extend(!0,{},a.summernote.lang["en-US"],a.summernote.lang[f.lang]),this.each(function(b,c){var d=a(c);d.data("summernote")||(d.data("summernote",new n(d,f)),d.data("summernote").triggerEvent("init"))});var g=this.first();if(c&&g.length){var h=g.data("summernote");return h.invoke.apply(h,d.from(arguments))}return this}});var o=function(b,c,d,e){this.render=function(f){var g=a(b);if(d&&d.contents&&g.html(d.contents),d&&d.className&&g.addClass(d.className),d&&d.data&&a.each(d.data,function(a,b){g.attr("data-"+a,b)}),d&&d.click&&g.on("mousedown",d.click),c){var h=g.find(".note-children-container");c.forEach(function(a){a.render(h.length?h:g)})}return e&&e(g,d),d&&d.callback&&d.callback(g),f&&f.append(g),g}},p={create:function(b,c){return function(){var d=a.isArray(arguments[0])?arguments[0]:[],e="object"==typeof arguments[1]?arguments[1]:arguments[0];return e&&e.children&&(d=e.children),new o(b,d,e,c)}}},q=p.create('<div class="note-editor note-frame panel panel-default"/>'),r=p.create('<div class="note-toolbar panel-heading"/>'),s=p.create('<div class="note-editing-area"/>'),t=p.create('<textarea class="note-codable"/>'),u=p.create('<div class="note-editable panel-body" contentEditable="true"/>'),v=p.create(['<div class="note-statusbar">','  <div class="note-resizebar">','    <div class="note-icon-bar"/>','    <div class="note-icon-bar"/>','    <div class="note-icon-bar"/>',"  </div>","</div>"].join("")),w=p.create('<div class="note-editor"/>'),x=p.create('<div class="note-editable" contentEditable="true"/>'),y=p.create('<div class="note-btn-group btn-group">'),z=p.create('<button type="button" class="note-btn btn btn-default btn-sm">',function(a,b){b&&b.tooltip&&a.attr({title:b.tooltip}).tooltip({container:"body",trigger:"hover",placement:"bottom"})}),A=p.create('<div class="dropdown-menu">',function(b,c){var d=a.isArray(c.items)?c.items.map(function(a){return'<li><a href="#" data-value="'+a+'">'+a+"</a></li>"}).join(""):c.items;b.html(d)}),B=p.create('<div class="dropdown-menu note-check">',function(b,c){var d=a.isArray(c.items)?c.items.map(function(a){return'<li><a href="#" data-value="'+a+'"><i class="fa fa-check" /> '+a+"</a></li>"}).join(""):c.items;b.html(d)}),C=p.create('<div class="note-color-palette"/>',function(a,b){for(var c=[],d=0,e=b.colors.length;e>d;d++){for(var f=b.eventName,g=b.colors[d],h=[],i=0,j=g.length;j>i;i++){var k=g[i];h.push(['<button type="button" class="note-color-btn"','style="background-color:',k,'" ','data-event="',f,'" ','data-value="',k,'" ','title="',k,'" ','data-toggle="button" tabindex="-1"></button>'].join(""))}c.push('<div class="note-color-row">'+h.join("")+"</div>")}a.html(c.join("")),a.find(".note-color-btn").tooltip({container:"body",trigger:"hover",placement:"bottom"})}),D=p.create('<div class="modal" aria-hidden="false"/>',function(a,b){a.html(['<div class="modal-dialog">','<div class="modal-content">',b.title?'<div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button><h4 class="modal-title">'+b.title+"</h4></div>":"",'<div class="modal-body">'+b.body+"</div>",b.footer?'<div class="modal-footer">'+b.footer+"</div>":"","</div>","</div>"].join(""))}),E=p.create(['<div class="note-popover popover bottom in">','  <div class="arrow"/>','  <div class="popover-content note-children-container"/>',"</div>"].join("")),F={editor:q,toolbar:r,editingArea:s,codable:t,editable:u,statusbar:v,airEditor:w,airEditable:x,buttonGroup:y,button:z,dropdown:A,dropdownCheck:B,palette:C,dialog:D,popover:E,toggleBtn:function(a,b){a.toggleClass("disabled",!b),a.attr("disabled",!b)},toggleBtnActive:function(a,b){a.toggleClass("active",b)},onDialogShown:function(a,b){a.one("shown.bs.modal",b)},onDialogHidden:function(a,b){a.one("hidden.bs.modal",b)},showDialog:function(a){a.modal("show")},hideDialog:function(a){a.modal("hide")},createLayout:function(a,b){var c=(b.airMode?F.airEditor([F.editingArea([F.airEditable()])]):F.editor([F.toolbar(),F.editingArea([F.codable(),F.editable()]),F.statusbar()])).render();return c.insertAfter(a),{note:a,editor:c,toolbar:c.find(".note-toolbar"),editingArea:c.find(".note-editing-area"),editable:c.find(".note-editable"),codable:c.find(".note-codable"),statusbar:c.find(".note-statusbar")}},removeLayout:function(a,b){a.html(b.editable.html()),b.editor.remove(),a.show()}};a.extend(a.summernote.lang,{"en-US":{font:{bold:"Bold",italic:"Italic",underline:"Underline",clear:"Remove Font Style",height:"Line Height",name:"Font Family",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",size:"Font Size"},image:{image:"Picture",insert:"Insert Image",resizeFull:"Resize Full",resizeHalf:"Resize Half",resizeQuarter:"Resize Quarter",floatLeft:"Float Left",floatRight:"Float Right",floatNone:"Float None",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Drag image or text here",dropImage:"Drop image or Text",selectFromFiles:"Select from files",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Image URL",remove:"Remove Image"},video:{video:"Video",videoLink:"Video Link",insert:"Insert Video",url:"Video URL?",providers:"(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"},link:{link:"Link",insert:"Insert Link",unlink:"Unlink",edit:"Edit",textToDisplay:"Text to display",url:"To what URL should this link go?",openInNewWindow:"Open in new window"},table:{table:"Table"},hr:{insert:"Insert Horizontal Rule"},style:{style:"Style",normal:"Normal",blockquote:"Quote",pre:"Code",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6"},lists:{unordered:"Unordered list",ordered:"Ordered list"},options:{help:"Help",fullscreen:"Full Screen",codeview:"Code View"},paragraph:{paragraph:"Paragraph",outdent:"Outdent",indent:"Indent",left:"Align left",center:"Align center",right:"Align right",justify:"Justify full"},color:{recent:"Recent Color",more:"More Color",background:"Background Color",foreground:"Foreground Color",transparent:"Transparent",setTransparent:"Set transparent",reset:"Reset",resetToDefault:"Reset to default"},shortcut:{shortcuts:"Keyboard shortcuts",close:"Close",textFormatting:"Text formatting",action:"Action",paragraphFormatting:"Paragraph formatting",documentStyle:"Document Style",extraKeys:"Extra keys"},help:{insertParagraph:"Insert Paragraph",undo:"Undoes the last command",redo:"Redoes the last command",tab:"Tab",untab:"Untab",bold:"Set a bold style",italic:"Set a italic style",underline:"Set a underline style",strikethrough:"Set a strikethrough style",removeFormat:"Clean a style",justifyLeft:"Set left align",justifyCenter:"Set center align",justifyRight:"Set right align",justifyFull:"Set full align",insertUnorderedList:"Toggle unordered list",insertOrderedList:"Toggle ordered list",outdent:"Outdent on current paragraph",indent:"Indent on current paragraph",formatPara:"Change current block's format as a paragraph(P tag)",formatH1:"Change current block's format as H1",formatH2:"Change current block's format as H2",formatH3:"Change current block's format as H3",formatH4:"Change current block's format as H4",formatH5:"Change current block's format as H5",formatH6:"Change current block's format as H6",insertHorizontalRule:"Insert horizontal rule","linkDialog.show":"Show Link Dialog"},history:{undo:"Undo",redo:"Redo"},specialChar:{specialChar:"SPECIAL CHARACTERS",select:"Select Special characters"}}});var G,H=function(){var a={BACKSPACE:8,TAB:9,ENTER:13,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,NUM0:48,NUM1:49,NUM2:50,NUM3:51,NUM4:52,NUM5:53,NUM6:54,NUM7:55,NUM8:56,B:66,E:69,I:73,J:74,K:75,L:76,R:82,S:83,U:85,V:86,Y:89,Z:90,SLASH:191,LEFTBRACKET:219,BACKSLASH:220,RIGHTBRACKET:221};return{isEdit:function(b){return d.contains([a.BACKSPACE,a.TAB,a.ENTER,a.SPACe],b)},isMove:function(b){return d.contains([a.LEFT,a.UP,a.RIGHT,a.DOWN],b)},nameFromCode:c.invertObject(a),code:a}}(),I=function(){var b=function(a,b){var c,e,f=a.parentElement(),g=document.body.createTextRange(),h=d.from(f.childNodes);for(c=0;c<h.length;c++)if(!m.isText(h[c])){if(g.moveToElementText(h[c]),g.compareEndPoints("StartToStart",a)>=0)break;e=h[c]}if(0!==c&&m.isText(h[c-1])){var i=document.body.createTextRange(),j=null;i.moveToElementText(e||f),i.collapse(!e),j=e?e.nextSibling:f.firstChild;var k=a.duplicate();k.setEndPoint("StartToStart",i);for(var l=k.text.replace(/[\r\n]/g,"").length;l>j.nodeValue.length&&j.nextSibling;)l-=j.nodeValue.length,j=j.nextSibling;j.nodeValue;b&&j.nextSibling&&m.isText(j.nextSibling)&&l===j.nodeValue.length&&(l-=j.nodeValue.length,j=j.nextSibling),f=j,c=l}return{cont:f,offset:c}},e=function(a){var b=function(a,e){var f,g;if(m.isText(a)){var h=m.listPrev(a,c.not(m.isText)),i=d.last(h).previousSibling;f=i||a.parentNode,e+=d.sum(d.tail(h),m.nodeLength),g=!i}else{if(f=a.childNodes[e]||a,m.isText(f))return b(f,0);e=0,g=!1}return{node:f,collapseToStart:g,offset:e}},e=document.body.createTextRange(),f=b(a.node,a.offset);return e.moveToElementText(f.node),e.collapse(f.collapseToStart),e.moveStart("character",f.offset),e},f=function(b,g,h,i){this.sc=b,this.so=g,this.ec=h,this.eo=i;var k=function(){if(j.isW3CRangeSupport){var a=document.createRange();return a.setStart(b,g),a.setEnd(h,i),a}var c=e({node:b,offset:g});return c.setEndPoint("EndToEnd",e({node:h,offset:i})),c};this.getPoints=function(){return{sc:b,so:g,ec:h,eo:i}},this.getStartPoint=function(){return{node:b,offset:g}},this.getEndPoint=function(){return{node:h,offset:i}},this.select=function(){var a=k();if(j.isW3CRangeSupport){var b=document.getSelection();b.rangeCount>0&&b.removeAllRanges(),b.addRange(a)}else a.select();return this},this.normalize=function(){var a=function(a,b){if(m.isVisiblePoint(a)&&!m.isEdgePoint(a)||m.isVisiblePoint(a)&&m.isRightEdgePoint(a)&&!b||m.isVisiblePoint(a)&&m.isLeftEdgePoint(a)&&b||m.isVisiblePoint(a)&&m.isBlock(a.node)&&m.isEmpty(a.node))return a;var c=m.ancestor(a.node,m.isBlock);if((m.isLeftEdgePointOf(a,c)||m.isVoid(m.prevPoint(a).node))&&!b||(m.isRightEdgePointOf(a,c)||m.isVoid(m.nextPoint(a).node))&&b){if(m.isVisiblePoint(a))return a;b=!b}var d=b?m.nextPointUntil(m.nextPoint(a),m.isVisiblePoint):m.prevPointUntil(m.prevPoint(a),m.isVisiblePoint);return d||a},b=a(this.getEndPoint(),!1),c=this.isCollapsed()?b:a(this.getStartPoint(),!0);return new f(c.node,c.offset,b.node,b.offset)},this.nodes=function(a,b){a=a||c.ok;var e=b&&b.includeAncestor,f=b&&b.fullyContains,g=this.getStartPoint(),h=this.getEndPoint(),i=[],j=[];return m.walkPoint(g,h,function(b){if(!m.isEditable(b.node)){var c;f?(m.isLeftEdgePoint(b)&&j.push(b.node),m.isRightEdgePoint(b)&&d.contains(j,b.node)&&(c=b.node)):c=e?m.ancestor(b.node,a):b.node,c&&a(c)&&i.push(c)}},!0),d.unique(i)},this.commonAncestor=function(){return m.commonAncestor(b,h)},this.expand=function(a){var c=m.ancestor(b,a),d=m.ancestor(h,a);if(!c&&!d)return new f(b,g,h,i);var e=this.getPoints();return c&&(e.sc=c,e.so=0),d&&(e.ec=d,e.eo=m.nodeLength(d)),new f(e.sc,e.so,e.ec,e.eo)},this.collapse=function(a){return a?new f(b,g,b,g):new f(h,i,h,i)},this.splitText=function(){var a=b===h,c=this.getPoints();return m.isText(h)&&!m.isEdgePoint(this.getEndPoint())&&h.splitText(i),m.isText(b)&&!m.isEdgePoint(this.getStartPoint())&&(c.sc=b.splitText(g),c.so=0,a&&(c.ec=c.sc,c.eo=i-g)),new f(c.sc,c.so,c.ec,c.eo)},this.deleteContents=function(){if(this.isCollapsed())return this;var b=this.splitText(),c=b.nodes(null,{fullyContains:!0}),e=m.prevPointUntil(b.getStartPoint(),function(a){return!d.contains(c,a.node)}),g=[];return a.each(c,function(a,b){var c=b.parentNode;e.node!==c&&1===m.nodeLength(c)&&g.push(c),m.remove(b,!1)}),a.each(g,function(a,b){m.remove(b,!1)}),new f(e.node,e.offset,e.node,e.offset).normalize()};var l=function(a){return function(){var c=m.ancestor(b,a);return!!c&&c===m.ancestor(h,a)}};this.isOnEditable=l(m.isEditable),this.isOnList=l(m.isList),this.isOnAnchor=l(m.isAnchor),this.isOnCell=l(m.isCell),this.isLeftEdgeOf=function(a){if(!m.isLeftEdgePoint(this.getStartPoint()))return!1;var b=m.ancestor(this.sc,a);return b&&m.isLeftEdgeOf(this.sc,b)},this.isCollapsed=function(){return b===h&&g===i},this.wrapBodyInlineWithPara=function(){if(m.isBodyContainer(b)&&m.isEmpty(b))return b.innerHTML=m.emptyPara,new f(b.firstChild,0,b.firstChild,0);var a=this.normalize();if(m.isParaInline(b)||m.isPara(b))return a;var e;if(m.isInline(a.sc)){var g=m.listAncestor(a.sc,c.not(m.isInline));e=d.last(g),m.isInline(e)||(e=g[g.length-2]||a.sc.childNodes[a.so])}else e=a.sc.childNodes[a.so>0?a.so-1:0];var h=m.listPrev(e,m.isParaInline).reverse();if(h=h.concat(m.listNext(e.nextSibling,m.isParaInline)),h.length){var i=m.wrap(d.head(h),"p");m.appendChildNodes(i,d.tail(h))}return this.normalize()},this.insertNode=function(a){var b=this.wrapBodyInlineWithPara().deleteContents(),c=m.splitPoint(b.getStartPoint(),m.isInline(a));return c.rightNode?c.rightNode.parentNode.insertBefore(a,c.rightNode):c.container.appendChild(a),a},this.pasteHTML=function(b){var c=a("<div></div>").html(b)[0],e=d.from(c.childNodes),f=this.wrapBodyInlineWithPara().deleteContents();return e.reverse().map(function(a){return f.insertNode(a)}).reverse()},this.toString=function(){var a=k();return j.isW3CRangeSupport?a.toString():a.text},this.getWordRange=function(a){var b=this.getEndPoint();if(!m.isCharPoint(b))return this;var c=m.prevPointUntil(b,function(a){return!m.isCharPoint(a)});return a&&(b=m.nextPointUntil(b,function(a){return!m.isCharPoint(a)})),new f(c.node,c.offset,b.node,b.offset)},this.bookmark=function(a){return{s:{path:m.makeOffsetPath(a,b),offset:g},e:{path:m.makeOffsetPath(a,h),offset:i}}},this.paraBookmark=function(a){return{s:{path:d.tail(m.makeOffsetPath(d.head(a),b)),offset:g},e:{path:d.tail(m.makeOffsetPath(d.last(a),h)),offset:i}}},this.getClientRects=function(){var a=k();return a.getClientRects()}};return{create:function(a,c,d,e){if(arguments.length)2===arguments.length&&(d=a,e=c);else if(j.isW3CRangeSupport){var g=document.getSelection();if(!g||0===g.rangeCount)return null;if(m.isBody(g.anchorNode))return null;var h=g.getRangeAt(0);a=h.startContainer,c=h.startOffset,d=h.endContainer,e=h.endOffset}else{var i=document.selection.createRange(),k=i.duplicate();k.collapse(!1);var l=i;l.collapse(!0);var n=b(l,!0),o=b(k,!1);m.isText(n.node)&&m.isLeftEdgePoint(n)&&m.isTextNode(o.node)&&m.isRightEdgePoint(o)&&o.node.nextSibling===n.node&&(n=o),a=n.cont,c=n.offset,d=o.cont,e=o.offset}return new f(a,c,d,e)},createFromNode:function(a){var b=a,c=0,d=a,e=m.nodeLength(d);return m.isVoid(b)&&(c=m.listPrev(b).length-1,b=b.parentNode),m.isBR(d)?(e=m.listPrev(d).length-1,d=d.parentNode):m.isVoid(d)&&(e=m.listPrev(d).length,d=d.parentNode),this.create(b,c,d,e)},createFromNodeBefore:function(a){return this.createFromNode(a).collapse(!0)},createFromNodeAfter:function(a){return this.createFromNode(a).collapse()},createFromBookmark:function(a,b){var c=m.fromOffsetPath(a,b.s.path),d=b.s.offset,e=m.fromOffsetPath(a,b.e.path),g=b.e.offset;return new f(c,d,e,g)},createFromParaBookmark:function(a,b){var c=a.s.offset,e=a.e.offset,g=m.fromOffsetPath(d.head(b),a.s.path),h=m.fromOffsetPath(d.last(b),a.e.path);return new f(g,c,h,e)}}}(),J=function(){var b=function(b){return a.Deferred(function(c){a.extend(new FileReader,{onload:function(a){var b=a.target.result;c.resolve(b)},onerror:function(){c.reject(this)}}).readAsDataURL(b)}).promise()},c=function(b,c){return a.Deferred(function(d){var e=a("<img>");e.one("load",function(){e.off("error abort"),d.resolve(e)}).one("error abort",function(){e.off("load").detach(),d.reject(e)}).css({display:"none"}).appendTo(document.body).attr({src:b,"data-filename":c})}).promise()};return{readFileAsDataURL:b,createImage:c}}(),K=function(a){var b=[],c=-1,d=a[0],e=function(){var b=I.create(),c={s:{path:[],offset:0},e:{path:[],offset:0}};return{contents:a.html(),bookmark:b?b.bookmark(d):c}},f=function(b){null!==b.contents&&a.html(b.contents),null!==b.bookmark&&I.createFromBookmark(d,b.bookmark).select()};this.rewind=function(){a.html()!==b[c].contents&&this.recordUndo(),c=0,f(b[c])},this.reset=function(){b=[],c=-1,a.html(""),this.recordUndo()},this.undo=function(){a.html()!==b[c].contents&&this.recordUndo(),c>0&&(c--,f(b[c]))},this.redo=function(){b.length-1>c&&(c++,f(b[c]))},this.recordUndo=function(){c++,b.length>c&&(b=b.slice(0,c)),b.push(e())}},L=function(){var b=function(b,c){if(j.jqueryVersion<1.9){var d={};return a.each(c,function(a,c){d[c]=b.css(c)}),d}return b.css.call(b,c)};this.fromNode=function(a){var c=["font-family","font-size","text-align","list-style-type","line-height"],d=b(a,c)||{};return d["font-size"]=parseInt(d["font-size"],10),d},this.stylePara=function(b,c){a.each(b.nodes(m.isPara,{includeAncestor:!0}),function(b,d){a(d).css(c)})},this.styleNodes=function(b,e){b=b.splitText();var f=e&&e.nodeName||"SPAN",g=!(!e||!e.expandClosestSibling),h=!(!e||!e.onlyPartialContains);if(b.isCollapsed())return[b.insertNode(m.create(f))];var i=m.makePredByNodeName(f),j=b.nodes(m.isText,{fullyContains:!0}).map(function(a){return m.singleChildAncestor(a,i)||m.wrap(a,f)});if(g){if(h){var k=b.nodes();i=c.and(i,function(a){return d.contains(k,a)})}return j.map(function(b){var c=m.withClosestSiblings(b,i),e=d.head(c),f=d.tail(c);return a.each(f,function(a,b){m.appendChildNodes(e,b.childNodes),m.remove(b)}),d.head(c)})}return j},this.current=function(b){var c=a(m.isElement(b.sc)?b.sc:b.sc.parentNode),d=this.fromNode(c);try{d=a.extend(d,{"font-bold":document.queryCommandState("bold")?"bold":"normal","font-italic":document.queryCommandState("italic")?"italic":"normal","font-underline":document.queryCommandState("underline")?"underline":"normal","font-subscript":document.queryCommandState("subscript")?"subscript":"normal","font-superscript":document.queryCommandState("superscript")?"superscript":"normal","font-strikethrough":document.queryCommandState("strikeThrough")?"strikethrough":"normal"})}catch(e){}if(b.isOnList()){var f=["circle","disc","disc-leading-zero","square"],g=a.inArray(d["list-style-type"],f)>-1;d["list-style"]=g?"unordered":"ordered"}else d["list-style"]="none";var h=m.ancestor(b.sc,m.isPara);if(h&&h.style["line-height"])d["line-height"]=h.style.lineHeight;else{var i=parseInt(d["line-height"],10)/parseInt(d["font-size"],10);d["line-height"]=i.toFixed(1)}return d.anchor=b.isOnAnchor()&&m.ancestor(b.sc,m.isAnchor),d.ancestors=m.listAncestor(b.sc,m.isEditable),d.range=b,d}},M=function(){this.insertOrderedList=function(){this.toggleList("OL")},this.insertUnorderedList=function(){this.toggleList("UL")},this.indent=function(){var b=this,e=I.create().wrapBodyInlineWithPara(),f=e.nodes(m.isPara,{includeAncestor:!0}),g=d.clusterBy(f,c.peq2("parentNode"));a.each(g,function(c,e){var f=d.head(e);m.isLi(f)?b.wrapList(e,f.parentNode.nodeName):a.each(e,function(b,c){a(c).css("marginLeft",function(a,b){return(parseInt(b,10)||0)+25})})}),e.select()},this.outdent=function(){var b=this,e=I.create().wrapBodyInlineWithPara(),f=e.nodes(m.isPara,{includeAncestor:!0}),g=d.clusterBy(f,c.peq2("parentNode"));
a.each(g,function(c,e){var f=d.head(e);m.isLi(f)?b.releaseList([e]):a.each(e,function(b,c){a(c).css("marginLeft",function(a,b){return b=parseInt(b,10)||0,b>25?b-25:""})})}),e.select()},this.toggleList=function(b){var e=this,f=I.create().wrapBodyInlineWithPara(),g=f.nodes(m.isPara,{includeAncestor:!0}),h=f.paraBookmark(g),i=d.clusterBy(g,c.peq2("parentNode"));if(d.find(g,m.isPurePara)){var j=[];a.each(i,function(a,c){j=j.concat(e.wrapList(c,b))}),g=j}else{var k=f.nodes(m.isList,{includeAncestor:!0}).filter(function(c){return!a.nodeName(c,b)});k.length?a.each(k,function(a,c){m.replace(c,b)}):g=this.releaseList(i,!0)}I.createFromParaBookmark(h,g).select()},this.wrapList=function(a,b){var c=d.head(a),e=d.last(a),f=m.isList(c.previousSibling)&&c.previousSibling,g=m.isList(e.nextSibling)&&e.nextSibling,h=f||m.insertAfter(m.create(b||"UL"),e);return a=a.map(function(a){return m.isPurePara(a)?m.replace(a,"LI"):a}),m.appendChildNodes(h,a),g&&(m.appendChildNodes(h,d.from(g.childNodes)),m.remove(g)),a},this.releaseList=function(b,c){var e=[];return a.each(b,function(b,f){var g=d.head(f),h=d.last(f),i=c?m.lastAncestor(g,m.isList):g.parentNode,j=i.childNodes.length>1?m.splitTree(i,{node:h.parentNode,offset:m.position(h)+1},{isSkipPaddingBlankHTML:!0}):null,k=m.splitTree(i,{node:g.parentNode,offset:m.position(g)},{isSkipPaddingBlankHTML:!0});f=c?m.listDescendant(k,m.isLi):d.from(k.childNodes).filter(m.isLi),(c||!m.isList(i.parentNode))&&(f=f.map(function(a){return m.replace(a,"P")})),a.each(d.from(f).reverse(),function(a,b){m.insertAfter(b,i)});var l=d.compact([i,k,j]);a.each(l,function(b,c){var d=[c].concat(m.listDescendant(c,m.isList));a.each(d.reverse(),function(a,b){m.nodeLength(b)||m.remove(b,!0)})}),e=e.concat(f)}),e}},N=function(){var b=new M;this.insertTab=function(a,b,c){var d=m.createText(new Array(c+1).join(m.NBSP_CHAR));b=b.deleteContents(),b.insertNode(d,!0),b=I.create(d,c),b.select()},this.insertParagraph=function(){var c=I.create();c=c.deleteContents(),c=c.wrapBodyInlineWithPara();var d,e=m.ancestor(c.sc,m.isPara);if(e){if(m.isEmpty(e)&&m.isLi(e))return void b.toggleList(e.parentNode.nodeName);if(m.isEmpty(e)&&m.isPara(e)&&m.isBlockquote(e.parentNode))m.insertAfter(e,e.parentNode),d=e;else{d=m.splitTree(e,c.getStartPoint());var f=m.listDescendant(e,m.isEmptyAnchor);f=f.concat(m.listDescendant(d,m.isEmptyAnchor)),a.each(f,function(a,b){m.remove(b)}),m.isHeading(d)&&m.isEmpty(d)&&(d=m.replace(d,"p"))}}else{var g=c.sc.childNodes[c.so];d=a(m.emptyPara)[0],g?c.sc.insertBefore(d,g):c.sc.appendChild(d)}I.create(d,0).normalize().select()}},O=function(){this.tab=function(a,b){var c=m.ancestor(a.commonAncestor(),m.isCell),e=m.ancestor(c,m.isTable),f=m.listDescendant(e,m.isCell),g=d[b?"prev":"next"](f,c);g&&I.create(g,0).select()},this.createTable=function(b,c,d){for(var e,f=[],g=0;b>g;g++)f.push("<td>"+m.blank+"</td>");e=f.join("");for(var h,i=[],j=0;c>j;j++)i.push("<tr>"+e+"</tr>");h=i.join("");var k=a("<table>"+h+"</table>");return d&&d.tableClassName&&k.addClass(d.tableClassName),k[0]}},P="bogus",Q=function(b){var c=this,e=b.layoutInfo.note,f=b.layoutInfo.editor,g=b.layoutInfo.editable,h=b.options,i=new L,k=new O,l=new N,n=new M,o=new K(g);this.initialize=function(){g.on("keydown",function(a){a.keyCode===H.code.ENTER&&b.triggerEvent("enter",a),b.triggerEvent("keydown",a)}).on("keyup",function(a){b.triggerEvent("keyup",a)}).on("focus",function(a){b.triggerEvent("focus",a)}).on("blur",function(a){b.triggerEvent("blur",a)}).on("mousedown",function(a){b.triggerEvent("mousedown",a)}).on("mouseup",function(a){b.triggerEvent("mouseup",a)}).on("input",function(a){b.triggerEvent("change",a)}).on("scroll",function(a){b.triggerEvent("scroll",a)}).on("paste",function(a){b.triggerEvent("paste",a)}),f.on("focusin",function(a){b.triggerEvent("focusin",a)}).on("focusout",function(a){b.triggerEvent("focusout",a)}),h.shortcuts&&this.bindKeyMap(),!h.airMode&&h.height&&g.outerHeight(h.height),g.html(e.html()),o.recordUndo()},this.destroy=function(){g.off()},this.bindKeyMap=function(){var a=h.keyMap[j.isMac?"mac":"pc"];g.on("keydown",function(d){var e=[];d.metaKey&&e.push("CMD"),d.ctrlKey&&!d.altKey&&e.push("CTRL"),d.shiftKey&&e.push("SHIFT");var f=H.nameFromCode[d.keyCode];f&&e.push(f);var g=a[e.join("+")];g?(d.preventDefault(),b.invoke(g)):H.isEdit(d.keyCode)&&c.afterCommand()})},this.createRange=function(){return this.focus(),I.create()},this.saveRange=function(a){this.focus(),g.data("range",I.create()),a&&I.create().collapse().select()},this.restoreRange=function(){var a=g.data("range");a&&(a.select(),this.focus())},this.saveTarget=function(a){g.data("target",a)},this.clearTarget=function(){g.removeData("target")},this.restoreTarget=function(){return g.data("target")},this.currentStyle=function(){var a=I.create();return a&&(a=a.normalize()),a?i.current(a):i.fromNode(g)},this.styleFromNode=function(a){return i.fromNode(a)},this.undo=function(){b.triggerEvent("before.command",g.html()),o.undo(),b.triggerEvent("change",g.html())},b.memo("help.undo",h.langInfo.help.undo),this.redo=function(){b.triggerEvent("before.command",g.html()),o.redo(),b.triggerEvent("change",g.html())},b.memo("help.redo",h.langInfo.help.redo),this.reset=function(){b.triggerEvent("before.command",g.html()),o.reset(),b.triggerEvent("change",g.html())},this.rewind=function(){b.triggerEvent("before.command",g.html()),o.rewind(),b.triggerEvent("change",g.html())};for(var p=this.beforeCommand=function(){b.triggerEvent("before.command",g.html()),c.focus()},q=this.afterCommand=function(a){o.recordUndo(),a||b.triggerEvent("change",g.html())},r=["bold","italic","underline","strikethrough","superscript","subscript","justifyLeft","justifyCenter","justifyRight","justifyFull","formatBlock","removeFormat","backColor","foreColor","fontName"],s=0,t=r.length;t>s;s++)this[r[s]]=function(a){return function(b){p(),document.execCommand(a,!1,b),q(!0)}}(r[s]),b.memo("help."+r[s],h.langInfo.help[r[s]]);this.tab=function(){var a=this.createRange();a.isCollapsed()&&a.isOnCell()?k.tab(a):(p(),l.insertTab(g,a,h.tabSize),q())},b.memo("help.tab",h.langInfo.help.tab),this.untab=function(){var a=this.createRange();a.isCollapsed()&&a.isOnCell()&&k.tab(a,!0)},b.memo("help.untab",h.langInfo.help.untab),this.wrapCommand=function(a){return function(){p(),a.apply(c,arguments),q()}},this.insertParagraph=this.wrapCommand(function(){l.insertParagraph(g)}),b.memo("help.insertParagraph",h.langInfo.help.insertParagraph),this.insertOrderedList=this.wrapCommand(function(){n.insertOrderedList(g)}),b.memo("help.insertOrderedList",h.langInfo.help.insertOrderedList),this.insertUnorderedList=this.wrapCommand(function(){n.insertUnorderedList(g)}),b.memo("help.insertUnorderedList",h.langInfo.help.insertUnorderedList),this.indent=this.wrapCommand(function(){n.indent(g)}),b.memo("help.indent",h.langInfo.help.indent),this.outdent=this.wrapCommand(function(){n.outdent(g)}),b.memo("help.outdent",h.langInfo.help.outdent),this.insertImage=function(a,c){J.createImage(a,c).then(function(a){p(),a.css({display:"",width:Math.min(g.width(),a.width())}),I.create().insertNode(a[0]),I.createFromNodeAfter(a[0]).select(),q()}).fail(function(){b.triggerEvent("image.upload.error")})},this.insertNode=this.wrapCommand(function(a){I.create().insertNode(a),I.createFromNodeAfter(a).select()}),this.insertText=this.wrapCommand(function(a){var b=I.create().insertNode(m.createText(a));I.create(b,m.nodeLength(b)).select()}),this.getSelectedText=function(){var a=this.createRange();return a.isOnAnchor()&&(a=I.createFromNode(m.ancestor(a.sc,m.isAnchor))),a.toString()},this.pasteHTML=this.wrapCommand(function(a){var b=I.create().pasteHTML(a);I.createFromNodeAfter(d.last(b)).select()}),this.formatBlock=this.wrapCommand(function(a){a=j.isMSIE?"<"+a+">":a,document.execCommand("FormatBlock",!1,a)}),this.formatPara=function(){this.formatBlock("P")},b.memo("help.formatPara",h.langInfo.help.formatPara);for(var s=1;6>=s;s++)this["formatH"+s]=function(a){return function(){this.formatBlock("H"+a)}}(s),b.memo("help.formatH"+s,h.langInfo.help["formatH"+s]);this.fontSize=function(b){var c=I.create();if(c.isCollapsed()){var e=i.styleNodes(c),f=d.head(e);a(e).css({"font-size":b+"px"}),f&&!m.nodeLength(f)&&(f.innerHTML=m.ZERO_WIDTH_NBSP_CHAR,I.createFromNodeAfter(f.firstChild).select(),g.data(P,f))}else p(),a(i.styleNodes(c)).css({"font-size":b+"px"}),q()},this.insertHorizontalRule=this.wrapCommand(function(){var b=I.create(),c=b.insertNode(a("<HR/>")[0]);c.nextSibling&&I.create(c.nextSibling,0).normalize().select()}),b.memo("help.insertHorizontalRule",h.langInfo.help.insertHorizontalRule),this.removeBogus=function(){var a=g.data(P);if(a){var b=d.find(d.from(a.childNodes),m.isText),c=b.nodeValue.indexOf(m.ZERO_WIDTH_NBSP_CHAR);-1!==c&&b.deleteData(c,1),m.isEmpty(a)&&m.remove(a),g.removeData(P)}},this.lineHeight=this.wrapCommand(function(a){i.stylePara(I.create(),{lineHeight:a})}),this.unlink=function(){var a=this.createRange();if(a.isOnAnchor()){var b=m.ancestor(a.sc,m.isAnchor);a=I.createFromNode(b),a.select(),p(),document.execCommand("unlink"),q()}},this.createLink=this.wrapCommand(function(b){var c=b.url,e=b.text,f=b.isNewWindow,g=b.range||this.createRange(),j=g.toString()!==e;h.onCreateLink&&(c=h.onCreateLink(c));var k=[];if(j){var l=g.insertNode(a("<A>"+e+"</A>")[0]);k.push(l)}else k=i.styleNodes(g,{nodeName:"A",expandClosestSibling:!0,onlyPartialContains:!0});a.each(k,function(b,d){a(d).attr("href",c),f?a(d).attr("target","_blank"):a(d).removeAttr("target")});var m=I.createFromNodeBefore(d.head(k)),n=m.getStartPoint(),o=I.createFromNodeAfter(d.last(k)),p=o.getEndPoint();I.create(n.node,n.offset,p.node,p.offset).select()}),this.getLinkInfo=function(){this.focus();var b=I.create().expand(m.isAnchor),c=a(d.head(b.nodes(m.isAnchor)));return{range:b,text:b.toString(),isNewWindow:c.length?"_blank"===c.attr("target"):!1,url:c.length?c.attr("href"):""}},this.color=this.wrapCommand(function(a){var b=a.foreColor,c=a.backColor;b&&document.execCommand("foreColor",!1,b),c&&document.execCommand("backColor",!1,c)}),this.insertTable=this.wrapCommand(function(a){var b=a.split("x"),c=I.create().deleteContents();c.insertNode(k.createTable(b[0],b[1],h))}),this.floatMe=this.wrapCommand(function(b){var c=a(this.restoreTarget());c.css("float",b)}),this.resize=this.wrapCommand(function(b){var c=a(this.restoreTarget());c.css({width:100*b+"%",height:""})}),this.resizeTo=function(a,b,c){var d;if(c){var e=a.y/a.x,f=b.data("ratio");d={width:f>e?a.x:a.y/f,height:f>e?a.x*f:a.y}}else d={width:a.x,height:a.y};b.css(d)},this.removeMedia=this.wrapCommand(function(){var c=a(this.restoreTarget()).detach();b.triggerEvent("media.delete",c,g)}),this.focus=function(){g.is(":focus")||(g.focus(),j.isFF&&I.createFromNode(g[0]).normalize().collapse().select())},this.isEmpty=function(){return m.isEmpty(g[0])||m.emptyPara===g.html()}},R=function(b){var c=this,e=b.layoutInfo.editable;this.events={"summernote.keydown":function(a,d){c.needKeydownHook()&&(d.ctrlKey||d.metaKey)&&d.keyCode===H.code.V&&(b.invoke("editor.saveRange"),c.$paste.focus(),setTimeout(function(){c.pasteByHook()},0))}},this.needKeydownHook=function(){return j.isMSIE&&j.browserVersion>10||j.isFF},this.initialize=function(){this.needKeydownHook()?(this.$paste=a("<div />").attr("contenteditable",!0).css({position:"absolute",left:-1e5,opacity:0}),e.before(this.$paste),this.$paste.on("paste",function(a){b.triggerEvent("paste",a)})):e.on("paste",this.pasteByEvent)},this.destroy=function(){this.needKeydownHook()&&(this.$paste.remove(),this.$paste=null)},this.pasteByHook=function(){var c=this.$paste[0].firstChild;if(m.isImg(c)){for(var d=c.src,e=atob(d.split(",")[1]),f=new Uint8Array(e.length),g=0;g<e.length;g++)f[g]=e.charCodeAt(g);var h=new Blob([f],{type:"image/png"});h.name="clipboard.png",b.invoke("editor.restoreRange"),b.invoke("editor.focus"),b.invoke("imageDialog.insertImages",[h])}else{var i=a("<div />").html(this.$paste.html()).html();b.invoke("editor.restoreRange"),b.invoke("editor.focus"),i&&b.invoke("editor.pasteHTML",i)}this.$paste.empty()},this.pasteByEvent=function(a){var c=a.originalEvent.clipboardData;if(c&&c.items&&c.items.length){var e=d.head(c.items);"file"===e.kind&&-1!==e.type.indexOf("image/")&&b.invoke("imageDialog.insertImages",[e.getAsFile()]),b.invoke("editor.afterCommand")}}},S=function(b){var c=a(document),d=b.layoutInfo.editor,e=b.layoutInfo.editable,f=b.options,g=f.langInfo,h=a(['<div class="note-dropzone">','  <div class="note-dropzone-message"/>',"</div>"].join("")).prependTo(d);this.initialize=function(){f.disableDragAndDrop?c.on("drop",function(a){a.preventDefault()}):this.attachDragAndDropEvent()},this.attachDragAndDropEvent=function(){var f=a(),i=h.find(".note-dropzone-message");c.on("dragenter",function(a){var c=b.invoke("codeview.isActivated"),e=d.width()>0&&d.height()>0;c||f.length||!e||(d.addClass("dragover"),h.width(d.width()),h.height(d.height()),i.text(g.image.dragImageHere)),f=f.add(a.target)}).on("dragleave",function(a){f=f.not(a.target),f.length||d.removeClass("dragover")}).on("drop",function(){f=a(),d.removeClass("dragover")}),h.on("dragenter",function(){h.addClass("hover"),i.text(g.image.dropImage)}).on("dragleave",function(){h.removeClass("hover"),i.text(g.image.dragImageHere)}),h.on("drop",function(c){var d=c.originalEvent.dataTransfer;d&&d.files&&d.files.length?(c.preventDefault(),e.focus(),b.invoke("imageDialog.insertImages",d.files)):a.each(d.types,function(c,e){var f=d.getData(e);e.toLowerCase().indexOf("text")>-1?b.invoke("editor.pasteHTML",f):a(f).each(function(){b.invoke("editor.insertNode",this)})})}).on("dragover",!1)}};j.hasCodeMirror&&(j.isSupportAmd?require(["CodeMirror"],function(a){G=a}):G=window.CodeMirror);var T=function(a){var b=a.layoutInfo.editor,c=a.layoutInfo.editable,d=a.layoutInfo.codable,e=a.options;this.sync=function(){var a=this.isActivated();a&&j.hasCodeMirror&&d.data("cmEditor").save()},this.isActivated=function(){return b.hasClass("codeview")},this.toggle=function(){this.isActivated()?this.deactivate():this.activate()},this.activate=function(){if(d.val(m.html(c,e.prettifyHtml)),d.height(c.height()),a.invoke("toolbar.updateCodeview",!0),b.addClass("codeview"),d.focus(),j.hasCodeMirror){var f=G.fromTextArea(d[0],e.codemirror);if(e.codemirror.tern){var g=new G.TernServer(e.codemirror.tern);f.ternServer=g,f.on("cursorActivity",function(a){g.updateArgHints(a)})}f.setSize(null,c.outerHeight()),d.data("cmEditor",f)}},this.deactivate=function(){if(j.hasCodeMirror){var f=d.data("cmEditor");d.val(f.getValue()),f.toTextArea()}var g=m.value(d,e.prettifyHtml)||m.emptyPara,h=c.html()!==g;c.html(g),c.height(e.height?d.height():"auto"),b.removeClass("codeview"),h&&a.triggerEvent("change",c.html(),c),c.focus(),a.invoke("toolbar.updateCodeview",!1)}},U=24,V=function(b){var c=a(document),d=b.layoutInfo.statusbar,e=b.layoutInfo.editable,f=b.options;this.initialize=function(){f.airMode||f.disableResizeEditor||d.on("mousedown",function(a){a.preventDefault(),a.stopPropagation();var b=e.offset().top-c.scrollTop();c.on("mousemove",function(a){var c=a.clientY-(b+U);c=f.minheight>0?Math.max(c,f.minheight):c,c=f.maxHeight>0?Math.min(c,f.maxHeight):c,e.height(c)}).one("mouseup",function(){c.off("mousemove")})})},this.destroy=function(){d.off()}},W=function(b){var c=b.layoutInfo.editor,d=b.layoutInfo.toolbar,e=b.layoutInfo.editable,f=b.layoutInfo.codable,g=a(window),h=a("html, body");this.toggle=function(){var a=function(a){e.css("height",a.h),f.css("height",a.h),f.data("cmeditor")&&f.data("cmeditor").setsize(null,a.h)};c.toggleClass("fullscreen");var i=c.hasClass("fullscreen");i?(e.data("orgHeight",e.css("height")),g.on("resize",function(){a({h:g.height()-d.outerHeight()})}).trigger("resize"),h.css("overflow","hidden")):(g.off("resize"),a({h:e.data("orgHeight")}),h.css("overflow","visible")),b.invoke("toolbar.updateFullscreen",i)}},X=function(b){var c=this,d=a(document),e=b.layoutInfo.editingArea,f=b.options;this.events={"summernote.mousedown":function(a,b){c.update(b.target)&&b.preventDefault()},"summernote.keyup summernote.scroll summernote.change summernote.dialog.shown":function(){c.update()}},this.initialize=function(){this.$handle=a(['<div class="note-handle">','<div class="note-control-selection">','<div class="note-control-selection-bg"></div>','<div class="note-control-holder note-control-nw"></div>','<div class="note-control-holder note-control-ne"></div>','<div class="note-control-holder note-control-sw"></div>','<div class="',f.disableResizeImage?"note-control-holder":"note-control-sizing",' note-control-se"></div>',f.disableResizeImage?"":'<div class="note-control-selection-info"></div>',"</div>","</div>"].join("")).prependTo(e),this.$handle.on("mousedown",function(a){if(m.isControlSizing(a.target)){a.preventDefault(),a.stopPropagation();var e=c.$handle.find(".note-control-selection").data("target"),f=e.offset(),g=d.scrollTop();d.on("mousemove",function(a){b.invoke("editor.resizeTo",{x:a.clientX-f.left,y:a.clientY-(f.top-g)},e,!a.shiftKey),c.update(e[0])}).one("mouseup",function(a){a.preventDefault(),d.off("mousemove"),b.invoke("editor.afterCommand")}),e.data("ratio")||e.data("ratio",e.height()/e.width())}})},this.destroy=function(){this.$handle.remove()},this.update=function(c){var d=m.isImg(c),e=this.$handle.find(".note-control-selection");if(b.invoke("imagePopover.update",c),d){var f=a(c),g=f.position(),h={w:f.outerWidth(!0),h:f.outerHeight(!0)};e.css({display:"block",left:g.left,top:g.top,width:h.w,height:h.h}).data("target",f);var i=h.w+"x"+h.h;e.find(".note-control-selection-info").text(i),b.invoke("editor.saveTarget",c)}else this.hide();return d},this.hide=function(){b.invoke("editor.clearTarget"),this.$handle.children().hide()}},Y=function(b){var c=this,e=/^(https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.|(?:mailto:)?[A-Z0-9._%+-]+@)(.+)$/i;this.events={"summernote.keyup":function(a,b){c.handleKeyup(b)},"summernote.keydown":function(a,b){c.handleKeydown(b)}},this.initialize=function(){this.lastWordRange=null},this.destroy=function(){this.lastWordRange=null},this.replace=function(){if(this.lastWordRange){var a=this.lastWordRange.toString();if(e.test(a)){var c=this.nodeFromKeyword(a);this.lastWordRange.insertNode(c),this.lastWordRange=null,b.invoke("editor.focus")}}},this.nodeFromKeyword=function(b){return a("<a />").html(b).attr("href",b)[0]},this.handleKeydown=function(a){if(d.contains([H.code.ENTER,H.code.SPACE],a.keyCode)){var c=b.invoke("editor.createRange").getWordRange();this.lastWordRange=c}},this.handleKeyup=function(a){d.contains([H.code.ENTER,H.code.SPACE],a.keyCode)&&this.replace()}},Z=function(a){var b=a.layoutInfo.note;this.events={"summernote.change":function(){b.val(a.invoke("code"))}},this.shouldInitialize=function(){return m.isTextarea(b[0])}},$=function(b){var e=this,f=a.summernote.ui,g=b.layoutInfo.toolbar,h=b.options,i=h.langInfo,k=c.invertObject(h.keyMap[j.isMac?"mac":"pc"]),l=this.representShortcut=function(a){var b=k[a];return j.isMac&&(b=b.replace("CMD","⌘").replace("SHIFT","⇧")),b=b.replace("BACKSLASH","\\").replace("SLASH","/").replace("LEFTBRACKET","[").replace("RIGHTBRACKET","]")," ("+b+")"};this.initialize=function(){this.addToolbarButtons(),this.addImagePopoverButtons(),this.addLinkPopoverButtons()},this.addToolbarButtons=function(){b.memo("button.style",function(){return f.buttonGroup([f.button({className:"dropdown-toggle",contents:'<i class="fa fa-magic"/> <span class="caret"/>',tooltip:i.style.style,data:{toggle:"dropdown"}}),f.dropdown({className:"dropdown-style",items:b.options.styleTags,click:b.createInvokeHandler("editor.formatBlock")})]).render()}),b.memo("button.bold",function(){return f.button({className:"note-btn-bold",contents:'<i class="fa fa-bold"/>',tooltip:i.font.bold+l("bold"),click:b.createInvokeHandler("editor.bold")}).render()}),b.memo("button.italic",function(){return f.button({className:"note-btn-italic",contents:'<i class="fa fa-italic"/>',tooltip:i.font.italic+l("italic"),click:b.createInvokeHandler("editor.italic")}).render()}),b.memo("button.underline",function(){return f.button({className:"note-btn-underline",contents:'<i class="fa fa-underline"/>',tooltip:i.font.underline+l("underline"),click:b.createInvokeHandler("editor.underline")}).render()}),b.memo("button.clear",function(){return f.button({contents:'<i class="fa fa-eraser"/>',tooltip:i.font.clear+l("removeFormat"),click:b.createInvokeHandler("editor.removeFormat")}).render()}),b.memo("button.fontname",function(){return f.buttonGroup([f.button({className:"dropdown-toggle",contents:'<span class="note-current-fontname"/> <span class="caret"/>',tooltip:i.font.name,data:{toggle:"dropdown"}}),f.dropdownCheck({className:"dropdown-fontname",items:h.fontNames.filter(function(a){return j.isFontInstalled(a)||d.contains(h.fontNamesIgnoreCheck,a)}),click:b.createInvokeHandler("editor.fontName")})]).render()}),b.memo("button.fontsize",function(){return f.buttonGroup([f.button({className:"dropdown-toggle",contents:'<span class="note-current-fontsize"/> <span class="caret"/>',tooltip:i.font.size,data:{toggle:"dropdown"}}),f.dropdownCheck({className:"dropdown-fontsize",items:h.fontSizes,click:b.createInvokeHandler("editor.fontSize")})]).render()}),b.memo("button.color",function(){return f.buttonGroup({className:"note-color",children:[f.button({className:"note-current-color-button",contents:'<i class="fa fa-font note-recent-color"/>',tooltip:i.color.recent,click:b.createInvokeHandler("editor.color"),callback:function(a){var b=a.find(".note-recent-color");b.css({"background-color":"yellow"}),a.data("value",{backColor:"yellow"})}}),f.button({className:"dropdown-toggle",contents:'<span class="caret"/>',tooltip:i.color.more,data:{toggle:"dropdown"}}),f.dropdown({items:["<li>",'<div class="btn-group">','  <div class="note-palette-title">'+i.color.background+"</div>",'  <div class="note-color-reset" data-event="backColor" data-value="inherit">'+i.color.transparent+"</div>",'  <div class="note-holder" data-event="backColor"/>',"</div>",'<div class="btn-group">','  <div class="note-palette-title">'+i.color.foreground+"</div>",'  <div class="note-color-reset" data-event="foreColor" data-value="inherit">'+i.color.resetToDefault+"</div>",'  <div class="note-holder" data-event="foreColor"/>',"</div>","</li>"].join(""),callback:function(b){b.find(".note-holder").each(function(){var b=a(this);b.append(f.palette({colors:h.colors,eventName:b.data("event")}).render())})},click:function(c){var d=a(c.target),e=d.data("event"),f=d.data("value");if(e&&f){var g="backColor"===e?"background-color":"color",h=d.closest(".note-color").find(".note-recent-color"),i=d.closest(".note-color").find(".note-current-color-button"),j=i.data("value");j[e]=f,h.css(g,f),i.data("value",j),b.invoke("editor."+e,f)}}})]}).render()}),b.memo("button.ol",function(){return f.button({contents:'<i class="fa fa-list-ul"/>',tooltip:i.lists.unordered+l("insertUnorderedList"),click:b.createInvokeHandler("editor.insertUnorderedList")}).render()}),b.memo("button.ul",function(){return f.button({contents:'<i class="fa fa-list-ol"/>',tooltip:i.lists.ordered+l("insertOrderedList"),click:b.createInvokeHandler("editor.insertOrderedList")}).render()}),b.memo("button.paragraph",function(){return f.buttonGroup([f.button({className:"dropdown-toggle",contents:'<i class="fa fa-align-left"/> <span class="caret"/>',tooltip:i.paragraph.paragraph,data:{toggle:"dropdown"}}),f.dropdown([f.buttonGroup({className:"note-align",children:[f.button({contents:'<i class="fa fa-align-left"/>',tooltip:i.paragraph.left+l("justifyLeft"),click:b.createInvokeHandler("editor.justifyLeft")}),f.button({contents:'<i class="fa fa-align-center"/>',tooltip:i.paragraph.center+l("justifyCenter"),click:b.createInvokeHandler("editor.justifyCenter")}),f.button({contents:'<i class="fa fa-align-right"/>',tooltip:i.paragraph.right+l("justifyRight"),click:b.createInvokeHandler("editor.justifyRight")}),f.button({contents:'<i class="fa fa-align-justify"/>',tooltip:i.paragraph.justify+l("justifyFull"),click:b.createInvokeHandler("editor.justifyFull")})]}),f.buttonGroup({className:"note-list",children:[f.button({contents:'<i class="fa fa-outdent"/>',tooltip:i.paragraph.outdent+l("outdent"),click:b.createInvokeHandler("editor.outdent")}),f.button({contents:'<i class="fa fa-indent"/>',tooltip:i.paragraph.indent+l("indent"),click:b.createInvokeHandler("editor.indent")})]})])]).render()}),b.memo("button.height",function(){return f.buttonGroup([f.button({className:"dropdown-toggle",contents:'<i class="fa fa-text-height"/> <span class="caret"/>',tooltip:i.font.height,data:{toggle:"dropdown"}}),f.dropdownCheck({items:h.lineHeights,className:"dropdown-line-height",click:b.createInvokeHandler("editor.lineHeight")})]).render()}),b.memo("button.table",function(){return f.buttonGroup([f.button({className:"dropdown-toggle",contents:'<i class="fa fa-table"/> <span class="caret"/>',tooltip:i.table.table,data:{toggle:"dropdown"}}),f.dropdown({className:"note-table",items:['<div class="note-dimension-picker">','  <div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"/>','  <div class="note-dimension-picker-highlighted"/>','  <div class="note-dimension-picker-unhighlighted"/>',"</div>",'<div class="note-dimension-display">1 x 1</div>'].join("")})],{callback:function(a){var c=a.find(".note-dimension-picker-mousecatcher");c.css({width:h.insertTableMaxSize.col+"em",height:h.insertTableMaxSize.row+"em"}).mousedown(b.createInvokeHandler("editor.insertTable")).on("mousemove",e.tableMoveHandler)}}).render()}),b.memo("button.link",function(){return f.button({contents:'<i class="fa fa-link"/>',tooltip:i.link.link,click:b.createInvokeHandler("linkDialog.show")}).render()}),b.memo("button.picture",function(){return f.button({contents:'<i class="fa fa-picture-o"/>',tooltip:i.image.image,click:b.createInvokeHandler("imageDialog.show")}).render()}),b.memo("button.video",function(){return f.button({contents:'<i class="fa fa-youtube-play"/>',tooltip:i.video.video,click:b.createInvokeHandler("videoDialog.show")}).render()}),b.memo("button.hr",function(){return f.button({contents:'<i class="fa fa-minus"/>',tooltip:i.hr.insert+l("insertHorizontalRule"),click:b.createInvokeHandler("editor.insertHorizontalRule")}).render()}),b.memo("button.fullscreen",function(){return f.button({className:"btn-fullscreen",contents:'<i class="fa fa-arrows-alt"/>',tooltip:i.options.fullscreen,click:b.createInvokeHandler("fullscreen.toggle")}).render()}),b.memo("button.codeview",function(){return f.button({className:"btn-codeview",contents:'<i class="fa fa-code"/>',tooltip:i.options.codeview,click:b.createInvokeHandler("codeview.toggle")}).render()}),b.memo("button.help",function(){return f.button({contents:'<i class="fa fa-question"/>',tooltip:i.options.help,click:b.createInvokeHandler("helpDialog.show")}).render()})},this.addImagePopoverButtons=function(){b.memo("button.imageSize100",function(){return f.button({contents:'<span class="note-fontsize-10">100%</span>',tooltip:i.image.resizeFull,click:b.createInvokeHandler("editor.resize","1")}).render()}),b.memo("button.imageSize50",function(){return f.button({contents:'<span class="note-fontsize-10">50%</span>',tooltip:i.image.resizeHalf,click:b.createInvokeHandler("editor.resize","0.5")}).render()}),b.memo("button.imageSize25",function(){return f.button({contents:'<span class="note-fontsize-10">25%</span>',tooltip:i.image.resizeQuarter,click:b.createInvokeHandler("editor.resize","0.25")}).render()}),b.memo("button.floatLeft",function(){return f.button({contents:'<i class="fa fa-align-left"/>',tooltip:i.image.floatLeft,click:b.createInvokeHandler("editor.floatMe","left")}).render()}),b.memo("button.floatRight",function(){return f.button({contents:'<i class="fa fa-align-right"/>',tooltip:i.image.floatRight,click:b.createInvokeHandler("editor.floatMe","right")}).render()}),b.memo("button.floatNone",function(){return f.button({contents:'<i class="fa fa-align-justify"/>',tooltip:i.image.floatNone,click:b.createInvokeHandler("editor.floatMe","none")}).render()}),b.memo("button.removeMedia",function(){return f.button({contents:'<i class="fa fa-trash-o"/>',tooltip:i.image.remove,click:b.createInvokeHandler("editor.removeMedia")}).render()})},this.addLinkPopoverButtons=function(){b.memo("button.linkDialogShow",function(){return f.button({contents:'<i class="fa fa-link"/>',tooltip:i.link.edit,click:b.createInvokeHandler("linkDialog.show")}).render()}),b.memo("button.unlink",function(){return f.button({contents:'<i class="fa fa-unlink"/>',tooltip:i.link.unlink,click:b.createInvokeHandler("editor.unlink")}).render()})},this.build=function(a,c){for(var d=0,e=c.length;e>d;d++){for(var g=c[d],h=g[0],i=g[1],j=f.buttonGroup({className:"note-"+h}).render(),k=0,l=i.length;l>k;k++){var m=b.memo("button."+i[k]);m&&j.append("function"==typeof m?m(b):m)}j.appendTo(a)}},this.updateCurrentStyle=function(){var c=b.invoke("editor.currentStyle");if(this.updateBtnStates({".note-btn-bold":function(){return"bold"===c["font-bold"]},".note-btn-italic":function(){return"italic"===c["font-italic"]},".note-btn-underline":function(){return"underline"===c["font-underline"]}}),c["font-family"]){var e=c["font-family"].split(",").map(function(a){return a.replace(/[\'\"]/g,"").replace(/\s+$/,"").replace(/^\s+/,"")}),f=d.find(e,function(a){return j.isFontInstalled(a)||d.contains(h.fontNamesIgnoreCheck,a)});g.find(".dropdown-fontname li a").each(function(){var b=a(this).data("value")+""==f+"";this.className=b?"checked":""}),g.find(".note-current-fontname").text(f)}if(c["font-size"]){var i=c["font-size"];g.find(".dropdown-fontsize li a").each(function(){var b=a(this).data("value")+""==i+"";this.className=b?"checked":""}),g.find(".note-current-fontsize").text(i)}if(c["line-height"]){var k=c["line-height"];g.find(".dropdown-line-height li a").each(function(){var b=a(this).data("value")+""==k+"";this.className=b?"checked":""})}},this.updateBtnStates=function(b){a.each(b,function(a,b){f.toggleBtnActive(g.find(a),b())})},this.tableMoveHandler=function(b){var c,d=18,e=a(b.target.parentNode),f=e.next(),g=e.find(".note-dimension-picker-mousecatcher"),i=e.find(".note-dimension-picker-highlighted"),j=e.find(".note-dimension-picker-unhighlighted");if(void 0===b.offsetX){var k=a(b.target).offset();c={x:b.pageX-k.left,y:b.pageY-k.top}}else c={x:b.offsetX,y:b.offsetY};var l={c:Math.ceil(c.x/d)||1,r:Math.ceil(c.y/d)||1};i.css({width:l.c+"em",height:l.r+"em"}),g.data("value",l.c+"x"+l.r),3<l.c&&l.c<h.insertTableMaxSize.col&&j.css({width:l.c+1+"em"}),3<l.r&&l.r<h.insertTableMaxSize.row&&j.css({height:l.r+1+"em"}),f.html(l.c+" x "+l.r)}},_=function(b){var c=a.summernote.ui,d=b.layoutInfo.note,e=b.layoutInfo.toolbar,f=b.options;this.shouldInitialize=function(){return!f.airMode},this.initialize=function(){f.toolbar=f.toolbar||[],f.toolbar.length?b.invoke("buttons.build",e,f.toolbar):e.hide(),d.on("summernote.keyup summernote.mouseup summernote.change",function(){b.invoke("buttons.updateCurrentStyle")}),b.invoke("buttons.updateCurrentStyle")},this.destroy=function(){e.children().remove()},this.updateFullscreen=function(a){c.toggleBtnActive(e.find(".btn-fullscreen"),a)},this.updateCodeview=function(a){c.toggleBtnActive(e.find(".btn-codeview"),a),a?this.deactivate():this.activate()},this.activate=function(){var a=e.find("button").not(".btn-codeview");c.toggleBtn(a,!0)},this.deactivate=function(){var a=e.find("button").not(".btn-codeview");c.toggleBtn(a,!1)}},aa=function(b){var c=this,d=a.summernote.ui,e=b.layoutInfo.editor,f=b.options,g=f.langInfo;this.initialize=function(){var b=f.dialogsInBody?a(document.body):e,c='<div class="form-group"><label>'+g.link.textToDisplay+'</label><input class="note-link-text form-control" type="text" /></div><div class="form-group"><label>'+g.link.url+'</label><input class="note-link-url form-control" type="text" value="http://" /></div>'+(f.disableLinkTarget?"":'<div class="checkbox"><label><input type="checkbox" checked> '+g.link.openInNewWindow+"</label></div>"),h='<button href="#" class="btn btn-primary note-link-btn disabled" disabled>'+g.link.insert+"</button>";
this.$dialog=d.dialog({className:"link-dialog",title:g.link.insert,body:c,footer:h}).render().appendTo(b)},this.destroy=function(){d.hideDialog(this.$dialog),this.$dialog.remove()},this.bindEnterKey=function(a,b){a.on("keypress",function(a){a.keyCode===H.code.ENTER&&b.trigger("click")})},this.showLinkDialog=function(e){return a.Deferred(function(a){var f=c.$dialog.find(".note-link-text"),g=c.$dialog.find(".note-link-url"),h=c.$dialog.find(".note-link-btn"),i=c.$dialog.find("input[type=checkbox]");d.onDialogShown(c.$dialog,function(){b.triggerEvent("dialog.shown"),f.val(e.text),f.on("input",function(){d.toggleBtn(h,f.val()&&g.val()),e.text=f.val()}),e.url||(e.url=e.text||"http://",d.toggleBtn(h,e.text)),g.on("input",function(){d.toggleBtn(h,f.val()&&g.val()),e.text||f.val(g.val())}).val(e.url).trigger("focus"),c.bindEnterKey(g,h),c.bindEnterKey(f,h),i.prop("checked",e.isNewWindow),h.one("click",function(b){b.preventDefault(),a.resolve({range:e.range,url:g.val(),text:f.val(),isNewWindow:i.is(":checked")}),c.$dialog.modal("hide")})}),d.onDialogHidden(c.$dialog,function(){f.off("input keypress"),g.off("input keypress"),h.off("click"),"pending"===a.state()&&a.reject()}),d.showDialog(c.$dialog)}).promise()},this.show=function(){var a=b.invoke("editor.getLinkInfo");b.invoke("editor.saveRange"),this.showLinkDialog(a).then(function(a){b.invoke("editor.restoreRange"),b.invoke("editor.createLink",a)}).fail(function(){b.invoke("editor.restoreRange")})},b.memo("help.linkDialog.show",f.langInfo.help["linkDialog.show"])},ba=function(b){var c=this,e=a.summernote.ui,f=b.options;this.events={"summernote.keyup summernote.mouseup summernote.change summernote.scroll":function(){c.update()},"summernote.dialog.shown":function(){c.hide()}},this.shouldInitailize=function(){return!d.isEmpty(f.popover.link)},this.initialize=function(){this.$popover=e.popover({className:"note-link-popover",callback:function(a){var b=a.find(".popover-content");b.prepend('<span><a target="_blank"></a>&nbsp;</span>')}}).render().appendTo("body");var a=this.$popover.find(".popover-content");b.invoke("buttons.build",a,f.popover.link)},this.destroy=function(){this.$popover.remove()},this.update=function(){var c=b.invoke("editor.createRange");if(c.isCollapsed()&&c.isOnAnchor()){var d=m.ancestor(c.sc,m.isAnchor),e=a(d).attr("href");this.$popover.find("a").attr("href",e).html(e);var f=m.posFromPlaceholder(d);this.$popover.css({display:"block",left:f.left,top:f.top})}else this.hide()},this.hide=function(){this.$popover.hide()}},ca=function(b){var c=this,d=a.summernote.ui,e=b.layoutInfo.editor,f=b.options,g=f.langInfo;this.initialize=function(){var b=f.dialogsInBody?a(document.body):e,c="";if(f.maximumImageFileSize){var h=Math.floor(Math.log(f.maximumImageFileSize)/Math.log(1024)),i=1*(f.maximumImageFileSize/Math.pow(1024,h)).toFixed(2)+" "+" KMGTP"[h]+"B";c="<small>"+g.image.maximumFileSize+" : "+i+"</small>"}var j='<div class="form-group note-group-select-from-files"><label>'+g.image.selectFromFiles+'</label><input class="note-image-input form-control" type="file" name="files" accept="image/*" multiple="multiple" />'+c+'</div><div class="form-group" style="overflow:auto;"><label>'+g.image.url+'</label><input class="note-image-url form-control col-md-12" type="text" /></div>',k='<button href="#" class="btn btn-primary note-image-btn disabled" disabled>'+g.image.insert+"</button>";this.$dialog=d.dialog({title:g.image.insert,body:j,footer:k}).render().appendTo(b)},this.destroy=function(){d.hideDialog(this.$dialog),this.$dialog.remove()},this.bindEnterKey=function(a,b){a.on("keypress",function(a){a.keyCode===H.code.ENTER&&b.trigger("click")})},this.insertImages=function(c){var d=f.callbacks;d.onImageUpload?b.triggerEvent("image.upload",c):a.each(c,function(a,c){var d=c.name;f.maximumImageFileSize&&f.maximumImageFileSize<c.size?b.triggerEvent("image.upload.error",g.image.maximumFileSizeError):J.readFileAsDataURL(c).then(function(a){b.invoke("editor.insertImage",a,d)}).fail(function(){b.triggerEvent("image.upload.error")})})},this.show=function(){b.invoke("editor.saveRange"),this.showImageDialog().then(function(a){d.hideDialog(c.$dialog),b.invoke("editor.restoreRange"),"string"==typeof a?b.invoke("editor.insertImage",a):c.insertImages(a)}).fail(function(){b.invoke("editor.restoreRange")})},this.showImageDialog=function(){return a.Deferred(function(a){var e=c.$dialog.find(".note-image-input"),f=c.$dialog.find(".note-image-url"),g=c.$dialog.find(".note-image-btn");d.onDialogShown(c.$dialog,function(){b.triggerEvent("dialog.shown"),e.replaceWith(e.clone().on("change",function(){a.resolve(this.files||this.value)}).val("")),g.click(function(b){b.preventDefault(),a.resolve(f.val())}),f.on("keyup paste",function(){var a=f.val();d.toggleBtn(g,a)}).val("").trigger("focus"),c.bindEnterKey(f,g)}),d.onDialogHidden(c.$dialog,function(){e.off("change"),f.off("keyup paste keypress"),g.off("click"),"pending"===a.state()&&a.reject()}),d.showDialog(c.$dialog)})}},da=function(b){var c=a.summernote.ui,e=b.options;this.shouldInitialize=function(){return!d.isEmpty(e.popover.image)},this.initialize=function(){this.$popover=c.popover({className:"note-image-popover"}).render().appendTo("body");var a=this.$popover.find(".popover-content");b.invoke("buttons.build",a,e.popover.image)},this.destroy=function(){this.$popover.remove()},this.update=function(a){if(m.isImg(a)){var b=m.posFromPlaceholder(a);this.$popover.css({display:"block",left:b.left,top:b.top})}else this.hide()},this.hide=function(){this.$popover.hide()}},ea=function(b){var c=this,d=a.summernote.ui,e=b.layoutInfo.editor,f=b.options,g=f.langInfo;this.initialize=function(){var b=f.dialogsInBody?a(document.body):e,c='<div class="form-group row-fluid"><label>'+g.video.url+' <small class="text-muted">'+g.video.providers+'</small></label><input class="note-video-url form-control span12" type="text" /></div>',h='<button href="#" class="btn btn-primary note-video-btn disabled" disabled>'+g.video.insert+"</button>";this.$dialog=d.dialog({title:g.video.insert,body:c,footer:h}).render().appendTo(b)},this.destroy=function(){d.hideDialog(this.$dialog),this.$dialog.remove()},this.bindEnterKey=function(a,b){a.on("keypress",function(a){a.keyCode===H.code.ENTER&&b.trigger("click")})},this.createVideoNode=function(b){var c,d=/^(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/,e=b.match(d),f=/\/\/instagram.com\/p\/(.[a-zA-Z0-9_-]*)/,g=b.match(f),h=/\/\/vine.co\/v\/(.[a-zA-Z0-9]*)/,i=b.match(h),j=/\/\/(player.)?vimeo.com\/([a-z]*\/)*([0-9]{6,11})[?]?.*/,k=b.match(j),l=/.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/,m=b.match(l),n=/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/,o=b.match(n),p=/^.+.(mp4|m4v)$/,q=b.match(p),r=/^.+.(ogg|ogv)$/,s=b.match(r),t=/^.+.(webm)$/,u=b.match(t);if(e&&11===e[1].length){var v=e[1];c=a("<iframe>").attr("frameborder",0).attr("src","//www.youtube.com/embed/"+v).attr("width","640").attr("height","360")}else if(g&&g[0].length)c=a("<iframe>").attr("frameborder",0).attr("src",g[0]+"/embed/").attr("width","612").attr("height","710").attr("scrolling","no").attr("allowtransparency","true");else if(i&&i[0].length)c=a("<iframe>").attr("frameborder",0).attr("src",i[0]+"/embed/simple").attr("width","600").attr("height","600").attr("class","vine-embed");else if(k&&k[3].length)c=a("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("src","//player.vimeo.com/video/"+k[3]).attr("width","640").attr("height","360");else if(m&&m[2].length)c=a("<iframe>").attr("frameborder",0).attr("src","//www.dailymotion.com/embed/video/"+m[2]).attr("width","640").attr("height","360");else if(o&&o[1].length)c=a("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","498").attr("width","510").attr("src","//player.youku.com/embed/"+o[1]);else{if(!(q||s||u))return!1;c=a("<video controls>").attr("src",b).attr("width","640").attr("height","360")}return c.addClass("note-video-clip"),c[0]},this.show=function(){var a=b.invoke("editor.getSelectedText");b.invoke("editor.saveRange"),this.showVideoDialog(a).then(function(a){d.hideDialog(c.$dialog),b.invoke("editor.restoreRange");var e=c.createVideoNode(a);e&&b.invoke("editor.insertNode",e)}).fail(function(){b.invoke("editor.restoreRange")})},this.showVideoDialog=function(e){return a.Deferred(function(a){var f=c.$dialog.find(".note-video-url"),g=c.$dialog.find(".note-video-btn");d.onDialogShown(c.$dialog,function(){b.triggerEvent("dialog.shown"),f.val(e).on("input",function(){d.toggleBtn(g,f.val())}).trigger("focus"),g.click(function(b){b.preventDefault(),a.resolve(f.val())}),c.bindEnterKey(f,g)}),d.onDialogHidden(c.$dialog,function(){f.off("input"),g.off("click"),"pending"===a.state()&&a.reject()}),d.showDialog(c.$dialog)})}},fa=function(b){var c=this,d=a.summernote.ui,e=b.layoutInfo.editor,f=b.options,g=f.langInfo;this.createShortCutList=function(){var c=f.keyMap[j.isMac?"mac":"pc"],d=a("<div />");return Object.keys(c).forEach(function(e){var f=a('<div class="help-list-item"/>'),g=c[e],h=b.memo("help."+g)?b.memo("help."+g):g,i=a("<label />").css({width:180,"max-width":200,"margin-right":10}).html(e),j=a("<span />").html(h);f.html(i).append(j),d.append(f)}),d.html()},this.initialize=function(){var b=f.dialogsInBody?a(document.body):e,c=['<p class="text-center">','<a href="//summernote.org/" target="_blank">Summernote 0.7.0</a> · ','<a href="//github.com/summernote/summernote" target="_blank">Project</a> · ','<a href="//github.com/summernote/summernote/issues" target="_blank">Issues</a>',"</p>"].join("");this.$dialog=d.dialog({title:g.options.help,body:this.createShortCutList(),footer:c,callback:function(a){a.find(".modal-body").css({"max-height":300,overflow:"scroll"})}}).render().appendTo(b)},this.destroy=function(){d.hideDialog(this.$dialog),this.$dialog.remove()},this.showHelpDialog=function(){return a.Deferred(function(a){d.onDialogHidden(c.$dialog,function(){b.triggerEvent("dialog.shown"),a.resolve()}),d.showDialog(c.$dialog)}).promise()},this.show=function(){b.invoke("editor.saveRange"),this.showHelpDialog().then(function(){b.invoke("editor.restoreRange")})}},ga=function(b){var e=this,f=a.summernote.ui,g=b.layoutInfo.editingArea,h=b.options,i=20;this.events={"summernote.keyup summernote.mouseup summernote.scroll":function(){e.update()},"summernote.change summernote.dialog.shown":function(){e.hide()},"summernote.focusout":function(a,b){b.relatedTarget&&m.ancestor(b.relatedTarget,c.eq(g[0]))||e.hide()}},this.shouldInitialize=function(){return h.airMode&&!d.isEmpty(h.popover.air)},this.initialize=function(){this.$popover=f.popover({className:"note-air-popover"}).render().appendTo("body");var a=this.$popover.find(".popover-content");b.invoke("buttons.build",a,h.popover.air)},this.destroy=function(){this.$popover.remove()},this.update=function(){var a=b.invoke("editor.currentStyle");if(a.range&&!a.range.isCollapsed()){var e=d.last(a.range.getClientRects());if(e){var f=c.rect2bnd(e);this.$popover.css({display:"block",left:Math.max(f.left+f.width/2,0)-i,top:f.top+f.height})}}else this.hide()},this.hide=function(){this.$popover.hide()}},ha=function(b){var e=this,f=a.summernote.ui,g=b.options.hint||[],h=a.isArray(g)?g:[g];this.events={"summernote.keyup":function(a,b){e.handleKeyup(b)},"summernote.keydown":function(a,b){e.handleKeydown(b)},"summernote.dialog.shown":function(){e.hide()}},this.shouldInitialize=function(){return h.length>0},this.initialize=function(){this.lastWordRange=null,this.$popover=f.popover({className:"note-hint-popover"}).render().appendTo("body"),this.$content=this.$popover.find(".popover-content"),this.$content.on("click",".note-hint-item",function(){e.$content.find(".active").removeClass("active"),a(this).addClass("active"),e.replace()})},this.destroy=function(){this.$popover.remove()},this.selectItem=function(a){this.$content.find(".active").removeClass("active"),a.addClass("active"),this.$content[0].scrollTop=a[0].offsetTop-this.$content.innerHeight()/2},this.moveDown=function(){var a=this.$content.find(".note-hint-item.active"),b=a.next();if(b.length)this.selectItem(b);else{var c=a.parent().next();c.length||(c=this.$content.find(".note-hint-group").first()),this.selectItem(c.find(".note-hint-item").first())}},this.moveUp=function(){var a=this.$content.find(".note-hint-item.active"),b=a.prev();if(b.length)this.selectItem(b);else{var c=a.parent().prev();c.length||(c=this.$content.find(".note-hint-group").last()),this.selectItem(c.find(".note-hint-item").last())}},this.replace=function(){var a=this.$content.find(".note-hint-item.active"),c=this.nodeFromItem(a);this.lastWordRange.insertNode(c),I.createFromNode(c).collapse().select(),this.lastWordRange=null,this.hide(),b.invoke("editor.focus")},this.nodeFromItem=function(a){var b=h[a.data("index")],c=a.data("item"),d=b.content?b.content(c):c;return"string"==typeof d&&(d=m.createText(d)),d},this.createItemTemplates=function(b,c){var d=h[b];return c.map(function(c,e){var f=a('<div class="note-hint-item"/>');return f.append(d.template?d.template(c):c+""),f.data({index:b,item:c}),0===b&&0===e&&f.addClass("active"),f})},this.handleKeydown=function(a){this.$popover.is(":visible")&&(a.keyCode===H.code.ENTER?(a.preventDefault(),this.replace()):a.keyCode===H.code.UP?(a.preventDefault(),this.moveUp()):a.keyCode===H.code.DOWN&&(a.preventDefault(),this.moveDown()))},this.searchKeyword=function(a,b,c){var d=h[a];if(d&&d.match.test(b)&&d.search){var e=d.match.exec(b);d.search(e[1],c)}else c()},this.createGroup=function(b,c){var d=a('<div class="note-hint-group note-hint-group-'+b+'"/>');return this.searchKeyword(b,c,function(a){a=a||[],a.length&&(d.html(e.createItemTemplates(b,a)),e.show())}),d},this.handleKeyup=function(a){if(d.contains([H.code.ENTER,H.code.UP,H.code.DOWN],a.keyCode)){if(a.keyCode===H.code.ENTER&&this.$popover.is(":visible"))return}else{var f=b.invoke("editor.createRange").getWordRange(),g=f.toString();if(h.length&&g){this.$content.empty();var i=c.rect2bnd(d.last(f.getClientRects()));i&&(this.$popover.css({left:i.left,top:i.top+i.height}).hide(),this.lastWordRange=f,h.forEach(function(a,b){a.match.test(g)&&e.createGroup(b,g).appendTo(e.$content)}))}else this.hide()}},this.show=function(){this.$popover.show()},this.hide=function(){this.$popover.hide()}};a.summernote=a.extend(a.summernote,{version:"0.7.0",ui:F,plugins:{},options:{modules:{editor:Q,clipboard:R,dropzone:S,codeview:T,statusbar:V,fullscreen:W,handle:X,autoLink:Y,autoSync:Z,buttons:$,toolbar:_,linkDialog:aa,linkPopover:ba,imageDialog:ca,imagePopover:da,videoDialog:ea,helpDialog:fa,airPopover:ga,hintPopover:ha},buttons:{},lang:"en-US",toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["fontname",["fontname"]],["color",["color"]],["para",["ul","ol","paragraph"]],["table",["table"]],["insert",["link","picture","video"]],["view",["fullscreen","codeview","help"]]],popover:{image:[["imagesize",["imageSize100","imageSize50","imageSize25"]],["float",["floatLeft","floatRight","floatNone"]],["remove",["removeMedia"]]],link:[["link",["linkDialogShow","unlink"]]],air:[["color",["color"]],["font",["bold","underline","clear"]],["para",["ul","paragraph"]],["table",["table"]],["insert",["link","picture"]]]},airMode:!1,width:null,height:null,focus:!1,tabSize:4,styleWithSpan:!0,shortcuts:!0,textareaAutoSync:!0,direction:null,styleTags:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],fontNames:["Arial","Arial Black","Comic Sans MS","Courier New","Helvetica Neue","Helvetica","Impact","Lucida Grande","Tahoma","Times New Roman","Verdana"],fontSizes:["8","9","10","11","12","14","18","24","36"],colors:[["#000000","#424242","#636363","#9C9C94","#CEC6CE","#EFEFEF","#F7F7F7","#FFFFFF"],["#FF0000","#FF9C00","#FFFF00","#00FF00","#00FFFF","#0000FF","#9C00FF","#FF00FF"],["#F7C6CE","#FFE7CE","#FFEFC6","#D6EFD6","#CEDEE7","#CEE7F7","#D6D6E7","#E7D6DE"],["#E79C9C","#FFC69C","#FFE79C","#B5D6A5","#A5C6CE","#9CC6EF","#B5A5D6","#D6A5BD"],["#E76363","#F7AD6B","#FFD663","#94BD7B","#73A5AD","#6BADDE","#8C7BC6","#C67BA5"],["#CE0000","#E79439","#EFC631","#6BA54A","#4A7B8C","#3984C6","#634AA5","#A54A7B"],["#9C0000","#B56308","#BD9400","#397B21","#104A5A","#085294","#311873","#731842"],["#630000","#7B3900","#846300","#295218","#083139","#003163","#21104A","#4A1031"]],lineHeights:["1.0","1.2","1.4","1.5","1.6","1.8","2.0","3.0"],tableClassName:"table table-bordered",insertTableMaxSize:{col:10,row:10},dialogsInBody:!1,maximumImageFileSize:null,callbacks:{onInit:null,onFocus:null,onBlur:null,onEnter:null,onKeyup:null,onKeydown:null,onSubmit:null,onImageUpload:null,onImageUploadError:null},codemirror:{mode:"text/html",htmlMode:!0,lineNumbers:!0},keyMap:{pc:{ENTER:"insertParagraph","CTRL+Z":"undo","CTRL+Y":"redo",TAB:"tab","SHIFT+TAB":"untab","CTRL+B":"bold","CTRL+I":"italic","CTRL+U":"underline","CTRL+SHIFT+S":"strikethrough","CTRL+BACKSLASH":"removeFormat","CTRL+SHIFT+L":"justifyLeft","CTRL+SHIFT+E":"justifyCenter","CTRL+SHIFT+R":"justifyRight","CTRL+SHIFT+J":"justifyFull","CTRL+SHIFT+NUM7":"insertUnorderedList","CTRL+SHIFT+NUM8":"insertOrderedList","CTRL+LEFTBRACKET":"outdent","CTRL+RIGHTBRACKET":"indent","CTRL+NUM0":"formatPara","CTRL+NUM1":"formatH1","CTRL+NUM2":"formatH2","CTRL+NUM3":"formatH3","CTRL+NUM4":"formatH4","CTRL+NUM5":"formatH5","CTRL+NUM6":"formatH6","CTRL+ENTER":"insertHorizontalRule","CTRL+K":"linkDialog.show"},mac:{ENTER:"insertParagraph","CMD+Z":"undo","CMD+SHIFT+Z":"redo",TAB:"tab","SHIFT+TAB":"untab","CMD+B":"bold","CMD+I":"italic","CMD+U":"underline","CMD+SHIFT+S":"strikethrough","CMD+BACKSLASH":"removeFormat","CMD+SHIFT+L":"justifyLeft","CMD+SHIFT+E":"justifyCenter","CMD+SHIFT+R":"justifyRight","CMD+SHIFT+J":"justifyFull","CMD+SHIFT+NUM7":"insertUnorderedList","CMD+SHIFT+NUM8":"insertOrderedList","CMD+LEFTBRACKET":"outdent","CMD+RIGHTBRACKET":"indent","CMD+NUM0":"formatPara","CMD+NUM1":"formatH1","CMD+NUM2":"formatH2","CMD+NUM3":"formatH3","CMD+NUM4":"formatH4","CMD+NUM5":"formatH5","CMD+NUM6":"formatH6","CMD+ENTER":"insertHorizontalRule","CMD+K":"linkDialog.show"}}}})});