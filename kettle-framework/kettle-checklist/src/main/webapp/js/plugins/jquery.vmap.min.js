/*!
 * jQVMap Version 1.1.0
 *
 * http://jqvmap.com
 *
 * Copyright 2012-2015, <PERSON> <<EMAIL>>
 * Copyright 2011-2012, <PERSON><PERSON>
 * Licensed under the MIT license.
 *
 * Fork Me @ https://github.com/manifestinteractive/jqvmap
 */
!function(){var t={colors:1,values:1,backgroundColor:1,scaleColors:1,normalizeFunction:1,enableZoom:1,showTooltip:1,borderColor:1,borderWidth:1,borderOpacity:1,selectedRegions:1,multiSelectRegion:1},e={onLabelShow:"labelShow",onRegionOver:"regionMouseOver",onRegionOut:"regionMouseOut",onRegionClick:"regionClick",onRegionSelect:"regionSelect",onRegionDeselect:"regionDeselect"},i=function(t,e,i){if(this.mode=window.SVGAngle?"svg":"vml",this.params=i,"svg"===this.mode)this.createSvgNode=function(t){return document.createElementNS(this.svgns,t)};else{try{document.namespaces.rvml||document.namespaces.add("rvml","urn:schemas-microsoft-com:vml"),this.createVmlNode=function(t){return document.createElement("<rvml:"+t+' class="rvml">')}}catch(s){this.createVmlNode=function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="rvml">')}}document.createStyleSheet().addRule(".rvml","behavior:url(#default#VML)")}"svg"===this.mode?this.canvas=this.createSvgNode("svg"):(this.canvas=this.createVmlNode("group"),this.canvas.style.position="absolute"),this.setSize(t,e)},s=function(t,e,i,s){t&&this.setColors(t),e&&this.setNormalizeFunction(e),i&&this.setMin(i),i&&this.setMax(s)},o=function(t){t=t||{};var e=this,r=o.maps[t.map];this.selectedRegions=[],this.multiSelectRegion=t.multiSelectRegion,this.container=t.container,this.defaultWidth=r.width,this.defaultHeight=r.height,this.color=t.color,this.selectedColor=t.selectedColor,this.hoverColor=t.hoverColor,this.hoverOpacity=t.hoverOpacity,this.setBackgroundColor(t.backgroundColor),this.width=t.container.width(),this.height=t.container.height(),this.resize(),jQuery(window).resize(function(){e.width=t.container.width(),e.height=t.container.height(),e.resize(),e.canvas.setSize(e.width,e.height),e.applyTransform()}),this.canvas=new i(this.width,this.height,t),t.container.append(this.canvas.canvas),this.makeDraggable(),this.rootGroup=this.canvas.createGroup(!0),this.index=o.mapIndex,this.label=jQuery("<div/>").addClass("jqvmap-label").appendTo(jQuery("body")).hide(),t.enableZoom&&(jQuery("<div/>").addClass("jqvmap-zoomin").text("+").appendTo(t.container),jQuery("<div/>").addClass("jqvmap-zoomout").html("&#x2212;").appendTo(t.container)),e.countries=[];for(var n in r.pathes){var a=this.canvas.createPath({path:r.pathes[n].path});a.setFill(this.color),a.id=e.getCountryId(n),e.countries[n]=a,"svg"===this.canvas.mode?a.setAttribute("class","jvectormap-region"):jQuery(a).addClass("jvectormap-region"),jQuery(this.rootGroup).append(a)}if(jQuery(t.container).delegate("svg"===this.canvas.mode?"path":"shape","mouseover mouseout",function(i){var s=i.target,o=i.target.id.split("_").pop(),n=jQuery.Event("labelShow.jqvmap"),a=jQuery.Event("regionMouseOver.jqvmap");"mouseover"===i.type?(jQuery(t.container).trigger(a,[o,r.pathes[o].name]),a.isDefaultPrevented()||e.highlight(o,s),t.showTooltip&&(e.label.text(r.pathes[o].name),jQuery(t.container).trigger(n,[e.label,o]),n.isDefaultPrevented()||(e.label.show(),e.labelWidth=e.label.width(),e.labelHeight=e.label.height()))):(e.unhighlight(o,s),e.label.hide(),jQuery(t.container).trigger("regionMouseOut.jqvmap",[o,r.pathes[o].name]))}),jQuery(t.container).delegate("svg"===this.canvas.mode?"path":"shape","click",function(i){if(!t.multiSelectRegion)for(var s in r.pathes)e.countries[s].currentFillColor=e.countries[s].getOriginalFill(),e.countries[s].setFill(e.countries[s].getOriginalFill());var o=i.target,n=i.target.id.split("_").pop();jQuery(t.container).trigger("regionClick.jqvmap",[n,r.pathes[n].name]),i.isDefaultPrevented()||(e.isSelected(n)?e.deselect(n,o):e.select(n,o))}),t.showTooltip&&t.container.mousemove(function(t){if(e.label.is(":visible")){var i=t.pageX-15-e.labelWidth,s=t.pageY-15-e.labelHeight;0>i&&(i=t.pageX+15),0>s&&(s=t.pageY+15),e.label.css({left:i,top:s})}}),this.setColors(t.colors),this.canvas.canvas.appendChild(this.rootGroup),this.applyTransform(),this.colorScale=new s(t.scaleColors,t.normalizeFunction,t.valueMin,t.valueMax),t.values&&(this.values=t.values,this.setValues(t.values)),t.selectedRegions)if(t.selectedRegions instanceof Array)for(var h in t.selectedRegions)this.select(t.selectedRegions[h].toLowerCase());else this.select(t.selectedRegions.toLowerCase());this.bindZoomButtons(),t.pins&&(this.pinHandlers=!1,this.placePins(t.pins,t.pinMode)),o.mapIndex++};i.prototype={svgns:"http://www.w3.org/2000/svg",mode:"svg",width:0,height:0,canvas:null,setSize:function(t,e){if("svg"===this.mode)this.canvas.setAttribute("width",t),this.canvas.setAttribute("height",e);else if(this.canvas.style.width=t+"px",this.canvas.style.height=e+"px",this.canvas.coordsize=t+" "+e,this.canvas.coordorigin="0 0",this.rootGroup){for(var i=this.rootGroup.getElementsByTagName("shape"),s=0,o=i.length;o>s;s++)i[s].coordsize=t+" "+e,i[s].style.width=t+"px",i[s].style.height=e+"px";this.rootGroup.coordsize=t+" "+e,this.rootGroup.style.width=t+"px",this.rootGroup.style.height=e+"px"}this.width=t,this.height=e},createPath:function(t){var e;if("svg"===this.mode)e=this.createSvgNode("path"),e.setAttribute("d",t.path),null!==this.params.borderColor&&e.setAttribute("stroke",this.params.borderColor),this.params.borderWidth>0&&(e.setAttribute("stroke-width",this.params.borderWidth),e.setAttribute("stroke-linecap","round"),e.setAttribute("stroke-linejoin","round")),this.params.borderOpacity>0&&e.setAttribute("stroke-opacity",this.params.borderOpacity),e.setFill=function(t){this.setAttribute("fill",t),null===this.getAttribute("original")&&this.setAttribute("original",t)},e.getFill=function(){return this.getAttribute("fill")},e.getOriginalFill=function(){return this.getAttribute("original")},e.setOpacity=function(t){this.setAttribute("fill-opacity",t)};else{e=this.createVmlNode("shape"),e.coordorigin="0 0",e.coordsize=this.width+" "+this.height,e.style.width=this.width+"px",e.style.height=this.height+"px",e.fillcolor=o.defaultFillColor,e.stroked=!1,e.path=i.pathSvgToVml(t.path);var s=this.createVmlNode("skew");s.on=!0,s.matrix="0.01,0,0,0.01,0,0",s.offset="0,0",e.appendChild(s);var r=this.createVmlNode("fill");e.appendChild(r),e.setFill=function(t){this.getElementsByTagName("fill")[0].color=t,null===this.getAttribute("original")&&this.setAttribute("original",t)},e.getFill=function(){return this.getElementsByTagName("fill")[0].color},e.getOriginalFill=function(){return this.getAttribute("original")},e.setOpacity=function(t){this.getElementsByTagName("fill")[0].opacity=parseInt(100*t,10)+"%"}}return e},createGroup:function(t){var e;return"svg"===this.mode?e=this.createSvgNode("g"):(e=this.createVmlNode("group"),e.style.width=this.width+"px",e.style.height=this.height+"px",e.style.left="0px",e.style.top="0px",e.coordorigin="0 0",e.coordsize=this.width+" "+this.height),t&&(this.rootGroup=e),e},applyTransformParams:function(t,e,i){"svg"===this.mode?this.rootGroup.setAttribute("transform","scale("+t+") translate("+e+", "+i+")"):(this.rootGroup.coordorigin=this.width-e+","+(this.height-i),this.rootGroup.coordsize=this.width/t+","+this.height/t)}},i.pathSvgToVml=function(t){var e,i,s="",o=0,r=0;return t.replace(/([MmLlHhVvCcSs])((?:-?(?:\d+)?(?:\.\d+)?,?\s?)+)/g,function(t,n,a){a=a.replace(/(\d)-/g,"$1,-").replace(/\s+/g,",").split(","),a[0]||a.shift();for(var h=0,l=a.length;l>h;h++)a[h]=Math.round(100*a[h]);switch(n){case"m":o+=a[0],r+=a[1],s="t"+a.join(",");break;case"M":o=a[0],r=a[1],s="m"+a.join(",");break;case"l":o+=a[0],r+=a[1],s="r"+a.join(",");break;case"L":o=a[0],r=a[1],s="l"+a.join(",");break;case"h":o+=a[0],s="r"+a[0]+",0";break;case"H":o=a[0],s="l"+o+","+r;break;case"v":r+=a[0],s="r0,"+a[0];break;case"V":r=a[0],s="l"+o+","+r;break;case"c":e=o+a[a.length-4],i=r+a[a.length-3],o+=a[a.length-2],r+=a[a.length-1],s="v"+a.join(",");break;case"C":e=a[a.length-4],i=a[a.length-3],o=a[a.length-2],r=a[a.length-1],s="c"+a.join(",");break;case"s":a.unshift(r-i),a.unshift(o-e),e=o+a[a.length-4],i=r+a[a.length-3],o+=a[a.length-2],r+=a[a.length-1],s="v"+a.join(",");break;case"S":a.unshift(r+r-i),a.unshift(o+o-e),e=a[a.length-4],i=a[a.length-3],o=a[a.length-2],r=a[a.length-1],s="c"+a.join(",")}return s}).replace(/z/g,"")},o.prototype={transX:0,transY:0,scale:1,baseTransX:0,baseTransY:0,baseScale:1,width:0,height:0,countries:{},countriesColors:{},countriesData:{},zoomStep:1.4,zoomMaxStep:4,zoomCurStep:1,setColors:function(t,e){if("string"==typeof t)this.countries[t].setFill(e),this.countries[t].setAttribute("original",e);else{var i=t;for(var s in i)this.countries[s]&&(this.countries[s].setFill(i[s]),this.countries[s].setAttribute("original",i[s]))}},setValues:function(t){var e,i=0,s=Number.MAX_VALUE;for(var o in t)e=parseFloat(t[o]),isNaN(e)||(e>i&&(i=t[o]),s>e&&(s=e));s===i&&i++,this.colorScale.setMin(s),this.colorScale.setMax(i);var r={};for(o in t)e=parseFloat(t[o]),r[o]=isNaN(e)?this.colorScale.getColor(e):this.color;this.setColors(r),this.values=t},setBackgroundColor:function(t){this.container.css("background-color",t)},setScaleColors:function(t){this.colorScale.setColors(t),this.values&&this.setValues(this.values)},setNormalizeFunction:function(t){this.colorScale.setNormalizeFunction(t),this.values&&this.setValues(this.values)},highlight:function(t,e){e=e||jQuery("#"+this.getCountryId(t))[0],this.hoverOpacity?e.setOpacity(this.hoverOpacity):this.hoverColor&&(e.currentFillColor=e.getFill()+"",e.setFill(this.hoverColor))},unhighlight:function(t,e){e=e||jQuery("#"+this.getCountryId(t))[0],e.setOpacity(1),e.currentFillColor&&e.setFill(e.currentFillColor)},selectIndex:function(t){for(var e=0;e<this.selectedRegions.length;e++)if(t===this.selectedRegions[e])return e;return-1},select:function(t,e){e=e||jQuery("#"+this.getCountryId(t))[0],this.isSelected(t)||(this.multiSelectRegion?this.selectedRegions.push(t):this.selectedRegions=[t],jQuery(this.container).trigger("regionSelect.jqvmap",[t]),this.selectedColor&&e&&(e.currentFillColor=this.selectedColor,e.setFill(this.selectedColor)))},deselect:function(t,e){if(e=e||jQuery("#"+this.getCountryId(t))[0],this.isSelected(t))this.selectedRegions.splice(this.selectIndex(t),1),jQuery(this.container).trigger("regionDeselect.jqvmap",[t]),e.currentFillColor=e.getOriginalFill(),e.setFill(e.getOriginalFill());else for(var i in this.countries)this.selectedRegions.splice(this.selectedRegions.indexOf(i),1),this.countries[i].currentFillColor=this.color,this.countries[i].setFill(this.color)},isSelected:function(t){return this.selectIndex(t)>=0},resize:function(){var t=this.baseScale;this.width/this.height>this.defaultWidth/this.defaultHeight?(this.baseScale=this.height/this.defaultHeight,this.baseTransX=Math.abs(this.width-this.defaultWidth*this.baseScale)/(2*this.baseScale)):(this.baseScale=this.width/this.defaultWidth,this.baseTransY=Math.abs(this.height-this.defaultHeight*this.baseScale)/(2*this.baseScale)),this.scale*=this.baseScale/t,this.transX*=this.baseScale/t,this.transY*=this.baseScale/t},reset:function(){for(var t in this.countries)this.countries[t].setFill(this.color);this.scale=this.baseScale,this.transX=this.baseTransX,this.transY=this.baseTransY,this.applyTransform()},applyTransform:function(){var t,e,i,s;this.defaultWidth*this.scale<=this.width?(t=(this.width-this.defaultWidth*this.scale)/(2*this.scale),i=(this.width-this.defaultWidth*this.scale)/(2*this.scale)):(t=0,i=(this.width-this.defaultWidth*this.scale)/this.scale),this.defaultHeight*this.scale<=this.height?(e=(this.height-this.defaultHeight*this.scale)/(2*this.scale),s=(this.height-this.defaultHeight*this.scale)/(2*this.scale)):(e=0,s=(this.height-this.defaultHeight*this.scale)/this.scale),this.transY>e?this.transY=e:this.transY<s&&(this.transY=s),this.transX>t?this.transX=t:this.transX<i&&(this.transX=i),this.canvas.applyTransformParams(this.scale,this.transX,this.transY)},makeDraggable:function(){var t,e,i=!1,s=this;s.isMoving=!1,s.isMovingTimeout=!1,this.container.mousemove(function(o){return i&&(s.transX-=(t-o.pageX)/s.scale,s.transY-=(e-o.pageY)/s.scale,s.applyTransform(),t=o.pageX,e=o.pageY,s.isMoving=!0,s.isMovingTimeout&&clearTimeout(s.isMovingTimeout),s.container.trigger("drag")),!1}).mousedown(function(s){return i=!0,t=s.pageX,e=s.pageY,!1}).mouseup(function(){return i=!1,s.isMovingTimeout=setTimeout(function(){s.isMoving=!1},100),!1})},bindZoomButtons:function(){var t=this;this.container.find(".jqvmap-zoomin").click(function(){t.zoomIn()}),this.container.find(".jqvmap-zoomout").click(function(){t.zoomOut()})},zoomIn:function(){var t=this,e=(jQuery("#zoom").innerHeight()-12-30-6-7-6)/(this.zoomMaxStep-this.zoomCurStep);if(t.zoomCurStep<t.zoomMaxStep){t.transX-=(t.width/t.scale-t.width/(t.scale*t.zoomStep))/2,t.transY-=(t.height/t.scale-t.height/(t.scale*t.zoomStep))/2,t.setScale(t.scale*t.zoomStep),t.zoomCurStep++;var i=jQuery("#zoomSlider");i.css("top",parseInt(i.css("top"),10)-e),t.container.trigger("zoomIn")}},zoomOut:function(){var t=this,e=(jQuery("#zoom").innerHeight()-12-30-6-7-6)/(this.zoomMaxStep-this.zoomCurStep);if(t.zoomCurStep>1){t.transX+=(t.width/(t.scale/t.zoomStep)-t.width/t.scale)/2,t.transY+=(t.height/(t.scale/t.zoomStep)-t.height/t.scale)/2,t.setScale(t.scale/t.zoomStep),t.zoomCurStep--;var i=jQuery("#zoomSlider");i.css("top",parseInt(i.css("top"),10)+e),t.container.trigger("zoomOut")}},setScale:function(t){this.scale=t,this.applyTransform()},getCountryId:function(t){return"jqvmap"+this.index+"_"+t},getPinId:function(t){return this.getCountryId(t)+"_pin"},placePins:function(t,e){var i=this;if((!e||"content"!==e&&"id"!==e)&&(e="content"),"content"===e?jQuery.each(t,function(t,e){if(0!==jQuery("#"+i.getCountryId(t)).length){var s=i.getPinId(t),o=jQuery("#"+s);o.length>0&&o.remove(),i.container.append('<div id="'+s+'" for="'+t+'" class="jqvmap_pin" style="position:absolute">'+e+"</div>")}}):jQuery.each(t,function(t,e){if(0!==jQuery("#"+i.getCountryId(t)).length){var s=i.getPinId(t),o=jQuery("#"+s);o.length>0&&o.remove(),i.container.append('<div id="'+s+'" for="'+t+'" class="jqvmap_pin" style="position:absolute"></div>'),o.append(jQuery("#"+e))}}),this.positionPins(),!this.pinHandlers){this.pinHandlers=!0;var s=function(){i.positionPins()};this.container.bind("zoomIn",s).bind("zoomOut",s).bind("drag",s)}},positionPins:function(){var t=this,e=this.container.find(".jqvmap_pin");jQuery.each(e,function(e,i){i=jQuery(i);var s=t.getCountryId(i.attr("for")),o=jQuery("#"+s),r=document.getElementById(s).getBBox(),n=o.position(),a=t.scale,h=n.left+r.width/2*a-i.width()/2,l=n.top+r.height/2*a-i.height()/2;i.css("left",h).css("top",l)})},getPin:function(t){var e=jQuery("#"+this.getPinId(t));return e.html()},getPins:function(){var t=this.container.find(".jqvmap_pin"),e={};return jQuery.each(t,function(t,i){i=jQuery(i);var s=i.attr("for"),o=i.html();e[s]=o}),JSON.stringify(e)},removePin:function(t){jQuery("#"+this.getPinId(t)).remove()},removePins:function(){this.container.find(".jqvmap_pin").remove()}},o.xlink="http://www.w3.org/1999/xlink",o.mapIndex=1,o.maps={},jQuery.fn.vectorMap=function(i){var s={map:"world_en",backgroundColor:"#a5bfdd",color:"#f4f3f0",hoverColor:"#c9dfaf",selectedColor:"#c9dfaf",scaleColors:["#b6d6ff","#005ace"],normalizeFunction:"linear",enableZoom:!0,showTooltip:!0,borderColor:"#818181",borderWidth:1,borderOpacity:.25,selectedRegions:null,multiSelectRegion:!1},r=this.data("mapObject");if("addMap"===i)o.maps[arguments[1]]=arguments[2];else if("set"===i&&t[arguments[1]])r["set"+arguments[1].charAt(0).toUpperCase()+arguments[1].substr(1)].apply(r,Array.prototype.slice.call(arguments,2));else{if("string"==typeof i&&"function"==typeof r[i])return r[i].apply(r,Array.prototype.slice.call(arguments,1));jQuery.extend(s,i),s.container=this,this.css({position:"relative",overflow:"hidden"}),r=new o(s),this.data("mapObject",r);for(var n in e)s[n]&&this.bind(e[n]+".jqvmap",s[n])}},s.prototype={colors:[],setMin:function(t){this.clearMinValue=t,this.minValue="function"==typeof this.normalize?this.normalize(t):t},setMax:function(t){this.clearMaxValue=t,this.maxValue="function"==typeof this.normalize?this.normalize(t):t},setColors:function(t){for(var e=0;e<t.length;e++)t[e]=s.rgbToArray(t[e]);this.colors=t},setNormalizeFunction:function(t){"polynomial"===t?this.normalize=function(t){return Math.pow(t,.2)}:"linear"===t?delete this.normalize:this.normalize=t,this.setMin(this.clearMinValue),this.setMax(this.clearMaxValue)},getColor:function(t){"function"==typeof this.normalize&&(t=this.normalize(t));for(var e,i=[],s=0,o=0;o<this.colors.length-1;o++)e=this.vectorLength(this.vectorSubtract(this.colors[o+1],this.colors[o])),i.push(e),s+=e;var r=(this.maxValue-this.minValue)/s;for(o=0;o<i.length;o++)i[o]*=r;for(o=0,t-=this.minValue;t-i[o]>=0;)t-=i[o],o++;var n;for(n=o===this.colors.length-1?this.vectorToNum(this.colors[o]).toString(16):this.vectorToNum(this.vectorAdd(this.colors[o],this.vectorMult(this.vectorSubtract(this.colors[o+1],this.colors[o]),t/i[o]))).toString(16);n.length<6;)n="0"+n;return"#"+n},vectorToNum:function(t){for(var e=0,i=0;i<t.length;i++)e+=Math.round(t[i])*Math.pow(256,t.length-i-1);return e},vectorSubtract:function(t,e){for(var i=[],s=0;s<t.length;s++)i[s]=t[s]-e[s];return i},vectorAdd:function(t,e){for(var i=[],s=0;s<t.length;s++)i[s]=t[s]+e[s];return i},vectorMult:function(t,e){for(var i=[],s=0;s<t.length;s++)i[s]=t[s]*e;return i},vectorLength:function(t){for(var e=0,i=0;i<t.length;i++)e+=t[i]*t[i];return Math.sqrt(e)}},s.arrayToRgb=function(t){for(var e,i="#",s=0;s<t.length;s++)e=t[s].toString(16),i+=1===e.length?"0"+e:e;return i},s.rgbToArray=function(t){return t=t.substr(1),[parseInt(t.substr(0,2),16),parseInt(t.substr(2,2),16),parseInt(t.substr(4,2),16)]}}(jQuery);
