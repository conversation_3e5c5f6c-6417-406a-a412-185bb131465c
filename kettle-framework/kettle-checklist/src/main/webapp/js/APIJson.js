/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 29-04-2016.
 */
(function () {
    'use strict';

    angular.module('listapp').factory('APIJson', APIJson);

    function APIJson() {
    	
    	var baseUrl = "http://dev.kettle.chaayos.com:9595/";
    	var chekListBaseUrl = "http://dev.kettle.chaayos.com:9595/";
    	
        var KETTLE_CONTEXT = baseUrl+"kettle-service/rest/v1/";
        var MASTER_CONTEXT = baseUrl+"master-service/rest/v1/";
        var CUSTOMER_CONTEXT = baseUrl+"kettle-crm/rest/v1/";
        var CHEKLIST_CONTEXT = chekListBaseUrl+"kettle-checklist/rest/v1/";
        
        var POS_METADATA_ROOT_CONTEXT = KETTLE_CONTEXT+"pos-metadata";
        var USER_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT+"users";
        var UNIT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT+"unit-metadata";
        var CRM_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT+"crm";
        var PRODUCT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT+"product-metadata";
        var REPORT_METADATA_ROOT_CONTEXT = KETTLE_CONTEXT+"report-metadata";
        var CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT+"customer-profile";
        var OFFER_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT+"offer-management";
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT+"user-management";
        var CASH_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT+"cash-management";
        var CHEKLIST_METADATA_CONTEXT = CHEKLIST_CONTEXT + "checklist-metadata/";
        var CHEKLIST_SERVICE_CONTEXT = CHEKLIST_CONTEXT + "checklist-service/";
        var CHEKLIST_MANAGEMENT_CONTEXT = CHEKLIST_CONTEXT + "checklist-management/";
        var CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT+"customer-offer-management";


        var service = {};
        
        service.urls = {
        	posMetaData: {
                deliveryPartners:POS_METADATA_ROOT_CONTEXT+"/delivery-partners",
                creditAccountAll:POS_METADATA_ROOT_CONTEXT+"/creditAccount/all",
                creditAccountUpdate:POS_METADATA_ROOT_CONTEXT+"/creditAccount/update",
                refLookUpUpsert:PRODUCT_METADATA_ROOT_CONTEXT+"/refLookUp/upsert"
            },
            users:{
                login:USER_SERVICES_ROOT_CONTEXT+"/login",
                adminLogin:USER_SERVICES_ROOT_CONTEXT+"/admin/login",
                changePassCode:USER_SERVICES_ROOT_CONTEXT+"/changePasscode",
                logout:USER_SERVICES_ROOT_CONTEXT+"/logout"
            },
            customer:{
            	signin:CUSTOMER_CONTEXT+CRM_SERVICES_ROOT_CONTEXT+"/signin"
            },
            unitMetaData: {
            	unit:UNIT_METADATA_ROOT_CONTEXT+"/unit-data",
                regions:UNIT_METADATA_ROOT_CONTEXT+"/regions",
                divisions:UNIT_METADATA_ROOT_CONTEXT+"/divisions",
                departments:UNIT_METADATA_ROOT_CONTEXT+"/departments",
                families:UNIT_METADATA_ROOT_CONTEXT+"/families",
                taxProfiles:UNIT_METADATA_ROOT_CONTEXT+"/tax-profiles",
                units:UNIT_METADATA_ROOT_CONTEXT+"/units",
                allUnits:UNIT_METADATA_ROOT_CONTEXT+"/all-units",
                addUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/add",
                updateUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/update",
                activateUnit:UNIT_METADATA_ROOT_CONTEXT+"/unit/activate",
                listTypes:UNIT_METADATA_ROOT_CONTEXT+"/listTypes",
                subCategories:UNIT_METADATA_ROOT_CONTEXT+"/sub-categories"
            },
            productMetaData: {
                deactivateProductMapping:PRODUCT_METADATA_ROOT_CONTEXT+"/product/mapping/deactivate",
                activateProductMapping:PRODUCT_METADATA_ROOT_CONTEXT+"/product/mapping/activate",
                unitProductPriceUpdate:PRODUCT_METADATA_ROOT_CONTEXT+"/unit-product/price/update",
                productPriceUpdate:PRODUCT_METADATA_ROOT_CONTEXT+"/product/price/update",
                products:PRODUCT_METADATA_ROOT_CONTEXT+"/products",
                productPriceMapping:PRODUCT_METADATA_ROOT_CONTEXT+"/product/price/mappings",
                vendor:PRODUCT_METADATA_ROOT_CONTEXT+"/vendor",
                deactivateProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/deactivate",
                activateProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/activate",
                addProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/add",
                updateProduct:PRODUCT_METADATA_ROOT_CONTEXT+"/product/update"
            },
            reportMetaData: {
                reportEnviroments:REPORT_METADATA_ROOT_CONTEXT+"/report/environments",
                adHocReports:REPORT_METADATA_ROOT_CONTEXT+"/report/adhoc-reports-list",
                reportCategories:REPORT_METADATA_ROOT_CONTEXT+"/report/categories",
                reportExecute:REPORT_METADATA_ROOT_CONTEXT+"/report/executet"
            },
            customerProfile: {
                refer:CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT+"/refer"
            },
            offerManagement: {
                marketingPartner:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner",
                offerCategory:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offer-category",
                paymentModes:OFFER_MANAGEMENT_ROOT_CONTEXT+"/payment-modes",
                marketingPartnerDeactivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner/deactivate",
                marketingPartnerActivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner/activate",
                marketingPartnerAdd:OFFER_MANAGEMENT_ROOT_CONTEXT+"/marketing-partner/add",
                offerCoupons:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offer/coupons",
                offers:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offers",
                offersAdd:OFFER_MANAGEMENT_ROOT_CONTEXT+"/offers/add",
                couponAvailablity:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/availablity",
                couponAdd:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/add",
                couponAuto:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/auto",
                couponMappingActivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/mapping/activate",
                couponMappingDeactivate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/mapping/deactivate",
                couponUpdate:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/update",
                couponSearch:OFFER_MANAGEMENT_ROOT_CONTEXT+"/coupon/search"
            },
            customerOfferManagement:{
                channelPartners:CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT+"/channel-partners"
            },
            userManagement: {
                managers:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/managers",
                users:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/users",
                user:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user",
                userAdd:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/add",
                userActivate:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/activate",
                userDeactivate:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/deactivate",
                userUnits:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/units",
                userUpdateMapping:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/update-mapping",
                userUpdate:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/update",
                userResetPasscode:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT+"/user/reset-passcode"
            },
            cashManagement:{
                pullSettlementsGet:CASH_MANAGEMENT_ROOT_CONTEXT+"/pullSettlements/get",
                pullSettlementsOpenGet:CASH_MANAGEMENT_ROOT_CONTEXT+"/pullSettlements/open/get",
                pullSettlementClose:CASH_MANAGEMENT_ROOT_CONTEXT+"/pullSettlement/close"

            },
            checkList:{
            	addCheckList:CHEKLIST_MANAGEMENT_CONTEXT+"checklist/add",
            	listTypes:CHEKLIST_METADATA_CONTEXT+"listTypes",
            	categories:CHEKLIST_METADATA_CONTEXT+"categories",
            	frequency:CHEKLIST_METADATA_CONTEXT+"frequency",
            	station:CHEKLIST_METADATA_CONTEXT+"station",
            	checklists:CHEKLIST_SERVICE_CONTEXT+"checklists",
            	submit:CHEKLIST_SERVICE_CONTEXT+"checklists/submit",
            }
        };

        return service;
    }


})();