package com.stpl.tech.kettle.inventory.domain.mongo;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;

@Document
public class CostDetail {

	@Id
	protected Integer costDetailId;
	protected String name;
	protected int unitId;
	protected BigDecimal quantity;
	protected BigDecimal price;
	protected String uom;
	protected PriceUpdateEntryType keyType;
	protected int keyId;
	protected Date lastUpdatedTimes;
	protected boolean latest;
	protected Date expiryDate;

	public Integer getCostDetailId() {
		return costDetailId;
	}

	public void setCostDetailId(Integer costDetailId) {
		this.costDetailId = costDetailId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public PriceUpdateEntryType getKeyType() {
		return keyType;
	}

	public void setKeyType(PriceUpdateEntryType keyType) {
		this.keyType = keyType;
	}

	public int getKeyId() {
		return keyId;
	}

	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}

	public Date getLastUpdatedTimes() {
		return lastUpdatedTimes;
	}

	public void setLastUpdatedTimes(Date lastUpdatedTimes) {
		this.lastUpdatedTimes = lastUpdatedTimes;
	}

	public boolean isLatest() {
		return latest;
	}

	public void setLatest(boolean latest) {
		this.latest = latest;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

}
