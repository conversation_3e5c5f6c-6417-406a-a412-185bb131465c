package com.stpl.tech.kettle.inventory.vo;

import java.math.BigDecimal;

public class RecipeInventory {

	private int id;
	private String name;
	private BigDecimal recipeQty;
	private BigDecimal availableQty;
	private BigDecimal expiryQty = BigDecimal.ZERO;
	private BigDecimal count = BigDecimal.ZERO;
	private BigDecimal expiryCount = BigDecimal.ZERO;

	private String uom;
	private boolean critical;

	public RecipeInventory(int id, String name, BigDecimal recipeQty, BigDecimal availableQty, String uom,
			boolean critical) {
		super();
		this.id = id;
		this.name = name;
		this.recipeQty = recipeQty;
		this.availableQty = availableQty;
		this.uom = uom;
		this.critical = critical;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public BigDecimal getRecipeQty() {
		return recipeQty;
	}

	public void setRecipeQty(BigDecimal recipeQty) {
		this.recipeQty = recipeQty;
	}

	public BigDecimal getAvailableQty() {
		return availableQty;
	}

	public void setAvailableQty(BigDecimal availableQty) {
		this.availableQty = availableQty;
	}

	public BigDecimal getCount() {
		return count;
	}

	public void setCount(BigDecimal count) {
		this.count = count;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public boolean isCritical() {
		return critical;
	}

	public void setCritical(boolean critical) {
		this.critical = critical;
	}

	public BigDecimal getExpiryQty() {
		return expiryQty;
	}

	public void setExpiryQty(BigDecimal expiryQty) {
		this.expiryQty = expiryQty;
	}

	public BigDecimal getExpiryCount() {
		return expiryCount;
	}

	public void setExpiryCount(BigDecimal expiryCount) {
		this.expiryCount = expiryCount;
	}

}
