package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

@RedisHash("CriticalProductMap")
public class CriticalProductMap implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	private String key;
	private int productId;
	private int unitId;
	private List<Integer> productsAffected;

	public CriticalProductMap() {
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<Integer> getProductsAffected() {
		if (productsAffected == null) {
			productsAffected = new ArrayList<>();
		}
		return productsAffected;
	}

	public void setProductsAffected(List<Integer> productsAffected) {
		this.productsAffected = productsAffected;
	}

}
