package com.stpl.tech.kettle.inventory.domain.redis;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

@RedisHash("InventorySnapShotVO")
public class InventorySnapShotVO {

	@Id
	protected Integer unitId;
	protected BigDecimal totalCost;
	List<InventoryData> inventory;

	public InventorySnapShotVO() {
		super();
	}

	public InventorySnapShotVO(Integer unitId, BigDecimal totalCost, List<InventoryData> inventory) {
		super();
		this.unitId = unitId;
		this.totalCost = totalCost;
		this.inventory = inventory;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public List<InventoryData> getInventory() {
		return inventory;
	}

	public void setInventory(List<InventoryData> inventory) {
		this.inventory = inventory;
	}

}
