package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import com.stpl.tech.master.recipe.model.RecipeDetail;

@RedisHash("RecipeMapData")
public class RecipeMapData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4369872062917242341L;
	@Id
	private Integer key;
	private RecipeDetail recipe;

	public RecipeMapData() {

	}

	public RecipeMapData(Integer key, RecipeDetail recipe) {
		super();
		this.key = key;
		this.recipe = recipe;
	}

	public Integer getKey() {
		return key;
	}

	public void setKey(Integer key) {
		this.key = key;
	}

	public RecipeDetail getRecipe() {
		return recipe;
	}

	public void setRecipe(RecipeDetail recipe) {
		this.recipe = recipe;
	}

}
