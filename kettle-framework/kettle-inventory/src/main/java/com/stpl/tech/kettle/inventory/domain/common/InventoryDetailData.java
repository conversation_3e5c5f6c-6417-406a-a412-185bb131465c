package com.stpl.tech.kettle.inventory.domain.common;

import java.io.Serializable;
import java.math.BigDecimal;

public class InventoryDetailData implements Serializable {
    private static final long serialVersionUID = 7008248248958425310L;
    private int id;
    private String name;
    private BigDecimal qty;
    private String u;

    public InventoryDetailData() {
    }

    public InventoryDetailData(int id, String name, BigDecimal qty, String u) {
        this.id = id;
        this.name = name;
        this.qty = qty;
        this.u = u;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getU() {
        return u;
    }

    public void setU(String u) {
        this.u = u;
    }
}
