package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

@RedisHash("KeyRepository")
public class KeyRepository implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	private String repoKey;
	private List<String> keyList;

	public KeyRepository() {
	}

	public KeyRepository(String key, List<String> keyList) {
		super();
		this.repoKey = key;
		this.keyList = keyList;
	}

	public String getRepoKey() {
		return repoKey;
	}

	public void setRepoKey(String repoKey) {
		this.repoKey = repoKey;
	}

	public List<String> getKeyList() {
		return keyList;
	}

	public void setKeyList(List<String> keyList) {
		this.keyList = keyList;
	}

}
