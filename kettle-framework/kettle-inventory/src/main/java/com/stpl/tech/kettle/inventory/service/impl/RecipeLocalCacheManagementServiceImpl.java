package com.stpl.tech.kettle.inventory.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.inventory.service.RecipeLocalCacheManagementService;
import com.stpl.tech.kettle.inventory.service.RecipeManagementService;
import com.stpl.tech.master.recipe.model.RecipeDetail;

@Service
public class RecipeLocalCacheManagementServiceImpl implements RecipeLocalCacheManagementService {

	@Autowired
	private RecipeManagementService recipeService;
	private Map<Integer, RecipeDetail> map = new HashMap<Integer, RecipeDetail>();

	@Override
	public RecipeDetail getRecipe(Integer recipeId) {
		if (!map.containsKey(recipeId)) {
			map.put(recipeId, recipeService.getRecipe(recipeId));
		}
		return map.get(recipeId);
	}

	@Override
	public void clearMap() {
		map = new HashMap<Integer, RecipeDetail>();
	}

	@Override
	public void clearRecipe(Integer recipeId) {
		map.remove(recipeId);
	}
}
