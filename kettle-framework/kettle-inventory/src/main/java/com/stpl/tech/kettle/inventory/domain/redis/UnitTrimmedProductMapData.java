package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.index.Indexed;

import com.stpl.tech.master.domain.model.TrimmedProductVO;

@RedisHash("UnitTrimmedProductMapData")
public class UnitTrimmedProductMapData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1163117474525182385L;
	@Id
	private String key;
	@Indexed
	private int unitId;
	@Indexed
	private int productId;
	private TrimmedProductVO product;
	@Indexed
	private Integer brandId;

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public TrimmedProductVO getProduct() {
		return product;
	}

	public void setProduct(TrimmedProductVO product) {
		this.product = product;
	}

}
