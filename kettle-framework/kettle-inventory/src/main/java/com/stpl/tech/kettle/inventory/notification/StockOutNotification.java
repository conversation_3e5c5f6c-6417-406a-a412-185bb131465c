package com.stpl.tech.kettle.inventory.notification;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.inventory.domain.common.StockOutData;
import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

public class StockOutNotification extends EmailNotification implements Notification {

	private List<StockOutData> stockOutData;
	private EnvType envType;
	private Date notificationTime;

	public StockOutNotification(List<StockOutData> list, EnvType env, Date notificationTime) {
		this.stockOutData = list;
		this.envType = env;
		this.notificationTime = notificationTime;
	}

	@Override
	public String getNotificationMessage() {
		return null;
	}

	@Override
	public String[] getToEmails() {
		return AppUtils.isDev(envType) ? new String[] { "<EMAIL>" }
				: new String[] { "<EMAIL>"};
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		return (AppUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "Inventory Stock Out Notification : on "
				+ new SimpleDateFormat("yyyy-MM-dd HH:mm").format(notificationTime);
	}

	@Override
	public String body() throws EmailGenerationException {

		Collections.sort(stockOutData);

		StringBuffer body = new StringBuffer(
				"<html><p><b>Inventory Stock Out Notification : </b><br/> <b>Notification Generation Timestamp: </b>"
						+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(notificationTime) + "</p>");
		body.append("<p><b> Stock Out Summary: </b><br/></p>");

		body.append("<table style='border: 1px solid black;'>");
		body.append("<tr style='border: 1px solid black;'>");
		body.append("<th style='border: 1px solid black;'>Employee</th>");
		body.append("<th style='border: 1px solid black;'>Unit</th>");
		body.append("<th style='border: 1px solid black;'>Product</th>");
		body.append("<th style='border: 1px solid black;'>Dimension</th>");
		body.append("</tr>");

		for (StockOutData data : stockOutData) {
			body.append("<tr>"
					+ "<td>" + data.getEmployeeName() + "</td>"
					+ "<td>" + data.getUnitName()	 + "</td>"
					+ "<td>" + data.getProductName() + "</td>"
					+ "<td>" + data.getDimension()   + "</td>"
					+ "</tr>");
		}

		body.append("</table>");
		return body.toString();

	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}

}
