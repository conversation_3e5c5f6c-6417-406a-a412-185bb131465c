package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;
import java.math.BigDecimal;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import com.fasterxml.jackson.annotation.JsonProperty;

@RedisHash("InventoryData")
public class InventoryData implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	private String key;
	private int id;
	private String name;
	private BigDecimal qty;
	private String u;
	private BigDecimal exQty = BigDecimal.ZERO;
	private BigDecimal price = BigDecimal.ZERO;

	public InventoryData() {
	}

	public InventoryData(String key, int id, String name, BigDecimal qty, String u, BigDecimal exQty) {
		super();
		this.key = key;
		this.id = id;
		this.name = name;
		this.qty = qty;
		this.u = u;
		this.exQty = exQty;
	}

	// @JsonIgnore
	@JsonProperty(value = "key")
	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	public String getU() {
		return u;
	}

	public void setU(String u) {
		this.u = u;
	}

	public BigDecimal getExQty() {
		return exQty;
	}

	public void setExQty(BigDecimal exQty) {
		this.exQty = exQty;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		InventoryData that = (InventoryData) o;

		return new EqualsBuilder()
			.append(id, that.id)
			.append(key, that.key)
			.append(name, that.name)
			.append(qty, that.qty)
			.append(u, that.u)
			.append(exQty, that.exQty)
			.append(price, that.price)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(key)
			.append(id)
			.append(name)
			.append(qty)
			.append(u)
			.append(exQty)
			.append(price)
			.toHashCode();
	}
}
