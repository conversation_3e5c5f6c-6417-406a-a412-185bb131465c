package com.stpl.tech.kettle.inventory.dao.mongo;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.inventory.domain.mongo.CostDetailWrapper;

@Repository
public interface CostDetailWrapperDao extends MongoRepository<CostDetailWrapper, Integer> {

	public int deleteCostDetailWrapperByUnitIdAndBusinessDate(int unitId, Date businessDate);

	@Query("{'unitId' : ?0, 'businessDate' : ?1}")
	public CostDetailWrapper findByUnitIdAndBusinessDate(int unitId, Date businessDate);

	@Query("{'businessDate' : { '$lte' :  ?0} }")
	public List<CostDetailWrapper> findOldRecordsByBusinessDate(Date currentDate);

}
