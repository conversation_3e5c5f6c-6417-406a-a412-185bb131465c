package com.stpl.tech.kettle.inventory.controller;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.util.InventoryUtils;
import com.stpl.tech.util.domain.error.ErrorInfo;

/**
 * <pre>
 * 
 * Abstract class to handle
 * 1. exception
 * 2. exception log
 * 3. exception response
 * 
 * </pre>
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractInventoryResource {

	/**
	 * static sl4j logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(InventoryDataResources.class);

	/**
	 * generic property class for Inventory service
	 */
	@Autowired
	private InventoryProperties props;

	/**
	 * Generic Exception Handler
	 * <p>
	 * This will handle all the exceptions returning from the API. It will log the
	 * error and then send a slack notification
	 * 
	 * @param request
	 * @param exception
	 * @return {@link ErrorInfo}
	 */
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(Exception.class)
	@ResponseBody
	public ErrorInfo handleAllExceptions(HttpServletRequest request, Exception exception) {
		LOG.error("ERROR", exception);
		InventoryUtils.slackIt(InventoryUtils.getUserAgent(request), request.getRequestURL().toString(),
				props.getEnvType(), exception);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.name(),
				exception);
	}

	/**
	 * <pre>
	 * ------------------------------------------------- 
	 * Add Exception class specific handlers below this. 
	 * -------------------------------------------------
	 * 
	 * </pre>
	 */

}
