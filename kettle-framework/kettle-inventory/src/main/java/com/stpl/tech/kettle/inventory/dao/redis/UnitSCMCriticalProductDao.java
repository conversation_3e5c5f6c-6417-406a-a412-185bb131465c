package com.stpl.tech.kettle.inventory.dao.redis;

import java.util.List;

import org.springframework.data.repository.CrudRepository;

import com.stpl.tech.kettle.inventory.domain.redis.UnitSCMCriticalProductMap;
import org.springframework.stereotype.Repository;

@Repository
public interface UnitSCMCriticalProductDao extends CrudRepository<UnitSCMCriticalProductMap, Integer> {

	   List<UnitSCMCriticalProductMap> findByUnitId(int unitId);

}
