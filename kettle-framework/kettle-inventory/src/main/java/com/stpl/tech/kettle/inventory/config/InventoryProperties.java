package com.stpl.tech.kettle.inventory.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.stpl.tech.util.EnvType;

@Service
public class InventoryProperties {

	@Autowired
	Environment environment;

	public EnvType getEnvType() {
		return EnvType.valueOf(environment.getProperty("environment.type"));
	}

	public String getInventoryClientToken() {
		return environment.getProperty("inventory.client.token");
	}

	/**
	 * Redis Cache Configuration
	 */
	public String getRedisHost() {
		return environment.getProperty("redis.host", "localhost");
	}

	public int getRedisPort() {
		return Integer.valueOf(environment.getProperty("redis.port", "6379")).intValue();
	}

	public String getRedisClientToken() {
		return environment.getProperty("redis.client.token");
	}

	public int 	getRedisDBIndex() {
		return Integer.valueOf(environment.getProperty("redis.db.index", "3")).intValue();
	}

	public String getRedisPassword() {
		return environment.getProperty("redis.pass", "R3d15D3V");
	}

	/**
	 * System URLs
	 */
	public String getKettleServiceBasePath() {
		return environment.getProperty("base.path.kettle.service");
	}

	public String getMasterServiceBasePath() {
		return environment.getProperty("base.path.master.service");
	}

	public String getSCMServiceBasePath() {
		return environment.getProperty("base.path.kettle.scm");
	}

	public Boolean isPrimary() {
		return Boolean.valueOf(System.getProperty("primary.server", "false"));
	}

	public String[] getSQSQueueZone() {
		return System.getProperty("sqs.zone.name","").split(",");
	}

	/**
	 * MongoDB Configuration
	 */
	public String getMongoURI() {
		return environment.getProperty("spring.data.mongodb.uri");
	}

	public String getMongoSchema() {
		return environment.getProperty("spring.data.mongodb.database");
	}


	public String getJdbcPassword(){
		return environment.getProperty("jdbc.pass");
	}

	public String getJdbcUserName(){
		return environment.getProperty("jdbc.user");
	}

	public String getJdbcDriverClassName(){
		return environment.getProperty("jdbc.driverClassName");
	}

	public String getUrl(){
		return environment.getProperty("jdbc.url");
	}

	public String getHibernateAuto(){
		return environment.getProperty("hibernate.hbm2ddl.auto");
	}

	public String getHibernateDialect(){
		return    environment.getProperty("hibernate.dialect");
	}

	public String getHibernateSql(){
		return environment.getProperty("hibernate.show_sql");
	}

	public String getBasePath() {
		return environment.getProperty("server.base.dir");
	}

	public String getServerZone(){ return environment.getProperty("serverZone","north"); }

}
