package com.stpl.tech.kettle.inventory.util;

public class InventoryServiceEndpoints {

	/**
	 * KETTLE Service URLs
	 */

	public static final String KETTLE_SERVICE_ENTRY_POINT = "/kettle-service/rest/v1/";
	public static final String GET_LAST_DAY_CLOSE = KETTLE_SERVICE_ENTRY_POINT + "order-management/last-day-close";
	public static final String SYNC_MISSING_ORDER_INVENTORY = KETTLE_SERVICE_ENTRY_POINT
			+ "order-management/sync-order-inventory";
	public static final String PRODUCT_INVENTORY = KETTLE_SERVICE_ENTRY_POINT + "pos-metadata/unit/trim/inventory";

	/**
	 * MASTER Service URLs
	 */

	public static final String MASTER_SERVICE_ENTRY_POINT = "/master-service/rest/v1/";
	public static final String GET_ALL_UNITS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/unit/all";
	public static final String GET_RECIPE = MASTER_SERVICE_ENTRY_POINT + "redis-cache/recipe";
	public static final String GET_ALL_RECIPE = MASTER_SERVICE_ENTRY_POINT + "recipe/find-all-with-exclude-ids";
	public static final String GET_UNIT_PRODUCTS = MASTER_SERVICE_ENTRY_POINT + "unit-metadata/unit-product-data";
	public static final String GET_UNIT_PRODUCTS_ALL = MASTER_SERVICE_ENTRY_POINT + "unit-metadata/unit-product-data-all";
	public static final String GET_UNIT_TRIMMED_PRODUCTS_ALL = MASTER_SERVICE_ENTRY_POINT + "unit-metadata/unit-trimmed-product-data-all";
	public static final String GET_ALL_UNIT_BASIC_DETAILS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/ubd/all";
	public static final String GET_UNIT_BASIC_DETAIL = MASTER_SERVICE_ENTRY_POINT + "redis-cache/ubd";
	public static final String GET_EMPLOYEE_BASIC_DETAIL = MASTER_SERVICE_ENTRY_POINT + "user-management/user/active";

	/**
	 * SCM Service URLs
	 */

	public static final String SCM_SERVICE_ENTRY_POINT = "/scm-service/rest/v1/";
	public static final String GET_COST_DETAIL_DATA = SCM_SERVICE_ENTRY_POINT + "scm-data/inventory";
	public static final String GET_UNIT_CLOSURE = SCM_SERVICE_ENTRY_POINT + "scm-data/";

}
