package com.stpl.tech.kettle.inventory.service;


import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.common.UnitProductInventoryRequest;
import com.stpl.tech.kettle.inventory.domain.mongo.InventoryDataVO;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.vo.RecipeInventory;
import org.springframework.web.servlet.View;

import java.util.List;
import java.util.Set;

public interface InventoryManagementService {

	/**
	 * Update Inventory by collecting data
	 * <p>
	 * persist parameter allows the data to be processed with persistence or
	 * skipping persistence when only processing is required
	 *
	 * @param responseData
	 * @param persist
	 *
	 */

	public void updateInventory(QuantityResponseData responseData, boolean persist);

	/**
	 * Pulls cost detail data from SCM unit by unit and update to Redis cache
	 */

	public void refreshAllUnits(boolean cafeOnly);

	/**
	 * Gets Recipe products quantity for Cafe product
	 *
	 * @param unitId
	 * @param productId
	 * @param dimension
	 * @return
	 */
	public List<RecipeInventory> getCafeProductRecipeInventoryData(int unitId, int productId, String dimension, boolean criticalOnly);

	public List<QuantityResponseData> getUnitTimeline(int unitId);

    public List<ProductInventory> getKettleInventory(int unitId);

	public boolean updateSCMProductInventory(InventoryDataVO data);

	Set<Integer> productsExpiryIds(List<InventoryData> list, Integer unitId, Integer count, Integer brandId);

	public void refreshInventoryForUnit(int unitId, boolean b);

	public void deleteMappingForUnit(int unitId);


    void clearCafeProductRecipe(int unitId, int productId, String dimension);

	View getUnitWiseProductInventory(UnitProductInventoryRequest request);
}
