/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.inventory.controller;

import com.stpl.tech.kettle.inventory.core.CafeInventoryReports;
import com.stpl.tech.kettle.inventory.domain.common.InventoryDetailData;
import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.common.UnitProductInventoryRequest;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.domain.redis.InventorySnapShotVO;
import com.stpl.tech.kettle.inventory.domain.redis.UnitSCMCriticalProductMap;
import com.stpl.tech.kettle.inventory.service.InventoryAggregationServices;
import com.stpl.tech.kettle.inventory.service.InventoryDataService;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.kettle.inventory.service.MasterDataService;
import com.stpl.tech.kettle.inventory.service.SnapShotService;
import com.stpl.tech.kettle.inventory.vo.InventoryInfo;
import com.stpl.tech.kettle.inventory.vo.RecipeInventory;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.View;

import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.stpl.tech.kettle.inventory.util.InventoryServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.inventory.util.InventoryServiceConstants.INVENTORY_DATA_ROOT_CONTEXT;
import static com.stpl.tech.kettle.inventory.util.InventoryServiceConstants.SEPARATOR;

/**
 * Hi There!
 * <p>
 * This resource provides inventory data for Menu products and SCM products. All
 * services are explanatory in themselves.
 *
 * <p>
 * Please maintain low request time
 *
 * <AUTHOR>
 *
 */

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + INVENTORY_DATA_ROOT_CONTEXT)
public class InventoryDataResources extends AbstractInventoryResource {

	/**
	 * static sl4j logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(InventoryDataResources.class);

	@Autowired
	private InventoryDataService inventoryService;

	@Autowired
	private SnapShotService snapShotService;

	@Autowired
	private InventoryManagementService inventoryManagementService;

	@Autowired
	private CafeInventoryReports cafeInventoryReports;

	@Autowired
	private InventoryAggregationServices inventoryAggregationServices;

	@Autowired
	private MasterDataService masterDataService;
	/**
	 * Returns Inventory Data for SCM products, this is applicable for all type of
	 * units i.e. CAFE, WAREHOUSE, KITCHEN
	 * <p>
	 * Chai on demand does not have any inventory
	 *
	 * @param unitId
	 * @return {@link InventoryData}
	 *
	 *
	 */
	@RequestMapping(method = RequestMethod.GET, value = "get-scm-products", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryData> getSCMProductInventoryData(@RequestParam("unitId") int unitId) {
		LOG.info("Request to fetch SCM product inventory data for unitId: {}", unitId);
		return inventoryService.getSCMProductInventoryData(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-scm-products/expire", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryData> getExpiringSCMProductInventoryData(@RequestParam("unitId") int unitId) {
		LOG.info("Request to fetch Expiring SCM product inventory data for unitId: {}", unitId);
		UnitSCMCriticalProductMap map = inventoryService.getCriticalSCMProducts(unitId);
		return inventoryService.getSCMProductInventoryData(unitId).stream()
				.filter(p -> BigDecimal.ZERO.compareTo(p.getExQty()) != 0 && map != null
						&& map.getCriticalProducts().contains(p.getId()))
				.collect(Collectors.toList());
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-scm-products/trimmed", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<Integer, BigDecimal> getSCMProductInventoryDataTrimmed(@RequestParam("unitId") int unitId) {
		LOG.info("Request to fetch SCM product inventory data for unitId: {}", unitId);
		return inventoryService.getSCMProductInventoryDataTrimmed(unitId);
	}

	/**
	 * Returns Inventory Data for MENU Products, this is applicable for CAFE units
	 * only, as KITCHEN, WAREHOUSE does not have MENU products
	 * <p>
	 * Chai on demand does not have any inventory
	 *
	 * @param unitId
	 * @return {@link InventoryData}
	 *
	 */

	@RequestMapping(method = RequestMethod.GET, value = "get-cafe-products", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryData> getCafeProductInventoryData(@RequestParam("unitId") int unitId) {
		LOG.info("Request to fetch Cafe product inventory data for unitId: {}", unitId);
		return inventoryService.getCafeProductInventoryData(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "save-cafe-inventory-sheet", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean saveCafeInventorySheet(@RequestParam Integer empId) {
		List<List<String>> salesData = new ArrayList<>();
		for (UnitBasicDetail unitBasicDetail : inventoryAggregationServices.getAllUnitBasicDetails()) {
			if (AppConstants.ACTIVE.equals(unitBasicDetail.getStatus())) {

				try{
					List<ProductInventory> inventoryList = getKettleInventory(unitBasicDetail.getId());

					if(inventoryList.size()!=0){
						for(ProductInventory ele:inventoryList){
							if(salesData.size()==0){
								List<String> obj1 = new ArrayList<>();
								obj1.add("Unit Id");
								obj1.add("Product Id");
								obj1.add("Quantity");
								salesData.add(obj1);
							}
							List<String> obj = new ArrayList<>();
							obj.add(unitBasicDetail.getId()+"");
							obj.add(ele.getId()+"");
							obj.add(ele.getQuantity()+"");
							salesData.add(obj);
						}
					}
				} catch (Exception e) {
					LOG.error("Skipping Fetching inventory for unitId {}", unitBasicDetail.getId(), e);
				}
			}
		}
		try {
			cafeInventoryReports.renderInventory(salesData,masterDataService.getEmployeeBasicDetail(empId).getEmailId());
			LOG.info("Excel Sheet Successfully Created");
			return true;
		} catch (Exception e) {
			LOG.error("Error in generating Excel Sheets", e);
			return  false;
		}
	}

	@RequestMapping(method = RequestMethod.GET, value = "gcp", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryDetailData> getCafeProductInventoryDetailData(@RequestParam("unitId") int unitId) {
		LOG.info("Request to fetch Cafe product inventory data for unitId: {}", unitId);
		return inventoryService.getCafeProductInventoryDetailData(unitId);
	}

	/**
	 * Returns Quantity for all products in recipe of the Cafe product to determine
	 * which product (lowest quantity) is resulting in the menu product quantity
	 *
	 * @param unitId
	 * @param productId
	 * @return
	 *
	 */
	@RequestMapping(method = RequestMethod.GET, value = "get-recipe", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<RecipeInventory> getCafeProductRecipe(@RequestParam("unitId") int unitId,
			@RequestParam("productId") int productId, @RequestParam("dimension") String dimension,
			@RequestParam(value = "onlyCritical", required = false) Boolean critical) {
		LOG.info("Request to fetch Cafe product recipe inventory data for unitId: {} and productId {}", unitId,
				productId);
		return inventoryService.getCafeProductRecipeInventoryData(unitId, productId, dimension,
				critical == null ? false : critical);
	}

	/**
	 * Returns Time line view data
	 *
	 * @param unitId
	 */
	@RequestMapping(method = RequestMethod.GET, value = "get-timeline", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<QuantityResponseData> getUnitTimeline(@RequestParam("unitId") int unitId) {
		LOG.info("Request to fetch Cafe product recipe inventory data for unitId: {}", unitId);
		return inventoryService.getUnitTimeline(unitId);
	}

	/**
	 * Made a post call as its counter part in kettle is a post call
	 *
	 * @param unitId
	 * @return
	 *
	 */
	@RequestMapping(method = RequestMethod.POST, value = "cafe-inventory", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryInfo> getCafeInventoryData(@RequestBody final int unitId) {

		LOG.info("Request to fetch Cafe inventory for unitId: {}", unitId);

		List<InventoryData> list = inventoryService.getCafeProductInventoryData(unitId);
		Map<Integer, InventoryInfo> map = new HashMap<>();
		for (InventoryData d : list) {
			if (d != null && d.getQty() != null) {
				InventoryInfo info = map.get(d.getId());
				if (info == null) {
					info = new InventoryInfo(d.getId(), d.getQty().intValue(), d.getExQty().intValue());
					map.put(d.getId(), info);
				} else {
					if (info.getQuantity() < d.getQty().intValue()) {
						info.setQuantity(d.getQty().intValue() > 0 ? d.getQty().intValue() : 0);
					}
					info.setEx(d.getExQty().intValue() > 0 ? d.getExQty().intValue() : 0);
				}
				info.getDim().put(d.getU(), d.getQty().intValue());
			}
		}

		for (InventoryInfo i : map.values()) {
			if (i.getDim() != null && i.getDim().size() < 2) {
				i.setDim(null);
			}
		}

		return new ArrayList<>(map.values());
	}

	@RequestMapping(method = RequestMethod.POST, value = "spi", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryDetailData> getProductInventoryDetail(@RequestBody final Map productIdsMap){
		Set<Integer> productList = new HashSet<>((ArrayList<Integer>) productIdsMap.get("productIds"));
		int unitId = Integer.parseInt(productIdsMap.get("unitId").toString());
		List<InventoryDetailData> list = inventoryService.getCafeProductInventoryDetailData(unitId);
		List<InventoryDetailData> resultant = new ArrayList<>();
		for(InventoryDetailData d : list){
			if (d != null && productList.contains(d.getId()) && d.getQty() != null) {
				resultant.add(d);
			}
		}
		return resultant;
	}

	@RequestMapping(method = RequestMethod.POST, value = "cafe-inventory/products", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<InventoryInfo> getUnitInventoryForProducts(@RequestBody final Map productIdsMap) {
		Set<Integer> productList = new HashSet<>((ArrayList<Integer>) productIdsMap.get("productIds"));
		int unitId = Integer.parseInt(productIdsMap.get("unitId").toString());
		List<InventoryData> list = inventoryService.getCafeProductInventoryData(unitId);
		Map<Integer, InventoryInfo> map = new HashMap<>();

		for (InventoryData d : list) {
			if (d != null && productList.contains(d.getId()) && d.getQty() != null) {
				InventoryInfo info = map.get(d.getId());
				if (info == null) {
					info = new InventoryInfo(d.getId(), d.getQty().intValue(), d.getExQty().intValue());
					map.put(d.getId(), info);
				} else {
					if (info.getQuantity() < d.getQty().intValue()) {
						info.setQuantity(d.getQty().intValue() > 0 ? d.getQty().intValue() : 0);
					}
					info.setEx(d.getExQty().intValue() > 0 ? d.getExQty().intValue() : 0);
				}
				info.getDim().put(d.getU(), d.getQty().intValue());
			}
		}
		for (InventoryInfo i : map.values()) {
			if (i.getDim() != null && i.getDim().size() < 2) {
				i.setDim(null);
			}
		}
		return new ArrayList<>(map.values());
	}

	/**
	 * Inventory Call for web services requiring product id to quantity map
	 *
	 * @param unitId
	 * @return
	 *
	 */
	@RequestMapping(method = RequestMethod.POST, value = "cafe-inventory/web", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<Integer, Integer> getCafeInventoryDataForWeb(@RequestBody final int unitId) {

		LOG.info("Request to fetch Cafe inventory for unitId: {} for web", unitId);

		Map<Integer, Integer> inventory = new HashMap<Integer, Integer>();
		List<InventoryData> list = inventoryService.getCafeProductInventoryData(unitId);
		for (InventoryData d : list) {
			if (d != null && d.getQty() != null) {
				inventory.put(d.getId(), d.getQty().intValue());
			}
		}
		return inventory;
	}

	/**
	 * Inventory Call for web services requiring product id to quantity map
	 *
	 * @param unitId
	 * @return
	 *
	 */
	@RequestMapping(method = RequestMethod.GET, value = "kettle-inventory", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<ProductInventory> getKettleInventory(@RequestParam final int unitId) {
		LOG.info("Request to fetch Cafe inventory updated in Kettle for unitId: {}", unitId);
		return inventoryService.getKettleInventory(unitId);
	}

	/**
	 * Inventory Call for web services requiring expiration cost to cafe
	 *
	 * @param unitId
	 * @return
	 *
	 */
	@RequestMapping(method = RequestMethod.GET, value = "snapshot", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public InventorySnapShotVO getScmSnapShot(@RequestParam final int unitId) {
		LOG.info("Request to fetch Cafe expiry cost for unitId: {}", unitId);
		InventorySnapShotVO s = snapShotService.getCurrentSnapShotVO(unitId);
		UnitSCMCriticalProductMap map = inventoryService.getCriticalSCMProducts(unitId);
		if (s != null) {
			List<InventoryData> l = new ArrayList<>();
			for (InventoryData d : s.getInventory()) {
				if (map != null && map.getCriticalProducts().contains(d.getId()) && d.getExQty() != null
						&& d.getExQty().intValue() > 0) {
					l.add(d);
				}
			}
			s.setInventory(l);
		}
		return s;
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-cafe-products/expire", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Set<Integer> getCafeExpiringProduct(@RequestParam("unitId") Integer unitId,
			@RequestParam("count") Integer count, @RequestParam("brandId") Integer brandId) {
		LOG.info("Request to fetch Expiring CAFE product inventory data for unitId: {},  count:{} ", unitId, count);
		UnitSCMCriticalProductMap map = inventoryService.getCriticalSCMProducts(unitId);
		List<InventoryData> list = inventoryService.getSCMProductInventoryData(unitId).stream()
				.filter(p -> BigDecimal.ZERO.compareTo(p.getExQty()) != 0 && map != null
						&& map.getCriticalProducts().contains(p.getId()))
				.collect(Collectors.toList());
		LOG.info("Request to fetch productIds");
		return inventoryManagementService.productsExpiryIds(list, unitId, count, brandId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "get-unitwise-product-inventory")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public View getCafeExpiringProduct(@RequestBody UnitProductInventoryRequest request) {
		LOG.info("Request to fetch Expiring CAFE product inventory data for   for {} products", request.getProducts());
		return inventoryManagementService.getUnitWiseProductInventory(request);
	}
}
