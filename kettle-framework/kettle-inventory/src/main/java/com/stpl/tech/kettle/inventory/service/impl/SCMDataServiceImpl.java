package com.stpl.tech.kettle.inventory.service.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.domain.mongo.CostDetail;
import com.stpl.tech.kettle.inventory.service.SCMDataService;
import com.stpl.tech.kettle.inventory.util.InventoryServiceEndpoints;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.util.domain.adapter.DateDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SCMDataServiceImpl implements SCMDataService {

	private static final Logger LOG = LoggerFactory.getLogger(SCMDataServiceImpl.class);

	@Autowired
	private InventoryProperties properties;

	/**
	 * Provides cost detail data for a particular unit
	 * 
	 * @param unitId
	 * @return List
	 * @throws URISyntaxException
	 */
	@Override
	public List<CostDetail> getCostDetails(int unitId) throws URISyntaxException {
		long startTime = System.currentTimeMillis();
		String endPoint = properties.getSCMServiceBasePath() + InventoryServiceEndpoints.GET_COST_DETAIL_DATA;
		String token = properties.getInventoryClientToken();
		Map<String, Object> reqObj = new HashMap<>();
		reqObj.put("unitId", Integer.valueOf(unitId));
		List<?> list = WebServiceHelper.postWithAuth(endPoint, token, unitId, List.class);
		List<CostDetail> data = new ArrayList<>();
		GsonBuilder gSonBuilder = new GsonBuilder();
		gSonBuilder.registerTypeAdapter(Date.class, new DateDeserializer());
		list.forEach(p -> {
			Gson gson = gSonBuilder.create();
			String str = gson.toJson(p);
			CostDetail cat = gson.fromJson(str, CostDetail.class);
			data.add(cat);
		});
		LOG.info("Downloaded CostDetails for unitId {} in {} miliseconds", unitId, System.currentTimeMillis() - startTime);
		return data;
	}

	@Override
	public DayCloseEvent getLastUnitClosure(int unitId) {
		long startTime = System.currentTimeMillis();
		String endPoint = properties.getSCMServiceBasePath() + InventoryServiceEndpoints.GET_UNIT_CLOSURE;
		String token = properties.getInventoryClientToken();
		DayCloseEvent event = WebServiceHelper.postWithAuth(endPoint, token, unitId, DayCloseEvent.class);
		LOG.info("Downloaded Day Close Event for unitId {} in {} miliseconds", unitId, System.currentTimeMillis() - startTime);
		return event;
	}

	
}
