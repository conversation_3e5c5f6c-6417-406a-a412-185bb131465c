package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

@RedisHash("UnitSCMCriticalProductMap")
public class UnitSCMCriticalProductMap implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2974139527482620973L;
	@Id
	private int unitId;
	private List<Integer> criticalProducts;

	public UnitSCMCriticalProductMap() {
	}

	public UnitSCMCriticalProductMap(int unitId, List<Integer> criticalProducts) {
		super();
		this.unitId = unitId;
		this.criticalProducts = criticalProducts;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<Integer> getCriticalProducts() {
		if (criticalProducts == null) {
			criticalProducts = new ArrayList<>();
		}
		return criticalProducts;
	}

	public void setCriticalProducts(List<Integer> criticalProducts) {
		this.criticalProducts = criticalProducts;
	}

}
