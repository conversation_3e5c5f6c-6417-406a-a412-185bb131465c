package com.stpl.tech.kettle.inventory.service.impl;

import org.springframework.stereotype.Component;
import org.springframework.util.ConcurrentReferenceHashMap;

@Component
public class KeyMutexFactory {

    private ConcurrentReferenceHashMap<String, Object> map;

    public KeyMutexFactory() {
        this.map = new ConcurrentReferenceHashMap<>();
    }

    public Object getMutex(String key) {
        return this.map.compute(key, (k, v) -> v == null ? new Object() : v);
    }
}