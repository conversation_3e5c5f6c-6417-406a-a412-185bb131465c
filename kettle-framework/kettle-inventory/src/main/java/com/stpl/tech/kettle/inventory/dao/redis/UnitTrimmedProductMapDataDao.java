package com.stpl.tech.kettle.inventory.dao.redis;

import org.springframework.data.repository.CrudRepository;

import com.stpl.tech.kettle.inventory.domain.redis.UnitTrimmedProductMapData;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitTrimmedProductMapDataDao  extends CrudRepository<UnitTrimmedProductMapData, String> {

   List<UnitTrimmedProductMapData> findAll();

   List<UnitTrimmedProductMapData> findByUnitId(int unitId);

   List<UnitTrimmedProductMapData> findByUnitIdAndBrandId(int unitId, Integer brandId);

}
