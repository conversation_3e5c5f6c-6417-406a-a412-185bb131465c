/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.inventory.util;

public class InventoryServiceConstants {

	public static final String API_VERSION = "v1";

	public static final String SEPARATOR = "/";

	public static final String INVENTORY_DATA_ROOT_CONTEXT = "inventory-data";

	public static final String INVENTORY_MANAGEMENT_ROOT_CONTEXT = "inventory-management";

	public static final String SCM = "SCM";

	public static final String KETTLE = "KETTLE";

}
