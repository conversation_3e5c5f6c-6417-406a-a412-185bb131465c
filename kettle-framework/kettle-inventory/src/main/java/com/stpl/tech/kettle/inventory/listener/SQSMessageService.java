package com.stpl.tech.kettle.inventory.listener;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;

import java.util.Objects;

@Service
public class SQSMessageService {

	/**
	 * static sl4j Logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(SQSMessageService.class);

	@Autowired
	private InventoryProperties props;

	@Autowired
	private InventoryManagementService service;

	/**
	 * Initializes SQS message listener service with error and main queue
	 *
	 * @throws JMSException
	 */
	@PostConstruct
	public void init() throws JMSException {
		if (!props.isPrimary()) {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& SKIPPING SQSMessageService Listener init FOR INVENTORY MODULE AS THIS IS NOT PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
		} else {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& RUNNING SQSMessageService Listener init FOR INVENTORY MODULE AS THIS IS A PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
			Regions region = AppUtils.getRegion(props.getEnvType());
			String[] unitZone = props.getSQSQueueZone();
			SQSSession errSession = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
			MessageProducer producer = SQSNotification.getInstance().getProducer(errSession, props.getEnvType().name(),
					"_INVENTORY_ERRORS");
			if(Objects.nonNull(unitZone) && unitZone.length>0 && !unitZone[0].equals("")){
				for (String zone : unitZone){
					SQSSession newInventorySession = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
					MessageProducer newInventoryQueue = SQSNotification.getInstance().getProducer(newInventorySession, props.getEnvType().name(),
							"_INVENTORY_NEW_"+zone.toUpperCase());
					InventoryMessageListener listener = new InventoryMessageListener(newInventoryQueue,producer, service);
					SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
					MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvType().name(),
							"_INVENTORY_"+zone.toUpperCase());
					consumer.setMessageListener(listener);
					SQSNotification.getInstance().getSqsConnection(region).start();
				}
			} else {
				SQSSession newInventorySession = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
				MessageProducer newInventoryQueue = SQSNotification.getInstance().getProducer(newInventorySession, props.getEnvType().name(),
						"_INVENTORY_NEW"+"_NORTH");
				InventoryMessageListener listener = new InventoryMessageListener(newInventoryQueue,producer, service);
				SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
				MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvType().name(),
						"_INVENTORY" +"_NORTH");
				consumer.setMessageListener(listener);
				SQSNotification.getInstance().getSqsConnection(region).start();
			}
		}
	}

	/**
	 * Method to stop Inventory queue listener.
	 * <p>
	 * To be used in pair with startQueueProcessing
	 *
	 * @throws JMSException
	 */
	public void stopQueueProcessing(Regions region) throws JMSException {
		LOG.info("Stopping Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).stop();
		LOG.info("Stopped Queueing Service");
	}

	/**
	 * Method to start Inventory queue listener.
	 * <p>
	 * To be used in pair with stopQueueProcessing
	 *
	 * @throws JMSException
	 */
	public void startQueueProcessing(Regions region) throws JMSException {
		LOG.info("Starting Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).start();
		LOG.info("Started Queueing Service");
	}
}
