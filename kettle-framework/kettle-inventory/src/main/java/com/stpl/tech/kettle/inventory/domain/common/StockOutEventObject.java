package com.stpl.tech.kettle.inventory.domain.common;

import java.util.Date;

public class StockOutEventObject {
    protected String unitName;

    protected int productId;

    protected String productName;

    protected int noOfUnits;

    protected Date timeStamp;

    protected int cafeManagerId;

    protected int managerId;

    public StockOutEventObject(String unitName, int productId, String productName, int noOfUnits, Date timeStamp, int cafeManagerId, int managerId) {
        this.unitName = unitName;
        this.productId = productId;
        this.productName = productName;
        this.noOfUnits = noOfUnits;
        this.timeStamp = timeStamp;
        this.cafeManagerId = cafeManagerId;
        this.managerId = managerId;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getNoOfUnits() {
        return noOfUnits;
    }

    public void setNoOfUnits(int noOfUnits) {
        this.noOfUnits = noOfUnits;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public int getCafeManagerId() {
        return cafeManagerId;
    }

    public void setCafeManagerId(int cafeManagerId) {
        this.cafeManagerId = cafeManagerId;
    }

    public int getManagerId() {
        return managerId;
    }

    public void setManagerId(int managerId) {
        this.managerId = managerId;
    }
}
