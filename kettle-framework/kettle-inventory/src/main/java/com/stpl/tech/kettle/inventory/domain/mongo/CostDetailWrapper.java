package com.stpl.tech.kettle.inventory.domain.mongo;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
public class CostDetailWrapper {

	@Id
	protected String id;
	@Indexed
	protected Integer unitId;
	protected String unitName;
	@Indexed
	protected Date businessDate;
	protected List<CostDetail> list;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<CostDetail> getList() {
		return list;
	}

	public void setList(List<CostDetail> list) {
		this.list = list;
	}

}
