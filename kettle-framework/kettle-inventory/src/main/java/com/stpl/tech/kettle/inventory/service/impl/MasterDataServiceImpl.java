package com.stpl.tech.kettle.inventory.service.impl;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.service.MasterDataService;
import com.stpl.tech.kettle.inventory.util.InventoryServiceEndpoints;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;

@Service
public class MasterDataServiceImpl implements MasterDataService {

	private static final Logger LOG = LoggerFactory.getLogger(SCMDataServiceImpl.class);

	@Autowired
	private InventoryProperties properties;

	/**
	 * Provides cafe Products for a particular unit
	 * 
	 * @param unitId
	 * @return List
	 * @throws URISyntaxException
	 */
	@Override
	public List<Product> getAllCafeProducts(int unitId) {
		long startTime = System.currentTimeMillis();
		String endPoint = properties.getMasterServiceBasePath() + InventoryServiceEndpoints.GET_UNIT_PRODUCTS_ALL;
		String token = properties.getInventoryClientToken();
		List<?> list = WebServiceHelper.postWithAuth(endPoint, token, unitId, List.class);
		List<Product> data = new ArrayList<>();
		GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
		list.forEach(p -> {
			Gson gson = gSonBuilder.create();
			String str = gson.toJson(p);
			Product cat = gson.fromJson(str, Product.class);
			data.add(cat);
		});
		LOG.info("Downloaded Product Data from master in {} miliseconds", System.currentTimeMillis() - startTime);
		return data;
	}

	@Override
	public List<TrimmedProductVO> getAllCafeTrimmedProducts(int unitId) {
		long startTime = System.currentTimeMillis();
		String endPoint = properties.getMasterServiceBasePath()
				+ InventoryServiceEndpoints.GET_UNIT_TRIMMED_PRODUCTS_ALL;
		String token = properties.getInventoryClientToken();
		List<?> list = WebServiceHelper.postWithAuth(endPoint, token, unitId, List.class);
		List<TrimmedProductVO> data = new ArrayList<>();
		GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
		list.forEach(p -> {
			Gson gson = gSonBuilder.create();
			String str = gson.toJson(p);
			//LOG.info("Serialized : {}", str);
			TrimmedProductVO cat = gson.fromJson(str, TrimmedProductVO.class);
			data.add(cat);
		});
		LOG.info("Downloaded Trimmed Product Data with size {} from master in {} miliseconds", data.size(),
				System.currentTimeMillis() - startTime);
		return data;
	}

	
	/**
	 * Get Recipe data for a recipe Id
	 * 
	 * @param recipeId
	 * @return {@link RecipeDetail}s
	 */
	@Override
	public RecipeDetail getRecipeDetail(String recipeId) {
		long startTime = System.currentTimeMillis();
		LOG.info("Request to fetch Recipe : {}", recipeId);
		String endPoint = properties.getMasterServiceBasePath() + InventoryServiceEndpoints.GET_RECIPE;
		String token = properties.getInventoryClientToken();
		RecipeDetail data = null;
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("recipeId", recipeId);
		try {
			data = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET, RecipeDetail.class, recipeId,
					uriVariables);
		} catch (URISyntaxException e) {
			LOG.error("Failed to download recipe {}", recipeId, e);
		}
		LOG.info("Downloaded Recipe Data for recipe {} from master in {} miliseconds", recipeId,
				System.currentTimeMillis() - startTime);
		return data;

	}

	/**
	 * Get All Unit basic details for Master Cache
	 */
	@Override
	public List<UnitBasicDetail> getAllUnitBasicDetail() {
		long startTime = System.currentTimeMillis();
		LOG.info("Request to fetch Unit Basic Details");
		String endPoint = properties.getMasterServiceBasePath() + InventoryServiceEndpoints.GET_ALL_UNIT_BASIC_DETAILS;
		String token = properties.getInventoryClientToken();
		Map<String, String> uriVariables = new HashMap<>();
		List<?> response = null;
		try {
			response = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.GET, List.class, null,
					uriVariables);
		} catch (URISyntaxException e) {
			LOG.error("Failed to download All Unit Basic Details", e);
		}
		List<UnitBasicDetail> list = new ArrayList<>();
		GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
		response.forEach(p -> {
			Gson gson = gSonBuilder.create();
			String str = gson.toJson(p);
			UnitBasicDetail d = gson.fromJson(str, UnitBasicDetail.class);
			list.add(d);
		});
		LOG.info("Downloaded Unit Basic Details from master in {} miliseconds", System.currentTimeMillis() - startTime);
		return list;

	}

	/**
	 * Get a single unit from master
	 */
	@Override
	public UnitBasicDetail getUnitBasicDetail(int unitId) {
		long startTime = System.currentTimeMillis();
		LOG.info("Request to fetch Unit Basic Detail for unitId : {}", unitId);
		String endPoint = properties.getMasterServiceBasePath() + InventoryServiceEndpoints.GET_UNIT_BASIC_DETAIL;
		String token = properties.getInventoryClientToken();
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("unitId", String.valueOf(unitId));
		UnitBasicDetail response = null;
		try {
			response = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.POST, UnitBasicDetail.class,
					String.valueOf(unitId), uriVariables);
		} catch (URISyntaxException e) {
			LOG.error("Failed to download All Unit Basic Details", e);
		}
		LOG.info("Downloaded Unit Basic Details for Unit Id {}  from master in {} miliseconds", unitId,
				System.currentTimeMillis() - startTime);
		return response;
	}

	@Override
	public EmployeeBasicDetail getEmployeeBasicDetail(int empId) {
		long startTime = System.currentTimeMillis();
		LOG.info("Request to fetch Employee basic Detail for : {}", empId);
		String endPoint = properties.getMasterServiceBasePath() + InventoryServiceEndpoints.GET_EMPLOYEE_BASIC_DETAIL;
		String token = properties.getInventoryClientToken();
		EmployeeBasicDetail data = null;
		Map<String, String> uriVariables = new HashMap<>();
		uriVariables.put("empId", String.valueOf(empId));
		try {
			data = WebServiceHelper.exchangeWithAuth(endPoint, token, HttpMethod.POST, EmployeeBasicDetail.class,
					String.valueOf(empId), uriVariables);
		} catch (URISyntaxException e) {
			LOG.error("Failed to download employee {}", empId, e);
		}
		LOG.info("Downloaded Employee basic Detail for employee {} from master in {} miliseconds", empId,
				System.currentTimeMillis() - startTime);
		return data;

	}

	@Override
	public List<RecipeDetail> getAllRecipeDetail(List<Integer> excludeIds) {
		long startTime = System.currentTimeMillis();
		LOG.info("Request to fetch Recipes with exclude ids as  : {}", excludeIds);
		String endPoint = properties.getMasterServiceBasePath() + InventoryServiceEndpoints.GET_ALL_RECIPE;
		String token = properties.getInventoryClientToken();

		List<?> list = WebServiceHelper.postWithAuth(endPoint, token, excludeIds, List.class);
		List<RecipeDetail> data = new ArrayList<>();
		GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
		list.forEach(p -> {
			Gson gson = gSonBuilder.create();
			String str = gson.toJson(p);
			RecipeDetail cat = gson.fromJson(str, RecipeDetail.class);
			data.add(cat);
		});

		LOG.info("Downloaded Recipe Data for recipes with exlude list {} from master in {} miliseconds", excludeIds,
				System.currentTimeMillis() - startTime);
		return data;

	}

}
