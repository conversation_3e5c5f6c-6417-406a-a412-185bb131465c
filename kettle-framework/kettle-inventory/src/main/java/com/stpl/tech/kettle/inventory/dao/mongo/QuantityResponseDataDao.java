package com.stpl.tech.kettle.inventory.dao.mongo;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.master.inventory.model.InventorySource;

@Repository
public interface QuantityResponseDataDao extends MongoRepository<QuantityResponseData, String> {

	@Query("{'unitId' : ?0, 'eventTime' : {'$gte' : ?1} , 'source' : ?2}")
	public List<QuantityResponseData> getByUnitIdAndEventTimeAndInventorySource(int unitId, Date startTime,
			InventorySource source);
	
	@Query("{'unitId' : ?0, 'orderId' : {'$gt' : ?1} , 'source' : ?2}")
	public List<QuantityResponseData> getByUnitIdAndOrderIdAndInventorySource(int unitId, int orderId,
			InventorySource source);

	@Query("{'eventTime' : {'$lte' : ?0} }")
	public List<QuantityResponseData> getByEventTime(Date startTime);

	@Query("{'unitId' : ?0}")
	public List<QuantityResponseData> findByUnitId(int unitId);

}
