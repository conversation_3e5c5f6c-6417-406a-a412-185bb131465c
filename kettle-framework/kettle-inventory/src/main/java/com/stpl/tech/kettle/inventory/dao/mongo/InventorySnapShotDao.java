package com.stpl.tech.kettle.inventory.dao.mongo;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.inventory.domain.mongo.InventorySnapShot;

@Repository
public interface InventorySnapShotDao extends MongoRepository<InventorySnapShot, Integer> {

	@Query("{'unitId' : ?0 , 'snapShotStatus' : ?1 }")
	public InventorySnapShot findByUnitIdAndStatus(int unitId, String status);

	@Query("{'unitId' : ?0 , 'snapShotStatus' : ?1 }")
	public List<InventorySnapShot> findAllByUnitIdAndStatus(int unitId, String name);

	@Query("{'snapShotStatus' : ?0 }")
	public List<InventorySnapShot> findAllByStatus(String name);
}
