package com.stpl.tech.kettle.inventory.domain.common;

import java.util.List;

public class UnitProductInventoryRequest {
    private List<UnitData> units;
    List<ProductInventory> products;

    public List<UnitData> getUnits() {
        return units;
    }

    public void setUnits(List<UnitData> units) {
        this.units = units;
    }

    public List<ProductInventory> getProducts() {
        return products;
    }

    public void setProducts(List<ProductInventory> products) {
        this.products = products;
    }
}
