package com.stpl.tech.kettle.inventory.service;

import com.stpl.tech.kettle.inventory.domain.mongo.CostDetail;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.domain.redis.UnitProductTrimmedData;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.domain.model.DayCloseEvent;

import java.util.List;

public interface InventoryAggregationServices {

	public List<CostDetail> fetchCostDetailData(int unitId);

	public List<UnitBasicDetail> getAllUnitBasicDetails(boolean purge);

	public List<UnitBasicDetail> getAllUnitBasicDetails();

	public UnitBasicDetail getUnitBasicDetail(int unitId);

    UnitBasicDetail getUnitBasicDetail(int unitId, boolean force);

    public void purgeData();

	public DayCloseEvent getLastScmUnitClosure(int unitId);

	public void refreshCafeProducts(int unitId);

	public EmployeeBasicDetail getEmployeeBasicDetail(int unitManagerId);

	UnitProductTrimmedData getAllCafeTrimmedProducts(int unitId);
}
