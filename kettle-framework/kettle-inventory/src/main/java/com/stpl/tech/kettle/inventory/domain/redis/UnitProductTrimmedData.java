package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import com.stpl.tech.master.domain.model.TrimmedProductVO;

@RedisHash("UnitProductTrimmedData")
public class UnitProductTrimmedData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7625650793221054043L;
	@Id
	private int unitId;
	private List<TrimmedProductVO> products;

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<TrimmedProductVO> getProducts() {
		return products;
	}

	public void setProducts(List<TrimmedProductVO> products) {
		this.products = products;
	}
}
