package com.stpl.tech.kettle.inventory.schedule;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.dao.mongo.CostDetailWrapperDao;
import com.stpl.tech.kettle.inventory.dao.mongo.QuantityResponseDataDao;
import com.stpl.tech.kettle.inventory.domain.common.StockOutData;
import com.stpl.tech.kettle.inventory.domain.mongo.CostDetailWrapper;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.notification.StockOutNotification;
import com.stpl.tech.kettle.inventory.service.InventoryAggregationServices;
import com.stpl.tech.kettle.inventory.service.InventoryDataService;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.kettle.inventory.service.SnapShotService;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;

/**
 * Scheduling class to contain all schedule tasks for inventory service
 *
 * <AUTHOR>
 */
@Component
public class InventoryScheduler {

	/**
	 * static sl4j logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(InventoryScheduler.class);

	@Autowired
	private CostDetailWrapperDao costDetailWrapperDao;

	@Autowired
	private QuantityResponseDataDao quantityResponseDataDao;

	@Autowired
	private InventoryManagementService inventoryManagementService;

	@Autowired
	private SnapShotService snapshotService;

	@Autowired
	private InventoryAggregationServices inventoryAggregationServices;

	@Autowired
	private InventoryDataService inventoryDataService;

	@Autowired
	private InventoryProperties props;

	@Scheduled(cron = "0 0 5 * * *", zone = "GMT+05:30")
	public void deleteOldRecords() throws ParseException, EmailGenerationException, IOException {
		if (!props.isPrimary()) {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& SKIPPING deleteOldRecords scheduled FOR INVENTORY MODULE AS THIS IS NOT PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
		} else {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& RUNNING deleteOldRecords scheduled FOR INVENTORY MODULE AS THIS IS A PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
			LOG.info("running inventory schedule task");
			Date currentDate = AppUtils.getBusinessDate();
			Date date = AppUtils.getOldDate(currentDate, 2);
			List<CostDetailWrapper> oldWrappers = costDetailWrapperDao.findOldRecordsByBusinessDate(date);
			costDetailWrapperDao.deleteAll(oldWrappers);
			quantityResponseDataDao.deleteAll(quantityResponseDataDao.getByEventTime(date));
		}

	}

	//@Scheduled(cron = "0 0 7 * * *", zone = "GMT+05:30")
	public void cleanUp() throws ParseException, EmailGenerationException, IOException {
		if (!props.isPrimary()) {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& SKIPPING cleanUp scheduled FOR INVENTORY MODULE AS THIS IS NOT PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
		} else {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& RUNNING cleanUp scheduled FOR INVENTORY MODULE AS THIS IS A PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
			LOG.info("running inventory cache refresh schedule task");
			inventoryManagementService.refreshAllUnits(false);
		}
	}

	@Scheduled(cron = "0 15 6 * * *", zone = "GMT+05:30")
	public void createWastageDataSnapShot() throws ParseException, EmailGenerationException, IOException {
		if (!props.isPrimary()) {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& SKIPPING createWastageDataSnapShot scheduled FOR INVENTORY MODULE AS THIS IS NOT PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
		} else {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& RUNNING createWastageDataSnapShot scheduled FOR INVENTORY MODULE AS THIS IS A PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
			LOG.info("running inventory schedule task to create Expiry SnapShot");
			for (UnitBasicDetail unitBasicDetail : inventoryAggregationServices.getAllUnitBasicDetails()) {
				if (AppConstants.ACTIVE.equals(unitBasicDetail.getStatus())) {
					snapshotService.createDailySnapShot(unitBasicDetail.getId());
				}
			}
		}
	}

	@Scheduled(cron = "0 0 * * * *", zone = "GMT+05:30")
	public void hourlyStockOutReport()
			throws ParseException, EmailGenerationException, IOException, DataUpdationException {
		if (!props.isPrimary()) {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& SKIPPING hourlyStockOutReport scheduled FOR INVENTORY MODULE AS THIS IS NOT PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
		} else {
			LOG.info(
					"&&&&&&&&&&&&&&&&&&& RUNNING hourlyStockOutReport scheduled FOR INVENTORY MODULE AS THIS IS A PRIMARY SERVER  &&&&&&&&&&&&&&&&&&&");
			LOG.info("running inventory schedule task to create stock out report");

			if (AppUtils.isDev(props.getEnvType())) {
				return;
			}

			List<Integer> skipStockOuts = new ArrayList<Integer>();
			skipStockOuts.add(26007);
			skipStockOuts.add(26008);
			skipStockOuts.add(26009);
			skipStockOuts.add(26010);
			skipStockOuts.add(26011);
			skipStockOuts.add(26097);
			skipStockOuts.add(26098);
			skipStockOuts.add(26070);

			List<StockOutData> list = new ArrayList<>();
			for (UnitBasicDetail unitBasicDetail : inventoryAggregationServices.getAllUnitBasicDetails()) {
				if (AppConstants.ACTIVE.equals(unitBasicDetail.getStatus()) && unitBasicDetail.isLiveInventoryEnabled()
						&& unitBasicDetail.isLive() && UnitCategory.CAFE.name().equals(unitBasicDetail.getCategory())) {
					if (skipStockOuts.contains(unitBasicDetail.getId())) {
						continue;
					}
					List<InventoryData> inventoryData = inventoryDataService
							.getCafeProductInventoryData(unitBasicDetail.getId());

					Integer cafeManagerId = unitBasicDetail.getCafeManagerId();
					EmployeeBasicDetail cafeManager = null;
					if (cafeManagerId != null) {
						cafeManager = inventoryAggregationServices.getEmployeeBasicDetail(cafeManagerId);
					}
					for (InventoryData i : inventoryData) {
						if (BigDecimal.ZERO.compareTo(i.getQty()) >= 0) {
							list.add(new StockOutData(cafeManager != null ? cafeManager.getName() : "",
									unitBasicDetail.getName(), i.getName(), i.getU()));
						}
					}
				}
			}
			StockOutNotification stockOutNotification = new StockOutNotification(list, props.getEnvType(),
					AppUtils.getCurrentTimestamp());
			stockOutNotification.sendEmail();
		}
	}
}
