package com.stpl.tech.kettle.inventory.service;

import com.stpl.tech.kettle.inventory.domain.mongo.InventorySnapShot;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.redis.InventorySnapShotVO;

import java.util.List;

public interface SnapShotService {

	public InventorySnapShot createDailySnapShot(int unitIdFs);

	public InventorySnapShot getCurrentSnapShot(int unitId);

	public void updateSnapShot(QuantityResponseData data);

	public InventorySnapShotVO getCurrentSnapShotVO(int unitId);

	List<InventorySnapShot> getCurrentSnapShotAllCafe();
}
