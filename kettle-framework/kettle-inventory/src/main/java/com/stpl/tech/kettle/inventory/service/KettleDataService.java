package com.stpl.tech.kettle.inventory.service;

import java.io.IOException;
import java.util.List;

import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.common.UnitClosure;

public interface KettleDataService {

	public UnitClosure getLastDayClose(int unitId);

	public void syncMissingOrders(int unitId, int closureId, List<Integer> orderIds);

    public List<ProductInventory> getUnitProductInventory(int unitId) throws IOException;
}
