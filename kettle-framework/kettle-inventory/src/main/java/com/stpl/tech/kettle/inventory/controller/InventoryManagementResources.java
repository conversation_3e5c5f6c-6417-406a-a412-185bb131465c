/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.inventory.controller;

import static com.stpl.tech.kettle.inventory.util.InventoryServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.inventory.util.InventoryServiceConstants.INVENTORY_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.inventory.util.InventoryServiceConstants.SEPARATOR;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.domain.mongo.InventoryDataVO;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.service.EmployeeManagementService;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.kettle.inventory.service.RecipeLocalCacheManagementService;
import com.stpl.tech.kettle.inventory.service.RecipeManagementService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.slack.Slack;
import com.stpl.tech.master.core.external.notification.SlackNotification;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR
		+ INVENTORY_MANAGEMENT_ROOT_CONTEXT, produces = MediaType.APPLICATION_JSON)
public class InventoryManagementResources extends AbstractInventoryResource {

	/**
	 * static sl4j Logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(InventoryManagementResources.class);

	@Autowired
	private InventoryManagementService inventoryService;

	@Autowired
	private RecipeManagementService recipeService;

	@Autowired
	private RecipeLocalCacheManagementService recipeLocalService;

	@Autowired
	private EmployeeManagementService employeeService;

	@Autowired
	private InventoryProperties props;

	/**
	 * Hard Refresh all units.
	 * <p>
	 * Data will be filled unit by unit when inventory is requested
	 * 
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.GET, value = "refresh-all")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void refreshCache() throws DataNotFoundException {
		LOG.info("Request to refresh Cache for all Units");
		inventoryService.refreshAllUnits(false);
	}

	/**
	 * Hard Refresh all units.
	 * <p>
	 * Data will be filled unit by unit when inventory is requested
	 * 
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.GET, value = "refresh-recipes")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void reloadAllRecipesCache() throws DataNotFoundException {
		LOG.info("Request to refresh missing recipes");
		recipeService.refreshAllRecipes();
		recipeLocalService.clearMap();
	}

	/**
	 * Hard Refresh all units.
	 * <p>
	 * Data will be filled unit by unit when inventory is requested
	 * 
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.GET, value = "refresh-all-recipe")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void refreshAllRecipe() throws DataNotFoundException {
		LOG.info("Request to refresh all recipes ");
		recipeService.reloadRecipes();
		recipeLocalService.clearMap();
	}

	@RequestMapping(method = RequestMethod.GET, value = "refresh-employee-cache")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void refreshEmployeeCache() throws DataNotFoundException {
		LOG.info("Request to refresh employee Cache for all employees");
		employeeService.refershAllEmployee();
	}

	@RequestMapping(method = RequestMethod.GET, value = "list-employee-cache")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Iterable<EmployeeBasicDetail> listEmployeeCache() throws DataNotFoundException {
		LOG.info("Request to fetch all employee Cache for all employees");
		return employeeService.listAllEmployee();
	}

	/**
	 * Refresh Cafe Inventory for a unit.
	 * <p>
	 * Data is recalculated for the requested unit.
	 * 
	 * @throws DataNotFoundException
	 */
	@Deprecated
	@RequestMapping(method = RequestMethod.GET, value = "refresh-unit")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void refreshCacheForUnit(@RequestParam("unitId") int unitId, @RequestParam("force") Boolean force)
			throws DataUpdationException {
		LOG.info("Request to refresh Cache for Unit: {}", unitId);
		throw new DataUpdationException("This API is now deprecated");
	}

	/**
	 * Refresh SCM Inventory for a unit.
	 * <p>
	 * Data is recalculated for the requested unit.
	 * 
	 */
	@RequestMapping(method = RequestMethod.GET, value = "refresh-scm-unit")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void refreshSCMCacheForUnit(@RequestParam("unitId") int unitId, @RequestParam("force") Boolean force) {
		LOG.info("Request to refresh Cache for Unit: {}", unitId);
		inventoryService.refreshInventoryForUnit(unitId, force == null ? false : force);
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "delete-mapping")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void deleteMappingForUnit(@RequestParam("unitId") int unitId) {
		LOG.info("Request to refresh Cache for Unit: {}", unitId);
		inventoryService.deleteMappingForUnit(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "scm/update")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateSCMProductForUnit(@RequestBody InventoryDataVO data) {
		LOG.info("Request to update SCM Product Data for Unit: {}", JSONSerializer.toJSON(data));
		inventoryService.updateSCMProductInventory(data);
//		SlackNotificationService.getInstance().send(props.getEnvType(), null, SlackNotification.INVENTORY_UPDATE, data.toString());
		SlackNotificationService.getInstance().sendNotification(props.getEnvType(), null, SlackNotification.INVENTORY_UPDATE, data.toString());
		return true;
	}

	@RequestMapping(method = RequestMethod.GET, value = "clear-recipe", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean getCafeProductRecipe(@RequestParam("unitId") int unitId,
										@RequestParam("productId") int productId,
										@RequestParam("dimension") String dimension) {
		LOG.info("Request to clear Cafe product recipe for unitId: {} and productId {} and dimension {}", unitId,
				productId, dimension);
		inventoryService.clearCafeProductRecipe(unitId, productId, dimension);
		return true;
	}

}
