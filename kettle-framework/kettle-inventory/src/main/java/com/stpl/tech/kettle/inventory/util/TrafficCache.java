package com.stpl.tech.kettle.inventory.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.domain.common.RequestClient;
import com.stpl.tech.util.notification.slack.Slack;

@Component
public class TrafficCache {

	@Autowired
	InventoryProperties env;

	private final long longDelay = 50000;
	private final long shortDelay = 30000;
	// for 30 second delays
	private final Map<String, RequestClient> longDelayPool = new HashMap<>();
	// for 10 second delays
	private final Map<String, RequestClient> shortDelayPool = new HashMap<>();

	public boolean allowRequest(String key) {
		boolean b = false;
		RequestClient c = shortDelayPool.get(key);
		if (c == null) {
			/*
			 * c = longDelayPool.get(key); if (c != null && c.getRequestCount() > 0) { b =
			 * false; } else {
			 */
			updateShortDelay(key);
			b = true;
			// }
		}
		// updateLongDelay(key);
		return b;
	}

	public int timeLeftInSeconds(String key) {
		RequestClient c = longDelayPool.get(key);
		if (c == null) {
			c = shortDelayPool.get(key);
		}
		if (c != null) {
			return new Long((c.getEndTime() - System.currentTimeMillis()) / 1000).intValue();
		}
		return 0;
	}

	private void updateShortDelay(String key) {
		addToPool(key, shortDelayPool, shortDelay);
	}

	private void updateLongDelay(String key) {
		addToPool(key, longDelayPool, longDelay);
	}

	private void addToPool(String key, Map<String, RequestClient> pool, long delay) {
		RequestClient c = pool.get(key);
		if (c != null) {
			c.setRequestCount(c.getRequestCount() + 1);
			if (c.getRequestCount() > 10) {
				slackIt(c);
			}
		} else {
			pool.put(key, new RequestClient(key, 1, delay));
		}
	}

	private void slackIt(RequestClient c) {
		String text = "Too many request from client: " + c.getKey();
//		SlackNotificationService.getInstance().send(env.getEnvType(), null, text);
		SlackNotificationService.getInstance().sendNotification(env.getEnvType(),null, SlackNotification.SYSTEM_ERRORS,text);
	}

	@Scheduled(fixedRate = 10000)
	public void removeRequestClients() {
		//removeFromPool(longDelayPool);
		removeFromPool(shortDelayPool);
	}

	private void removeFromPool(Map<String, RequestClient> pool) {
		List<String> l = getExpired(pool);
		l.forEach(p -> pool.remove(p));
	}

	private List<String> getExpired(Map<String, RequestClient> pool) {
		List<String> l = new ArrayList<>();
		long time = System.currentTimeMillis();
		for (RequestClient c : pool.values()) {
			if (c.isExpired(time)) {
				l.add(c.getKey());
			}
		}
		return l;
	}
}
