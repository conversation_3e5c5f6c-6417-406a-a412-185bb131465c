package com.stpl.tech.kettle.inventory.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.inventory.domain.common.InventoryDetailData;
import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.domain.redis.UnitSCMCriticalProductMap;
import com.stpl.tech.kettle.inventory.vo.RecipeInventory;
import com.stpl.tech.master.core.exception.DataUpdationException;

public interface InventoryDataService {

	public List<InventoryData> getSCMProductInventoryData(int unitId);

    Map<Integer, BigDecimal> getSCMProductInventoryDataTrimmed(int unitId);

    public List<InventoryData> getCafeProductInventoryData(int unitId);

	public List<RecipeInventory> getCafeProductRecipeInventoryData(int unitId, int productId, String dimension, boolean criticalOnly);

	public List<QuantityResponseData> getUnitTimeline(int unitId);

    public List<ProductInventory> getKettleInventory(int unitId);

	public UnitSCMCriticalProductMap getCriticalSCMProducts(int unitId);

    List<InventoryDetailData> getCafeProductInventoryDetailData(int unitId);
}
