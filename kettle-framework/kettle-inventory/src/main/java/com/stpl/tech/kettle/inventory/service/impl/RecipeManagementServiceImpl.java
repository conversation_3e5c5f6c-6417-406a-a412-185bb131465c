package com.stpl.tech.kettle.inventory.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.inventory.dao.redis.RecipeMapDataDao;
import com.stpl.tech.kettle.inventory.domain.redis.RecipeMapData;
import com.stpl.tech.kettle.inventory.service.MasterDataService;
import com.stpl.tech.kettle.inventory.service.RecipeManagementService;
import com.stpl.tech.master.recipe.model.RecipeDetail;

@Service
public class RecipeManagementServiceImpl implements RecipeManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(RecipeManagementServiceImpl.class);
	private static final long THRESHOLD = 10;
	private static final String BULK_RECIPE_LOAD = "BULK_RECIPE_LOAD";
	@Autowired
	private MasterDataService masterDataService;

	@Autowired
	private RecipeMapDataDao recipeMapDao;

	@Override
	public RecipeDetail getRecipe(Integer recipeId) {
		Optional<RecipeMapData> recipe = null;
		if (recipeMapDao.existsById(recipeId)) {
			recipe = recipeMapDao.findById(recipeId);
		} else {
			if (recipeMapDao.count() <= THRESHOLD) {
				synchronized (BULK_RECIPE_LOAD) {
					long totalStartTime = System.currentTimeMillis();
					if (recipeMapDao.count() <= THRESHOLD) {
						// This is done when the getRecipe call is made after flush DB
						LOG.info(
								"######### BULK CALL(1) : Called refresh All Recipe from get Recipe By Id as this a flush DB use case #########");
						refreshAllRecipes();
					} else {
						LOG.info(
								"######### SKIPPED CALL(FEW) : Parallel Calls of refresh All Recipe from get Recipe By Id as this a flush DB use case #########");
					}
					recipe = recipeMapDao.findById(recipeId);
					if (recipe.isEmpty()) {
						LOG.info("######### DANGER CALL(NEVER) : $$$$$ - This should never be called #########");
						recipe = Optional.of(getRecipeMapDatafromDB(recipeId));
					}
					LOG.info(
							"######### Refresh Inventory in case of Flush DB when called for recipeId: {} took {} milliseconds #########",
							recipeId, System.currentTimeMillis() - totalStartTime);
				}
			} else {
				recipe = Optional.of(getRecipeMapDatafromDB(recipeId));
			}
		}
		return recipe.get().getRecipe();
	}

	private RecipeMapData getRecipeMapDatafromDB(Integer recipeId) {
		LOG.info("######### GENUINE CALL(RARE) : This should be called with a less frequency #########");
		RecipeMapData recipe = new RecipeMapData();
		RecipeDetail recipeData = masterDataService.getRecipeDetail(recipeId + "");
		recipe = new RecipeMapData();
		recipe.setKey(recipeId);
		recipe.setRecipe(recipeData);
		recipe = recipeMapDao.save(recipe);
		return recipe;
	}

	@Override
	public void refreshAllRecipes() {
		List<Integer> recipeIds = new ArrayList<>();
		Iterable<RecipeMapData> recipes = recipeMapDao.findAll();
		if (recipes != null) {
			for (RecipeMapData recipe : recipes) {
				recipeIds.add(recipe.getKey());
			}
		}
		List<RecipeDetail> newRecipes = masterDataService.getAllRecipeDetail(recipeIds);
		LOG.info("Found {} new recipes to be loaded into the cache", newRecipes == null ? 0 : newRecipes.size());
		if (newRecipes != null) {
			List<RecipeMapData> recipeMaps = new ArrayList<>();
			for (RecipeDetail r : newRecipes) {
				RecipeMapData data = new RecipeMapData(r.getRecipeId(), r);
				recipeMaps.add(data);
			}
			recipeMapDao.saveAll(recipeMaps);
		}
	}

	@Override
	public void reloadRecipes() {
		List<Integer> recipeIds = new ArrayList<>();
		recipeIds.add(0);
		List<RecipeDetail> newRecipes = masterDataService.getAllRecipeDetail(recipeIds);
		LOG.info("Found {} new recipes to be loaded into the cache", newRecipes == null ? 0 : newRecipes.size());
		if (newRecipes != null) {
			List<RecipeMapData> recipeMaps = new ArrayList<>();
			for (RecipeDetail r : newRecipes) {
				RecipeMapData data = new RecipeMapData(r.getRecipeId(), r);
				recipeMaps.add(data);
			}
			recipeMapDao.saveAll(recipeMaps);
		}
	}

}
