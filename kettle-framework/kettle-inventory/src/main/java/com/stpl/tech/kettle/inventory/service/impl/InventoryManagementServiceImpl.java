package com.stpl.tech.kettle.inventory.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.jms.JMSException;

import com.stpl.tech.kettle.inventory.domain.common.StockOutEventObject;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.dao.mongo.InventoryDataDao;
import com.stpl.tech.kettle.inventory.dao.mongo.QuantityResponseDataDao;
import com.stpl.tech.kettle.inventory.dao.mongo.UnitProductStockEventDataDao;
import com.stpl.tech.kettle.inventory.dao.mysql.UnitProductStockEventDataDetailDao;
import com.stpl.tech.kettle.inventory.dao.redis.CriticalProductMapDao;
import com.stpl.tech.kettle.inventory.dao.redis.ProductTrimmedDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitKeyDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitProductInventoryDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitSCMCriticalProductDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitTrimmedProductMapDataDao;
import com.stpl.tech.kettle.inventory.data.UnitProductStockEventDataDetail;
import com.stpl.tech.kettle.inventory.domain.common.InventoryDataVo;
import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.common.UnitClosure;
import com.stpl.tech.kettle.inventory.domain.common.UnitData;
import com.stpl.tech.kettle.inventory.domain.common.UnitProductInventoryRequest;
import com.stpl.tech.kettle.inventory.domain.mongo.CostDetail;
import com.stpl.tech.kettle.inventory.domain.mongo.InventoryDataVO;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.mongo.UnitProductsStockEventData;
import com.stpl.tech.kettle.inventory.domain.redis.CriticalProductMap;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.domain.redis.KeyRepository;
import com.stpl.tech.kettle.inventory.domain.redis.UnitProductTrimmedData;
import com.stpl.tech.kettle.inventory.domain.redis.UnitTrimmedProductMapData;
import com.stpl.tech.kettle.inventory.service.InventoryAggregationServices;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.kettle.inventory.service.KettleDataService;
import com.stpl.tech.kettle.inventory.service.RecipeLocalCacheManagementService;
import com.stpl.tech.kettle.inventory.service.RecipeManagementService;
import com.stpl.tech.kettle.inventory.service.SnapShotService;
import com.stpl.tech.kettle.inventory.util.InventoryUtils;
import com.stpl.tech.kettle.inventory.util.TrafficCache;
import com.stpl.tech.kettle.inventory.vo.RecipeInventory;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.inventory.service.StockEventService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.TrimmedProductPrice;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.redis.core.dao.UnitBasicDetailDao;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.notification.slack.Slack;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.jms.JMSException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class InventoryManagementServiceImpl implements InventoryManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(InventoryManagementServiceImpl.class);

	private static HashSet<Integer> unitsToUpdate = new HashSet<>();

	@Autowired
	private QuantityResponseDataDao quantityResponseDataDao;
	@Autowired
	private UnitProductInventoryDataDao inventoryDataDao;
	@Autowired
	private UnitBasicDetailDao unitBasicDetailDao;
	@Autowired
	private ProductTrimmedDataDao prouductTrimmedDataDao;
	@Autowired
	private UnitKeyDataDao unitKeyDataDao;
	@Autowired
	private InventoryAggregationServices aggregator;
	@Autowired
	private CriticalProductMapDao criticalProductMapDao;
	@Autowired
	private UnitTrimmedProductMapDataDao unitProductMapDataDao;
	@Autowired
	private InventoryProperties props;
	@Autowired
	private StockEventService stockEventService;
	@Autowired
	private SnapShotService snapShotService;
	@Autowired
	private TrafficCache trafficCache;

	@Autowired
	private KeyMutexFactory factory;

	@Autowired
	private RecipeManagementService recipeService;

	@Autowired
	private RecipeLocalCacheManagementService recipeLocalService;

	@Autowired
	private UnitProductStockEventDataDao unitProductStockEventDataDao;

	@Autowired
	private InventoryDataDao inventoryUpdateDao;

	@Autowired
	private UnitTrimmedProductMapDataDao unitTrimmedProductMapDataDao;

	@Autowired
	private UnitProductStockEventDataDetailDao unitProductStockEventDataDetailDao;
	@Autowired
	private UnitSCMCriticalProductDao unitSCMCriticalProductDao;

	@Autowired
	private KettleDataService kettleDataService;

	@Autowired
	private Environment environment;
	@Override
	@Transactional(rollbackFor = Exception.class, value = "InventoryDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateInventory(QuantityResponseData data, boolean persist) {
		try {
			if (persist) {
				data.setId(data.createId());
				quantityResponseDataDao.save(data);
			}
			processInventory(data);
			processSnapShot(data);
		} catch (Exception e) {
			LOG.error("Error processing inventory event:::", e);
		}
	}

	private void processSnapShot(QuantityResponseData data) {
		if (!InventoryAction.ADD.equals(data.getAction())) {
			return;
		}
		snapShotService.updateSnapShot(data);
	}

	private void processInventory(QuantityResponseData data) throws DataUpdationException {
		// target unit
		int unitId = data.getUnitId();
		synchronized (factory.getMutex(unitId + "")) {

			Date systemExpireTime = InventoryUtils.getNextExpireTimeLimit();
			// Flush Action to reset Inventory for Unit
			boolean flush = checkFlush(data);
			if (flush) {
				return;
			}
			// in case inventory is not uploaded refresh inventory
			checkUnitIsCached(unitId);

			for (ProductQuantityData q : data.getDetails()) {
				Optional<InventoryData> i = inventoryDataDao
						.findById(InventoryUtils.unitSCMProductKey(unitId, q.getId(), q.getU()));
				if (i.isPresent()) {
					InventoryUtils.updateInventoryData(i.get(), q.getQ(), systemExpireTime, q.getE(), q.getP(),
							data.getAction());
					inventoryDataDao.save(i.get());
				} else {
					// add it to the inventory Bro!
					addScmProductToCache(q, unitId, systemExpireTime);
				}
			}

			long outerstartTime = System.currentTimeMillis();

			Set<Integer> targetProducts = filterTargetProducts(data);
			for (Integer productId : targetProducts) {
				if (productId == null) {
					continue;
				}
				Optional<UnitTrimmedProductMapData> upmd = unitProductMapDataDao
						.findById(InventoryUtils.getUnitProductKey(unitId, productId));
				if(upmd.isPresent()){
					TrimmedProductVO product = upmd.get().getProduct();
					if (ProductStatus.ACTIVE.equals(product.getStatus())) {
						updateCafeProductInventoryOptimized(data.getUnitId(), product, data.getSource());
					}
				}
			}

			LOG.info("Product Refresh Completed in {} milliseconds", System.currentTimeMillis() - outerstartTime);
		}
	}

	private boolean checkFlush(QuantityResponseData data) throws DataUpdationException {
		if (InventoryAction.FLUSH.equals(data.getAction())) {
			refreshInventoryForUnit(data.getUnitId(), true);
			return true;
		}
		if (InventoryAction.FLUSH_CAFE.equals(data.getAction())) {
			aggregator.purgeData();
			refreshAllUnits(true);
			return true;
		}
		return false;
	}

	private boolean checkUnitIsCached(int unitId) throws DataUpdationException {
		String key = InventoryUtils.unitCafeProductRepoKey(unitId);
		Optional<KeyRepository> keyRepo = unitKeyDataDao.findById(key);
		if (keyRepo.isEmpty() || keyRepo.get().getKeyList() == null || keyRepo.get().getKeyList().isEmpty()) {
			synchronized (factory.getMutex(unitId + "_CAFE")) {
				keyRepo = unitKeyDataDao.findById(key);
				if (keyRepo.isEmpty() || keyRepo.get().getKeyList() == null || keyRepo.get().getKeyList().isEmpty()) {
					LOG.info("Cafe inventory unavailable for unit: {}", unitId);
					refreshInventoryForUnit(unitId, false);
					keyRepo = unitKeyDataDao.findById(key);
					if (keyRepo == null) {
						return false;
					}
				}
			}
		}
		return true;

	}

	private void addScmProductToCache(ProductQuantityData data, int unitId, Date systemExpireTime) {
		String key = InventoryUtils.unitSCMProductKey(unitId, PriceUpdateEntryType.PRODUCT, data.getId(), data.getU());
		InventoryData d = new InventoryData(key, data.getId(), "", data.getQ(), data.getU(), BigDecimal.ZERO);
		d.setPrice(data.getP());
		if (InventoryUtils.isShortExpire(systemExpireTime, data.getE())) {
			d.setExQty(data.getQ());
		}
		inventoryDataDao.save(d);
		String unitKey = InventoryUtils.unitSCMProductRepoKey(unitId);
		Optional<KeyRepository> repo = unitKeyDataDao.findById(unitKey);
		if (repo.isPresent()) {
			repo.get().getKeyList().add(key);
			unitKeyDataDao.save(repo.get());
		}
		LOG.info("New Data added to SCM redis Cache for unit : {}", unitId);
	}

	private Set<Integer> filterTargetProducts(QuantityResponseData data) {
		Set<Integer> productSet = new HashSet<>();
		data.getDetails().forEach(p -> {
			// product affected list is collected via Redis cache based on recipe
			Optional<CriticalProductMap> map = criticalProductMapDao
					.findById(InventoryUtils.getCriticalProductKey(p.getId(), data.getUnitId()));
			if (map.isPresent()) {
				productSet.addAll(map.get().getProductsAffected());
			}
		});
		return productSet;
	}

	/**
	 * Refresh All Active Units Data
	 * <p>
	 * This might take some time and will be heavy on processing.
	 */
	@Override
	public void refreshAllUnits(boolean cafeOnly) {
		long startTime = System.currentTimeMillis();
		LOG.info("Refresh started for all units");

		/**
		 * Purge is sent as true to delete exiting unit detail data so that no unit is
		 * missed in case a new unit is activated.
		 */
		aggregator.purgeData();
		List<UnitBasicDetail> unitList = aggregator.getAllUnitBasicDetails(true);
		recipeService.refreshAllRecipes();
		recipeLocalService.clearMap();
		unitKeyDataDao.deleteAll();
		inventoryDataDao.deleteAll();
		/*
		 * for (UnitBasicDetail ubd : unitList) { if (AppUtils.isActive(ubd.getStatus())
		 * && UnitCategory.CAFE.name().equals(ubd.getCategory())) {
		 * refreshCafeProductsInventory(ubd.getId()); } }
		 */

		LOG.info("Refresh Completed in {} milliseconds", System.currentTimeMillis() - startTime);
	}

	/**
	 * Pulls cost detail data from SUMO for a particular and update to Redis cache
	 * <p>
	 * This is Hard Refresh API, It collects data from the source.
	 */

	private boolean refreshSCMProductInventory(int unitId, boolean force) {

		/**
		 * delayed queue
		 */
		if (!force && !trafficCache.allowRequest(String.valueOf(unitId) + "SCM")) {
			LOG.info("Multiple SCM Refresh calls for Unit {}", unitId);
			return false;
		}
		UnitBasicDetail unit = aggregator.getUnitBasicDetail(unitId);
		if (UnitCategory.CAFE.name().equals(unit.getCategory()) && AppConstants.ACTIVE.equals(unit.getStatus())
				&& unit.isLive()) {
			Date expireTime = InventoryUtils.getNextExpireTimeLimit();
			long totalStartTime = System.currentTimeMillis();
			long startTime = System.currentTimeMillis();

			LOG.info("Updating SCM inventory for unit: {}", unitId);
			Map<String, InventoryData> map = null;

			synchronized (factory.getMutex(unitId + "")) {

				List<CostDetail> list = aggregator.fetchCostDetailData(unitId);
				if (list == null || list.isEmpty()) {
					LOG.info("No SCM Inventory Data for unit : {}", unitId);
					return false;
				}

				LOG.info("Saved Cost Detail Data for unit : {} in {} milliseconds", unitId,
						System.currentTimeMillis() - startTime);
				startTime = System.currentTimeMillis();

				// remove data from REDIS cache
				removeSCMData(unitId);

				// add to REDIS cache product id and quantity and unit
				map = new HashMap<String, InventoryData>();
				for (CostDetail c : list) {
					InventoryUtils.addToMap(map, c, expireTime);
				}

				inventoryDataDao.saveAll(map.values());
				list = null;

				LOG.info("Saved SCM Inventory for unit : {} in {} milliseconds", unitId,
						System.currentTimeMillis() - startTime);
				startTime = System.currentTimeMillis();

				String key = InventoryUtils.unitSCMProductRepoKey(unitId);
				unitKeyDataDao.deleteById(key);
				unitKeyDataDao.save(new KeyRepository(key, new ArrayList<String>(map.keySet())));

				// collect last day close in sumo
				// update amendments
				updateSCMAmendments(unitId);

				LOG.info("Saved SCM Inventory keys for unit : {} in {} milliseconds", unitId,
						System.currentTimeMillis() - startTime);

				LOG.info("SCM inventory Cache updated for unit: {} in {} milliseconds", unitId,
						System.currentTimeMillis() - totalStartTime);
				return true;
			}
		} else {
			return false;
		}
	}

	private void updateSCMAmendments(int unitId) {
		// TODO Auto-generated method stub

	}

	private void removeSCMData(int unitId) {
		String scmKey = InventoryUtils.unitSCMProductRepoKey(unitId);
		Optional<KeyRepository> keyRepo = unitKeyDataDao.findById(scmKey);
		if (keyRepo.isPresent() && keyRepo.get().getKeyList() != null && !keyRepo.get().getKeyList().isEmpty()) {
			inventoryDataDao.deleteAll(inventoryDataDao.findAllById(keyRepo.get().getKeyList()));
			unitKeyDataDao.delete(keyRepo.get());
		}
	}

	/**
	 * refresh cafe inventory.
	 *
	 * <li>1. Collect SCM Inventory, if inventory is not available fetch inventory
	 * <li>2. Process Inventory product by product using recipe
	 * <li>3. Process Kettle Orders and consume inventory
	 */
	private boolean refreshCafeProductsInventoryOptimized(int unitId, boolean force) {
		/**
		 * delayed queue
		 */
		Stopwatch watch = Stopwatch.createUnstarted();
		if (!force && !trafficCache.allowRequest(String.valueOf(unitId) + AppConstants.KETTLE)) {
			LOG.info("Multiple Optimized Refresh calls for Unit {}", unitId);
			return false;
		}

		watch.start();
		UnitBasicDetail unit = aggregator.getUnitBasicDetail(unitId, force);
		LOG.info("Inside refreshCafeProductsInventoryOptimized : aggregator.getUnitBasicDetail unitId: {} took {} ms",
				unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		if (UnitCategory.CAFE.name().equals(unit.getCategory()) && AppConstants.ACTIVE.equals(unit.getStatus())
				&& unit.isLive()) {

			unitsToUpdate.add(unitId);

			List<String> unitProductKeys = new ArrayList<String>();
			// in case inventory is not uploaded
			watch.reset();
			watch.start();
			if (!unitKeyDataDao.existsById(InventoryUtils.unitSCMProductRepoKey(unitId))) {
				if (!refreshSCMProductInventory(unitId, force)) {
					return false;
				}
			}
			LOG.info("Inside refreshCafeProductsInventoryOptimized : refreshSCMProductInventory unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));

			long startTime = System.currentTimeMillis();
			LOG.info("Updating Optimized CAFE inventory for unitId: {}", unitId);

			watch.reset();
			watch.start();
			// clear cafe data first
			clearCafeProductsData(unitId);
			LOG.info("Inside refreshCafeProductsInventoryOptimized : clearCafeProductsData unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));

			watch.reset();
			watch.start();
			UnitProductTrimmedData products = aggregator.getAllCafeTrimmedProducts(unitId);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : aggregator.getAllCafeTrimmedProducts unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			// LOG.info("Got {} products for unit : {}", products.getProducts().size(),
			// unitId);
			watch.reset();
			watch.start();
			for (TrimmedProductVO product : products.getProducts()) {
				// LOG.info("Finding cafe inventory for {} ", product);
				if (ProductStatus.ACTIVE.equals(product.getStatus()) && product.getInventoryTracked()) {
					unitProductKeys.addAll(updateCafeProductInventoryOptimized(unitId, product,InventorySource.CACHE_REFRESH));
				}
			}
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : unitProductKeys.addAll(updateCafeProductInventoryOptimized unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));

			watch.reset();
			watch.start();
			String key = InventoryUtils.unitCafeProductRepoKey(unitId);
			// LOG.info("Got {} product keys for unit : {}", unitProductKeys.size(),
			// unitId);
			unitKeyDataDao.deleteById(key);
			unitKeyDataDao.save(new KeyRepository(key, unitProductKeys));
			LOG.info("Inside refreshCafeProductsInventoryOptimized : unitKeyDataDao.save unitId: {} took {} ms", unitId,
					watch.stop().elapsed(TimeUnit.MILLISECONDS));

			/**
			 * This will process orders this could take time and connects to kettle
			 */
			try {
				watch.reset();
				watch.start();
				processOrders(unitId);
				LOG.info(
						"Inside refreshCafeProductsInventoryOptimized : orderManager.processOrders unitId: {} took {} ms",
						unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			} catch (Exception e) {
				LOG.error("Error while processing orders for unitId {}", unitId, e);
			}
			LOG.info("Cafe inventory Cache updated for unit: {} in {} milliseconds", unitId,
					System.currentTimeMillis() - startTime);

			unitsToUpdate.remove(unitId);
		}
		return true;
	}

	private void clearCafeProductsData(int unitId) {
		LOG.info("Trying to remove Cafe inventory for unit: {}", unitId);
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		String cafeKey = InventoryUtils.unitCafeProductRepoKey(unitId);
		Optional<KeyRepository> keyRepo = unitKeyDataDao.findById(cafeKey);
		LOG.info(
				"Inside refreshCafeProductsInventoryOptimized : clearCafeProductsData - unitKeyDataDao.findOne unitId: {} took {} ms",
				unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		if (keyRepo.isPresent() && keyRepo.get().getKeyList() != null && !keyRepo.get().getKeyList().isEmpty()) {
			// LOG.info("Removing Cafe inventory for unit: {} with key list size {}",
			// unitId, keyRepo.getKeyList().size());
			watch.reset();
			watch.start();
			inventoryDataDao.deleteAll(inventoryDataDao.findAllById(keyRepo.get().getKeyList()));
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : clearCafeProductsData - inventoryDataDao.delete(inventoryDataDao.findAll unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			watch.reset();
			watch.start();
			unitKeyDataDao.delete(keyRepo.get());
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : clearCafeProductsData - unitKeyDataDao.delete unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			watch.reset();
			watch.start();
			aggregator.refreshCafeProducts(unitId);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : clearCafeProductsData - aggregator.refreshCafeProducts unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		} else {
			LOG.info("Did not find cafe inventory for unit: {}", unitId);
		}
	}

	public List<String> updateCafeProductInventoryOptimized(int unitId, TrimmedProductVO product, InventorySource source) {
		// LOG.info("Calculating updateCafeProductInventoryOptimized for unitId {} and
		// product {} ", unitId, product.getId());
		List<String> cafeProductKeys = new ArrayList<String>();
		try {
			Map<String, BigDecimal> criticalProductKeys;
			Map<String, BigDecimal> productKeys;
			Map<InventoryData, BigDecimal> inventoryStockMap = new HashMap<>();
			RecipeDetail rd;
			for (TrimmedProductPrice pp : product.getPrices().values()) {
				criticalProductKeys = new HashMap<>();
				productKeys = new HashMap<>();
				rd = recipeLocalService.getRecipe(pp.getRecipeId());
				InventoryUtils.calculateProductInventory(unitId, rd, criticalProductKeys, productKeys);
				if (!criticalProductKeys.isEmpty()) {
					cafeProductKeys.addAll(calculateCafeInventoryOptimized(criticalProductKeys, unitId, product, pp,
							inventoryStockMap));
				} else {
					cafeProductKeys.addAll(
							calculateCafeInventoryOptimized(productKeys, unitId, product, pp, inventoryStockMap));
				}
			}
			createStockNotification(inventoryStockMap, unitId,source,product.getBrandId());

		} catch (Exception e) {
			LOG.error("Error in calculating product keys for unit {} and product {} :::: ", unitId, product,e);
		}
		return cafeProductKeys;
	}

	private List<String> calculateCafeInventoryOptimized(Map<String, BigDecimal> productKeys, int unitId,
			TrimmedProductVO product, TrimmedProductPrice pp, Map<InventoryData, BigDecimal> inventoryStockMap) {
		List<String> cafeProductKeys = new ArrayList<>();
		Optional<InventoryData> inventory;
		BigDecimal minCount = null;
		BigDecimal count = BigDecimal.ZERO;
		BigDecimal quantity = BigDecimal.ZERO;
		BigDecimal maxExpiry = null;
		BigDecimal expiryCount = BigDecimal.ZERO;
		BigDecimal expiryQuantity = BigDecimal.ZERO;
		String cafeProductDimensionKey = null;
		for (String key : productKeys.keySet()) {
			inventory = inventoryDataDao.findById(key);

			if (inventory.isEmpty()) {
				LOG.info("Inventory not available for product key {} at unit {}", key, unitId);
				quantity = BigDecimal.ZERO;
				expiryQuantity = BigDecimal.ZERO;
			} else {
				quantity = inventory.get().getQty();
				expiryQuantity = inventory.get().getExQty();
			}
			// skip count if recipe quantity is ZERO
			if (BigDecimal.ZERO.compareTo(productKeys.get(key)) != 0) {
				count = AppUtils.divideWithScale10(quantity, productKeys.get(key));
				expiryCount = AppUtils.divideWithScale10(expiryQuantity, productKeys.get(key));
			} else {
				count = minCount;
				expiryCount = maxExpiry;
			}

			if (minCount != null) {
				minCount = getMinimumValue(minCount, count);
			} else {
				minCount = count;
			}
			if (maxExpiry != null) {
				maxExpiry = getMaximumValue(maxExpiry, expiryCount);
			} else {
				maxExpiry = expiryCount;
			}
		}

		// handle for garden fresh
		if (product.getId() == 868) {
			minCount = BigDecimal.ZERO;
			for (String key : productKeys.keySet()) {
				inventory = inventoryDataDao.findById(key);

				if (inventory.isEmpty()) {
					LOG.info("Inventory not available for product key {} at unit {}", key, unitId);
					quantity = BigDecimal.ZERO;
					expiryQuantity = BigDecimal.ZERO;
				} else {
					quantity = inventory.get().getQty();
					expiryQuantity = inventory.get().getExQty();
				}

				// skip count if recipe quantity is ZERO
				if (BigDecimal.ZERO.compareTo(productKeys.get(key)) != 0) {
					count = AppUtils.divideWithScale10(quantity, productKeys.get(key));
					expiryCount = AppUtils.divideWithScale10(expiryQuantity, productKeys.get(key));
				} else {
					count = minCount;
					expiryCount = maxExpiry;
				}

				if (count != null && count.intValue() < 0) {
					count = BigDecimal.ZERO;
				}

				if (expiryCount != null && expiryCount.intValue() < 0) {
					expiryCount = BigDecimal.ZERO;
				}

				if (minCount != null) {
					minCount = AppUtils.add(minCount, count);
				} else {
					minCount = count;
				}

				if (maxExpiry != null) {
					maxExpiry = AppUtils.add(maxExpiry, expiryCount);
				} else {
					maxExpiry = expiryCount;
				}
			}

		}

		cafeProductDimensionKey = InventoryUtils.unitCafeProductKey(unitId, product.getId(), pp.getDimension());
		Optional<InventoryData> d = inventoryDataDao.findById(cafeProductDimensionKey);

		// this will update stock out and stock in notifications for channel partners
		// this will set stock events related data to the map

		if (d.isPresent() && minCount != null && d.get().getQty() != null) {
			inventoryStockMap.put(d.get(), minCount.compareTo(BigDecimal.ZERO) > 0 ? minCount.setScale(0, RoundingMode.FLOOR)
					: BigDecimal.ZERO);
			// createStockNotification(d, minCount, unitId);
		}
		minCount = minCount != null && minCount.compareTo(BigDecimal.ZERO) > 0
				? minCount.setScale(0, RoundingMode.FLOOR)
				: BigDecimal.ZERO;
		BigDecimal finalInventoryValue = getMinimumValue(minCount, maxExpiry);
		finalInventoryValue = finalInventoryValue != null && finalInventoryValue.compareTo(BigDecimal.ZERO) > 0
				? finalInventoryValue
				: BigDecimal.ZERO;
		inventoryDataDao.save(new InventoryData(cafeProductDimensionKey, product.getId(), product.getName(), minCount,
				pp.getDimension(), finalInventoryValue));
		cafeProductKeys.add(cafeProductDimensionKey);
		// LOG.info("Adding cafeProductKeys fro key {}", cafeProductDimensionKey);
		return cafeProductKeys;
	}

	/**
	 * Creates Stock-In and Stock-Out Notifications for channel partners
	 *
	 * @param d
	 * @param finalQty
	 * @param unitId
	 */
//	@Deprecated
//	private void createStockNotification(InventoryData d, BigDecimal finalQty, int unitId) {
//		UnitBasicDetail ubd = aggregator.getUnitBasicDetail(unitId);
//		UnitTrimmedProductMapData upmd = unitProductMapDataDao
//				.findOne(InventoryUtils.getUnitProductKey(unitId, d.getId()));
//		if (ubd == null || !ubd.isLiveInventoryEnabled()) {
//			return;
//		}
//		if (upmd == null || upmd.getProduct() == null || !upmd.getProduct().getInventoryTracked()) {
//			return;
//		}
//
//		List<UnitProductsStockEventData> stockEvents = new ArrayList<>();
//		UnitProductsStockEvent unitProductsStockOutEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_OUT);
//		UnitProductsStockEvent unitProductsStockInEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_IN);
//
//		if (d.getQty().intValue() > 0 && finalQty.intValue() <= 0) {
//			unitProductsStockOutEvent.getProductIds().add(Integer.valueOf(d.getId()).toString());
//			unitProductsStockOutEvent.getProductDimensions().add(new IdName(d.getId(), d.getU()));
//			slackToManagerId(ubd, d, AppConstants.STOCK_OUT);
//			stockEvents.add(createStockData(ubd.getId(), ubd.getName(), d.getId(), d.getName(), StockStatus.STOCK_OUT));
//		} else if (d.getQty().intValue() > 1 && finalQty.intValue() == 1) {
//			slackToManagerId(ubd, d, AppConstants.ABOUT_TO_STOCK_OUT);
//		} else if (d.getQty().intValue() <= 0 && finalQty.intValue() > 0) {
//			unitProductsStockInEvent.getProductIds().add(Integer.valueOf(d.getId()).toString());
//			unitProductsStockInEvent.getProductDimensions().add(new IdName(d.getId(), d.getU()));
//			stockEvents.add(createStockData(ubd.getId(), ubd.getName(), d.getId(), d.getName(), StockStatus.STOCK_IN));
//		}
//		publishStockEvents(unitProductsStockOutEvent);
//		publishStockEvents(unitProductsStockInEvent);
//
//		unitProductStockEventDataDao.save(stockEvents);
//
//	}

	private void createStockNotification(Map<InventoryData, BigDecimal> stockDataMap, int unitId, InventorySource source,Integer brandId) {
		UnitBasicDetail ubd = aggregator.getUnitBasicDetail(unitId);
		List<UnitProductsStockEventData> stockEvents = new ArrayList<>();
		List<UnitProductStockEventDataDetail> stockEventsDetails = new ArrayList<>();
		UnitProductsStockEvent unitProductsStockOutEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_OUT);
		UnitProductsStockEvent unitProductsStockInEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_IN);
		Date eventTime = AppUtils.getCurrentTimestamp();
		Map<Integer, Map<InventoryData, BigDecimal>> productStockDataMap = new HashMap<>();
		for (InventoryData d : stockDataMap.keySet()) {
			if (!productStockDataMap.containsKey(d.getId()) || productStockDataMap.get(d.getId()) == null) {
				productStockDataMap.put(d.getId(), new HashMap<>());
			}
			productStockDataMap.get(d.getId()).put(d, stockDataMap.get(d));
		}
		for (Integer productId : productStockDataMap.keySet()) {
			Optional<UnitTrimmedProductMapData> upmd = unitProductMapDataDao
					.findById(InventoryUtils.getUnitProductKey(unitId, productId));
			if (ubd == null || !ubd.isLiveInventoryEnabled()) {
				return;
			}
			if (upmd.isEmpty() || upmd.get().getProduct() == null || !upmd.get().getProduct().getInventoryTracked()) {
				return;
			}
			for (InventoryData d : productStockDataMap.get(productId).keySet()) {
				BigDecimal finalQty = productStockDataMap.get(productId).get(d);
				if (d.getQty().intValue() > 0 && finalQty.intValue() <= 0) {
					if (!unitProductsStockOutEvent.getProductIds().contains(productId.toString())) {
						unitProductsStockOutEvent.getProductIds().add(productId.toString());
					}
					unitProductsStockOutEvent.getProductDimensions().add(new IdName(d.getId(), d.getU()));
					publishNotificationOnKnock(ubd, d, AppConstants.STOCK_OUT);
					slackToManagerId(ubd, d, AppConstants.STOCK_OUT);
					try {
						UnitProductsStockEventData unitProductsStockEventData = createStockData(ubd.getId(), ubd.getName(), d.getId(), d.getName(), StockStatus.STOCK_OUT, d.getU(), source.toString(), eventTime, brandId);
						stockEvents.add(unitProductsStockEventData);
						stockEventsDetails.add(createStockDataSql(unitProductsStockEventData));
					}catch (Exception e){
						LOG.error("Unable to produce stock out event ");
					}
				} else if (d.getQty().intValue() > 1 && finalQty.intValue() == 1) {
					publishNotificationOnKnock(ubd, d, AppConstants.ABOUT_TO_STOCK_OUT);
					slackToManagerId(ubd, d, AppConstants.ABOUT_TO_STOCK_OUT);
				} else if (d.getQty().intValue() <= 0 && finalQty.intValue() > 0) {
					if (!unitProductsStockInEvent.getProductIds().contains(productId.toString())) {
						unitProductsStockInEvent.getProductIds().add(productId.toString());
					}
					unitProductsStockInEvent.getProductDimensions().add(new IdName(d.getId(), d.getU()));
					try {
						UnitProductsStockEventData unitProductsStockEventData = createStockData(ubd.getId(), ubd.getName(), d.getId(), d.getName(), StockStatus.STOCK_IN, d.getU(), source.toString(), eventTime, brandId);
						stockEvents.add(unitProductsStockEventData);
						stockEventsDetails.add(createStockDataSql(unitProductsStockEventData));
					}catch (Exception e){
						LOG.error("Unable to produce stock in event");
					}
				}
			}
		}
		publishStockEvents(unitProductsStockOutEvent);
		publishStockEvents(unitProductsStockInEvent);
		unitProductStockEventDataDao.saveAll(stockEvents);
		try {
			unitProductStockEventDataDetailDao.saveAll(stockEventsDetails);
		}catch (Exception e){
			LOG.error("Exception Caught While Saving unit Product Stock Event",e);
		}
	}

	private UnitProductsStockEventData createStockData(int unitId, String unitName, int productId, String productName,
													   StockStatus status, String dimension, String eventType, Date eventTime, Integer brandId) {
		UnitProductsStockEventData data = new UnitProductsStockEventData();
		data.setEventTimeStamp(eventTime);
		data.setProductId(productId);
		data.setProductName(productName);
		data.setUnitId(unitId);
		data.setUnitName(unitName);
		data.setStatus(status.name());
		data.setDimension(dimension);
		data.setEventType(eventType);
		data.setBrandId(brandId);
		return data;
	}

	private UnitProductStockEventDataDetail createStockDataSql(UnitProductsStockEventData unitProductsStockEventData) {
		UnitProductStockEventDataDetail data = new UnitProductStockEventDataDetail();
		data.setEventTimeStamp(unitProductsStockEventData.getEventTimeStamp());
		data.setProductId(unitProductsStockEventData.getProductId());
		data.setProductName(unitProductsStockEventData.getProductName());
		data.setUnitId(unitProductsStockEventData.getUnitId());
		data.setUnitName(unitProductsStockEventData.getUnitName());
		data.setStatus(unitProductsStockEventData.getStatus());
		data.setDimension(unitProductsStockEventData.getDimension());
		data.setEventType(unitProductsStockEventData.getEventType());
		data.setBrandId(unitProductsStockEventData.getBrandId());
		return data;
	}

	@Deprecated
	private void slackToManager(UnitBasicDetail ubd, InventoryData d, String status) {
		try {
			if (!ubd.isLiveInventoryEnabled()) {
				return;
			}
			String message = String.format("Unit : *%s*\nProduct : *%s*\nDimension : %s\nTime : %s\nStatus : %s\n",
					ubd.getName(), d.getName(), d.getU(), AppUtils.getCurrentTimestamp(), status);
			// area manager
			EmployeeBasicDetail e1 = aggregator.getEmployeeBasicDetail(ubd.getUnitManagerId());
			if (e1 != null && e1.getSlackChannel() != null && e1.getSlackChannel().trim().length() > 0) {
				LOG.info("Slack to AM {} ", e1.getSlackChannel());
				SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Inventory", e1.getSlackChannel(), null,
						message);
			}
			// deputy area manager
			EmployeeBasicDetail e2 = aggregator.getEmployeeBasicDetail(ubd.getCafeManagerId());
			if (e2 != null && e2.getSlackChannel() != null && e2.getSlackChannel().trim().length() > 0) {
				LOG.info("Slack to DAM {} ", e2.getSlackChannel());
				SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Inventory", e2.getSlackChannel(), null,
						message);
			}
			// cafe
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Inventory", null,
					SlackNotification.STOCK_OUT_EVENT.getChannel(props.getEnvType()), message);
		} catch (Exception e) {
			LOG.error("Error while publishing slack", e);
		}
	}

	private void publishNotificationOnKnock(UnitBasicDetail unit, InventoryData d, String status) {
		try {
			if (!unit.isLiveInventoryEnabled()) {
				return;
			}
			StockOutEventObject stockOutEventObject = new StockOutEventObject(unit.getName(), d.getId(), d.getName(), d.getQty().intValue(), AppUtils.getCurrentTimestamp(), unit.getCafeManagerId(), unit.getUnitManagerId());
			WebServiceHelper.postWithAuth(environment.getProperty("knock.base.url") +  AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-stockout-notification", environment.getProperty("knock.master.token"), stockOutEventObject, Boolean.class);
		} catch (Exception e) {
			LOG.error("Error while sending stock out notification to knock::", e);
		}

	}

	private void slackToManagerId(UnitBasicDetail ubd, InventoryData d, String status) {
		try {
			if (!ubd.isLiveInventoryEnabled()) {
				return;
			}
			String message = String.format("Unit : *%s*\nProduct : *%s*\nDimension : %s\nTime : %s\nStatus : %s\n",
					ubd.getName(), d.getName(), d.getU(), AppUtils.getCurrentTimestamp(), status);
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Inventory", null,
					!AppUtils.isProd(props.getEnvType())
							? props.getEnvType().name().toLowerCase() + "_" + ubd.getUnitManagerId() + "_notify"
							: ubd.getUnitManagerId() + "_notify",
					message);
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Inventory", null,
					!AppUtils.isProd(props.getEnvType())
							? props.getEnvType().name().toLowerCase() + "_" + ubd.getCafeManagerId() + "_notify"
							: ubd.getCafeManagerId() + "_notify",
					message);
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Inventory", null,
					SlackNotification.STOCK_OUT_EVENT.getChannel(props.getEnvType()), message);
		} catch (Exception e) {
			LOG.error("Error while publishing slack", e);
		}
	}

	private BigDecimal getMinimumValue(BigDecimal qty1, BigDecimal qty2) {
		if (qty1 == null && qty2 == null) {
			return null;
		}
		if (qty1 == null) {
			return qty2;
		}
		if (qty2 == null) {
			return qty1;
		}
		return qty1.compareTo(qty2) > 0 ? qty2 : qty1;
	}

	private BigDecimal getMaximumValue(BigDecimal qty1, BigDecimal qty2) {
		if (qty1 == null && qty2 == null) {
			return null;
		}
		if (qty1 == null) {
			return qty2;
		}
		if (qty2 == null) {
			return qty1;
		}
		return qty1.compareTo(qty2) > 0 ? qty1 : qty2;
	}

	@Override
	public List<RecipeInventory> getCafeProductRecipeInventoryData(int unitId, int productId, String dimension,
			boolean criticalOnly) {
		Optional<UnitTrimmedProductMapData> upmd = unitProductMapDataDao
				.findById(InventoryUtils.getUnitProductKey(unitId, productId));
		TrimmedProductVO product = upmd.map(UnitTrimmedProductMapData::getProduct).orElse(null);
		Map<String, RecipeInventory> criticalProductKeys = new HashMap<>();
		Map<String, RecipeInventory> productKeys = new HashMap<>();
		RecipeDetail rd;
		Optional<InventoryData> inventory;
		BigDecimal quantity = BigDecimal.ZERO;
		BigDecimal count;
		BigDecimal expiryQuantity = BigDecimal.ZERO;
		BigDecimal expiryCount;
		List<RecipeInventory> inventoryList = new ArrayList<>();
		RecipeInventory rinv = null;
		for (TrimmedProductPrice pp : product.getPrices().values()) {
			if (pp.getDimension().equalsIgnoreCase(dimension)) {
				rd = recipeLocalService.getRecipe(pp.getRecipeId());
				InventoryUtils.getAllRecipeProductInventory(unitId, rd, criticalProductKeys, productKeys);
				for (String key : productKeys.keySet()) {
					rinv = productKeys.get(key);
					if (criticalOnly && !rinv.isCritical()) {
						continue;
					}
					count = BigDecimal.ZERO;
					expiryCount = BigDecimal.ZERO;
					inventory = inventoryDataDao.findById(key);
					if (inventory.isPresent()) {
						quantity = inventory.get().getQty();
						expiryQuantity = inventory.get().getExQty();
						count = AppUtils.divideWithScale10(quantity, rinv.getRecipeQty());
						expiryCount = AppUtils.divideWithScale10(expiryQuantity, rinv.getRecipeQty());
					} else {
						quantity = BigDecimal.ZERO;
						expiryQuantity = BigDecimal.ZERO;
					}
					rinv.setAvailableQty(quantity);
					rinv.setCount(count);
					rinv.setExpiryCount(expiryCount);
					rinv.setExpiryQty(expiryQuantity);
					inventoryList.add(rinv);
				}
			}
		}
		return inventoryList;
	}

	@Override
	public List<QuantityResponseData> getUnitTimeline(int unitId) {
		List<QuantityResponseData> list = quantityResponseDataDao.findByUnitId(unitId);
		return list;
	}

	private void publishStockEvents(UnitProductsStockEvent unitProductsStockEvent) {
		if (unitProductsStockEvent != null && unitProductsStockEvent.getProductIds().size() > 0) {
			try {
				stockEventService.publishStockEvent(props.getEnvType().name(), unitProductsStockEvent);
			} catch (JMSException e) {
				LOG.error("Error publishing STOCK event:::", e);
			} catch (Exception e) {
				LOG.error("Error publishing STOCK event:::", e);
			}
		}
	}

	@Override
	public List<ProductInventory> getKettleInventory(int unitId) {
		return getUnitProductInventory(unitId);
	}

	public List<ProductInventory> getUnitProductInventory(int unitId) {
		try {
			return kettleDataService.getUnitProductInventory(unitId);
		} catch (IOException e) {
			LOG.error("I/O Exception while getting inventory for unit {}", unitId, e);
			return null;
		}
	}
	@Override
	public boolean updateSCMProductInventory(InventoryDataVO data) {
		inventoryUpdateDao.save(data);
		QuantityResponseData d = new QuantityResponseData();
		d.setUnitId(data.getUnitId());
		d.setAction(InventoryAction.ADD);
		d.setEventTime(AppUtils.getCurrentTimestamp());
		d.setSource(InventorySource.CAFE_AMENDMENT);

		ProductQuantityData pqd = new ProductQuantityData();
		pqd.setId(data.getId());
		pqd.setQ(data.getQty());
		pqd.setU(data.getU());
		pqd.setP(data.getPrice());

		List<ProductQuantityData> l = new ArrayList<ProductQuantityData>();
		l.add(pqd);
		d.setDetails(l);
		updateInventory(d, true);
		return true;
	}

	@Override
	public Set<Integer> productsExpiryIds(List<InventoryData> data, Integer unitId, Integer count, Integer brandId) {
		List<Integer> productIds = new ArrayList<>();
		List<Integer> productList = new ArrayList<>();
		Integer currentCount = 0;
		for (InventoryData list : data) {
			Optional<CriticalProductMap> mapOfCriticalProduct = criticalProductMapDao
					.findById(InventoryUtils.getCriticalProductKey(list.getId(), unitId));
			if (mapOfCriticalProduct != null) {// null pointer check
				productList = mapOfCriticalProduct.get().getProductsAffected();
			} else {
				LOG.info("NO critical Product map found: ");
			}
			LOG.info("expiring  productList:{}", productList);
			productIds.addAll(productList);
			currentCount += 1;
			if (currentCount.equals(count) && count > 0) {
				break;
			}
		}
		// List<Integer> finalList = new ArrayList<>();
		Set<Integer> finalList = new HashSet<>();
		List<UnitTrimmedProductMapData> unitTrimmedProductData = unitTrimmedProductMapDataDao
				.findByUnitIdAndBrandId(unitId, brandId);
		LOG.info("size of data fetched from redis is:{}", unitTrimmedProductData.size());
		/*
		 * List<UnitTrimmedProductMapData> unitProduct =
		 * unitTrimmedProductData.stream().filter(p -> p.getUnitId() == unitId)
		 * .collect(Collectors.toList());
		 */
		LOG.info("after appealing filter ,  unitId:{} and size:{} ", unitId, unitTrimmedProductData.size());
		if (!unitTrimmedProductData.isEmpty()) {
			for (Integer x : productIds) {
				for (UnitTrimmedProductMapData product : unitTrimmedProductData) {
					if (product.getProductId() == x) {
						if (product.getBrandId().equals(brandId)) {
							finalList.add(x);
						}
					}
				}
				if (finalList.size() == 5) {
					break;
				}
			}
		}
		LOG.info("Returning productId list:{}", finalList.size());
		return finalList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "InventoryDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void refreshInventoryForUnit(int unitId, boolean force) {
		refreshSCMProductInventory(unitId, force);
		UnitBasicDetail unit = aggregator.getUnitBasicDetail(unitId);
		if (UnitCategory.CAFE.name().equals(unit.getCategory()) || UnitCategory.COD.name().equals(unit.getCategory())) {
			refreshCafeProductsInventoryOptimized(unitId, force);
		}

	}

	/**
	 * Check for last closure id from kettle and process orders after that time.
	 * <p>
	 * collect orders from Inventory database first, then ask kettle to send orders
	 * other then already process and consume them naturally
	 *
	 * @param unitId
	 *
	 */
	public void processOrders(int unitId) {

		LOG.info("Processing orders for unit : {}", unitId);
		UnitClosure closure = kettleDataService.getLastDayClose(unitId);

		LOG.info("Clousure Details {}", JSONSerializer.toJSON(closure));
		/*Date startTime = AppUtils.getPreviousBusinessDate();
		if (closure != null) {
			startTime = closure.getStartTime();
		}*/
		/*List<QuantityResponseData> orders = quantityResponseDataDao.getByUnitIdAndEventTimeAndInventorySource(unitId,
				startTime, InventorySource.CAFE_ORDER);
		*/
		int lastOrderId = 0;
		if(closure != null) {
			lastOrderId = closure.getLastOrderId();
		}
		List<QuantityResponseData> orders = quantityResponseDataDao.getByUnitIdAndOrderIdAndInventorySource(unitId,
				lastOrderId, InventorySource.CAFE_ORDER);

		List<Integer> orderIds = new ArrayList<>();
		LOG.info("Processing {} Orders for unit {}", orders.size(), unitId);
		orders.forEach(o -> {
			updateInventory(o, false);
			orderIds.add(o.getOrderId());
		});
		/*
		List<QuantityResponseData> orders2 = quantityResponseDataDao.getByUnitIdAndEventTimeAndInventorySource(unitId,
				startTime, InventorySource.NON_WASTAGE_ORDER_CANCELLATION);*/

		List<QuantityResponseData> orders2 = quantityResponseDataDao.getByUnitIdAndOrderIdAndInventorySource(unitId,
				lastOrderId, InventorySource.NON_WASTAGE_ORDER_CANCELLATION);

		LOG.info("Processing NON_WASTAGE_ORDER_CANCELLATION {} Orders for unit {}", orders2.size(), unitId);
		orders2.forEach(o -> {
			updateInventory(o, false);
		});

		// -- IMPORTANT --
		// sync call from kettle
		kettleDataService.syncMissingOrders(unitId, closure != null ? closure.getId() : 0, orderIds);

	}

	@Override
	public void deleteMappingForUnit(int unitId) {
		try {
			unitBasicDetailDao.deleteById(unitId);

		} catch (Exception e) {
			LOG.error("Error in deleting unitBasicDetailDao for unit {}", unitId, e);
		}
		try {
			prouductTrimmedDataDao.deleteById(unitId);

		} catch (Exception e) {
			LOG.error("Error in deleting prouductTrimmedDataDao for unit {}", unitId, e);
		}
		try {
			unitSCMCriticalProductDao.deleteById(unitId);

		} catch (Exception e) {
			LOG.error("Error in deleting unitSCMCriticalProductDao for unit {}", unitId, e);
		}
		try {
			unitProductMapDataDao.deleteAll(unitProductMapDataDao.findByUnitId(unitId));

		} catch (Exception e) {
			LOG.error("Error in deleting unitProductMapDataDao for unit {}", unitId, e);
		}
	}

	@Override
	public void clearCafeProductRecipe(int unitId, int productId, String dimension) {
		Optional<UnitTrimmedProductMapData> upmdOptional = unitProductMapDataDao
				.findById(InventoryUtils.getUnitProductKey(unitId, productId));
		if (upmdOptional.isPresent()){
			UnitTrimmedProductMapData upmd = upmdOptional.get();
			TrimmedProductVO product = upmd.getProduct();
			for (TrimmedProductPrice pp : product.getPrices().values()) {
				if (pp.getDimension().equalsIgnoreCase(dimension)) {
					recipeLocalService.clearRecipe(pp.getRecipeId());
				}
			}
		}
	}

	@Override
	public View getUnitWiseProductInventory(UnitProductInventoryRequest inventoryRequest) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
											  HttpServletResponse response) throws Exception {
				String fileName = "InventoryData.xlsx";
				response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
				response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
				ExcelWriter writer = new ExcelWriter(workbook);
				List<String> keyList = new ArrayList<>();
				Map<String, InventoryData> inventoryMap = new HashMap<>();
				List<InventoryDataVo> listData = new ArrayList<>();
				for (UnitData unit : inventoryRequest.getUnits()) {
					for (ProductInventory inv : inventoryRequest.getProducts()) {
						keyList.add(InventoryUtils.unitCafeProductKey(unit.getId(), inv.getId(), inv.getDimension()));
					}
					Iterable<InventoryData> inventoryDataList = inventoryDataDao.findAllById(keyList);
					for (InventoryData data : inventoryDataList) {
						inventoryMap.put(data.getId() + data.getU(), data);
						listData.add(new InventoryDataVo(unit.getId(), unit.getName(), data.getId(), data.getName(), data.getU(), data.getQty(), data.getExQty()));
					}
					for (ProductInventory inv : inventoryRequest.getProducts()) {
						if (!inventoryMap.containsKey(inv.getId() + inv.getDimension())) {
							listData.add(new InventoryDataVo(unit.getId(), unit.getName(), inv.getId(), inv.getName(), inv.getDimension(), BigDecimal.ZERO, BigDecimal.ZERO));
						}
					}
				}
				writer.writeSheet(listData, InventoryDataVo.class);
			}

		};
	}
}
