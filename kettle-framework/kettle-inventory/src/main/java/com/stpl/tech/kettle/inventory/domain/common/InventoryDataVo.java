package com.stpl.tech.kettle.inventory.domain.common;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import java.io.Serializable;
import java.math.BigDecimal;

@ExcelSheet(value = "Inventory Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class InventoryDataVo  implements Serializable {
    @ExcelField(headerName = "UNIT_ID")
    private Integer unitId;
    @ExcelField(headerName = "UNIT_NAME")
    private String unitName;
    @ExcelField(headerName = "PRODUCT_ID")
    private int id;
    @ExcelField(headerName = "PRODUCT_NAME")
    private String name;
    @ExcelField(headerName = "DIMENSION")
    private String u;
    @ExcelField(headerName = "QUANTITY")
    private BigDecimal qty;
    @ExcelField(headerName = "EXPIRED_QUANTITY")
    private BigDecimal exQty = BigDecimal.ZERO;

    public InventoryDataVo() {
    }

    public InventoryDataVo(Integer unitId,String unitName,int id, String name, String u, BigDecimal qty, BigDecimal exQty) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.id = id;
        this.name = name;
        this.qty = qty;
        this.u = u;
        this.exQty = exQty;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getU() {
        return u;
    }

    public void setU(String u) {
        this.u = u;
    }
}
