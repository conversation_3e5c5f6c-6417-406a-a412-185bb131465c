package com.stpl.tech.kettle.inventory.domain.mongo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;

@Document
public class InventorySnapShot {

	@Id
	protected String id;
	@Indexed
	protected Integer unitId;
	protected String unitName;
	@Indexed
	protected Date businessDate;
	@Indexed
	protected String snapShotStatus;
	protected Date creationTime;
	protected Date lastUpdateTime;
	protected BigDecimal totalCost;
	List<InventoryData> inventory;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<InventoryData> getInventory() {
		return inventory;
	}

	public void setInventory(List<InventoryData> inventory) {
		this.inventory = inventory;
	}

	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public String getSnapShotStatus() {
		return snapShotStatus;
	}

	public void setSnapShotStatus(String snapShotStatus) {
		this.snapShotStatus = snapShotStatus;
	}

}
