package com.stpl.tech.kettle.inventory.core;

import java.io.ByteArrayOutputStream;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.master.notification.ReportEmailNotification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;

@Component
@Description("Cafe Inventory Report")
public class CafeInventoryReports {

	private static final Logger LOG = LoggerFactory.getLogger(CafeInventoryReports.class);

	@Autowired
	private InventoryProperties props;


	private List<AttachmentData> attachmentData;
	private static final String LINE_DELIMITER = "\r\n";


	public List<AttachmentData> getAttachmentData() {
		if (attachmentData == null) {
			attachmentData = new ArrayList<>();
		}
		return attachmentData;
	}

	public void renderInventory(List<List<String>> inventoryData,String empEmailId)throws IOException, EmailGenerationException {
		LOG.info("$$$$$$$$$$ BEGINING RENDERING EMAIL $$$$$$$$$$");
		String businessDate = AppUtils.getDateString(AppUtils.getBusinessDate(), "dd-MM-yyyy");
		ReportEmailNotification email = new ReportEmailNotification("Cafe Inventory Report" + businessDate,
				props.getEnvType());
		String[] toEmails = new String[] {"<EMAIL>" };
		String[] fileName = new String[] { "CAFE_INVENTORY_DATA" };
		email.setFromEmail("<EMAIL>");
		email.setToEmails(setToEmails(toEmails,empEmailId));
		List<AttachmentData> attachments = new ArrayList<>();


		for (String file : fileName) {
			attachments.add(createAttachments(file, inventoryData));
		}
		email.sendRawMail(attachments);
		LOG.info("$$$$$$$$$$ END SENDING EMAIL $$$$$$$$$$");
	}

	private AttachmentData createAttachments( String fileName, List<List<String>> inventoryData) throws IOException {

		AttachmentData data = new AttachmentData(
				fileName + "-" + AppUtils.getCurrentDateISTFormatted(),
				AppConstants.CSV_MIME_TYPE);

		getAttachmentData().add(data);
		ByteArrayOutputStream b = new ByteArrayOutputStream();
		for (List<String> list : inventoryData) {
			b.write(StringUtils.collectionToCommaDelimitedString(list).getBytes());
			b.write(LINE_DELIMITER.getBytes());
		}
		data.setAttachment(b.toByteArray());
		return data;

	}

	public String[] setToEmails(String[] toEmails,String empEmailId) {
		if(empEmailId!=null){
			return new String[] { empEmailId };
		}
		else{
			return toEmails;
		}
	}

}
