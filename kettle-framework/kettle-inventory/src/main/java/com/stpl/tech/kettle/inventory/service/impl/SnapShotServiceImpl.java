package com.stpl.tech.kettle.inventory.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.inventory.dao.mongo.InventorySnapShotDao;
import com.stpl.tech.kettle.inventory.dao.redis.InventorySnapShotVODao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitSCMCriticalProductDao;
import com.stpl.tech.kettle.inventory.domain.mongo.CostDetail;
import com.stpl.tech.kettle.inventory.domain.mongo.InventorySnapShot;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.mongo.SnapShotStatus;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.domain.redis.InventorySnapShotVO;
import com.stpl.tech.kettle.inventory.domain.redis.UnitSCMCriticalProductMap;
import com.stpl.tech.kettle.inventory.service.SCMDataService;
import com.stpl.tech.kettle.inventory.service.SnapShotService;
import com.stpl.tech.kettle.inventory.util.InventoryUtils;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.util.AppUtils;

@Service
public class SnapShotServiceImpl implements SnapShotService {

	private static final Logger LOG = LoggerFactory.getLogger(SnapShotServiceImpl.class);

	@Autowired
	private SCMDataService scmDataService;

	@Autowired
	private InventorySnapShotDao inventorySnapShotDao;

	@Autowired
	private UnitSCMCriticalProductDao unitSCMCriticalProductDao;

	@Autowired
	private InventorySnapShotVODao inventorySnapShotVODao;

	@Override
	public InventorySnapShot createDailySnapShot(int unitId) {
		LOG.info("Creating Snapshot for UnitId {}", unitId);
		Optional<UnitSCMCriticalProductMap> criticalProductMap = unitSCMCriticalProductDao.findById(unitId);
		InventorySnapShot inventorySnapShot = null;
		try {
			disableOldSnapShot(unitId);
			Date expireTime = InventoryUtils.getNextExpireTimeLimit();
			List<CostDetail> list = scmDataService.getCostDetails(unitId);
			Map<String, InventoryData> map = new HashMap<String, InventoryData>();
			for (CostDetail c : list) {
				InventoryUtils.addToMap(map, c, expireTime);
			}

			inventorySnapShot = new InventorySnapShot();
			inventorySnapShot.setInventory(new ArrayList<>(map.values()));
			inventorySnapShot.setBusinessDate(AppUtils.getBusinessDate());
			inventorySnapShot.setCreationTime(AppUtils.getCurrentTimestamp());
			inventorySnapShot.setUnitId(unitId);
			inventorySnapShot.setSnapShotStatus(SnapShotStatus.ACTIVE.name());
			inventorySnapShot.setLastUpdateTime(AppUtils.getCurrentTimestamp());

			for (InventoryData i : map.values()) {
				if (criticalProductMap.isPresent() && criticalProductMap.get().getCriticalProducts().contains(i.getId())
						&& BigDecimal.ZERO.compareTo(i.getExQty()) != 0) {
					inventorySnapShot.setTotalCost(AppUtils.add(inventorySnapShot.getTotalCost(),
							AppUtils.multiply(i.getExQty(), i.getPrice())));
				}
			}

			inventorySnapShot = inventorySnapShotDao.save(inventorySnapShot);
			inventorySnapShotVODao.save(new InventorySnapShotVO(inventorySnapShot.getUnitId(),
					inventorySnapShot.getTotalCost(), inventorySnapShot.getInventory()));

		} catch (Exception e) {
			LOG.error("Error while creating snapshot", e);
		}
		return inventorySnapShot;
	}

	private void disableOldSnapShot(int unitId) {
		List<InventorySnapShot> snaps = inventorySnapShotDao.findAllByUnitIdAndStatus(unitId,
				SnapShotStatus.ACTIVE.name());
		if (snaps != null) {
			for (InventorySnapShot s : snaps) {
				s.setSnapShotStatus(SnapShotStatus.IN_ACTIVE.name());
				inventorySnapShotDao.save(s);
			}
		}
	}

	@Override
	public InventorySnapShotVO getCurrentSnapShotVO(int unitId) {
		Optional<InventorySnapShotVO> snap = inventorySnapShotVODao.findById(unitId);
		if (snap.isEmpty()) {
			getCurrentSnapShot(unitId);
			snap = inventorySnapShotVODao.findById(unitId);
		}
		return snap.orElse(null);
	}


	@Override
	public InventorySnapShot getCurrentSnapShot(int unitId) {
		InventorySnapShot snap = inventorySnapShotDao.findByUnitIdAndStatus(unitId, SnapShotStatus.ACTIVE.name());
		if (snap == null) {
			snap = createDailySnapShot(unitId);
		}
		inventorySnapShotVODao
				.save(new InventorySnapShotVO(snap.getUnitId(), snap.getTotalCost(), snap.getInventory()));
		return snap;
	}

	@Override
	public List<InventorySnapShot> getCurrentSnapShotAllCafe() {
		List<InventorySnapShot> snap = inventorySnapShotDao.findAllByStatus(SnapShotStatus.ACTIVE.name());
		return snap;
	}

	@Override
	public void updateSnapShot(QuantityResponseData data) {
		Optional<UnitSCMCriticalProductMap> criticalProductMap = unitSCMCriticalProductDao.findById(data.getUnitId());
		Date systemExpireTime = InventoryUtils.getNextExpireTimeLimit();
		InventorySnapShot snap = getCurrentSnapShot(data.getUnitId());
		//disableOldSnapShot(data.getUnitId());
		for (ProductQuantityData q : data.getDetails()) {
			for (InventoryData i : snap.getInventory()) {
				if (i.getId() == q.getId()) {
					LOG.info("Snapshot quantity updated for {} by {}", q.getId(), q.getQ());
					InventoryUtils.updateInventoryData(i, q.getQ(), systemExpireTime, q.getE(), q.getP(),
							data.getAction());
				}
			}
		}
		for (InventoryData i : snap.getInventory()) {
			if (criticalProductMap.isPresent() && criticalProductMap.get().getCriticalProducts().contains(i.getId())
					&& BigDecimal.ZERO.compareTo(i.getExQty()) != 0) {
				snap.setTotalCost(AppUtils.add(snap.getTotalCost(), AppUtils.multiply(i.getExQty(), i.getPrice())));
			}
		}
		snap.setSnapShotStatus(SnapShotStatus.ACTIVE.name());
		snap.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		inventorySnapShotDao.save(snap);
		inventorySnapShotVODao
				.save(new InventorySnapShotVO(snap.getUnitId(), snap.getTotalCost(), snap.getInventory()));
	}

}
