package com.stpl.tech.kettle.inventory.config;

import java.util.Properties;

import javax.sql.DataSource;

import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.PlatformTransactionManager;

import com.stpl.tech.master.core.external.inventory.service.impl.InventoryServiceImpl;

@SpringBootApplication
@Configuration
@EnableScheduling
@EnableRedisRepositories(basePackages = { "com.stpl.tech.kettle.inventory.dao.redis", "com.stpl.tech.redis.core.dao" })
@EnableMongoRepositories(basePackages = { "com.stpl.tech.kettle.inventory.dao.mongo" })
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.kettle.inventory.dao.mysql" }, entityManagerFactoryRef = "InventoryDataSourceEMFactory", transactionManagerRef = "InventoryDataSourceTM")
@ComponentScan(basePackages = { "com.stpl.tech.kettle.inventory", "com.stpl.tech.redis.core.service",
		"com.stpl.tech.redis.core.cache",
		"com.stpl.tech.master.core.external.inventory.service" }, excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
				InventoryServiceImpl.class }))
@Import(value = {SpringUtilityServiceConfig.class, MasterSecurityConfiguration.class})
public class InventoryServiceConfig extends SpringBootServletInitializer{

	@Autowired
	private InventoryProperties env;

	public InventoryServiceConfig() {
		super();
	}

	public static void main(String[] args) {
		SpringApplication.run(InventoryServiceConfig.class);
	}

//	@Bean
//	JedisConnectionFactory jedisConnectionFactory() {
//		JedisConnectionFactory jedisConFactory = new JedisConnectionFactory();
//		jedisConFactory.setHostName(env.getRedisHost());
//		jedisConFactory.setPort(env.getRedisPort());
//		jedisConFactory.setUsePool(true);
//		jedisConFactory.setTimeout(100000);
//		jedisConFactory.setDatabase(env.getRedisDBIndex());
//		//jedisConFactory.setPassword(env.getRedisPassword());
//		return jedisConFactory;
//	}

	@Bean
	public RedisTemplate<?, ?> redisTemplate() {
		RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
		redisTemplate.setConnectionFactory(jedisConnectionFactory());
		return redisTemplate;
	}
//
//	@Bean
//	public StringRedisSerializer stringRedisSerializer() {
//		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
//		return stringRedisSerializer;
//	}

	@Bean
	public JedisConnectionFactory jedisConnectionFactory() {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(env.getRedisHost(), env.getRedisPort());
		config.setDatabase(env.getRedisDBIndex());
		return new JedisConnectionFactory(config);
	}

	@Bean
	public CacheManager cacheManager() {
		return RedisCacheManager.RedisCacheManagerBuilder.fromConnectionFactory(jedisConnectionFactory())
				.build();
	}

//	@Bean
//	public MongoClient factory() throws UnknownHostException {
//		MongoClientURI uri = new MongoClientURI(env.getMongoURI());
//		return new MongoClient(uri);
//	}
//
//	@Bean
//	public MongoDbFactory getMongoDbFactory() throws Exception {
//		return new SimpleMongoDbFactory(factory(), env.getMongoSchema());
//	}
//
//	@Bean(name = "mongoTemplate")
//	public MongoTemplate getMongoTemplate() throws Exception {
//		MongoTemplate mongoTemplate = new MongoTemplate(getMongoDbFactory());
//		return mongoTemplate;
//	}


	@Bean(name = "InventoryDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean inventoryDataSourceEMFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(inventoryDataSource());
		em.setPackagesToScan("com.stpl.tech.kettle.inventory.data");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(masterAdditionalProperties());
		em.setPersistenceUnitName("InventoryDataSourcePUName");
		return em;
	}

	@Bean(name = "InventoryDataSource")
	public DataSource inventoryDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getJdbcDriverClassName());
		dataSource.setUrl(env.getUrl());
		dataSource.setUsername(env.getJdbcUserName());
		dataSource.setPassword(env.getJdbcPassword());
		return dataSource;
	}

	@Bean(name = "InventoryDataSourceTM")
	public PlatformTransactionManager inventoryTransactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(inventoryDataSourceEMFactory().getObject());
		return transactionManager;
	}

	final Properties masterAdditionalProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto",env.getHibernateAuto());
		hibernateProperties.setProperty("hibernate.dialect", env.getHibernateDialect());
		hibernateProperties.setProperty("hibernate.show_sql", env.getHibernateSql());
		return hibernateProperties;
	}

	@Bean(name = "InventoryDataSourceET")
	public PersistenceExceptionTranslationPostProcessor clmExceptionTranslation() {
		return new PersistenceExceptionTranslationPostProcessor();
	}

	/*
	 * @PreDestroy public void shutdown() { MongoClient mongo = null; try { mongo =
	 * factory(); if (mongo != null) { mongo.close(); } } catch
	 * (UnknownHostException e) { }
	 *
	 * }
	 */
	
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(InventoryServiceConfig.class);
	}
}
