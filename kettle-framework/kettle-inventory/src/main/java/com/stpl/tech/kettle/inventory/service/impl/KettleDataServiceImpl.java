package com.stpl.tech.kettle.inventory.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import com.stpl.tech.kettle.inventory.domain.common.InventorySyncRequest;
import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.common.UnitClosure;
import com.stpl.tech.kettle.inventory.service.KettleDataService;
import com.stpl.tech.kettle.inventory.util.InventoryServiceEndpoints;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;

/**
 * <AUTHOR>
 *
 */
@Service
public class KettleDataServiceImpl implements KettleDataService {

	private static final Logger LOG = LoggerFactory.getLogger(KettleDataServiceImpl.class);

	@Autowired
	private InventoryProperties properties;

	@Override
	public UnitClosure getLastDayClose(int unitId) {
		long startTime = System.currentTimeMillis();
		LOG.info("Requesting Last Day Close details got unit Id: {}", unitId);
		String endPoint = properties.getKettleServiceBasePath() + InventoryServiceEndpoints.GET_LAST_DAY_CLOSE;
		String token = properties.getInventoryClientToken();
		UnitClosure unitClosure = WebServiceHelper.postWithAuth(endPoint, token, unitId, UnitClosure.class);
		LOG.info("Downloaded Product Data from master in {} miliseconds", System.currentTimeMillis() - startTime);
		return unitClosure;
	}

	@Override
	public void syncMissingOrders(int unitId, int closureId, List<Integer> orderIds) {
		long startTime = System.currentTimeMillis();
		LOG.info("Requesting order sync for unit Id: {}", unitId);
		String endPoint = properties.getKettleServiceBasePath()
				+ InventoryServiceEndpoints.SYNC_MISSING_ORDER_INVENTORY;
		String token = properties.getInventoryClientToken();
		InventorySyncRequest request = new InventorySyncRequest();
		request.setUnitId(unitId);
		request.setClosureId(closureId);
		request.setOrderIds(orderIds);
		Boolean response = WebServiceHelper.postWithAuth(endPoint, token, request, Boolean.class);
		if (response) {
			LOG.info("Sync Successful in {} miliseconds", System.currentTimeMillis() - startTime);
		} else {
			LOG.info("Sync Failed in {} miliseconds", System.currentTimeMillis() - startTime);
		}
	}

	@Override
	public List<ProductInventory> getUnitProductInventory(int unitId) throws IOException {
		LOG.info("Requesting product inventory for unit Id: {}", unitId);
		String endPoint = properties.getKettleServiceBasePath() + InventoryServiceEndpoints.PRODUCT_INVENTORY;
		String token = properties.getInventoryClientToken();
		List<?> inventories = WebServiceHelper.postWithAuth(endPoint, token, unitId, List.class);
		List<ProductInventory> data = new ArrayList<>();
		GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
		Gson gson = gSonBuilder.create();
		inventories.forEach(p -> {
			data.add(gson.fromJson(gson.toJson(p), ProductInventory.class));
		});
		return data;
	}
}
