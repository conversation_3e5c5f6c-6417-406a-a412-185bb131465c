package com.stpl.tech.kettle.inventory.domain.common;

public class StockOutData implements Comparable<StockOutData> {

	private String employeeName;
	private String unitName;
	private String productName;
	private String dimension;

	public StockOutData() {
		super();
	}

	public StockOutData(String employeeName, String unitName, String productName, String dimension) {
		super();
		this.employeeName = employeeName;
		this.unitName = unitName;
		this.productName = productName;
		this.dimension = dimension;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Override
	public int compareTo(StockOutData s1) {
		return this.employeeName.compareTo(s1.getEmployeeName());
	}

}
