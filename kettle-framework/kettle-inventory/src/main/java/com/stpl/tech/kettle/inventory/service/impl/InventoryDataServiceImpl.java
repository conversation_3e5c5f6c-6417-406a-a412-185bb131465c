package com.stpl.tech.kettle.inventory.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.stpl.tech.kettle.inventory.domain.common.InventoryDetailData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.inventory.dao.redis.UnitKeyDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitProductInventoryDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitSCMCriticalProductDao;
import com.stpl.tech.kettle.inventory.domain.common.ProductInventory;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.domain.redis.KeyRepository;
import com.stpl.tech.kettle.inventory.domain.redis.UnitSCMCriticalProductMap;
import com.stpl.tech.kettle.inventory.service.InventoryAggregationServices;
import com.stpl.tech.kettle.inventory.service.InventoryDataService;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.kettle.inventory.util.InventoryUtils;
import com.stpl.tech.kettle.inventory.vo.RecipeInventory;
import com.stpl.tech.redis.core.util.WebServiceHelper;

@Service
public class InventoryDataServiceImpl implements InventoryDataService {

	private static final Logger LOG = LoggerFactory.getLogger(InventoryDataServiceImpl.class);

	@Autowired
	private UnitProductInventoryDataDao inventoryDataDao;

	@Autowired
	private UnitKeyDataDao unitKeyDataDao;

	@Autowired
	private InventoryManagementService inventoryManagementService;

	@Autowired
	private UnitSCMCriticalProductDao unitSCMCriticalProductDao;

	@Autowired
	private InventoryAggregationServices inventoryAggregationServices;

	@Autowired
	private KeyMutexFactory factory;

	@Override
	public List<InventoryData> getSCMProductInventoryData(int unitId) {
		/**
		 * create SCM unit key which contains all product keys
		 */
		String key = InventoryUtils.unitSCMProductRepoKey(unitId);
		Optional<KeyRepository> keyRepo = unitKeyDataDao.findById(key);

		/**
		 * check if key is present, if not then there is no data for unit. in such case
		 * data is calculated for unit
		 */
		if (keyRepo.isEmpty() || keyRepo.get().getKeyList() == null || keyRepo.get().getKeyList().isEmpty()) {
			synchronized (factory.getMutex(unitId + "_SCM")) {
				keyRepo = unitKeyDataDao.findById(key);
				if (keyRepo.isEmpty() || keyRepo.get().getKeyList() == null || keyRepo.get().getKeyList().isEmpty()) {
					inventoryManagementService.refreshInventoryForUnit(unitId, false);
					keyRepo = unitKeyDataDao.findById(key);
					if (keyRepo == null) {
						return null;
					}
				}
			}
		}
		return WebServiceHelper.toList(inventoryDataDao.findAllById(keyRepo.get().getKeyList()));
	}

	@Override
	public Map<Integer, BigDecimal> getSCMProductInventoryDataTrimmed(int unitId){
		List<InventoryData> dataList = getSCMProductInventoryData(unitId);
		Map<Integer, BigDecimal> inventoryMap = new HashMap<>();
		for (InventoryData inventoryData : dataList) {
			inventoryMap.put(inventoryData.getId(), inventoryData.getQty());
		}
		return inventoryMap;
	}

	@Override
	public List<InventoryData> getCafeProductInventoryData(int unitId) {
		/**
		 * create Kettle Unit key which contains all product keys
		 */
		KeyRepository keyRepo = getInventoryKeyRepositoryData(unitId);
		if (keyRepo == null) return null;
		return WebServiceHelper.toList(inventoryDataDao.findAllById(keyRepo.getKeyList()));
	}

	private KeyRepository getInventoryKeyRepositoryData(int unitId) {
		String key = InventoryUtils.unitCafeProductRepoKey(unitId);
		Optional<KeyRepository> keyRepo = unitKeyDataDao.findById(key);

		/**
		 * check if key is present, if not then there is no data for the unit, in such
		 * case data is calculated for the unit
		 */
		if (keyRepo.isEmpty() || keyRepo.get().getKeyList() == null || keyRepo.get().getKeyList().isEmpty()) {
			synchronized (factory.getMutex(unitId + "_CAFE")) {
				keyRepo = unitKeyDataDao.findById(key);
				if (keyRepo.isEmpty() || keyRepo.get().getKeyList() == null || keyRepo.get().getKeyList().isEmpty()) {
					LOG.info("Cafe inventory unavailable for unit: {}", unitId);
					inventoryManagementService.refreshInventoryForUnit(unitId, false);
					keyRepo = unitKeyDataDao.findById(key);
					if (keyRepo.isEmpty()) {
						return null;
					}
				}
			}
		}
		if(keyRepo.isPresent()){
			return keyRepo.get();
		}
		return null;
	}

	@Override
	public List<RecipeInventory> getCafeProductRecipeInventoryData(int unitId, int productId, String dimension,
			boolean criticalOnly) {
		return inventoryManagementService.getCafeProductRecipeInventoryData(unitId, productId, dimension, criticalOnly);
	}

	@Override
	public List<QuantityResponseData> getUnitTimeline(int unitId) {
		return inventoryManagementService.getUnitTimeline(unitId);
	}

	@Override
	public List<ProductInventory> getKettleInventory(int unitId) {
		return inventoryManagementService.getKettleInventory(unitId);
	}

	@Override
	public UnitSCMCriticalProductMap getCriticalSCMProducts(int unitId){
		Optional<UnitSCMCriticalProductMap> map = unitSCMCriticalProductDao.findById(unitId);
		if (map.isEmpty()) {
			inventoryAggregationServices.refreshCafeProducts(unitId);
			map = unitSCMCriticalProductDao.findById(unitId);
		}
		return map.get();
	}

	@Override
	public List<InventoryDetailData> getCafeProductInventoryDetailData(int unitId) {
		KeyRepository keyRepo = getInventoryKeyRepositoryData(unitId);
		if (keyRepo == null) return null;
		List<InventoryDetailData> inventoryDetailData = new ArrayList<>();
		for(InventoryData data: inventoryDataDao.findAllById(keyRepo.getKeyList())){
			inventoryDetailData.add(new InventoryDetailData(data.getId(),data.getName(),data.getQty(),data.getU()));
		}
		return inventoryDetailData;
	}

}
