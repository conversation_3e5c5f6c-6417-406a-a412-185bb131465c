package com.stpl.tech.kettle.inventory.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.inventory.dao.redis.EmployeeBasicDetailDao;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.service.EmployeeManagementService;

@Service
public class EmployeeManagementServiceImpl implements EmployeeManagementService {

	@Autowired
	private EmployeeBasicDetailDao employeeDao;

	@Override
	public void refershAllEmployee() {
		employeeDao.deleteAll();
	}

	@Override
	public Iterable<EmployeeBasicDetail> listAllEmployee() {
		return employeeDao.findAll();
	}

}