package com.stpl.tech.kettle.inventory.domain.mongo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;

@Document
public class QuantityResponseData implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	private String id;
	@Indexed
	private int unitId;
	private List<ProductQuantityData> details;
	private InventoryAction action;
	@Indexed
	private InventorySource source;
	@Indexed
	private Integer orderId;
	@Indexed
	private Date eventTime;

	public QuantityResponseData() {
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<ProductQuantityData> getDetails() {
		return details;
	}

	public void setDetails(List<ProductQuantityData> details) {
		this.details = details;
	}

	public InventoryAction getAction() {
		return action;
	}

	public void setAction(InventoryAction action) {
		this.action = action;
	}

	public InventorySource getSource() {
		return source;
	}

	public void setSource(InventorySource source) {
		this.source = source;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer lastOrderId) {
		this.orderId = lastOrderId;
	}

	public Date getEventTime() {
		return eventTime;
	}

	public void setEventTime(Date eventTime) {
		this.eventTime = eventTime;
	}

	public String createId() {
		return (this.unitId > 0 ? this.unitId + "" : "") + (this.source != null ? this.source.name() : "")
				+ (this.orderId != null ? "" + this.orderId : "") + (this.action != null ? this.action.name() : "");
	}

}