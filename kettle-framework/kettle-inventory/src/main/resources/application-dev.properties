#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2018] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#
log.base.dir=/usr/share/tomcat8/logs/
spring.application.name=kettle-inventory
monitoring.user-name=admin
monitoring.user-password=Chaayos12345

# Enable JavaMelody auto-configuration (optional, default: true)
javamelody.enabled=false
# Data source names to exclude from monitoring (optional, comma-separated)
#javamelody.excluded-datasources=secretSource,topSecretSource
# Enable monitoring of Spring services and controllers (optional, default: true)
javamelody.spring-monitoring-enabled=false
# Initialization parameters for JavaMelody (optional)
# See: https://github.com/javamelody/javamelody/wiki/UserGuide#6-optional-parameters
#    log http requests:
javamelody.init-parameters.log=true
#    to exclude images, css, fonts and js urls from the monitoring:
javamelody.init-parameters.url-exclude-pattern=(/webjars/.*|/css/.*|/images/.*|/fonts/.*|/js/.*)
#    to aggregate digits in http requests:
# javamelody.init-parameters.http-transform-pattern: \d+
#    to add basic auth:
javamelody.init-parameters.authorized-users=${monitoring.user-name}:${monitoring.user-password}
#    to change the default storage directory:
javamelody.init-parameters.storage-directory==${log.base.dir}/${spring.application.name}/javamelody
#    to change the default "/monitoring" path:
# javamelody.init-parameters.monitoring-path=/admin/performance

management.endpoints.web.exposure.include=*
javamelody.management-endpoint-monitoring-enabled=true
spring.main.allow-bean-definition-overriding=true

# Enable response compression
server.compression.enabled=true
# The comma-separated list of mime types that should be compressed
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
# Compress the response only if the response size is at least 1KB
server.compression.min-response-size=1024

#management.server.servlet.contextPath=/${spring.application.name}-management
log.location=${log.base.dir}/${spring.application.name}/log
server.servlet.context-path=/${spring.application.name}
spring.mvc.servlet.path=/${serverZone}/rest

server.port=9699

environment.type=DEV

#Inventory Redis Source
redis.host=********
redis.port=6379
#redis.db.index=3
#redis.pass=R3d15D3V
redis.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImludmVudG9yeS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkU3MjU0IiwiaWF0IjoxNTIxMDIzMzkwfQ.LbBQlZofH0m1vDu4gmdKjZXwY4f48plQ0DJnZR36zVo

#Inventory service authentication key
inventory.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImludmVudG9yeS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkU3MjU0IiwiaWF0IjoxNTIxMDIzMzkwfQ.LbBQlZofH0m1vDu4gmdKjZXwY4f48plQ0DJnZR36zVo

#MongoDB configuration
spring.data.mongodb.uri=*********************************************************************************************************************************************************
spring.data.mongodb.database=inventory_dev
spring.data.mongodb.auto-connect-retry=true

#System URLs
base.path.kettle.service=http://********:8080
base.path.master.service=http://********:8080
base.path.kettle.crm=http://********:8080
base.path.kettle.scm=http://********:8080



jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=********************************************************
jdbc.user=root
jdbc.pass=Chaayos123#@!

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

knock.notification.url=http://********:9696/knock-service/rest/notification/send-notification-to-topic
knock.master.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IktOT0NLIiwiZW52VHlwZSI6IlNQUk9EIiwicGFydG5lcklkIjoyMiwicGFzc0NvZGUiOiI3QTMyNjNaRTY1WTVCQzgiLCJpYXQiOjE2NjA4ODYyMDd9.JL01lF-JHfLMKcrQ8r8VOqtOx7hGcAXEnNx4XmBgTYs
#  eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiTDNGVnVCV0haVVNYMDdGSlVRS3FaSnZhZ25Od0RIem9oUnQvalVBeC9zbDNHb2RUTjc2S1RCdTFFbDU4SHlOZzFxdVQ5cG9qcVhWZkRTK2w4ZGRjdnc9PSIsInVuaXRJZCI6MCwidGVybWluYWxJZCI6MCwidXNlcklkIjoxMDAwMjYsImlhdCI6MTY3NDM4Njc5MiwiaXNzdWVyIjoiS05PQ0tfU0VSVklDRSJ9.bC9YzdRMWtwvwYYoZkqfUclExsFbTw7awKwWGyU9v9Q
knock.base.url=http://********:8080/knock-service/

node.type=false
server.node.ip.details=********,********,********
